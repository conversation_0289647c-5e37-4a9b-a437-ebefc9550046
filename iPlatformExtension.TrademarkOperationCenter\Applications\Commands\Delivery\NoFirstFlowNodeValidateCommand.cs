﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

internal sealed record NoFirstFlowNodeValidateCommand(IEnumerable<DeliveryValidationDto> ValidationItems, BatchDeliveriesValidateType ValidateType) : IRequest<BatchDeliveryValidateResult>;