﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class BaseCaseStatusRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    UnitOfWorkManage<PlatformFreeSql> uowManger,
    IMemoryCache memoryCache,
    DefaultRedisCache redisCache,
    CacheExpirationToken<BasCaseStatus> expirationToken)
    : DefaultRepository<BasCaseStatus, string>(freeSql, uowManger), IBaseCaseStatusRepository
{
    IMemoryCache ICacheableRepository<string, BasCaseStatus>.MemoryCache => memoryCache;

    CacheExpirationToken<BasCaseStatus> ICacheableRepository<string, BasCaseStatus>.ExpirationToken => expirationToken;
    
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}