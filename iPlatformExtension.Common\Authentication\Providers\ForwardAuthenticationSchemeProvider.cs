﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Authentication.Providers;

public sealed class ForwardAuthenticationSchemeProvider(
    IOptions<AuthenticationOptions> options,
    IHttpContextAccessor httpContextAccessor)
    : AuthenticationSchemeProvider(options)
{
    private readonly IDictionary<Type, AuthenticationSchemeOptions?> _schemeOptions = new Dictionary<Type, AuthenticationSchemeOptions?>();

    private readonly object _lock = new();

    public override async Task<AuthenticationScheme?> GetDefaultAuthenticateSchemeAsync()
    {
        var schemes = await GetAllSchemesAsync();
        foreach (var scheme in schemes)
        {
            var handlerType = scheme.HandlerType;
            if (!_schemeOptions.TryGetValue(handlerType, out var options))
            {
                options = GetAuthenticationSchemeOptions(handlerType, scheme.Name);
                lock (_lock)
                {
                    if (!_schemeOptions.ContainsKey(handlerType))
                    {
                        _schemeOptions[handlerType] = options;
                    }
                }
            }
            
            var schemeName = options?.ForwardDefaultSelector?.Invoke(httpContextAccessor.HttpContext!);
            if (schemeName == null) continue;
            var targetScheme = await GetSchemeAsync(schemeName);
            if (targetScheme is not null)
            {
                return targetScheme;
            }
        }
        return await base.GetDefaultAuthenticateSchemeAsync();
    }

    private AuthenticationSchemeOptions? GetAuthenticationSchemeOptions(Type handlerType, string schemeName)
    {
        var services = httpContextAccessor.HttpContext?.RequestServices;
        ArgumentNullException.ThrowIfNull(services);

        var schemeOptionsType = typeof(IOptionsMonitor<>).MakeGenericType(
            GetAbstractAuthenticationHandlerType(handlerType)?.GetGenericArguments() ??
            throw new InvalidOperationException($"can not find any base type of {typeof(AuthenticationHandler<>).Name}"));
        var schemeOptions = services.GetService(schemeOptionsType) as IOptionsMonitor<AuthenticationSchemeOptions>;
        return schemeOptions?.Get(schemeName);
    }

    private static Type? GetAbstractAuthenticationHandlerType(Type? handlerType)
    {
        while (true)
        {
            if (handlerType is null || (handlerType.IsGenericType && handlerType.GetGenericTypeDefinition() == typeof(AuthenticationHandler<>)))
            {
                return handlerType;
            }

            handlerType = handlerType.BaseType;
        }
    }
}