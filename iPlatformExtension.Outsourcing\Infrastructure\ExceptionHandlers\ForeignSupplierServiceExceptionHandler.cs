﻿using System.Diagnostics;
using Grpc.Core;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Grpc.Interceptors;
using iPlatformExtension.Messages;
using iPlatformExtension.Outsourcing.Messages;

namespace iPlatformExtension.Outsourcing.Infrastructure.ExceptionHandlers;

internal sealed class ForeignSupplierServiceExceptionHandler(ILogger<ForeignSupplierServiceExceptionHandler> logger) 
    : ServerGrpcExceptionHandler<Supplier, MessageResult>(logger)
{
    protected override ValueTask<MessageResult> UnaryServerHandleAsync(Supplier request, Exception exception, ServerCallContext context)
    {
        return ValueTask.FromResult(new MessageResult
        {
            Success = false,
            ResultCode = ResultCode.Error,
            Message = exception.Message,
            Data = request.Pack(),
            TraceId = Activity.Current?.TraceId.ToString()
        });
    }

    protected override ValueTask<MessageResult> ClientStreamingServerHandleAsync(IAsyncStreamReader<Supplier> requestStreamReader, Exception exception,
        ServerCallContext context)
    {
        var current = requestStreamReader.Current;
        return ValueTask.FromResult(new MessageResult()
        {
            Success = false,
            ResultCode = ResultCode.Error,
            Message = exception.Message,
            Data = current.Pack(),
            TraceId = Activity.Current?.TraceId.ToString()
        });
    }

    protected override Task ServerStreamingServerHandleAsync(Supplier request, IServerStreamWriter<MessageResult> responseStream, Exception exception,
        ServerCallContext context)
    {
        return responseStream.WriteAsync(new MessageResult
        {
            Success = false,
            ResultCode = ResultCode.Error,
            Message = exception.Message,
            Data = request.Pack(),
            TraceId = Activity.Current?.TraceId.ToString()
        }, context.CancellationToken);
    }

    protected override Task DuplexStreamingServerHandleAsync(IAsyncStreamReader<Supplier> reader, IServerStreamWriter<MessageResult> writer, Exception exception,
        ServerCallContext context)
    {
        var current = reader.Current;
        return writer.WriteAsync(new MessageResult
        {
            Success = false,
            ResultCode = ResultCode.Error,
            Message = exception.Message,
            Data = current.Pack(),
            TraceId = Activity.Current?.TraceId.ToString()
        }, context.CancellationToken);
    }
}