﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using iPlatformExtension.Outsourcing.Infrastructure.Authorization.SupplierAssignment;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class NotAssignedCountQueryHandler(
    IPublisher publisher,
    NotAssignedProcQueryContextFactory contextFactory) 
    : IRequestHandler<NotAssignedCountQuery, long?>
{
    public async Task<long?> Handle(NotAssignedCountQuery request, CancellationToken cancellationToken)
    {
        var context = await contextFactory.CreateAsync(request.AuthorizationType, cancellationToken);
        if (context is null)
        {
            return null;
        }
        
        await publisher.Publish(context, cancellationToken);

        return await context.CountQueryBuilder.Build().CountAsync(cancellationToken);
    }
}