﻿using iPlatformExtension.Commission.Infrastructure.Models;
using MediatR;

namespace iPlatformExtension.Commission.Application.Commands.Patent;

/// <summary>
/// 专利提成权值推送命令
/// </summary>
/// <param name="Year">年</param>
/// <param name="Month">月</param>
public sealed record PushPatentCommissionWeightCommand(int Year, int Month) : IBackgroundTracingCommand, IRequest
{
    /// <inheritdoc />
    public string OperationName => "推送专利提成权值数据";
}