﻿using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

internal sealed record BuildProcApplicantInfoCommand(CaseProcInfo ProcInfo, List<DeliApplicant> Applicants) : IMatchTrademarkProcCommand
{
    public string CtrlProcId => ProcInfo.CtrlProcId;

    public string CaseDirection => ProcInfo.CaseInfo.CaseDirection;
}