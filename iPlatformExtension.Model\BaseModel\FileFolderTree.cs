using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "file_folder_tree", DisableSyncStructure = true)]
	public partial class FileFolderTree {

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "folder_id", StringLength = 50)]
		public string FolderId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "icon", StringLength = 250)]
		public string Icon { get; set; }

		[ Column(Name = "id", StringLength = 50)]
		public string Id { get; set; }

		[ Column(Name = "is_dept")]
		public bool IsDept { get; set; } = false;

		[ Column(Name = "is_open")]
		public bool IsOpen { get; set; } = false;

		[ Column(Name = "load_type", StringLength = 50)]
		public string LoadType { get; set; }

		[ Column(Name = "name", StringLength = 500)]
		public string Name { get; set; }

		[ Column(Name = "pId", StringLength = 50)]
		public string PId { get; set; }

	}

}
