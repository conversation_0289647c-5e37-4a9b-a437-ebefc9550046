﻿using System.Text.RegularExpressions;
using CSScriptLib;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.MailCenter.Applications.Commands.AnalysisRule;
using iPlatformExtension.MailCenter.Applications.Models.Analysis;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Attributes;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AnalysisRule;

/// <summary>
/// 解析规则
/// </summary>
internal sealed class ConfigAnalysisCommandHandler(
    IFreeSql<MailCenterFreeSql> freeSql,
    IMailConfigHistoryRepository mailConfigHistoryRepository,
    IMailReceiveRepository mailReceive,
    IMailReceiveFlowRepository mailReceiveFlowRepository,
    EntityTypeInfoProvider entityTypeInfoProvider,
    IHttpContextAccessor httpContextAccessor,
    IMailHostRepository mailHostRepository,
    IFlowRecordRepository flowRecordRepository,
    IMailReaderListRepository mailReaderListRepository,
    IFlowPrivateListRepository flowPrivateListRepository,
    IMailCorrelativeRepository mailCorrelativeRepository
) : IRequestHandler<ConfigAnalysisCommand>
{
    public async Task Handle(ConfigAnalysisCommand request, CancellationToken cancellationToken)
    {
        var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
        //获取所有规则
        var mailConfigs = await freeSql
            .Select<MailConfig, MailConfigFilter>()
            .WithLock()
            .LeftJoin(it => it.t1.ConfigId == it.t2.ConfigId)
            .Where(it => it.t1.IsEnabled == SysEnum.Status.Enable.GetHashCode())
            .Where(it => it.t1.HostId.Contains(request.MailAnalysis.HostId))
            .ToListAsync(it => new { it.t1, it.t2 }, cancellationToken);
        var mailConfigList = mailConfigs
            .Select(it => new MailConfigDto(
                it.t1.ConfigId,
                it.t2.FilterHead,
                it.t2.FilterType,
                it.t2.FilterValue
            ))
            .GroupBy(it => it.ConfigId)
            .ToDictionary(a => a.Key, b => b.ToList());

        //删除历史
        var mailReceiveFlowDelteHistory = await mailReceiveFlowRepository
            .Where(it => request.MailAnalysis.MailId == it.MailId)
            .ToListAsync(cancellationToken);
        await flowRecordRepository.DeleteAsync(
            it => it.MailId == request.MailAnalysis.MailId,
            cancellationToken
        );

        await mailReceiveFlowRepository.DeleteAsync(mailReceiveFlowDelteHistory, cancellationToken);
        var mailReaderLists = await mailReaderListRepository
            .Where(it => request.MailAnalysis.MailId.Contains(it.MailId))
            .ToListAsync(cancellationToken);
        await mailReaderListRepository.DeleteAsync(mailReaderLists, cancellationToken);
        await mailCorrelativeRepository.DeleteAsync(
            it => it.MailId == request.MailAnalysis.MailId,
            cancellationToken
        );
        await mailConfigHistoryRepository.DeleteAsync(
            it => it.MailId == request.MailAnalysis.MailId,
            cancellationToken
        );
        var flowPrivateLists = await flowPrivateListRepository
            .Where(it => request.MailAnalysis.MailId == it.MailId)
            .ToListAsync(cancellationToken);
        await flowPrivateListRepository.DeleteAsync(flowPrivateLists, cancellationToken);

        var hitRuleList = mailConfigList
            .ToDictionary(it => it.Key, value => SelectRule(request.MailAnalysis, value.Value))
            .Where(it => it.Value)
            .Select(it => it.Key)
            .ToList();
        var cacheValueAsync = await mailHostRepository.GetCacheValueAsync(request.MailAnalysis.HostId
, cancellationToken: cancellationToken);
        if (cacheValueAsync?.IsPrivate == true)
        {
            request.MailAnalysis.Status = SysEnum.ReceiveFileType.Sort.GetHashCode();
            var mailReceiveFlow = new MailReceiveFlow
            {
                Id = Guid.NewGuid().ToString(),
                CreateBy = userId,
                CreateTime = DateTime.Now,
                MailId = request.MailAnalysis.MailId,
                UndertakeUserId = cacheValueAsync.PrivateUserId,
            };

            await mailReceiveFlowRepository.InsertAsync(mailReceiveFlow, cancellationToken);
        }
        else
        {
            var mailConfigDos = mailConfigs
                .Select(it => it.t1)
                .DistinctBy(it => it.ConfigId)
                .Where(it => hitRuleList.Contains(it.ConfigId))
                .ToList();
            request.MailAnalysis.Status = SysEnum.ReceiveFileType.Sort.GetHashCode();
            var mailReceiveFlow = new MailReceiveFlow
            {
                Id = Guid.NewGuid().ToString(),
                CreateBy = userId,
                CreateTime = DateTime.Now,
                MailId = request.MailAnalysis.MailId,
            };
            if (mailConfigDos.Count == 1)
            {
                var rule = mailConfigDos.FirstOrDefault();

                if (rule.ConfigType == SysEnum.ConfigType.Finish.ToString())
                {
                    mailReceiveFlow.SortBy = rule.HandUser;
                    mailReceiveFlow.UndertakeUserId = rule.UndertakeUser;
                    mailReceiveFlow.SortTime = DateTime.Now;
                    request.MailAnalysis.Status = SysEnum.ReceiveFileType.Handle.GetHashCode();

                    var allotRecord = new FlowRecord
                    {
                        Id = Guid.NewGuid().ToString(),
                        AuditTime = DateTime.Now,
                        AuditType = MailAction.Submit.ToString(),
                        AuditUser = rule.HandUser,
                        CurNodeId = SysEnum.MailFlowAction.Allot.ToString(),
                        IsCurrent = MailFlowActionStatus.DisEnable.GetHashCode(),
                        MailId = request.MailAnalysis.MailId,
                        Version = "1",
                        AuditRemark = rule.ConfigRemark,
                    };
                    var handleRecord = new FlowRecord
                    {
                        Id = Guid.NewGuid().ToString(),
                        AuditUser = rule.UndertakeUser,
                        CurNodeId = SysEnum.MailFlowAction.Handle.ToString(),
                        IsCurrent = MailFlowActionStatus.Enable.GetHashCode(),
                        MailId = request.MailAnalysis.MailId,
                        Version = "1",
                        PreRecordId = allotRecord.Id,
                    };
                    var flowRecordList = new List<FlowRecord>() { allotRecord, handleRecord };
                    await flowRecordRepository.InsertAsync(flowRecordList, cancellationToken);
                }
                else if (rule.ConfigType == SysEnum.ConfigType.Allot.ToString())
                {
                    request.MailAnalysis.Status = SysEnum.ReceiveFileType.Sort.GetHashCode();
                    mailReceiveFlow.UndertakeUserId = rule.UndertakeUser;
                }
                else if (rule.ConfigType == SysEnum.ConfigType.Ignore.ToString())
                {
                    request.MailAnalysis.Status = SysEnum.ReceiveFileType.Ignore.GetHashCode();
                    mailReceiveFlow.IgnoreBy = rule.IgnoreUser;
                    mailReceiveFlow.IgnoreTime = DateTime.Now;
                }

                if (
                    rule.ConfigType == SysEnum.ConfigType.Allot.ToString()
                    || rule.ConfigType == SysEnum.ConfigType.Finish.ToString()
                )
                {
                    if (!string.IsNullOrWhiteSpace(rule.ReadUser))
                    {
                        var readUserList = rule.ReadUser.Split(';');
                        var readerLists = readUserList.Select(it => new MailReaderList
                        {
                            Id = Guid.NewGuid().ToString(),
                            CreateBy = "52c742fa-76d4-4e5d-b3ad-c51841d576dd",
                            CreateTime = DateTime.Now,
                            MailId = request.MailAnalysis.MailId,
                            Status = 0,
                            UserId = it,
                            //todo: 判断Reader的邮件类型
                        });
                        await mailReaderListRepository.InsertAsync(readerLists, cancellationToken);
                    }
                }
            }
            await mailReceiveFlowRepository.InsertAsync(mailReceiveFlow, cancellationToken);

            await mailConfigHistoryRepository.InsertAsync(
                mailConfigDos.Select(it => new MailConfigHistory()
                {
                    ConfigId = it.ConfigId,
                    CreateTime = DateTime.Now,
                    MailConfigHistoryId = Guid.NewGuid().ToString(),
                    MailId = request.MailAnalysis.MailId,
                }),
                cancellationToken
            );
        }
        await mailReceive.UpdateAsync(request.MailAnalysis, cancellationToken);
    }

    /// <summary>
    /// 规则匹配
    /// </summary>
    /// <param name="mailReceive"></param>
    /// <param name="configs"></param>
    /// <returns></returns>
    private bool SelectRule(MailAnalysis mailReceive, List<MailConfigDto> configs)
    {
        var entityTypeInfo = entityTypeInfoProvider.Get(typeof(MailAnalysis));
        //规则同字段分组
        var groupBy = configs
            .GroupBy(it => ConvertToFilterHead(it.FilterHead))
            .ToDictionary(a => a.Key, b => b.ToList());
        var dictionaryFilter = new Dictionary<MailMatch, string>();

        groupBy.ForEach(filter =>
        {
            var containSelect = filter
                .Value.Where(it => it.FilterType == SysEnum.FilterType.Contain.ToString())
                .Select(it =>
                    it.FilterValue.Contains('.')
                        ? it.FilterValue.Replace(".", "\\.")
                        : it.FilterValue
                )
                .ToList();
            var containFilter =
                containSelect.Count() > 1
                    ? containSelect.Aggregate(
                        (current, next) =>
                            !string.IsNullOrWhiteSpace(next)
                                ? $"({current})" + "|" + $"({next})"
                                : $"({current})"
                    )
                    : containSelect.Select(it => $"({it})").FirstOrDefault();

            var equalFilters = filter
                .Value.Where(it => it.FilterType == SysEnum.FilterType.Equal.ToString())
                .Select(it => it.FilterValue)
                .ToList();
            var equalFilter =
                equalFilters.Count() > 1
                    ? equalFilters.Aggregate(
                        (current, next) =>
                            !string.IsNullOrWhiteSpace(next)
                                ? $"^({current})$" + "|" + $"^({next})$"
                                : $"^({current})$"
                    )
                    : equalFilters.Select(it => $"^({it})$").FirstOrDefault();

            dictionaryFilter.TryAdd(
                new MailMatch()
                {
                    Field = filter.Key.GetTableName<FieldNameAttribute>() ?? filter.Key.ToString(),
                    Table = filter.Key.GetTableName<TableNameAttribute>(),
                },
                new List<string>
                {
                    containFilter ?? string.Empty,
                    equalFilter ?? string.Empty,
                }.Aggregate(
                    (current, next) =>
                        string.IsNullOrWhiteSpace(current) ? $"{equalFilter}"
                        : !string.IsNullOrWhiteSpace(next) ? $"{current}" + "|" + $"{next}"
                        : $"{current}"
                )
            );
        });

        //所有过滤条件匹配
        foreach (var config in dictionaryFilter)
        {
            foreach (var entityPropertyInfo in entityTypeInfo.EntityPropertyInfos)
            {
                if (config.Key.Table is null)
                {
                    if (config.Key.Field != entityPropertyInfo.PropertyName)
                        continue;
                    var invoke = entityPropertyInfo.Get?.Invoke(mailReceive);
                    if (!Regex.IsMatch(invoke?.ToString() ?? string.Empty, config.Value))
                    {
                        return false;
                    }
                }
                else
                {
                    if (config.Key.Table != entityPropertyInfo.PropertyName)
                        continue;
                    var invoke = entityPropertyInfo.Get?.Invoke(mailReceive);
                    if (invoke is null)
                        continue;

                    if (invoke is IEnumerable<object> list)
                    {
                        if (!list.Any(CheckPropertyMatch))
                        {
                            return false;
                        }
                    }
                    else
                    {
                        if (!CheckPropertyMatch(invoke))
                        {
                            return false;
                        }
                    }

                    bool CheckPropertyMatch(object obj)
                    {
                        var type = obj.GetType();
                        var properties = type.GetProperties();
                        foreach (var property in properties)
                        {
                            if (config.Key.Field != property.Name)
                                continue;
                            var propertyValue = property.GetValue(obj)?.ToString() ?? string.Empty;
                            if (!Regex.IsMatch(propertyValue, config.Value))
                            {
                                return false;
                            }
                        }
                        return true;
                    }
                }
            }
        }

        return true;
    }

    private FilterHead ConvertToFilterHead(string filterHeadString)
    {
        if (Enum.TryParse<FilterHead>(filterHeadString, out FilterHead result))
        {
            return result;
        }
        throw new ArgumentException($"无法将字符串 '{filterHeadString}' 转换为 FilterHead 枚举值");
    }
}
