﻿namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;

/// <summary>
/// 日志扩展
/// </summary>
public static partial class LoggingExtension
{
    /// <summary>
    /// 记录锁定任务成功
    /// </summary>
    /// <param name="logger">日志组件</param>
    /// <param name="procId">任务ID</param>
    /// <param name="traceId">追踪ID</param>
    [LoggerMessage(LogLevel.Information, Message = "请求[{TraceId}]回调上锁任务[{ProcId}]成功")]
    public static partial void LogLockProcSuccessfully(this ILogger logger, string procId, string? traceId);

    /// <summary>
    /// 记录解锁任务成功
    /// </summary>
    /// <param name="logger">日志</param>
    /// <param name="procId">任务ID</param>
    /// <param name="traceId">跟踪ID</param>
    [LoggerMessage(LogLevel.Information, Message = "释放请求[{TraceId}]回调上锁任务[{ProcId}]成功")]
    public static partial void LogReleaseProcSuccessfully(this ILogger logger, string procId, string? traceId);

    /// <summary>
    /// 记录解锁任务失败
    /// </summary>
    /// <param name="logger">日志组件</param>
    /// <param name="procId">任务ID</param>
    /// <param name="traceId">跟踪ID</param>
    [LoggerMessage(LogLevel.Warning, Message = "请求[{TraceId}]回调上锁任务[{ProcId}]失败")]
    public static partial void LogReleaseProcFailed(this ILogger logger, string procId, string? traceId);

    /// <summary>
    /// 记录自动递交结果回调流程执行失败
    /// </summary>
    /// <param name="logger">日志组件</param>
    /// <param name="procId">任务id</param>
    /// <param name="exception">异常</param>
    [LoggerMessage(LogLevel.Error, Message = "任务【{ProcId}】自动递交回调执行流程失败")]
    public static partial void LogNextFlowError(this ILogger logger, string procId, Exception exception);
}