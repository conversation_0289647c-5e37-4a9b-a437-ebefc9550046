﻿using MimeKit.IO;
using MimeKit;
using iPlatformExtension.Common.Clients.HuaweiObs;
using System.Net;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Common.Clients.HuaweiObs.Options;
using Microsoft.Extensions.Options;
using iPlatformExtension.Common.Cache;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using CSScriptLib;
using SharpCompress.Common;
using System.IO;
using Google.Protobuf.WellKnownTypes;
using Org.BouncyCastle.Utilities.Zlib;
using Org.BouncyCastle.Utilities;
using System.Collections;
using System.Collections.Concurrent;
using iPlatformExtension.Model.Dto.Flow;
using Org.BouncyCastle.Bcpg.OpenPgp;
using System.Collections.Generic;
using static MailKit.Net.Imap.ImapMailboxFilter;
using MailKit.Security;
using System.Threading;
using MailKit.Net.Imap;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using Microsoft.Extensions.Hosting;
using MailKit.Net.Pop3;

namespace iPlatformExtension.MailCenter.Infrastructure.Extension
{
    public sealed class MailTool()
    {
        private static ConcurrentDictionary<string, List<FlowAnalyseDto>> ConcurrentToDoList = new ConcurrentDictionary<string, List<FlowAnalyseDto>>();

        private readonly string _keyName = "TodoList";
        public void TryAdd(List<FlowAnalyseDto> value)
        {
            if (ConcurrentToDoList == null)
            {
                ConcurrentToDoList = new ConcurrentDictionary<string, List<FlowAnalyseDto>>();
            }
            else
            {
                ConcurrentToDoList.Clear();
                ConcurrentToDoList.TryAdd(_keyName, value);
            }
        }

        public void TryClear()
        {
            if (ConcurrentToDoList == null)
            {
                ConcurrentToDoList = new ConcurrentDictionary<string, List<FlowAnalyseDto>>();
            }
            else
            {
                ConcurrentToDoList.Clear();
            }
        }

        public List<FlowAnalyseDto> TryGetTodoList()
        {
            if (ConcurrentToDoList == null)
            {
                ConcurrentToDoList = new ConcurrentDictionary<string, List<FlowAnalyseDto>>();
            }
            ConcurrentToDoList.TryGetValue(_keyName, out List<FlowAnalyseDto> res);
            return res;
        }

        /// <summary>
        /// 验证邮箱
        /// </summary>
        public async Task VerifyMailAvailableAsync(VerifyMailDto MailDto, CancellationToken cancellationToken)
        {
            try
            {
                if (MailDto.ImapHost.Contains("imap"))
                {
                    using (var client = new ImapClient(/*new ProtocolLogger(CreateMailLog())*/))
                    {
                        await client.ConnectAsync(MailDto.ImapHost, MailDto.ImapPort.Value,
                             SecureSocketOptions.StartTls, cancellationToken);
                        await client.AuthenticateAsync(MailDto.SenderAccount, MailDto.SenderPassword, cancellationToken);
                        await client.DisconnectAsync(true, cancellationToken);
                    }
                }
                else
                {
                    using (var client = new Pop3Client())
                    {
                        await client.ConnectAsync(MailDto.ImapHost, MailDto.ImapPort.Value, SecureSocketOptions.None, cancellationToken);
                        await client.AuthenticateAsync(MailDto.SenderAccount, MailDto.SenderPassword, cancellationToken);
                        await client.DisconnectAsync(true, cancellationToken);
                    }
                }
            }
            catch (Exception)
            {
                throw new ApplicationException("邮箱验证失败,请确认邮箱帐号或密码是否正确。");
            }
        }

    }
}
