﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.Public.Applications.Commands.MyTrademarkCase;

/// <summary>
/// 任务移入分类
/// </summary>
/// <param name="ProcId">任务id列表</param>
/// <param name="PrivateId">标签id</param>
public record MoveProcessCommand(List<string> ProcId,string PrivateId) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

