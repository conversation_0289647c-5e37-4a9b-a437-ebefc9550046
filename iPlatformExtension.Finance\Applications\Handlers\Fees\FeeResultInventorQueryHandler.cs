﻿using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Service.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultInventorQueryHandler(IInventorQueryService inventorQuery)
    : IRequestHandler<FeeResultInventorQuery>
{
    public async Task Handle(FeeResultInventorQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return ;

        var caseIds = request.CaseIds;
        var feeResults = request.FeeResults;

        var inventors = await inventorQuery.GetInventorsByCaseIdsAsync(caseIds);
        
        foreach (var feeItem in feeResults)
        {
            feeItem.Inventors = inventors.TryGetValue(feeItem.CaseId, out var inventorInfos)
                ? inventorInfos
                : Array.Empty<InventorInfo>();
        }
        
    }
}