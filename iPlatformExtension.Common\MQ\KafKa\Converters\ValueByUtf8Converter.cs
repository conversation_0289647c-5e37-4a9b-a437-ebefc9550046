﻿using Confluent.Kafka;
using iPlatformExtension.Model.Dto.MailCenter;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Collections;

namespace iPlatformExtension.Common.MQ.KafKa.Converters
{
    public class ValueByUtf8Converter<T> : ISerializer<T>,IDeserializer<T>
    {
        public byte[] Serialize(T data, SerializationContext context)
        {
            if (data == null)
                return null;

            var json = JsonConvert.SerializeObject(data);
            return Encoding.UTF8.GetBytes(json);
        }


        public T Deserialize(ReadOnlySpan<byte> data, bool isNull, SerializationContext context)
        {
            string jsonString = Encoding.UTF8.GetString(data);
            return JsonConvert.DeserializeObject<T>(jsonString) ?? throw new JsonException("反序列化失败");
        }
    }
}
