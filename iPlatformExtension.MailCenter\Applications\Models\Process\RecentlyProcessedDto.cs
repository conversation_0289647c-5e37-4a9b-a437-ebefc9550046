﻿namespace iPlatformExtension.MailCenter.Applications.Models.Process;

/// <summary>
/// 收件待办清单Dto
/// </summary>
/// <param name="MailId">邮件id</param>
/// <param name="MailFrom">发件人</param>
/// <param name="MailNo">收件编号</param>
/// <param name="MailPriority">邮件重要性Highest:紧急,其他(High,Normal,Low,Lowest)</param>
/// <param name="MailSubject">邮件主题</param>
/// <param name="MailTo">收件人</param>
/// <param name="Status">邮件状态-1:取消,0:待解析,1:解析中,2:待分拣;3:办理中;4:已忽略;5:已办理;500:出错</param>
/// <param name="MailDate">发送时间</param>
/// <param name="AuditType">提交类型:Submit,Reject,Transfer</param>
/// <param name="AuditRemark">办理建议</param>
/// <param name="AuditTime">到达时间</param>
/// <param name="SortBy">分拣人</param>
/// <param name="AuditUser">当前办理人</param>
/// <param name="UndertakeUser">当前办理人</param>
public record RecentlyProcessedDto(
    string MailId,
    string MailFrom,
    string MailNo,
    string MailPriority,
    string MailSubject,
    string MailTo,
    int? Status,
    DateTime? MailDate,
    string AuditType,
    string AuditRemark,
    DateTime? AuditTime,
    string SortByTemp,
    string CurNodeId,
    string AuditUserTemp,
    string UndertakeUserTemp)
{
    public object SortBy { get; set; }
    public object AuditUser { get; set; }
    public object UndertakeUser { get; set; }
};

