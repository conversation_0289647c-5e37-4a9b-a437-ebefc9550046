﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 开案相关案关联表
/// </summary>
[Table(Name = "apply_case_related")]
public class ApplyCaseRelated
{
    /// <summary>
    /// 主键
    /// </summary>
    [Column(Name = "id", IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    /// <summary>
    /// 案件id
    /// </summary>
    [Column(Name = "case_id")]
    public string CaseId { get; set; } = null!;

    /// <summary>
    /// 关联的案件id
    /// </summary>
    [Column(Name = "related_case_id")]
    public string RelatedCaseId { get; set; } = null!;

    /// <summary>
    /// 关联类型
    /// </summary>
    [Column(Name = "related_type")]
    public string RelatedType { get; set; } = null!;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Column(Name = "creation_time", ServerTime = DateTimeKind.Local)]
    public DateTime CreationTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [Column(Name = "update_time", ServerTime = DateTimeKind.Local)]
    public DateTime UpdateTime { get; set; }

    /// <summary>
    /// 开案id
    /// </summary>
    [Column(Name = "apply_id")]
    public string ApplyId { get; set; } = null!;

    /// <summary>
    /// 是否新案
    /// </summary>
    [Column(Name = "new_case")]
    public bool IsNewCase { get; set; }
}