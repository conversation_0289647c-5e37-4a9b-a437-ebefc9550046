﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface IApplicantTypeRepository : 
    IBaseRepository<BasApplicantType, string>, 
    IScopeDependency,
    IRedisCacheableRepository<string, BasApplicantType>
{
    Task<BasApplicantType?> ICacheableRepository<string, BasApplicantType>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Where(type => type.ApplicantTypeId == key).WithLock().FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasApplicantType>> ICacheableRepository<string, BasApplicantType>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasApplicantType>.GenerateKey(BasApplicantType value)
    {
        return value.ApplicantTypeId;
    }
}