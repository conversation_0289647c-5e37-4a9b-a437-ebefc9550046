﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.Public.Applications.Commands.Statistics;

/// <summary>
/// 催稿通知
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="DateType">日期类型：1-初稿期限内 2-初稿期限外 3-定稿期限内 4-定稿期限外 5-官方期限</param>
/// <param name="WarningType">警告类型:1-邮件 2-企微</param>
/// <param name="Warning">提示语</param>
public record UrgentDraftCommand([Required(ErrorMessage = "任务id是必须的")] List<string> ProcId, int DateType, List<int> WarningType, string? Warning) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

