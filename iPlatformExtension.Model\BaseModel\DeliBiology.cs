using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_biology", DisableSyncStructure = true)]
	public partial class DeliBiology {

		[ Column(Name = "biology_address", StringLength = 2000)]
		public string BiologyAddress { get; set; }

		[ Column(Name = "biology_class", StringLength = 50)]
		public string BiologyClass { get; set; }

		[ Column(Name = "biology_date")]
		public DateTime? BiologyDate { get; set; }

		[ Column(Name = "biology_id", StringLength = 50)]
		public string BiologyId { get; set; }

		[ Column(Name = "biology_islive")]
		public bool? BiologyIslive { get; set; }

		[ Column(Name = "biology_serial", StringLength = 50)]
		public string BiologySerial { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; }

		[ Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
		public string ProcId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
