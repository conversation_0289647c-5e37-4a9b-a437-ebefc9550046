﻿using Confluent.Kafka;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Options;
using MongoDB.Bson;
using System.Diagnostics;
using System.Linq.Expressions;
using System.Net;
using System.Text;
using static Hangfire.Storage.JobStorageFeatures;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public class FlowAnalyseCommandHandler : INotificationHandler<FlowAnalyseCommand>//, IScopeDependency
    {

        private readonly IFreeSql _freeSql;
        private readonly ILogger<FlowAnalyseCommandHandler> _logger;

        private readonly HubConnection _hubConnection;

        public FlowAnalyseCommandHandler(IFreeSql freeSql, HubConnection hubConnection, ILogger<FlowAnalyseCommandHandler> logger)
        {
            _freeSql = freeSql;
            _hubConnection = hubConnection;
            _logger = logger;
        }



        public async Task Handle(FlowAnalyseCommand request, CancellationToken cancellationToken)
        {
            //计算

            try
            {
                var totalCount = 0L;
                var list = new List<FlowAnalyseDto>();

                //总待办数量
                var lstTotal = _freeSql.Select<SysFlowActivity>().WithLock().Where((o) => o.FlowType == "DE" && o.FlowSubType == "TII" && o.Status == FLOW_STATUS.S1000)
                    .GroupBy(o => o.CurUserId).Select(o =>
                    new FlowAnalyseDto
                    {
                        Count = o.Count(),
                        UserId = o.Value.CurUserId,
                        Type = "Total".ToString()
                    });

                list.AddRange(lstTotal);

                //待递交
                var lstReady = _freeSql.Select<SysFlowActivity>().WithLock().Where((o) => o.FlowType == "DE" && o.FlowSubType == "TII" && o.CaseProcInfo.DeliInfo.Status == (int)DeliveryStatus.Ready && o.CaseProcInfo.DeliInfo.OperationResult == true)
                    .GroupBy(o => o.CurUserId).Select(o =>
                    new FlowAnalyseDto
                    {
                        Count = o.Count(),
                        UserId = o.Value.CurUserId,
                        Type = ((int)DeliveryStatus.Ready).ToString()
                    });

                list.AddRange(lstReady);

                //已下单
                var lstOrdered = _freeSql.Select<SysFlowActivity>().WithLock().Where((o) => o.FlowType == "DE" && o.FlowSubType == "TII" && (o.CaseProcInfo.DeliInfo.Status == (int)DeliveryStatus.Ordered || o.CaseProcInfo.DeliInfo.Status == (int)DeliveryStatus.Delivering) && o.CaseProcInfo.DeliInfo.OperationResult == true).GroupBy(o => o.CurUserId).Select(o =>
                    new FlowAnalyseDto
                    {
                        Count = o.Count(),
                        UserId = o.Value.CurUserId,
                        Type = ((int)DeliveryStatus.Ordered).ToString()
                    });

                list.AddRange(lstOrdered);

                //失败
                var lstError = _freeSql.Select<SysFlowActivity>().WithLock().Where((o) => o.FlowType == "DE" && o.FlowSubType == "TII" && o.CaseProcInfo.DeliInfo.OperationResult == false).GroupBy(o => o.CurUserId).Select(o =>
                 new FlowAnalyseDto
                 {
                     Count = o.Count(),
                     UserId = o.Value.CurUserId,
                     Type = ((int)DeliveryStatus.Error).ToString()
                 });

                list.AddRange(lstError);


                //已递交

                var lstComplete = _freeSql.Select<SysFlowActivity>().WithLock().Where((o) => o.FlowType == "DE" && o.FlowSubType == "TII" && o.CurFlowNode.NodeCode == "TII_DE_End" && o.CaseProcInfo.DeliInfo.OperationResult == true && o.CaseProcInfo.DeliInfo.Status < (int)DeliveryStatus.Confirmed).GroupBy(o => o.CurUserId).Select(o =>
               new FlowAnalyseDto
               {
                   Count = o.Count(),
                   UserId = o.Value.CurUserId,
                   Type = ((int)DeliveryStatus.Complete).ToString()
               });

                list.AddRange(lstComplete);

                //已完成

                var lstConfirmed = _freeSql.Select<SysFlowActivity>().WithLock().Where((o) => o.CurFlowNode.NodeCode == "TII_DE_End" && o.CaseProcInfo.DeliInfo.OperationResult == true && o.CaseProcInfo.DeliInfo.Status == (int)DeliveryStatus.Confirmed).GroupBy(o => o.CurUserId).Select(o =>
               new FlowAnalyseDto
               {
                   Count = o.Count(),
                   UserId = o.Value.CurUserId,
                   Type = ((int)DeliveryStatus.Confirmed).ToString()
               });

                list.AddRange(lstConfirmed);

               // string data = list.ToJson();
                if (_hubConnection.State == HubConnectionState.Connected)
                {
                  await  _hubConnection.InvokeAsync("NotificationFlowMessageAsync", "ToDoMessage", list);
                }

                
            }
            catch (Exception ex)
            {
                _logger.LogError($"流程统计执行失败！message:{ex.Message}");
            }
        }
    }
}
