using iPlatformExtension.MailCenter.Applications.Models.Template;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.Template
{
    /// <summary>
    /// 获取邮件模板列表查询
    /// </summary>
    public class GetMailTemplateListQuery : QueryBase, IRequest<PageResult<GetMailTemplateListDto>>
    {
        /// <summary>
        /// 搜索条件（支持模板编号、模板名称模糊查询）
        /// </summary>
        public string? Search { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public sbyte? IsEnabled { get; set; }
    }
}
