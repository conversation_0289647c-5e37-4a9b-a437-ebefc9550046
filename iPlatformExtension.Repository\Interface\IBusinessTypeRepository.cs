﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Repository.Interface;

public interface IBusinessTypeRepository : 
    IBaseRepository<BasBusinessType, string>, 
    IScopeDependency, 
    IStringKeyCacheableRepository<BusinessTypeInfo>, 
    IRedisCacheableRepository<string, BusinessTypeInfo>
{
    Task<BusinessTypeInfo?> ICacheableRepository<string, BusinessTypeInfo>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Orm.Select<BasBusinessType>().WithLock().Where(businessType => businessType.BusinessTypeId == key)
            .ToOneAsync(type => new BusinessTypeInfo(type.BusinessTypeId, type.BusinessTypeZhCn, type.BusinessTypeEnUs,
                type.BusinessTypeCode), cancellationToken)!;
    }

    async Task<IEnumerable<BusinessTypeInfo>> ICacheableRepository<string, BusinessTypeInfo>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Orm.Select<BasBusinessType>().WithLock()
            .ToListAsync(type => new BusinessTypeInfo(type.BusinessTypeId, type.BusinessTypeZhCn, type.BusinessTypeEnUs,
            type.BusinessTypeCode), cancellationToken)!;
    }

    string IStringKeyCacheableRepository<BusinessTypeInfo>.GetCacheTextValue(BusinessTypeInfo value)
    {
        return value.CnName ?? string.Empty;
    }

    string ICacheableRepository<string, BusinessTypeInfo>.GenerateKey(BusinessTypeInfo value)
    {
        return value.Id;
    }
}