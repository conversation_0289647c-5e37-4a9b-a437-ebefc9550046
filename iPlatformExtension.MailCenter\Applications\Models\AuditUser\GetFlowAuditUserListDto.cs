namespace iPlatformExtension.MailCenter.Applications.Models.AuditUser
{
    /// <summary>
    /// 发件审核人列表DTO
    /// </summary>
    public class GetFlowAuditUserListDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 审核人信息
        /// </summary>
        public object AuditingUser { get; set; }

        /// <summary>
        /// 所在部门
        /// </summary>
        public string Department { get; set; }

        /// <summary>
        /// 在职状态
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime? CreateTime { get; set; }
        
        /// <summary>
        /// 添加人
        /// </summary>
        public object CreateUser { get; set; }
    }
}
