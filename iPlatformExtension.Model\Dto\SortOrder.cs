﻿using System.ComponentModel;
using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 排序次序
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter<SortOrder>))]
public enum SortOrder
{
    /// <summary>
    /// 正序
    /// </summary>
    [Description("正序")]
    Ascending = 0,
    
    /// <summary>
    /// 反序
    /// </summary>
    [Description("反序")]
    Descending = 1,
}