using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_district", DisableSyncStructure = true)]
	public partial class BasDistrict {

		[ Column(Name = "district_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DistrictId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "crm_value", StringLength = 50)]
		public string CrmValue { get; set; }

		[ Column(Name = "district_code", StringLength = 50, IsNullable = false)]
		public string DistrictCode { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "is_manage")]
		public bool IsManage { get; set; } = false;

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "text_en_us", StringLength = 50)]
		public string TextEnUs { get; set; }

		[ Column(Name = "text_ja_jp", StringLength = 50)]
		public string TextJaJp { get; set; }

		[ Column(Name = "text_zh_cn", StringLength = 50)]
		public string TextZhCn { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
