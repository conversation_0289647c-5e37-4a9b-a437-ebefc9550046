﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using CSScriptLib;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    public class GetMyHostListHandler(IFreeSql<PlatformFreeSql> msSql, IHttpContextAccessor httpContextAccessor, IMailHostRepository mailHostRepository) : IRequestHandler<GetMyHostListQuery, PageResult<GetMailHostListDto>>
    {

        public async Task<PageResult<GetMailHostListDto>> Handle(GetMyHostListQuery request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;

            var userinfo = await msSql.Select<SysUserInfo>().Where(o => o.UserId == userId).ToOneAsync(cancellationToken);

            var hostIds = await mailHostRepository.Orm.Select<MailAccess>()
                .WhereIf(request.AccessMode != HostAccessModeEnum.Manager && request.AccessMode != HostAccessModeEnum.All && request.AccessMode != HostAccessModeEnum.OnlyPublic,
                o => (o.UseType == UseTypeEnum.User && o.UseId == userId || o.UseType == UseTypeEnum.Dept && o.UseId == userinfo.DeptId)
                && o.AccessMode == request.AccessMode)
                .WhereIf(request.AccessMode == HostAccessModeEnum.Manager,
                    o => (o.UseType == UseTypeEnum.User && o.UseId == userId) && o.AccessMode == HostAccessModeEnum.Manager)

                .ToListAsync(o => o.HostId);


            var hostList = await mailHostRepository
                .WhereIf(request.AccessMode == HostAccessModeEnum.All, o => !string.IsNullOrEmpty(o.Account))
                .WhereIf(request.AccessMode == HostAccessModeEnum.OnlyPublic, o => !o.IsPrivate)
                .WhereIf(request.AccessMode != HostAccessModeEnum.OnlyPublic && request.AccessMode != HostAccessModeEnum.All && request.AccessMode != HostAccessModeEnum.Sorter, o => hostIds.Any(h => h == o.HostId) )
                .WhereIf(request.AccessMode == HostAccessModeEnum.Sorter, o => hostIds.Any(h => h == o.HostId) || (o.IsPrivate && o.PrivateUserId == userId))
                .WhereIf(!string.IsNullOrWhiteSpace(request.Search), o => o.Account.Contains(request.Search) || o.ShowName.Contains(request.Search))
                .Count(out long totalCount)
                .Take(request.PageSize).Skip(request.PageSize * (request.PageIndex - 1))
                .OrderByDescending(o => o.IsEnabled)
                .ToListAsync<GetMailHostListDto>();
            return new PageResult<GetMailHostListDto>()
            {
                Data = hostList,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };
        }
    }
}
