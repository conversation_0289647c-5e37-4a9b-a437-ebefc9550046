﻿namespace iPlatformExtension.MailCenter.Applications.Models.SysConfig;

/// <summary>
/// 获取解析规则详情
/// </summary>
/// <param name="FilterId">过滤详情主键id</param>
/// <param name="FilterHead">规则执行对象:mail_from:发件人,title:邮件主题,case_direction:邮件相关案件流向,mail_content:邮件正文,mail_sign:邮件签名</param>
/// <param name="FilterType">规则逻辑:index:包含,equal:等于</param>
/// <param name="FilterValue">规则内容</param>
/// <param name="Seq">排序</param>
public record GetAnalysisRuleDetailDto(string FilterId, string FilterHead, string FilterType, string FilterValue, int? Seq);

