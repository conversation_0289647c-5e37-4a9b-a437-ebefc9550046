﻿using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using iPlatformExtension.Outsourcing.Infrastructure.Extensions;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class SupplierProcOutsourcingResultHandler(IForeignSupplierRepository foreignSupplierRepository) 
    : IProcOutsourcingResultHandler
{
    public ValueTask<bool> MatchAsync(ProcOutsourcingResultNotification notification, CancellationToken cancellationToken)
    {
        return ValueTask.FromResult(!string.IsNullOrWhiteSpace(notification.Result.SupplierId));
    }

    public async Task HandleAsync(ProcOutsourcingResultNotification notification, CancellationToken cancellationToken)
    {
        var dto = notification.Result;
        var foreignSupplier = await foreignSupplierRepository.GetCacheValueAsync(dto.SupplierId ?? string.Empty, cancellationToken: cancellationToken);
        if (foreignSupplier is not null)
        {
            dto.AgencyInfo = foreignSupplier.CreateForeignAgencyInfo();
        }
    }
}