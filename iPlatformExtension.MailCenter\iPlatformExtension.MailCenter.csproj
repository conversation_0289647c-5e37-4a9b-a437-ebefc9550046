﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="HtmlAgilityPack" Version="1.12.1" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.3" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.4" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.5" />
    <PackageReference Include="ModelContextProtocol.AspNetCore" Version="0.1.0-preview.10" />
    <PackageReference Include="nacos-sdk-csharp.AspNetCore" Version="1.3.5" />
    <PackageReference Include="nacos-sdk-csharp.Extensions.Configuration" Version="1.3.5" />
    <PackageReference Include="NLog.DiagnosticSource" Version="5.2.0" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\iPlatformExtension.MailCenterRepository\iPlatformExtension.MailCenterRepository.csproj" />
    <ProjectReference Include="..\iPlatformExtension.Repository\iPlatformExtension.Repository.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="appsettings.Local.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="appsettings.Staging.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Update="Applications\GenerateCode\CommandCode.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>CommandCode.cs</LastGenOutput>
    </None>
    <None Update="Applications\GenerateCode\QueryCode.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>QueryCode.cs</LastGenOutput>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Applications\GenerateCode\CommandCode.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>CommandCode.tt</DependentUpon>
    </Compile>
    <Compile Update="Applications\GenerateCode\QueryCode.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>QueryCode.tt</DependentUpon>
    </Compile>
  </ItemGroup>

</Project>
