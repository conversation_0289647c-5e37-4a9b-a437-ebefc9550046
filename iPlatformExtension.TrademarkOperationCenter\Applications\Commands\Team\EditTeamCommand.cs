﻿using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using MediatR;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;

/// <summary>
/// 编辑团队命令
/// </summary>
/// <param name="TeamName">团队名称</param>
/// <param name="TeamDescription">团队说明</param>
/// <param name="IsExclusive">是否专属</param>
/// <param name="IsEffect">是否生效</param>
/// <param name="TeamId">团队id</param>
/// <param name="CustomerId">客户id</param>
/// <param name="Members">团队成员</param>
/// <param name="Seq">排序</param>
/// <param name="AuthorizeUser">授权用户,多个使用;拼接</param>
public record EditTeamCommand(string TeamName, string? TeamDescription, bool IsExclusive, bool IsEffect, [Required(ErrorMessage = "团队Id为必须")] string TeamId,
    List<string>? CustomerId, IEnumerable<MemberInput>? Members,int? Seq,string? AuthorizeUser) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

