﻿using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class WithdrawDeliveryButtonQueryHandler : IRequestHandler<WithdrawDeliveryButtonQuery, bool>
{

    public Task<bool> Handle(WithdrawDeliveryButtonQuery request, CancellationToken cancellationToken)
    {
        
        bool result;
        var deliveryInfo = request.DeliveryInfo;
        var deliveryStatus = (DeliveryStatus) (deliveryInfo.Status ?? 0);
        
        
        
        switch (deliveryStatus)
        {
            case DeliveryStatus.Confirmed:
            case DeliveryStatus.Complete:
                result = true;
                break;
            case DeliveryStatus.Delivering:
            case DeliveryStatus.Ordered:
            case DeliveryStatus.Stopped:
            case DeliveryStatus.Ready:
            default:
                result = false;
                break;
        }

        return Task.FromResult(result && (deliveryInfo.IsAuto ?? false));
    }
}