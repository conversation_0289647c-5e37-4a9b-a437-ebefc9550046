﻿using System.Collections;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 汇率缓存键
/// </summary>
/// <param name="Currency">当前币种</param>
/// <param name="TargetCurrency">目标币种</param>
public record struct CurrencyRateKey(string Currency, string TargetCurrency);

/// <summary>
/// 汇率等值比较器
/// </summary>
public struct CurrencyRateKeyEqualityComparer : IEqualityComparer<CurrencyRateKey>
{
    public bool Equals(CurrencyRateKey x, CurrencyRateKey y)
    {
        return x.Currency == y.Currency && x.TargetCurrency == y.TargetCurrency;
    }

    public int GetHashCode(CurrencyRateKey obj)
    {
        var hasCode = obj.Currency.GetHashCode() ^ obj.TargetCurrency.GetHashCode();
        return (hasCode << 16) ^ hasCode;
    }
}