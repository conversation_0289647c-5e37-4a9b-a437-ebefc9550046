using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "hr_zkt_user_setting", DisableSyncStructure = true)]
	public partial class HrZktUserSetting {

		[ Column(Name = "cur_id", StringLength = 50, IsNullable = false)]
		public string CurId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_partner")]
		public bool IsPartner { get; set; } = false;

		[ Column(Name = "is_suckle")]
		public bool IsSuckle { get; set; } = false;

		[ Column(Name = "setting_id", StringLength = 50)]
		public string SettingId { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
