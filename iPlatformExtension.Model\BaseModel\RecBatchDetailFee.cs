using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rec_batch_detail_fee", DisableSyncStructure = true)]
	public partial class RecBatchDetailFee {

		[ Column(Name = "key_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string KeyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "amount", StringLength = 50)]
		public string Amount { get; set; }

		[ Column(Name = "fee_id", StringLength = 50)]
		public string FeeId { get; set; }

		[ Column(Name = "fee_name", StringLength = 50)]
		public string FeeName { get; set; }

		[ Column(Name = "info_id", StringLength = 50, IsNullable = false)]
		public string InfoId { get; set; }

		[ Column(Name = "pay_office_serial", StringLength = 50)]
		public string PayOfficeSerial { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

	}

}
