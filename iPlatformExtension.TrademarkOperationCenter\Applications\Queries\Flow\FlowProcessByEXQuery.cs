﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using MediatR;
using System.Linq.Expressions;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow
{
    public class FlowProcessByEXQuery : QueryBase, IRequest<PageResult<GetFlowProcessDto>>
    {
        public Expression<Func<SysFlowActivity, bool>> whereCommon { get; set; }
        public Expression<Func<SysFlowActivity, bool>> whereTodo { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        public string Sort { get; set; } = "UpdateTime";

        /// <summary>
        /// 生效排序字段正序/倒序
        /// true:正序
        /// false:倒序
        /// </summary>
        public bool IsAscending { get; set; } = false;

        /// <summary>
        /// 模糊查询:案件名称
        /// </summary>
        public string? SearchKey { get; set; }
    }
}
