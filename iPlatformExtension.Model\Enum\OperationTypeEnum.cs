﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Enum
{
    public enum OperationTypeEnum
    {
        [Description("添加")]
        Add,
        [Description("忽略")]
        Ignore,
        [Description("删除")]
        Delete,
        [Description("分拣")]
        Sort,
        [Description("提交")]
        Submit,
        [Description("退回")]
        Reject,
        [Description("移交")]
        Transfer,
        [Description("发件审核")]
        SentAudit,
        //[Description("定时发送")]
        //ScheduledSent,
        //[Description("发送失败")]
        //SendError,
        [Description("已发送")]
        Send,
        //[Description("发件前添加阅读人")]
        //SentAddReader,
        [Description("发件后添加阅读人")]
        SendAddReader,
        [Description("发件阅读删除")]
        SendDelReader,
        //[Description("修改发件时间")]
        //UpdateSentTime,
        [Description("发件退回")]
        SendReject,
        [Description("发件作废")]
        SentInvalid,
    }
}
