namespace iPlatformExtension.Common.Clients.IPlatformWeb.Options;

/// <summary>
/// jwt��֤����
/// </summary>
public class PlatformJwtOptions
{
    /// <summary>
    /// token�䷢��
    /// </summary>
    public string Issuer { get; set; } = null!;

    /// <summary>
    /// token����
    /// </summary>
    public string Audience { get; set; } = null!;

    /// <summary>
    /// ǩ����Կ
    /// </summary>
    public string SecretKey { get; set; } = null!;

    /// <summary>
    /// token��Чʱ��
    /// </summary>
    public TimeSpan TokenLifeTime { get; set; }

    /// <summary>
    /// token��֤������ʱ�䡣�����������ʱ��
    /// </summary>
    public DateTime? NotBefore { get; set; }
}