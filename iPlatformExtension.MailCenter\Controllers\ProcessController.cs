﻿using iPlatformExtension.MailCenter.Applications.Models.Process;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Process;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.MailCenter.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class ProcessController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// 收件办理列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("CollectionProcessList")]
        public async Task<IEnumerable<CollectionProcessListDto>> CollectionProcessList([FromQuery] CollectionProcessListQuery query)
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 发件待审清单列表
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>发件待审清单列表</returns>
        [HttpGet("SendProcessList")]
        public async Task<IEnumerable<SendProcessListDto>> SendProcessList([FromQuery] SendProcessListQuery query)
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 最近7天办理
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("RecentlyProcessed")]
        public async Task<IEnumerable<RecentlyProcessedDto>> RecentlyProcessed([FromQuery] RecentlyProcessedQuery query)
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 最近7天办理发件待审清单
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>最近7天办理发件待审清单</returns>
        [HttpGet("RecentlySendProcessed")]
        public async Task<IEnumerable<SendProcessListDto>> RecentlySendProcessed([FromQuery] RecentlySendProcessedQuery query)
        {
            return await mediator.Send(query);
        }


    }
}
