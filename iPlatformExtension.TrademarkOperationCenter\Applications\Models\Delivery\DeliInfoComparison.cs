﻿using FreeSql.DataAnnotations;
using iPlatformExtension.Model.BaseModel;
using System.Text.Json.Serialization;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery {

	/// <summary>
	///  实时数据与快照内容对比载体
	/// </summary>
	public partial class DeliInfoComparison
    {

		[ Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ProcId { get; set; }

		[ Column(Name = "app_pages")]
		public int? AppPages { get; set; }

		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "case_name", StringLength = 500)]
		public string CaseName { get; set; }

		[ Column(Name = "case_name_en", StringLength = 500)]
		public string CaseNameEn { get; set; }

		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "cert_code", StringLength = 50)]
		public string CertCode { get; set; }

		[ Column(Name = "claims")]
		public int? Claims { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "cpc_file_no", StringLength = 50)]
		public string CpcFileNo { get; set; }

		[JsonIgnore]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "delay_review", StringLength = 50)]
		public string DelayReview { get; set; }

		[ Column(Name = "dependent_claims")]
		public int? DependentClaims { get; set; }

		[ Column(Name = "essence_exam_fr", StringLength = 10)]
		public string EssenceExamFr { get; set; }

		[ Column(Name = "essence_exam_fro", StringLength = 200)]
		public string EssenceExamFro { get; set; }

		[ Column(Name = "figure", StringLength = 50)]
		public string Figure { get; set; }

		[ Column(Name = "first_priority_date")]
		public DateTime? FirstPriorityDate { get; set; }

		[ Column(Name = "flow_cur_user_id", StringLength = 50)]
		public string FlowCurUserId { get; set; }

        [JsonIgnore]
        public DateTime? FlowUpdateTime { get; set; }

		[ Column(Name = "flow_update_user", StringLength = 50)]
		public string FlowUpdateUser { get; set; }

		[ Column(Name = "independent_claims")]
		public int? IndependentClaims { get; set; }

		[ Column(Name = "initial_app_date")]
		public DateTime? InitialAppDate { get; set; }

		[ Column(Name = "initial_app_no", StringLength = 50)]
		public string InitialAppNo { get; set; }

		[ Column(Name = "invalidate_code", StringLength = 50)]
		public string InvalidateCode { get; set; }

		[ Column(Name = "is_ahead_pub")]
		public bool? IsAheadPub { get; set; }

		[ Column(Name = "is_auto")]
		public bool? IsAuto { get; set; } = false;

		[ Column(Name = "is_en_name")]
		public bool? IsEnName { get; set; } = false;

		[ Column(Name = "is_essence_exam")]
		public bool? IsEssenceExam { get; set; }

		[ Column(Name = "is_fee_reduce")]
		public bool? IsFeeReduce { get; set; }

		[ Column(Name = "is_heredity")]
		public bool? IsHeredity { get; set; }

		[ Column(Name = "is_local_design")]
		public bool? IsLocalDesign { get; set; } = false;

		[ Column(Name = "is_priority_review")]
		public bool? IsPriorityReview { get; set; }

		[ Column(Name = "is_product")]
		public bool? IsProduct { get; set; } = false;

		[ Column(Name = "is_request_das")]
		public bool? IsRequestDas { get; set; } = false;

		[ Column(Name = "is_same_day")]
		public bool? IsSameDay { get; set; }

		[ Column(Name = "is_secrecy_request")]
		public bool? IsSecrecyRequest { get; set; }

		[ Column(Name = "is_seq")]
		public bool? IsSeq { get; set; }

		[ Column(Name = "is_similar")]
		public bool? IsSimilar { get; set; } = false;

		[ Column(Name = "novelity1")]
		public bool? Novelity1 { get; set; }

		[ Column(Name = "novelity2")]
		public bool? Novelity2 { get; set; }

		[ Column(Name = "novelity3")]
		public bool? Novelity3 { get; set; }

		[ Column(Name = "office_id", StringLength = 50)]
		public string OfficeId { get; set; }

		/// <summary>
		/// 官方期限
		/// </summary>
		[Column(Name = "official_deadline")]
		public DateTime? OfficialDeadline { get; set; }

		[ Column(Name = "pct_ahead0")]
		public bool? PctAhead0 { get; set; } = true;

		[ Column(Name = "pct_ahead1")]
		public bool? PctAhead1 { get; set; }

		[ Column(Name = "pct_app_date")]
		public DateTime? PctAppDate { get; set; }

		[ Column(Name = "pct_app_no", StringLength = 50)]
		public string PctAppNo { get; set; }

		[ Column(Name = "pct_declare", StringLength = 10)]
		public string PctDeclare { get; set; }

		[ Column(Name = "pct_declare10", StringLength = 50)]
		public string PctDeclare10 { get; set; }

		[ Column(Name = "pct_declare11", StringLength = 50)]
		public string PctDeclare11 { get; set; }

		[ Column(Name = "pct_declare12", StringLength = 50)]
		public string PctDeclare12 { get; set; }

		[ Column(Name = "pct_declare20", StringLength = 50)]
		public string PctDeclare20 { get; set; }

		[ Column(Name = "pct_declare21", StringLength = 50)]
		public string PctDeclare21 { get; set; }

		[ Column(Name = "pct_declare22", StringLength = 50)]
		public string PctDeclare22 { get; set; }

		[ Column(Name = "pct_declare23", StringLength = 50)]
		public string PctDeclare23 { get; set; }

		[ Column(Name = "pct_declare30", StringLength = 50)]
		public string PctDeclare30 { get; set; }

		[ Column(Name = "pct_declare31", StringLength = 50)]
		public string PctDeclare31 { get; set; }

		[ Column(Name = "pct_declare32", StringLength = 50)]
		public string PctDeclare32 { get; set; }

		[ Column(Name = "pct_declare40", StringLength = 50)]
		public string PctDeclare40 { get; set; }

		[ Column(Name = "pct_declare41", StringLength = 50)]
		public string PctDeclare41 { get; set; }

		[ Column(Name = "pct_declare42", StringLength = 50)]
		public string PctDeclare42 { get; set; }

		[ Column(Name = "pct_language", StringLength = 50)]
		public string PctLanguage { get; set; }

		[ Column(Name = "pct_pub_date")]
		public DateTime? PctPubDate { get; set; }

		[ Column(Name = "pct_pub_no", StringLength = 50)]
		public string PctPubNo { get; set; }

		[ Column(Name = "picture_count")]
		public int? PictureCount { get; set; }

		[ Column(Name = "product")]
		public int? Product { get; set; }

		[ Column(Name = "request_das", StringLength = 50)]
		public string RequestDas { get; set; }

		[ Column(Name = "similar")]
		public int? Similar { get; set; }

		[ Column(Name = "specification_pages")]
		public int? SpecificationPages { get; set; }

		[JsonIgnore]
		[ Column(Name = "status")]
		public int? Status { get; set; }

        [JsonIgnore]
        public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

        [JsonIgnore]
        public DateTime? UploadTime { get; set; }

        [JsonIgnore]
        public string UploadUserId { get; set; }

		/// <summary>
		/// 代理名义
		/// </summary>
		[Column(Name = "agency_name")]
		public string? AgencyName { get; set; }

		/// <summary>
		/// 联系人
		/// </summary>
		[Column(Name = "contact_person")]
		public string? ContactPerson { get; set; }

		/// <summary>
		/// 联系电话
		/// </summary>
		[Column(Name = "contact_tel")]
		public string? ContactTel { get; set; }

		/// <summary>
		/// 联系邮箱
		/// </summary>
		[Column(Name = "contact_mailbox")]
		public string? ContactMailBox { get; set; }

		/// <summary>
		/// 联系邮编
		/// </summary>
		[Column(Name = "contact_postcode")]
		public string? ContactPostCode { get; set; }

		/// <summary>
		/// 联系人地址
		/// </summary>
		[Column(Name = "contact_address")]
		public string? ContactAddress { get; set; }

		/// <summary>
		/// 描述
		/// </summary>
		[Column(Name = "description")]
		public string? Description { get; set; }

		/// <summary>
		/// 是否颜色组合
		/// </summary>
		[Column(Name = "is_multipart_color", IsNullable = false)]
		public bool IsMultipartColor { get; set; }

		/// <summary>
		/// 是否三维
		/// </summary>
		[Column(Name = "is_three_d", IsNullable = false)]
		public bool IsThreeD { get; set; }

		/// <summary>
		/// 是否声音
		/// </summary>
		[Column(Name = "is_voice", IsNullable = false)]
		public bool IsVoice { get; set; }
		
		/// <summary>
		/// 我方文号
		/// </summary>
		[Column(Name = "volume", StringLength = 50, IsNullable = false)]
		public string Volume { get; set; } = "";
		
		/// <summary>
		/// 任务编号
		/// </summary>
		[Column(Name = "proc_no", StringLength = 10, IsNullable = false)]
		public string ProcNo { get; set; } = "";
		
		/// <summary>
		/// 承办人Id
		/// </summary>
		[Column(Name = "undertaker_id", StringLength = 50, IsNullable = false)]
		public string UndertakerId { get; set; } = "";

		/// <summary>
		/// 标识种类
		/// </summary>
		[Column(Name = "marking_type")]
		public string? MarkingType { get; set; }

		/// <summary>
		/// 权大师订单号
		/// </summary>
		[Column(Name = "order_token")]
		public string OrderToken { get; set; } = string.Empty;

		/// <summary>
		/// 递交key
		/// </summary>
		public string? DeliveryKey { get; set; }

		/// <summary>
		/// 递交申请人信息
		/// </summary>
		public virtual ICollection<DeliApplicantComparison>? Applicants { get; set; }

		/// <summary>
		/// 递交优先权信息
		/// </summary>
		public virtual ICollection<DeliPriorityComparison>? Priorities { get; set; }

		// /// <summary>
		// /// 递交的案件关联信息
		// /// </summary>
		// [Navigate(nameof(CaseId))]
		// public virtual DeliCase? DeliveryCase { get; set; }

		/// <summary>
		/// 尼斯分类集合导航属性
		/// </summary>
		public virtual ICollection<DeliveryNiceCategoryComparison>? NiceCategories { get; set; }

		/// <summary>
		/// 递交文件
		/// </summary>
		//public virtual ICollection<DeliFilesComparison>? Files { get; set; }

	}

}
