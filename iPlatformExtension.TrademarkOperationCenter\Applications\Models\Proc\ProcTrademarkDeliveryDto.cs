﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;

/// <summary>
/// 任务递交信息
/// </summary>
public sealed class ProcTrademarkDeliveryDto
{
    /// <summary>
    /// 官方代理人
    /// </summary>
    public string? AgentUser { get; set; }
    
    /// <summary>
    /// 商标递交联系人联系电话
    /// </summary>
    public string? TrademarkDeliveryContactTel { get; set; }
    
    /// <summary>
    /// 商标递交联系人邮编
    /// </summary>
    public string? TrademarkDeliveryContactPostCode { get; set; }
    
    /// <summary>
    /// 商标递交联系人邮箱
    /// </summary>
    public string? TrademarkDeliveryContactMailbox { get; set; }
    
    /// <summary>
    /// 商标递交联系人名称
    /// </summary>
    public string? TrademarkDeliveryContactor { get; set; }
    
    /// <summary>
    /// 递交联系地址
    /// </summary>
    public string? TrademarkDeliveryContactAddressCn { get; set; }
    
    /// <summary>
    /// 境内代理机构id
    /// </summary>
    public string? TrademarkDeliveryAgencyId { get; set; }
    
    /// <summary>
    /// 递交key
    /// </summary>
    public string? DeliveryKey { get; set; }
    
    /// <summary>
    /// 申请类别
    /// </summary>
    public string? TrademarkNiceClasses { get; set; }

    /// <summary>
    /// 是否拆分
    /// </summary>
    public bool? IsSplit { get; set; }

    /// <summary>
    /// 名义变更类型
    /// </summary>
    public string? NominalChangesType { get; set; }
    
    /// <summary>
    /// 法律条款
    /// </summary>
    public string LawProvisions { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否涉及绝对理由
    /// </summary>
    public bool? HasAbsoluteReason { get; set; }

    /// <summary>
    /// 引证商标注册号
    /// </summary>
    public string CitedRegisterNumbers { get; set; } = string.Empty;

    /// <summary>
    /// 同意地址延及后续
    /// </summary>
    public bool? ExtendToSameAddress { get; set; }
		
    /// <summary>
    /// 许可类型
    /// </summary>
    public string LicenseType { get; set; } = string.Empty;

    /// <summary>
    /// 合同生效日期
    /// </summary>
    public DateOnly? ContractEffectiveDate { get; set; }

    /// <summary>
    /// 合同终止日期
    /// </summary>
    public DateOnly? ContractTerminationDate { get; set; }

    /// <summary>
    /// 是否保留补充材料权利
    /// </summary>
    public bool? ReservationOfSupplementaryMaterial { get; set; }

    /// <summary>
    /// 交官名称
    /// </summary>
    public string OfficialName { get; set; } = string.Empty;
}