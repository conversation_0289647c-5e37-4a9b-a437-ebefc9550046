﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkTransfer;

internal sealed class BuildTrademarkTransferInfoHandler(
    IHttpContextAccessor httpContextAccessor, 
    IDeliveryOtherInfoRepository otherInfoRepository) : 
    IMatchTrademarkProcCommandHandler<BuildOtherInfoCommand>
{
    async Task IMatchNotificationHandler<BuildOtherInfoCommand>.HandleAsync(BuildOtherInfoCommand notification, CancellationToken cancellationToken)
    {
        var procInfo = notification.ProcInfo;
        var operatorId = (httpContextAccessor.HttpContext?.User).GetUserId();
        
        var otherInfo = await otherInfoRepository.GetAsync(procInfo.ProcId, cancellationToken) ?? new DeliOtherInfo()
        {
            ProcId = procInfo.ProcId,
            CreationTime = DateTime.Now,
            Creator = operatorId
        };

        otherInfo.CtrlProcMark = procInfo.CtrlProcMark;
        otherInfo.Updater = operatorId;
        otherInfo.UpdateTime = DateTime.Now;
        otherInfo.TrademarkNiceClasses = string.Empty;
        
        await otherInfoRepository.InsertOrUpdateAsync(otherInfo, cancellationToken);
    }

    string IMatchTrademarkProcCommandHandler<BuildOtherInfoCommand>.CtrlProcId => CtrlProcIds.TrademarkTransfer;

    IEnumerable<string> IMatchTrademarkProcCommandHandler<BuildOtherInfoCommand>.CaseDirections => [CaseDirection.II];
}