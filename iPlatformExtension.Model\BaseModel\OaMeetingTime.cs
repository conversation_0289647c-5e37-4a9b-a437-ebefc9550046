using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "oa_meeting_time", DisableSyncStructure = true)]
	public partial class OaMeetingTime {

		/// <summary>
		/// 时间id
		/// </summary>
		[ Column(Name = "date_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DateId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 开始/结束时间，30分钟一段
		/// </summary>
		[ Column(Name = "date_time", StringLength = 50)]
		public string DateTime { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		/// <summary>
		/// 时间排序
		/// </summary>
		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
