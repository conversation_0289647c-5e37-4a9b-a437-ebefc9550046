﻿using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Applications.PipelineBehaviors.FeeQuery;

internal sealed class CheckFeeTypeParametersBehavior : CheckFeesQueryParametersBehaviorBase<BuildFeeTypesCommand>
{
    public CheckFeeTypeParametersBehavior(ILoggerFactory loggerFactory) : base(loggerFactory)
    {
    }

    public override bool Check(FeeQueryDto dto)
    {
        return dto.AnnualFeeType is not null;
    }
}