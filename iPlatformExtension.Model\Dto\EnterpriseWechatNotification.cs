using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 企业微信通知
/// </summary>
public class EnterpriseWechatNotification
{
    /// <summary>
    /// 应用编码
    /// </summary>
    [JsonPropertyName("serviceCode")]
    public string ApplicationCode { get; set; } = "Platform";

    /// <summary>
    /// 用户工号
    /// </summary>
    [JsonPropertyName("receivedUserAccount")]
    public string UserAccount { get; set; } = default!;

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 详情链接
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 按钮文案
    /// </summary>
    [JsonPropertyName("btntxt")]
    public string? ButtonText { get; set; }
}