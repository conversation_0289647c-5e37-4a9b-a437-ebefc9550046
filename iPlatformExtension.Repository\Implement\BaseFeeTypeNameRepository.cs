﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class BaseFeeTypeNameRepository(
    IFreeSql freeSql,
    UnitOfWorkManager uowManger,
    IMemoryCache memoryCache,
    DefaultRedisCache redisCache,
    CacheExpirationToken<FeeNameInfo> expirationToken)
    : DefaultRepository<BasFeeTypeName, string>(freeSql, uowManger), IBaseFeeTypeNameRepository
{
    IMemoryCache ICacheableRepository<string, FeeNameInfo>.MemoryCache => memoryCache;

    CacheExpirationToken<FeeNameInfo> ICacheableRepository<string, FeeNameInfo>.ExpirationToken => expirationToken;
    
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}