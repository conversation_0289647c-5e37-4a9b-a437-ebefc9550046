using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "file_directory_f", DisableSyncStructure = true)]
	public partial class FileDirectoryF {

		[ Column(Name = "id", IsPrimary = true, IsIdentity = true)]
		public int Id { get; set; }

		[ Column(Name = "path_name", StringLength = 50)]
		public string PathName { get; set; }

		[ Column(Name = "path_type", StringLength = 50)]
		public string PathType { get; set; }

		[ Column(Name = "remark", StringLength = 50)]
		public string Remark { get; set; }

		[ Column(Name = "status")]
		public int? Status { get; set; } = 0;

	}

}
