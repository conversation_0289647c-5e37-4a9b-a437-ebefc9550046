﻿using System.Text.Json.Serialization;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Dto;
using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Finance.Applications.Models.Fees;

/// <summary>
/// 费项查询列表项
/// </summary>
public record FeeListItemDto
{
    /// <summary>
    /// 费项id
    /// </summary>
    [ExcelColumn(Name = "费用标识ID", IndexName = "A")]
    public string FeeId { get; init; } = default!;

    /// <summary>
    /// 任务名称
    /// </summary>
    [ExcelColumn(Name = "任务名称", IndexName = "F")]
    public string? ProcName { get; set; }

    /// <summary>
    /// 我方文号
    /// </summary>
    [ExcelColumn(Name = "我方文号", IndexName = "B")]
    public string Volume { get; init; } = default!;

    /// <summary>
    /// 发票号
    /// </summary>
    [ExcelColumn(Name = "发票号")]
    public string InvoiceNo { get; set; } = string.Empty;

    /// <summary>
    /// 案件状态id
    /// </summary>
    [JsonIgnore]
    [ExcelIgnore]
    public string CaseStatusId { get; init; } = default!;

    /// <summary>
    /// 内部状态
    /// case_status_id:bas_case_status
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> CaseStatus { get; set; }

    /// <summary>
    /// 申请途径
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> ApplyChannel { get; set; }

    /// <summary>
    /// 内部状态名称
    /// </summary>
    [JsonIgnore]
    [ExcelColumn(Name = "内部状态", IndexName = "C")]
    public string CaseStatusName => CaseStatus.Value;

    /// <summary>
    /// 任务状态值
    /// </summary>
    [ExcelIgnore]
    [JsonIgnore]
    public string ProcStatusId { get; set; } = default!;

    /// <summary>
    /// 任务状态
    /// </summary>
    [JsonIgnore]
    [ExcelIgnore]
    public KeyValuePair<string, string> ProcStatus { get; set; } = default!;

    /// <summary>
    /// 任务状态
    /// </summary>
    [ExcelColumn(Name = "任务状态")]
    public string ProcStatusName => ProcStatus.Value;

    /// <summary>
    /// 客户名称
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> CustomerName { get; init; }

    /// <summary>
    /// 客户名称
    /// </summary>
    [JsonIgnore]
    [ExcelColumn(Name = "客户名称", IndexName = "E")]
    public string Customer => CustomerName.Value;

    /// <summary>
    /// 客户信息
    /// </summary>
    [ExcelIgnore]
    public CustomerInfo CustomerInfo { get; set; }

    /// <summary>
    /// 费用名称
    /// fee_type_name_id:bas_fee_type_name
    /// </summary>
    [ExcelIgnore]
    public FeeNameInfo FeeName { get; set; }

    /// <summary>
    /// 费用名称excel导出
    /// </summary>
    [JsonIgnore]
    [ExcelColumn(Name = "费用名称", IndexName = "G")]
    public string FeeTypeName => FeeName.CnName;

    /// <summary>
    /// 年费类型
    /// </summary>
    [ExcelColumn(Name = "年费分类")]
    [JsonIgnore]
    public string AnnualFeeType { get; set; } = string.Empty;

    /// <summary>
    /// 是否首缴年费
    /// </summary>
    [JsonIgnore]
    [ExcelIgnore]
    public bool IsFirstPayAnnual { get; set; }

    /// <summary>
    /// 费用名称id
    /// </summary>
    [JsonIgnore]
    [ExcelIgnore]
    public string FeeNameId { get; set; } = default!;

    /// <summary>
    /// 费用类型
    /// 字典表"fee_type"
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> FeeClass { get; set; }

    /// <summary>
    /// 费用类型名称
    /// </summary>
    [ExcelColumn(Name = "费用类型", IndexName = "H")]
    public string FeeClassName => FeeClass.Value;

    /// <summary>
    /// 币别
    /// </summary>
    [ExcelColumn(Name = "币别", IndexName = "I")]
    public string Currency { get; init; } = default!;

    /// <summary>
    /// 实际金额
    /// </summary>
    [ExcelColumn(Name = "实际金额", IndexName = "J")]
    public decimal Amount { get; init; }

    /// <summary>
    /// 申请号
    /// </summary>
    [ExcelColumn(Name = "申请号")]
    public string? AppNo { get; init; }

    /// <summary>
    /// 申请日
    /// </summary>
    [ExcelColumn(Name = "申请日", Format = "yyyy-MM-dd")]
    public DateTime? ApplicationDate { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [ExcelColumn(Name = "费用备注")]
    public string? Remark { get; set; }

    /// <summary>
    /// 案件备注
    /// </summary>
    [JsonIgnore]
    [ExcelColumn(Name = "案件备注")]
    public string CaseRemark { get; set; } = string.Empty;

    /// <summary>
    /// 收费规则
    /// 字典表"charge_rule"
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> ChargeRule { get; set; }

    /// <summary>
    /// 收费规则名称
    /// </summary>
    [ExcelColumn(Name = "收费规则", IndexName = "K")]
    [JsonIgnore]
    public string ChargeRuleName => ChargeRule.Value;

    /// <summary>
    /// 支付方式
    /// 字典表"pay_way"
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> PayWay { get; set; }

    /// <summary>
    /// 支付方式名称
    /// </summary>
    [ExcelColumn(Name = "支付方式", IndexName = "L")]
    [JsonIgnore]
    public string PayWayName => PayWay.Value;

    /// <summary>
    /// 付款状态
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> PayStatus { get; set; }

    /// <summary>
    /// 付款状态名称
    /// </summary>
    [ExcelColumn(Name = "付款状态", IndexName = "M")]
    [JsonIgnore]
    public string PayStatusName => PayStatus.Value;

    /// <summary>
    /// 进国家日期
    /// </summary>
    [ExcelIgnore]
    public DateTime? EntryDate { get; set; }

    /// <summary>
    /// 到款状态
    /// 字典表"receive_status"
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> ReceiverStatus { get; set; }

    /// <summary>
    /// 到款状态描述
    /// </summary>
    [ExcelColumn(Name = "到款状态")]
    [JsonIgnore]
    public string ReceiveStatusDescription => ReceiverStatus.Value;

    /// <summary>
    /// 缴官费状态
    /// 字典表"officer_status"
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string>? OfficerStatus { get; set; }

    /// <summary>
    /// 递交key
    /// 缴费key
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string>? DeliveryKey { get; set; }

    /// <summary>
    /// 缴费状态名称
    /// </summary>
    [ExcelColumn(Name = "缴费状态")]
    [JsonIgnore]
    public string OfficerStatusName => OfficerStatus?.Value ?? string.Empty;

    /// <summary>
    /// 官费标识
    /// </summary>
    [ExcelIgnore]
    public string? OfficialFeeMark { get; set; }

    /// <summary>
    /// 标识说明
    /// </summary>
    [ExcelIgnore]
    public string? OfficialFeeMarkDescription { get; set; }
    
    /// <summary>
    /// 缴费发文日
    /// </summary>
    [ExcelIgnore]
    public DateTime? OfficialPaymentPublicationDate { get; set; }

    /// <summary>
    /// 不缴费说明
    /// </summary>
    [ExcelIgnore]
    public string? NotPaymentInstructions { get; set; }
    
    /// <summary>
    /// 预请款日期
    /// </summary>
    [ExcelIgnore]
    [JsonIgnore]
    public DateTime? PreRequestDate { get; init; }

    /// <summary>
    /// 请款日期
    /// </summary>
    [ExcelColumn(Name = "请款日期", Format = "yyyy-MM-dd")]
    [JsonIgnore]
    public DateTime? RequestDate { get; set; }
    
    /// <summary>
    /// 应收日期
    /// </summary>
    [JsonIgnore]
    [ExcelIgnore]
    public DateTime? ReceiveDueDate { get; init; }

    /// <summary>
    /// 缴官费期限
    /// </summary>
    [ExcelColumn(Name = "缴费期限", IndexName = "P", Format = "yyyy-MM-dd")]
    public DateTime? PayOfficerLegalDate { get; set; }

    /// <summary>
    /// 缴官费通知书核查状态
    /// </summary>
    [ExcelIgnore]
    public bool OfficialNotificationChecked { get; set; }
    
    /// <summary>
    /// 待支出清单核查状态
    /// </summary>
    [ExcelIgnore]
    public bool PaymentListChecked { get; set; }
    
    /// <summary>
    /// 缴费日期
    /// </summary>
    [JsonIgnore]
    [ExcelColumn(Name = "缴费日期", IndexName = "Q", Format = "yyyy-MM-dd")]
    public DateTime? PayOfficerDate { get; init; }

    /// <summary>
    /// 请款单号
    /// </summary>
    [JsonIgnore]
    [ExcelColumn(Name = "请款单号")]
    public string BillNo { get; set; } = string.Empty;

    /// <summary>
    /// 创建日期
    /// </summary>
    [ExcelColumn(Name = "创建日期", Format = "yyyy-MM-dd")]
    public DateTime CreationTime { get; set; }

    /// <summary>
    /// 创建用户id
    /// </summary>
    [ExcelIgnore]
    [JsonIgnore]
    public string? CreatorId { get; set; }

    /// <summary>
    /// 创建用户姓名
    /// </summary>
    [JsonIgnore]
    [ExcelColumn(Name = "创建者")]
    public string CreatorName { get; set; } = string.Empty;

    /// <summary>
    /// 案件类型
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> CaseType { get; set; }

    /// <summary>
    /// 案件类型名称
    /// </summary>
    [ExcelColumn(Name = "案件类型", IndexName = "D")]
    public string CaseTypeName => CaseType.Value;
    
    /// <summary>
    /// 字典表"payment_name"
    /// 缴费名义
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string>? PaymentName { get; set; }

    /// <summary>
    /// 案件流向
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> CaseDirection { get; set; }

    /// <summary>
    /// 案件流向名称
    /// </summary>
    [ExcelColumn(Name = "案件流向")]
    [JsonIgnore]
    public string CaseDirectionName => CaseDirection.Value;

    /// <summary>
    /// 费项所属案件的主承办人
    /// </summary>
    [ExcelIgnore]
    public string Undertaker { get; set; } = string.Empty;

    /// <summary>
    /// 费项所属案件的承办人名字
    /// </summary>
    [JsonIgnore]
    [ExcelColumn(Name = "主承办人")]
    public string UndertakerName { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [ExcelIgnore]
    public string? TitularWriterId { get; set; }

    /// <summary>
    /// 名义承办人
    /// </summary>
    [ExcelColumn(Name = "名义承办人")]
    [JsonIgnore]
    public string TitularWriterName { get; set; } = string.Empty;

    

    /// <summary>
    /// 费项所属任务承办人
    /// </summary>
    [ExcelIgnore]
    public string ProcUndertaker { get; set; } = string.Empty;

    /// <summary>
    /// 任务编号
    /// </summary>
    [ExcelIgnore]
    public string? ProcNo { get; init; }

    /// <summary>
    /// 任务id
    /// </summary>
    [ExcelIgnore]
    public string ProcId { get; set; } = string.Empty;

    /// <summary>
    /// 客户文号
    /// </summary>
    [ExcelColumn(Name = "客户文号")]
    public string CustomerCaseNo { get; set; } = default!;

    /// <summary>
    /// 案件名称
    /// </summary>
    [ExcelColumn(Name = "案件名称")]
    public string CaseName { get; set; } = default!;

    /// <summary>
    /// 案件英文名称
    /// </summary>
    [ExcelIgnore]
    public string? CaseNameEn { get; set; }

    /// <summary>
    /// 案件id
    /// </summary>
    [ExcelIgnore]
    public string CaseId { get; set; } = default!;

    /// <summary>
    /// 销售（案源人）
    /// </summary>
    [ExcelIgnore]
    public string? AllocateSales { get; set; }

    /// <summary>
    /// 当前销售
    /// </summary>
    [ExcelIgnore]
    public string CurrentSales { get; set; } = string.Empty;

    /// <summary>
    /// 当前销售名称
    /// </summary>
    [ExcelColumn(Name = "客户销售")]
    [JsonIgnore]
    public string CurrentSalesName { get; set; } = string.Empty;

    /// <summary>
    /// 案件销售id
    /// </summary>
    [ExcelIgnore]
    [JsonIgnore]
    public string? CaseSalesId { get; set; }

    /// <summary>
    /// 案件销售
    /// </summary>
    [JsonIgnore]
    [ExcelColumn(Name = "案件销售")]
    public string CaseSalesName { get; set; } = string.Empty;

    /// <summary>
    /// 跟案人
    /// </summary>
    [ExcelIgnore]
    public string? AllocateTracker { get; set; }

    /// <summary>
    /// 当前跟案人
    /// </summary>
    [ExcelIgnore]
    public string CurrentTracker { get; set; } = default!;

    /// <summary>
    /// 售前名称
    /// </summary>
    [ExcelColumn(Name = "售前人员")]
    [JsonIgnore]
    public string CurrentTrackerName { get; set; } = string.Empty;

    /// <summary>
    /// 案源类别
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> CaseSourceType { get; set; }

    /// <summary>
    /// 费项上的案源类型
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> FeesCaseSourceType { get; set; }

    /// <summary>
    /// 到款日期
    /// </summary>
    [ExcelColumn(Name = "到款日期", Format = "yyyy-MM-dd")]
    public DateTime? ReceiveDate { get; set; }

    /// <summary>
    /// 申请类型
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> ApplyType { get; set; } = default!;

    /// <summary>
    /// 申请类型名称
    /// </summary>
    [JsonIgnore]
    [ExcelColumn(Name = "申请类型")]
    public string ApplyTypeName => ApplyType.Value;

    /// <summary>
    /// 商标申请类型
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> TrademarkApplicationType { get; set; }

    /// <summary>
    /// 案件业务类型id
    /// </summary>
    [JsonIgnore]
    [ExcelIgnore]
    public string? CaseBusinessTypeId { get; init; }

    /// <summary>
    /// 任务类型ID
    /// </summary>
    [JsonIgnore]
    [ExcelIgnore]
    public string? ProcBusinessTypeId { get; set; }

    /// <summary>
    /// 业务类型ID
    /// </summary>
    [JsonIgnore]
    [ExcelIgnore]
    public string? BusinessTypeId =>
        Model.Constants.CaseType.IsBelongPatent(CaseType.Key) ? CaseBusinessTypeId : ProcBusinessTypeId;

    /// <summary>
    /// 业务类型
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> BusinessType { get; set; } = default!;

    /// <summary>
    /// 业务类型信息
    /// </summary>
    [ExcelIgnore]
    [JsonIgnore]
    public BusinessTypeInfo? BusinessTypeInfo { get; set; }

    /// <summary>
    /// 业务类型名称
    /// </summary>
    [ExcelColumn(Name = "业务类型")]
    [JsonIgnore]
    public string BusinessTypeName => BusinessType.Value;

    /// <summary>
    /// 委案日期
    /// </summary>
    [ExcelColumn(Name = "委案日期", Format = "yyyy-MM-dd")]
    [JsonIgnore]
    public DateTime? EntrustDate { get; set; }

    /// <summary>
    /// 完成日
    /// </summary>
    [ExcelColumn(Name = "完成日", Format = "yyyy-MM-dd")]
    public DateTime? FinishDate { get; set; }

    /// <summary>
    /// 案源分所id
    /// </summary>
    [JsonIgnore]
    [ExcelIgnore]
    public string? BelongCompanyId { get; set; }

    /// <summary>
    /// 案源分所名称
    /// </summary>
    [ExcelColumn(Name = "案源分所")]
    [JsonIgnore]
    public string BelongCompanyName { get; set; } = string.Empty;

    /// <summary>
    /// 管理分所id
    /// </summary>
    [JsonIgnore]
    [ExcelIgnore]
    public string? ManageCompanyId { get; set; }

    /// <summary>
    /// 管理分所名称
    /// </summary>
    [ExcelColumn(Name = "管理分所")]
    [JsonIgnore]
    public string ManageCompanyName { get; set; } = string.Empty;

    /// <summary>
    /// 任务特性
    /// </summary>
    [ExcelIgnore]
    public KeyValuePair<string, string> ProcAttribute { get; set; } = default!;

    /// <summary>
    /// 任务分类名称
    /// </summary>
    [ExcelColumn(Name = "任务分类")]
    [JsonIgnore]
    public string ProcAttributeName => ProcAttribute.Value;

    /// <summary>
    /// 是否同日递交案
    /// </summary>
    [ExcelIgnore]
    public bool IsSameDayCase { get; set; }

    /// <summary>
    /// 送官方日
    /// </summary>
    [ExcelIgnore]
    public DateTime? SendOfficeDate { get; set; }

    /// <summary>
    /// 1+1套案申请
    /// </summary>
    [JsonIgnore]
    [ExcelColumn(Name = "1+1套案申请")]
    public string SameDayCase => IsSameDayCase.GetBooleanChineseDescription();

    /// <summary>
    /// 是否同时提实审
    /// </summary>
    [ExcelIgnore]
    [JsonIgnore]
    public bool IsEssenceExam { get; set; }

    /// <summary>
    /// 是否同时提实审
    /// </summary>
    [ExcelColumn(Name = "同时提实审")]
    [JsonIgnore]
    public string EssenceExam => IsEssenceExam.GetBooleanChineseDescription();

    /// <summary>
    /// 新申请递交日
    /// </summary>
    [JsonIgnore]
    [ExcelColumn(Name = "新申请递交日", Format = "yyyy-MM-dd")]
    public DateTime? DeliverDate { get; set; }

    /// <summary>
    /// 递交方式
    /// </summary>
    [ExcelIgnore]
    public string? DeliveryType { get; set; }

    /// <summary>
    /// 代理人处理状态编码
    /// </summary>
    [ExcelIgnore]
    [JsonIgnore]
    public string? SubProcStatusCode { get; set; }

    /// <summary>
    /// 代理人处理状态
    /// </summary>
    [ExcelColumn(Name = "代理人处理状态")]
    [JsonIgnore]
    public string SubProcStatusName { get; set; } = string.Empty;

    /// <summary>
    /// 划拨商务人员
    /// </summary>
    [ExcelIgnore]
    public string? AllocateBusinessUser { get; set; }

    /// <summary>
    /// 当前商务
    /// </summary>
    [ExcelIgnore]
    public string CurrentBusinessUser { get; set; } = string.Empty;

    /// <summary>
    /// 当前商务名称
    /// </summary>
    [ExcelColumn(Name = "商务")]
    [JsonIgnore]
    public string CurrentBusinessUserCnName { get; set; } = string.Empty;

    /// <summary>
    /// 承办部门id
    /// </summary>
    [ExcelIgnore]
    [JsonIgnore]
    public string? UndertakeDepartmentId { get; set; }

    /// <summary>
    /// 承办部门名称
    /// </summary>
    [ExcelColumn(Name = "主承办部门")]
    [JsonIgnore]
    public string UndertakeDepartmentName { get; set; } = string.Empty;

    /// <summary>
    /// 线索提报人
    /// </summary>
    [ExcelIgnore]
    public string? AllocateClueUser { get; set; }

    /// <summary>
    /// 当前线索提报人
    /// </summary>
    [ExcelIgnore]
    public string CurrentClueUser { get; set; } = default!;

    /// <summary>
    /// 国家编码
    /// </summary>
    [ExcelIgnore]
    public string CountryId { get; set; } = default!;

    /// <summary>
    /// 国家/地区名称
    /// </summary>
    [ExcelColumn(Name = "国家/地区")]
    [JsonIgnore]
    public string CountryName { get; set; } = string.Empty;

    /// <summary>
    /// 所属国家id
    /// </summary>
    [ExcelIgnore]
    [JsonIgnore]
    public string CustomerCountryId { get; set; } = default!;

    /// <summary>
    /// 所属国家
    /// </summary>
    [ExcelColumn(Name = "所属国家")]
    [JsonIgnore]
    public string CustomerCountryName { get; set; } = string.Empty;

    /// <summary>
    /// 任务类型id
    /// </summary>
    [JsonIgnore]
    [ExcelIgnore]
    public string CtrlProcId { get; set; } = default!;

    /// <summary>
    /// 任务类型信息
    /// </summary>
    [ExcelIgnore]
    public CtrlProcInfo CtrlProcInfo { get; set; }

    /// <summary>
    /// 商标分类
    /// </summary>
    [ExcelColumn(Name = "国际分类")]
    public string? TrademarkClass { get; set; }

    /// <summary>
    /// 商标注册号
    /// </summary>
    [ExcelIgnore]
    public string? TrademarkRegisterNo { get; set; }

    /// <summary>
    /// 申请人
    /// </summary>
    [ExcelIgnore]
    public IEnumerable<ApplicantInfo> Applicants { get; set; } = Array.Empty<ApplicantInfo>();

    /// <summary>
    /// 申请人（中文）
    /// </summary>
    [ExcelColumn(Name = "申请人（中文）")]
    [JsonIgnore]
    public string ApplicantCnNames => string.Join("，", Applicants.Select(info => info.ApplicantCnName));
    
    /// <summary>
    /// PCT国际申请号
    /// </summary>
    [ExcelIgnore]
    public string? PctApplyNo { get; set; }

    /// <summary>
    /// PCT国际申请日
    /// </summary>
    [ExcelIgnore]
    public DateTime? PctApplyDate { get; set; }

    /// <summary>
    /// 优先权信息
    /// </summary>
    [ExcelIgnore]
    public IEnumerable<PriorityInfo> Priorities { get; set; } = Array.Empty<PriorityInfo>();

    /// <summary>
    /// 发明人信息
    /// </summary>
    [ExcelIgnore]
    public IEnumerable<InventorInfo> Inventors { get; set; } = Array.Empty<InventorInfo>();

    /// <summary>
    /// 管控标识
    /// </summary>
    [ExcelIgnore]
    public IEnumerable<CustomerControlIdentifierInfo> CustomerControlIdentifiers { get; set; } = [];

    /// <summary>
    /// 缴费人名称
    /// </summary>
    [ExcelIgnore]
    public string? PaymentAgency { get; set; }
}