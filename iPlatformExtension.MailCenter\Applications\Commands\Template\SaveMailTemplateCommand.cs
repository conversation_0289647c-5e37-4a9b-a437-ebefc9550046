using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.MailCenter.Applications.Commands.Template
{
    /// <summary>
    /// 保存邮件模板命令
    /// </summary>
    /// <param name="TemplateId">模板ID（为空时新增，不为空时修改）</param>
    /// <param name="Name">模板名称</param>
    /// <param name="Title">邮件标题</param>
    /// <param name="Body">邮件正文</param>
    /// <param name="Sql2">SQL脚本</param>
    /// <param name="IsEnabled">是否启用</param>
    public record SaveMailTemplateCommand(
        string? TemplateId,
        [Required(ErrorMessage = "模板名称不能为空")] string Name,
        [Required(ErrorMessage = "邮件标题不能为空")] string Title,
        [Required(ErrorMessage = "邮件正文不能为空")] string Body,
        string? Sql2,
        sbyte? IsEnabled = 1
    ) : IRequest<string>, IUnitOfWorkCommandMysql;
}
