﻿using iPlatformExtension.Public.Applications.Models.DateCalculation;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using MediatR;

namespace iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;

/// <summary>
/// 计算
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="Field">字段名称：DurationOutpost/指示外所期限 DurationCustomer/返客户期限</param>
/// <param name="DateCal">期限内容</param>
/// <param name="CaseId">案件id</param>
public record CalDateQuery(string? ProcId, string Field, DateCalDto? DateCal, string? ApplyId, string? CaseId) : IRequest<CalDateDto>;

