﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.MailCenter.Controllers;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class SetFlowPrivateHandler(IMailFlowPrivateRepository mailFlowPrivateRepository, IHttpContextAccessor httpContextAccessor, IFlowPrivateListRepository flowPrivateListRepository) : IRequestHandler<SetFlowPirvateCommand>
    {
        public async Task Handle(SetFlowPirvateCommand request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userId);
            var userPrivates = await mailFlowPrivateRepository
                .Where(o => o.UserId == userId).ToListAsync(cancellationToken);
            if (userPrivates.Any())
            {
                await flowPrivateListRepository.Where(o => userPrivates.Any(p => p.Id == o.PrivateId) && request.info.MailIds.Any(m => m == o.MailId)).ToDelete().ExecuteAffrowsAsync(cancellationToken);

                List<FlowPrivateList> addList = new List<FlowPrivateList>();
                foreach (var mailId in request.info.MailIds)
                {
                    if (!string.IsNullOrEmpty(request.info.MailFlowPrivateId))
                    {
                        addList.Add(new FlowPrivateList()
                        {
                            Id = Guid.NewGuid().ToString(),
                            MailId = mailId,
                            PrivateId = request.info.MailFlowPrivateId
                        });
                    }
                }
                if (addList.Any())
                {
                    await flowPrivateListRepository.InsertAsync(addList);
                }
            }
        }
    }
}
