using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_currency_rate_list", DisableSyncStructure = true)]
	public partial class BillCurrencyRateList {

		[ Column(Name = "bill_currency_id", StringLength = 50)]
		public string Bill<PERSON>urrencyId { get; set; }

		[ Column(Name = "bill_exchange_rate", DbType = "decimal(21,6)")]
		public decimal? BillExchangeRate { get; set; }

		[ Column(Name = "bill_id", StringLength = 50, IsNullable = false)]
		public string BillId { get; set; }

		[ Column(Name = "fee_currency_id", StringLength = 50)]
		public string FeeCurrencyId { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
