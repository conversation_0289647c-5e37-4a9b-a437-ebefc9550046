﻿namespace iPlatformExtension.Model.Dto.CDC;

/// <summary>
/// 消息负载中的源信息
/// </summary>
public class CdcMessagePayloadSource
{
    /// <summary>
    /// 版本信息
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// cdc连接器
    /// </summary>
    public string Connector { get; set; } = string.Empty;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 触发的时间戳
    /// </summary>
    public long TsMs { get; set; }

    /// <summary>
    /// 是否快照
    /// </summary>
    public string Snapshot { get; set; } = string.Empty;

    /// <summary>
    /// 数据库名称
    /// </summary>
    public string Db { get; set; } = string.Empty;

    /// <summary>
    /// 数据库的架构
    /// </summary>
    public string Schema { get; set; } = string.Empty;

    /// <summary>
    /// 表名
    /// </summary>
    public string Table { get; set; } = string.Empty;

    /// <summary>
    /// 数据变更序列
    /// </summary>
    public string ChangeLsn { get; set; } = string.Empty;

    /// <summary>
    /// 提交序列
    /// </summary>
    public string CommitLsn { get; set; } = string.Empty;

    /// <summary>
    /// 事件序列号
    /// </summary>
    public int EventSerialNo { get; set; }
}