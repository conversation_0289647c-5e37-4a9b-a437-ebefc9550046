using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_country", DisableSyncStructure = true)]
	public partial class BasCountry {

		/// <summary>
		/// 国家主键ID
		/// </summary>
		[ Column(Name = "country_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CountryId { get; set; }

		/// <summary>
		/// 代码
		/// </summary>
		[ Column(Name = "country_code", StringLength = 50)]
		public string CountryCode { get; set; }

		/// <summary>
		/// 英文名称
		/// </summary>
		[ Column(Name = "country_en_us", StringLength = 100)]
		public string CountryEnUs { get; set; }

		/// <summary>
		/// 日文名称
		/// </summary>
		[ Column(Name = "country_ja_jp", StringLength = 100)]
		public string CountryJaJp { get; set; }

		/// <summary>
		/// 中文名称
		/// </summary>
		[ Column(Name = "country_zh_cn", StringLength = 50)]
		public string CountryZhCn { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 国家ID
		/// </summary>
		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_ep")]
		public bool? IsEp { get; set; } = false;

		/// <summary>
		/// 是否为马德里成员国
		/// </summary>
		[ Column(Name = "is_madrid")]
		public bool IsMadrid { get; set; } = false;

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// 大洲名称
		/// </summary>
		[Column(Name = "continent_cn")]
		public string? ContinentCn { get; set; }

		/// <summary>
		/// 区号
		/// </summary>
		[Column(Name = "country_calling_code")]
		public string? CountryCallingCode { get; set; }

		/// <summary>
		/// 商标递交国家编码
		/// </summary>
		[Column(Name = "trademark_delivery_code", StringLength = 20, IsNullable = false)]
		public string TrademarkDeliveryCode { get; set; } = string.Empty;

		/// <summary>
		/// 商标递交国家名称
		/// </summary>
		[Column(Name = "trademark_delivery_country_name", StringLength = 20, IsNullable = false)]
		public string TrademarkDeliveryCountryName { get; set; } = string.Empty;
	}

}
