using iPlatformExtension.MailService.Applications.Commands;
using iPlatformExtension.MailService.Extensions;
using MailKit;
using MailKit.Net.Imap;
using MailKit.Net.Smtp;
using MailKit.Security;
using MediatR;
using MimeKit;
using System.Security.Authentication;

namespace iPlatformExtension.MailService.Applications.Handlers
{
    /// <summary>
    /// 通过SMTP发送邮件的处理程序
    /// </summary>
    public class SendMailBySmtpHandler(
        ILogger<SendMailBySmtpHandler> logger
        ) : IRequestHandler<SendMailBySmtpCommand, bool>
    {
        public async Task<bool> Handle(SendMailBySmtpCommand request, CancellationToken cancellationToken)
        {
            int maxRetries = 3;
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    // 如果没有提供配置，则抛出异常
                    if (request.ConfigDto == null)
                    {
                        throw new ApplicationException($"邮件配置不能为空:{nameof(SendMailBySmtpCommand.ConfigDto)}");
                    }
                    // 创建邮件消息
                    var message = new MimeMessage();
                    message.From.Add(new MailboxAddress(request.ConfigDto.ShowName, request.ConfigDto.SenderAccount));

                    // 添加收件人
                    foreach (var toEmail in request.ToEmails)
                    {
                        message.To.Add(MailboxAddress.Parse(toEmail));
                    }

                    // 添加抄送人
                    if (request.CcEmails != null)
                    {
                        foreach (var ccEmail in request.CcEmails)
                        {
                            message.Cc.Add(MailboxAddress.Parse(ccEmail));
                        }
                    }

                    // 添加密送人
                    if (request.BccEmails != null)
                    {
                        foreach (var bccEmail in request.BccEmails)
                        {
                            message.Bcc.Add(MailboxAddress.Parse(bccEmail));
                        }
                    }

                    message.Subject = request.Subject;

                    // 创建邮件正文
                    var bodyBuilder = new BodyBuilder();
                    if (request.IsHtml)
                    {
                        bodyBuilder.HtmlBody = request.Body;
                    }
                    else
                    {
                        bodyBuilder.TextBody = request.Body;
                    }

                    // 添加附件
                    if (request.AttachmentPaths != null)
                    {
                        foreach (var attachmentPath in request.AttachmentPaths)
                        {
                            bodyBuilder.Attachments.Add(attachmentPath);
                        }
                    }

                    message.Body = bodyBuilder.ToMessageBody();
                    message.Headers.Add("Disposition-Notification-To", request.ConfigDto.SenderAccount);
                    message.Headers.Add("Return-Receipt-To", request.ConfigDto.SenderAccount);

                    using (var client = new SmtpClient())
                    {
                        client.Timeout = 300000; // 设置超时时间
                        // 配置SMTP服务器设置
                        client.ServerCertificateValidationCallback = (s, c, h, e) => true; // 忽略SSL证书验证（仅用于测试，生产环境请确保安全性）
                        client.Connect(request.ConfigDto.SmtpHost, request.ConfigDto.SmtpPort.Value, MailKit.Security.SecureSocketOptions.StartTlsWhenAvailable); // 连接到SMTP服务器

                        // 如果SMTP服务器需要身份验证，请提供用户名和密码
                        await client.AuthenticateAsync(request.ConfigDto.SenderAccount, request.ConfigDto.SenderPassword);

                        // 发送邮件
                        var res = await client.SendAsync(message);
                        Console.WriteLine($"{message.Subject}邮件发送结果:{res}");
                        // 断开连接
                        await client.DisconnectAsync(true);
                    }

                    logger.LogInformation($"邮件发送成功：主题 {request.Subject}");
                    return true;
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"邮件发送失败：{ex.Message}");
                    if (i == maxRetries - 1)
                    {
                        Console.WriteLine($"{request.Subject}更新失败。");
                        logger.LogError(ex, $"{request.Subject}更新失败。");
                    }
                    Console.WriteLine($"{request.Subject}进入重试{maxRetries}");
                    // 指数退避重试
                    await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, i)));
                   
                }
            }
            return false;
        }
    }
}