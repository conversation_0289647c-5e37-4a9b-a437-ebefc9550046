using FreeSql;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class BuildForeignBillQueryHandler(IFreeSql freeSql) : 
    IRequestHandler<BuildForeignBillQueryCommand>, IFeesQueryStatementBuilder
{
    public async Task Handle(BuildForeignBillQueryCommand request, CancellationToken cancellationToken)
    {
        var (dto, feesQuery) = request;
        List<string> bankIds = [];
        
        if (dto.BankAccountNumbers.Any())
        {
            bankIds.AddRange(await freeSql.Select<CusBank>().WithLock()
                .Where(bank => dto.BankAccountNumbers.Contains(bank.AccountNumber))
                .ToListAsync(bank => bank.BankId, cancellationToken));
        }
        
        BuildFeesQueryStatement(dto, feesQuery, bankIds);
    }

    public static void BuildFeesQueryStatement(FeeQueryDto dto, ISelect<CaseFeeList> feesQuery, params object?[] args)
    {
        var bankIds = args[0] as List<string>;
        
        
    }
}