using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_contact", DisableSyncStructure = true)]
	public partial class CusContact {

		[ Column(Name = "contact_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ContactId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "address_cn", StringLength = 500)]
		public string AddressCn { get; set; }

		[ Column(Name = "address_en", StringLength = 500)]
		public string AddressEn { get; set; }

		/// <summary>
		/// 称呼。 例如：
		/// </summary>
		[ Column(Name = "call_name", StringLength = 50)]
		public string CallName { get; set; }

		[ Column(Name = "contact_name", StringLength = 200)]
		public string ContactName { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string? CreateUserId { get; set; }

		[ Column(Name = "crm_contact_id", StringLength = 50)]
		public string CrmContactId { get; set; }

		[ Column(Name = "email", StringLength = 500)]
		public string Email { get; set; }

		[ Column(Name = "family_address", StringLength = 500)]
		public string FamilyAddress { get; set; }

		[ Column(Name = "fax", StringLength = 50)]
		public string Fax { get; set; }

		[ Column(Name = "gender", StringLength = 50)]
		public string Gender { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_inventor")]
		public bool? IsInventor { get; set; } = false;

		[ Column(Name = "mobile", StringLength = 50)]
		public string? Mobile { get; set; }

		[ Column(Name = "personal_email", StringLength = 50)]
		public string PersonalEmail { get; set; }

		[ Column(Name = "position", StringLength = 100)]
		public string Position { get; set; }

		[ Column(Name = "postcode", StringLength = 50)]
		public string Postcode { get; set; }

		[ Column(Name = "qq", StringLength = 50)]
		public string Qq { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		[ Column(Name = "tel", StringLength = 100)]
		public string? Tel { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string? UpdateUserId { get; set; }

		[ Column(Name = "wechat_no", StringLength = 50)]
		public string WechatNo { get; set; }

	}

}
