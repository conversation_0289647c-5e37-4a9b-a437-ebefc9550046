﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Repository.Interface;

/// <summary>
/// 简单CRUD封装
/// </summary>
public interface IUserInfoRepository : 
    IBaseRepository<SysUserInfo, string>, 
    IRedisCacheableRepository<string, UserInfoDto>,
    IRedisCacheableRepository<string, UserBaseInfo>,
    IStringKeyCacheableRepository<SysUserInfo>,
    IScopeDependency
{
    #region 通过username查询用户缓存信息

    string ICacheableRepository<string, UserInfoDto>.CacheKey => nameof(UserInfoDto);

    async ValueTask<string?> GetUserIdByUserNameAsync(string userName)
    {
        return (await ((ICacheableRepository<string, UserInfoDto>)this).GetCacheValueAsync(userName))?.UserId;
    }

    Task<UserInfoDto?> ICacheableRepository<string, UserInfoDto>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Where(info => info.UserName == key).FirstAsync<UserInfoDto>(info =>
            new UserInfoDto()
            {
                UserId = info.UserId,
                UserName = info.UserName,
                CnName = info.CnName,
                DeptId = info.DeptId,
                EmailAddress = info.Email,
                IsEnabled = info.IsEnabled
            }, cancellationToken)!;
    }

    async Task<IEnumerable<UserInfoDto>> ICacheableRepository<string, UserInfoDto>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().Take(100).ToListAsync<UserInfoDto>(info => new UserInfoDto()
        {
            UserId = info.UserId,
            UserName = info.UserName,
            CnName = info.CnName,
            DeptId = info.DeptId,
            EmailAddress = info.Email,
            IsEnabled = info.IsEnabled
        }, cancellationToken);
    }

    string ICacheableRepository<string, UserInfoDto>.GenerateKey(UserInfoDto value)
    {
        return value.UserName;
    }

    #endregion

    #region 通过userId查询用户缓存信息

    string ICacheableRepository<string, UserBaseInfo>.CacheKey => nameof(UserBaseInfo);

    Task<UserBaseInfo?> ICacheableRepository<string, UserBaseInfo>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Where(info => info.UserId == key).ToOneAsync<UserBaseInfo>(info =>
            new UserBaseInfo()
            {
                UserId = info.UserId,
                UserName = info.UserName,
                CnName = info.CnName,
                DeptId = info.DeptId,
                EmailAddress = info.Email,
                IsEnabled = info.IsEnabled
            }, cancellationToken)!;
    }

    async Task<IEnumerable<UserBaseInfo>> ICacheableRepository<string, UserBaseInfo>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().Take(10).ToListAsync<UserBaseInfo>(info => new UserBaseInfo()
        {
            UserId = info.UserId,
            UserName = info.UserName,
            CnName = info.CnName,
            DeptId = info.DeptId,
            EmailAddress = info.Email,
            IsEnabled = info.IsEnabled
        }, cancellationToken);
    }

    string ICacheableRepository<string, UserBaseInfo>.GenerateKey(UserBaseInfo value)
    {
        return value.UserId;
    }

    #endregion

    #region 通过userId查询用户以及角色缓存
    
    string ICacheableRepository<string, SysUserInfo>.CacheKey => nameof(SysUserInfo);

    Task<IEnumerable<SysUserInfo>> ICacheableRepository<string, SysUserInfo>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return Task.FromResult(Enumerable.Empty<SysUserInfo>());
    }

        Task<SysUserInfo?> ICacheableRepository<string, SysUserInfo>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
        {
            return Select.WithLock().IncludeMany(info => info.Roles).Where(info => info.UserId == key)
                .ToOneAsync(info => new SysUserInfo()
                {
                    UserId = info.UserId,
                    UserName = info.UserName,
                    DeptId = info.DeptId,
                    CnName = info.CnName,
                    Email = info.Email,
                    IsEnabled = info.IsEnabled,
                    LeaveDate = info.LeaveDate,
                    ManageCompany = info.ManageCompany
                }, cancellationToken)!;
        }

    string ICacheableRepository<string, SysUserInfo>.GenerateKey(SysUserInfo value)
    {
        return value.UserId;
    }

    string? IStringKeyCacheableRepository<SysUserInfo>.GetCacheTextValue(SysUserInfo value)
    {
        return value.CnName;
    }

    #endregion
}