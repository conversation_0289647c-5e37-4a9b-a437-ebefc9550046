﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 收件详情
/// </summary>
/// <param name="MailId">邮件id</param>
/// <param name="IgnoreTime">忽略时间</param>
/// <param name="MailNo">收件编号</param>
/// <param name="Status">收件状态</param>
/// <param name="MailTo">收件人</param>
/// <param name="MailDate">收件时间</param>
/// <param name="SortTime">分拣时间</param>
/// <param name="UndertakeUser">承办人</param>
/// <param name="FinishDate">完成时间</param>
/// <param name="IgnoreByUser">忽略人</param>
/// <param name="SortBy">分拣人</param>
/// <param name="HostId">邮箱id</param>
/// <param name="CreateTime">创建时间</param>
/// <param name="SendName">发件名称</param>
public record GetReceiveExtensionDto(
    string MailId,
    string IgnoreByTemp,
    DateTime? IgnoreTime,
    string MailNo,
    int? Status,
    string MailTo,
    DateTime? MailDate,
    string SortByTemp,
    DateTime? SortTime,
    string UndertakeUserTemp,
    DateTime? FinishDate,
    string? HostId,
    DateTime? CreateTime,
    string? SendName
)
{
    public object IgnoreByUser { get; set; }

    public object SortBy { get; set; }

    public object UndertakeUser { get; set; }
};
