using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ud_task_details_error", DisableSyncStructure = true)]
	public partial class UdTaskDetailsError {

		[ Column(Name = "error_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ErrorId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_id", StringLength = 50)]
		public string ColumnId { get; set; }

		[ Column(Name = "error_message", StringLength = -2)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "error_type", StringLength = 50)]
		public string ErrorType { get; set; }

		[ Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

		[ Column(Name = "task_id", StringLength = 50)]
		public string TaskId { get; set; }

	}

}
