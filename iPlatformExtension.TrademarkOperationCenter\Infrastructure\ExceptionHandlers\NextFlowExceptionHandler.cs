﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.ExceptionHandlers;

internal sealed class NextFlowExceptionHandler(ILogger<NextFlowExceptionHandler> logger) : IRequestExceptionHandler<NextFlowCommand, Unit, Exception>
{
    public Task Handle(NextFlowCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        if (request.IsAuto)
        {
            logger.LogNextFlowError(request.ProcId, exception);
            state.SetHandled(Unit.Value);
        }

        return Task.CompletedTask;
    }
}