﻿﻿using iPlatformExtension.MailCenter.Applications.Commands.Signature;
using iPlatformExtension.MailCenter.Applications.Models.Signature;
using iPlatformExtension.MailCenter.Applications.Queries.Signature;
using iPlatformExtension.Model.Dto;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.MailCenter.Controllers
{
    /// <summary>
    /// 邮件签名控制器
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SignatureController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// 获取签名列表
        /// </summary>
        /// <remarks>
        /// 获取签名列表接口，支持按签名名称模糊查询
        /// </remarks>
        /// <param name="query">查询参数</param>
        /// <returns>签名列表分页结果</returns>
        [HttpGet("GetSignatureList")]
        public async Task<PageResult<GetSignatureListDto>> GetSignatureList([FromQuery] GetSignatureListQuery query)
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取签名详情
        /// </summary>
        /// <remarks>
        /// 获取签名详情接口，根据签名ID查询签名详细信息
        /// </remarks>
        /// <param name="id">签名ID</param>
        /// <returns>签名详情</returns>
        [HttpGet("GetSignatureDetail/{id}")]
        public async Task<GetSignatureDetailDto> GetSignatureDetail(string id)
        {
            return await mediator.Send(new GetSignatureDetailQuery(id));
        }

        /// <summary>
        /// 保存签名
        /// </summary>
        /// <remarks>
        /// 保存签名接口，支持新增和更新操作：
        /// - 当ID为空时，执行新增操作
        /// - 当ID有值时，执行更新操作
        /// </remarks>
        /// <param name="command">保存签名命令</param>
        /// <returns>签名ID</returns>
        [HttpPost("SaveSignature")]
        public async Task<string> SaveSignature([FromBody] SaveSignatureCommand command)
        {
            var result = await mediator.Send(command);
            return result;
        }

        /// <summary>
        /// 删除签名
        /// </summary>
        /// <remarks>
        /// 删除签名接口，根据签名ID删除签名
        /// </remarks>
        /// <param name="id">签名ID</param>
        /// <returns></returns>
        [HttpDelete("DeleteSignature/{id}")]
        public async Task DeleteSignature(string id)
        {
            await mediator.Send(new DeleteSignatureCommand(id));
        }
    }
}
