﻿
using System.Collections.Concurrent;

namespace iPlatformExtension.Common.TypeInfo;

public class EntityTypeInfoProvider
{
    private readonly ConcurrentDictionary<Type, Lazy<EntityTypeInfo>> _typeInfos = new(Environment.ProcessorCount, 31);

    public EntityTypeInfo Get(Type entityType) =>
        _typeInfos.GetOrAdd(entityType,
            type => new Lazy<EntityTypeInfo>(() =>
                new EntityTypeInfoBuilder(type).BuildEntityTypeInfo().BuildProperties().Build())).Value;
}