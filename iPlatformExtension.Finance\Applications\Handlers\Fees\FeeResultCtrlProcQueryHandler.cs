﻿using iPlatformExtension.Finance.Applications.Queries;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultCtrlProcQueryHandler(
    IBaseCtrlProcRepository baseCtrlProcRepository,
    ISystemDictionaryRepository systemDictionaryRepository)
    : IRequestHandler<FeeResultCtrlProcQuery>
{
    public async Task Handle(FeeResultCtrlProcQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return;

        foreach (var feeItem in request.FeeResults)
        {
            var baseCtrlProc = await baseCtrlProcRepository.GetCacheValueAsync(feeItem.CtrlProcId);
            if (baseCtrlProc is null)
                throw new ApplicationException($"费项[{feeItem.FeeId}]的任务属性为空");

            feeItem.CtrlProcInfo = new CtrlProcInfo(baseCtrlProc.CtrlProcId, baseCtrlProc.CtrlProcZhCn,
                baseCtrlProc.CtrlProcEnUs);
            feeItem.ProcName = baseCtrlProc.CtrlProcZhCn;
            feeItem.ProcAttribute =
                await systemDictionaryRepository.GetChineseKeyValueAsync(
                    KeyValuePair.Create(SystemDictionaryName.Attribute, baseCtrlProc?.AttributeId ?? string.Empty));
            
        }
    }
}