﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto
{
    /// <summary>
    /// 委案订单费项状态查询结果Dto
    /// </summary>
    public class FeeStatusDto
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string OrderCode { get; set; }

        /// <summary>
        /// 订单下案件Id
        /// </summary>
        public string CaseId { get; set; }

        /// <summary>
        /// 产品Id
        /// </summary>
        public string OrderProductId { get; set; }

        /// <summary>
        /// 产品费项Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 费项Id
        /// </summary>
        public string FeeLineId { get; set; }

        /// <summary>
        /// 费项名称
        /// </summary>
        public string FeeName { get; set; }

        /// <summary>
        /// 费项类别
        /// </summary>
        public string FeeType { get; set; }

        /// <summary>
        /// 费项金额
        /// </summary>
        public decimal? FeeAmount { get; set; }

        /// <summary>
        /// 请款状态
        /// </summary>
        public string PaymentRequestStatus { get; set; }

        /// <summary>
        /// 缴费状态
        /// </summary>
        public string PaymentStatus { get; set; }

        /// <summary>
        /// 业务系统费项Id
        /// </summary>
        public string BenchFeeId { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public string IsDeleted { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 币别
        /// </summary>
        public string Currency { get; set; } = null!;

        /// <summary>
        /// 标准金额
        /// </summary>
        public decimal? StandardAmount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 折扣（即费减比例）
        /// </summary>
        [JsonIgnore]
        public string? Discount { get; set; }

        /// <summary>
        /// 费减比例
        /// </summary>
        public int FeeReduce => int.TryParse(Discount, out var feeReduce) ? feeReduce : 0;
    }
}
