using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_case_proc_status", DisableSyncStructure = true)]
	public partial class BasCaseProcStatus {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "attribute", StringLength = 50)]
		public string Attribute { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "text_en_us", StringLength = 100)]
		public string TextEnUs { get; set; }

		[ Column(Name = "text_ja_jp", StringLength = 100)]
		public string TextJaJp { get; set; }

		[ Column(Name = "text_zh_cn", StringLength = 50)]
		public string TextZhCn { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "value", StringLength = 50)]
		public string Value { get; set; }

	}

}
