using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "express_logistics_detail", DisableSyncStructure = true)]
	public partial class ExpressLogisticsDetail {

		[ Column(Name = "detail_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DetailId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "accept_code", StringLength = 50, IsNullable = false)]
		public string AcceptCode { get; set; }

		[ Column(Name = "accept_seq")]
		public int? AcceptSeq { get; set; }

		[ Column(Name = "accept_station", StringLength = 500, IsNullable = false)]
		public string AcceptStation { get; set; }

		[ Column(Name = "accept_time")]
		public DateTime AcceptTime { get; set; }

		[ Column(Name = "create_time")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "logistic_id", StringLength = 50, IsNullable = false)]
		public string LogisticId { get; set; }

	}

}
