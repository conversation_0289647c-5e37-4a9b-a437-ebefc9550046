using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class DomesticTrademarkCommissionRepository(IFreeSql freeSql, UnitOfWorkManager unitOfWorkManager) 
    : DefaultRepository<DomesticTrademarkCommission, string>(freeSql, unitOfWorkManager), 
        IDomesticTrademarkCommissionRepository;