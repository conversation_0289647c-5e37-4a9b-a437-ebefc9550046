using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{

    [ Table(Name = "app_applicant_list", DisableSyncStructure = true)]
    public partial class AppApplicantList
    {

        /// <summary>
        /// 地址名称
        /// </summary>
        [ Column(Name = "address_cn", StringLength = 500)]
        public string AddressCn { get; set; }

        /// <summary>
        /// 地址ID
        /// </summary>
        [ Column(Name = "address_id", StringLength = 50)]
        public string AddressId { get; set; }

        /// <summary>
        /// 申请人ID
        /// </summary>
        [ Column(Name = "applicant_id", StringLength = 50)]
        public string ApplicantId { get; set; }

        /// <summary>
        /// 案件ID
        /// </summary>
        [ Column(Name = "case_id", StringLength = 50)]
        public string CaseId { get; set; }

        /// <summary>
        /// 类别
        /// </summary>
        [ Column(Name = "classify", StringLength = 50, IsNullable = false)]
        public string Classify { get; set; } = "A";

        /// <summary>
        /// 唯一ID
        /// </summary>
        [ Column(Name = "id", StringLength = 50, IsNullable = false)]
        public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

        /// <summary>
        /// 是否存在
        /// </summary>
        [ Column(Name = "is_represent")]
        public bool IsRepresent { get; set; } = false;

        /// <summary>
        /// 排序
        /// </summary>
        [ Column(Name = "seq")]
        public short? Seq { get; set; }

    }

}
