﻿using iPlatformExtension.Mail.Applications.Models;
using iPlatformExtension.Messages.Mails;

namespace iPlatformExtension.Mail.Infrastructure;

/// <summary>
/// 通知邮件模板扩展程序
/// </summary>
public static class NotificationMailTemplateExtension
{
    private static readonly NotificationMailTemplate defaultTemplate =new ();
    
    private static readonly NotificationMailTemplate deadlineLogicalException = new()
    {
        Subject = "期限时间逻辑异常报表",
        Body = """
               流程时限管理专员：你好！<br/>
               附件案件的任务在期限设定的逻辑上存在异常，请及时关注并更正，谢谢！<br/>
               """,
        IsHtmlBody = true,
    };

    private static readonly NotificationMailTemplate mineExpired15Days = new()
    {
        Subject = "我的任务到期提醒（15天内，含当天）",
        Body = """
               尊敬的{0}：您好！<br/>
               您有以下案件任务的处理期限临近，请您合理安排时间并及时完成，详见附件表格。<br/>
               您也可登录业务系统，在系统首页【我的任务到期提醒（15天内，含当天）】中查看并及时处理。<br/>
               谢谢！<br/>
               """,
        IsHtmlBody = true,
        NeedToFormatReceiver = true
    };

    private static readonly NotificationMailTemplate mineDeadlineExpired15Days = new()
    {
        Subject = "我的绝限任务到期提醒（15天内，含当天）",
        Body = """
               尊敬的{0}：您好！<br/>
               您有以下案件任务的处理绝限临近，请您合理安排时间并及时完成，详见附件表格。<br/>
               您也可登录业务系统，在系统首页【我的任务到期提醒（15天内，含当天）-官方期限】中查看并及时处理。<br/>
               谢谢！！<br/>
               """,
        IsHtmlBody = true,
        NeedToFormatReceiver = true
    };

    private static readonly NotificationMailTemplate myTeamExpired15Days = new()
    {
        Subject = "我的团队任务到期提醒（15天内，含当天）",
        Body = """
               尊敬的{0}：您好！<br/>
               您的团队有以下案件任务的处理期限临近，请您督促其合理安排时间并及时完成，团队成员的案件任务清单详见附件表格。<br/>
               您也可登录业务系统，在系统首页【我的团队任务到期提醒（15天内，含当天）】中查看并及时处理。<br/>
               谢谢！<br/>
               """,
        IsHtmlBody = true,
        NeedToFormatReceiver = true
    };

    private static readonly NotificationMailTemplate myTeamDeadlineExpired15Days = new()
    {
        Subject = "我的团队的绝限任务到期提醒（15天内，含当天）",
        Body = """
               尊敬的{0}：您好！<br/>
               您的团队有以下案件任务的处理绝限临近，请您督促其合理安排时间并及时完成，团队成员的绝限任务清单详见附件表格。<br/>
               您也可登录业务系统，在系统首页【我的团队任务到期提醒（15天内，含当天）-官方期限】中查看并及时处理。<br/>
               谢谢！<br/>
               """,
        IsHtmlBody = true,
        NeedToFormatReceiver = true
    };

    private static readonly NotificationMailTemplate internalDeadlineExpiredDays = new()
    {
        Subject = "内部期限超过7天汇总表",
        Body = """
               各位老师：您好！<br/>
               初稿期限（内）、定稿期限（内） 截至今日，超期7天以上（含7天）未完成任务汇总详见附件表格，请质检部和各专业部领导督办。<br/>
               谢谢！<br/>
               """,
        IsHtmlBody = true,
    };

    private static readonly NotificationMailTemplate checkDelivery = new()
    {
        Subject = "应启动而未启动递交流程或管制完成的代理师任务的排查情况",
        Body = """
                  尊敬的{0}：您好！<br/>
                  请您复核附件清单中的任务是否可以启动递交或者通知流程进行任务的完成管制，谢谢配合！<br/>
                  """,
        IsHtmlBody = true,
        NeedToFormatReceiver = true
    };

    /// <summary>
    /// 尝试根据模板枚举获取邮件正文内容
    /// </summary>
    /// <param name="template">模板的枚举值</param>
    /// <param name="mailTemplate">邮件模板</param>
    /// <returns>模板的枚举参数是否在给定的枚举范围内</returns>
    internal static bool TryGetMailTemplate(this BodyTemplate template, out NotificationMailTemplate mailTemplate)
    {
        mailTemplate = template switch
        {
            BodyTemplate.DeadlineLogicalException => deadlineLogicalException,
            BodyTemplate.MineExpired15Days => mineExpired15Days,
            BodyTemplate.MineDeadlineExpired15Days => mineDeadlineExpired15Days,
            BodyTemplate.MyTeamExpired15Days => myTeamExpired15Days,
            BodyTemplate.MyTeamDeadlineExpired15Days => myTeamDeadlineExpired15Days,
            BodyTemplate.InternalDeadlineExpiredDays => internalDeadlineExpiredDays,
            BodyTemplate.CheckDelivery => checkDelivery,
            _ => defaultTemplate,
        };

        return mailTemplate != defaultTemplate;
    }
}