using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_contact_list", DisableSyncStructure = true)]
	public partial class CaseContactList {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "contact_id", StringLength = 50)]
		public string ContactId { get; set; }

		[ Column(Name = "contact_type_code", StringLength = 50)]
		public string ContactTypeCode { get; set; }

		[ Column(Name = "contact_type_id", StringLength = 50)]
		public string ContactTypeId { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

	}

}
