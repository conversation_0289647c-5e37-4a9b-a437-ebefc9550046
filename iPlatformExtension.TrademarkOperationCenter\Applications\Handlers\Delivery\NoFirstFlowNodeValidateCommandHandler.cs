﻿using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class NoFirstFlowNodeValidateCommandHandler : IRequestHandler<NoFirstFlowNodeValidateCommand, BatchDeliveryValidateResult>
{
    public Task<BatchDeliveryValidateResult> Handle(NoFirstFlowNodeValidateCommand request, CancellationToken cancellationToken)
    {
        var (items, validateType) = request;

        var result = items.Where(validationDto => validationDto.CurrentNodeId is null or FlowNodes.BeginNodeId).Aggregate(
            new BatchDeliveryValidateResult(items.Count()),
            (validateResult, dto) => validateType switch
            {
                BatchDeliveriesValidateType.Reject => validateResult.CannotReject(dto.ProcNo),
                BatchDeliveriesValidateType.HandOver => validateResult.CannotHandOver(dto.ProcNo),
                _ => throw new ArgumentOutOfRangeException(nameof(validateType), validateType, "当前验证只支持退回或移交操作")
            });

        return Task.FromResult(result);
    }
}