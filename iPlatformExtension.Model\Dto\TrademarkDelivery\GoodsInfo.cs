﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 商品小项
/// </summary>
public sealed class GoodsInfo
{
    /// <summary>
    /// 商标原申请人地址
    /// </summary>
    [JsonPropertyName("address")]
    public string Address { get; set; } = string.Empty;

    /// <summary>
    /// 商标申请人名称
    /// </summary>
    [JsonPropertyName("applicantName")]
    public string ApplicantName { get; set; } = string.Empty;

    /// <summary>
    /// 商标名称
    /// </summary>
    [JsonPropertyName("brandName")]
    public string BrandName { get; set; } = string.Empty;

    /// <summary>
    /// 商标注册号
    /// </summary>
    [JsonPropertyName("brandRegisterNo")]
    public string BrandRegisterNo { get; set; } = string.Empty;

    /// <summary>
    /// 商标流程状态，商标注册申请 | 申请收文
    /// </summary>
    [JsonPropertyName("categoryFlowStateName")]
    public string CategoryFlowStateName { get; set; } = string.Empty;

    /// <summary>
    /// 大类名称
    /// </summary>
    [JsonPropertyName("firstCgName")]
    public string FirstCgName { get; set; } = string.Empty;

    /// <summary>
    /// 大类编号，45
    /// </summary>
    [JsonPropertyName("firstCgNo")]
    public string FirstCgNo { get; set; } = string.Empty;

    /// <summary>
    /// 商标流程状态
    /// </summary>
    [JsonPropertyName("flowListName")]
    public string FlowListName { get; set; } = string.Empty;

    /// <summary>
    /// 商标小项信息
    /// </summary>
    [JsonPropertyName("goodsList")]
    public IEnumerable<GoodsItem> GoodsList { get; set; } = Array.Empty<GoodsItem>();

    /// <summary>
    /// 商标小项信息，4506-为法律咨询目的监控知识产权 4506-向发明人提供知识产权咨询服务 4506-商标代理服务 4506-有关知识产权的顾问服务 4506-法律咨询
    /// 4506-版权管理 4506-知识产权代理服务 4506-知识产权咨询 4506-计算机软件许可咨询 4506-通过网站提供法律服务信息
    /// </summary>
    [JsonPropertyName("goodsText")]
    public string GoodsText { get; set; } = string.Empty;

    /// <summary>
    /// 前端显示商标商品项信息，4506-为法律咨询目的监控知识产权 4506-向发明人提供知识产权咨询服务 4506-商标代理服务 ...
    /// </summary>
    [JsonPropertyName("goodsTextShow")]
    public string GoodsTextShow { get; set; } = string.Empty;

    /// <summary>
    /// 商标logo地址
    /// </summary>
    [JsonPropertyName("logoUrl")]
    public string LogoUrl { get; set; } = string.Empty;

    /// <summary>
    /// 专用权开始时间
    /// </summary>
    [JsonPropertyName("privateDate")]
    public string PrivateDate { get; set; } = string.Empty;

    /// <summary>
    /// 商标状态
    /// </summary>
    [JsonPropertyName("processName")]
    public string ProcessName { get; set; } = string.Empty;

    /// <summary>
    /// 商标注册号，45896679
    /// </summary>
    [JsonPropertyName("receiptNo")]
    public string ReceiptNo { get; set; } = string.Empty;

    /// <summary>
    /// 大类编号，45
    /// </summary>
    [JsonPropertyName("typeCode")]
    public string TypeCode { get; set; } = string.Empty;
}