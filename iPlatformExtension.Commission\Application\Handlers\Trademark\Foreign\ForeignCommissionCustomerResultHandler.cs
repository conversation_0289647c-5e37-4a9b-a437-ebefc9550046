﻿using iPlatformExtension.Commission.Application.Notifications.Trademark.Foreign;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class ForeignCommissionCustomerResultHandler(ICustomerRepository customerRepository) : INotificationHandler<ForeignCommissionResultNotification>
{
    public async Task Handle(ForeignCommissionResultNotification notification, CancellationToken cancellationToken)
    {
        var commission = notification.ForeignTrademarkBonus;
        var customerId = commission.CustomerId;
        
        var customerInfo = await customerRepository.GetCacheValueAsync(customerId, cancellationToken:cancellationToken);
        commission.CustomerName = customerInfo?.CustomerName ?? string.Empty;
    }
}