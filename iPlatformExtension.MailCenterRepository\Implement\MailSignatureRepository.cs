﻿﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;

namespace iPlatformExtension.MailCenterRepository.Implement
{
    /// <summary>
    /// 邮件签名仓储实现
    /// </summary>
    internal class MailSignatureRepository(
        IFreeSql<MailCenterFreeSql> fsql,
        UnitOfWorkManage<MailCenterFreeSql> manager)
        : DefaultRepository<MailSignature, string>(fsql, manager), IMailSignatureRepository;
}
