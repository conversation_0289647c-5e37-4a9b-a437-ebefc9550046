﻿using AutoMapper;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.LicenseFiling;

internal sealed class BuildLicenseFilingNiceInfoCommandHandler(
    IMapper mapper, IDeliveryNiceCategoryRepository niceCategoryRepository) 
    : BuildProcNiceInfoCommandHandler(mapper, niceCategoryRepository)
{
    public override string CtrlProcId => CtrlProcIds.LicenseFiling;

    public override IEnumerable<string> CaseDirections => [CaseDirection.II];
}