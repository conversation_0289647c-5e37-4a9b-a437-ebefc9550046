﻿
namespace iPlatformExtension.Outsourcing.Infrastructure.MessageQueue;

public class ConsumerOptions
{
    /// <summary>
    /// 消费者组名
    /// </summary>
    public string GroupId { get; set; } = null!;

    /// <summary>
    /// 消费者实例名称
    /// </summary>
    public string? ConsumerName { get; set; }

    /// <summary>
    /// 主题信息
    /// </summary>
    public IEnumerable<TopicInfo> TopicInfos { get; set; } = [];
}