﻿namespace iPlatformExtension.Model.Dto;

public record UserInfoDto
{
    /// <summary>
    /// 用户id
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 用户中文名
    /// </summary>
    public string CnName { get; set; } = default!;

    /// <summary>
    /// 工号
    /// </summary>
    public string UserName { get; set; } = default!;

    /// <summary>
    /// 部门id
    /// </summary>
    public string DeptId { get; set; } = default!;

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 电子邮箱
    /// </summary>
    public string? EmailAddress { get; set; }
}

/// <summary>
/// 用户基本信息
/// </summary>
public record UserBaseInfo : UserInfoDto
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="userId"></param>
    public UserBaseInfo(string userId)
    {
        UserId = userId;
    }

    /// <summary>
    /// 
    /// </summary>
    public UserBaseInfo()
    {
        
    }
}