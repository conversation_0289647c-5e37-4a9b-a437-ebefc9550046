﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

internal interface IMatchTrademarkDeliveryBusinessTypeNotificationHandler<in TNotification> : 
    IMatchNotificationHandler<TNotification> where TNotification : IMatchTrademarkDeliveryBusinessTypeNotification
{
    protected IEnumerable<string> MatchBusinessTypes { get; }

    protected IReadOnlyDictionary<string, FileDescriptionKey> DescriptionKeys { get; }
    
    protected IFileDescriptionRepository FileDescriptionRepository { get; }

    ValueTask<bool> IMatchNotificationHandler<TNotification>.MatchAsync(TNotification notification, CancellationToken cancellationToken)
    {
        var businessType = notification.TrademarkDeliveryBusinessType;
        return new ValueTask<bool>(MatchBusinessTypes.Contains(businessType));
    }

    async Task IMatchNotificationHandler<TNotification>.HandleAsync(TNotification notification, CancellationToken cancellationToken)
    {
        var files = notification.Files;
        notification.Files = await files.Where(dto => dto.FileType == "申请人文件").ToAsyncEnumerable().SelectAwait(async fileListDto =>
        {
            var originalFileDescription = fileListDto.FileDescriptionId is not null
                ? await FileDescriptionRepository.GetCacheValueAsync(fileListDto.FileDescriptionId)
                : await FileDescriptionRepository.GetCacheValueAsync(
                    new FileDescriptionKey(fileListDto.FileType ?? string.Empty,
                        fileListDto.FileDescription ?? string.Empty));

            if (originalFileDescription is null)
            {
                throw new PropertyMissingException(fileListDto.FileId, fileListDto.FileDescriptionId, "申请人文件", "文件描述");
            }

            var fileCode = originalFileDescription.TextCode ?? string.Empty;
            if (!DescriptionKeys.TryGetValue(fileCode, out var descriptionKey))
            {
                throw new NotFoundException(fileCode, "文件编码");
            }

            var fileDescription = await FileDescriptionRepository.GetCacheValueAsync(descriptionKey);
            if (fileDescription is null)
            {
                throw new NotFoundException(descriptionKey.FileDescription, "文件描述");
            }

            fileListDto.FileDescriptionId = fileDescription.FileDescId;
            fileListDto.FileDescription = descriptionKey.FileDescription;
            fileListDto.FileType = descriptionKey.FileType;

            return fileListDto;
        }).ToArrayAsync(cancellationToken);
    }
}