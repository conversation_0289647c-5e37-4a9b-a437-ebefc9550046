﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailService.Applications.Commands;
using iPlatformExtension.MailService.Applications.Queries;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailService.Applications.Handlers
{
    public class MailHostHandler(IFreeSql<MailCenterFreeSql> freeSql) : IRequestHandler<MailHostQuery, IEnumerable<MailHost>>
    {
        public async Task<IEnumerable<MailHost>> Handle(MailHostQuery request, CancellationToken cancellationToken)
        {
            return await freeSql.Select<MailHost>()
                .WhereIf(!string.IsNullOrWhiteSpace(request.HostId), o => o.HostId == request.HostId)
                .Where(o => o.IsPrivate == request.IsPrivate && o.IsEnabled == request.IsEnabled).ToListAsync(cancellationToken);
        }
    }
}
