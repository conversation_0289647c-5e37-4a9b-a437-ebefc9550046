syntax = "proto3";

package commission;

import "google/protobuf/timestamp.proto";
import "common_messages.proto";

option csharp_namespace = "iPlatformExtension.Commission.Grpc";

enum FeeTypes {
  OtherFees = 0;
}

message Fee {
  FeeTypes type = 1;
  string currency = 2;
  string value = 3;
}

message UserWeight {
  string username = 1;
  string cnName = 2;
  string weight = 3;
  string deptName = 4;
  string deptId = 5;
  string districtCode = 6;
  string districtName = 7;
  map<int32, Fee> fees = 8;
}



message CommissionWeight {
  int32 year = 1;
  int32 month = 2;
  string volume = 3;
  string procNo = 4;
  string customerName = 5;
  string caseDirection = 6;
  string procName = 7;
  google.protobuf.Timestamp commissionDate = 8;
  string procId = 9;
  UserWeight weight = 10;
  string undertakerName = 11;
  string undertakerUsername = 12;
  string customerId = 13;
  string caseType = 14;
  string id = 15;
}

message UserReward {
  string username = 1;
  string cnName = 2;
  string reward = 3;
  string deptName = 4;
  string deptId = 5;
  string districtCode = 6;
  string districtName = 7;
}

message CommissionReward {
  int32 year = 1;
  int32 month = 2;
  string volume = 3;
  string procNo = 4;
  string customerName = 5;
  string caseDirection = 6;
  string procName = 7;
  google.protobuf.Timestamp commissionDate = 8;
  string procId = 9;
  repeated UserReward rewards = 10;
  string customerId = 11;
  string caseType = 12;
}

service CommissionService {
  
  rpc PushCommissionWeights(stream CommissionWeight) returns (stream MessageResult);
  
  rpc PushCommissionRewards(stream CommissionReward) returns (stream MessageResult);
}
