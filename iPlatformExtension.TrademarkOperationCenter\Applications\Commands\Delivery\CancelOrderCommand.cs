﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

internal record CancelOrderCommand(string ProcId) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>
{
    public DeliInfo? DeliveryInfo { get; }

    public CancelOrderCommand(DeliInfo deliveryInfo) : this(deliveryInfo.ProcId)
    {
        DeliveryInfo = deliveryInfo;
    }
}