﻿using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Applications.PipelineBehaviors.FeeQuery;

internal sealed class CheckApplicantsParametersBehavior : CheckFeesQueryParametersBehaviorBase<BuildApplicantInfoCommand>
{
    public CheckApplicantsParametersBehavior(ILoggerFactory loggerFactory) : base(loggerFactory)
    {
    }

    public override bool Check(FeeQueryDto dto)
    {
        return dto.Applicants.Any();
    }
}