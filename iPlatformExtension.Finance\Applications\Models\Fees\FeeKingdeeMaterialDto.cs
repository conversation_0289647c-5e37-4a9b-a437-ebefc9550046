﻿namespace iPlatformExtension.Finance.Applications.Models.Fees
{
    /// <summary>
    /// 费项发票配置
    /// </summary>
    public class FeeKingdeeMaterialDto
    {
        /// <summary>
        /// 费项id
        /// </summary>
        public string FeeId { get; init; } = default!;

        /// <summary>
        /// 案件类型
        /// </summary>
        public string CaseType { get; init; } = default!;

        /// <summary>
        /// 案件流向
        /// </summary>
        public string CaseDirection { get; init; } = default!;

        /// <summary>
        /// 费用类型
        /// </summary>
        public string FeeClass { get; init; } = default!;

        /// <summary>
        ///  申请类型
        /// </summary>
        public string ApplyTypeId { get; init; } = default!;

        /// <summary>
        /// 费项名称id
        /// </summary>
        public string FeeTypeNameId { get; init; } = default!;

        /// <summary>
        /// 发票名称
        /// </summary>
        public string? FeeNameAlias { get; set; }

        /// <summary>
        /// 金蝶物料编码
        /// </summary>
        public string? KingdeeMaterialCode { get; set; }

        /// <summary>
        /// 金蝶物料名称
        /// </summary>
        public string? KingdeeMaterialName { get; set; }

        /// <summary>
        /// 税项编码
        /// </summary>
        public string? TaxationCode { get; set; }

        /// <summary>
        /// 税项名称
        /// </summary>
        public string? TaxationName { get; set; }
    }
}
