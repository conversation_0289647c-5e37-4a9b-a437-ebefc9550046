﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class MentorRepository(
    IFreeSql freeSql, 
    IMemoryCache memoryCache, 
    CacheExpirationToken<MentorInfo> expirationToken, 
    IRedisCache<RedisCacheOptionsBase> redisCache) : IMentorRepository
{
    public IMemoryCache MemoryCache { get; } = memoryCache;

    public CacheExpirationToken<MentorInfo> ExpirationToken { get; } = expirationToken;

    public async Task<IEnumerable<MentorInfo>> GetValuesFromDbAsync(CancellationToken cancellationToken = default)
    {
        return await freeSql.Select<SysUserInfo>().WithLock()
            .Where(info => info.SupervisorUserId != null)
            .Where(info => info.Mentor!.IsEnabled == true)
            .ToListAsync(info => new MentorInfo()
            {
                UserId = info.UserId,
                UserName = info.UserName,
                MentorId = info.Mentor!.UserId,
                MentorUserName = info.Mentor!.UserName,
                DeptId = info.Mentor!.DeptId,
                MentorCnName = info.Mentor!.CnName,
                Email = info.Mentor!.Email,
                IsEnabled = info.Mentor!.IsEnabled
            }, cancellationToken);
    }

    public Task<MentorInfo?> GetValueFromDbAsync(string key, CancellationToken cancellationToken = default)
    {
        return freeSql.Select<SysUserInfo>().WithLock()
            .Where(info => info.UserId == key || info.UserName == key)
            .Where(info => info.SupervisorUserId != null)
            .Where(info => info.Mentor!.IsEnabled == true)
            .ToOneAsync(info => new MentorInfo()
            {
                UserId = info.UserId,
                UserName = info.UserName,
                MentorId = info.Mentor!.UserId,
                MentorUserName = info.Mentor!.UserName,
                DeptId = info.Mentor!.DeptId,
                MentorCnName = info.Mentor!.CnName,
                Email = info.Mentor!.Email,
                IsEnabled = info.Mentor!.IsEnabled
            }, cancellationToken)!;
    }

    public string GenerateKey(MentorInfo value)
    {
        return value.UserId;
    }

    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}