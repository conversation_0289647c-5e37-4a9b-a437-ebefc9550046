﻿using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal sealed class BaseCountryForeignFeeResultHandler(IBaseCountryRepository countryRepository) : ForeignFeeResultHandlerBase
{
    protected override async ValueTask GetResultAsync(ForeignBillFeeExportDto dto)
    {
        dto.BelongCountry = await countryRepository.GetTextValueAsync(dto.BelongCountry) ?? string.Empty;
    }
}