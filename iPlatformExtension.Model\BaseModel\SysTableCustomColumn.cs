using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_table_custom_column", DisableSyncStructure = true)]
	public partial class SysTableCustomColumn {

		[ Column(Name = "column_id", StringLength = 50)]
		public string ColumnId { get; set; }

		[ Column(Name = "custom_id", StringLength = 50)]
		public string CustomId { get; set; }

		[ Column(Name = "is_show")]
		public bool? IsShow { get; set; } = true;

		[ Column(Name = "max_length")]
		public int? MaxLength { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "sort", StringLength = 50)]
		public string Sort { get; set; }

		[ Column(Name = "sort_sn")]
		public int? SortSn { get; set; }

	}

}
