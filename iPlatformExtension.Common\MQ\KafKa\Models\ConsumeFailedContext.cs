﻿using Confluent.Kafka;

namespace iPlatformExtension.Common.MQ.KafKa.Models;

public class ConsumeFailedContext<TMessageKey, TMessageValue>(ConsumeResult<TMessageKey, TMessageValue> messageResult)
{
    public bool Handled { get; set; }

    public Exception? CurrentException { get; set; }

    public ConsumeResult<TMessageKey, TMessageValue> MessageResult { get; } = messageResult;
    

    public ConsumeFailedContext(ConsumeResult<TMessageKey, TMessageValue> messageResult, Exception exception) : this(messageResult)
    {
        CurrentException = exception;
    }
}