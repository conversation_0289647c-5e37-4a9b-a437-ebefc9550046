﻿using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultCompanyQueryHandler(ICompanyRepository companyRepository)
    : IRequestHandler<FeeResultCompanyQuery>
{
    public async Task Handle(FeeResultCompanyQuery request, CancellationToken cancellationToken)
    {
        foreach (var feeListItemDto in request.FeeResults)
        {
            var belongCompany =
                await companyRepository.GetCacheValueAsync(feeListItemDto.BelongCompanyId ?? string.Empty);
            feeListItemDto.BelongCompanyName = belongCompany?.CompanyNameCn ?? string.Empty;

            var manageCompany =
                await companyRepository.GetCacheValueAsync(feeListItemDto.ManageCompanyId ?? string.Empty);
            feeListItemDto.ManageCompanyName = manageCompany?.CompanyNameCn ?? string.Empty;
        }
    }
}