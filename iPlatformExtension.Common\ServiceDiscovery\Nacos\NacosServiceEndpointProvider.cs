﻿using Microsoft.AspNetCore.Connections;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.Primitives;
using Microsoft.Extensions.ServiceDiscovery;
using Nacos.V2;

namespace iPlatformExtension.Common.ServiceDiscovery.Nacos;

internal sealed class NacosServiceEndpointProvider(
    INacosNamingService namingService, 
    string groupName, 
    string serviceName,
    IChangeToken reloadToken) 
    : IServiceEndpointProvider
{
    public ValueTask DisposeAsync()
    {
        return ValueTask.CompletedTask;
    }

    public async ValueTask PopulateAsync(IServiceEndpointBuilder endpoints, CancellationToken cancellationToken)
    {
        var instances = await namingService.GetAllInstances(serviceName, groupName);
        foreach (var instance in instances)
        {
            var metadata = instance.Metadata;
            var isSecure = metadata.TryGetValue("secure", out var secureValue) &&
                           Convert.ToBoolean(secureValue);
            var uri = new UriBuilder(isSecure ? Uri.UriSchemeHttps : Uri.UriSchemeHttp, instance.Ip, instance.Port).Uri;
            
            var featureCollection = new FeatureCollection(3);
            featureCollection.Set(metadata);
            featureCollection.Set(instance);
            featureCollection.Set(new NacosServiceNameFeature(groupName, serviceName, instance.ToInetAddr(), uri.Scheme));
            
            endpoints.Endpoints.Add(ServiceEndpoint.Create(new UriEndPoint(uri), featureCollection));

            if (!metadata.TryGetValue("ASPNETCORE_HTTPS_PORTS", out var portsString)) continue;
            
            var ports = portsString.Split(';',
                StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries);
            
            Array.ForEach(ports, port =>
            {
                var sslUri = new UriBuilder(Uri.UriSchemeHttps, instance.Ip, Convert.ToInt32(port)).Uri;
                endpoints.Endpoints.Add(ServiceEndpoint.Create(new UriEndPoint(sslUri), featureCollection));
            });
        }
        
        endpoints.AddChangeToken(reloadToken);
    }
}