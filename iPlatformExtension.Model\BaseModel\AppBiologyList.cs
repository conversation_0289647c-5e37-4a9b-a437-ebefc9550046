using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_biology_list", DisableSyncStructure = true)]
	public partial class AppBiologyList {

		/// <summary>
		/// 生物保藏列表ID
		/// </summary>
		[ Column(Name = "cur_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CurId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 保藏地址
		/// </summary>
		[ Column(Name = "biology_address", StringLength = 2000)]
		public string BiologyAddress { get; set; }

		/// <summary>
		/// 分类名称
		/// </summary>
		[ Column(Name = "biology_class", StringLength = 50)]
		public string BiologyClass { get; set; }

		/// <summary>
		/// 保藏日期
		/// </summary>
		[ Column(Name = "biology_date")]
		public DateTime? BiologyDate { get; set; }

		/// <summary>
		/// 生物保藏ID
		/// </summary>
		[ Column(Name = "biology_id", StringLength = 50)]
		public string BiologyId { get; set; }

		/// <summary>
		/// 是否存活
		/// </summary>
		[ Column(Name = "biology_islive")]
		public bool BiologyIslive { get; set; } = false;

		/// <summary>
		/// 保藏编号
		/// </summary>
		[ Column(Name = "biology_serial", StringLength = 50)]
		public string BiologySerial { get; set; }

		/// <summary>
		/// 案件ID
		/// </summary>
		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50, IsNullable = false)]
		public string UpdateUserId { get; set; }

	}

}
