using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;

namespace FinanceTest;

public class FreeSqlTest
{
    [Theory]
    [InlineData("Ben")]
    [InlineData("John")]
    public async Task IncludeMany(string applicantId)
    {
        var services = new ServiceCollection()
            .AddObjectPools()
            .AddLogging(builder => builder.AddConsole())
            .AddFreeSql(
                "data source=**************,7433;initial catalog=acip_iplatform;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true",
                DataType.SqlServer)
            .BuildServiceProvider();

        var freeSql = services.GetRequiredService<IFreeSql>();
        var procInfoQuery = freeSql.Select<CaseProcInfo>().WithLock()
            .Include(procInfo => procInfo.CaseInfo)
            .IncludeMany(caseProcInfo => caseProcInfo.CaseInfo.Applicants,
                applicants => applicants.Where(applicant => applicant.ApplicantId == applicantId));

        var procIds = await procInfoQuery.Where(procInfo => procInfo.CaseInfo.CaseId == "string").ToListAsync(true);
        Assert.Empty(procIds);
    }
}