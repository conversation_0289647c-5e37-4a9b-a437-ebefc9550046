﻿using System.Text;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class SaveBatchDeliveryCommandHandler(
    IMediator mediator,
    IHttpContextAccessor httpContextAccessor,
    ObjectPool<StringBuilder> stringBuilderPool)
    : IRequestHandler<SaveBatchDeliveryCommand, DeliveryBatchOperationResult>
{
    public async Task<DeliveryBatchOperationResult> Handle(SaveBatchDeliveryCommand request, CancellationToken cancellationToken)
    {
        var userId = (httpContextAccessor.HttpContext?.User).GetUserId();

        var result = new DeliveryBatchOperationResult();
        foreach (var (procId, version) in request.Items)
        {
            result.Results.Add(await mediator.Send(new AddOrUpdateDeliveryBatchItemCommand(procId, version, userId, request.Refresh), cancellationToken));
        }

        if (!result.Success)
        {
            var stringBuilder = stringBuilderPool.Get();
            result.Message = result.GetMessage(stringBuilder);
            stringBuilderPool.Return(stringBuilder);
        }

        return result;
    }
}