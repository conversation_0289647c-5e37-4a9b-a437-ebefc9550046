﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class CurrencyRepository(
    IFreeSql<PlatformFreeSql> freeSql, 
    IMemoryCache memoryCache, 
    IRedisCache<RedisCacheOptionsBase> redisCache,
    CacheExpirationToken<BasCurrency> expirationToken) :
    BaseRepository<BasCurrency, string>(freeSql), ICurrencyRepository
{
    public IMemoryCache MemoryCache { get; } = memoryCache;

    public CacheExpirationToken<BasCurrency> ExpirationToken { get; } = expirationToken;

    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}