﻿using iPlatformExtension.Finance.Applications.Queries;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultApplyTypeQueryHandler(IApplyTypeRepository applyTypeRepository)
    : IRequestHandler<FeeResultApplyTypeQuery>
{
    public async Task Handle(FeeResultApplyTypeQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return;

        foreach (var feeItem in request.FeeResults)
        {
            feeItem.ApplyType = await applyTypeRepository.GetChineseKeyValueAsync(feeItem.ApplyType.Key);
        }
    }
}