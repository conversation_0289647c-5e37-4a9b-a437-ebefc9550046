﻿using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;
using MediatR;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Linq;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery
{
    public class UpdateCaseProcInfoHandler : IRequestHandler<UpdateCaseProcInfoCommand, bool>
    {
        private readonly IMediator _mediator;
        private readonly ILogger _logger;
        private readonly ICaseProcInfoRepository _caseProcInfoRepository;
        private readonly ICaseExtendInfoRepository _caseExtendInfoRepository;
        private readonly IFlowHistoryRepository _flowHistoryRepository;

        public UpdateCaseProcInfoHandler(IMediator mediator, ILoggerFactory loggerFactory, ICaseProcInfoRepository caseProcInfoRepository, ICaseExtendInfoRepository caseExtendInfoRepository, IFlowHistoryRepository flowHistoryRepository)
        {
            _mediator = mediator;
            _logger = loggerFactory.CreateLogger(GetType());
            _caseProcInfoRepository = caseProcInfoRepository;
            _caseExtendInfoRepository = caseExtendInfoRepository;
            _flowHistoryRepository = flowHistoryRepository;
        }

        public async Task<bool> Handle(UpdateCaseProcInfoCommand request, CancellationToken cancellationToken)
        {
            var caseProcInfo = await _caseProcInfoRepository.Where(o => o.ProcId == request.InfoDto.ProcId).FirstAsync(cancellationToken);
            if (caseProcInfo != null)
            {
                if (!string.IsNullOrEmpty(request.InfoDto.FirstExamineUserId))
                {
                    caseProcInfo.FirstExamineUserId = request.InfoDto.FirstExamineUserId;
                }
                caseProcInfo.SubProcStatusId = request.InfoDto.SubProcStatus;
                caseProcInfo.ProcStatusId = request.InfoDto.ProcStatus;
                if (!string.IsNullOrEmpty(request.InfoDto.LogoFileID))
                {
                    var ext = _caseExtendInfoRepository.Where(o => o.CaseId == request.InfoDto.CaseId).First();
                    if (ext != null)
                    {
                        ext.PicFileNo = $"a002{request.InfoDto.LogoFileID}";
                        await _caseExtendInfoRepository.UpdateAsync(ext);
                    }
                }
                if (request.InfoDto.AllocateDate.HasValue)
                {
                    caseProcInfo.AllocateDate = request.InfoDto.AllocateDate.Value;
                }

                if (request.InfoDto.IsAddProc.HasValue)
                {
                    caseProcInfo.IsAddProc = request.InfoDto.IsAddProc.Value.ToString();
                }
                if (!string.IsNullOrEmpty(request.InfoDto.ProcNote))
                {
                    caseProcInfo.ProcNote += request.InfoDto.ProcNote;
                }


                if (request.InfoDto.ProcStatus == "TYCL")
                {
                    //deli_info.status
                    caseProcInfo.FinishDate = DateTime.Now;
                    if (caseProcInfo.DeliInfo.IsAuto.Value)
                    {
                        caseProcInfo.SendOfficialDate = caseProcInfo.DeliInfo.DeliveryDate;
                    }
                    else
                    {
                        var flowHistory = await _flowHistoryRepository.Where(o => o.ObjId == request.InfoDto.ProcId && o.FlowNode.NodeCode == "DJ").OrderByDescending(o => o.AuditTime).FirstAsync();
                        caseProcInfo.SendOfficialDate = flowHistory?.AuditTime;
                    }
                }
                await _caseProcInfoRepository.UpdateAsync(caseProcInfo, cancellationToken);
                return true;
            }
            return false;
        }
    }
}
