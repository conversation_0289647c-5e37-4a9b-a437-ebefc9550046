﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailService.Applications.Commands;
using iPlatformExtension.MailService.Extensions;
using iPlatformExtension.MailService.Infrastructure;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MailKit.Net.Imap;
using MailKit.Net.Pop3;
using MailKit.Security;
using MediatR;
using MimeKit;
using System;
using System.Diagnostics;
using CSScriptLib;
using System.Runtime.Intrinsics.X86;
using iPlatformExtension.MailService.Applications.Models;
using MimeKit.Text;
using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Model.Enum;

namespace iPlatformExtension.MailService.Applications.Handlers
{
    /// <summary>
    /// POP收件
    /// </summary>
    public class MailReceiveByPopHandler(IFreeSql<MailCenterFreeSql> freeSql,
        ILogger<MailReceiveHandler> logger,
        MailTool mailTool,
        IMailAttachmentsRepository mailAttachmentsRepository,
        IWxWorkClient wxWorkClientExtension,
        IMailReceiveRepository mailReceiveRepository) : IRequestHandler<ReceiveMailByPopCommand>
    {
        public async Task Handle(ReceiveMailByPopCommand request, CancellationToken cancellationToken)
        {
            try
            {
                using (var client = new Pop3Client())
                {
                    client.Timeout = 300000;
                    await client.ConnectAsync(request.ConfigDto.ImapHost, request.ConfigDto.ImapPort.Value, SecureSocketOptions.None, cancellationToken);
                    await client.AuthenticateAsync(request.ConfigDto.SenderAccount, request.ConfigDto.SenderPassword, cancellationToken);

                    // 获取收件箱中的邮件数量
                    var uids = await client.GetMessageUidsAsync(cancellationToken);
                    int messageCount = uids.Count();
                    Console.WriteLine("收件箱中共有 {0} 封邮件", messageCount);
                    string id = "";
                    // 遍历每封邮件并输出主题
                    int dateLimit = 5;
                    int repeatLimit = request.repeatLimit;
                    for (int i = 1; i <= messageCount; i++)
                    {
                        var uid = uids[messageCount - i];
                        try
                        {
                            Stopwatch stopwatch = new Stopwatch();
                             stopwatch.Start();
                            var res = await freeSql.Select<MailReceive>().WithLock().Where(o => o.Uid == uid && o.HostId == request.ConfigDto.HostId).FirstAsync(o => o.MailId, cancellationToken);
                            if (res != null)
                            {
                                Console.WriteLine($"{uid}已入库,跳过,线程数量:{mailTool.TryGetRecord().Count()}---序号:{Thread.CurrentThread.ManagedThreadId},{request.ConfigDto.SenderAccount}总量:{i}/{messageCount}");
                                repeatLimit--;
                                if (repeatLimit == 0)
                                {
                                    Console.WriteLine($"连续多次重复,停止获取,线程数量:{mailTool.TryGetRecord().Count()}---{Thread.CurrentThread.ManagedThreadId},{request.ConfigDto.SenderAccount}总量:{i}/{uids.Count}");
                                    break;
                                }
                                continue;
                            }
                            repeatLimit = request.repeatLimit;
                            var message = await client.GetMessageAsync(messageCount - i, cancellationToken);
                            if (message.Date.LocalDateTime.Date < DateTime.Now.AddDays(-2).Date)
                            {
                                Console.WriteLine("超出日期邮件不再收取", messageCount);
                                dateLimit--;
                                if (dateLimit == 0)
                                {
                                    break;
                                }
                                continue;
                            }
                            dateLimit = 5;
                            id = uid.ToString();
                            var mailNo = mailTool.GetReceiptNumber();
                            var mailId = Guid.NewGuid().ToString();
                            var mailUsers = mailTool.GetMailUsers(message, mailId);
                            var size = await mailTool.CalculateEmailSize(message, cancellationToken);
                            var cids = await mailTool.DownloadCid(message, uid, cancellationToken);
                            var emlUrl = await mailTool.DownloadMailEmlAsync(message, mailNo);
                            var mailAttachments = await mailTool.DownloadAttachFile(message, mailId, cancellationToken);

                            var fromUser = mailUsers.Where(o => o.AddressType == AddressTypeEnum.From).Select(o => $"{o.DisplayName}<{o.MailAddress}>").First();
                            var ccUsers = mailUsers.Where(o => o.AddressType == AddressTypeEnum.Cc).Select(o => $"{o.DisplayName}<{o.MailAddress}>").ToList();

                            var htmlBody = message.HtmlBody;
                            if (message.HtmlBody is null)
                            {
                                if (message.Body is MimeKit.Multipart multipart)
                                {
                                    foreach (var part in multipart)
                                    {
                                        if (part is MimeKit.TextPart textPart && textPart.Format == TextFormat.Html)
                                        {
                                            htmlBody = textPart.Text;
                                            break;
                                        }
                                    }
                                }
                            }


                            var mailReceive = new MailReceive
                            {
                                MailId = mailId,
                                Uid = uid,
                                HostId = request.ConfigDto.HostId,
                                MailHtmlBody = mailTool.ReplaceCid(htmlBody, cids),
                                MailCc = string.Join(",", ccUsers),
                                MailDate = message.Date.LocalDateTime,
                                MailFrom = fromUser,
                                MailTextBody = mailTool.ReplaceCid(message.TextBody, cids),
                                // MailHeader = string.Join(",", message.Headers),
                                MailSubject = message.Subject,
                                MailTo = $"{request.ConfigDto.ShowName}<{request.ConfigDto.SenderAccount}>",
                                MailSize = size,
                                CreateTime = DateTime.Now,
                                CreateBy = "system",
                                Attachments = message.Attachments.Count(),
                                MailNo = mailNo,
                                MailPriority = message.XPriority.ToString(),
                                MailEmlUrl = emlUrl
                            };

                            // await mailReceiveRepository.InsertAsync(mailReceive, cancellationToken);
                            await freeSql.Insert(mailReceive).ExecuteAffrowsAsync(cancellationToken);
                            if (mailAttachments != null && mailAttachments.Count > 0)
                            {
                                // await mailAttachmentsRepository.InsertAsync(mailAttachments, cancellationToken);
                                await freeSql.Insert(mailAttachments).ExecuteAffrowsAsync(cancellationToken);
                            }

                            if (mailUsers.Any())
                            {
                                await freeSql.Insert(mailUsers).ExecuteAffrowsAsync(cancellationToken);
                            }
                            TimeSpan elapsedTime = stopwatch.Elapsed;
                            Console.WriteLine($"线程数量:{mailTool.TryGetRecord().Count()}---序号:{Thread.CurrentThread.ManagedThreadId},{request.ConfigDto.SenderAccount}总量:{i}/{messageCount},LongRunningMethod 耗时: {elapsedTime.TotalSeconds} 秒");
                            await Task.Delay(TimeSpan.FromMilliseconds(100), cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, $"{request.ConfigDto.SenderAccount}-{id}邮箱获取过程中失败");
                            continue;
                        }
                    }
                    await client.DisconnectAsync(true, cancellationToken);
                    Task.CompletedTask.Wait();
                    mailTool.TryGetRecord().TryRemove(request.ConfigDto.SenderAccount.ToString(), out string v);
                    Console.WriteLine($"{request.ConfigDto.SenderAccount}获取完毕,释放连接");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("邮箱启动失败");
                mailTool.TryGetRecord().TryRemove(request.ConfigDto.SenderAccount.ToString(), out string v);
                logger.LogError(ex, $"{request.ConfigDto.SenderAccount}邮箱启动失败");
                if (!request.ConfigDto.IsPrivate)
                {
                    await wxWorkClientExtension.SentRobotMessageAsync($"{request.ConfigDto.SenderAccount}邮箱启动失败，确认最近是否有修改密码，请及时处理。", "MailReceive");
                }
                Task.CompletedTask.Wait();
            }
        }
    }
}
