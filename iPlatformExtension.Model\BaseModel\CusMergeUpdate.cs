using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_merge_update", DisableSyncStructure = true)]
	public partial class CusMergeUpdate {

		[ Column(Name = "obj_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ObjId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "error_message", StringLength = 1000)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "is_del")]
		public bool? IsDel { get; set; }

		[ Column(Name = "is_do")]
		public bool? IsDo { get; set; } = false;

		[ Column(Name = "merge_id", StringLength = 50)]
		public string MergeId { get; set; }

		[ Column(Name = "new_id", StringLength = 50)]
		public string NewId { get; set; }

		[ Column(Name = "new_name", StringLength = 500)]
		public string NewName { get; set; }

		[ Column(Name = "old_id", StringLength = 50)]
		public string OldId { get; set; }

		[ Column(Name = "old_name", StringLength = 500)]
		public string OldName { get; set; }

	}

}
