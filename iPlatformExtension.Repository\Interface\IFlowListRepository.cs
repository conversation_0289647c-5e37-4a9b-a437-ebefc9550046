﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.Flow;

namespace iPlatformExtension.Repository.Interface;

public interface IFlowListRepository: IBaseRepository<SysFlowList, string>,
        ICacheableRepository<FlowListKey, IEnumerable<FlowListNode>>,
        IScopeDependency
{
    FlowListKey ICacheableRepository<FlowListKey, IEnumerable<FlowListNode>>.GenerateKey(IEnumerable<FlowListNode> value)
    {
        if (value is IGrouping<FlowListKey, FlowListNode> group)
        {
            return group.Key;
        }

        var node = value.First();
        return new FlowListKey(node.FlowType, node.FlowSubType, node.DepartmentId);
    }

    async Task<IEnumerable<IEnumerable<FlowListNode>>> ICacheableRepository<FlowListKey, IEnumerable<FlowListNode>>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        var flowConfigs = await Select.From<SysFlowConfig, SysFlowNode>().WithLock()
            .InnerJoin((list, config, node) => list.FlowId == config.FlowId)
            .InnerJoin((list, config, node) => list.NodeId == node.NodeId)
            .Where((list, config, node) => config.DeptId == "base")
            .Where((list, config, node) => config.IsEnabled == true)
            .OrderBy((list, config, node) => list.Seq)
            .ToListAsync((list, config, node) => new FlowListNode
            {
                FlowId = config.FlowId,
                DepartmentId = config.DeptId,
                FlowType = config.FlowType,
                FlowSubType = config.FlowSubType,
                NodeId = list.NodeId,
                Next = list.Next,
                Seq = list.Seq!.Value,
                Users = list.UserList,
                NodeName = node.NameZhCn
            }, cancellationToken);

        return flowConfigs.GroupBy(node => new FlowListKey(node.FlowType, node.FlowSubType, node.DepartmentId), KeyEqualityComparer);
    }

    async Task<IEnumerable<FlowListNode>?> ICacheableRepository<FlowListKey, IEnumerable<FlowListNode>>.GetValueFromDbAsync(FlowListKey key, CancellationToken cancellationToken)
    {
        return await Select.From<SysFlowConfig, SysFlowNode>().WithLock()
            .InnerJoin((list, config, node) => list.FlowId == config.FlowId)
            .InnerJoin((list, config, node) => list.NodeId == node.NodeId)
            .Where((list, config, node) => config.DeptId == key.DepartmentId)
            .Where((list, config, node) => config.FlowType == key.FlowType)
            .Where((list, config, node) => config.FlowSubType == key.FlowSubType)
            .Where((list, config, node) => config.IsEnabled == true)
            .OrderBy((list, config, node) => list.Seq)
            .ToListAsync((list, config, node) => new FlowListNode
            {
                FlowId = config.FlowId,
                DepartmentId = config.DeptId,
                FlowType = config.FlowType,
                FlowSubType = config.FlowSubType,
                NodeId = list.NodeId,
                Next = list.Next,
                Seq = list.Seq!.Value,
                Users = list.UserList,
                NodeName = node.NameZhCn
            }, cancellationToken);
    }
}