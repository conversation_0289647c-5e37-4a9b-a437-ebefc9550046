using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_proc_config_history", DisableSyncStructure = true)]
	public partial class SysProcConfigHistory {

		[ Column(Name = "history_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string HistoryId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "config_ids", StringLength = 2000)]
		public string ConfigIds { get; set; }

		[ Column(Name = "config_types", StringLength = 500)]
		public string ConfigTypes { get; set; }

		[ Column(Name = "filter_id", StringLength = 50)]
		public string FilterId { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "result", StringLength = -2)]
		public string Result { get; set; }

		[ Column(Name = "stage", StringLength = 50)]
		public string Stage { get; set; }

		[ Column(Name = "status")]
		public int? Status { get; set; } = 0;

		[ Column(Name = "trigger_notice_id", StringLength = 50)]
		public string TriggerNoticeId { get; set; }

		[ Column(Name = "trigger_proc_id", StringLength = 50)]
		public string TriggerProcId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
