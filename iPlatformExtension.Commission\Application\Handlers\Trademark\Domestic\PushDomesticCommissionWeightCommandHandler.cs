﻿using FreeSql.Internal.Model;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core.Utils;
using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Commission.Grpc;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class PushDomesticCommissionWeightCommandHandler(
    IFreeSql freeSql,
    ISender sender,
    CommissionService.CommissionServiceClient commissionServiceClient) 
    : IRequestHandler<PushDomesticCommissionWeightCommand>
{
    public async Task Handle(PushDomesticCommissionWeightCommand request, CancellationToken cancellationToken)
    {
        var (userId, deptIds, year, month) = request;

        var defaultFilter = new DynamicFilterInfo
        {
            Field = nameof(DomesticTrademarkCommission.Pushed),
            Operator = DynamicFilterOperator.Equals,
            Value = false,
            Logic = DynamicFilterLogic.Or,
            Filters =
            [
                new DynamicFilterInfo
                {
                    Field = nameof(DomesticTrademarkCommission.ProcMainUndertakerId),
                    Operator = DynamicFilterOperator.Equals,
                    Value = userId
                }
            ]
        };

        if (deptIds.Any())
        {
            defaultFilter.Filters.Add(new DynamicFilterInfo
            {
                Field = nameof(DomesticTrademarkCommission.DeptId),
                Operator = DynamicFilterOperator.Any,
                Value = deptIds
            });
        }

        var commissions = await freeSql.Select<DomesticTrademarkCommission>().WithLock()
            .WhereDynamicFilter(defaultFilter)
            .Where(commission => commission.Year == year && commission.Month == month)
            .ToListAsync(cancellationToken);
        
        if (commissions.Count == 0)
            return;

        var context = commissionServiceClient.PushCommissionWeights(cancellationToken: cancellationToken);
        var requestStream = context.RequestStream;
        var responseStream = context.ResponseStream;

        var responseCallbackTask =
            Task.Run(
                () => responseStream.ForEachAsync(result =>
                    sender.Send(new DomesticCommissionPushedResultCommand(result), cancellationToken)),
                cancellationToken);
        
        foreach (var commission in commissions)
        {
            var commissionWeight = new CommissionWeight
            {
                Year = commission.Year,
                Month = commission.Month,
                Volume = commission.Volume,
                ProcNo = commission.ProcNo,
                CustomerName = commission.CustomerName,
                CaseDirection = CaseDirection.II,
                ProcName = commission.CtrlProcZhCn,
                CommissionDate = Timestamp.FromDateTime(TimeZoneInfo.ConvertTimeToUtc(commission.CommissionDate, TimeZoneInfo.Local)),
                ProcId = commission.ProcId,
                CustomerId = commission.CustomerId,
                CaseType = CaseType.Trade,
                UndertakerUsername = commission.UserName,
                UndertakerName = commission.CnName,
                Weight = new UserWeight
                {
                    Username = commission.UserName,
                    CnName = commission.CnName,
                    Weight = (commission.EditedProcPoint ?? commission.ProcPoint).ToString("F4"),
                    DeptName = commission.DeptName,
                    DeptId = commission.DeptId,
                    DistrictCode = commission.DistrictCode,
                    DistrictName = commission.DistrictName
                },
                Id = commission.ProcId
            };
            
            await requestStream.WriteAsync(commissionWeight, cancellationToken);
        }

        await requestStream.CompleteAsync();

        await responseCallbackTask;
    }
}