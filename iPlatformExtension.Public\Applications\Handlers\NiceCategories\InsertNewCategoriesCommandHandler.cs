﻿using System.Buffers;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Public.Applications.Commands.NiceCategories;
using MediatR;
using Microsoft.Data.SqlClient;

namespace iPlatformExtension.Public.Applications.Handlers.NiceCategories;

internal sealed class InsertNewCategoriesCommandHandler(IFreeSql freeSql, IHostEnvironment hostEnvironment)
    : IRequestHandler<InsertNewCategoriesCommand>
{
    public async Task Handle(InsertNewCategoriesCommand request, CancellationToken cancellationToken)
    {
        const int parentCacheCount = 500;
        var bufferLength = Enumerable.Count<NiceCategoryInfo>(request.NiceCategoryInfos);
        
        var versionId = request.VersionId;
        var parentCategoryCache = new Dictionary<string, NiceCategoryInfo>(parentCacheCount);
        var niceCategoryLevels = ArrayPool<BasTrademarkItemsLevel>.Shared.Rent(bufferLength);
        var niceCategoryItems = ArrayPool<BasTrademarkItems>.Shared.Rent(bufferLength);

        var index = 0;
        foreach (var niceCategoryInfo in Enumerable.OrderBy<NiceCategoryInfo, int>(request.NiceCategoryInfos, info => Convert.ToInt32((string?) info.Level)))
        {
            var levelValue = Convert.ToInt32(niceCategoryInfo.Level);
            if (levelValue is 0 or 1)
            {
                parentCategoryCache.TryAdd(niceCategoryInfo.CategoryId, niceCategoryInfo);
            }

            NiceCategoryInfo? parentNiceCategoryInfo;
            NiceCategoryInfo? grandParentNiceCategoryInfo;
            switch (levelValue)
            {
                case 0:
                    grandParentNiceCategoryInfo = parentNiceCategoryInfo = null;
                    break;
                case 1:
                    grandParentNiceCategoryInfo = null;
                    parentNiceCategoryInfo = parentCategoryCache[niceCategoryInfo.ParentCategory];
                    break;
                case 2:
                    parentNiceCategoryInfo = parentCategoryCache[niceCategoryInfo.ParentCategory];
                    grandParentNiceCategoryInfo = parentCategoryCache[parentNiceCategoryInfo.ParentCategory];
                    break;
                default:
                    throw new IndexOutOfRangeException("尼斯分类层级关系超过枚举范围");
            }
            
            var level = new BasTrademarkItemsLevel()
            {
                CreateTime = DateTime.Now,
                CreateUserId = request.OperatorId,
                CurId = niceCategoryInfo.CategoryId,
                PId = parentNiceCategoryInfo?.CategoryNumber ?? string.Empty,
                FpId = grandParentNiceCategoryInfo?.CategoryNumber ?? string.Empty,
                Lid = niceCategoryInfo.CategoryNumber,
                Seq = Convert.ToInt32(niceCategoryInfo.Sort),
                IsEnabled = true,
                IsOpen = true,
                VersionId = versionId,
                UpdateTime = DateTime.Now,
                UpdateUserId = request.OperatorId,
                Level = levelValue
            };
            level.TrademarkItems = new BasTrademarkItems()
            {
                CreateTime = DateTime.Now,
                CreateUserId = request.OperatorId,
                HistoryId = level.HistoryId,
                Id = level.Lid,
                CurId = level.CurId,
                IsEnabled = true,
                ItemType = "尼斯商品",
                TextZhCn = niceCategoryInfo.CategoryName,
                Remark = niceCategoryInfo.CategoryIntroduction,
                VersionId = versionId,
                UpdateTime = DateTime.Now,
                UpdateUserId = request.OperatorId,
                TextEnUs = niceCategoryInfo.CategoryName,
                TextJaJp = niceCategoryInfo.CategoryName
            };

            niceCategoryLevels[index] = level;
            niceCategoryItems[index] = level.TrademarkItems;

            index++;
        }

        var bulkCopyTimeout = hostEnvironment.IsProduction() ? 30 : 60;
        
        await freeSql.Insert(
                new ArraySegment<BasTrademarkItemsLevel>(niceCategoryLevels, 0, bufferLength))
            .ExecuteSqlBulkCopyAsync(SqlBulkCopyOptions.TableLock, bufferLength, bulkCopyTimeout, cancellationToken);
        await freeSql.Insert(
                new ArraySegment<BasTrademarkItems>(niceCategoryItems, 0, bufferLength))
            .ExecuteSqlBulkCopyAsync(SqlBulkCopyOptions.TableLock, bufferLength, bulkCopyTimeout, cancellationToken);
        
        ArrayPool<BasTrademarkItemsLevel>.Shared.Return(niceCategoryLevels);
        ArrayPool<BasTrademarkItems>.Shared.Return(niceCategoryItems);
    }
}