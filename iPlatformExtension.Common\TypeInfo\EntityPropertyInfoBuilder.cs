﻿using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using System.Reflection;
using FreeSql.DataAnnotations;
using iPlatformExtension.Common.Office.Excel.Models;
using iPlatformExtension.Model.Attributes;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Common.TypeInfo;

internal readonly struct EntityPropertyInfoBuilder(PropertyInfo propertyInfo)
{
    private readonly EntityPropertyInfo _entityPropertyInfo = new()
    {
        PropertyName = propertyInfo.Name,
        PropertyType = propertyInfo.PropertyType
    };

    public EntityPropertyInfoBuilder BuildDbColumnInfo()
    {
        var columnInfo = propertyInfo.GetCustomAttribute<ColumnAttribute>();
        if (columnInfo is null)
        {
            _entityPropertyInfo.ColumnIgnore = true;
        }
        else
        {
            _entityPropertyInfo.ColumnIgnore = columnInfo.IsIgnore;
            _entityPropertyInfo.ColumnName = columnInfo.Name ?? propertyInfo.Name;
            _entityPropertyInfo.IsKey = columnInfo.IsPrimary;
        }

        return this;
    }

    public EntityPropertyInfoBuilder BuildModelBindInfo()
    {
        var modelBindPropertyInfo = propertyInfo.GetCustomAttribute<BindPropertyAttribute>();
        if (modelBindPropertyInfo is not null)
        {
            _entityPropertyInfo.ModelBindPropertyName = modelBindPropertyInfo.Name;
        }

        return this;
    }

    public EntityPropertyInfoBuilder BuildGetter(System.Reflection.TypeInfo entityTypeInfo)
    {
        var propertyType = _entityPropertyInfo.PropertyType;
        var entityType = entityTypeInfo.AsType();
        
        var getMethod = propertyInfo.GetGetMethod();
        if (getMethod is not null)
        {
            
            var instance = Expression.Parameter(typeof(object));
            var instanceConvert = propertyType.IsValueType
                ? Expression.Convert(instance, entityType)
                : Expression.TypeAs(instance, entityType);
            var body = Expression.Property(instanceConvert, propertyInfo);
            var returnCast = Expression.Convert(body, typeof(object));
            _entityPropertyInfo.Get = Expression.Lambda<Func<object, object?>>(returnCast, instance).Compile();
        }

        return this;
    }

    public EntityPropertyInfoBuilder BuildSetter(System.Reflection.TypeInfo entityTypeInfo)
    {
        var propertyType = _entityPropertyInfo.PropertyType;
        var entityType = entityTypeInfo.AsType();
        
        var setMethod = propertyInfo.GetSetMethod();
        if (setMethod is not null)
        {
            var instance = Expression.Parameter(typeof(object));
            var instanceConvert = Expression.Convert(instance, entityType);
            var parameter = Expression.Parameter(typeof(object));
            var parameterConvert = Expression.Convert(parameter, propertyType);
            var body = Expression.Call(instanceConvert, setMethod, parameterConvert);
            _entityPropertyInfo.Set = Expression.Lambda<Action<object, object?>>(body, instance, parameter).Compile();
        }

        return this;
    }

    public EntityPropertyInfoBuilder BuildDisplayInfo()
    {
        var displayInfo = propertyInfo.GetCustomAttribute<DisplayAttribute>();
        _entityPropertyInfo.DisplayInfo = displayInfo;

        var hideInfo = propertyInfo.GetCustomAttribute<HideAttribute>();
        _entityPropertyInfo.IsHidden = hideInfo is not null;

        var keyValueDisplayInfo = propertyInfo.GetCustomAttribute<KeyValueDisplayAttribute>();
        _entityPropertyInfo.KeyValueDisplayInfo = keyValueDisplayInfo;

        return this;
    }

    public EntityPropertyInfoBuilder BuildExcelInfo()
    {
        var genericType = Nullable.GetUnderlyingType(propertyInfo.PropertyType);
        var excelColumn = propertyInfo.GetCustomAttribute<ExcelColumnAttribute>();
        var excelColumnName = propertyInfo.GetCustomAttribute<ExcelColumnNameAttribute>();
        var excelFormat = propertyInfo.GetCustomAttribute<ExcelFormatAttribute>()?.Format;
        var excelColumnIndex = propertyInfo.GetCustomAttribute<ExcelColumnIndexAttribute>();
        var excelColumnWidth = propertyInfo.GetCustomAttribute<ExcelColumnWidthAttribute>()?.ExcelColumnWidth;
        var excelIgnore = propertyInfo.GetCustomAttribute<ExcelIgnoreAttribute>();
        
        var excelColumnInfo = new ExcelColumnInfo
        {
            ExcelColumnIndex = excelColumnIndex?.ExcelColumnIndex ?? excelColumn?.Index,
            ExcelColumnName = excelColumnName?.ExcelColumnName ?? excelColumn?.Name ?? propertyInfo.Name,
            ExcelColumnAliases = excelColumnName?.Aliases ?? excelColumn?.Aliases ?? [],
            ExcludeNullableType = genericType ?? propertyInfo.PropertyType,
            Nullable = genericType is not null,
            ExcelFormat = excelFormat ?? excelColumn?.Format,
            ExcelColumnWidth = excelColumnWidth ?? excelColumn?.Width,
            ExcelIndexName = excelColumn?.IndexName,
            ExcelIgnore = excelIgnore?.ExcelIgnore ?? excelColumn?.Ignore ?? false,
            PropertyInfo = _entityPropertyInfo
        };

        _entityPropertyInfo.ExcelColumnInfo = excelColumnInfo;

        return this;
    }

    public EntityPropertyInfo Build() => _entityPropertyInfo;
}