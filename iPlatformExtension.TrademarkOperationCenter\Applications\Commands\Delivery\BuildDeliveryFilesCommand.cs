﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

internal record BuildDeliveryFilesCommand(string ProcId, IEnumerable<string> CaseFileIds)
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;