﻿using MediatR;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Copy;

/// <summary>
/// 多对多复制
/// </summary>
/// <param name="Table"></param>
/// <param name="SourceId"></param>
/// <param name="TargetId"></param>
/// <param name="CopyFieldList"></param>
internal sealed record CopyManyCommand(
    string Table, 
    [Required] string SourceId, 
    [Required] string[] TargetId, 
    [Required] List<Tuple<string, string,string>> CopyFieldList) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

