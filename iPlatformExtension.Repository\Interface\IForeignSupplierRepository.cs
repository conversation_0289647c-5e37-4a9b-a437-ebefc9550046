﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Interface;

public interface IForeignSupplierRepository : 
        IBaseRepository<ForeignSupplier, long>,
        IRedisCacheableRepository<string, ForeignSupplier>,
        IStringKeyCacheableRepository<ForeignSupplier>,
        IScopeDependency
{
    string? IStringKeyCacheableRepository<ForeignSupplier>.GetCacheTextValue(ForeignSupplier value)
    {
        return value.CnName;
    }

    Task<ForeignSupplier?> ICacheableRepository<string, ForeignSupplier>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Where(supplier => supplier.SupplierId == key).ToOneAsync(cancellationToken)!;
    }

    async Task<IEnumerable<ForeignSupplier>> ICacheableRepository<string, ForeignSupplier>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, ForeignSupplier>.GenerateKey(ForeignSupplier value)
    {
        return value.SupplierId;
    }
}