﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal abstract class DeliveryCommandHandlerBase(
    IMediator mediator, 
    IRedisCache<RedisCacheOptionsBase> redisCache,
    IDeliveryInfoRepository deliveryInfoRepository) 
    : ISendDeliveryCommandHandler
{
    protected readonly IMediator _mediator = mediator;
    
    protected readonly IRedisCache<RedisCacheOptionsBase> _redisCache = redisCache;
    
    protected readonly IDeliveryInfoRepository _deliveryInfoRepository = deliveryInfoRepository;

    public virtual async Task<SendDeliveryInternalCommand?> HandleSendStartupCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        var deliveryInfo = context.DeliveryInfo;
        
        if (deliveryInfo.DeliveryType != "1")
        {
            throw new ApplicationException("非网交方式不支持自动递交");
        }
        
        if (!await _mediator.Send(new StartupDeliveryButtonQuery(deliveryInfo), cancellationToken))
        {
            throw new ApplicationException($"当前递交状态不能做{context.DeliveryOperation}操作");
        }
        
        return new SendDeliveryInternalCommand(new DeliveryMessage()
            {
                ProcId = deliveryInfo.ProcId,
                Version = deliveryInfo.Version
            },
            context.CurrentUserId ?? string.Empty, context.DeliveryOperation,
            context.MessageKey, null, true);
    }

    public virtual async Task<SendDeliveryInternalCommand?> HandleSendStopCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        var deliveryInfo = context.DeliveryInfo;
        if (!await _mediator.Send(new StopDeliveryButtonQuery(deliveryInfo), cancellationToken))
        {
            throw new ApplicationException($"当前递交状态不能做{context.DeliveryOperation}操作");
        }

        return new SendDeliveryInternalCommand(new DeliveryMessage()
            {
                ProcId = deliveryInfo.ProcId,
                Version = deliveryInfo.Version
            },
            context.CurrentUserId ?? string.Empty, context.DeliveryOperation,
            context.MessageKey, null, false);
    }

    public virtual async Task<SendDeliveryInternalCommand?> HandleSendWithdrawCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        var deliveryInfo = context.DeliveryInfo;
        if (!await _mediator.Send(new WithdrawDeliveryButtonQuery(deliveryInfo), cancellationToken))
        {
            throw new ApplicationException($"当前递交状态不能做{context.DeliveryOperation}操作");
        }

        if (deliveryInfo.DeliveryDate.HasValue)
        {
            var withdrawDeadline = deliveryInfo.DeliveryDate.Value.Date.AddHours(20);
            if (DateTime.Now >= withdrawDeadline)
            {
                throw new ApplicationException($"{withdrawDeadline.ToShortTimeString()}以后不能做撤回操作");
            }
        }

        if (await _redisCache.TryLockAsync(LockKey.DeliveringLockKey, deliveryInfo.ProcId, TimeSpan.FromMinutes(5)))
        {
            return new SendDeliveryInternalCommand(new DeliveryMessage()
            {
                ProcId = deliveryInfo.ProcId,
                Version = deliveryInfo.Version
            }, context.CurrentUserId ?? string.Empty, context.DeliveryOperation, context.MessageKey, null, false);
        }
        else
        {
            throw new ApplicationException($"递交任务{deliveryInfo.ProcId}递交或锁定中");
        }
    }

    public virtual async Task<SendDeliveryInternalCommand?> HandleSendCancelCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        var deliveryInfo = context.DeliveryInfo;
        
        deliveryInfo.IsAuto = false;
        deliveryInfo.OperationResult = true;
        deliveryInfo.Status = (int) DeliveryStatus.Complete;
        await _deliveryInfoRepository.UpdateAsync(deliveryInfo, cancellationToken);

        return new SendDeliveryInternalCommand(new DeliveryMessage()
            {
                ProcId = deliveryInfo.ProcId,
                Version = deliveryInfo.Version,
                OrderNo = deliveryInfo.OrderNo,
                DeliveryKey = deliveryInfo.DeliveryKey
            },
            context.CurrentUserId ?? string.Empty, context.DeliveryOperation, context.MessageKey, null, false);
    }
}