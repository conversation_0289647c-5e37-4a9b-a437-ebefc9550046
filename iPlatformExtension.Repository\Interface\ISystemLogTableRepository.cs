﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Repository.Interface;

public interface ISystemLogTableRepository : 
    IBaseRepository<SysLogTable, string>,
    IRedisCacheableRepository<DatabaseColumnKey, SysLogTable>,
    IScopeDependency
{
    Task<SysLogTable?> ICacheableRepository<DatabaseColumnKey, SysLogTable>.GetValueFromDbAsync(DatabaseColumnKey key, CancellationToken cancellationToken)
    {
        var (tableName, columnName) = key;
        return Where(table => table.TableId == tableName)
            .Where(column => column.ColumnId == columnName)
            .WithLock()
            .ToOneAsync(cancellationToken)!;
    }
    
    async Task<IEnumerable<SysLogTable>> ICacheableRepository<DatabaseColumnKey, SysLogTable>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Where(table => table.TableId == "case_proc_info")
            .WithLock()
            .ToListAsync(cancellationToken)!;
    }

    DatabaseColumnKey ICacheableRepository<DatabaseColumnKey, SysLogTable>.GenerateKey(SysLogTable value)
    {
        return new DatabaseColumnKey(value.TableId, value.ColumnId);
    }
}