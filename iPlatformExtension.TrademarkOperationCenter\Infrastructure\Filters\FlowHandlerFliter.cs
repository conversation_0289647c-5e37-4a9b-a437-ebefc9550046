﻿using Microsoft.AspNetCore.Mvc.Filters;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Filters
{
    public class FlowHandlerFliter : Attribute, IActionFilter
    {
        public void OnActionExecuted(ActionExecutedContext context)
        {
            throw new NotImplementedException();
        }

        public void OnActionExecuting(ActionExecutingContext context)
        {
            throw new NotImplementedException();
        }
    }
}
