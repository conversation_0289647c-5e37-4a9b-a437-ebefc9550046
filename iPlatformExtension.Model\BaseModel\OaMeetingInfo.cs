using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "oa_meeting_info", DisableSyncStructure = true)]
	public partial class OaMeetingInfo {

		/// <summary>
		/// 预定号id
		/// </summary>
		[ Column(Name = "meeting_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string MeetingId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "attend_meetings_id", StringLength = 4000)]
		public string AttendMeetingsId { get; set; }

		/// <summary>
		/// 开始时间
		/// </summary>
		[ Column(Name = "begin_time", StringLength = 50)]
		public string BeginTime { get; set; }

		/// <summary>
		/// 会议室编号
		/// </summary>
		[ Column(Name = "conference_room_id", StringLength = 50)]
		public string ConferenceRoomId { get; set; }

		/// <summary>
		/// 联系人部门
		/// </summary>
		[ Column(Name = "contacts_department", StringLength = 50)]
		public string ContactsDepartment { get; set; }

		/// <summary>
		/// 联系人邮箱
		/// </summary>
		[ Column(Name = "contacts_mail", StringLength = 50)]
		public string ContactsMail { get; set; }

		/// <summary>
		/// 联系人
		/// </summary>
		[ Column(Name = "contacts_name", StringLength = 50)]
		public string ContactsName { get; set; }

		/// <summary>
		/// 联系人手机
		/// </summary>
		[ Column(Name = "contacts_tel", StringLength = 50)]
		public string ContactsTel { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户id
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 结束时间
		/// </summary>
		[ Column(Name = "end_time", StringLength = 50)]
		public string EndTime { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		/// <summary>
		/// 会议编号
		/// </summary>
		[ Column(Name = "meeting_code", StringLength = 50)]
		public string MeetingCode { get; set; }

		/// <summary>
		/// 会议概要
		/// </summary>
		[ Column(Name = "meeting_content", StringLength = 500)]
		public string MeetingContent { get; set; }

		/// <summary>
		/// 订购日期
		/// </summary>
		[ Column(Name = "meeting_date")]
		public DateTime? MeetingDate { get; set; }

		/// <summary>
		/// 特别需求
		/// </summary>
		[ Column(Name = "meeting_demand", StringLength = 500)]
		public string MeetingDemand { get; set; }

		/// <summary>
		/// 会议主题
		/// </summary>
		[ Column(Name = "meeting_name", StringLength = 500)]
		public string MeetingName { get; set; }

		/// <summary>
		/// 与会人员
		/// </summary>
		[ Column(Name = "meeting_people_id", StringLength = 4000)]
		public string MeetingPeopleId { get; set; }

		/// <summary>
		/// 会议资源
		/// </summary>
		[ Column(Name = "meeting_resources", StringLength = 500)]
		public string MeetingResources { get; set; }

		/// <summary>
		/// 会议类型
		/// </summary>
		[ Column(Name = "meeting_type", StringLength = 50)]
		public string MeetingType { get; set; }

		/// <summary>
		/// 远程会议室编号
		/// </summary>
		[ Column(Name = "teleconference_id", StringLength = 50)]
		public string TeleconferenceId { get; set; }

		/// <summary>
		/// 修改时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 修改用户id
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
