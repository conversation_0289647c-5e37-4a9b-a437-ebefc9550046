using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.AuditUser;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AuditUser
{
    /// <summary>
    /// 保存发件审核人命令处理程序
    /// </summary>
    internal sealed class SaveAuditUserCommandHandler(
        IFlowAuditUserRepository flowAuditUserRepository,
        IUserInfoRepository userInfoRepository,
        IHttpContextAccessor httpContextAccessor) : IRequestHandler<SaveAuditUserCommand, List<string>>
    {
        public async Task<List<string>> Handle(SaveAuditUserCommand request, CancellationToken cancellationToken)
        {
            // 获取当前用户ID
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var now = DateTime.Now;

            // 初始化结果列表，用于存储新插入的记录ID
            var resultIds = new List<string>();

            // 处理审核人ID列表
            var auditingUserIds = new List<string>();

            // 如果传入了审核人ID列表，直接使用
            if (request.AuditingUserIds != null && request.AuditingUserIds.Count > 0)
            {
                auditingUserIds.AddRange(request.AuditingUserIds.Where(id => !string.IsNullOrEmpty(id)));
            }
            // 如果没有传入审核人ID列表但传入了审核人名称列表，则通过名称查找ID
            else if (request.AuditingUserNames != null && request.AuditingUserNames.Count > 0)
            {
                foreach (var userName in request.AuditingUserNames.Where(name => !string.IsNullOrEmpty(name)))
                {
                    // 通过用户名查询用户ID
                    var userInfo = await userInfoRepository.Where(u => u.CnName == userName)
                        .FirstAsync(cancellationToken);

                    if (userInfo != null)
                    {
                        auditingUserIds.Add(userInfo.UserId);
                    }
                }
            }

            // 如果审核人ID列表为空，则抛出异常
            if (auditingUserIds.Count == 0)
            {
                throw new ApplicationException("审核人ID列表不能为空，且未能通过审核人名称找到对应的用户");
            }

            // 为每个审核人创建一条记录
            foreach (var auditingUserId in auditingUserIds)
            {
                // 新增记录
                var newId = Guid.NewGuid().ToString();
                var flowAuditUser = new FlowAuditUser
                {
                    Id = newId,
                    AuditingId = auditingUserId,
                    CreateBy = userId,
                    CreateTime = now,
                    UpdateBy = userId,
                    UpdateTime = now
                };

                await flowAuditUserRepository.InsertAsync(flowAuditUser, cancellationToken);
                resultIds.Add(newId);
            }

            return resultIds;
        }
    }
}
