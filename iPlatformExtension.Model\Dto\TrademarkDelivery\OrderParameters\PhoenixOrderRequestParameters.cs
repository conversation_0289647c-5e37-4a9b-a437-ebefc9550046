﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;

/// <summary>
/// 权大师下单参数基类
/// </summary>
public abstract class PhoenixOrderRequestParameters : PhoenixRequestParameters
{
    /// <summary>
    /// 我方订单唯一编号
    /// 取任务id
    /// </summary>
    public string OrderToken { get; set; } = default!;

    /// <summary>
    /// 权大师系统的华进用户名
    /// </summary>
    public string UserName { get; set; } = "华进联合专利商标代理有限公司";

    /// <summary>
    /// 代理机构id
    /// </summary>
    public string AgentOrganId { get; set; } = "15864";
    
    /// <summary>
    /// 代理人电话
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? AgentOrganTel { get; set; }

    /// <summary>
    /// 代理机构名称
    /// </summary>
    public string AgentOrganName { get; set; } = "华进联合专利商标代理有限公司";
}