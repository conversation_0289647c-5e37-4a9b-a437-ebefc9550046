﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto
{

    /// <summary>
    ///请款对象返回model
    /// </summary>
    public class RequestObjectReDto
    {
        /// <summary>
        /// 纳税人资格
        /// </summary>
        public string IsGeneralTaxpayer { get; set; }

        /// <summary>
        /// 结算货币
        /// </summary>
        public string SettlementCurrency { get; set; }

        /// <summary>
        /// 账单语言
        /// </summary>
        public string BillLanguage { get; set; }

        /// <summary>
        /// 请款对象id
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 请款对象名称
        /// </summary>
        public string TextZhCn { get; set; }

        /// <summary>
        /// 英文名称
        /// </summary>
        public string TextEnUs { get; set; }

        /// <summary>
        /// 日文名称
        /// </summary>
        public string TextJaJp { get; set; }

        /// <summary>
        /// 发票抬头
        /// </summary>
        public string InvoicesTitle { get; set; }

        /// <summary>
        /// 纳税识别号
        /// </summary>
        public string IdentifyNumber { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string AddressCn { get; set; }

        /// <summary>
        /// 开户行
        /// </summary>
        public string BankName { get; set; }

        /// <summary>
        /// 开票代码
        /// </summary>
        public string InvoicesCode { get; set; }

        /// <summary>
        /// 是否有效（前端需要把无效的过滤掉）
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string AccountNo { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { get; set; } 

        /// <summary>
        /// 请款对象编码
        /// </summary>
        public string? RequestObjectCode { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        public string CustomerId { get; set; } = default!;

        /// <summary>
        /// 是否境外
        /// </summary>
        public bool IsOutbound { get; set; }
    }
}
