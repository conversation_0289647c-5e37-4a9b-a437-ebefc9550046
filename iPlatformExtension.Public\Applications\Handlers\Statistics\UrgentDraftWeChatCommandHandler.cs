﻿using iPlatformExtension.Common.Clients.BladeCommon;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Commands.Statistics;
using MediatR;
using static MongoDB.Driver.WriteConcern;
using System.Text;
using Microsoft.Extensions.ObjectPool;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using static iPlatformExtension.Model.Enum.SysEnum;
using Microsoft.Extensions.Hosting;

namespace iPlatformExtension.Public.Applications.Handlers.Statistics
{
    /// <summary>
    /// 微信催稿
    /// </summary>
    internal sealed class UrgentDraftWeChatCommandHandler(IFreeSql freeSql, BladeCommonClient bladeCommonClient,
        ObjectPool<StringBuilder> stringBuilderPool, IUserInfoRepository userInfoRepository, IHttpContextAccessor httpContextAccessor,
        IBaseCtrlProcRepository ctrlProcRepository, IBaseCaseStatusRepository iBaseCaseStatusRepository, IHostEnvironment hostEnvironment) : IRequestHandler<UrgentDraftWeChatCommand>
    {
        public async Task Handle(UrgentDraftWeChatCommand request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            if (string.IsNullOrWhiteSpace(userId))
            {
                throw new ArgumentNullException("UserId");
            }

            var chineseKeyValueAsync = await userInfoRepository.GetChineseKeyValueAsync(userId);
            var caseProcInfos = await freeSql.Select<CaseProcInfo>().Where(it => request.ProcId.Contains(it.ProcId))
                .ToOneAsync(it => new { it.CaseInfo.Volume, it.BasCtrlProc.CtrlProcZhCn, it.ProcNote, it.CaseInfo.CaseName, it.CaseId, it.ProcId, it.CtrlProcProperty }, cancellationToken);
            var warnTime = DateTime.Now;
            foreach (var urgentDraftUser in request.UrgentUserList)
            {
                var stringBuilder = stringBuilderPool.Get();
                stringBuilder.Append($"【催办提醒】{caseProcInfos.Volume}-{caseProcInfos.CtrlProcZhCn}");
                var warn = string.IsNullOrWhiteSpace(request.Warning) ? "您好！请及时处理以下任务，谢谢！" : request.Warning;
                stringBuilder.Append($"\r\n{warn}");
                stringBuilder.Append($"\r\n我方文号：{caseProcInfos.Volume}");
                var baseBasCaseStatus = string.IsNullOrWhiteSpace(caseProcInfos.CtrlProcProperty) ? null : await iBaseCaseStatusRepository.GetCacheValueAsync(caseProcInfos.CtrlProcProperty, cancellationToken: cancellationToken);
                var caseStatusZhCn = baseBasCaseStatus == null ? "" : "(" + baseBasCaseStatus?.CaseStatusZhCn + ")";
                stringBuilder.Append($"\r\n案件任务：{caseProcInfos.CtrlProcZhCn}{caseStatusZhCn}");
                stringBuilder.Append($"\r\n{((ReportType)request.DateType).GetDescription()}");
                stringBuilder.Append($"\r\n催稿人：{chineseKeyValueAsync.Value} 提醒时间：{warnTime.ToString("yyyy-MM-dd HH:mm")}");
                var hostUrl = hostEnvironment.IsProduction() ? "http://ipr.aciplaw.com/index.aspx" : "http://***************/index.aspx";

                await bladeCommonClient.SendTextCardNotificationAsync(new EnterpriseWechatNotification
                {
                    Title = "【本人期限前1天/当天任务提醒】",
                    //UserAccount = "H04788",
                    //UserAccount = "H05091",
                    UserAccount = urgentDraftUser.UserCode,
                    ButtonText = "立即查看",
                    Url = $"{hostUrl}?menu_code=case_info&obj_id={caseProcInfos.CaseId}",
                    Content = stringBuilder.ToString()
                });

                stringBuilderPool.Return(stringBuilder);
                await freeSql.Insert(new UrgentDraftHistory()
                {
                    CreateTime = warnTime,
                    DateType = request.DateType,
                    Msg = request.Warning ?? "",
                    ProcId = caseProcInfos.ProcId,
                    ReminderId = urgentDraftUser.UserId,
                    UrgentUserId = userId,
                    WarningType = 2
                }).WithTransaction(freeSql.Ado.TransactionCurrentThread).ExecuteAffrowsAsync(cancellationToken);
            }
        }

    }
}

