﻿using iPlatformExtension.Commission.Application.Notifications.WinningReward;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class CtrlProcRewardUserDisplayResultHandler(IBaseCtrlProcRepository baseCtrlProcRepository) 
    : INotificationHandler<RewardUserDisplayNotification>
{
    public async Task Handle(RewardUserDisplayNotification notification, CancellationToken cancellationToken)
    {
        var rewards = notification.UserRewards;
        foreach (var reward in rewards)
        {
            reward.CtrlProcName = await baseCtrlProcRepository.GetTextValueAsync(reward.CtrlProcName) ?? string.Empty;
        }
    }
}