﻿using FreeSql;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Service.Interface;

public interface ICustomerQueryService : IFreeSqlQueryService, ITransientDependency
{
    async Task<PageResult<CustomerReDto>> GetCusCustomerListAsync(string? customerName, bool? isEnabled, int page,
        int pageSize)
    {
        var queryData = await DbQuery.Select<CusCustomer>().WithLock()
            .From<SysUserInfo>((customerList, sysUserInfo)
                => customerList.LeftJoin(customer => customer.BusiUserId == sysUserInfo.UserId))
            .WhereIf(!string.IsNullOrEmpty(customerName), tuple => tuple.t1.CustomerFullName.Contains(customerName!) || tuple.t1.CustomerName.Contains(customerName!))
            .WhereIf(isEnabled is not null, tuple => tuple.t1.IsEnabled == isEnabled).Count(out var totalCount)
            .Page(page, pageSize).ToListAsync(p => new CustomerReDto
            {
                CustomerId = p.t1.CustomerId,
                CustomerName = p.t1.CustomerName,
                CustomerNameEn = p.t1.CustomerNameEn,
                CrmCustomerCode = p.t1.CrmCustomerCode,
                District = p.t1.District,
                IsEnabled = p.t1.IsEnabled,
                BusiUserId = p.t1.BusiUserId,
                BusiUserNum = p.t2.UserName,
                BusiUserName = p.t2.CnName,
                IsCooperation = p.t1.IsCooperation,
                Email = p.t1.Email
            });

        return new PageResult<CustomerReDto>
        {
            Data = queryData,
            Page = page,
            PageSize = pageSize,
            Total = totalCount
        };
    }

    async Task<CustomerReDto> GetCustomerDetailAsync(string customerId, bool? isEnabled = null)
    {
        return await DbQuery.Select<CusCustomer>().WithLock()
            .From<SysUserInfo>()
            .LeftJoin<SysUserInfo>((customer, sysUserInfo) => customer.BusiUserId == sysUserInfo.UserId)
            .Where(tuple => tuple.t1.CustomerId == customerId)
            .WhereIf(isEnabled.HasValue, tuple => tuple.t1.IsEnabled == isEnabled!.Value)
            .FirstAsync(p => new CustomerReDto
            {
                CustomerId = p.t1.CustomerId,
                CustomerName = p.t1.CustomerName,
                CustomerNameEn = p.t1.CustomerNameEn,
                CrmCustomerCode = p.t1.CrmCustomerCode,
                CrmCustomerId = p.t1.CrmCustomerId,
                District = p.t1.District,
                IsEnabled = p.t1.IsEnabled,
                BusiUserId = p.t1.BusiUserId,
                BusiUserNum = p.t2.UserName,
                BusiUserName = p.t2.CnName,
                IsCooperation = p.t1.IsCooperation,
                Email = p.t1.Email
            });
    }

    List<RequestObjectReDto> GetRequestObjectList(string billId)
    {
        var a = DbQuery.Select<CusRequestObject>()
            .Where(p => DbQuery.Select<BillInfo>()
                .From<CusCustomer>((a, b) => 
                    a.InnerJoin(c => c.CustomerId == b.CustomerId)).Where((a, b) => 
                    a.BillId == billId && (b.CustomerId == p.CustomerId || b.ParentId == p.CustomerId)).Any());
        return a.ToList(p => new RequestObjectReDto
        {
            IsGeneralTaxpayer = p.IsGeneralTaxpayer,
            SettlementCurrency = p.SettlementCurrency,
            BillLanguage = p.BillLanguage,
            Value = p.RequestObjectId,
            TextZhCn = (string.IsNullOrEmpty(p.RequestObjectCode) ? "" : p.RequestObjectCode) + " " + p.RequestObjectName + (p.RequestObjectName == p.InvoicesTitle ? "" : "[" + p.InvoicesTitle + "]"),
            TextEnUs = p.RequestObjectName,
            TextJaJp = p.RequestObjectName,
            InvoicesTitle = p.InvoicesTitle,
            IdentifyNumber = p.IdentifyNumber,
            AddressCn = p.AddressCn,
            BankName = p.BankName,
            InvoicesCode = p.InvoicesCode,
            IsEnabled = p.IsEnabled,
            AccountNo = p.AccountNo,
            Tel = p.Tel
        });
    }
        
    Task<List<CompanyReDto>> GetCompanyListAsync() =>
        DbQuery.Select<BasCompany>().WithLock()
            .Where(p => p.IsEnabled == true && (p.K3CCompanyCode != null || p.K3CCompanyCode != string.Empty))
            .OrderBy(p => p.Seq).ToListAsync(p => new CompanyReDto
            {
                Id = p.CompanyId,
                Value = p.CompanyId,
                TextZhCn = p.CompanyNameCn,
                TextEnUs = p.CompanyNameEn,
                TextJaJp = p.CompanyNameJp,
                TaxRate = p.TaxRate,
                DistrictId = p.DistrictId,
                Seq = p.Seq,
                CompanyCode = p.CompanyCode
            });

    List<DistrictReDto> GetDistrictList() =>
        DbQuery.Select<BasDistrict>().Where(p => p.IsEnabled == true).OrderBy(p => p.Seq).ToList(p => new DistrictReDto
        {
            Value = p.DistrictCode,
            TextZhCn = p.TextZhCn,
            TextEnUs = p.TextEnUs,
            TextJaJp = p.TextJaJp,
            Seq = p.Seq
        });

    List<CustomerAddressReDto> GetCustomerAddressList(string customerId, string? addressOrApplicant)
    {
        var strWhere = "";
        if (!string.IsNullOrEmpty(addressOrApplicant))
        {
            strWhere = " and (a.address_cn like N'%" + addressOrApplicant + "%' " + " or applicant_name_cn like N'%" + addressOrApplicant + "%' )";
        }
        var sql =
            $" with T as( select jc.form_obj_id, ja.join_obj_id, a.create_time, a.address_id, a.address_cn, a.address_en, a.is_enabled, a.is_default, a.form_type, a.obj_id, a.seq, a.update_time, applicant_name_cn from cus_customer cc  left  join cus_join_list jc on cc.customer_id = jc.join_obj_id left  join cus_join_list ja on jc.form_obj_id = ja.join_obj_id left join cus_address_list as a on a.address_id = ja.form_obj_id or a.address_id = jc.form_obj_id left join cus_applicant as c on ja.form_obj_id = c.applicant_id or jc.form_obj_id = c.applicant_id  where a.is_enabled = 1 and a.address_id is not null and (cc.customer_id = @customerId or cc.parent_id = @customerId) {strWhere} ) select address_cn AddressCn,address_en AddressEn,address_id AddressId,applicant_name_cn ApplicantNameCn,is_default IsDefault,is_enabled IsEnabled, stuff((select ';' + case when b.join_obj_id = 'ALL' then'ALL' else d.text_zh_cn end from cus_join_list b left join sys_dictionary d on b.join_obj_id = d.value and d.dictionary_name = 'case_direction' where b.form_obj_id = T.address_id and b.join_type = 'case_direction' for xml path('')),1,1,'') as CaseDirectionText,  stuff((select ';' + case when b.join_obj_id = 'ALL' then'ALL' else d.text_zh_cn end from cus_join_list b left join sys_dictionary d on b.join_obj_id = d.value and d.dictionary_name = 'case_type'  where b.form_obj_id = T.address_id and b.join_type = 'case_type' for xml path('')),1,1,'') as CaseTypeText,  stuff((select ';' + case when b.join_obj_id = 'ALL' then'ALL' else d.text_zh_cn end from cus_join_list b left join sys_dictionary d on b.join_obj_id = d.value and d.dictionary_name = 'address_use_type'  where b.form_obj_id = T.address_id and b.join_type = 'address_type' for xml path('')),1,1,'') as AddressTypeText  from (select * from T where join_obj_id is not null or address_id not in (select address_id from T where join_obj_id is not null ) ) as T ";
        return DbQuery.Ado.Query<CustomerAddressReDto>(sql, new { customerId, addressOrApplicant });
    }

    List<CustomerContactReDto> GetCustomerContactList(string customerId, string? contactName)
    {
        var strWhere = "";
        if (!string.IsNullOrEmpty(contactName))
        {
            strWhere = " and c.contact_name like N'%" + contactName + "%' ";
        }
        var sql = $@" 
                            select distinct c.contact_id as 'value',c.contact_name as 'text',c.call_name CallName,
                                            c.contact_id ContactId,c.contact_name ContactName,c.position ,c.tel,c.mobile,c.fax ,c.email,c.qq,
                                            c.postcode,c.address_cn AddressCn,c.address_en,c.seq,c.remark,c.update_user_id,
                                            c.update_time,c.create_time,c.is_enabled IsEnabled,
                                            stuff((select ';' + a.contact_type_zh_cn from cus_join_list b inner join bas_contact_type a on b.join_obj_id = a.contact_type_id where b.form_obj_id = c.contact_id and b.join_type ='contact_type' for xml path('')),1,1,'') as  ContactType
                            from cus_customer cc  left join
                            cus_join_list jc on jc.join_obj_id = cc.customer_id or jc.join_obj_id = cc.parent_id and cc.customer_id = @customerId
                            left join cus_contact as c on c.contact_id = jc.form_obj_id 
                            where c.contact_id is not null and cc.customer_id = @customerId {strWhere} and c.is_enabled = 1";
        return DbQuery.Ado.Query<CustomerContactReDto>(sql, new { customerId, contactName });
    }

    Task<IEnumerable<CustomerFollowReDto>> GetCustomerCustomerFollowListAsync(string customerId,
        string customerUserType)
        => QueryCustomerFollowsAsync(new CustomerFollowQueryDto(new[] { customerId }, ArraySegment<string>.Empty,
            new[] { customerUserType }));

    async Task<IEnumerable<CustomerFollowReDto>> QueryCustomerFollowsAsync(CustomerFollowQueryDto dto)
    {
        var queryBuilder = new CustomerFollowQueryBuilder(DbQuery);
        var query = queryBuilder.AddCustomerIds(dto.CustomerIds).AddCustomerUserTypes(dto.CustomerUserTypes)
            .Build();

        return await query.From<SysUserInfo>()
            .InnerJoin((follows, userInfo) => follows.TrackUser == userInfo.UserId)
            .ToListAsync((a, b) => new CustomerFollowReDto
            {
                UserId = a.TrackUser,
                UserName = b.CnName,
                UserNum = b.UserName,
                CaseDirection = a.CaseDirection,
                CaseType = a.CaseType,
                CustomerUserType = a.CustomerUserType
            });
    }

    internal readonly struct CustomerFollowQueryBuilder(IFreeSql freeSql)
    {

        private const string CustomerFollowAlias = nameof(CusFollowList);

        private readonly ISelect<CusFollowList> _customerFollowQuery = freeSql.Select<CusFollowList>().As(CustomerFollowAlias).WithLock();

        internal CustomerFollowQueryBuilder AddCustomerIds(IEnumerable<string> customerIds)
        {
            _customerFollowQuery.WhereIfDynamicFilter(customerIds.Any(),
                customerIds.BuildContainsDynamicFilter(nameof(CusFollowList.CustomerId), CustomerFollowAlias,
                    otherFilters: true.BuildEqualsDynamicFilter(nameof(CusFollowList.IsEnabled), CustomerFollowAlias)));

            return this;
        }

        internal CustomerFollowQueryBuilder AddCustomerUserTypes(IEnumerable<string> customerUserTypes)
        {
            _customerFollowQuery.WhereIfDynamicFilter(customerUserTypes.Any(),
                customerUserTypes.BuildContainsDynamicFilter(nameof(CusFollowList.CustomerUserType),
                    CustomerFollowAlias));

            return this;
        }

        internal ISelect<CusFollowList> Build() => _customerFollowQuery;
    }

    /// <summary>
    /// 获取商务所有负责的客户
    /// </summary>
    /// <param name="userCode"></param>
    /// <returns></returns>
    async Task<List<CustomerReDto>> GetCusCustomerListByBussAsync(string userCode)
    {
        var queryData = await DbQuery.Select<CusCustomer>().WithLock()
            .From<SysUserInfo>((customerList, sysUserInfo)
                => customerList.InnerJoin(customer => customer.BusiUserId == sysUserInfo.UserId && sysUserInfo.UserName == userCode && (customer.CrmCustomerCode != null || customer.CrmCustomerCode != "")))
            .ToListAsync(p => new CustomerReDto
            {
                CustomerId = p.t1.CustomerId,
                CustomerName = p.t1.CustomerName,
                CustomerNameEn = p.t1.CustomerNameEn,
                CrmCustomerCode = p.t1.CrmCustomerCode,
                District = p.t1.District,
                IsEnabled = p.t1.IsEnabled,
                BusiUserId = p.t1.BusiUserId,
                BusiUserNum = p.t2.UserName,
                BusiUserName = p.t2.CnName,
                IsCooperation = p.t1.IsCooperation
            });

        return queryData;
    }

    /// <summary>
    /// 获取所有境外客户
    /// </summary>
    /// <returns></returns>
    async Task<List<CustomerReDto>> GetCusCustomerOutListAsync()
    {
        //var queryData = await DbQuery.Select<CusCustomer>().WithLock()
        //    .From<SysUserInfo>((customerList, sysUserInfo)
        //        => customerList.InnerJoin(customer => customer.BusiUserId == sysUserInfo.UserId && (customer.CrmCustomerCode!=null || customer.CrmCustomerCode != "") && customer.CountryId !="CN"))
        //    .ToListAsync(p => new CustomerReDto
        //    {
        //        CustomerId = p.t1.CustomerId,
        //        CustomerName = p.t1.CustomerName,
        //        CustomerNameEn = p.t1.CustomerNameEn,
        //        CrmCustomerCode = p.t1.CrmCustomerCode,
        //        District = p.t1.District,
        //        IsEnabled = p.t1.IsEnabled,
        //        BusiUserId = p.t1.BusiUserId,
        //        BusiUserNum = p.t2.UserName,
        //        BusiUserName = p.t2.CnName,
        //        IsCooperation = p.t1.IsCooperation
        //    });

        //return queryData;
        var queryData = await DbQuery.Select<CusCustomer>().WithLock()
           .Where(customer => (customer.CrmCustomerCode != null || customer.CrmCustomerCode != "") && customer.CountryId != "CN")
           .ToListAsync(p => new CustomerReDto
           {
               CustomerId = p.CustomerId,
               CustomerName = p.CustomerName,
               CustomerNameEn = p.CustomerNameEn,
               CrmCustomerCode = p.CrmCustomerCode,
               District = p.District,
               IsEnabled = p.IsEnabled,
               BusiUserId = p.BusiUserId,

               IsCooperation = p.IsCooperation
           });

        return queryData;
    }

    /// <summary>
    /// 根据客户名称查询客户，模糊匹配，不分页
    /// </summary>
    /// <param name="customerName"></param>
    /// <returns></returns>
    async Task<List<CustomerReDto>> GetCusCustomerNotPageListAsync(string? customerName)
    {
        if (string.IsNullOrEmpty(customerName))
        {
            return await DbQuery.Select<CusCustomer>().WithLock()
                .Limit(30)
            .ToListAsync(c => new CustomerReDto
            {
                CustomerId = c.CustomerId,
                CustomerName = c.CustomerName,
                CustomerNameEn = c.CustomerNameEn,
                CrmCustomerCode = c.CrmCustomerCode,
                District = c.District,
                IsEnabled = c.IsEnabled,
                BusiUserId = c.BusiUserId,

                IsCooperation = c.IsCooperation
            });
        }
        else
        {
            return await DbQuery.Select<CusCustomer>().WithLock()

            .Where(c => c.CustomerName.Contains(customerName))
            .ToListAsync(c => new CustomerReDto
            {
                CustomerId = c.CustomerId,
                CustomerName = c.CustomerName,
                CustomerNameEn = c.CustomerNameEn,
                CrmCustomerCode = c.CrmCustomerCode,
                District = c.District,
                IsEnabled = c.IsEnabled,
                BusiUserId = c.BusiUserId,

                IsCooperation = c.IsCooperation
            });
        }

    }
}