﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 关联邮件dto
/// </summary>
/// <param name="MailId">邮件id</param>
/// <param name="ReceiveType">邮件类型</param>
/// <param name="MailNo">邮件编号</param>
/// <param name="MailFrom">发件人</param>
/// <param name="MailSubject">邮件主题</param>
/// <param name="CreateTime">创建时间</param>
/// <param name="MailDate">邮件发送时间</param>
public record GetRelateMailListDto(
    string MailId,
    string ReceiveType,
    string MailNo,
    string MailFrom,
    string MailSubject,
    DateTime? CreateTime,
    DateTime? MailDate
);