﻿# iPlatformExtension.MailCenter

iPlatformExtension.MailCenter 为邮件中心业务代码，提供完整的邮件收发、处理和管理功能。

## 项目概述

邮件中心系统是 iPlatformExtension 平台的扩展模块，采用 CQRS (Command Query Responsibility Segregation) 架构模式和 MediatR 中介者模式实现，具有良好的代码组织结构，便于维护和扩展。

## 主要功能模块

### 1. 邮件接收管理 (ReceiveController)
- 获取邮箱列表
- 下载附件功能
- 邮件接收相关操作
- 邮件关联信息查询

### 2. 邮件处理流程 (ProcessController)
- 收件办理列表查询
- 最近7天办理记录查询
- 邮件流程处理

### 3. 系统配置管理 (SysConfigController)
- 保存收件解析规则
- 获取任务
- 邮箱管理（保存、查询、删除）
- 系统参数配置

### 4. 内容管理 (ContentManageController)
- 邮件内容管理
- 模板管理

### 5. 解析规则管理 (AnalysisRuleController)
- 邮件解析规则配置
- 规则应用与管理

## 技术架构

- **Commands**：处理写操作，如保存邮箱、保存解析规则等
- **Queries**：处理读操作，如获取邮箱列表、获取收件列表等
- **Handlers**：处理具体的业务逻辑
- **MediatR**：实现命令和查询的中介者模式

## 数据模型

项目包含多个数据模型：
- MailHost：邮箱配置
- MailReceive：邮件接收
- MailSend：邮件发送
- MailAttachments：邮件附件
- MailConfig：邮件配置
- MailTemplate：邮件模板
- 等其他相关模型