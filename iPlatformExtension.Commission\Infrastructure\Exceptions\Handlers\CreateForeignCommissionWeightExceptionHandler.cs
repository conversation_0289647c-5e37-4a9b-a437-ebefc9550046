﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class CreateForeignCommissionWeightExceptionHandler(ILogger<CreateForeignCommissionWeightCommand> logger) 
    : IRequestExceptionHandler<CreateForeignCommissionWeightCommand, Unit, Exception>
{
    public Task Handle(CreateForeignCommissionWeightCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        logger.LogCreatingForeignTrademarkWeightError(exception);
        state.SetHandled(Unit.Value);
        
        return Task.CompletedTask;
    }
}