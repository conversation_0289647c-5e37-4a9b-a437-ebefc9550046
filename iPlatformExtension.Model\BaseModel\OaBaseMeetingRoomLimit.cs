using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "oa_base_meeting_room_limit", DisableSyncStructure = true)]
	public partial class OaBaseMeetingRoomLimit {

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "limit_id", StringLength = 50, IsNullable = false)]
		public string LimitId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "limit_type", StringLength = 50)]
		public string LimitType { get; set; }

		[ Column(Name = "meeting_type", StringLength = 50)]
		public string MeetingType { get; set; }

		[ Column(Name = "room_id", StringLength = 50)]
		public string RoomId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
