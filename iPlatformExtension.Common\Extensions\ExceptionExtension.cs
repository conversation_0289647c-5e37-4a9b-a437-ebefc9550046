using System.Text;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;

namespace iPlatformExtension.Common.Extensions;

public static class ExceptionExtension
{
    public static string BuildExceptionMessage(this StringBuilder errorBuilder, PhoenixRequestException exception)
    {
        var response = exception.Response;
        errorBuilder.Append(exception.Message).Append("[业务状态码]：").Append(response.SubCode)
            .Append("[业务信息]：").Append(response.SubMessage);
        return errorBuilder.ToString();
    }

    public static string BuildExceptionMessage(this StringBuilder stringBuilder, Exception exception)
    {
        for (var e = exception; e is not null; e = e.InnerException)
        {
            stringBuilder.Append(e.Message);
        }

        return stringBuilder.ToString();
    }
    
    public static Exception WrapException(this Microsoft.Data.SqlClient.SqlException exception)
    {
        var number = exception.Number;
        return number switch
        {
            2627 => new AlreadyExistsExceptions(exception.Message),
            2601 => new AlreadyExistsExceptions(exception.Message),
            _ => exception
        };
    }
}