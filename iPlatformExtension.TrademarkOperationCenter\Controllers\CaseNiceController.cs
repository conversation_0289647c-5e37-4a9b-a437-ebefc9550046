﻿using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Nice;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Nice;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers;

/// <summary>
/// 案件的尼斯分类信息
/// </summary>
[ApiController]
[Route("[controller]")]
[Authorize]
[Produces(MediaTypeNames.Application.Json)]
[Consumes(MediaTypeNames.Application.Json)]
public sealed class CaseNiceController : ControllerBase
{
    private readonly IMediator _mediator;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="mediator">中介者组件</param>
    public CaseNiceController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// 分页获取案件尼斯分类数据
    /// </summary>
    /// <param name="caseId">案件id</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页面大小</param>
    /// <returns>案件尼斯分类数据</returns>
    [HttpGet]
    public Task<IEnumerable<StandardCategoryDto>> GetCaseNiceCategoriesAsync([Required]string caseId, int? pageIndex, int? pageSize)
    {
        return _mediator.Send(new CaseNiceQuery(caseId, pageIndex, pageSize));
    }

    /// <summary>
    /// 编辑案件尼斯分类信息
    /// </summary>
    /// <param name="dto">案件的尼斯分类数据</param>
    /// <returns></returns>
    [HttpPost]
    [HttpPut]
    [Authorize(Roles = "商标流程人员")]
    public Task SaveCaseNiceCategoriesAsync(CaseNiceCategoriesDto dto)
    {
        return _mediator.Send(new SaveCaseNiceCommand(dto.VersionId, dto.CaseId, dto.StandardCategories,
            dto.CustomCategories, dto.GrandNumbers));
    }
}