﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto
{
    /// <summary>
    /// 地区返回model 
    /// </summary>
    public class DistrictReDto
    {
        /// <summary>
        /// 地区编码
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 中文名称
        /// </summary>
        public string TextZhCn { get; set; }

        /// <summary>
        /// 英文名称
        /// </summary>
        public string TextEnUs { get; set; }

        /// <summary>
        /// 日文名称
        /// </summary>
        public string TextJaJp { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int? Seq { get; set; }
    }
}
