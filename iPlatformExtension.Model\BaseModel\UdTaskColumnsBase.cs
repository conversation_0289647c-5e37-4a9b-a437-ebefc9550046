using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ud_task_columns_base", DisableSyncStructure = true)]
	public partial class UdTaskColumnsBase {

		[ Column(Name = "column_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ColumnId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_name_en_us", StringLength = 50)]
		public string ColumnNameEnUs { get; set; }

		[ Column(Name = "column_name_ja_jp", StringLength = 50)]
		public string ColumnNameJaJp { get; set; }

		[ Column(Name = "column_name_zh_cn", StringLength = 50)]
		public string ColumnNameZhCn { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "datebase_id", StringLength = 50)]
		public string DatebaseId { get; set; }

		[ Column(Name = "form_data_code", StringLength = 50)]
		public string FormDataCode { get; set; }

		[ Column(Name = "form_type", StringLength = 50)]
		public string FormType { get; set; }

		[ Column(Name = "is_key_id")]
		public bool? IsKeyId { get; set; }

		[ Column(Name = "is_not_null")]
		public bool? IsNotNull { get; set; }

		[ Column(Name = "length")]
		public int? Length { get; set; }

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

		[ Column(Name = "table_code", StringLength = 50)]
		public string TableCode { get; set; }

		[ Column(Name = "table_name", StringLength = 50)]
		public string TableName { get; set; }

	}

}
