using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_file_history", DisableSyncStructure = true)]
	public partial class CaseFileHistory {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "create_time", StringLength = 50)]
		public string CreateTime { get; set; }

		[ Column(Name = "ctrl_proc", StringLength = 500)]
		public string CtrlProc { get; set; }

		[ Column(Name = "ctrl_proc_property", StringLength = 50)]
		public string CtrlProcProperty { get; set; }

		[ Column(Name = "ctrl_proc_real", StringLength = 50)]
		public string CtrlProcReal { get; set; }

		[ Column(Name = "desc_id", StringLength = 50)]
		public string DescId { get; set; }

		[ Column(Name = "file_ex", StringLength = 50)]
		public string FileEx { get; set; }

		[ Column(Name = "file_name", StringLength = 500)]
		public string FileName { get; set; }

		[ Column(Name = "file_no", StringLength = 50)]
		public string FileNo { get; set; }

		[ Column(Name = "file_size")]
		public long? FileSize { get; set; }

		[ Column(Name = "full_name", StringLength = 500)]
		public string FullName { get; set; }

		[ Column(Name = "full_name1", StringLength = 500)]
		public string FullName1 { get; set; }

		[ Column(Name = "notice_id", StringLength = 50)]
		public string NoticeId { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "receive_date")]
		public DateTime? ReceiveDate { get; set; }

		[ Column(Name = "receive_date_n", StringLength = 50)]
		public string ReceiveDateN { get; set; }

		[ Column(Name = "receive_no", StringLength = 50)]
		public string ReceiveNo { get; set; }

		[ Column(Name = "tongzhismc", StringLength = 100)]
		public string Tongzhismc { get; set; }

		[ Column(Name = "upload_times")]
		public int? UploadTimes { get; set; }

		[ Column(Name = "volume", StringLength = 500)]
		public string Volume { get; set; }

		[ Column(Name = "volume_real", StringLength = 50)]
		public string VolumeReal { get; set; }

	}

}
