﻿using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Commission.Application.Models.Trademark;

/// <summary>
/// 建议类任务提成规则
/// </summary>
public class SuggestionProcWeightRuleDto
{
    /// <summary>
    /// 规则id
    /// </summary>
    public int RuleId { get; set; }

    /// <summary>
    /// 任务名称
    /// </summary>
    public INameInfo CtrlProc { get; set; } = null!;

    /// <summary>
    /// 任务标识
    /// </summary>
    public INameInfo CtrlProcMark { get; set; } = null!;

    /// <summary>
    /// 是否可用
    /// </summary>
    public string IsEnable { get; set; } = string.Empty;
}