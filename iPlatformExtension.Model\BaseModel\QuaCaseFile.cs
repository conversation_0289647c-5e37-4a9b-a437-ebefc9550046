using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "qua_case_file", DisableSyncStructure = true)]
	public partial class QuaCaseFile {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "detail_id", StringLength = 50)]
		public string DetailId { get; set; }

		[ Column(Name = "file_id", StringLength = 50)]
		public string FileId { get; set; }

	}

}
