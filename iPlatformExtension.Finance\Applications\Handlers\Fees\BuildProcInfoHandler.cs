﻿using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class BuildProcInfoHandler(IUserInfoRepository userInfoRepository)
    : IRequestHandler<BuildProcInfoCommand>, IFeesQueryStatementBuilder
{
    private const string ProcAlias = nameof(CaseProcInfo);

    public async Task Handle(BuildProcInfoCommand request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
        {
            return;
        }

        object?[] userIds = await request.Dto.UndertakerIds.ToAsyncEnumerable()
            .SelectAwait(async username => await userInfoRepository.GetUserIdByUserNameAsync(username))
            .ToArrayAsync(cancellationToken);
        
        BuildFeesQueryStatement(request.Dto, request.FeesQuery, userIds);
    }

    public static void BuildFeesQueryStatement(FeeQueryDto dto, ISelect<CaseFeeList> feesQuery, params object?[] args)
    {
        feesQuery = feesQuery.InnerJoin(caseFeeList =>
            caseFeeList.CaseProcInfo.ProcId == caseFeeList.ProcId);

        feesQuery.WhereDynamicFilter(
                dto.SendOfficialDatePeriod.BuildDynamicTimePeriodFilter(nameof(CaseProcInfo.SendOfficialDate),
                    ProcAlias))
            .WhereIfDynamicFilter(
                dto.BusinessTypeIds.Any() && dto.CaseTypes.Any(caseType => !CaseType.IsBelongPatent(caseType)),
                dto.BusinessTypeIds.BuildContainsDynamicFilter(nameof(CaseProcInfo.BusTypeId), ProcAlias))
            .WhereDynamicFilter(
                dto.CtrlProcIds.BuildContainsDynamicFilter(nameof(CaseProcInfo.CtrlProcId), ProcAlias))
            .WhereIfDynamicFilter(args.Any(),
                args.BuildContainsDynamicFilter(
                    nameof(CaseProcInfo.UndertakeUserId), ProcAlias))
            .WhereDynamicFilter(
                dto.CompletionTimePeriod.BuildDynamicTimePeriodFilter(nameof(CaseProcInfo.FinishDate),
                    ProcAlias))
            .WhereIfDynamicFilter(dto.AllocateDateRange.NeedToQuery,
                dto.AllocateDateRange.BuildDynamicTimePeriodFilter(nameof(CaseProcInfo.AllocateDate), ProcAlias))
            .WhereDynamicFilter(dto.ProcNos.BuildContainsDynamicFilter(nameof(CaseProcInfo.ProcNo), ProcAlias))
            .WhereIfDynamicFilter(dto.TrademarkDeliveryAgencyIds.Any(),
                dto.TrademarkDeliveryAgencyIds.BuildContainsDynamicFilter(
                    nameof(CaseProcInfo.TrademarkDeliveryAgencyId), ProcAlias));
    }
}