﻿namespace FileMigration;

public sealed class WorkOptions
{
    public int BufferSize { get; set; } = 50;

    public TimeOnly WorkStartTime { get; set; } = new TimeOnly(22, 0);

    public TimeSpan WorkTimeSpan { get; set; } = TimeSpan.FromHours(8);

    public TimeSpan DelayTimeSpan { get; set; } = TimeSpan.FromSeconds(10);

    public string BucketName { get; set; } = default!;

    public int DefaultLeastId { get; set; } = 0;
}