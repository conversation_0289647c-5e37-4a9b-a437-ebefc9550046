using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(DisableSyncStructure = true)]
	public partial class 企业信息补全 {

		[ Column(Name = "cid", StringLength = 50)]
		public string Cid { get; set; }

		[ Column(Name = "cname", StringLength = 500)]
		public string Cname { get; set; }

		[ Column(Name = "code", StringLength = 50)]
		public string Code { get; set; }

	}

}
