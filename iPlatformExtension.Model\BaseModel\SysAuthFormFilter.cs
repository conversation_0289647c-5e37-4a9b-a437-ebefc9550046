using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_auth_form_filter", DisableSyncStructure = true)]
	public partial class SysAuthFormFilter {

		[ Column(Name = "form_filter_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FormFilterId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "data_code", StringLength = 50)]
		public string DataCode { get; set; }

		[ Column(Name = "default_desc", StringLength = 500)]
		public string DefaultDesc { get; set; }

		[ Column(Name = "default_id", StringLength = 50)]
		public string DefaultId { get; set; }

		[ Column(Name = "filter_id", StringLength = 50)]
		public string FilterId { get; set; }

		[ Column(Name = "filter_name_en_us", StringLength = 100)]
		public string FilterNameEnUs { get; set; }

		[ Column(Name = "filter_name_ja_jp", StringLength = 100)]
		public string FilterNameJaJp { get; set; }

		[ Column(Name = "filter_name_zh_cn", StringLength = 100)]
		public string FilterNameZhCn { get; set; }

		[ Column(Name = "form_id", StringLength = 50)]
		public string FormId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
