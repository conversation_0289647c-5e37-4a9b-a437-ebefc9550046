using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_request_type", DisableSyncStructure = true)]
	public partial class BasRequestType {

		[ Column(Name = "request_type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string RequestTypeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "request_type_en_us", StringLength = 100)]
		public string RequestTypeEnUs { get; set; }

		[ Column(Name = "request_type_ja_jp", StringLength = 100)]
		public string RequestTypeJaJp { get; set; }

		[ Column(Name = "request_type_zh_cn", StringLength = 50)]
		public string RequestTypeZhCn { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
