﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Finance.Applications.Queries.Public;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Service.Interface;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Finance.Controllers
{
    /// <summary>
    /// 客户数据
    /// </summary>
    [Route("/api/[controller]/[action]")]
    [ApiController]
    public sealed class CustomerController : ControllerBase
    {
        private readonly ICustomerQueryService _customerService;

        private readonly IMediator _mediator;

        /// <summary>
        /// 注入服务
        /// </summary>
        /// <param name="customerService"></param>
        /// <param name="mediator"></param>
        public CustomerController(ICustomerQueryService customerService, IMediator mediator)
        {
            _customerService = customerService;
            _mediator = mediator;
        }

        /// <summary>
        /// 通过客户名称查询客户
        /// </summary>
        /// <param name="customerName">客户名称（模糊查询）</param>
        /// <param name="isEnabled">是否有效客户</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">页面大小</param>
        /// <returns></returns>
        [HttpGet]
        [ResponseCache(Duration = 600, VaryByQueryKeys = ["customerName", "isEnabled", "page", "pageSize"])]
        public Task<PageResult<CustomerReDto>> GetCusCustomerList(string? customerName, bool? isEnabled, int page = 1, int pageSize = 10)
        {
            return  _customerService.GetCusCustomerListAsync(customerName, isEnabled, page, pageSize);
        }

        /// <summary>
        /// 根据客户id查询客户详情
        /// </summary>
        /// <param name="customerId">客户id</param>
        /// <param name="isEnabled">是否有效</param>
        /// <returns></returns>
        [HttpGet("/customer")]
        [ResponseCache(Duration = 600, VaryByQueryKeys = ["customerId", "isEnabled"])]
        public Task<CustomerReDto> GetCustomerDetailAsync([Required] string customerId, bool? isEnabled = null) =>
            _customerService.GetCustomerDetailAsync(customerId, isEnabled);

        /// <summary>
        /// 通过账单id获取请款对象
        /// </summary>
        /// <param name="billId">账单id（必填）</param>
        /// <returns></returns>
        [HttpGet]
        [ResponseCache(Duration = 60, VaryByQueryKeys = ["billId"], Location = ResponseCacheLocation.Client)]
        public Task<IEnumerable<RequestObjectReDto>> GetRequestObjectList([Required]string billId)
        {
            return _mediator.Send(new RequestObjectQuery()
            {
                BillId = billId
            });
        }


        /// <summary>
        /// 请款/收款主体
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ResponseCache(Duration = 600)]
        public Task<List<CompanyReDto>> GetCompanyList()
        {
            return _customerService.GetCompanyListAsync();
        }


        /// <summary>
        /// 地区
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ResponseCache(Duration = 3600)]
        public List<DistrictReDto> GetDistrictList()
        {
            return _customerService.GetDistrictList();
        }


        /// <summary>
        /// 客户地址
        /// </summary>
        /// <param name="customerId">客户id（必填）</param>
        /// <param name="addressOrApplicant">地址或申请人名称（模糊查询）</param>
        /// <returns></returns>
        [HttpGet]
        [ResponseCache(Duration = 600, VaryByQueryKeys = ["customerId", "addressOrApplicant"])]
        public List<CustomerAddressReDto> GetCustomerAddressList(string customerId, string? addressOrApplicant)
        {
            return _customerService.GetCustomerAddressList(customerId, addressOrApplicant);
        }

        /// <summary>
        /// 客户联系人
        /// </summary>
        /// <param name="customerId">客户id（必填）</param>
        /// <param name="contactName">联系人姓名（模糊查询）</param>
        /// <returns></returns>
        [HttpGet]
        [ResponseCache(Duration = 600, VaryByQueryKeys = ["customerId", "contactName"])]
        public List<CustomerContactReDto> GetCustomerContactList(string customerId, string? contactName)
        {
            return _customerService.GetCustomerContactList(customerId, contactName);
        }


        /// <summary>
        /// 通过客户id获取请款对象
        /// </summary>
        /// <param name="customerIds">客户id数组</param>
        /// <returns></returns>
        [HttpPost]
        public Task<IEnumerable<RequestObjectReDto>> GetRequestObjectListByCusTomer(List<string> customerIds)
        {
            return _mediator.Send(new RequestObjectQuery()
            {
                CustomerIds = customerIds
            });
        }

        /// <summary>
        /// 客户案源人和跟案人列表
        /// </summary>
        /// <param name="customerId">客户id</param>
        /// <param name="customerUserType">案源人：ay  跟案人：ga</param>
        /// <returns></returns>
        [HttpGet]
        [ResponseCache(Duration = 60, Location = ResponseCacheLocation.Client, VaryByQueryKeys = ["customerId", "customerUserType"
        ])]
        public async Task<IEnumerable<CustomerFollowReDto>> GetCustomerCustomerFollowList(string customerId, string customerUserType)
        {
            return (await _customerService.GetCustomerCustomerFollowListAsync(customerId, customerUserType)).DistinctBy(dto => dto.UserId);
        }

        /// <summary>
        /// 获取商务所有负责的客户
        /// </summary>
        /// <param name="userCode">商务员工号</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<List<CustomerReDto>> GetCusCustomerListByBuss(string userCode)
        {
            return await _customerService.GetCusCustomerListByBussAsync(userCode);
        }

        /// <summary>
        /// 获取所有境外客户
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<List<CustomerReDto>> GetCusCustomerOutList()
        {
            return await _customerService.GetCusCustomerOutListAsync();
        }

        /// <summary>
        /// 根据客户名称查询客户，模糊匹配，不分页
        /// </summary>
        /// <param name="customerName"></param>
        /// <returns></returns>
        [HttpGet]
        [ResponseCache(Duration = 60, Location = ResponseCacheLocation.Client, VaryByQueryKeys = ["customerName"])]
        public async Task<List<CustomerReDto>> GetCusCustomerNotPageList(string? customerName)
        {
            return await _customerService.GetCusCustomerNotPageListAsync(customerName);
        }

    }
}
