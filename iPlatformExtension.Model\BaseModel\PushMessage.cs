using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "push_message", DisableSyncStructure = true)]
	public partial class PushMessage {

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user", StringLength = 50, IsNullable = false)]
		public string CreateUser { get; set; }

		[ Column(Name = "msg_content", StringLength = -2, IsNullable = false)]
		public string MsgContent { get; set; }

		[ Column(Name = "msg_id", StringLength = 50, IsNullable = false)]
		public string MsgId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "msg_title", StringLength = 500, IsNullable = false)]
		public string MsgTitle { get; set; }

		[ Column(Name = "receive_user", StringLength = 50, IsNullable = false)]
		public string ReceiveUser { get; set; }

		[ Column(Name = "send_time")]
		public DateTime? SendTime { get; set; }

	}

}
