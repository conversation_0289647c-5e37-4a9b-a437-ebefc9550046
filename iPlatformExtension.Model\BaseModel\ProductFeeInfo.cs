
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{

    [ Table(Name = "product_fee_info", DisableSyncStructure = true)]
    public partial class ProductFeeInfo
    {

        /// <summary>
        /// 产品费项Id
        /// </summary>
        [ Column(StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string Id { get; set; }

        /// <summary>
        /// 币别
        /// </summary>
        [ Column(StringLength = 50)]
        public string Currency { get; set; }

        /// <summary>
        /// 费用金额
        /// </summary>
        [ Column(DbType = "decimal(18,0)")]
        public decimal FeeAmount { get; set; }

        /// <summary>
        /// 费项类型
        /// </summary>
        [ Column(StringLength = 50, IsNullable = false)]
        public string FeeLineId { get; set; } = null!;

        /// <summary>
        /// 费项名称
        /// </summary>
        [ Column(IsNullable = false)]
        public string FeeName { get; set; } = null!;

        /// <summary>
        /// 费用类型
        /// </summary>
        [ Column(StringLength = 50, IsNullable = false)]
        public string FeeType { get; set; } = null!;

        /// <summary>
        /// 请款状态
        /// </summary>
        [ Column(StringLength = 50)]
        public string? PaymentRequestStatus { get; set; }

        /// <summary>
        /// 产品实例Id
        /// </summary>
        [ Column(StringLength = 50, IsNullable = false)]
        public string ProductId { get; set; } = null!;

        /// <summary>
        /// 备注
        /// </summary>
        
        public string? Remark { get; set; }

        /// <summary>
        /// 业务系统的案件费项id
        /// </summary>
        public string? CaseFeeId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool IsEnable { get; set; } = true;

        /// <summary>
        /// 标准金额
        /// </summary>
        [ Column(DbType = "decimal(18,0)")]
        public decimal StandardAmount { get; set; }


        /// <summary>
        /// 费减比例%
        /// </summary>
        public string FeeReduce { get; set; }
}
	

}
