﻿using iPlatformExtension.Common.Clients.HttpMessageHandlers;
using iPlatformExtension.Common.Clients.IPlatformWeb.Options;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Clients.IPlatformWeb.HttpMessageHandlers;

public sealed class PlatformFileAuthorizationHandler : BasicAuthorizationHandlerBase
{
    public override string UserName { get; }
    
    public override string Password { get; }

    public PlatformFileAuthorizationHandler(IOptionsMonitor<PlatformFileOptions> options)
    {
        var clientOptions = options.CurrentValue;
        UserName = clientOptions.UserName;
        Password = clientOptions.Password;
    }
}