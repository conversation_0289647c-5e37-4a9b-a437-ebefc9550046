﻿namespace iPlatformExtension.Finance.Applications.Models.ForeignBill;

internal sealed class ForeignBillPaymentBatchDto
{
    public required string BillId { get; set; }

    public string RemittanceCompany { get; set; } = string.Empty;

    public string PaidBank { get; set; } = string.Empty;

    public string RemittanceFeeSharingMethod { get; set; } = string.Empty;

    public string Remark { get; set; } = string.Empty;

    public string Country { get; set; } = string.Empty;
}