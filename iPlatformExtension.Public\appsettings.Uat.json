﻿{
  "nacos": {
    "ServerAddresses": [ "************:18848" ],
    "DefaultTimeOut": 15000,
    "Namespace": "uat",
    "ListenInterval": 1000,
    "ServiceName": "iPlatformExtension.Public",
    "UserName": "nacos",
    "Password": "Acip1234",
    "Listeners": [
      {
        "Optional": true,
        "DataId": "appsettings.json",
        "Group": "iPlatformExtension"
      },
      {
        "Optional": true,
        "DataId": "clients.json",
        "Group": "iPlatformExtension"
      },
      {
        "Optional": true,
        "DataId": "mqs.json",
        "Group": "iPlatformExtension.TrademarkOperationCenter"
      }
    ]
  }
}
