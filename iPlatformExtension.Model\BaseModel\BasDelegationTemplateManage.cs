using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_delegation_template_manage", DisableSyncStructure = true)]
	public partial class BasDelegationTemplateManage {

		[ Column(Name = "template_id", StringLength = 100, IsPrimary = true, IsNullable = false)]
		public string TemplateId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "data_id", StringLength = 50)]
		public string DataId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "is_head")]
		public bool? IsHead { get; set; } = false;

		[ Column(Name = "mail_cc", StringLength = 500)]
		public string MailCc { get; set; }

		[ Column(Name = "mail_subject", StringLength = 500)]
		public string MailSubject { get; set; }

		[ Column(Name = "mail_to", StringLength = 500)]
		public string MailTo { get; set; }

		[ Column(Name = "remark", StringLength = 150)]
		public string Remark { get; set; }

		[ Column(Name = "template_name", StringLength = 50)]
		public string TemplateName { get; set; }

		[ Column(Name = "template_num", StringLength = 10)]
		public string TemplateNum { get; set; }

		[ Column(Name = "template_sub_type", StringLength = 50)]
		public string TemplateSubType { get; set; }

		[ Column(Name = "template_type", StringLength = 50)]
		public string TemplateType { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
