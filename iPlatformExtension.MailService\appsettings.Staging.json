{"nacos": {"ServerAddresses": ["************:18848"], "DefaultTimeOut": 15000, "Namespace": "test", "ListenInterval": 1000, "ServiceName": "iPlatformExtension.MailService", "UserName": "nacos", "Password": "Acip1234", "Ip": "************", "Port": 5079, "Listeners": [{"Optional": true, "DataId": "appsettings.json", "Group": "iPlatformExtension"}, {"Optional": true, "DataId": "clients.json", "Group": "iPlatformExtension.MailCenter"}, {"Optional": true, "DataId": "mqs.json", "Group": "iPlatformExtension.MailCenter"}], "Metadata": {"ASPNETCORE_HTTPS_PORTS": "5079"}}}