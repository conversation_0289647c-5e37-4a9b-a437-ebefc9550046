﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_receive", DisableSyncStructure = true)]
	public partial class MailReceive {

		[Column(Name = "mail_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string MailId { get; set; }

		/// <summary>
		/// 附件数量
		/// </summary>
		[Column(Name = "attachments", DbType = "int")]
		public int? Attachments { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 邮箱配置用户id
		/// </summary>
		[Column(Name = "host_id", StringLength = 50)]
		public string HostId { get; set; }

		/// <summary>
		/// 抄送(仅取第一个人)
		/// </summary>
		[Column(Name = "mail_cc", StringLength = 50, IsNullable = false)]
		public string MailCc { get; set; }

		/// <summary>
		/// 收件日期
		/// </summary>
		[Column(Name = "mail_date", DbType = "datetime")]
		public DateTime? MailDate { get; set; }

		/// <summary>
		/// 原邮件eml
		/// </summary>
		[Column(Name = "mail_eml_url", StringLength = 200)]
		public string MailEmlUrl { get; set; }

		/// <summary>
		/// 发件人
		/// </summary>
		[Column(Name = "mail_from", StringLength = 50)]
		public string MailFrom { get; set; }

		/// <summary>
		/// 邮件表头信息
		/// </summary>
		[Column(Name = "mail_header")]
		public string MailHeader { get; set; }

		/// <summary>
		/// 邮件正文
		/// </summary>
		[Column(Name = "mail_html_body", StringLength = -2)]
		public string MailHtmlBody { get; set; }

		/// <summary>
		/// 收件编号
		/// </summary>
		[Column(Name = "mail_no", StringLength = 50)]
		public string MailNo { get; set; }

		/// <summary>
		/// 邮件优先级,Highest:紧急,其他(High,Normal,Low,Lowest)
		/// </summary>
		[Column(Name = "mail_priority", StringLength = 20)]
		public string MailPriority { get; set; }

		/// <summary>
		/// 邮件正文大小
		/// </summary>
		[Column(Name = "mail_size", DbType = "int")]
		public int? MailSize { get; set; }

		/// <summary>
		/// 主题
		/// </summary>
		[Column(Name = "mail_subject", StringLength = 200)]
		public string MailSubject { get; set; }

		/// <summary>
		/// 签名
		/// </summary>
		[Column(Name = "mail_text_body", StringLength = -2)]
		public string MailTextBody { get; set; }

		/// <summary>
		/// 收件人(仅取第一个人)
		/// </summary>
		[Column(Name = "mail_to", StringLength = 50)]
		public string MailTo { get; set; }

		/// <summary>
		/// -1:取消,0:待解析,1:解析中,2:待分拣;3:办理中;4:已忽略;5:已办理;500:出错
		/// </summary>
		[Column(Name = "status", DbType = "int")]
		public int? Status { get; set; } = 0;

		/// <summary>
		/// 原邮件id
		/// </summary>
		[Column(Name = "uid", StringLength = 50, IsNullable = false)]
		public string Uid { get; set; }

		/// <summary>
		/// 版本号
		/// </summary>
		[Column(Name = "version", DbType = "int")]
		public int Version { get; set; } = 0;

	}

}
