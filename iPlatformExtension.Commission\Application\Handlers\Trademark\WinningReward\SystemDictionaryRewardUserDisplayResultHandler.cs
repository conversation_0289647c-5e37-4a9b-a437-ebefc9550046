﻿using iPlatformExtension.Commission.Application.Notifications.WinningReward;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class SystemDictionaryRewardUserDisplayResultHandler(ISystemDictionaryRepository dictionaryRepository) 
    : INotificationHandler<RewardUserDisplayNotification>
{
    public async Task Handle(RewardUserDisplayNotification notification, CancellationToken cancellationToken)
    {
        var rewards = notification.UserRewards;
        foreach (var reward in rewards)
        {
            reward.RulingResult = await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.RulingResult, reward.RulingResult);
            reward.RewardType = reward.RewardBeneficiaryType switch
            {
                WinningRewardBeneficiaryType.Mentor => "核稿奖励",
                WinningRewardBeneficiaryType.Undertaker => "撰写奖励",
                _ => string.Empty
            };
        }
    }
}