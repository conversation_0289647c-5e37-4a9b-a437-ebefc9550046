﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_reader_list", DisableSyncStructure = true)]
	public partial class MailReaderList {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime CreateTime { get; set; }

		/// <summary>
		/// 邮件ID
		/// </summary>
		[Column(Name = "mail_id", StringLength = 50, IsNullable = false)]
		public string MailId { get; set; }

		/// <summary>
		/// 类型:1:发件,2:收件
		/// </summary>
		[Column(Name = "mail_type", StringLength = 10, IsNullable = false)]
		public string MailType { get; set; } = "2";

		/// <summary>
		/// 阅读状态 0:未读,1:已读,2:作废
		/// </summary>
		[Column(Name = "status", DbType = "int")]
		public int? Status { get; set; }

		[Column(Name = "update_by", StringLength = 50)]
		public string UpdateBy { get; set; }

		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

		[Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
