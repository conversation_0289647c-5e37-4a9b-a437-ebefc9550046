﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public class GetFlowInfoHandler : IRequestHandler<FlowInfoQuery, GetFlowInfoDto?>
    {
        private readonly IFreeSql _freeSql;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public GetFlowInfoHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor)
        {
            _freeSql = freeSql;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<GetFlowInfoDto?> Handle(FlowInfoQuery request, CancellationToken cancellationToken)
        {
            var history = await _freeSql.Select<SysFlowHistory>().Where(o => o.ObjId == request.ObjId && o.FlowType == request.FlowType).OrderByDescending(o => o.AuditTime).FirstAsync();

            var objid = request.ObjId;
            //todo:兼容业务系统流程显示,需优化
            if (request.FlowType == "EX")
            {
                objid = await _freeSql.Select<CaseProcFlow>().Where(o => o.ProcId == request.ObjId && o.FlowType == ProcFlowEnum.EX).ToOneAsync(o => o.ProcFlowId);
            }

            var sql = _freeSql.Select<SysFlowActivity, SysFlowConfig, SysFlowNode, SysUserInfo, SysDeptInfo, BasCompany, BasDistrict, SysDeptInfo, SysUserInfo, CaseProcFlow>()
                .InnerJoin((fa, fc, fn, ui, fdept, bc, bd, dept, curUser,procFlow) => fa.FlowId == fc.FlowId)
                .InnerJoin((fa, fc, fn, ui, fdept, bc, bd, dept, curUser,procFlow) => fa.CurNodeId == fn.NodeId)
                .LeftJoin((fa, fc, fn, ui, fdept, bc, bd, dept, curUser,procFlow) => fa.CreateUserId == ui.UserId)
                .LeftJoin((fa, fc, fn, ui, fdept, bc, bd, dept, curUser,procFlow) => ui.DeptId == fdept.DeptId)
                .LeftJoin((fa, fc, fn, ui, fdept, bc, bd, dept, curUser,procFlow) => ui.ManageCompany == bc.CompanyId)
                .LeftJoin((fa, fc, fn, ui, fdept, bc, bd, dept, curUser,procFlow) => bc.DistrictId == bd.DistrictCode)
                .LeftJoin((fa, fc, fn, ui, fdept, bc, bd, dept, curUser,procFlow) => fc.DeptId == dept.DeptId)
                .LeftJoin((fa, fc, fn, ui, fdept, bc, bd, dept, curUser,procFlow) => curUser.UserId == fa.CurUserId)
                .LeftJoin((fa, fc, fn, ui, fdept, bc, bd, dept, curUser,procFlow) => procFlow.ProcFlowId == fa.ObjId && procFlow.FlowType == ProcFlowEnum.EX)
                .Where((fa, fc, fn, ui, fdept, bc, bd, dept, curUser,procFlow) => fa.FlowType == request.FlowType && fa.ObjId == objid).WithLock();

            var info = await sql.OrderByDescending((fa, fc, fn, ui, fdept, bc, bd, dept, curUser,procFlow) => fa.UpdateTime)
                 .FirstAsync((fa, fc, fn, ui, fdept, bc, bd, dept, curUser,procFlow) => new GetFlowInfoDto()
                 {
                     ObjId = fa.ObjId,
                     Status = fa.Status,
                     UpdateTime = fa.UpdateTime,
                     FlowId = fc.FlowId,
                     FlowType = fc.FlowType,
                     FlowSubType = fc.FlowSubType,
                     IsSkip = fc.IsSkip,
                     IsEnable = fc.IsEnabled,
                     CurNodeId = fa.CurNodeId,
                     FullName = fdept.FullName,
                     NameZhCn = fn.NameZhCn,
                     NodeCode = fn.NodeCode,
                     CreateUserId = fa.CreateUserId,
                     CnName = $"{ui.CnName}({bd.TextZhCn},{dept.DeptName})",
                     CurAuditUserID = curUser.UserId,
                     CurAuditName = curUser.CnName,
                     CurAuditUserName = curUser.UserName,
                     ProcFlowId = procFlow.ProcFlowId,
                     ProcID = procFlow.ProcId,
                 });
            if (history != null && info != null)
            {
                info.RejectNodeId = history.NodeId;
                info.RejectUserId = history.AuditUserId;
                info.RejectCnName = history.AuditCnName;
            }
            return info;
        }
    }
}
