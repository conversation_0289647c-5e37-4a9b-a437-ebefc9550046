using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "dem_list", DisableSyncStructure = true)]
	public partial class DemList {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "content", StringLength = -2, IsNullable = false)]
		public string Content { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "dept_id", StringLength = 50)]
		public string DeptId { get; set; }

		[ Column(Name = "error_columns", StringLength = 200)]
		public string ErrorColumns { get; set; }

		[ Column(Name = "error_message", StringLength = 200)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "expect_date")]
		public DateTime? ExpectDate { get; set; }

		[ Column(Name = "file_name", StringLength = 50)]
		public string FileName { get; set; }

		[ Column(Name = "is_submit")]
		public bool? IsSubmit { get; set; }

		[ Column(Name = "is_zentao", StringLength = 50)]
		public string IsZentao { get; set; }

		[ Column(Name = "kaifa_id", StringLength = 50, IsNullable = false)]
		public string KaifaId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "kaifa_remark", StringLength = -2)]
		public string KaifaRemark { get; set; }

		[ Column(Name = "level", StringLength = 50)]
		public string Level { get; set; }

		[ Column(Name = "list_id", StringLength = 50)]
		public string ListId { get; set; }

		[ Column(Name = "module", StringLength = 50)]
		public string Module { get; set; }

		[ Column(Name = "pinggu_date")]
		public DateTime? PingguDate { get; set; }

		[ Column(Name = "pinggu_user", StringLength = 50)]
		public string PingguUser { get; set; }

		[ Column(Name = "profit", StringLength = -2)]
		public string Profit { get; set; }

		[ Column(Name = "shiji_date")]
		public DateTime? ShijiDate { get; set; }

		[ Column(Name = "source", StringLength = -2)]
		public string Source { get; set; }

		[ Column(Name = "status", StringLength = 50)]
		public string Status { get; set; }

		[ Column(Name = "suggest", StringLength = -2)]
		public string Suggest { get; set; }

		[ Column(Name = "supplement", StringLength = -2)]
		public string Supplement { get; set; }

		[ Column(Name = "title", StringLength = 50)]
		public string Title { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "xuqiu_type", StringLength = 50)]
		public string XuqiuType { get; set; }

		[ Column(Name = "yanqi_date")]
		public DateTime? YanqiDate { get; set; }

		[ Column(Name = "yuji_date")]
		public DateTime? YujiDate { get; set; }

		[ Column(Name = "yuji_kaishi")]
		public DateTime? YujiKaishi { get; set; }

	}

}
