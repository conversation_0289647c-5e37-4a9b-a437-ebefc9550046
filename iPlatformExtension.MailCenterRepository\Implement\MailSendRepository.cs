﻿﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;

namespace iPlatformExtension.MailCenterRepository.Implement;

internal class MailSendRepository(
IFreeSql<MailCenterFreeSql> fsql,
UnitOfWorkManage<MailCenterFreeSql> manager)
: DefaultRepository<MailSend, string>(fsql, manager), IMailSendRepository;
