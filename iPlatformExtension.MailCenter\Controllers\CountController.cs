﻿using iPlatformExtension.MailCenter.Applications.Models.Count;
using iPlatformExtension.MailCenter.Applications.Queries.Count;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.MailCenter.Controllers
{
    /// <summary>
    /// 统计控制器
    /// </summary>
    /// <param name="mediator"></param>
    [Route("[controller]")]
    [ApiController]
    public class CountController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// 邮件数量统计
        /// </summary>
        /// <param name="query"></param>
        [HttpGet]
        public async Task<IEnumerable<MailDto>> MailCount([FromQuery] MailQuery query)
        {
            return await mediator.Send(query);
        }


    }
}
