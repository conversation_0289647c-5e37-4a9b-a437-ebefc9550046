﻿using System.Text.Json;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class SaveProcExtensionDynamicConfigCommandHandler(
    EntityTypeInfoProvider typeInfoProvider,
    IProcPropertiesDynamicConfigRepository dynamicConfigRepository)
    : IRequestHandler<SaveProcExtensionDynamicConfigCommand>
{
    public async Task Handle(SaveProcExtensionDynamicConfigCommand request, CancellationToken cancellationToken)
    {
        var (ctrlProcId, typeName, caseDirection) = request;
        
        var entityType = typeInfoProvider.Get(Type.GetType($"iPlatformExtension.Model.BaseModel.{typeName},{typeof(CaseProcInfo).Assembly}") ??
                                               throw new NotFoundException(typeName, "实体类型"));

        IEnumerable<EntityPropertyInfo> properties = entityType.EntityPropertyInfos;
        var dynamicConfigs = properties
            .Where(entityPropertyInfo => entityPropertyInfo.DisplayInfo is not null)
            .Select(entityPropertyInfo =>
            {
                var propertyType = entityPropertyInfo.PropertyType;
                var navigationType =
                    propertyType.IsGenericType &&
                    typeof(ICollection<>).IsAssignableFrom(propertyType.GetGenericTypeDefinition())
                        ? NavigationType.Many
                        : !propertyType.IsValueType && propertyType != typeof(string)
                            ? NavigationType.One
                            : NavigationType.None;

                var displayInfo = entityPropertyInfo.DisplayInfo;
                var keyValueDisplayInfo = entityPropertyInfo.KeyValueDisplayInfo;
    
                return new ProcPropertiesDynamicConfig
                {
                    BelongClassName = typeName,
                    CtrlProcId = ctrlProcId,
                    DisplayName = displayInfo?.Name ?? string.Empty,
                    FieldName = entityPropertyInfo.ColumnName,
                    Include = navigationType,
                    Key = JsonNamingPolicy.CamelCase.ConvertName(entityPropertyInfo.PropertyName),
                    PropertyName = entityPropertyInfo.PropertyName,
                    TableName = entityType.TableName ?? typeName,
                    Order = displayInfo?.Order ?? -1,
                    CaseDirection = caseDirection,
                    IsShow = !entityPropertyInfo.IsHidden,
                    IsKeyValue = keyValueDisplayInfo is not null,
                    IsDictionary = keyValueDisplayInfo?.IsSystemDictionary ?? false,
                    DisplayValueSourceType = keyValueDisplayInfo?.DisplayValueSourceType?.Name ?? string.Empty,
                    DictionaryName = keyValueDisplayInfo?.DictionaryName ?? string.Empty
                };
            }).ToArray();
        
        await dynamicConfigRepository.DeleteAsync(
            config => config.CtrlProcId == ctrlProcId && config.BelongClassName == typeName &&
                      config.CaseDirection == caseDirection, cancellationToken);
        
        await dynamicConfigRepository.InsertAsync(dynamicConfigs, cancellationToken);

    }
}