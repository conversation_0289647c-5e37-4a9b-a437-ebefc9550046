﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_correlative", DisableSyncStructure = true)]
	public partial class MailCorrelative {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 关联类型:发件:SendMail,收件:ReceiveMail,案件:Case,任务:Proc,客户:Customer
		/// </summary>
		[Column(Name = "correlate_type", StringLength = 50)]
		public string CorrelateType { get; set; }

		/// <summary>
		/// 关联人
		/// </summary>
		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		/// <summary>
		/// 关联时间
		/// </summary>
		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		[Column(Name = "mail_id", StringLength = 50)]
		public string MailId { get; set; }

		/// <summary>
		/// 关联类型对应的id
		/// </summary>
		[Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

	}

}
