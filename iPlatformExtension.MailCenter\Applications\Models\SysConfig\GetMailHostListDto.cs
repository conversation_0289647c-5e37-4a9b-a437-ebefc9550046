﻿using iPlatformExtension.Common.Attributes;

namespace iPlatformExtension.MailCenter.Applications.Models.SysConfig
{
    public class GetMailHostListDto
    {
        /// <summary>
        /// 邮箱ID
        /// </summary>
        public string HostId { get; set; }

        /// <summary>
        /// 邮箱帐号
        /// </summary>
        public string Account { get; set; }

        [CopyIgnoreField]
        public string CreateBy { get; set; }

        [CopyIgnoreField]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 收件服务器
        /// </summary>
        public string ImapHost { get; set; }

        /// <summary>
        /// 收件服务端口
        /// </summary>
        public int? ImapPort { get; set; }

        /// <summary>
        /// 是否转达
        /// </summary>
        public bool IsConvey { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 是否个人邮箱
        /// </summary>
        public bool IsPrivate { get; set; }


        /// <summary>
        /// 是否开启加密(默认关闭)
        /// </summary>
        public bool IsSsl { get; set; } = false;

        /// <summary>
        /// 是否系统邮箱
        /// </summary>
        public bool IsSystem { get; set; }

        //public string Password { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 展示名称
        /// </summary>
        public string ShowName { get; set; }

        /// <summary>
        /// 发件服务
        /// </summary>
        public string SmtpHost { get; set; }

        /// <summary>
        /// 发件服务端口
        /// </summary>
        public int? SmtpPort { get; set; }
        /// <summary>
        /// 更新人
        /// </summary>
        public string UpdateBy { get; set; }

        [CopyIgnoreField]
        public DateTime? UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 个人邮箱关联用户id
        /// </summary>
        public string PrivateUserId { get; set; }
    }
}
