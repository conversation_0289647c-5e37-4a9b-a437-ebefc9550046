﻿using System.Data.Common;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class NotAssignedProcQueryContextFactory(
    IOptionsMonitor<NotAssignedProcQueryOptions> optionsMonitor,
    IFreeSql<PlatformFreeSql> freeSql, IHttpContextAccessor httpContextAccessor) 
{
    public async Task<NotAssignedProcQueryContext?> CreateAsync(string authorizationType, CancellationToken cancellationToken)
    {
        var user = httpContextAccessor.HttpContext?.User;
        if (user is null)
        {
            throw new NotAuthenticatedException();
        }

        var options = optionsMonitor.Get(authorizationType);

        var userId = user.GetUserId();
        var roleCodes = user.GetRoleCodes();
        var authIds = await freeSql.Select<SysAuth>()
            .Where(auth => auth.FormId == authorizationType)
            .Where(auth => auth.AuthUser == userId || roleCodes.Contains(auth.AuthUser))
            .ToListAsync(auth => auth.AuthId, cancellationToken);

        switch (authIds.Count)
        {
            case 0:
                return null;
            case > 1:
                throw new ApplicationException("用户权限异常，请联系管理员");
        }

        var authId = authIds[0];
        var ctrlProcIds = await options.GetDefaultCtrlProcIdsAsync(freeSql, cancellationToken);
        var defaultQuery = options.BuildDefaultQuery(freeSql.Select<CaseProcInfo>().WithLock(SqlServerLock.ReadPast)
            .Where(info => string.IsNullOrEmpty(info.ForeginAgencyId))
            .Where(info => info.IsEnabled == true));
        var filters = await freeSql.Select<SysAuthFilter>()
            .Where(filter => filter.AuthId == authId)
            .ToDictionaryAsync(filter => filter.FilterId, cancellationToken);

        var dbParameters = new List<DbParameter>();
        var countQueryBuilder = new NotAssignedProcQueryContext.NotAssignedCountQueryBuilder(options.BuildDefaultQuery(
            freeSql.Select<CaseProcInfo>().WithLock(SqlServerLock.ReadPast)
                .WithParameters(dbParameters)
                .Where(info => string.IsNullOrEmpty(info.ForeginAgencyId))
                .Where(info => info.IsEnabled == true)));
        
        foreach (var specialCtrlProcId in options.SpecialCtrlProcIds.Where(ctrlProcIds.Contains))
        {
            countQueryBuilder.OtherCountQueries.Add(specialCtrlProcId, options.BuildDefaultQuery(freeSql
                .Select<CaseProcInfo>().WithLock(SqlServerLock.ReadPast)
                .WithParameters(dbParameters)
                .Where(info => string.IsNullOrEmpty(info.ForeginAgencyId))
                .Where(info => info.IsEnabled == true)));
        }
        
        return new NotAssignedProcQueryContext(options, defaultQuery, filters, ctrlProcIds, countQueryBuilder);
    }
}