﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_host", DisableSyncStructure = true)]
	public partial class MailHost {

		[Column(Name = "host_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string HostId { get; set; }

		[Column(Name = "account", StringLength = 100)]
		public string Account { get; set; }

		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime CreateTime { get; set; }

		[Column(Name = "imap_host", StringLength = 100)]
		public string ImapHost { get; set; }

		[Column(Name = "imap_port", DbType = "int")]
		public int? ImapPort { get; set; }

		/// <summary>
		/// 是否转达
		/// </summary>
		[Column(Name = "is_convey")]
		public bool IsConvey { get; set; }

		[Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; }

		/// <summary>
		/// 是否个人邮箱
		/// </summary>
		[Column(Name = "is_private")]
		public bool IsPrivate { get; set; }

		[Column(Name = "is_ssl")]
		public bool IsSsl { get; set; }

		/// <summary>
		/// 是否系统邮箱
		/// </summary>
		[Column(Name = "is_system")]
		public bool IsSystem { get; set; }

		[Column(Name = "password", StringLength = -1)]
		public string Password { get; set; }

		/// <summary>
		/// 私人邮箱关联用户id
		/// </summary>
		[Column(Name = "private_user_id", StringLength = 50)]
		public string PrivateUserId { get; set; }

		[Column(Name = "remark", StringLength = -1)]
		public string Remark { get; set; }

		[Column(Name = "show_name", StringLength = 100)]
		public string ShowName { get; set; }

		[Column(Name = "smtp_host", StringLength = 100)]
		public string SmtpHost { get; set; }

		[Column(Name = "smtp_port", DbType = "int")]
		public int? SmtpPort { get; set; }

		[Column(Name = "update_by", StringLength = 50)]
		public string UpdateBy { get; set; }

		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

	}

}
