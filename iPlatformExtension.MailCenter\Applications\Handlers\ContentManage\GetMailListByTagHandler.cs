﻿using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using iPlatformExtension.Common.Extensions;
using Microsoft.AspNetCore.Http;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Common.Helper;
using Microsoft.AspNetCore.Server.HttpSys;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Enum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class GetMailListByTagHandler(IFreeSql<MailCenterFreeSql> mySql, IMailTagRepository mailTagRepository,
        IMailTagListRepository mailTagListRepository,
        IMailReceiveRepository mailReceiveRepository,
        IHttpContextAccessor httpContextAccessor, IFreeSql<PlatformFreeSql> msSql) : IRequestHandler<GetMailListByTagQuery, PageResult<MailListByTagDto>>
    {
        public async Task<PageResult<MailListByTagDto>> Handle(GetMailListByTagQuery request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var mailType = request.MailType.ToString();
            var tagList = mySql.Select<MailTag, MailTagList>().WithLock()
                .InnerJoin((tag, taglst) => tag.Id == taglst.TagId)
                .Where(o => o.t1.MailType == mailType)
                .WhereIf(!string.IsNullOrEmpty(request.TagId), a => a.t2.TagId == request.TagId)
                .WithTempQuery(o => new { o.t2.MailId, o.t1.UserId, o.t2.TagId, o.t1.MailType }).ToList();

            long totalCount = 0;
            var lst = new List<MailListByTagDto>();
            if (request.MailType.ToString() == SysEnum.MailType.Receive.ToString())
            {
                lst = await mySql.Select<MailReceive, MailReceiveFlow>().InnerJoin(o => o.t1.MailId == o.t2.MailId).WithLock()
                   .WhereIf(!string.IsNullOrEmpty(request.Search), o =>
                       o.t1.MailSubject.Contains(request.Search) ||
                       o.t1.MailNo.Contains(request.Search) ||
                       o.t1.MailFrom.Contains(request.Search))
                   .Where(o => tagList.Any(t => t.MailId == o.t1.MailId && t.UserId == userId ))
                   .Count(out long total)
                   .Take(request.PageSize).Skip(request.PageSize * (request.PageIndex - 1))
                   .OrderByDescending(o => o.t1.MailNo)
                   .ToListAsync<MailListByTagDto>(cancellationToken);
                totalCount = total;
            }
            else
            {
                lst = await mySql.Select<MailSend, MailSendFlow>().InnerJoin(o => o.t1.MailId == o.t2.MailId).WithLock()
                   .WhereIf(!string.IsNullOrEmpty(request.Search), o =>
                       o.t1.MailSubject.Contains(request.Search) ||
                       o.t1.MailNo.Contains(request.Search) ||
                       o.t1.MailFrom.Contains(request.Search))
                   .Where(o => tagList.Any(t => t.MailId == o.t1.MailId && t.UserId == userId))
                   .Count(out long total)
                   .Take(request.PageSize).Skip(request.PageSize * (request.PageIndex - 1))
                   .OrderByDescending(o => o.t1.MailNo)
                   .ToListAsync<MailListByTagDto>(cancellationToken);
                totalCount = total;
            }

            var users = await msSql.Select<SysUserInfo>()
                .Where(o => lst.Any(u => u.IgnoreBy == o.UserId || u.SortBy == o.UserId || u.UndertakeUserId == o.UserId))
                .ToListAsync(o => new { UserId = o.UserId, CnName = o.CnName });

            lst.ForEach(o =>
            {
                o.IgnoreByUserName = users.Find(u => u.UserId == o.IgnoreBy)?.CnName;
                o.SortByUserName = users.Find(u => u.UserId == o.SortBy)?.CnName;
                o.UndertakeUserName = users.Find(u => u.UserId == o.UndertakeUserId)?.CnName;
            });

            return new PageResult<MailListByTagDto>()
            {
                Data = lst,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };

        }
    }
}
