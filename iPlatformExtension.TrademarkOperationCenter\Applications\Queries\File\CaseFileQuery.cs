﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.File;

/// <summary>
/// 案件附件查询
/// </summary>
/// <param name="ObjectIds">关联id集合</param>
/// <param name="Keyword">查询关键词</param>
/// <param name="PageIndex">页码</param>
/// <param name="PageSize">页面大小</param>
/// <param name="ReturnUrl">返回url</param>
public sealed record CaseFileQuery(
    IEnumerable<string> ObjectIds, 
    string? Keyword, 
    int? PageIndex, 
    int? PageSize,
    bool ReturnUrl = false) : IRequest<IEnumerable<FileListDto>>, IPaginationParameters;