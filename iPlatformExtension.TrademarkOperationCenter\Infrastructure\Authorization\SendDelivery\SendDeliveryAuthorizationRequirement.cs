﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using Microsoft.AspNetCore.Authorization;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Authorization.SendDelivery;

/// <summary>
/// 发动递交命令权限认证条件
/// </summary>
/// <param name="FlowType">流程类型</param>
/// <param name="FlowSubType">流程自类型</param>
public record SendDeliveryAuthorizationRequirement(string FlowType, string FlowSubType) : IAuthorizationRequirement
{
    /// <summary>
    /// 需要做验证的按钮
    /// </summary>
    public IList<DeliveryButton> RequireToAuthorizeButtons { get; } = new List<DeliveryButton>();
}