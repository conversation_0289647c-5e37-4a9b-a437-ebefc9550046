﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class ProcForeignSupplierContactRepository
    : BaseRepository<ProcForeignSupplierContact>, IProcForeignSupplierContactRepository
{
    public ProcForeignSupplierContactRepository(IFreeSql<PlatformFreeSql> freeSql, UnitOfWorkManage<PlatformFreeSql> unitOfWorkManager) 
        : base(unitOfWorkManager.Orm ?? freeSql)
    {
        unitOfWorkManager.Binding(this);
    }
}