﻿using System.Runtime.CompilerServices;
using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Finance.Applications.Queries.ForeignBill;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal sealed class ForeignBillPaymentBatchQueryHandler(IFreeSql freeSql) : IRequestHandler<ForeignBillPaymentBatchQuery, IEnumerable<ForeignBillPaymentBatchDto>>
{
    public async Task<IEnumerable<ForeignBillPaymentBatchDto>> Handle(ForeignBillPaymentBatchQuery request, CancellationToken cancellationToken)
    {
        var billIds = request.BillIds;

        var results = await freeSql.Select<BillPayBatchList>()
            .From<BillPayBatch, CusCustomer>().WithLock()
            .LeftJoin((list, batch, customer) => list.BatchId == batch.BatchId)
            .LeftJoin((list, batch, customer) => batch.CustomerId == customer.CustomerId)
            .Where((list, batch, customer) => billIds.Contains(list.BillId))
            .ToListAsync((list, batch, customer) => new ForeignBillPaymentBatchDto()
            {
                BillId = list.BillId,
                PaidBank = batch.BankId ?? string.Empty,
                RemittanceCompany = batch.CompanyId ?? string.Empty,
                RemittanceFeeSharingMethod = batch.RemittanceChargesTodetail ?? string.Empty,
                Remark = batch.Remark ?? string.Empty,
                Country = customer.CountryId ?? string.Empty
            }, cancellationToken);


        return results;
    }
}