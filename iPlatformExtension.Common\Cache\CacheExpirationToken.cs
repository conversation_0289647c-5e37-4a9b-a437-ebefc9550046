﻿using iPlatformExtension.Common.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;

namespace iPlatformExtension.Common.Cache;

/// <summary>
/// 缓存失效令牌。
/// 用于手动触发缓存失效。
/// </summary>
/// <typeparam name="T">缓存值的类型参数。用于区分不同的类型的缓存。</typeparam>
public sealed class CacheExpirationToken<T>(ILogger<CacheExpirationToken<T>> logger) : CacheExpirationToken(logger)
{
    public override string Name { get; } = GetName(typeof(T));

    private static string GetName(Type cacheValueType)
    {
        switch (cacheValueType.IsGenericType)
        {
            case false when !cacheValueType.IsSystemType():
                return cacheValueType.Name;
            case true:
            {
                var genericParameters = cacheValueType.GetGenericArguments();
                for (var i = genericParameters.Length - 1; i >= 0; i--)
                {
                    var genericParameterType = genericParameters[i];

                    var name = GetName(genericParameterType);
                    if (name != string.Empty)
                    {
                        return name;
                    }
                }

                break;
            }
        }

        return string.Empty;
    }
}

/// <summary>
/// 缓存失效令牌。
/// 用于手动触发缓存失效。
/// </summary>
public abstract class CacheExpirationToken(ILogger logger) : IChangeToken
{
    private CancellationTokenSource _tokenSource = new();

    public abstract string Name { get; }

    public IDisposable RegisterChangeCallback(Action<object> callback, object? state)
    {
        return _tokenSource.Token.Register(callback!, state);
    }

    public bool ActiveChangeCallbacks => true;

    public bool HasChanged => _tokenSource.IsCancellationRequested;

    /// <summary>
    /// 触发缓存失效。
    /// </summary>
    public void ExpireCache()
    {
        _tokenSource.Cancel();
        logger.LogDebug("清除[{Name}]的缓存", Name);
    }

    /// <summary>
    /// 令牌重置。保证注册令牌时，缓存为没有失效的状态。
    /// </summary>
    /// <returns>重置后的令牌</returns>
    internal IChangeToken Refresh()
    {
        if (!_tokenSource.IsCancellationRequested) return this;
        Interlocked.Exchange(ref _tokenSource, new CancellationTokenSource());
        logger.LogDebug("重置[{Name}]的缓存失效令牌", Name);
        return this;
    }
}
