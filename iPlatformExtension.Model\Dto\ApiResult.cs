﻿using System.ComponentModel;
using System.Diagnostics;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Pooled;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 
/// </summary>
public abstract class ApiResult<T> : IPooledObject
{
    /// <summary>
    /// 默认成功消息
    /// </summary>
    protected const string DefaultSuccessMessage = "操作成功";

    /// <summary>
    /// 默认失败消息
    /// </summary>
    protected const string DefaultFailedMessage = "操作失败";
    
    /// <summary>
    /// 请求结果
    /// </summary>
    public virtual bool Success { get; set; } = true;
    
    /// <summary>
    /// 请求返回的数据
    /// </summary>
    public virtual T? Data { get; set; }

    /// <summary>
    /// 提示信息
    /// </summary>
    public virtual string Message { get; set; } = DefaultSuccessMessage;

    /// <summary>
    /// 成功
    /// </summary>
    /// <returns></returns>
    public virtual ApiResult<T> Succeed(string? message = DefaultSuccessMessage)
    {
        Success = true;
        Message = string.IsNullOrWhiteSpace(message) ? DefaultSuccessMessage : message;
        return this;
    }

    /// <summary>
    /// 成功
    /// </summary>
    /// <param name="data">返回数据</param>
    /// <returns></returns>
    public virtual ApiResult<T> Succeed(T? data)
    {
        Succeed();
        Data = data;
        return this;
    }

    /// <summary>
    /// 失败
    /// </summary>
    /// <returns></returns>
    public virtual ApiResult<T> Fail(string? message = DefaultFailedMessage)
    {
        Success = false;
        Message = string.IsNullOrWhiteSpace(message) ? DefaultFailedMessage : message;
        return this;
    }

    /// <summary>
    /// 失败
    /// </summary>
    /// <param name="exception">异常</param>
    /// <returns></returns>
    public virtual ApiResult<T> Fail(Exception exception)
    {
        ArgumentNullException.ThrowIfNull(exception);
        Fail(exception.Message);
        Data = default(T);
        return this;
    }

    /// <summary>
    /// 从对象池中获取执行的操作
    /// </summary>
    public abstract void OnGet();

    /// <summary>
    /// 回归对象池中执行的操作
    /// </summary>
    public virtual void OnReturn()
    {
        Data = default(T);
        Succeed();
    }
}

/// <summary>
/// 
/// </summary>
public abstract class ApiResult : ApiResult<object>
{
    
}

/// <summary>
/// 有追踪id的apiResult
/// </summary>
public class TraceApiResult<T> : ApiResult<T>
{
    /// <summary>
    /// 请求追踪id
    /// </summary>
    [Description("请求追踪id")]
    public string? TraceId { get; set; } = Activity.Current?.TraceId.ToString();

    /// <summary>
    /// 从对象池中获取执行的操作
    /// </summary>
    public override void OnGet()
    {
        TraceId = Activity.Current?.TraceId.ToString();
    }

    /// <summary>
    /// 回归对象池中执行的操作
    /// </summary>
    public override void OnReturn()
    {
        base.OnReturn();
        TraceId = null;
    }
}

/// <inheritdoc />
public class TraceApiResult : TraceApiResult<object>
{
    /// <inheritdoc />
    public override TraceApiResult<object> Fail(Exception exception)
    {
        base.Fail(exception);
        Data = exception.Data;
        return this;
    }
}

/// <summary>
/// 返回接口统一封装
/// </summary>
[Description("返回接口统一封装")]
public class ResultData<T> : TraceApiResult<T>
{
    /// <summary>
    /// 结果编码
    /// </summary>
    [Description("结果编码")]
    public int Code { get; set; } = ResultCode.Success;
    
    /// <summary>
    /// 请求结果
    /// </summary>
    [Description("请求结果")]
    public override bool Success { get; set; } = true;
    
    /// <summary>
    /// 请求返回的数据
    /// </summary>
    [Description("请求返回的数据")]
    public override T? Data { get; set; }

    /// <summary>
    /// 提示信息
    /// </summary>
    [Description("提示信息")]
    public override string Message { get; set; } = DefaultSuccessMessage;

    /// <summary>
    /// 成功
    /// </summary>
    /// <param name="message">成功信息</param>
    /// <returns></returns>
    public override ResultData<T> Succeed(string? message = DefaultSuccessMessage)
    {
        base.Succeed(message);
        Code = ResultCode.Success;
        return this;
    }

    /// <summary>
    /// 失败
    /// </summary>
    /// <param name="message">失败消息</param>
    /// <returns></returns>
    public override ResultData<T> Fail(string? message = DefaultFailedMessage)
    {
        base.Fail(message);
        Code = ResultCode.Failure;
        return this;
    }

    /// <summary>
    /// 失败
    /// </summary>
    /// <param name="exception">异常</param>
    /// <returns></returns>
    public override ResultData<T> Fail(Exception exception)
    {
        base.Fail(exception);
        return this;
    }

    /// <summary>
    /// 回归对象池中执行的操作
    /// </summary>
    public override void OnReturn()
    {
        base.OnReturn();
        Code = ResultCode.Success;
    }
}

/// <inheritdoc />
public class ResultData : ResultData<object>
{
    /// <summary>
    /// 成功
    /// </summary>
    /// <param name="message">成功信息</param>
    /// <returns></returns>
    public override ResultData Succeed(string? message = DefaultSuccessMessage)
    {
        base.Succeed(message);
        Code = ResultCode.Success;
        return this;
    }

    /// <summary>
    /// 失败
    /// </summary>
    /// <param name="message">失败消息</param>
    /// <returns></returns>
    public override ResultData Fail(string? message = DefaultFailedMessage)
    {
        base.Fail(message);
        Code = ResultCode.Failure;
        return this;
    }

    /// <summary>
    /// 失败
    /// </summary>
    /// <param name="exception">异常</param>
    /// <returns></returns>
    public override ResultData Fail(Exception exception)
    {
        base.Fail(exception);
        Data = exception.Data;
        return this;
    }
}

/// <summary>
/// 分页结果
/// </summary>
public class PaginationResult : ResultData
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="pageResult">分页结果</param>
    public PaginationResult(PageResultBase pageResult)
    {
        (Page, PageSize, TotalPage, Total, HasNextPage, HasPreviousPage) = pageResult;
        Data = pageResult;
    }

    /// <summary>
    /// 返回数据
    /// </summary>
    public sealed override object? Data
    {
        get => base.Data;
        set => base.Data = value;
    }

    /// <summary>
    /// 无参构造函数
    /// </summary>
    public PaginationResult()
    {
        
    }
    
    /// <summary>
    /// 页码
    /// </summary>
    [Description("页码")]
    public int Page { get;  set; }
    
    /// <summary>
    /// 页面大小
    /// </summary>
    [Description("页面大小")]
    public int PageSize { get;  set; }
    
    /// <summary>
    /// 总页数
    /// </summary>
    [Description("总页数")]
    public long TotalPage { get;  set; }
    
    /// <summary>
    /// 总数
    /// </summary>
    [Description("总数")]
    public long Total { get;  set; }

    /// <summary>
    /// 是否有下一页
    /// </summary>
    [Description("是否有下一页")]
    public bool HasNextPage { get;  set; }

    /// <summary>
    /// 是否还有上一页
    /// </summary>
    [Description("是否还有上一页")]
    public bool HasPreviousPage { get;  set; }
    
    /// <summary>
    /// 成功
    /// </summary>
    /// <param name="message">成功信息</param>
    /// <returns></returns>
    public override PaginationResult Succeed(string? message = DefaultSuccessMessage)
    {
        base.Succeed(message);
        Code = ResultCode.Success;
        return this;
    }

    /// <summary>
    /// 失败
    /// </summary>
    /// <param name="message">失败消息</param>
    /// <returns></returns>
    public override PaginationResult Fail(string? message = DefaultFailedMessage)
    {
        base.Fail(message);
        Code = ResultCode.Failure;
        return this;
    }

    /// <summary>
    /// 失败
    /// </summary>
    /// <param name="exception">异常</param>
    /// <returns></returns>
    public override PaginationResult Fail(Exception exception)
    {
        base.Fail(exception);
        return this;
    }
}

/// <summary>
/// 分页结果泛型
/// </summary>
/// <typeparam name="T">泛型参数</typeparam>
public class PaginationResult<T> : PaginationResult
{
    /// <summary>
    /// 无参构造函数
    /// </summary>
    public PaginationResult()
    {
        
    }
    
    /// <summary>
    /// 基类构造函数
    /// </summary>
    /// <param name="pageResult">分页数据</param>
    public PaginationResult(PageResultBase pageResult) : base(pageResult)
    {
    }

    /// <summary>
    /// 数据
    /// </summary>
    [Description("数据")]
    public new IEnumerable<T>? Data
    {
        get => (IEnumerable<T>?) base.Data;
        set => base.Data = value;
    }
}