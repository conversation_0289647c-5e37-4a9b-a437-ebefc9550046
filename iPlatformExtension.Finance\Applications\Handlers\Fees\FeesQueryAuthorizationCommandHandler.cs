using System.Security.Claims;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeesQueryAuthorizationCommandHandler(
    IFreeSql freeSql,
    IHttpContextAccessor httpContextAccessor,
    ILogger<FeesQueryAuthorizationCommandHandler> logger)
    : IRequestHandler<FeesQueryAuthorizationCommand, FeeQueryDto>
{
    private readonly ILogger _logger = logger;

    public async Task<FeeQueryDto> Handle(FeesQueryAuthorizationCommand request, CancellationToken cancellationToken)
    {
        var queryDto = request.Dto;
        var httpContext = httpContextAccessor.HttpContext;
        var userId = (httpContext?.User).GetUserId();
        var isSales = httpContext?.User.HasClaim(ClaimTypes.Role, "sales") ?? false;
        var isCommercial = httpContext?.User.HasClaim(ClaimTypes.Role, "business") ?? false;

        if (queryDto.IgnoreAuthorization)
        {
            return queryDto;
        }
        
        _logger.LogInformation("当前用户[{UserId}], 是销售：[{IsSales}], 是商务：[{IsCommercial}]", userId, isSales, isCommercial);
        
        var companyIds = await freeSql.Select<BasCompany>().WithLock()
            .Where(company => queryDto.BelongCompanyCodes.Contains(company.CompanyCode))
            .ToListAsync(company => company.CompanyId, cancellationToken);
        
        var customerIdSet = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        if (isSales)
        {
            var customerIds = await freeSql.Select<CusFollowList>().WithLock()
                .Where(list => list.TrackUser == userId && list.IsEnabled == true && list.CustomerUserType == "ay")
                .ToListAsync(list => list.CustomerId, cancellationToken);
            customerIdSet.UnionWith(customerIds);
        }

        if (isCommercial)
        {
            var customerIds = await freeSql.Select<CusCustomer>().WithLock()
                .Where(customer => customer.BusiUserId == userId && customer.IsEnabled == true)
                .ToListAsync(customer => customer.CustomerId, cancellationToken);
            customerIdSet.UnionWith(customerIds);
        }

        if (!(isCommercial || isSales))
        {
            customerIdSet.UnionWith(queryDto.CustomerIds);
        }
        else
        {
            if (queryDto.CustomerIds.Any())
            {
                customerIdSet.IntersectWith(queryDto.CustomerIds);
            }
            if (!customerIdSet.Any())
            {
                customerIdSet.Add(string.Empty);
            }
        }
        queryDto.CustomerIds = customerIdSet.ToArray();

        var belongCompanyIds = new HashSet<string>(companyIds, StringComparer.OrdinalIgnoreCase);
        if (queryDto.CaseSourceCompanyIds.Any())
        {
            belongCompanyIds.IntersectWith(queryDto.CaseSourceCompanyIds);
        }
        queryDto.CaseSourceCompanyIds = belongCompanyIds.ToArray();

        return queryDto;
    }
}