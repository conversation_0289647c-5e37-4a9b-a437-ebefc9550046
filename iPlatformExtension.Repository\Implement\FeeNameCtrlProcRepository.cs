﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class FeeNameCtrlProcRepository(
    IFreeSql<PlatformFreeSql> freeSql, 
    UnitOfWorkManage<PlatformFreeSql> unitOfWorkManager) : 
    DefaultRepository<FeeNameCtrlProc>(freeSql, unitOfWorkManager), IFeeNameCtrlProcRepository;