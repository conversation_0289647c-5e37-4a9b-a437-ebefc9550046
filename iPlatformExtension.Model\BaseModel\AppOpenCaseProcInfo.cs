using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_open_case_proc_info", DisableSyncStructure = true)]
	public partial class AppOpenCaseProcInfo {

		[ Column(Name = "allocate_date")]
		public DateTime? AllocateDate { get; set; }

		[ Column(Name = "app_obj_id", StringLength = 50)]
		public string AppObjId { get; set; }

		[ Column(Name = "audit_result", StringLength = 500)]
		public string AuditResult { get; set; }

		[ Column(Name = "back_inventor_date")]
		public DateTime? BackInventorDate { get; set; }

		[ Column(Name = "back_ipr_date")]
		public DateTime? BackIprDate { get; set; }

		[ Column(Name = "bus_type_id", StringLength = 50)]
		public string BusTypeId { get; set; }

		[ Column(Name = "cancellation_reason", StringLength = 2000)]
		public string CancellationReason { get; set; }

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 200)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "ctrl_proc_mark", StringLength = 50)]
		public string CtrlProcMark { get; set; }

		[ Column(Name = "ctrl_proc_property", StringLength = 50)]
		public string CtrlProcProperty { get; set; }

		[ Column(Name = "cus_due_date")]
		public DateTime? CusDueDate { get; set; }

		[ Column(Name = "cus_finish_date")]
		public DateTime? CusFinishDate { get; set; }

		[ Column(Name = "cus_first_date")]
		public DateTime? CusFirstDate { get; set; }

		[ Column(Name = "cus_inventor_date")]
		public DateTime? CusInventorDate { get; set; }

		[ Column(Name = "cus_ipr_date")]
		public DateTime? CusIprDate { get; set; }

		[ Column(Name = "customer_case_no", StringLength = 50)]
		public string CustomerCaseNo { get; set; }

		[ Column(Name = "customer_case_no_t", StringLength = 50)]
		public string CustomerCaseNoT { get; set; }

		[ Column(Name = "entrust_date")]
		public DateTime? EntrustDate { get; set; }

		[ Column(Name = "examine_percentage", DbType = "money")]
		public decimal? ExaminePercentage { get; set; }

		[ Column(Name = "examiner", StringLength = 50)]
		public string Examiner { get; set; }

		[ Column(Name = "examiner_dept", StringLength = 500)]
		public string ExaminerDept { get; set; }

		[ Column(Name = "examiner_tel", StringLength = 50)]
		public string ExaminerTel { get; set; }

		[ Column(Name = "file_desc_id", StringLength = 50)]
		public string FileDescId { get; set; }

		[ Column(Name = "filing_type", StringLength = 50)]
		public string FilingType { get; set; }

		[ Column(Name = "finish_date")]
		public DateTime? FinishDate { get; set; }

		[ Column(Name = "finish_doc_date")]
		public DateTime? FinishDocDate { get; set; }

		[ Column(Name = "first_doc_date")]
		public DateTime? FirstDocDate { get; set; }

		[ Column(Name = "first_examine_user_id", StringLength = 50)]
		public string FirstExamineUserId { get; set; }

		[ Column(Name = "foregin_agency_id", StringLength = 50)]
		public string ForeginAgencyId { get; set; }

		[ Column(Name = "int_due_date")]
		public DateTime? IntDueDate { get; set; }

		[ Column(Name = "int_finish_date")]
		public DateTime? IntFinishDate { get; set; }

		[ Column(Name = "int_first_date")]
		public DateTime? IntFirstDate { get; set; }

		[ Column(Name = "invalidate_code", StringLength = 50)]
		public string InvalidateCode { get; set; }

		[ Column(Name = "invalidate_holder_name", StringLength = 200)]
		public string InvalidateHolderName { get; set; }

		[ Column(Name = "invalidate_request_user", StringLength = 200)]
		public string InvalidateRequestUser { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "is_insteadofsubmitting")]
		public bool? IsInsteadofsubmitting { get; set; }

		[ Column(Name = "is_substance", StringLength = 50)]
		public string IsSubstance { get; set; }

		[ Column(Name = "legal_due_date")]
		public DateTime? LegalDueDate { get; set; }

		[ Column(Name = "legal_provisions", StringLength = 50)]
		public string LegalProvisions { get; set; }

		[ Column(Name = "n_CameFileID", StringLength = 50)]
		public string NCameFileID { get; set; }

		[ Column(Name = "n_StatusID", StringLength = 50)]
		public string NStatusID { get; set; }

		[ Column(Name = "notice_name", StringLength = 500)]
		public string NoticeName { get; set; }

		[ Column(Name = "objection", StringLength = 50)]
		public string Objection { get; set; }

		[ Column(Name = "objection_id", StringLength = 1000)]
		public string ObjectionId { get; set; }

		[ Column(Name = "objection_name", StringLength = 2000)]
		public string ObjectionName { get; set; }

		[ Column(Name = "official_note", StringLength = 4000)]
		public string OfficialNote { get; set; }

		[ Column(Name = "opponents", StringLength = 50)]
		public string Opponents { get; set; }

		[ Column(Name = "out_user", StringLength = 50)]
		public string OutUser { get; set; }

		[ Column(Name = "postmark_date")]
		public DateTime? PostmarkDate { get; set; }

		[ Column(Name = "proc_app_date")]
		public DateTime? ProcAppDate { get; set; }

		[ Column(Name = "proc_app_no", StringLength = 50)]
		public string ProcAppNo { get; set; }

		[ Column(Name = "proc_dept_id", StringLength = 50)]
		public string ProcDeptId { get; set; }

		[ Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
		public string ProcId { get; set; }

		[ Column(Name = "proc_no", StringLength = 50)]
		public string ProcNo { get; set; }

		[ Column(Name = "proc_note", StringLength = 4000)]
		public string ProcNote { get; set; }

		[ Column(Name = "proc_status_id", StringLength = 50)]
		public string ProcStatusId { get; set; }

		[ Column(Name = "reason_remark", StringLength = 2000)]
		public string ReasonRemark { get; set; }

		[ Column(Name = "receive_date")]
		public DateTime? ReceiveDate { get; set; }

		[ Column(Name = "receive_no", StringLength = 50)]
		public string ReceiveNo { get; set; }

		[ Column(Name = "requestor", StringLength = 2000)]
		public string Requestor { get; set; }

		[ Column(Name = "result_remark", StringLength = 2000)]
		public string ResultRemark { get; set; }

		[ Column(Name = "send_official_date")]
		public DateTime? SendOfficialDate { get; set; }

		[ Column(Name = "send_partner_date")]
		public DateTime? SendPartnerDate { get; set; }

		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		[ Column(Name = "simple_deliver_date")]
		public DateTime? SimpleDeliverDate { get; set; }

		[ Column(Name = "sub_proc_status_id", StringLength = 50)]
		public string SubProcStatusId { get; set; }

		[ Column(Name = "supplementary_deliver_date")]
		public DateTime? SupplementaryDeliverDate { get; set; }

		[ Column(Name = "titular_write_user", StringLength = 50)]
		public string TitularWriteUser { get; set; }

		[ Column(Name = "track_user_id", StringLength = 50)]
		public string TrackUserId { get; set; }

		[ Column(Name = "translate_amount", StringLength = 50)]
		public string TranslateAmount { get; set; }

		[ Column(Name = "translate_reader", StringLength = 50)]
		public string TranslateReader { get; set; }

		[ Column(Name = "translate_type", StringLength = 50)]
		public string TranslateType { get; set; }

		[ Column(Name = "translator", StringLength = 50)]
		public string Translator { get; set; }

		[ Column(Name = "undertake_user_id", StringLength = 50)]
		public string UndertakeUserId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
