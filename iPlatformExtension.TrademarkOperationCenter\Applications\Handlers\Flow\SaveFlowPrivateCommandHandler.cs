﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    /// <summary>
    /// 保存自定义标签
    /// </summary>
    internal sealed class SaveFlowPrivateCommandHandler : IRequestHandler<SaveFlowPrivateCommand>
    {
        private readonly IFreeSql _freeSql;
        private readonly ISysFlowPrivateRepository _sysFlowPrivateRepository;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly IFlowActivityRepository _sysFlowActivityRepository;

        public SaveFlowPrivateCommandHandler(IFreeSql freeSql, ISysFlowPrivateRepository sysFlowPrivateRepository,
            IHttpContextAccessor httpContextAccessor, IMapper mapper,
            IFlowActivityRepository sysFlowActivityRepository)
        {
            _freeSql = freeSql;
            _sysFlowPrivateRepository = sysFlowPrivateRepository;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
            _sysFlowActivityRepository = sysFlowActivityRepository;
        }

        public async Task Handle(SaveFlowPrivateCommand request, CancellationToken cancellationToken)
        {
            var userid = _httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userid);
            var privateIdList = request.PrivateList.Select(x => x.PrivateId);
            var deleteList = await _sysFlowPrivateRepository
                .Where(it => it.UserId == userid && it.FlowType == request.FlowType)
                .WhereIf(privateIdList.Any(), it => !privateIdList.Contains(it.PrivateId))
                .WhereIf(request.FlowSubType is not null, it => it.FlowSubType == request.FlowSubType).ToListAsync(cancellationToken);

            await _sysFlowPrivateRepository.DeleteAsync(deleteList, cancellationToken);

            var flowPrivateList = _mapper.Map<List<SysFlowPrivate>>(request.PrivateList);

            foreach (var flowPrivate in flowPrivateList)
            {
                if (string.IsNullOrWhiteSpace(flowPrivate.PrivateId)) { flowPrivate.PrivateId = Guid.NewGuid().ToString(); }
                flowPrivate.UserId = userid;
                flowPrivate.FlowType = request.FlowType;
                if (request.FlowSubType is not null)
                {
                    flowPrivate.FlowSubType = request.FlowSubType;
                }
                await _sysFlowPrivateRepository.InsertOrUpdateAsync(flowPrivate, cancellationToken);
            }

            var deleteIds = deleteList.Select(it => it.PrivateId);
            var sysFlowActivities = await _sysFlowActivityRepository.Where(it => deleteIds.Contains(it.PrivateId)).ToListAsync(cancellationToken);
            sysFlowActivities.ForEach(it => it.PrivateId = "");
            await _sysFlowActivityRepository.UpdateAsync(sysFlowActivities, cancellationToken);
        }
    }
}

