﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace iPlatformExtension.Commission.Infrastructure.OpenApi;

internal sealed class OpenApiHeaderFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        operation.Parameters.Add(new OpenApiParameter()
        {
            Name = "Authorization",
            In = ParameterLocation.Header,
            Required = false,
            Description = "Authorization header using the Bearer scheme."
        });
    }
}