﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Nice;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Nice;

internal sealed class ProcNiceCategoryQueryHandler(IFreeSql freeSql) : IRequestHandler<ProcNiceCategoryQuery, IEnumerable<NiceCategoryDto>>
{
    public async Task<IEnumerable<NiceCategoryDto>> Handle(ProcNiceCategoryQuery request, CancellationToken cancellationToken)
    {
        var procInfo = await freeSql.Select<CaseProcInfo>(request.ProcId).WithLock()
            .Include(procInfo => procInfo.CaseInfo).ToOneAsync(procInfo => new CaseProcInfo
            {
                ProcId = procInfo.ProcId,
                TrademarkNiceClasses = procInfo.TrademarkNiceClasses,
                CaseInfo = new CaseInfo
                {
                    Id = procInfo.CaseInfo.Id,
                    TrademarkClass = procInfo.CaseInfo.TrademarkClass
                }
            }, cancellationToken);

        const StringSplitOptions splitOptions = StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries;
        var procNiceClasses =
            procInfo.TrademarkNiceClasses?.Split(';', splitOptions).Select(classValue => classValue.PadLeft(2, '0'))
                .ToArray() ?? Array.Empty<string>();
        var caseNiceClasses =
            procInfo.CaseInfo.TrademarkClass?.Split(';', splitOptions).Select(classValue => classValue.PadLeft(2, '0'))
                .ToArray() ?? Array.Empty<string>();

        var procNiceInfos = await freeSql.Select<ProcNiceCategory>().WithLock()
            .Where(category => category.ProcId == request.ProcId).ToListAsync(cancellationToken);
        return caseNiceClasses.GroupJoin(procNiceInfos, niceClass => niceClass, category => category.GrandNumber,
            (niceClass, categories) => new NiceCategoryDto(string.Empty, 0, niceClass, string.Empty, null)
            {
                Selected = procNiceClasses.Contains(niceClass),
                CustomCategories = categories.OrderBy(category => category.Order).Select(category => category.CategoryName).ToList()
            }).ToList();
    }
}