using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace iPlatformExtension.Common.Clients.WxWork
{
    public class WxWorkClient : IWxWorkClient
    {
        public readonly ConcurrentDictionary<string, WxResponseMessage> Tokens = new ConcurrentDictionary<string, WxResponseMessage>();
        public readonly string base_uri = "https://qyapi.weixin.qq.com/";
        public readonly string gettoken_uri = "/cgi-bin/gettoken";
        public readonly string send = "/cgi-bin/message/send?access_token={0}";
        public readonly string get_jsapi_ticket = "/cgi-bin/get_jsapi_ticket?access_token={0}";
        public readonly string getuserinfo = "/cgi-bin/auth/getuserinfo?access_token={0}&code={1}";
        public readonly string send_robot = "/cgi-bin/webhook/send?key={0}";



        public readonly Dictionary<string, string> RedirectCodeList = new Dictionary<string, string>() { { "MailCenter", "/123" }, };

        public WxWorkClient(IConfiguration configuration)
        {
            wxConfig = new WxConfig();
            configuration.Bind("WxConfig", wxConfig);
            _httpClient = new HttpClient() { BaseAddress = new Uri(base_uri) };

        }

        static JsonSerializerOptions jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };

        private HttpClient _httpClient;

        public WxConfig wxConfig { get; set; }

        public string GetToken()
        {
            // return "3y__nSIzj2ckpnesLYkxv-_RvELWwNwZZ_I8T5KdviBXpUsU1NvGVPTpk4VKkToKFqQt0b5cevgrJ3OhqUMXWZp8GWOPfGBzi8X-ZwnKeT6udW2idogbWp5bhyWpLEyO2cuszf3BDc5ITpgTDIX3cz_XiOH71S9zpp1hKyxpCgb-IR3kaqhZUUMCiwGpI7Z6KINVjLI_sTLrSXDe9ssj8w";
            ArgumentNullException.ThrowIfNull(wxConfig);
            string sectionName = "wx_token";
            if (Tokens.TryGetValue(sectionName, out WxResponseMessage value))
            {
                if (value.ExpiresTime > DateTime.Now.AddSeconds(-60))
                {
                    return value.access_token;
                }
                Tokens.Remove(sectionName, out _);
            }

            var content = _httpClient.GetAsync($"{gettoken_uri}?corpid={wxConfig.Corpid}&corpsecret={wxConfig.Corpsecret}").GetAwaiter().GetResult();
            //var content = "{\"errcode\":0,\"errmsg\":\"ok\",\"access_token\":\"mydTJq5x5YIH9jGDXzTFGsTitFwKAGT-1sa2JWi2wA2NWLqRUKgdtd5ytE4FHlCTt6NKD5ZfqvzCHP3pramK1fCcTPw0ZNIcoppignVrxxB1V7XFbxXznm8oXVATUNiuyS-uekAtu3uUKFRxSIVi2EgPR8U18p500dZH3wKXs9iA7edxtfzAKJX6hm-wCFs8NXxaf3LP10-vI6xwtDvLQA\",\"expires_in\":7200}";
            //var message = Newtonsoft.Json.JsonConvert.DeserializeObject<WxResponseMessage>(content);
            var message = content.Content.ReadFromJsonAsync<WxResponseMessage>(jsonOptions).GetAwaiter().GetResult();
            if (message.errcode == 0 && message.errmsg == "ok")
            {
                Tokens.TryAdd(sectionName, message);
                return message.access_token;
            }
            throw new Exception(message.errmsg);
        }

        public string GetTicket()
        {
            // return "bxLdikRXVbTPdHSM05e5ux3cXXA3BlCNurvRLh13Y5RNjn4X5B_eq6jwTTe5IZ9KjQ-W9x6j7lItpBgYsLCuQQ";
            string sectionName = "wx_ticket";
            if (Tokens.TryGetValue(sectionName, out WxResponseMessage value))
            {
                if (value.ExpiresTime > DateTime.Now.AddSeconds(-60))
                {
                    return value.ticket;
                }
                Tokens.Remove(sectionName, out _);
            }
            var uri = string.Format(get_jsapi_ticket, GetToken());
            var res = _httpClient.GetAsync(uri).GetAwaiter().GetResult();
            var wxResponseMessage = res.Content.ReadFromJsonAsync<WxResponseMessage>(jsonOptions).GetAwaiter().GetResult();

            if (wxResponseMessage.errcode == 0 && wxResponseMessage.errmsg == "ok")
            {
                Tokens.TryAdd(sectionName, wxResponseMessage);
                return wxResponseMessage.ticket;
            }
            throw new Exception(wxResponseMessage.errmsg);
        }



        public async Task<WxResponseMessage> SendMarkdownMessageAsync(string UserName, WxMarkdown content)
        {
            try
            {
                ArgumentNullException.ThrowIfNull(content);
                WxMarkdownMessage wxMessage = new WxMarkdownMessage() { touser = UserName, agentid = wxConfig.Agentid, markdown = content };
                var data = Newtonsoft.Json.JsonConvert.SerializeObject(wxMessage);
                HttpContent httpContent = new StringContent(data, Encoding.UTF8, new System.Net.Http.Headers.MediaTypeHeaderValue("application/json"));
                var uri = string.Format(send, GetToken());
                var res = await _httpClient.PostAsync(uri, httpContent);
                WxResponseMessage? wxResponseMessage = await res.Content.ReadFromJsonAsync<WxResponseMessage>(jsonOptions);
                return wxResponseMessage;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 发送机器人消息
        /// </summary>
        /// <param name="Message">消息主体</param>
        /// <param name="Scope">生效范围</param>
        public async Task SentRobotMessageAsync(string Message, string Scope)
        {
            ArgumentNullException.ThrowIfNull(Message);

            var MessageTitle = wxConfig.Robots
                .Where(o => o.Scope == Scope).FirstOrDefault()?.MessageTitle;

            var robotGroups = wxConfig.Robots
                .Where(o => o.Scope == Scope)
                .SelectMany(o => o.Groups.Where(g => g.IsOpen))
                .ToList();

            var tasks = robotGroups.Select(async g =>
            {
                var content = JsonSerializer.Serialize(new
                {
                    msgtype = "text",
                    text = new
                    {
                        content = $"【{MessageTitle}】{Message}"
                    }
                });

                using var httpContent = new StringContent(
                    content,
                    Encoding.UTF8,
                    "application/json"
                );

                var uri = string.Format(send_robot, g.Key);
                var res = await _httpClient.PostAsync(uri, httpContent);
                return await res.Content.ReadFromJsonAsync<WxResponseMessage>(jsonOptions);
            });

            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// 发送文本卡片消息
        /// </summary>
        /// <param name="UserName"></param>
        /// <param name="content">卡片消息</param>
        /// <param name="redirectConfig">重定向配置</param>
        /// <returns></returns>
        public async Task<WxResponseMessage> SendCardMessageAsync(string UserName, TextCardContent content, RedirectConfig redirectConfig = null)
        {
            try
            {
                ArgumentNullException.ThrowIfNull(content);
                content.btntxt = wxConfig.RedirctTargetName;
                content.url = wxConfig.RedirectUri;

                if (redirectConfig != null)
                {
                    content.url = $"{content.url}?redirect_config={System.Web.HttpUtility.UrlEncode(Newtonsoft.Json.JsonConvert.SerializeObject(redirectConfig))}";
                }
                WxCardMessage wxMessage = new WxCardMessage() { msgtype = "textcard", touser = UserName, agentid = wxConfig.Agentid, textcard = content };
                var data = Newtonsoft.Json.JsonConvert.SerializeObject(wxMessage);
                HttpContent httpContent = new StringContent(data, Encoding.UTF8, new System.Net.Http.Headers.MediaTypeHeaderValue("application/json"));
                var uri = string.Format(send, GetToken());
                var res = await _httpClient.PostAsync(uri, httpContent);
                WxResponseMessage wxResponseMessage = await res.Content.ReadFromJsonAsync<WxResponseMessage>(jsonOptions);
                return wxResponseMessage;
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        public async Task<WxResponseMessage> GetUserInfoAsync(string code)
        {
            var uri = string.Format(getuserinfo, GetToken(), code);
            var res = await _httpClient.GetAsync(uri);
            WxResponseMessage wxResponseMessage = await res.Content.ReadFromJsonAsync<WxResponseMessage>(jsonOptions);
            return wxResponseMessage;
        }

        public async Task<WxResponseMessage> SendTextCardMessage(WxMessageContent wx)
        {
            var appid = wxConfig.Corpid;
            var news = new TextCardContent()
            {
                btntxt = wxConfig.RedirctTargetName,
                title = $"商标代理人平台流程更新提醒",
                description = //$"<div class=\"black\">{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}</div>" +
                $" <div class=\"black\">{wx.Title}</div>" +
                $"<div class=\"black\">提交人：{wx.OperationUser}</div>" +
                $"<div class=\"black\">接收人：{wx.Auditor}</div>" +
                $"<div class=\"black\">提交时间：{wx.SubmitDate}</div>" +
                $"<div class=\"black\">操作路径:【{wx.OperationPath}】</div>"
                ,
                url = wxConfig.RedirectUri
            };
            return await SendCardMessageAsync(wx.RecipientUserName, news);
        }


        public async Task<WxResponseMessage> SendTodoMessage(WxMessageContent wx)
        {
            var appid = wxConfig.Corpid;
            var news = new WxMarkdown()
            {
                content =
                $"`{wx.Title}`待办提醒 " +
                " \n>**事项详情** " +
                $" \n>事　项：<font color=\"info\">{wx.Title}提醒</font> " +
                $" \n>提交人：{wx.OperationUser}  " +
                $" \n>接收人：{wx.Auditor}  " +
                $" \n>提交时间：<font color=\"warning\">{wx.SubmitDate}</font>  " +
                $" \n>操作路径：<font color=\"comment\">【商标代理人平台-流程中心-我的待办-{wx.Title}】</font>  " +
                " \n>  " +
               $" \n>[系统地址]({wxConfig.RedirectUri})"
            };
            return await SendMarkdownMessageAsync("H04133", news);
        }

    }
}
