﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;
using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Commission.Infrastructure.Constant;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.Customer;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class ComputeForeignCommissionPointCommandHandler(IBigClientRepository clientRepository) 
    : IRequestHandler<ComputeForeignCommissionPointCommand, bool>
{
    public async Task<bool> Handle(ComputeForeignCommissionPointCommand request, CancellationToken cancellationToken)
    {
        var commission = request.ForeignTrademarkBonus;
        var freeSql = clientRepository.Orm;

        var configs = await freeSql.Select<TrademarkBonusConfig>()
            .Where(config => config.CtrlProcId == commission.CtrlProcId)
            .Where(config => config.IsEnabled == true)
            .Where(config => config.CaseDirection == commission.CaseDirection)
            .Where(config => config.CtrlProcStatusId == commission.ProcStatusId)
            .ToListAsync(cancellationToken);

        List<TrademarkBonusConfig> matchConfigs;
        if (!string.IsNullOrWhiteSpace(commission.CtrlProcMark))
        {
            matchConfigs = configs.Where(config => config.CtrlProcMark == commission.CtrlProcMark).ToList();
            if (matchConfigs.Count == 0)
            {
                matchConfigs = configs.Where(config => string.IsNullOrWhiteSpace(config.CtrlProcMark)).ToList();
            }
        }
        else
        {
            matchConfigs = configs.Where(config => string.IsNullOrWhiteSpace(config.CtrlProcMark)).ToList();
        }

        if (matchConfigs.Count is not 1)
        {
            return false;
        }

        var matchedConfig = matchConfigs[0];
        
        var factor = new ProcCommissionWeightFactor
        {
            ProcPoint = matchedConfig.RealPoint,
            TrademarkClassesPoint = matchedConfig.MultipartClassesPoint
        };
        
        var bigClient =
            await clientRepository.GetCacheValueAsync(
                new BigClientKey(commission.CustomerId, commission.CaseDirection, true), cancellationToken: cancellationToken);
        
        var isBigClient = bigClient is not null;
        commission.Bigclient = isBigClient.GetBooleanChineseDescription();
        
        if (bigClient is not null)
        {
            factor.BigClientPoint = bigClient.RealPoint;
        }

        commission.ProcPoint = factor.CalculateForeignTrademarkPoint(commission.TrademarkItemsNum ?? 0);

        return true;
    }
}