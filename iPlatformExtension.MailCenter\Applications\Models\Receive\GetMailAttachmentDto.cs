﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 获取邮件附件
/// </summary>
/// <param name="AttachmentId">附件主键id</param>
/// <param name="Bucket">块</param>
/// <param name="Extension">文件后缀</param>
/// <param name="FileName">存储文件名称</param>
/// <param name="InputTime">上传时间</param>
/// <param name="MailId">邮件id</param>
/// <param name="RealName">文件真实名称</param>
/// <param name="ServerPath">服务器地址</param>
/// <param name="FileSize">文件大小</param>
public record GetMailAttachmentDto(string AttachmentId, string Bucket, string Extension, string FileName, DateTime? InputTime,
    string MailId, string RealName, string ServerPath,double? FileSize,string DownUrl);

