﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;

/// <summary>
/// 批量递交验证类型
/// </summary>
public enum BatchDeliveriesValidateType
{
    /// <summary>
    /// 启动
    /// </summary>
    Startup,
    
    /// <summary>
    /// 更新
    /// </summary>
    Update,
    
    /// <summary>
    /// 流程递交
    /// </summary>
    FlowSubmit,
    
    /// <summary>
    /// 递交
    /// </summary>
    Deliver,
    
    /// <summary>
    /// 移交
    /// </summary>
    HandOver,
    
    /// <summary>
    /// 退回
    /// </summary>
    Reject,
    /// <summary>
    /// 提交到流程部启动(旧递交)
    /// </summary>
    StartupByT,
}