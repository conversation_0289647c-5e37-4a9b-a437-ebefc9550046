using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ud_task_details_case_porc_contact_list", DisableSyncStructure = true)]
	public partial class UdTaskDetailsCasePorcContactList {

		[ Column(Name = "obj_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ObjId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(StringLength = 50)]
		public string 案件任务ID { get; set; }

		[ Column(StringLength = 50)]
		public string 旧联系人 { get; set; }

		[ Column(StringLength = 50)]
		public string 新联系人 { get; set; }

		[ Column(Name = "contact_id", StringLength = 50)]
		public string ContactId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "error_columns", StringLength = 50)]
		public string ErrorColumns { get; set; }

		[ Column(Name = "error_message", StringLength = 50)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "id", StringLength = 50)]
		public string Id { get; set; }

		[ Column(Name = "o_contact_id", StringLength = 50)]
		public string OContactId { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "task_id", StringLength = 50)]
		public string TaskId { get; set; }

	}

}
