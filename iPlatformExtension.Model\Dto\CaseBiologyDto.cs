using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 
/// </summary>
public class CaseBiologyDto
{
    [JsonPropertyName("cur_id")]
    public string CurId { get; set; } = Guid.NewGuid().ToString().ToUpper();

    [JsonPropertyName("biology_address")]
    public string BiologyAddress { get; set; }

    [JsonPropertyName("biology_class")]
    public string BiologyClass { get; set; }

    [JsonPropertyName("biology_date")]
    public DateTime? BiologyDate { get; set; }

    [JsonPropertyName("biology_id")]
    public string BiologyId { get; set; }

    [JsonPropertyName("biology_islive")]
    public string BiologyIslive { get; set; }

    [JsonPropertyName("biology_serial")]
    public string BiologySerial { get; set; }

    [JsonPropertyName("case_id")]
    public string CaseId { get; set; }

    [JsonPropertyName("updateTime")]
    public DateTime UpdateTime { get; set; }

    [JsonPropertyName("updateUser")]
    public string UpdateUserId { get; set; }
}