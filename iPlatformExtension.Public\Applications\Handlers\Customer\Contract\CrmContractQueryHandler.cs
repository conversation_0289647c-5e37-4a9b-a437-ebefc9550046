﻿using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models.Customer.Contract;
using iPlatformExtension.Public.Applications.Models.File;
using iPlatformExtension.Public.Applications.Queries.Customer.Contract;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Customer.Contract;

internal sealed class CrmContractQueryHandler(IFreeSql<PlatformFreeSql> freeSql, HuaweiObsClient obsClient) : IRequestHandler<CrmContractQuery, CrmContractDto>
{
    public async Task<CrmContractDto> Handle(CrmContractQuery request, CancellationToken cancellationToken)
    {
        var crmContractId = request.CrmContractId;
        var crmContract = await freeSql.Select<CusContract>()
            .Where(contract => contract.CrmContractId == crmContractId)
            .ToOneAsync(contract => new CrmContractDto
            {
                ContractId = contract.ContractId,
                ContractNo = contract.ContractNo,
                ContractTitle = contract.ContractTitle,
                CrmContractId = contract.CrmContractId,
                ArchiveStatus = contract.ArchiveStatus,
                ApplicationType = contract.ApplicationType,
                ContractType = contract.ContractType,
                SigningDate = new DateOnly(contract.SigningDate!.Value.Year, contract.SigningDate.Value.Month, contract.SigningDate.Value.Day),
                EndDate = new DateOnly(contract.EndDate!.Value.Year, contract.EndDate.Value.Month, contract.EndDate.Value.Day),
                SignatoryUser = contract.SignatoryUser,
                MyContractingEntity = contract.MyContractingEntity,
                CustomerContractingEntity = contract.CustomerContractingEntity,
                Operator = contract.UpdateUserId,
            }, cancellationToken);

        if (crmContract is null)
        {
            throw new NotFoundException(crmContractId, "CRM合同");
        }
        
        var details = await freeSql.Select<ContractDetail>().Where(detail => detail.ContractId == crmContract.ContractId)
            .ToListAsync(cancellationToken);
        crmContract.Details = details.Select(detail => new ContractDetailDto
        {
            BusinessType = detail.ContractBusinessType,
            PreSalesUser = detail.PreSalesUsername,
            Remark = detail.Remark,
            CrmContractId = crmContractId,
            IsEnable = detail.IsEnable,
            EffectiveDate = new DateOnly(detail.StartDate.Year, detail.StartDate.Month, detail.StartDate.Day)
        }).ToList();

        var caseFiles = await freeSql.Select<CaseFile>().Where(file => file.ObjId == crmContract.ContractId)
            .ToListAsync(cancellationToken);
        var fileIds = caseFiles.Select(caseFile => int.Parse(caseFile.FileNo[4..])).ToList();
        var files = await freeSql.Select<FileListA>().Where(file => fileIds.Contains(file.Id))
            .ToListAsync(cancellationToken);
        crmContract.Files = files.Select(file => new FileInfoDto
        {
            FileName = file.RealName,
            FileUrl = obsClient.GenerateTemporaryUrl(file.GetObjectName(), file.Bucket, TimeSpan.FromDays(1)).SignUrl
        }).ToList();
        
        return crmContract;
    }
}