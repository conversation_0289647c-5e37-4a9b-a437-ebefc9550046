﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Company;
using iPlatformExtension.Public.Applications.Queries.Company;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Company
{
    /// <summary>
    /// 内部机构查询接口处理者
    /// </summary>
    internal sealed class SearchCompanyQueryHandler : IRequestHandler<SearchCompanyQuery, IEnumerable<SearchCompanyDto>>
    {
        private readonly IFreeSql _freeSql;

        public SearchCompanyQueryHandler(IFreeSql freeSql)
        {
            _freeSql = freeSql;
        }

        public async Task<IEnumerable<SearchCompanyDto>> Handle(SearchCompanyQuery request, CancellationToken cancellationToken)
        {
            var dbSelect = _freeSql.Select<BasCompany>()
                .Where(it => it.IsEnabled == true)
                .WhereIf(request.Name != null,
                    it => it.CompanyNameCn.Contains(request.Name) || it.CompanyNameEn.Contains(request.Name));
            if (request.PageIndex is not null && request.PageSize is not null)
            {
                dbSelect = dbSelect.Page(request.PageIndex.Value, request.PageSize.Value).Count(out var totalCount);
                var result = await dbSelect.WithLock().ToListAsync(it => new SearchCompanyDto(it.CompanyId, it.CompanyNameCn, it.CompanyNameEn, it.ShortNameCn, it.ShortNameEn,
                it.Fax, it.AddressCn, it.AddressDetail, it.AddressEn, it.Tel, it.Email, it.PostCode,it.Seq), cancellationToken);
                return new PageResult<SearchCompanyDto>()
                {
                    Data = result,
                    Page = request.PageIndex.Value,
                    PageSize = request.PageSize.Value,
                    Total = totalCount
                };
            }
            return await dbSelect.WithLock().ToListAsync(it => new SearchCompanyDto(it.CompanyId, it.CompanyNameCn, it.CompanyNameEn, it.ShortNameCn, it.ShortNameEn,
                it.Fax, it.AddressCn, it.AddressDetail, it.AddressEn, it.Tel, it.Email, it.PostCode,it.Seq), cancellationToken);
        }
    }
}

