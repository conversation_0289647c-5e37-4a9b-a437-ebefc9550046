using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_case_inventor_list", DisableSyncStructure = true)]
	public partial class ACaseInventorList {

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "id", StringLength = 50)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "inventor_id", StringLength = 100)]
		public string InventorId { get; set; }

		[ Column(Name = "is_represent")]
		public bool? IsRepresent { get; set; } = false;

		[ Column(Name = "is_unpub")]
		public bool? IsUnpub { get; set; } = false;

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
