using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "temp_agency_info", DisableSyncStructure = true)]
	public partial class TempAgencyInfo {

		
		public double? 成立年限 { get; set; }

		
		public double? 机构代码 { get; set; }

		
		public string 机构名称 { get; set; }

		
		public string 机构性质 { get; set; }

		
		public string 机构状态 { get; set; }

		
		public double? 专利代理师总数 { get; set; }

	}

}
