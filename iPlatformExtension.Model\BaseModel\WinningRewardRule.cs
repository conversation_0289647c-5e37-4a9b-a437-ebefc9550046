﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 胜诉奖励规则表
	/// </summary>
	[Table(Name = "winning_reward_rule", DisableSyncStructure = true)]
	public partial class WinningRewardRule {

		/// <summary>
		/// 主键
		/// </summary>
		[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
		public int Id { get; set; }

		/// <summary>
		/// 案件流向
		/// </summary>
		[Column(Name = "case_direction", StringLength = 50, IsNullable = false)]
		public string CaseDirection { get; set; } = "";

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "creation_time", InsertValueSql = "getdate()")]
		public DateTime CreationTime { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[Column(Name = "creator", StringLength = 50, IsNullable = false)]
		public string Creator { get; set; } = "";

		/// <summary>
		/// 任务名称id
		/// </summary>
		[Column(Name = "ctrl_proc_id", StringLength = 50, IsNullable = false)]
		public string CtrlProcId { get; set; } = "";

		/// <summary>
		/// 日期类型
		/// </summary>
		[Column(Name = "date_type", StringLength = 50, IsNullable = false)]
		public string DateType { get; set; } = "";

		/// <summary>
		/// 是否有效
		/// </summary>
		[Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 导师奖励
		/// </summary>
		[Column(Name = "mentor_reward", DbType = "money")]
		public decimal MentorReward { get; set; } = 0.00M;

		/// <summary>
		/// 裁定结果
		/// </summary>
		[Column(Name = "ruling_result", StringLength = 50, IsNullable = false)]
		public string RulingResult { get; set; } = "";

		/// <summary>
		/// 形势变更
		/// </summary>
		[Column(Name = "situation_changed")]
		public bool SituationChanged { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 更新者
		/// </summary>
		[Column(Name = "updater", StringLength = 50, IsNullable = false)]
		public string Updater { get; set; } = "";

		/// <summary>
		/// 撰写人奖励
		/// </summary>
		[Column(Name = "writer_reward", DbType = "money")]
		public decimal WriterReward { get; set; } = 0.00M;

		/// <summary>
		/// 导师流程id
		/// </summary>
		[Column(Name = "mentor_flow_node_id")]
		public string MentorFlowNodeId { get; set; } = "751DDA05-106B-429A-AB48-B64403B17E32";

	}

}
