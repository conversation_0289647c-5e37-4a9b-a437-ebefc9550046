﻿using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File
{
    internal sealed class AddDeliFileHandler(
        IDeliveryFilesRepository deliveryFileRepository,
        IFileDescriptionRepository fileDescriptionRepository,
        HuaweiObsClient huaweiObsClient,
        IHttpContextAccessor httpContextAccessor)
        : IRequestHandler<AddDeliFileCommand>
    {
        public async Task Handle(AddDeliFileCommand request, CancellationToken cancellationToken)
        {
            var currentUser = httpContextAccessor.HttpContext?.User.GetGivenName();
            var fileIds = request.Files.Select(dto => dto.Id).ToArray();
            var fileList = await deliveryFileRepository.Orm.Select<FileListA>().Where(a => fileIds.Contains(a.Id))
                .ToListAsync(cancellationToken);
            var files = await request.Files.ToAsyncEnumerable()
                .JoinAwait(fileList.ToAsyncEnumerable(), dto => new ValueTask<int>(dto.Id), a => new ValueTask<int>(a.Id), 
                async (fileInfo, a) => new DeliFiles()
                {
                    Id = fileInfo.Id,
                    ProcId = fileInfo.ProcId,
                    FileName = fileInfo.FileName,
                    FileDesc = fileInfo.FileDescription,
                    FileCode = (await fileDescriptionRepository.GetCacheValueAsync(fileInfo.FileDescriptionId))?.TextCode ?? string.Empty,
                    FileEx = Path.GetExtension(fileInfo.FileName),
                    IsShow = true,
                    BaseFileType = fileInfo.BaseFileType,
                    IsIdentity = fileInfo.IsIdentity,
                    UploadTime = DateTime.Now,
                    Uploader = currentUser ?? string.Empty,
                    Url = huaweiObsClient.GenerateTemporaryUrl(a.GetObjectName(), a.Bucket, TimeSpan.FromDays(365 * 7)).SignUrl
                }).ToArrayAsync(cancellationToken);

            await deliveryFileRepository.InsertAsync(files, cancellationToken);
        }
    }
}
