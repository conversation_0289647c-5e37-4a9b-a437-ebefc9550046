﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

/// <summary>
/// 发起递交命令
/// </summary>
/// <param name="Message">递交信息</param>
/// <param name="CurrentUserId">当前用户id</param>
/// <param name="Operation">递交操作</param>
/// <param name="MessageKey">消息键</param>
/// <param name="DisplayJsonId">预览id</param>
/// <param name="DisplayHistory">是否显示预览信息</param>
public record SendDeliveryInternalCommand(
    DeliveryMessage Message,
    string CurrentUserId, 
    string Operation, 
    string MessageKey, 
    int? DisplayJsonId, 
    bool DisplayHistory) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;