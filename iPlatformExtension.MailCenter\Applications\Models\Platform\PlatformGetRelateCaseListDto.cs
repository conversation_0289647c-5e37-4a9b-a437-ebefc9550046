﻿namespace iPlatformExtension.MailCenter.Applications.Models.Platform;

/// <summary>
/// 获取关联案件
/// </summary>
/// <param name="MailId">邮件id</param>
/// <param name="CtrlProc">任务</param>
/// <param name="MailNo">邮件编号</param>
/// <param name="MailSubject">邮件主题</param>
/// <param name="MailFrom">发件人</param>
/// <param name="MailDate">收件日期</param>
/// <param name="Status">收件状态</param>
/// <param name="CreateBy">关联人</param>
/// <param name="CreateTime">关联时间</param>
/// <param name="ReceiveType">收件类型</param>
/// <param name="SendName">发件人名称</param>
public record PlatformGetRelateCaseListDto(
    string MailId,
    string MailNo,
    string MailSubject,
    string MailFrom,
    DateTime? MailDate,
    int? Status,
    string CreateByTemp,
    DateTime? CreateTime,
    string ObjId,
    string ReceiveType,
    string? SendName)
{
    public object CtrlProc { get; set; }
    public object CreateBy { get; set; }
};

