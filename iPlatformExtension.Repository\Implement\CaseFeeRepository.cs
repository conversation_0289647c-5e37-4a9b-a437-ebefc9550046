﻿using System.Linq.Expressions;
using FreeSql;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class CaseFeeRepository : DefaultRepository<CaseFeeList, string>, ICaseFeeRepository
{
    public CaseFeeRepository(IFreeSql freeSql, UnitOfWorkManager unitOfWorkManager) : base(freeSql, unitOfWorkManager)
    {
    }
}