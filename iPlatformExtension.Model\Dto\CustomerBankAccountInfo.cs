﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 银行账户信息
/// </summary>
public class CustomerBankAccountInfo
{
    /// <summary>
    /// 账户编号
    /// </summary>
    public string AccountNumber { get; set; } = string.Empty;

    /// <summary>
    /// 银行id
    /// </summary>
    public string BankId { get; set; } = string.Empty;

    /// <summary>
    /// 客户id
    /// </summary>
    public string CustomerId { get; set; } = string.Empty;

    /// <summary>
    /// swift码
    /// </summary>
    public string SwiftCode { get; set; } = string.Empty;

    /// <summary>
    /// 收款人
    /// </summary>
    public string BeneficiaryName { get; set; } = string.Empty;

    /// <summary>
    /// 银行名称
    /// </summary>
    public string BankName { get; set; } = string.Empty;
}