﻿using AutoMapper;
using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Commission.Application.Notifications.Proc;
using iPlatformExtension.Commission.Application.Notifications.WinningReward;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class CreateRewardCommandHandler(
    IMapper mapper,
    IMediator mediator,
    IHttpContextAccessor httpContextAccessor,
    IWinningRewardProcRepository winningRewardProcRepository) 
    : IRequestHandler<CreateRewardCommand>
{
    public async Task Handle(CreateRewardCommand request, CancellationToken cancellationToken)
    {
        var (procId, dto) = request;
        var freeSql = winningRewardProcRepository.Orm;

        var rewardProc = await freeSql.Select<CaseProcInfo>(procId).WithLock()
            .ToOneAsync(info => new WinningRewardProc
            {
                ProcId = info.ProcId,
                AppNo = info.CaseInfo.AppNo,
                CaseId = info.CaseId,
                CaseName = info.CaseInfo.CaseName,
                CommissionDate = info.RewardEffectiveDate!.Value,
                CreationTime = DateTime.Now,
                Creator = UserIds.Administrator.ToSqlStringConstant(),
                CtrlProcId = info.CtrlProcId,
                CustomerId = info.CaseInfo.CustomerId,
                Month = info.RewardEffectiveDate.Value.Month,
                ProcNo = info.ProcNo,
                Pushed = false,
                RegisterNo = info.CaseInfo.RegisterNo,
                RulingResult = info.ResultRemark,
                SituationChanged = info.SituationChanged,
                UpdateTime = DateTime.Now,
                Updater = UserIds.Administrator.ToSqlStringConstant(),
                Volume = info.CaseInfo.Volume,
                Year = info.RewardEffectiveDate.Value.Year,
                CaseDirection = info.CaseInfo.CaseDirection,
                CaseType = info.CaseInfo.CaseTypeId,
            }, cancellationToken);

        var undertakerReward = await freeSql.Select<CaseProcInfo>(procId).WithLock().ToOneAsync(info =>
            new WinningRewardUser()
            {
                ProcId = info.ProcId,
                BeneficiaryType = WinningRewardBeneficiaryType.Undertaker,
                UserId = info.ProcUndertakeMainUserId ?? string.Empty
            }, cancellationToken);

        rewardProc.RewardUsers =
        [
            undertakerReward
        ];

        if (dto is not null)
        {
            if (await freeSql.Select<WinningRewardProc>()
                    .InnerJoin<CaseProcInfo>((winningRewardProc, info) => winningRewardProc.ProcNo == info.ProcNo)
                    .AnyAsync(winningRewardProc => winningRewardProc.ProcId == procId, cancellationToken))
            {
                throw new ApplicationException("已存在相同任务编号的胜诉奖励！");
            }
            
            var notification = new ProcMentorQueryNotification(procId, rewardProc.CaseType, rewardProc.CaseDirection);
            await mediator.Publish(notification, cancellationToken);
            var mentorId = notification.MentorId;
            
            if (!string.IsNullOrWhiteSpace(mentorId))
            {
                rewardProc.Beneficiaries[WinningRewardBeneficiaryType.Mentor] = new WinningRewardUser()
                {
                    BeneficiaryType = WinningRewardBeneficiaryType.Mentor,
                    UserId = mentorId,
                    ProcId = procId
                };
            }
            
            mapper.Map(dto, rewardProc);
            
            var userId = httpContextAccessor.HttpContext?.User.GetUserId() ?? string.Empty;
            rewardProc.Updater = rewardProc.Creator = userId;
            rewardProc.UpdateTime = rewardProc.CreationTime = DateTime.Now;
            
            await mediator.Publish(new RewardResultNotification(rewardProc), cancellationToken);
            await winningRewardProcRepository.InsertAsync(rewardProc, cancellationToken);
        }
        else if (await mediator.Send(new ComputeRewardCommand(rewardProc), cancellationToken))
        {
            await mediator.Publish(new RewardResultNotification(rewardProc), cancellationToken);
            await winningRewardProcRepository.InsertAsync(rewardProc, cancellationToken);
        }
    }
}