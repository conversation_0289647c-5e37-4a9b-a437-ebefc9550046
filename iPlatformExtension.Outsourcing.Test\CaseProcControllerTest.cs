﻿using System.Net;
using System.Net.Http.Json;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Infrastructure.Constants;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Outsourcing.Test;

public class CaseProcControllerTest(OutsourcingWebFactory factory) : IClassFixture<OutsourcingWebFactory>
{
    private readonly HttpClient _client = factory.CreateClient();

    private readonly JsonOptions _jsonOptions = factory.Services.GetRequiredService<IOptions<JsonOptions>>().Value;


    [Fact]
    public async Task UpdateProcSupplierAsync_ShouldReturnSuccess()
    {
        var procId = "testProcId";
        var version = 1;
        var patchDocument = new JsonPatchDocument<ProcPatchDto>();
        patchDocument.Replace(p => p.ForeignNumber, "newForeignNumber");

        var response = await _client.PatchAsync($"proc/{procId}?version={version}", JsonContent.Create(patchDocument, options: _jsonOptions.JsonSerializerOptions));

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    public async Task GetProcOutsourcingAsync_ShouldReturnSuccess()
    {
        var procId = "testProcId";

        var response = await _client.GetAsync($"proc/{procId}/outsourcing");

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        var result = await response.Content.ReadFromJsonAsync<ResultData<ProcOutsourcingDto>>(_jsonOptions.JsonSerializerOptions);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task AnyProcSupplierAsync_ShouldReturnSuccess()
    {
        var procId = "testProcId";

        var response = await _client.HeadAsync($"proc/{procId}/supplier");

        Assert.Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Theory]
    [InlineData(AuthorizationType.PatentOutsourcing, 0L, 9565L)]
    [InlineData(AuthorizationType.TrademarkOutsourcing, 666L, 1056L)]
    public async Task GetNotAssignedCountAsync_ShouldReturnSuccess(string authorizationType, long? personalCount, long? wholeCount)
    {

        var response = await _client.GetAsync($"proc/not-assigned-count?authorizationType={authorizationType}");

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        var result = await response.Content.ReadFromJsonAsync<ResultData<NotAssignedCountDto>>(_jsonOptions.JsonSerializerOptions);
        Assert.NotNull(result);
        Assert.NotNull(result.Data);
        
        Assert.Equal(personalCount, result.Data.PersonalCount);
        Assert.Equal(wholeCount, result.Data.WholeCount);
    }

    [Theory]
    [InlineData(AuthorizationType.TrademarkOutsourcing, "", 1, 10, nameof(CaseProcInfo.EntrustDate), SortOrder.Descending, 666)]
    [InlineData(AuthorizationType.TrademarkOutsourcingAll, "", 1, 10, nameof(CaseProcInfo.EntrustDate), SortOrder.Descending, 1056)]
    public async Task GetTrademarkNotAssignedProcInfosAsync_ShouldTotalEqualCount(
        string authorizationType,
        string keyword,
        int pageIndex,
        int pageSize,
        string sortField,
        SortOrder sortOrder,
        int totalCount)
    {

        var response = await _client.GetAsync($"proc/not-assigned/trademark?authorizationType={authorizationType}&keyword={keyword}&pageIndex={pageIndex}&pageSize={pageSize}&sortField={sortField}&sortOrder={sortOrder}");

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        var result = await response.Content.ReadFromJsonAsync<PaginationResult<NotAssignedTrademarkProcDto>>(_jsonOptions.JsonSerializerOptions);
        Assert.NotNull(result);
        Assert.NotEmpty(result.Data!);
        Assert.Equal(totalCount, result.Total);
    }
    
    [Theory]
    [InlineData(AuthorizationType.TrademarkOutsourcing, "ST25712968HK", 1, 10, nameof(CaseProcInfo.EntrustDate), SortOrder.Descending)]
    [InlineData(AuthorizationType.TrademarkOutsourcing, "ST25712969AE", 1, 10, nameof(CaseProcInfo.EntrustDate), SortOrder.Descending)]
    [InlineData(AuthorizationType.TrademarkOutsourcing, "ST25712966US", 1, 10, nameof(CaseProcInfo.EntrustDate), SortOrder.Descending)]
    [InlineData(AuthorizationType.TrademarkOutsourcing, "GT25712947BN", 1, 10, nameof(CaseProcInfo.EntrustDate), SortOrder.Descending)]
    [InlineData(AuthorizationType.TrademarkOutsourcing, "ST24734230US", 1, 10, nameof(CaseProcInfo.EntrustDate), SortOrder.Descending)]
    [InlineData(AuthorizationType.TrademarkOutsourcing, "GT25712957TH", 1, 10, nameof(CaseProcInfo.EntrustDate), SortOrder.Descending)]
    public async Task GetTrademarkNotAssignedProcInfosAsync_ShouldOnyReturnByVolumeKeyword(
        string authorizationType,
        string keyword,
        int pageIndex,
        int pageSize,
        string sortField,
        SortOrder sortOrder)
    {

        var response = await _client.GetAsync($"proc/not-assigned/trademark?authorizationType={authorizationType}&keyword={keyword}&pageIndex={pageIndex}&pageSize={pageSize}&sortField={sortField}&sortOrder={sortOrder}");

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        var result = await response.Content.ReadFromJsonAsync<PaginationResult<NotAssignedTrademarkProcDto>>(_jsonOptions.JsonSerializerOptions);
        Assert.NotNull(result);
        Assert.NotEmpty(result.Data!);
        Assert.True(result.Data!.All(info => info.Volume.StartsWith(keyword)));
    }

    [Fact]
    public async Task GetWholeNotAssignedProcInfosAsync_ShouldReturnSuccess()
    {
        var authorizationType = "testAuthorizationType";
        var keyword = "testKeyword";
        var pageIndex = 1;
        var pageSize = 10;
        var sortField = "EntrustDate";
        var sortOrder = SortOrder.Descending;

        var response = await _client.GetAsync($"proc/not-assigned/whole?authorizationType={authorizationType}&keyword={keyword}&pageIndex={pageIndex}&pageSize={pageSize}&sortField={sortField}&sortOrder={sortOrder}");

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        var result = await response.Content.ReadFromJsonAsync<IEnumerable<NotAssignedTrademarkProcDto>>(_jsonOptions.JsonSerializerOptions);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task UpdateProcInfosAsync_ShouldReturnSuccess()
    {
        var documents = new List<ProcJsonPatchDocument>
        {
            new (new List<Operation<ProcPatchDto>>())
            {
                ProcId = "testProcId",
                Version = 1
            }
        };

        var response = await _client.PatchAsync("proc", JsonContent.Create(documents, options: _jsonOptions.JsonSerializerOptions));

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    public async Task ExportPersonalNotAssignedProcInfosAsync_ShouldReturnSuccess()
    {
        var authorizationType = "testAuthorizationType";
        var keyword = "testKeyword";

        var response = await _client.GetAsync($"proc/not-assigned/personal/excel?authorizationType={authorizationType}&keyword={keyword}");

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        Assert.Equal("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", response.Content.Headers.ContentType?.ToString());
    }

    [Fact]
    public async Task ExportWholeNotAssignedProcInfosAsync_ShouldReturnSuccess()
    {
        var authorizationType = "testAuthorizationType";
        var keyword = "testKeyword";

        var response = await _client.GetAsync($"proc/not-assigned/whole/excel?authorizationType={authorizationType}&keyword={keyword}");

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        Assert.Equal("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", response.Content.Headers.ContentType?.ToString());
    }

    [Fact]
    public async Task GetMyProcInfoAsync_ShouldReturnSuccess()
    {
        var procId = "testProcId";

        var response = await _client.GetAsync($"proc/my-proc-info?procId={procId}");

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        var result = await response.Content.ReadFromJsonAsync<ResultData<MyProcInfoDto>>(_jsonOptions.JsonSerializerOptions);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GenerateSupplierQueryParametersAsync_ShouldReturnSuccess()
    {
        var procIds = new[] { "testProcId1", "testProcId2" };

        var response = await _client.PostAsJsonAsync("proc/supplier-query", procIds, _jsonOptions.JsonSerializerOptions);

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        var result = await response.Content.ReadFromJsonAsync<ResultData<SupplierQueryParameters>>(_jsonOptions.JsonSerializerOptions);
        Assert.NotNull(result);
    }
}
