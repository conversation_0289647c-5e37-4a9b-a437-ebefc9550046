using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_apply_info_xm_an", DisableSyncStructure = true)]
	public partial class AppApplyInfoXmAn {

		[ Column(Name = "accelerate", StringLength = 50)]
		public string Accelerate { get; set; }

		[ Column(Name = "active_case")]
		public bool ActiveCase { get; set; }

		[ Column(Name = "active_type_id", StringLength = 50)]
		public string ActiveTypeId { get; set; }

		[ Column(Name = "agency_currency_id", StringLength = 50)]
		public string AgencyCurrencyId { get; set; }

		[ Column(Name = "agency_fee_sum", DbType = "money")]
		public decimal? AgencyFeeSum { get; set; }

		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		[ Column(Name = "annual_total", StringLength = 50)]
		public string AnnualTotal { get; set; }

		[ Column(Name = "app_date")]
		public DateTime? AppDate { get; set; }

		[ Column(Name = "app_no", StringLength = 50)]
		public string AppNo { get; set; }

		[ Column(Name = "apply_channel", StringLength = 50)]
		public string ApplyChannel { get; set; }

		[ Column(Name = "apply_id", StringLength = 50, IsNullable = false)]
		public string ApplyId { get; set; }

		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "bus_type_id", StringLength = 50)]
		public string BusTypeId { get; set; }

		[ Column(Name = "case_descriptions", StringLength = 4000)]
		public string CaseDescriptions { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_emergent", StringLength = 50)]
		public string CaseEmergent { get; set; }

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "case_level_id", StringLength = 50)]
		public string CaseLevelId { get; set; }

		[ Column(Name = "case_name", StringLength = 500)]
		public string CaseName { get; set; }

		[ Column(Name = "case_name_en", StringLength = 500)]
		public string CaseNameEn { get; set; }

		[ Column(Name = "case_remark", StringLength = 4000)]
		public string CaseRemark { get; set; }

		[ Column(Name = "case_request", StringLength = 2000)]
		public string CaseRequest { get; set; }

		[ Column(Name = "case_status_id", StringLength = 50)]
		public string CaseStatusId { get; set; }

		[ Column(Name = "charge_date")]
		public DateTime? ChargeDate { get; set; }

		[ Column(Name = "chushen_pub_date")]
		public DateTime? ChushenPubDate { get; set; }

		[ Column(Name = "chushen_pub_no", StringLength = 50)]
		public string ChushenPubNo { get; set; }

		[ Column(Name = "color_detail", StringLength = 200)]
		public string ColorDetail { get; set; }

		[ Column(Name = "color_form", StringLength = 50)]
		public string ColorForm { get; set; }

		[ Column(Name = "contract_no", StringLength = 200)]
		public string ContractNo { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "ctrl_proc_mark", StringLength = 50)]
		public string CtrlProcMark { get; set; }

		[ Column(Name = "ctrl_proc_property", StringLength = 50)]
		public string CtrlProcProperty { get; set; }

		[ Column(Name = "ctrl_proc_supply", StringLength = 50)]
		public string CtrlProcSupply { get; set; }

		[ Column(Name = "cus_due_date")]
		public DateTime? CusDueDate { get; set; }

		[ Column(Name = "cus_finish_date")]
		public DateTime? CusFinishDate { get; set; }

		[ Column(Name = "cus_first_date")]
		public DateTime? CusFirstDate { get; set; }

		[ Column(Name = "customer_volume", StringLength = 50)]
		public string CustomerVolume { get; set; }

		[ Column(Name = "deliver_agency", StringLength = 50)]
		public string DeliverAgency { get; set; }

		[ Column(Name = "division_property", StringLength = 50)]
		public string DivisionProperty { get; set; }

		[ Column(Name = "fee_reduce", StringLength = 50)]
		public string FeeReduce { get; set; }

		[ Column(Name = "filing_type", StringLength = 50)]
		public string FilingType { get; set; }

		[ Column(Name = "first_agency_user", StringLength = 50)]
		public string FirstAgencyUser { get; set; }

		[ Column(Name = "first_pay_annual", StringLength = 50)]
		public string FirstPayAnnual { get; set; }

		[ Column(Name = "first_priority_date")]
		public DateTime? FirstPriorityDate { get; set; }

		[ Column(Name = "foregin_agency_id", StringLength = 50)]
		public string ForeginAgencyId { get; set; }

		[ Column(Name = "foregin_agency_remark", StringLength = 2000)]
		public string ForeginAgencyRemark { get; set; }

		[ Column(Name = "foregin_contact_id", StringLength = 50)]
		public string ForeginContactId { get; set; }

		[ Column(Name = "initial_app_date")]
		public DateTime? InitialAppDate { get; set; }

		[ Column(Name = "initial_app_no", StringLength = 50)]
		public string InitialAppNo { get; set; }

		[ Column(Name = "int_due_date")]
		public DateTime? IntDueDate { get; set; }

		[ Column(Name = "int_finish_date")]
		public DateTime? IntFinishDate { get; set; }

		[ Column(Name = "int_first_date")]
		public DateTime? IntFirstDate { get; set; }

		[ Column(Name = "invalid_code", StringLength = 50)]
		public string InvalidCode { get; set; }

		[ Column(Name = "invalid_date")]
		public DateTime? InvalidDate { get; set; }

		[ Column(Name = "invalid_holder_name", StringLength = 4000)]
		public string InvalidHolderName { get; set; }

		[ Column(Name = "invalid_request_user", StringLength = 1000)]
		public string InvalidRequestUser { get; set; }

		[ Column(Name = "is_active")]
		public bool? IsActive { get; set; }

		[ Column(Name = "is_advance_check")]
		public bool? IsAdvanceCheck { get; set; }

		[ Column(Name = "is_ahead_pub")]
		public bool? IsAheadPub { get; set; }

		[ Column(Name = "is_allocate")]
		public bool? IsAllocate { get; set; }

		[ Column(Name = "is_ca")]
		public bool? IsCa { get; set; }

		[ Column(Name = "is_cip")]
		public bool? IsCip { get; set; }

		[ Column(Name = "is_color")]
		public bool IsColor { get; set; }

		[ Column(Name = "is_division")]
		public bool? IsDivision { get; set; }

		[ Column(Name = "is_essence_exam")]
		public bool? IsEssenceExam { get; set; }

		[ Column(Name = "is_fee_config")]
		public bool? IsFeeConfig { get; set; }

		[ Column(Name = "is_fee_reduce")]
		public bool? IsFeeReduce { get; set; }

		[ Column(Name = "is_finish")]
		public bool? IsFinish { get; set; }

		[ Column(Name = "is_grace")]
		public bool IsGrace { get; set; }

		[ Column(Name = "is_pph")]
		public bool? IsPph { get; set; }

		[ Column(Name = "is_priority_review")]
		public bool? IsPriorityReview { get; set; }

		[ Column(Name = "is_request_das")]
		public bool? IsRequestDas { get; set; }

		[ Column(Name = "is_same_day")]
		public bool? IsSameDay { get; set; }

		[ Column(Name = "is_secrecy_request")]
		public bool? IsSecrecyRequest { get; set; }

		[ Column(Name = "is_stereoscopic")]
		public bool? IsStereoscopic { get; set; }

		[ Column(Name = "issue_date", StringLength = 50)]
		public string IssueDate { get; set; }

		[ Column(Name = "issue_no", StringLength = 50)]
		public string IssueNo { get; set; }

		[ Column(Name = "legal_due_date")]
		public DateTime? LegalDueDate { get; set; }

		[ Column(Name = "manage_company", StringLength = 50)]
		public string ManageCompany { get; set; }

		[ Column(Name = "manage_district", StringLength = 50)]
		public string ManageDistrict { get; set; }

		[ Column(Name = "material", StringLength = 50)]
		public string Material { get; set; }

		[ Column(Name = "member_country", StringLength = 500)]
		public string MemberCountry { get; set; }

		[ Column(Name = "multi_type", StringLength = 50)]
		public string MultiType { get; set; }

		[ Column(Name = "out_user", StringLength = 50)]
		public string OutUser { get; set; }

		[ Column(Name = "pct_app_date")]
		public DateTime? PctAppDate { get; set; }

		[ Column(Name = "pct_app_no", StringLength = 50)]
		public string PctAppNo { get; set; }

		[ Column(Name = "pct_deliver_language", StringLength = 50)]
		public string PctDeliverLanguage { get; set; }

		[ Column(Name = "pct_enter")]
		public bool PctEnter { get; set; }

		[ Column(Name = "pct_pub_date")]
		public DateTime? PctPubDate { get; set; }

		[ Column(Name = "pct_pub_language", StringLength = 50)]
		public string PctPubLanguage { get; set; }

		[ Column(Name = "pct_pub_no", StringLength = 50)]
		public string PctPubNo { get; set; }

		[ Column(Name = "pct_search_unit", StringLength = 200)]
		public string PctSearchUnit { get; set; }

		[ Column(Name = "pic_file_no", StringLength = 50)]
		public string PicFileNo { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "proc_note", StringLength = 2000)]
		public string ProcNote { get; set; }

		[ Column(Name = "project_no", StringLength = 50)]
		public string ProjectNo { get; set; }

		[ Column(Name = "register_class", StringLength = 50)]
		public string RegisterClass { get; set; }

		[ Column(Name = "register_no", StringLength = 50)]
		public string RegisterNo { get; set; }

		[ Column(Name = "register_type_code", StringLength = 100)]
		public string RegisterTypeCode { get; set; }

		[ Column(Name = "return_interface_date")]
		public DateTime? ReturnInterfaceDate { get; set; }

		[ Column(Name = "return_inventor_date")]
		public DateTime? ReturnInventorDate { get; set; }

		[ Column(Name = "review_stage", StringLength = 50)]
		public string ReviewStage { get; set; }

		[ Column(Name = "second_agency_user", StringLength = 50)]
		public string SecondAgencyUser { get; set; }

		[ Column(Name = "show_mode", StringLength = 500)]
		public string ShowMode { get; set; }

		[ Column(Name = "simple_deliver_date")]
		public DateTime? SimpleDeliverDate { get; set; }

		[ Column(Name = "subsidize")]
		public bool? Subsidize { get; set; }

		[ Column(Name = "tech_field_id", StringLength = 500)]
		public string TechFieldId { get; set; }

		[ Column(Name = "titular_write_user", StringLength = 50)]
		public string TitularWriteUser { get; set; }

		[ Column(Name = "track_user", StringLength = 50)]
		public string TrackUser { get; set; }

		[ Column(Name = "track_user_id", StringLength = 50)]
		public string TrackUserId { get; set; }

		[ Column(Name = "translate_amount", StringLength = 50)]
		public string TranslateAmount { get; set; }

		[ Column(Name = "translate_type", StringLength = 50)]
		public string TranslateType { get; set; }

		[ Column(Name = "undertake_dept_id", StringLength = 50)]
		public string UndertakeDeptId { get; set; }

		[ Column(Name = "undertake_user_id", StringLength = 50)]
		public string UndertakeUserId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "valid_date")]
		public DateTime? ValidDate { get; set; }

		[ Column(Name = "version_id", StringLength = 50)]
		public string VersionId { get; set; }

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

		[ Column(Name = "zhuce_pub_date")]
		public DateTime? ZhucePubDate { get; set; }

		[ Column(Name = "zhuce_pub_no", StringLength = 50)]
		public string ZhucePubNo { get; set; }

	}

}
