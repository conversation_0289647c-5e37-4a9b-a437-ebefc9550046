using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_search_tables", DisableSyncStructure = true)]
	public partial class SysSearchTables {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "is_main")]
		public bool? IsMain { get; set; }

		[ Column(Name = "join_sql", StringLength = 2000)]
		public string JoinSql { get; set; }

		[ Column(Name = "search_form_code", StringLength = 50)]
		public string SearchFormCode { get; set; }

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

		[ Column(Name = "table_code", StringLength = 50)]
		public string TableCode { get; set; }

		[ Column(Name = "table_name", StringLength = 50)]
		public string TableName { get; set; }

	}

}
