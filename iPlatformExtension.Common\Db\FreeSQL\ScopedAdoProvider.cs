using System.Data.Common;
using FreeSql.Internal.CommonProvider;
using FreeSql.Internal.Model;
using FreeSql.Internal.ObjectPool;

namespace iPlatformExtension.Common.Db.FreeSQL;

internal sealed class ScopedAdoProvider : AdoProvider
{
    private AdoProvider? _adoProvider;

    public ScopedAdoProvider(AdoProvider adoProvider) : base(adoProvider.DataType, adoProvider.ConnectionString, adoProvider.SlaveConnectionStrings)
    {
        _adoProvider = adoProvider;
        _util = adoProvider._util;
        MasterPool = adoProvider.MasterPool;
        SlavePools.AddRange(adoProvider.SlavePools);
    }

    private AdoProvider AdoProvider
    {
        get
        {
            ObjectDisposedException.ThrowIf(_adoProvider == null, this);
            return _adoProvider;
        }
    }

    public override void ReturnConnection(IObjectPool<DbConnection> pool, Object<DbConnection> conn, Exception ex)
    {
        AdoProvider.ReturnConnection(pool, conn, ex);
    }

    public override DbCommand CreateCommand()
    {
        return AdoProvider.CreateCommand();
    }

    public override DbParameter[] GetDbParamtersByObject(string sql, object obj)
    {
        return AdoProvider.GetDbParamtersByObject(sql, obj);
    }

    public override object AddslashesProcessParam(object param, Type mapType, ColumnInfo mapColumn)
    {
        return AdoProvider.AddslashesProcessParam(param, mapType, mapColumn);
    }

    public new void Dispose()
    {
        try
        {
            SlavePools.Clear();
            MasterPool = null;
            _util = null;
            _adoProvider = null;
        }
        finally
        {
            GC.SuppressFinalize(this);
        }
    }
    
    ~ScopedAdoProvider()
    {
        Dispose();
    }
}