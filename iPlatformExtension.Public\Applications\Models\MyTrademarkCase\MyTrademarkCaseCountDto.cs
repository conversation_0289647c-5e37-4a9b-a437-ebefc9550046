﻿namespace iPlatformExtension.Public.Applications.Models.MyTrademarkCase;

/// <summary>
/// 获取标签dto
/// </summary>
/// <param name="OutTab">出口商标标签</param>
/// <param name="PrivateTab">个人标签</param>
public record MyTrademarkCaseCountDto(List<TrademarkCaseOutCountDto> OutTab, List<TrademarkCaseCountDto> PrivateTab);

/// <summary>
/// 商标计数dto
/// </summary>
/// <param name="Title">标题</param>
/// <param name="Count">计数</param>
public record TrademarkCaseOutCountDto(string Title, int Count)
{
    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; } = Title;
    /// <summary>
    /// 计数
    /// </summary>
    public int Count { get; set; } = Count;

    /// <summary>
    /// 状态计数
    /// </summary>
    public List<TrademarkTypeCountDto>? StatusCountList { get; set; }
};
/// <summary>
/// 商标计数dto
/// </summary>
/// <param name="Title">标题</param>
/// <param name="Count">计数</param>
public record TrademarkCaseCountDto(string Title, int Count)
{
    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; } = Title;
    /// <summary>
    /// 计数
    /// </summary>
    public int Count { get; set; } = Count;
};

/// <summary>
/// 商标计数dto
/// </summary>
/// <param name="ProcStatusId">标题</param>
/// <param name="Count">计数</param>
public record TrademarkTypeCountDto(string Title, string ProcStatusId, int Count, string? ProcStatusName = null)
{
    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; } = Title;
    /// <summary>
    /// 标题
    /// </summary>
    public string ProcStatusId { get; set; } = ProcStatusId;

    public string? ProcStatusName { get; set; } = ProcStatusName;
    /// <summary>
    /// 计数
    /// </summary>
    public int Count { get; set; } = Count;

};

