﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class CustomerBankRepository(
    IFreeSql<PlatformFreeSql> freeSql, 
    IMemoryCache memoryCache, 
    IRedisCache<RedisCacheOptionsBase> redisCache,
    CacheExpirationToken<IGrouping<string, CustomerBankAccountInfo>> expirationToken)
    : BaseRepository<CusBank, string>(freeSql), ICustomerBankRepository
{
    public IMemoryCache MemoryCache { get; } = memoryCache;

    public CacheExpirationToken<IGrouping<string, CustomerBankAccountInfo>> ExpirationToken { get; } = expirationToken;

    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}