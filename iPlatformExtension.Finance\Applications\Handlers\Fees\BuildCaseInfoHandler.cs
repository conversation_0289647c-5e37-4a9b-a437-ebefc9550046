﻿using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class BuildCaseInfoHandler(IUserInfoRepository userInfoRepository)
    : IRequestHandler<BuildCaseInfoCommand>, IFeesQueryStatementBuilder
{
    private const string CaseAlias = nameof(CaseInfo);

    public async Task Handle(BuildCaseInfoCommand request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
        {
            return;
        }

        var queryDto = request.Dto;

        var salesIds = await queryDto.SalesIds.ToAsyncEnumerable()
            .SelectAwait(async username => await userInfoRepository.GetUserIdByUserNameAsync(username))
            .ToArrayAsync(cancellationToken);

        var mainUndertakerIds = await queryDto.MainUndertakerIds.ToAsyncEnumerable()
            .SelectAwait(async username => await userInfoRepository.GetUserIdByUserNameAsync(username))
            .ToArrayAsync(cancellationToken);
        
        BuildFeesQueryStatement(queryDto, request.FeesQuery, salesIds, mainUndertakerIds);
    }

    public static void BuildFeesQueryStatement(FeeQueryDto dto, ISelect<CaseFeeList> feesQuery, params object?[] args)
    {
        var saleIds = args[0] as string[] ?? [];
        var mainUndertakerIds = args[1] as string[] ?? [];

        feesQuery.InnerJoin(caseFeeList => caseFeeList.CaseProcInfo.CaseInfo.Id == caseFeeList.CaseProcInfo.CaseId)
            .WhereDynamicFilter(
                dto.CustomerIds.BuildContainsDynamicFilter(nameof(CaseInfo.CustomerId), alias: CaseAlias))
            .WhereDynamicFilter(
                dto.Volumes.BuildContainsDynamicFilter(nameof(CaseInfo.Volume), alias: CaseAlias))
            .WhereDynamicFilter(
                dto.AppNumbers.BuildContainsDynamicFilter(nameof(CaseInfo.AppNo), alias: CaseAlias))
            .WhereIfDynamicFilter(!Equals(dto.CaseTypes, CaseType.AllTypes),
                dto.CaseTypes.BuildContainsDynamicFilter(nameof(CaseInfo.CaseTypeId), alias: CaseAlias))
            .WhereDynamicFilter(
                dto.CustomerCaseNumbers.BuildContainsDynamicFilter(nameof(CaseInfo.CustomerCaseNo),
                    alias: CaseAlias))
            .WhereDynamicFilter(
                dto.ManageCompanyIds.BuildContainsDynamicFilter(nameof(CaseInfo.ManageCompany),
                    alias: CaseAlias))
            .WhereDynamicFilter(
                dto.CaseSourceCompanyIds.BuildContainsDynamicFilter(nameof(CaseInfo.BelongCompany),
                    alias: CaseAlias))
            .WhereIfDynamicFilter(dto.BusinessTypeIds.Any() && dto.CaseTypes.Any(CaseType.IsBelongPatent),
                dto.BusinessTypeIds.BuildContainsDynamicFilter(nameof(CaseInfo.BusinessTypeId), CaseAlias))
            .WhereDynamicFilter(
                dto.RegisterNos.BuildContainsDynamicFilter(nameof(CaseInfo.RegisterNo), alias: CaseAlias))
            .WhereIfDynamicFilter(saleIds.Any(), saleIds.BuildContainsDynamicFilter(
                nameof(CaseInfo.SalesUserId), alias: CaseAlias))
            .WhereIfDynamicFilter(mainUndertakerIds.Any(), mainUndertakerIds.BuildContainsDynamicFilter(
                nameof(CaseInfo.UndertakeMainUserId),
                CaseAlias))
            .WhereIfDynamicFilter(dto.ApplyTypes.Any(),
                dto.ApplyTypes.BuildContainsDynamicFilter(nameof(CaseInfo.ApplyTypeId), CaseAlias,
                    otherFilters: CaseType.Patent.BuildEqualsDynamicFilter(nameof(CaseInfo.CaseTypeId),
                        alias: CaseAlias)))
            .WhereDynamicFilter(
                dto.CaseDirections.BuildContainsDynamicFilter(nameof(CaseInfo.CaseDirection), CaseAlias))
            .WhereIfDynamicFilter(dto.ApplyTimePeriod.NeedToQuery,
                dto.ApplyTimePeriod.BuildDynamicTimePeriodFilter(nameof(CaseInfo.AppDate), CaseAlias))
            .WhereIfDynamicFilter(
                dto.EntrustTimePeriod.NeedToQuery,
                dto.EntrustTimePeriod.BuildDynamicTimePeriodFilter(nameof(CaseInfo.EntrustDate), CaseAlias))
            .WhereDynamicFilter(dto.CaseStatus.BuildContainsDynamicFilter(nameof(CaseInfo.CaseStatusId), CaseAlias));
    }
}