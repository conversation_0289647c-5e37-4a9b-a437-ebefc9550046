using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_notice_cert", DisableSyncStructure = true)]
	public partial class CaseNoticeCert {

		[ Column(Name = "cert_no", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CertNo { get; set; }

		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "cert_name", StringLength = 50)]
		public string CertName { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
