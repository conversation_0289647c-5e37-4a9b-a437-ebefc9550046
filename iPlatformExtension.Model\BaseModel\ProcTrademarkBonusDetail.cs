using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "proc_trademark_bonus_detail", DisableSyncStructure = true)]
	public partial class ProcTrademarkBonusDetail {

		[ Column(Name = "@month")]
		public int Month { get; set; }

		[ Column(Name = "@page_index")]
		public int PageIndex { get; set; }

		[ Column(Name = "@page_size")]
		public int PageSize { get; set; }

		[ Column(Name = "@send_official_date_begin")]
		public DateTime SendOfficialDateBegin { get; set; }

		[ Column(Name = "@send_official_date_end")]
		public DateTime SendOfficialDateEnd { get; set; }

		[ Column(Name = "@track_user_id", DbType = "varchar(50)", IsNullable = false)]
		public string TrackUserId { get; set; }

	}

}
