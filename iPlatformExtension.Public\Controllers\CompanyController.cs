﻿using iPlatformExtension.Public.Applications.Models.Company;
using iPlatformExtension.Public.Applications.Queries.Company;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers
{
    /// <summary>
    /// 内部机构
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class CompanyController : ControllerBase
    {
        private readonly IMediator _mediator;
        
        /// <summary>
        /// 构造函数依赖注入
        /// </summary>
        /// <param name="mediator"></param>
        public CompanyController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 搜索内部机构
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IEnumerable<SearchCompanyDto>> GetCompany([FromQuery] SearchCompanyQuery query)
        {
            return await _mediator.Send(query);
        }

        /// <summary>
        /// 搜索代理机构
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetAgency")]
        public async Task<IEnumerable<SearchAgencyDto>> GetAgency([FromQuery] SearchAgencyQuery query)
        {
            return await _mediator.Send(query);
        }
    }
}
