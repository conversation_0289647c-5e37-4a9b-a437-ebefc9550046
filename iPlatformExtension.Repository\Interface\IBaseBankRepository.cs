﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface IBaseBankRepository : 
    IBaseRepository<BasSelfBank, string>,
    IRedisCacheableRepository<string, BasSelfBank>,
    IScopeDependency,
    IStringKeyCacheableRepository<BasSelfBank>
{
    Task<BasSelfBank?> ICacheableRepository<string, BasSelfBank>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Where(bank => bank.BankId == key).WithLock().ToOneAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasSelfBank>> ICacheableRepository<string, BasSelfBank>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasSelfBank>.GenerateKey(BasSelfBank value)
    {
        return value.BankId;
    }

    string IStringKeyCacheableRepository<BasSelfBank>.GetCacheTextValue(BasSelfBank value)
    {
        return value.BankNameZhCn;
    }
}