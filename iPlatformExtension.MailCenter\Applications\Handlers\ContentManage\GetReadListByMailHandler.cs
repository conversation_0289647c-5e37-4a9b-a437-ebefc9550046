﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using System.Collections.Generic;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class GetReadListByMailHandler(IMailReaderListRepository mailReaderListRepository, IFreeSql<MailCenterFreeSql> mySql, IFreeSql<PlatformFreeSql> msSql) : IRequestHandler<GetReadListByMailQuery, PageResult<GetReadListByMailDto>>
    {
        public async Task<PageResult<GetReadListByMailDto>> Handle(GetReadListByMailQuery request, CancellationToken cancellationToken)
        {
            var lst = await mailReaderListRepository.Where(o => request.MailId == o.MailId)
                .Count(out long totalCount)
                .Take(request.PageSize).Skip(request.PageSize * (request.PageIndex - 1)).OrderBy(o=>o.CreateTime).ToListAsync(o =>
                new GetReadListByMailDto
                {
                    CreateTime = o.CreateTime,
                    Status = o.Status,
                    CreateBy = o.UpdateBy,
                    UpdateTime = o.UpdateTime,
                    UserId = o.UserId,
                    CreateByUserId = o.CreateBy

                }, cancellationToken).ConfigureAwait(false);

            var userList = await msSql.Select<SysUserInfo>().Where(u => lst.Any(o => o.UserId == u.UserId || o.CreateByUserId == u.UserId)).ToListAsync((u => new { UserName = u.CnName, UserId = u.UserId }), cancellationToken).ConfigureAwait(false);
            lst.ForEach(o =>
            {
                o.UserName = userList.FirstOrDefault(u => u.UserId == o.UserId)?.UserName;
                o.CreateBy = userList.FirstOrDefault(u => u.UserId == o.CreateByUserId)?.UserName;
            });
            return new PageResult<GetReadListByMailDto>()
            {
                Data = lst,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };
        }
    }
}
