﻿using System.Numerics;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CommonTest;

public class RedisTest
{
    private readonly IRedisCache<RedisCacheOptionsBase> _redisCache;

    private const string TestLockKey = nameof(TestLockKey);

    private const string TestEntityKey = nameof(TestEntityKey);

    private const string TestCacheKey = nameof(TestCacheKey);

    public RedisTest()
    {
        var services = new ServiceCollection().AddLogging(builder => builder.AddConsole()).AddCache()
            .ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
            {
                options.ConnectionString = "*************:6379,password=Acip_cc@54,defaultDatabase=10";
                options.Converters = new[] {new JsonRedisValueConverter()};
            }).Services
            .BuildServiceProvider();

        _redisCache = services.GetRequiredService<DefaultRedisCache>();
    }

    [Fact]
    public async Task TestLockExpirationAsync()
    {
        var lockTimespan = TimeSpan.FromSeconds(5);
        
        var lockResult1 = await _redisCache.TryLockAsync(TestLockKey, TestEntityKey, lockTimespan);
        var lockResult2 = await _redisCache.TryLockAsync(TestLockKey, TestEntityKey, lockTimespan);
        
        Assert.True(lockResult1);
        Assert.False(lockResult2);
    }

    [Fact]
    public async Task TestLockTimeExpiredAsync()
    {
        await _redisCache.RemoveCacheValueAsync(TestLockKey, TestEntityKey);
        var lockTimespan = TimeSpan.FromSeconds(5);
        
        var lockResult1 = await _redisCache.TryLockAsync(TestLockKey, TestEntityKey, lockTimespan);
        await Task.Delay(lockTimespan);
        var lockResult2 = await _redisCache.TryLockAsync(TestLockKey, TestEntityKey, lockTimespan);
        
        Assert.True(lockResult1);
        Assert.True(lockResult2);
    }

    [Theory]
    [InlineData(4671)]
    [InlineData(-3423)]
    [InlineData(342.5679)]
    [InlineData(-4232.264)]
    public async Task TestSerializeNumericAsync(double cacheValue)
    {
        await _redisCache.RemoveCacheValueAsync(TestCacheKey, TestEntityKey);

        await _redisCache.SetCacheValueAsync(TestCacheKey, TestEntityKey, cacheValue);
        var result = await _redisCache.GetCacheValueAsync<string, double>(TestCacheKey, TestEntityKey);
        
        Assert.Equal(cacheValue, result);
    }
}