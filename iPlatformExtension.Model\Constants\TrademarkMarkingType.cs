﻿namespace iPlatformExtension.Model.Constants;

/// <summary>
/// 商标标识种类
/// </summary>
public static class TrademarkMarkingType
{
    /// <summary>
    /// 声音
    /// </summary>
    public const string Sound = "sound";

    /// <summary>
    /// 数字
    /// </summary>
    public const string Numeric = "figure";

    /// <summary>
    /// 立体
    /// </summary>
    public const string Solid = "solid";

    /// <summary>
    /// 颜色组合
    /// </summary>
    public const string Color = "color";

    /// <summary>
    /// 外文
    /// </summary>
    public const string ForeignLanguage = "english";

    /// <summary>
    /// 中文
    /// </summary>
    public const string Chinese = "words";

    /// <summary>
    /// 图形
    /// </summary>
    public const string Graphics = "graph";

    /// <summary>
    /// 肖像
    /// </summary>
    public const string Portraits = "portraits";
}