using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using iPlatformExtension.Common.Clients.BladeCommon;
using iPlatformExtension.Common.Clients.HttpMessageHandlers;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Model.Dto;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting.Internal;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.Extensions;

public static class BladeCommonClientExtension
{
    public static IServiceCollection AddBladeCommonClient(this IServiceCollection services,
        string configureSectionPath = "BladeCommon")
    {
        services.AddOptions<BladeCommonClientOptions>()
            .BindConfiguration(configureSectionPath)
            .Configure(options =>
            {
                options.SerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.SerializerOptions.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
                options.SerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.SerializerOptions.Converters.Add(new DateOnlyJsonConverter());
                options.SerializerOptions.Converters.Add(new DateTimeJsonConverter());
                options.SerializerOptions.Converters.Add(new DateTimeOffsetJsonConverter());
                options.SerializerOptions.Converters.Add(new NullableDateTimeJsonConverter());
                options.SerializerOptions.Converters.Add(new NullableDateTimeOffsetConverter());
            });

        services.TryAddScoped<EnterpriseWechatNotificationApplicationCodeHandler>();
        services.TryAddScoped<W3CTraceContextHandler>();
        services.TryAddScoped<NacosServiceTokenHandler>();

        var builder = services.AddHttpClient<BladeCommonClient>()
            .AddHttpMessageHandler<NacosServiceTokenHandler>()
            .AddHttpMessageHandler<EnterpriseWechatNotificationApplicationCodeHandler>()
            .AddHttpMessageHandler<W3CTraceContextHandler>()
            .AddHttpMessageHandler(provider =>
                new HttpRequestExceptionHandler<BladeCommonClient>(provider.GetRequiredService<ILoggerFactory>(),
                    true))
            .AddPolicyHandler(PollyExtension.GetRetryPolicy());

        if (!"Local".Equals(Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"), StringComparison.CurrentCultureIgnoreCase))
        {
            builder.AddServiceDiscovery();
        }

        return services;
    }

    public static Task<BladeXResult<EnterpriseWechatNotificationResult>?> SendSimpleNotificationAsync(
        this BladeCommonClient client, EnterpriseWechatNotification notification) =>
        client.SendEnterpriseWechatNotificationAsync("/qiyewechat/sendtext", notification);

    public static Task<BladeXResult<EnterpriseWechatNotificationResult>?> SendTextCardNotificationAsync(
        this BladeCommonClient client, EnterpriseWechatNotification notification)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(notification.Url);
        ArgumentException.ThrowIfNullOrWhiteSpace(notification.ButtonText);
        return client.SendEnterpriseWechatNotificationAsync("/qiyewechat/sendtextcard", notification);
    }
    
    public static Task<BladeXResult<EnterpriseWechatNotificationResult>?> SendMarkdownNotificationAsync(
        this BladeCommonClient client, EnterpriseWechatNotification notification) =>
        client.SendEnterpriseWechatNotificationAsync("/qiyewechat/sendmarkdown", notification);
}