﻿using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;

/// <summary>
/// 商标续展下单接口
/// </summary>
public sealed class TrademarkRenewalOrder : PhoenixOrderRequestParameters
{
    /// <summary>
    /// 申请人信息
    /// </summary>
    public DeliveryApplicantInfo DeliveryApplicantInfo { get; set; } = default!;

    /// <summary>
    /// 序列化的申请人信息
    /// </summary>
    [JsonSerializationSource(nameof(DeliveryApplicantInfo))]
    public string? Applicant { get; private set; }

    /// <summary>
    /// 申请人附件信息
    /// </summary>
    public IEnumerable<ApplicantAttachment> Attachments { get; set; } = Array.Empty<ApplicantAttachment>();

    /// <summary>
    /// 序列化的申请人附件信息
    /// </summary>
    [JsonSerializationSource(nameof(Attachments))]
    public string? ApplicantAttachments { get; private set; }

    /// <summary>
    /// 商标和商品信息
    /// </summary>
    public IEnumerable<BrandInfo> BrandInfos { get; set; } = Array.Empty<BrandInfo>();

    /// <summary>
    /// 序列化的商标商品信息
    /// </summary>
    [JsonSerializationSource(nameof(BrandInfos))]
    public string? BrandTeam { get; private set; }

    /// <summary>
    /// 订单信息
    /// </summary>
    public OrderInfo OrderInfo { get; set; } = default!;

    /// <summary>
    /// 订单信息的序列化
    /// </summary>
    [JsonSerializationSource(nameof(OrderInfo))]
    public string? Order { get; private set; }

}