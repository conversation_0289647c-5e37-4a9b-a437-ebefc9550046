using iPlatformExtension.MailCenter.Applications.Models.Customer;
using iPlatformExtension.MailCenter.Applications.Queries.Customer;
using iPlatformExtension.Model.Dto;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.MailCenter.Controllers
{
    /// <summary>
    /// 客户信息控制器
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class CustomerController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mediator">中介者</param>
        public CustomerController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 查询客户信息
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>客户信息列表</returns>
        [HttpGet("GetCustomerList")]
        public async Task<PageResult<CustomerDto>> GetCustomerList([FromQuery] GetCustomerListQuery query)
        {
            return await _mediator.Send(query);
        }
    }
}
