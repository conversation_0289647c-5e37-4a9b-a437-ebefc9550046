﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "map_config", DisableSyncStructure = true)]
	public partial class MapConfig {

		[Column(Name = "map_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string MapId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[Column(Name = "create_user", StringLength = 50)]
		public string CreateUser { get; set; }

		/// <summary>
		/// 任务id
		/// </summary>
		[Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		/// <summary>
		/// 是否生效
		/// </summary>
		[Column(Name = "is_enabled", DbType = "int")]
		public int? IsEnabled { get; set; }

		/// <summary>
		/// 规则编号
		/// </summary>
		[Column(Name = "map_no")]
		public string MapNo { get; set; }

		/// <summary>
		/// 提取字符
		/// </summary>
		[Column(Name = "map_value", StringLength = 500)]
		public string MapValue { get; set; }

		/// <summary>
		/// 解析范围
		/// </summary>
		[Column(Name = "scope", StringLength = 50)]
		public string Scope { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[Column(Name = "update_user", StringLength = 50)]
		public string UpdateUser { get; set; }

	}

}
