/*----------------------------------------------------------------------------------
// Copyright 2019 Huawei Technologies Co.,Ltd.
// Licensed under the Apache License, Version 2.0 (the "License"); you may not use
// this file except in compliance with the License.  You may obtain a copy of the
// License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations under the License.
//----------------------------------------------------------------------------------*/
using System.Collections.Generic;

namespace OBS.Model
{
    /// <summary>
    /// Bucket notification configuration
    /// </summary>
    public class NotificationConfiguration
    {

        private IList<TopicConfiguration> topicConfigurations;

        private IList<FunctionGraphConfiguration> functionGraphConfigurations;

        /// <summary>
        /// List of bucket event notification settings
        /// </summary>
        /// <remarks>
        /// <para>
        /// Optional parameter
        /// </para>
        /// </remarks>
        public IList<TopicConfiguration> TopicConfigurations
        {
            get
            {
                
                return this.topicConfigurations ?? (this.topicConfigurations = new List<TopicConfiguration>());
            }
            set { this.topicConfigurations = value; }
        }


        public IList<FunctionGraphConfiguration> FunctionGraphConfigurations
        {
            get
            {

                return this.functionGraphConfigurations ?? (this.functionGraphConfigurations = new List<FunctionGraphConfiguration>());
            }
            set { this.functionGraphConfigurations = value; }
        }
    }
}
