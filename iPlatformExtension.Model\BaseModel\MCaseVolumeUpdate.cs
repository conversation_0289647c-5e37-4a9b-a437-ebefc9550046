using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "M_CaseVolume_Update", DisableSyncStructure = true)]
	public partial class MCaseVolumeUpdate {

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

	}

}
