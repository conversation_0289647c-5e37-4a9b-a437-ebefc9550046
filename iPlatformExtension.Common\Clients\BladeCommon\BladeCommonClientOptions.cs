﻿using System.Text.Json;
using Nacos.V2.Common;

namespace iPlatformExtension.Common.Clients.BladeCommon;

public class BladeCommonClientOptions
{
    public string ServiceName { get; set; } = "common-service";

    public string GroupName { get; set; } = Constants.GROUP;

    public string EnterpriseWechatServiceCode { get; set; } = "Platform";

    public string HostAddress { get; set; } = string.Empty;

    public JsonSerializerOptions SerializerOptions { get; set; } = new();
}