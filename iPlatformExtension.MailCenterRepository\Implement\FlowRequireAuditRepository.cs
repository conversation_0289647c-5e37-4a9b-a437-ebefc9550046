﻿﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;

namespace iPlatformExtension.MailCenterRepository.Implement;

/// <summary>
/// 发件必审人仓储实现
/// </summary>
internal class FlowRequireAuditRepository(
    IFreeSql<MailCenterFreeSql> fsql,
    UnitOfWorkManage<MailCenterFreeSql> manager)
    : DefaultRepository<FlowRequireAudit, string>(fsql, manager), IFlowRequireAuditRepository;
