using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_bonus_month_info_4m", DisableSyncStructure = true)]
	public partial class BillBonusMonthInfo4m {

		
		public double? 负值 { get; set; }

		[ Column(Name = "month")]
		public int? Month { get; set; }

		[ Column(Name = "name")]
		public string Name { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

		[ Column(Name = "year")]
		public int? Year { get; set; }

	}

}
