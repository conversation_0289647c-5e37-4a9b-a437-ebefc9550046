﻿using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 递交申请人信息
/// </summary>
public class ApplicantInfoSnapshot
{
    /// <summary>
    /// 申请人id
    /// </summary>
    public string ApplicantId { get; set; } = string.Empty;
    
    /// <summary>
    /// 申请人地址英文
    /// </summary>
    public string ApplicantAddressEn { get; set; } = "";

    /// <summary>
    /// 申请人地址
    /// </summary>
    public string ApplicantAddressZh { get; set; } = "";

    /// <summary>
    /// 申请人英文名
    /// </summary>
    public string ApplicantNameEn { get; set; } = "";

    /// <summary>
    /// 申请人中文名
    /// </summary>
    public string ApplicantNameZh { get; set; } = "";

    /// <summary>
    /// 申请人邮政编码
    /// </summary>
    public string ApplicantPostCode { get; set; } = "";

    /// <summary>
    /// 申请人类型
    /// </summary>
    public string ApplicantType { get; set; } = "";

    /// <summary>
    /// 书式类型
    /// </summary>
    public int BookType { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public string Country { get; set; } = "";
    
    /// <summary>
    /// 身份证号
    /// 统一社会信用代码
    /// </summary>
    public string IdentityNumber { get; set; } = String.Empty;

    /// <summary>
    /// 证件类型
    /// </summary>
    public string CertificateType { get; set; } = string.Empty;

    /// <summary>
    /// 证件号吗
    /// </summary>
    public string CertificateNumber { get; set; } = string.Empty;

    /// <summary>
    /// 身份证明原件是否为中文
    /// </summary>
    public bool IsChineseIdentity { get; set; }

    /// <summary>
    /// 递交业务类型
    /// </summary>
    public string? DeliveryBusinessType { get; set; }
}