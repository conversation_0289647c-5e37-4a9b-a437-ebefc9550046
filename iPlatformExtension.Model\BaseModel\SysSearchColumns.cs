using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_search_columns", DisableSyncStructure = true)]
	public partial class SysSearchColumns {

		/// <summary>
		/// 配置表主键ID
		/// </summary>
		[ Column(Name = "column_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ColumnId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 栏位名称
		/// </summary>
		[ Column(Name = "column_name_en_us", StringLength = 50)]
		public string ColumnNameEnUs { get; set; }

		/// <summary>
		/// 栏位名称
		/// </summary>
		[ Column(Name = "column_name_ja_jp", StringLength = 50)]
		public string ColumnNameJaJp { get; set; }

		/// <summary>
		/// 栏位名称
		/// </summary>
		[ Column(Name = "column_name_zh_cn", StringLength = 50)]
		public string ColumnNameZhCn { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		/// <summary>
		/// 数据库对应ID
		/// </summary>
		[ Column(Name = "datebase_id", StringLength = 50)]
		public string DatebaseId { get; set; }

		/// <summary>
		/// 下拉框等的数据源ID,文本框则为空,对应sys_search_initdata
		/// </summary>
		[ Column(Name = "form_data_code", StringLength = 50)]
		public string FormDataCode { get; set; }

		/// <summary>
		/// 表单元素类型 text date select等
		/// </summary>
		[ Column(Name = "form_type", StringLength = 50)]
		public string FormType { get; set; }

		[ Column(Name = "is_select_init")]
		public bool? IsSelectInit { get; set; } = false;

		/// <summary>
		/// 查询匹配方式：like = in等
		/// </summary>
		[ Column(Name = "match_mode", StringLength = 50)]
		public string MatchMode { get; set; } = "=";

		[ Column(Name = "min_width", StringLength = 50)]
		public string MinWidth { get; set; }

		[ Column(Name = "name_as", StringLength = 2000)]
		public string NameAs { get; set; }

		/// <summary>
		/// 栏位页面显示ID
		/// </summary>
		[ Column(Name = "show_id", StringLength = 50)]
		public string ShowId { get; set; }

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

		/// <summary>
		/// 栏位所在表简称 构建SQL时使用
		/// </summary>
		[ Column(Name = "table_code", StringLength = 50)]
		public string TableCode { get; set; }

		[ Column(Name = "table_join_code", StringLength = 50)]
		public string TableJoinCode { get; set; }

		/// <summary>
		/// 栏位所在表名称
		/// </summary>
		[ Column(Name = "table_name", StringLength = 50)]
		public string TableName { get; set; }

	}

}
