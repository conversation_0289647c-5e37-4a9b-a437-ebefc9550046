﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow
{
    [Serializable]
    public class FlowConfigQuery : IRequest<List<GetFlowConfigDto>>
    {
        /// <summary>
        /// 流程类型
        /// </summary>
        public string FlowType { get; set; }

        /// <summary>
        /// 案件类型
        /// </summary>
        public string FlowSubType { get; set; }



        /// <summary>
        /// 团队ID(团队流程必填)
        /// </summary>
        public string? TeamId { get; set; }


        /// <summary>
        /// 团队ID(团队流程必填)
        /// </summary>
        public string? ProcDeptId { get; set; }

        


    }
}
