using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_serial_config", DisableSyncStructure = true)]
	public partial class SysSerialConfig {

		[ Column(Name = "config_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ConfigId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "allow_null")]
		public bool? AllowNull { get; set; } = true;

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "serial_id", StringLength = 50)]
		public string SerialId { get; set; }

		[ Column(Name = "unit_id", StringLength = 50)]
		public string UnitId { get; set; }

		[ Column(Name = "value", StringLength = 10)]
		public string Value { get; set; }

	}

}
