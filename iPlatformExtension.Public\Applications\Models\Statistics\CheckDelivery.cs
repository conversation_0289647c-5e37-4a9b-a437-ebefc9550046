﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Public.Applications.Models.Statistics
{
    /// <summary>
    /// 检查递交
    /// </summary>
    public record CheckDelivery
    {

        [ExcelColumn(Name = "我方文号"), ExcelColumnIndex("A")]
        public string Volume { get; init; }

        [ExcelColumn(Name = "申请类型"), ExcelColumnIndex("B")]
        public string? ApplyTypeValue { get; set; }

        [ExcelColumn(Ignore = true)]
        public string? ApplyTypeId { get; set; }


        [ExcelColumn(Name = "任务名称"), ExcelColumnIndex("C")]
        public string? CtrlProcZhCn { get; set; }
        [ExcelColumn(Ignore = true)]
        public string? CtrlProcId { get; set; }


        [ExcelColumn(Ignore = true)]
        public string? CtrlProcProperty { get; init; }

        [ExcelColumn(Name = "任务属性"), ExcelColumnIndex("D")]
        public string? CtrlProcPropertyValue { get; set; }

        [ExcelColumn(Ignore = true)]
        public string? ProcStatusId { get; init; }

        [ExcelColumn(Name = "任务状态"), ExcelColumnIndex("E")]
        public string? ProcStatusName { get; set; }

        [ExcelColumn(Name = "任务备注"), ExcelColumnIndex("F")]
        public string? ProcNote { get; init; }

        [ExcelColumn(Ignore = true)]
        public string? UndertakeUserId { get; init; }

        [ExcelColumn(Name = "承办人"), ExcelColumnIndex("G")]
        public string? UndertakeUserName { get; set; }

        [ExcelColumn(Ignore = true)]
        public string? DeptId { get; set; }

        [ExcelColumn(Name = "承办人所在部门"), ExcelColumnIndex("H")]
        public string? DeptName { get; set; }

        [ExcelColumn(Name = "代理人处理状态"), ExcelColumnIndex("I")]
        public string? SubProcStatusValue { get; set; }


        [ExcelColumn(Name = "名义承办人"), ExcelColumnIndex("J")]
        public string? TitularWriteUserName { get; set; }

        [ExcelColumn(Ignore = true)]
        public string? TitularWriteUser { get; set; }


        [ExcelColumn(Name = "客户名称"), ExcelColumnIndex("L")]
        public string? CustomerName { get; init; }

        [ExcelColumn(Name = "管理分所"), ExcelColumnIndex("M")]
        public string? ManageCompanyValue { get; set; }

        [ExcelColumn(Ignore = true)]
        public string? ManageCompany { get; set; }

        [ExcelColumn(Name = "案源分所"), ExcelColumnIndex("N")]
        public string? BelongCompanyValue { get; set; }

        [ExcelColumn(Ignore = true)]
        public string? BelongCompany { get; set; }
    }
}
