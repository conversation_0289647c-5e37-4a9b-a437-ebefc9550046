﻿using System.Net.Http.Headers;
using System.Net.Mime;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Customer.Contract;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Xunit.Abstractions;

namespace iPlatform.Extension.Public.Test;

public class CustomerControllerTest(PublicWebApplicationFactory factory, ITestOutputHelper testOutputHelper) 
    : IClassFixture<PublicWebApplicationFactory>
{
    private readonly HttpClient _httpClient = factory.CreateClient();
    private readonly IOptions<JsonOptions> _jsonOptions = factory.Services.GetRequiredService<IOptions<JsonOptions>>();

    [Fact]
    public async Task PostCrmContractAsync_ReturnsOkResult()
    {
        // Arrange
        var jsonSerializerOptions = _jsonOptions.Value.JsonSerializerOptions;
        var crmCustomerId = "1552418413740720";
        var crmContractId = Guid.CreateVersion7().ToString();
        
        var formDataContent = new MultipartFormDataContent();
        formDataContent.Add(new StringContent("测试合同2"), "contractTitle");
        formDataContent.Add(new StringContent(DateOnly.FromDateTime(DateTime.Today).ToString("yyyy-MM-dd")), "signingDate");
        formDataContent.Add(new StringContent(DateOnly.FromDateTime(DateTime.Today).ToString("yyyy-MM-dd")), "effectiveDate");
        formDataContent.Add(new StringContent(DateOnly.FromDateTime(DateTime.Today.AddYears(1)).ToString("yyyy-MM-dd")), "endDate");
        formDataContent.Add(new StringContent("我方签约主体"), "myContractingEntity");
        formDataContent.Add(new StringContent("对方签约主体"), "customerContractingEntity");
        formDataContent.Add(new StringContent("H04788"), "operator");
        formDataContent.Add(new StringContent("businessType12__c"), "applicationType");
        formDataContent.Add(new StringContent("1"), "contractType");
        formDataContent.Add(new StringContent("北京异构智能科技有限公司"), "customerName");
        formDataContent.Add(new StringContent("5123894"), "contractNo");
        formDataContent.Add(new StringContent(crmContractId), "crmContractId");
        formDataContent.Add(new StringContent("1"), "archiveStatus");
        formDataContent.Add(new StringContent("H04788"), "signatoryUser");
        
        // 添加合同明细数据
        formDataContent.Add(new StringContent("1"), "details[0].businessType");
        formDataContent.Add(new StringContent("admin"), "details[0].preSalesUser");
        formDataContent.Add(new StringContent("测试备注1"), "details[0].remark");
        formDataContent.Add(new StringContent(DateOnly.FromDateTime(DateTime.Today).ToString("yyyy-MM-dd")), "details[0].effectiveDate");
        formDataContent.Add(new StringContent("admin"), "details[0].updater");
        formDataContent.Add(new StringContent(crmContractId), "details[0].crmContractId");

        formDataContent.Add(new StringContent("2"), "details[1].businessType");
        formDataContent.Add(new StringContent("H04788"), "details[1].preSalesUser");
        formDataContent.Add(new StringContent("测试备注2"), "details[1].remark");
        formDataContent.Add(new StringContent(DateOnly.FromDateTime(DateTime.Today).ToString("yyyy-MM-dd")), "details[1].effectiveDate");
        formDataContent.Add(new StringContent("admin"), "details[1].updater");
        formDataContent.Add(new StringContent(crmContractId), "details[1].crmContractId");
        
        // 添加多个文件
        var files = new[]
        {
            ("E:/WXWork/1688857797287188/Cache/File/2025-04/crm对接说明文档.docx", "crm对接说明文档.docx"),
            ("E:/WXWork/1688857797287188/Cache/File/2025-04/crm对接说明文档(1).docx", "crm对接说明文档(1).docx")
        };

        foreach (var (filePath, fileName) in files)
        {
            var fileStream = File.OpenRead(filePath);
            var fileContent = new StreamContent(fileStream);
            formDataContent.Add(fileContent, "files", fileName);
        }

        // Act
        var response = await _httpClient.PostAsync(
            $"customer/crm/{crmCustomerId}/contract", 
            formDataContent
        );
        var result = await response.Content.ReadFromJsonAsync<ResultData>(
            jsonSerializerOptions
        );
        testOutputHelper.WriteLine("返回结果:{0}", result?.Message ?? string.Empty);

        // Assert
        response.EnsureSuccessStatusCode();
        Assert.NotNull(result);
        Assert.True(result.Success);
    }

    [Fact]
    public async Task UpdateCrmContractAsync_ReturnsOkResult()
    {
        // Arrange
        var jsonSerializerOptions = _jsonOptions.Value.JsonSerializerOptions;
        var crmContractId = "01967f53-826b-7e8e-a5f9-1efab20993e6";
        var patchDoc = new JsonPatchDocument<CrmContractPatchDto>();
        patchDoc.Replace(x => x.ContractTitle, "更新后的合同标题");
        patchDoc.Replace(x => x.SigningDate, DateOnly.FromDateTime(DateTime.Today.AddDays(1)));
        patchDoc.Replace(dto => dto.MyContractingEntity, "华进");
        patchDoc.Replace(dto => dto.Operator, "admin");

        // Act
        var content = JsonContent.Create(patchDoc.Operations,
            new MediaTypeHeaderValue(MediaTypeNames.Application.JsonPatch), jsonSerializerOptions);
        var response = await _httpClient.PatchAsync(
            $"customer/crm-contract/{crmContractId}",
            content
        );
        var result = await response.Content.ReadFromJsonAsync<ResultData>(
            jsonSerializerOptions
        );
        testOutputHelper.WriteLine("返回结果：{0}", result?.Message ?? string.Empty);

        // Assert
        response.EnsureSuccessStatusCode();
        Assert.NotNull(result);
        Assert.True(result.Success);
    }

    [Fact]
    public async Task AddCrmContractDetailsAsync_ReturnsOkResult()
    {
        // Arrange
        var jsonSerializerOptions = _jsonOptions.Value.JsonSerializerOptions;
        var crmContractId = "01967f53-826b-7e8e-a5f9-1efab20993e6";
        var details = new List<CreateContractDetailDto>
        {
            new()
            {
                BusinessType = "测试业务",
                PreSalesUser = "H04246",
                Remark = "测试备注",
                EffectiveDate = DateOnly.FromDateTime(DateTime.Today.AddDays(3)),
                Updater = "H04246"
            }
        };

        // Act
        var response = await _httpClient.PostAsJsonAsync(
            $"customer/crm-contract/{crmContractId}/details",
            details,
            jsonSerializerOptions
        );
        var result = await response.Content.ReadFromJsonAsync<ResultData>(
            jsonSerializerOptions
        );
        testOutputHelper.WriteLine("返回结果：{0}", result?.Message);

        // Assert
        response.EnsureSuccessStatusCode();
        Assert.NotNull(result);
        Assert.True(result.Success);
    }
}