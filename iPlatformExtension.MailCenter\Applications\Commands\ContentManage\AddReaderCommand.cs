﻿using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.Model.Enum;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.ContentManage
{
    public class AddReaderCommand : IRequest, IUnitOfWorkCommandMysql
    {
        /// <summary>
        /// 添加模式:追加:Add,替换未读:Replace
        /// </summary>
        public string AddType { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string[] UserList { get; set; }

        /// <summary>
        /// 邮件ID
        /// </summary>
        public string[] MailList { get; set; }

        /// <summary>
        /// 邮件类型:receive:收件标签,send:发件标签.
        /// </summary>
        public string MailType { get; set; } = SysEnum.MailType.Receive.ToString();
    }
}
