﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File;

internal sealed class ProcApplicantCaseFile<PERSON><PERSON><PERSON><PERSON><PERSON>(IMediator mediator) : 
    IRequestHandler<ProcApplicantCaseFileQuery, IEnumerable<FileListDto>>
{
    public async Task<IEnumerable<FileListDto>> Handle(ProcApplicantCaseFileQuery request, CancellationToken cancellationToken)
    {
        var files = await mediator.Send(new ApplicantCaseFileQuery(request.ApplicantId, null, null, null, true),
            cancellationToken);

        if (request.TrademarkDeliveryBusinessType is null) 
            return files;
        
        var notification = new ProcApplicantCaseFileDescriptionQuery(request.TrademarkDeliveryBusinessType, files);
        await mediator.Publish(notification, cancellationToken);

        return notification.Files;
    }
}