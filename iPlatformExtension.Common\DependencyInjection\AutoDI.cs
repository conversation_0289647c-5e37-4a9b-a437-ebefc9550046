﻿using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace iPlatformExtension.Common.DependencyInjection
{
    /// <summary>
    /// 自动注入
    /// </summary>
    public static class AutoDependencyInjection
    {
        private static readonly TypeFilter serviceTypeFilter = (serviceType, criteria) =>
        {
            if (criteria is Type lifetimeDependency)
            {
                return lifetimeDependency.IsAssignableFrom(serviceType) && lifetimeDependency != serviceType;
            }

            return false;
        };

        public static IServiceCollection AddDataService(this IServiceCollection services)
        {
            #region 依赖注入
            //services.AddScoped<IUserService, UserService>();           
            var baseType = typeof(IDependency);
            var path = AppDomain.CurrentDomain.RelativeSearchPath ?? AppDomain.CurrentDomain.BaseDirectory;
            var getFiles = Directory.GetFiles(path, "iPlatformExtension.*.dll");
            var referencedAssemblies = getFiles.Select(Assembly.LoadFrom).ToList();

            var implementServiceTypes = referencedAssemblies
                .SelectMany(a => a.GetTypes()).Where(type => baseType.IsAssignableFrom(type) && type.IsClass).ToList();
            implementServiceTypes.ForEach(serviceType =>
            {
                var scopeDependency = typeof(IScopeDependency);
                if (scopeDependency.IsAssignableFrom(serviceType))
                {
                    services.AddScoped(serviceType);
                    var interfaceTypes = serviceType
                        .FindInterfaces(serviceTypeFilter, scopeDependency);
                    Array.ForEach(interfaceTypes, interfaceType => services.AddScoped(interfaceType, 
                        provider => provider.GetRequiredService(serviceType)));
                }
                
                var transientDependency = typeof(ITransientDependency);
                if (transientDependency.IsAssignableFrom(serviceType))
                {
                    services.AddTransient(serviceType);
                    var interfaceTypes = serviceType
                        .FindInterfaces(serviceTypeFilter, transientDependency);
                    Array.ForEach(interfaceTypes, interfaceType => services.AddTransient(interfaceType, 
                        provider => provider.GetRequiredService(serviceType)));
                }
                
                var singletonDependency = typeof(ISingletonDependency);
                if (singletonDependency.IsAssignableFrom(serviceType))
                {
                    services.AddSingleton(serviceType);
                    var interfaceTypes = serviceType
                        .FindInterfaces(serviceTypeFilter, singletonDependency);
                    Array.ForEach(interfaceTypes, interfaceType => services.AddSingleton(interfaceType, 
                        provider => provider.GetRequiredService(serviceType)));
                }
            });
            
            
            // var types = referencedAssemblies
            //     .SelectMany(a => a.GetTypes())
            //     .Where(x => x != baseType && !LifeTimes.Contains(x) && baseType.IsAssignableFrom(x)).ToList();
            // var implementTypes = types.Where(x => x.IsClass).ToList();
            // var interfaceTypes = types.Where(x => x.IsInterface).ToList();
            // foreach (var implementType in implementTypes)
            // {
            //     if (typeof(IScopeDependency).IsAssignableFrom(implementType))
            //     {
            //         var interfaceType = interfaceTypes.FirstOrDefault(x => x.IsAssignableFrom(implementType));
            //         if (interfaceType != null)
            //             services.AddScoped(interfaceType, implementType);
            //     }
            //     else if (typeof(ISingletonDependency).IsAssignableFrom(implementType))
            //     {
            //         var interfaceType = interfaceTypes.FirstOrDefault(x => x.IsAssignableFrom(implementType));
            //         if (interfaceType != null)
            //             services.AddSingleton(interfaceType, implementType);
            //     }
            //     else
            //     {
            //         var interfaceType = interfaceTypes.FirstOrDefault(x => x.IsAssignableFrom(implementType));
            //         if (interfaceType != null)
            //             services.AddTransient(interfaceType, implementType);
            //     }
            // }


            #endregion
            return services;
        }
    }
}
