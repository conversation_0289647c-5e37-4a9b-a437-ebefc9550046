﻿using System.Diagnostics;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.PipelineBehaviors.FeeUpdate;

internal sealed class LockBehavior<TRequest>(IRedisCache<RedisCacheOptionsBase> redisCache)
    : IPipelineBehavior<TRequest, IEnumerable<FeesUpdateResult>>
    where TRequest : IFeesUpdateCommand
{
    private const string FeesUpdateLockKey = nameof(FeesUpdateLockKey);

    public async Task<IEnumerable<FeesUpdateResult>> Handle(TRequest request, RequestHandlerDelegate<IEnumerable<FeesUpdateResult>> next, CancellationToken cancellationToken)
    {
        IEnumerable<FeesUpdateResult> results = Array.Empty<FeesUpdateResult>();

        if (request is not IFeesUpdateCommand command)
            return results;
        
        var feeIds = request.FeesParameters.Select(dto => dto.FeeId).ToArray();
        var lockTasks = feeIds.Select(feeId => redisCache.TryLockAsync(FeesUpdateLockKey, feeId, TimeSpan.FromMinutes(3)))
            .ToArray();

        var lockResults = await Task.WhenAll(lockTasks).WaitAsync(cancellationToken).ConfigureAwait(false);
        try
        {
            if (lockResults.All(result => result))
            {
                results = await next();
            }
            else
            {
                for (var i = 0; i < lockResults.Length; i++)
                {
                    if (!lockResults[i])
                        throw FeesUpdateException.UpdatingError(feeIds[i]);
                }
            }
        }
        finally
        {
            var releaseLockTasks = lockResults.Select((result, i) =>
                result ? redisCache.RemoveCacheValueAsync(FeesUpdateLockKey, feeIds[i]) : Task.FromResult(true)).ToArray();
            var releaseLockResults = await Task.WhenAll(releaseLockTasks).WaitAsync(cancellationToken).ConfigureAwait(false);
        
            Debug.Assert(releaseLockResults.All(result => result), "费项更新锁有部分未解锁成功");
        }

        return results;
    }
}