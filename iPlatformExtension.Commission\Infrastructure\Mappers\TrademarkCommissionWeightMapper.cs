﻿using AutoMapper;
using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Commission.Infrastructure.Mappers;

internal sealed class TrademarkCommissionWeightMapper : Profile
{
    public TrademarkCommissionWeightMapper()
    {
        CreateMap<CommissionWeightDto, DomesticTrademarkCommission>()
            .ForMember(commission => commission.BigClient,
                expression =>
                    expression.MapFrom((dto, _) => dto.IsBigClient?.GetBooleanChineseDescription() ?? string.Empty))
            .ForMember(commission => commission.CommissionDate,
                expression => expression.MapFrom(dto => dto.CommissionDate.ToDateTime(TimeOnly.MinValue)))
            .ForMember(commission => commission.Year, expression => expression.MapFrom(dto => dto.CommissionDate.Year))
            .ForMember(commission => commission.Month,
                expression => expression.MapFrom(dto => dto.CommissionDate.Month))
            .ForMember(commission => commission.PointDate, expression => expression.MapFrom(dto => DateTime.Now))
            .ForMember(commission => commission.EditedProcPoint, expression => expression.MapFrom(dto => dto.EditedProcPoint));
        
        CreateMap<CommissionWeightDto, RpForeignTrademarkBonus>()
            .ForMember(bonus => bonus.Bigclient, expression => expression.MapFrom((dto, _) => dto.IsBigClient?.GetBooleanChineseDescription() ?? string.Empty))
            .ForMember(bonus => bonus.Month, expression => expression.MapFrom(dto => dto.CommissionDate.Month))
            .ForMember(bonus => bonus.Year, expression => expression.MapFrom(dto => dto.CommissionDate.Year))
            .ForMember(bonus => bonus.ReceiveDate, expression => expression.MapFrom(dto => dto.CommissionDate.ToDateTime(TimeOnly.MinValue)))
            .ForMember(bonus => bonus.PointDate, expression => expression.MapFrom(dto => DateTime.Now))
            .ForMember(bonus => bonus.EditedProcPoint, expression => expression.MapFrom(dto => dto.EditedProcPoint));

        CreateMap<DomesticWeightPatchDto, DomesticTrademarkCommission>()
            .ForMember(commission => commission.ProcMainUndertakerId, expression => expression.MapFrom(dto => dto.UndertakerId))
            .ForMember(commission => commission.EditedProcPoint, expression => expression.MapFrom(dto => dto.EditedProcPoint));
        CreateMap<DomesticTrademarkCommission, DomesticWeightPatchDto>()
            .ForMember(dto => dto.UndertakerId, expression => expression.MapFrom(dto => dto.ProcMainUndertakerId))
            .ForMember(dto => dto.EditedProcPoint, expression => expression.MapFrom(dto => dto.EditedProcPoint));

        CreateMap<ForeignWeightPatchDto, RpForeignTrademarkBonus>()
            .ForMember(bonus => bonus.EditedProcPoint, expression => expression.MapFrom(dto => dto.EditedProcPoint));
        CreateMap<RpForeignTrademarkBonus, ForeignWeightPatchDto>()
            .ForMember(dto => dto.EditedProcPoint, expression => expression.MapFrom(dto => dto.EditedProcPoint));

        CreateMap<SuggestionProcPointConfig, SuggestionProcWeightMatchRulePatchDto>();
        CreateMap<SuggestionProcWeightMatchRulePatchDto, SuggestionProcPointConfig>();
    }
}