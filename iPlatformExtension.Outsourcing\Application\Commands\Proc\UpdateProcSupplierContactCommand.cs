﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;

namespace iPlatformExtension.Outsourcing.Application.Commands.Proc;

internal sealed record UpdateProcSupplierContactCommand(
    string ProcId,
    string ContactId,
    JsonPatchDocument<ProcContactPatchDto> Document) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

   