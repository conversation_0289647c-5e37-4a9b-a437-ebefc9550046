﻿using System.Net.Http.Json;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Common.Clients.Holiday
{
    public class HolidayClient
    {
        private readonly HttpClient _httpClient;

        public HolidayClient(HttpClient httpClient)
        {
            _httpClient = httpClient;
            _httpClient.BaseAddress = new Uri("http://timor.tech/");
        }

        public async Task<HolidayResult?> GetHoliday(string year)
        {
            return await _httpClient.GetFromJsonAsync<HolidayResult>($"api/holiday/year/{year}");
        }
    }
}
