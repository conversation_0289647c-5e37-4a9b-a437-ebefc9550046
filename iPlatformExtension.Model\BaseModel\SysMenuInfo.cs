using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_menu_info", DisableSyncStructure = true)]
	public partial class SysMenuInfo {

		[ Column(Name = "menu_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string MenuId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "class_name", StringLength = 100)]
		public string ClassName { get; set; }

		[ Column(Name = "fa", StringLength = 50)]
		public string Fa { get; set; }

		[ Column(Name = "is_business")]
		public bool IsBusiness { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_expand")]
		public bool? IsExpand { get; set; } = false;

		[ Column(Name = "is_page")]
		public bool IsPage { get; set; } = false;

		[ Column(Name = "is_process")]
		public bool? IsProcess { get; set; }

		[ Column(Name = "is_refresh")]
		public bool IsRefresh { get; set; } = false;

		[ Column(Name = "menu_auth", StringLength = 50)]
		public string MenuAuth { get; set; }

		[ Column(Name = "menu_code", StringLength = 50)]
		public string MenuCode { get; set; }

		[ Column(Name = "menu_desc", StringLength = 200)]
		public string MenuDesc { get; set; }

		[ Column(Name = "menu_url", StringLength = 500)]
		public string MenuUrl { get; set; }

		[ Column(Name = "name", StringLength = 100)]
		public string Name { get; set; }

		[ Column(Name = "name_en_us", StringLength = 100)]
		public string NameEnUs { get; set; }

		[ Column(Name = "name_ja_jp", StringLength = 100)]
		public string NameJaJp { get; set; }

		[ Column(Name = "name_zh_cn", StringLength = 100)]
		public string NameZhCn { get; set; }

		[ Column(Name = "parent_id", StringLength = 50)]
		public string ParentId { get; set; }

		[ Column(Name = "root_id", StringLength = 50)]
		public string RootId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
