﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Repository.Implement;

internal class SystemDictionaryRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    UnitOfWorkManage<PlatformFreeSql> uowManger,
    IMemoryCache memoryCache,
    IRedisCache<StackExchangeRedisCacheOptions> redisCache,
    ILogger<SystemDictionaryRepository> logger,
    CacheExpirationToken<SysDictionary> expirationToken,
    CacheExpirationToken<IGrouping<string, SysDictionary>> expirationToken1)
    : DefaultRepository<SysDictionary, string>(freeSql, uowManger), ISystemDictionaryRepository
{
    public CacheExpirationToken<SysDictionary> ExpirationToken { get; } = expirationToken;

    public IMemoryCache MemoryCache { get; } = memoryCache;

    CacheExpirationToken<IGrouping<string, SysDictionary>> ICacheableRepository<string, IGrouping<string, SysDictionary>>.ExpirationToken => expirationToken1;


    ILogger ISystemDictionaryRepository.Logger { get; } = logger;

    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}