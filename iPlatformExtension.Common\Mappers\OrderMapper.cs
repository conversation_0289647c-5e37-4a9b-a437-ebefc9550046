using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.CaseEntrust;

namespace iPlatformExtension.Common.Mappers;

/// <summary>
/// 
/// </summary>
public sealed class OrderMapper : Profile
{
    /// <summary>
    /// 
    /// </summary>
    public OrderMapper()
    {
        CreateMap<CaseInfo, CaseDetailDto>();
        CreateMap<CaseEntrustInfo, CaseEntrustResultDto>()
            .ForMember(dto => dto.Operator,
                options => options.MapFrom(info => info.UpdateUserID))
            .ForMember(dto => dto.OperationTime,
                options => options.MapFrom(info => info.UpdateTime));
        CreateMap<CasePriorityInfo, CasePriorityInfoDto>();
        CreateMap<CaseBiologyList, CaseBiologyDto>()
            .ForMember(dto => dto.BiologyIslive, 
                options => options.MapFrom(list => list.BiologyIslive ? "Y" : "N"));
        CreateMap<AppCaseDto, FeeConfigDto>()
        .ForMember(dto => dto.ctrl_proc_id,
        options => options.MapFrom(info => info.CtrlProcId))
        .ForMember(dto => dto.proc_id,
        options => options.MapFrom(info => info.ProcId))
        .ForMember(dto => dto.country_id,
        options => options.MapFrom(info => info.CountryId))
        .ForMember(dto => dto.apply_type_id,
        options => options.MapFrom(info => info.ApplyTypeId))
        .ForMember(dto => dto.case_direction,
        options => options.MapFrom(info => info.CaseDirection))
        .ForMember(dto => dto.case_id,
        options => options.MapFrom(info => info.CaseId))
        .ForMember(dto => dto.app_date,
        options => options.MapFrom(info => info.AppDate))
        .ForMember(dto => dto.case_emergent,
        options => options.MapFrom(info => info.CaseEmergent))
        .ForMember(dto => dto.fee_reduce,
        options => options.MapFrom(info => info.FeeReduce))
        .ForMember(dto => dto.first_pay_annual,
        options => options.MapFrom(info => info.FirstPayAnnual))
        .ForMember(dto => dto.is_grace,
        options => options.MapFrom(info => info.IsGrace))
        .ForMember(dto => dto.is_essence_exam,
        options => options.MapFrom(info => info.IsEssenceExam))
        .ForMember(dto => dto.division_property,
        options => options.MapFrom(info => info.DivisionProperty))
        .ForMember(dto => dto.all_applicant,
        options => options.MapFrom(info => info.IsAllocate));

        CreateMap<CaseFeeDto, ProductFeeInfo>()
            .ForMember(info => info.Id, options => options.MapFrom(dto => dto.FeeId))
            .ForMember(info => info.Currency, options => options.MapFrom(dto => dto.currencyId))
            .ForMember(info => info.FeeAmount, options => options.MapFrom(dto => dto.Amount))
            .ForMember(info => info.ProductId, options => options.MapFrom(dto => dto.orderProductId))
            .ForMember(info => info.PaymentRequestStatus, options => options.MapFrom(dto => dto.payStatus));
    }
}