using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_fee_type_official_list", DisableSyncStructure = true)]
	public partial class BasFeeTypeOfficialList {

		[ Column(Name = "apply_type", StringLength = 50, IsNullable = false)]
		public string ApplyType { get; set; }

		[ Column(Name = "list_id", StringLength = 50, IsNullable = false)]
		public string ListId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; }

		[ Column(Name = "pct_enter")]
		public bool PctEnter { get; set; } = false;

	}

}
