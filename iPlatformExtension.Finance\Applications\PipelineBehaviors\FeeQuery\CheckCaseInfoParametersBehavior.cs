﻿using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Applications.PipelineBehaviors.FeeQuery;

internal sealed class CheckCaseInfoParametersBehavior(ILoggerFactory loggerFactory)
    : CheckFeesQueryParametersBehaviorBase<BuildCaseInfoCommand>(loggerFactory)
{
    public override bool Check(FeeQueryDto dto)
    {
        var needToQueryCaseInfo = false;
        if (dto.CustomerIds.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.Volumes.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.AppNumbers.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.CaseDirections.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.CustomerCaseNumbers.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.ManageCompanyIds.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.Applicants.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.CaseSourceCompanyIds.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.BusinessTypeIds.Any() &&
                 (!dto.CaseTypes.Any() || dto.CaseTypes.Any(caseType => caseType == CaseType.Patent)))
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.RegisterNos.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.SalesIds.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.MainUndertakerIds.Any() && (!dto.CaseTypes.Any() || dto.CaseTypes.Any(CaseType.IsBelongPatent)))
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.ApplyTimePeriod.NeedToQuery)
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.EntrustTimePeriod.NeedToQuery &&
                 (!dto.CaseTypes.Any() || dto.CaseTypes.Any(CaseType.IsBelongPatent)))
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.ApplyTypes.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.CaseTypes.Any() && !Equals(dto.CaseTypes, CaseType.AllTypes))
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.BelongCompanyCodes.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.CaseStatus.Any())
        {
            needToQueryCaseInfo = true;
        }
        else if (dto.CustomerControlIdentifiers.Any())
        {
            needToQueryCaseInfo = true;
        }

        return needToQueryCaseInfo;
    }
}