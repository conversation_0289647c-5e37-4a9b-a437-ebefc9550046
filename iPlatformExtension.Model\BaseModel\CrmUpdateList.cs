using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "crm_update_list", DisableSyncStructure = true)]
	public partial class CrmUpdateList {

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "finish_time")]
		public DateTime? FinishTime { get; set; }

		[ Column(Name = "is_exec")]
		public bool IsExec { get; set; } = false;

		[ Column(Name = "key_id", StringLength = 50, IsNullable = false)]
		public string KeyId { get; set; }

		[ Column(Name = "remark", StringLength = 1000)]
		public string Remark { get; set; }

		[ Column(Name = "update_desc", StringLength = 100)]
		public string UpdateDesc { get; set; }

		[ Column(Name = "update_id", StringLength = 50, IsNullable = false)]
		public string UpdateId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "update_name", StringLength = 100)]
		public string UpdateName { get; set; }

	}

}
