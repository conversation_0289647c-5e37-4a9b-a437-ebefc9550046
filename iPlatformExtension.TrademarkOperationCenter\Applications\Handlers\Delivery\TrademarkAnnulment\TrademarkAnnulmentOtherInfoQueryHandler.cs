﻿using AutoMapper;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkAnnulment;

internal sealed class TrademarkAnnulmentOtherInfoQueryHandler(
    ISystemDictionaryRepository dictionaryRepository, IDeliveryCitedTrademarkRepository citedTrademarkRepository, IMapper mapper) 
    : OtherInfoResultNotificationHandlerBase(dictionaryRepository)
{
    protected override ValueTask<bool> MatchAsync(OtherInfoResultQuery query, CancellationToken cancellationToken)
    {
        return new ValueTask<bool>(StringComparer.OrdinalIgnoreCase.Equals(query.CtrlProcId, CtrlProcIds.TrademarkAnnulment));
    }

    protected override async Task HandleAsync(OtherInfoResultQuery query, CancellationToken cancellationToken)
    {
        var dto = query.TrademarkDeliveryDto;
        var otherInfoSnapshot = dto.OtherInfoSnapshot;

        var annulmentBasis =
            otherInfoSnapshot.AnnulmentLawBasic?.Split(';',
                StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>();
        if (annulmentBasis.Length > 0)
        {
            for (var i = 0; i < annulmentBasis.Length; i++)
            {
                var basis = annulmentBasis[i];
                annulmentBasis[i] =
                    (await _dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.AnnulmentLawProvision, basis))
                    .Value;
            }

            otherInfoSnapshot.AnnulmentLawBasic = string.Join(';', annulmentBasis);
        }

        var citedTrademarks = await citedTrademarkRepository.Where(trademark => trademark.ProcId == dto.ProcId).WithLock()
            .ToListAsync(cancellationToken);
        otherInfoSnapshot.CitedTrademarkInfos = citedTrademarks.Count > 0
            ? mapper.Map<List<CitedTrademarkInfo>>(citedTrademarks)
            : Array.Empty<CitedTrademarkInfo>();
        
        await base.HandleAsync(query, cancellationToken);
    }
}