﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Repository.Interface;

public interface IRequestObjectRepository : 
    IBaseRepository<CusRequestObject, string>, 
    IScopeDependency,
    IRedisCacheableRepository<string, CusRequestObject>
{
    Task<CusRequestObject?> ICacheableRepository<string, CusRequestObject>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Select.WithLock()
            .Where(requestObject => requestObject.RequestObjectId == key)
            .FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<CusRequestObject>> ICacheableRepository<string, CusRequestObject>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().Take(100).ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, CusRequestObject>.GenerateKey(CusRequestObject value)
    {
        return value.RequestObjectId;
    }
}