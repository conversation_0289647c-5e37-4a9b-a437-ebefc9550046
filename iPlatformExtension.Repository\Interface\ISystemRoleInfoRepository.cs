﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.Enum;

namespace iPlatformExtension.Repository.Interface
{
    public interface ISystemRoleInfoRepository :
        IBaseRepository<SysRoleInfo, string>,
        IStringKeyCacheableRepository<SysRoleInfo>,
        IScopeDependency,
        IRedisCacheableRepository<string, SysRoleInfo>
    {
        Task<SysRoleInfo?> ICacheableRepository<string, SysRoleInfo>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
        {
            return Orm.Select<SysRoleInfo>().WithLock().Where(sysRoleInfo => sysRoleInfo.RoleId == key).FirstAsync(cancellationToken)!;
        }

        async Task<IEnumerable<SysRoleInfo>> ICacheableRepository<string, SysRoleInfo>.GetValuesFromDbAsync(CancellationToken cancellationToken)
        {
            return await Orm.Select<SysRoleInfo>().WithLock().ToListAsync(cancellationToken);
        }

        string IStringKeyCacheableRepository<SysRoleInfo>.GetCacheTextValue(SysRoleInfo value)
        {
            return value.RoleName;
        }

        string ICacheableRepository<string, SysRoleInfo>.GenerateKey(SysRoleInfo value)
        {
            return value.RoleId;
        }

        /// <summary>
        /// 获取指定角色id缓存
        /// </summary>
        /// <param name="roleCode"></param>
        /// <param name="roleType"></param>
        /// <returns></returns>
        public string GetRole(SysEnum.RoleCode roleCode, SysEnum.RoleType roleType);
    }
}
