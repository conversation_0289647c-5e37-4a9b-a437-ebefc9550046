﻿using FreeSql.Internal.Model;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core.Utils;
using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;
using iPlatformExtension.Commission.Application.Queries.Trademark.Foreign;
using iPlatformExtension.Commission.Grpc;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class PushForeignCommissionWeightCommandHandler(
    IFreeSql freeSql, 
    ISender sender, 
    CommissionService.CommissionServiceClient client) : IRequestHandler<PushForeignCommissionWeightCommand>
{
    public async Task Handle(PushForeignCommissionWeightCommand request, CancellationToken cancellationToken)
    {
        var (year, month, userId, deptIds) = request;

        var defaultFilter = new DynamicFilterInfo
        {
            Field = nameof(DomesticTrademarkCommission.Pushed),
            Operator = DynamicFilterOperator.Equals,
            Value = false,
            Logic = DynamicFilterLogic.Or,
            Filters =
            [
                new DynamicFilterInfo
                {
                    Field = nameof(RpForeignTrademarkBonus.UndertakeUserId),
                    Operator = DynamicFilterOperator.Equals,
                    Value = userId
                }
            ]
        };

        if (deptIds.Any())
        {
            defaultFilter.Filters.Add(new DynamicFilterInfo
            {
                Field = nameof(RpForeignTrademarkBonus.DeptId),
                Operator = DynamicFilterOperator.Any,
                Value = deptIds
            });
        }

        var commissions = await freeSql.Select<RpForeignTrademarkBonus>().WithLock()
            .WhereDynamicFilter(defaultFilter)
            .Where(commission => commission.Year == year && commission.Month == month)
            .ToListAsync(cancellationToken);
        
        if (commissions.Count == 0)
            return;

        var context = client.PushCommissionWeights(cancellationToken: cancellationToken);
        var requestStream = context.RequestStream;
        var responseStream = context.ResponseStream;

        var responseTask =
            Task.Run(
                () => responseStream.ForEachAsync(result =>
                    sender.Send(new ForeignCommissionPushedResultCommand(result), cancellationToken)),
                cancellationToken);
        
        for (var i = 0; i < commissions.Count; i++)
        {
            var commission = commissions[i];

            var commissionWeight = new CommissionWeight
            {
                Year = commission.Year,
                Month = commission.Month,
                Volume = commission.Volume,
                ProcNo = commission.ProcNo,
                CustomerName = commission.CustomerName,
                CaseDirection = commission.CaseDirection,
                ProcName = commission.CtrlProcZhCn,
                CommissionDate =
                    Timestamp.FromDateTime(TimeZoneInfo.ConvertTimeToUtc(commission.ReceiveDate,
                        TimeZoneInfo.Local)),
                ProcId = commission.ProcId,
                CustomerId = commission.CustomerId,
                CaseType = CaseType.Trade,
                UndertakerUsername = commission.UserName,
                UndertakerName = commission.CnName,
                Weight = new UserWeight
                {
                    Username = commission.UserName,
                    CnName = commission.CnName,
                    Weight = (commission.EditedProcPoint ?? commission.ProcPoint).ToString("F4"),
                    DeptName = commission.DeptName,
                    DeptId = commission.DeptId,
                    DistrictCode = commission.DistrictCode,
                    DistrictName = commission.DistrictName
                },
                Id = commission.ProcId
            };
            
            var mentorWeight = await sender.Send(new MentorWeightQuery(commission.UndertakeUserId), cancellationToken);
            if (mentorWeight is not null)
            {
                mentorWeight.Weight = commissionWeight.Weight.Weight;
                commissionWeight.Weight = mentorWeight;
            }
            
            await requestStream.WriteAsync(commissionWeight, cancellationToken);
        }
        
        await requestStream.CompleteAsync();

        await responseTask.ConfigureAwait(false);
    }
}