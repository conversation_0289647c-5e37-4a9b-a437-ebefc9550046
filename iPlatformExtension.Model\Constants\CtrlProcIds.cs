﻿namespace iPlatformExtension.Model.Constants;

/// <summary>
/// 任务名称id
/// </summary>
public static class CtrlProcIds
{
    /// <summary>
    /// 注册申请
    /// </summary>
    public const string TrademarkRegistration = "6EE51434-9C6A-4DD7-B14A-21051F1E9B93";

    /// <summary>
    /// 商标续展申请
    /// </summary>
    public const string TrademarkRenewal = "122DB662-9DD2-4C3B-A407-85F78D50CF4A";

    /// <summary>
    /// 商标转让或移交
    /// </summary>
    public const string TrademarkTransfer = "958A5BBA-12D1-44B8-94B8-395CD5E60750";

    /// <summary>
    /// 驳回复审申请
    /// </summary>
    public const string RefuseReexamination = "5B4DDB0C-444F-445A-893F-256C8B8B91C0";

    /// <summary>
    /// 变更名义/地址
    /// </summary>
    public const string NominalChange = "3101ABB5-35BE-43D2-A148-38FE764E1648";

    /// <summary>
    /// 商标异议申请
    /// </summary>
    public const string TrademarkObjections = "F58D8969-E6DA-4B95-B3DF-D67F3ACFF44B";

    /// <summary>
    /// 撤销三年
    /// </summary>
    public const string TrademarkWithdraw3Years = "DD886CBE-D8D4-4BE0-97F7-FD521674269A";

    /// <summary>
    /// 无效宣告申请
    /// </summary>
    public const string TrademarkAnnulment = "2B2A3E4A-088E-4EE9-A090-574D309EED28";

    /// <summary>
    /// 商标许可备案
    /// </summary>
    public const string LicenseFiling = "C851541E-47F9-4C46-8F63-1CE564642E59";
}