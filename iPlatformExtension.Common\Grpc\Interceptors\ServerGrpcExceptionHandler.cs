﻿using Grpc.Core.Interceptors;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.Grpc.Interceptors;

public abstract class ServerGrpcExceptionHandler<TRequest, TResponse>(ILogger logger) 
    : GrpcExceptionHandler<TRequest, TResponse>(logger) where TRequest : class where TResponse : class
{
    protected sealed override ValueTask<TResponse> UnaryClientHandleAsync(TRequest request, Exception exception, ClientInterceptorContext<TRequest, TResponse> context)
    {
        throw new NotSupportedException();
    }
}