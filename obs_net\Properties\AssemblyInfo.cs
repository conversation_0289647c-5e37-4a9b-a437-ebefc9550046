﻿/*----------------------------------------------------------------------------------
// Copyright 2019 Huawei Technologies Co.,Ltd.
// Licensed under the Apache License, Version 2.0 (the "License"); you may not use
// this file except in compliance with the License.  You may obtain a copy of the
// License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations under the License.
//----------------------------------------------------------------------------------*/
using OBS.Internal;
using System.Reflection;
using System.Runtime.InteropServices;


[assembly: AssemblyTitle("eSDK_Storage_OBS_.NET.dll")]
[assembly: AssemblyDescription("OBS SDK for C#")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("OBS SDK for C#")]
[assembly: AssemblyCopyright("")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]


[assembly: ComVisible(false)]

[assembly: Guid("58439d74-c580-459d-ad66-69f938bd7551")]


[assembly: AssemblyVersion(Constants.ObsSdkVersion)]
[assembly: AssemblyFileVersion(Constants.ObsSdkVersion)]

