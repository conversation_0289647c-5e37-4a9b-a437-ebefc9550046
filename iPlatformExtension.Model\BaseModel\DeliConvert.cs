using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_convert", DisableSyncStructure = true)]
	public partial class DeliConvert {

		[ Column(Name = "begin_time")]
		public DateTime? BeginTime { get; set; }

		[ Column(Name = "convert_type", StringLength = 50)]
		public string ConvertType { get; set; }

		[ Column(Name = "end_time")]
		public DateTime? EndTime { get; set; }

		[ Column(Name = "input_time")]
		public DateTime? InputTime { get; set; }

		[ Column(Name = "is_template")]
		public bool? IsTemplate { get; set; } = true;

		[ Column(Name = "is_xml")]
		public bool? IsXml { get; set; } = false;

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		[ Column(Name = "status")]
		public int? Status { get; set; }

	}

}
