using iPlatformExtension.Model.MailCenter;

namespace iPlatformExtension.MailCenter.Applications.Models.Analysis
{
    /// <summary>
    /// 邮件分析
    /// </summary>
    public class MailAnalysis : MailReceive
    {
        /// <summary>
        /// 邮件用户
        /// </summary>
        public List<MailUser>? MailUsers { get; set; } = [];

        /// <summary>
        /// 邮件发送人
        /// </summary>
        public List<MailUser>? MailFroms { get; set; } = [];

        /// <summary>
        /// 邮件附件
        /// </summary>
        public List<MailAttachments>? MailAttachments { get; set; } = [];
    }
}
