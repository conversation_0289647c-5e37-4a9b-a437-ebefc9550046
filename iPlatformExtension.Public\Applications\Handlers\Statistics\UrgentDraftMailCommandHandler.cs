﻿using Grpc.Core;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Messages.Mails;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Commands.Statistics;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;
using MongoDB.Bson;
using static iPlatformExtension.Common.GrpcServices.Notification;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.Public.Applications.Handlers.Statistics
{
    /// <summary>
    /// 催稿邮件
    /// </summary>
    internal sealed class UrgentDraftMailCommandHandler(
        IFreeSql freeSql,
        NotificationClient notificationClient,
        ILogger<UrgentDraftMailCommand> logger,
        IHttpContextAccessor contextAccessor,
        IUserInfoRepository userInfoRepository
    ) : IRequestHandler<UrgentDraftMailCommand>
    {
        public async Task Handle(
            UrgentDraftMailCommand request,
            CancellationToken cancellationToken
        )
        {
            var userId = contextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            if (string.IsNullOrWhiteSpace(userId))
            {
                throw new ArgumentNullException("UserId");
            }
            var chineseKeyValueAsync = await userInfoRepository.GetChineseKeyValueAsync(userId);

            var caseProcInfos = await freeSql
                .Select<CaseProcInfo>()
                .Where(it => request.ProcId.Contains(it.ProcId))
                .ToOneAsync(
                    it => new
                    {
                        it.CaseInfo.Volume,
                        it.BasCtrlProc.CtrlProcZhCn,
                        it.ProcNote,
                        it.CaseInfo.CaseName,
                        it.CaseId,
                        it.ProcId,
                        it.IntFinishDate,
                        it.CusFinishDate,
                        it.IntFirstDate,
                        it.CusFirstDate,
                        it.LegalDueDate,
                    },
                    cancellationToken
                );

            var first = await freeSql
                .Select<MailTemplet>()
                .Where(it => it.FlowType == "UrgentDraft")
                .ToOneAsync(cancellationToken);
            var asyncDuplexStreamingCall = notificationClient.NotifyAsync();
            var readTask = Task.Factory.StartNew(
                async () =>
                {
                    await foreach (
                        var response in asyncDuplexStreamingCall.ResponseStream.ReadAllAsync()
                    )
                    {
                        if (response is null)
                            continue;

                        if (response.Success)
                        {
                            logger.LogInformation(
                                "追踪Id：{TraceId}。信息：{Message}",
                                response.TraceId,
                                response.Message
                            );
                        }
                        else
                        {
                            logger.LogError(
                                "追踪Id：{TraceId}。信息：{Message}",
                                response.TraceId,
                                response.Message
                            );
                        }
                    }
                },
                cancellationToken
            );
            foreach (var urgentDraftUser in request.UrgentUserList)
            {
                var warnTime = DateTime.Now;
                string? date = "";
                switch (request.DateType)
                {
                    case 1:
                        date = caseProcInfos.IntFirstDate?.ToString("yyyy-MM-dd");
                        break;
                    case 2:
                        date = caseProcInfos.CusFirstDate?.ToString("yyyy-MM-dd");
                        break;
                    case 3:
                        date = caseProcInfos.IntFinishDate?.ToString("yyyy-MM-dd");
                        break;
                    case 4:
                        date = caseProcInfos.CusFinishDate?.ToString("yyyy-MM-dd");
                        break;
                    case 5:
                        date = caseProcInfos.LegalDueDate?.ToString("yyyy-MM-dd");
                        break;
                }
                var sendBody = first.BodyZhCn;
                sendBody = sendBody
                    .Replace("[收件人]", urgentDraftUser.Name)
                    .Replace(
                        "[提醒语]",
                        string.IsNullOrWhiteSpace(request.Warning)
                            ? "您好！您有以下任务请及时处理，谢谢！"
                            : request.Warning
                    )
                    .Replace("[任务名称]", caseProcInfos.CtrlProcZhCn)
                    .Replace("[期限类型]", ((ReportType)request.DateType).GetDescription())
                    .Replace("[具体日期]", date ?? "")
                    .Replace("[任务备注]", caseProcInfos.ProcNote)
                    .Replace("[我方文号]", caseProcInfos.Volume)
                    .Replace("[发件人]", chineseKeyValueAsync.Value)
                    .Replace("[案件名称]", caseProcInfos.CaseName)
                    .Replace(
                        "[链接地址]",
                        first.MailAddress.Replace("@obj_id", $"{caseProcInfos.CaseId}")
                    )
                    .Replace("[提交时间]", warnTime.ToString("yyyy-MM-dd HH:mm"));
                var notificationMail = new NotificationMail
                {
                    Subject = $"【催办提醒】{caseProcInfos.Volume}-{caseProcInfos.CtrlProcZhCn}",
                    Sender = "【正式版本】系统邮箱",
                    BodyText = sendBody,
                    MessageId = Guid.NewGuid().ToString(),
                    BodyTemplate = BodyTemplate.None,
                    IsHtmlBody = true,
                };

                //notificationMail.Receivers.AddRange(new List<string>() { "ea937da8-85fe-498d-9843-17e7d616c692"
                //    , "c1597c51-781b-4682-81c7-e01dc23431ce"
                //});
                notificationMail.Receivers.AddRange(new List<string>() { urgentDraftUser.UserId });
                logger.LogInformation(
                    notificationMail.ToJson() + notificationMail.Receivers.ToJson()
                );

                await asyncDuplexStreamingCall.RequestStream.WriteAsync(
                    notificationMail,
                    cancellationToken
                );
                await freeSql
                    .Insert<UrgentDraftHistory>(
                        new UrgentDraftHistory()
                        {
                            CreateTime = warnTime,
                            DateType = request.DateType,
                            Msg = request.Warning ?? "",
                            ProcId = caseProcInfos.ProcId,
                            ReminderId = urgentDraftUser.UserId,
                            UrgentUserId = userId,
                            WarningType = 1,
                        }
                    )
                    .WithTransaction(freeSql.Ado.TransactionCurrentThread)
                    .ExecuteAffrowsAsync(cancellationToken);
            }

            await asyncDuplexStreamingCall.RequestStream.CompleteAsync();

            await readTask;
            return;
        }
    }
}
