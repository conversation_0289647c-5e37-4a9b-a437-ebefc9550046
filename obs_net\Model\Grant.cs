/*----------------------------------------------------------------------------------
// Copyright 2019 Huawei Technologies Co.,Ltd.
// Licensed under the Apache License, Version 2.0 (the "License"); you may not use
// this file except in compliance with the License.  You may obtain a copy of the
// License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations under the License.
//----------------------------------------------------------------------------------*/

namespace OBS.Model
{
    /// <summary>
    /// Authorized user/user group (grantee) and permission information
    /// </summary>
    public class Grant
    {
        

        /// <summary>
        /// Grantee (group) name
        /// </summary>
        public Grantee Grantee
        {
            get;
            set;
        }

        /// <summary>
        /// Permission information
        /// </summary>
        public PermissionEnum? Permission
        {
            get;
            set;
        }

        /// <summary>
        /// Identifier specifying whether to deliver the bucket ACL. (This is only applicable to bucket ACLs.)
        /// </summary>
        public bool Delivered
        {
            set;
            get;
        }

    }
}


