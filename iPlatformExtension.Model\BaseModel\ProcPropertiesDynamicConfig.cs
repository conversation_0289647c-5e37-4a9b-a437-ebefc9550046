﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 任务属性动态配置
/// </summary>
[Table(Name = "proc_properties_dynamic_config", DisableSyncStructure = true)]
public sealed class ProcPropertiesDynamicConfig
{

	/// <summary>
	/// 类名
	/// </summary>
	[Column(Name = "class_name", IsNullable = false)]
	public string BelongClassName { get; set; } = null!;

	/// <summary>
	/// 任务类型id
	/// </summary>
	[Column(Name = "ctrl_proc_id", IsNullable = false)]
	public string CtrlProcId { get; set; } = null!;

	/// <summary>
	/// 显示名称
	/// </summary>
	[Column(Name = "display_name", StringLength = 50, IsNullable = false)]
	public string DisplayName { get; set; } = null!;

	/// <summary>
	/// 字段名称
	/// </summary>
	[Column(Name = "field_name", IsNullable = false)]
	public string? FieldName { get; set; }

	/// <summary>
	/// 自增主键
	/// </summary>
	[Column(Name = "id", IsIdentity = true, IsPrimary = true)]
	public int Id { get; set; }

	/// <summary>
	/// 是否导航属性
	/// </summary>
	[Column(Name = "include", MapType = typeof(string), IsNullable = false)]
	public NavigationType Include { get; set; } = NavigationType.None;

	/// <summary>
	/// 键名
	/// </summary>
	[Column(Name = "key", IsNullable = false)]
	public string Key { get; set; } = null!;

	/// <summary>
	/// 属性名称
	/// </summary>
	[Column(Name = "property_name", IsNullable = false)]
	public string PropertyName { get; set; } = null!;

	/// <summary>
	/// 表名
	/// </summary>
	[Column(Name = "table_name", IsNullable = false)]
	public string TableName { get; set; } = null!;

	/// <summary>
	/// 排序
	/// </summary>
	[Column(Name = "order", IsNullable = false)]
	public int Order { get; set; } = -1;

	/// <summary>
	/// 案件流向
	/// </summary>
	[Column(Name = "case_direction")]
	public string CaseDirection { get; set; } = default!;

	/// <summary>
	/// 是否显示
	/// 默认为true
	/// </summary>
	[Column(Name = "is_show", IsNullable = false)]
	public bool IsShow { get; set; } = true;

	/// <summary>
	/// 是否键值对
	/// </summary>
	[Column(Name = "is_key_value", IsNullable = false)]
	public bool IsKeyValue { get; set; }

	/// <summary>
	/// 是否字典
	/// </summary>
	[Column(Name = "is_dictionary", IsNullable = false)]
	public bool IsDictionary { get; set; }

	/// <summary>
	/// 字典名称
	/// </summary>
	[Column(Name = "dictionary_name", IsNullable = false)]
	public string DictionaryName { get; set; } = string.Empty;

	/// <summary>
	/// 显示值的来源类型
	/// </summary>
	[Column(Name = "display_value_source_type")]
	public string DisplayValueSourceType { get; set; } = string.Empty;

	/// <summary>
	/// 是否只读。
	/// 如果是则完全不能编辑
	/// </summary>
	[Column(Name = "is_readonly", IsNullable = false)]
	public bool IsReadonly { get; set; }
}