﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.MyTrademarkCase
{
    /// <summary>
    /// 任务id获取出口商标页签
    /// </summary>
    internal sealed class ProcTrademarkTabQueryHandler(IFreeSql freeSql)
        : IRequestHandler<ProcTrademarkTabQuery, ProcTrademarkTabDto>
    {
        public async Task<ProcTrademarkTabDto> Handle(ProcTrademarkTabQuery request, CancellationToken cancellationToken)
        {
            var sysPaperTab = await freeSql.Select<SysPaperTab, SysDictionary>().WithLock()
                .LeftJoin(it => it.t1.DictionaryId == it.t2.DictionaryId)
                .WhereIf(request.CaseDirection is "OO" or "IO", it => it.t2.DictionaryName == "trademark_tab_out")
                .WhereIf(request.CaseDirection == "II", it => it.t2.DictionaryName == "trademark_tab")
                .Where(it => it.t1.CtrlProcId == request.CtrlProcId && it.t1.CaseDirection == request.CaseDirection && it.t1.IsEnable == true)
                .FirstAsync(it => new ProcTrademarkTabDto(it.t1.DictionaryId, it.t2.TextEnUs, it.t2.Value), cancellationToken);
            return sysPaperTab;
        }
    }
}

