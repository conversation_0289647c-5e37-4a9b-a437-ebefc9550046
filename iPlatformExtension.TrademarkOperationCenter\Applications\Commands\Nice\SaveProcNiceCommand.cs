﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Nice;

internal sealed record SaveProcNiceCommand(string ProcId, IEnumerable<CustomCategoryDto> CustomCategories, IEnumerable<string> GrandNumbers)
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;