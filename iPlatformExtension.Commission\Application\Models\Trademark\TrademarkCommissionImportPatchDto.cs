﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Commission.Application.Models.Trademark;

/// <summary>
/// TrademarkCommissionImportPatchDto 类用于表示从Excel导入的商标佣金数据更新模型。
/// </summary>
internal sealed class TrademarkCommissionImportPatchDto
{
    /// <summary>
    /// 任务年份。
    /// </summary>
    [ExcelColumnName("年份")]
    public int Year { get; set; }

    /// <summary>
    /// 任务月份。
    /// </summary>
    [ExcelColumnName("月份")]
    public int Month { get; set; }

    /// <summary>
    /// 任务编号。
    /// </summary>
    [ExcelColumnName("任务编号")]
    public string ProcNo { get; set; } = string.Empty;

    /// <summary>
    /// 任务标识。
    /// </summary>
    [ExcelColumnName("任务标识")]
    public string ProcMark { get; set; } = string.Empty;

    /// <summary>
    /// 任务状态。
    /// </summary>
    [ExcelColumnName("任务状态")]
    public string ProcStatus { get; set; } = string.Empty;

    /// <summary>
    /// 主承办人名称。
    /// </summary>
    [ExcelColumnName("主承办人")]
    public string MainUndertakerName { get; set; } = string.Empty;

    /// <summary>
    /// 调整后权值。
    /// </summary>
    [ExcelColumnName("调整后权值")]
    public string ProcPoint { get; set; } = string.Empty;
}
