using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_bonus_config_bigcus", DisableSyncStructure = true)]
	public partial class BasBonusConfigBigcus {

		[ Column(Name = "cus_rule_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CusRuleId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "base_date_type", StringLength = 50, IsNullable = false)]
		public string BaseDateType { get; set; }

		[ Column(Name = "bonus_amount", DbType = "money")]
		public decimal? BonusAmount { get; set; }

		[ Column(Name = "bonus_point", DbType = "money")]
		public decimal? BonusPoint { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "rule_id", StringLength = 50)]
		public string RuleId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
