using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_notice_config", DisableSyncStructure = true)]
	public partial class SysNoticeConfig {

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_status_id", StringLength = 50)]
		public string CaseStatusId { get; set; }

		[ Column(Name = "config_type", StringLength = 50)]
		public string ConfigType { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "file_code", StringLength = 50, IsNullable = false)]
		public string FileCode { get; set; }

		[ Column(Name = "official_status_id", StringLength = 50)]
		public string OfficialStatusId { get; set; }

		[ Column(Name = "orig_file_code", StringLength = 50)]
		public string OrigFileCode { get; set; }

		[ Column(Name = "send_file")]
		public bool SendFile { get; set; } = false;

		[ Column(Name = "send_mail")]
		public bool SendMail { get; set; } = false;

	}

}
