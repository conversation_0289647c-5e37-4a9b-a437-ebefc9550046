﻿using System.Configuration;
using System.Text.Json;
using FluentValidation;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Clients.Phoenix;

/// <summary>
/// 权大师递交客户端
/// </summary>
public sealed class PhoenixClient
{
    private readonly HttpClient _httpClient;

    private readonly JsonSerializerOptions _serializerOptions;

    public PhoenixClient(HttpClient httpClient, string clientName, IOptionsMonitor<PhoenixClientOptions> optionsMonitor, IValidator<PhoenixClientOptions> validator)
    {
        _httpClient = httpClient;
        ClientName = clientName;

        var options = optionsMonitor.Get(clientName);
        var validResult = validator.Validate(options);
        if (!validResult.IsValid)
        {
            throw new ConfigurationErrorsException("递交配置选项验证错误！请确认是否已添加对应的递交Key以及正确配置对应选项!");
        }

        _serializerOptions = options.SerializerOptions;
        _httpClient.BaseAddress = new Uri(options.HostAddress);
        _httpClient.Timeout = options.RequestTimeout;
    }

    public string ClientName { get; }
    
    public Task<PhoenixResponseParameters<OrderData>?> ControlTrademarkAsync<TRequest>(string uri, TRequest parameters)
        where TRequest : PhoenixOrderBaseRequestParameters
    {
        return RequestEnsureAsync<TRequest, PhoenixResponseParameters<OrderData>>(uri, parameters, _serializerOptions);
    }

    public Task<PhoenixResponseParameters<OrderData>?> CreateOrderAsync<TRequest>(string uri, TRequest parameters)
        where TRequest : PhoenixOrderRequestParameters
    {
        return RequestEnsureAsync<TRequest, PhoenixResponseParameters<OrderData>>(uri, parameters, _serializerOptions);
    }

    public Task<PhoenixResponseParameters<OrderData>?> OperateOrderAsync<TRequest>(string uri, TRequest parameters)
        where TRequest : PhoenixOrderOperationParameters
    {
        return RequestEnsureAsync<TRequest, PhoenixResponseParameters<OrderData>>(uri, parameters, _serializerOptions);
    }

    public Task<PhoenixResponseParameters<OrderData>?> CancelOrderAsync(CancelOrderParameters parameters)
    {
        return RequestEnsureAsync<CancelOrderParameters, PhoenixResponseParameters<OrderData>>(PhoenixUri.CancelOrder, parameters,
            _serializerOptions);
    }

    public Task<PhoenixResponseParameters<PhoenixOrderInfo>?> GetOrderInfoAsync(PhoenixOrderInfoParameters request)
    {
        return RequestEnsureAsync<PhoenixOrderInfoParameters, PhoenixResponseParameters<PhoenixOrderInfo>>(
            PhoenixUri.GetOrder, request, _serializerOptions);
    }

    public Task<PhoenixResponseParameters<IEnumerable<LawProvisionsResponse>>?> GetLawProvisionsAsync(
        LawProvisionsRequest request)
    {
        return RequestEnsureAsync<LawProvisionsRequest, PhoenixResponseParameters<IEnumerable<LawProvisionsResponse>>>(PhoenixUri.LawProvisions, request,
            _serializerOptions);
    }

    /// <summary>
    /// 获取尼斯分类
    /// </summary>
    /// <param name="parameters">请求参数</param>
    /// <typeparam name="TRequest">权大师请求参数类型</typeparam>
    /// <returns>尼斯分类信息</returns>
    public Task<PhoenixResponseParameters<NiceCategoryData>?> GetNiceCategoriesAsync<TRequest>(TRequest parameters)
        where TRequest : PhoenixRequestParameters
    {
        return RequestEnsureAsync<TRequest, PhoenixResponseParameters<NiceCategoryData>>(PhoenixUri.NiceCategories, parameters,
            _serializerOptions);
    }

    private async Task<TResponse?> RequestEnsureAsync<TRequest, TResponse>(string uri, TRequest request, JsonSerializerOptions serializerOptions)
    {
        var response = await _httpClient.PostJsonAsync<TRequest, TResponse>(uri, request, serializerOptions);
        if (response is PhoenixResponseParameters phoenixResponse && (phoenixResponse.Code != 9091 || phoenixResponse.SubCode != 10002))
        {
            throw new PhoenixRequestException(phoenixResponse);
        }

        return response;
    }
}