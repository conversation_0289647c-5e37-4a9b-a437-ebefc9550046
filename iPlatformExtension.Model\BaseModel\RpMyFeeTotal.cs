using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_my_fee_total", DisableSyncStructure = true)]
	public partial class RpMyFeeTotal {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "amount", DbType = "decimal(20,2)")]
		public decimal? Amount { get; set; }

		[ Column(Name = "case_type", StringLength = 100)]
		public string CaseType { get; set; }

		[ Column(Name = "cseq")]
		public int? Cseq { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "fee_class", StringLength = 100)]
		public string FeeClass { get; set; }

		[ Column(Name = "fseq")]
		public int? Fseq { get; set; }

		[ Column(Name = "refresh_time", InsertValueSql = "getdate()")]
		public DateTime? RefreshTime { get; set; }

		[ Column(Name = "statistic_type", StringLength = 50)]
		public string StatisticType { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
