using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_notice_export_config", DisableSyncStructure = true)]
	public partial class SysNoticeExportConfig {

		[ Column(Name = "apply_type_id", StringLength = 150)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "export_id", StringLength = 500)]
		public string ExportId { get; set; }

		[ Column(Name = "export_type", StringLength = 50)]
		public string ExportType { get; set; }

		[ Column(Name = "file_code", StringLength = 50)]
		public string FileCode { get; set; }

		[ Column(Name = "orig_file_code", StringLength = 50)]
		public string OrigFileCode { get; set; }

	}

}
