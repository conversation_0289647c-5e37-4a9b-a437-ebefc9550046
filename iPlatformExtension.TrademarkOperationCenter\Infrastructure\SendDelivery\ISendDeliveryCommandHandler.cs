﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;

/// <summary>
/// 递交命令处理者
/// </summary>
public interface ISendDeliveryCommandHandler
{
    /// <summary>
    /// 处理发起递交
    /// </summary>
    /// <param name="context">发送上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    Task<SendDeliveryInternalCommand?> HandleSendStartupCommandAsync(SendContext context, CancellationToken cancellationToken);

    /// <summary>
    /// 处理中止递交
    /// </summary>
    /// <param name="context">发送上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    Task<SendDeliveryInternalCommand?> HandleSendStopCommandAsync(SendContext context, CancellationToken cancellationToken);

    /// <summary>
    /// 处理撤回递交
    /// </summary>
    /// <param name="context">发送上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    Task<SendDeliveryInternalCommand?> HandleSendWithdrawCommandAsync(SendContext context, CancellationToken cancellationToken);

    /// <summary>
    /// 处理取消订单
    /// 删除订单
    /// </summary>
    /// <param name="context"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<SendDeliveryInternalCommand?> HandleSendCancelCommandAsync(SendContext context, CancellationToken cancellationToken);
}