﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Applications.Models.Fees;

/// <summary>
/// 费项查询参数
/// </summary>
public record FeeQueryDto : IPaginationParameters
{
    /// <summary>
    /// 费项id集合
    /// </summary>
    public IEnumerable<string> FeeIds { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 年费类型。
    /// 不传给null值
    /// 0：年费
    /// 1：非年费（含授权费）
    /// 2：授权费
    /// 3：非年费（不含授权费）
    /// </summary>
    public AnnualFeeType? AnnualFeeType { get; init; }

    /// <summary>
    /// 客户id
    /// case_info
    /// customer_id
    /// </summary>
    public IEnumerable<string> CustomerIds { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 结算方式
    /// case_fee_IEnumerable
    /// balance_way
    /// </summary>
    public IEnumerable<string> BalanceWays { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 币别
    /// </summary>
    public IEnumerable<string> Currencies { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 我方文号
    /// case_info
    /// volume
    /// </summary>
    public IEnumerable<string> Volumes { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 案源分所编码
    /// case_info
    /// belong_company
    /// </summary>
    public IEnumerable<string> BelongCompanyCodes { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 申请号
    /// case_info
    /// app_no
    /// </summary>
    public IEnumerable<string> AppNumbers { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 配案日
    /// </summary>
    public PeriodQueryDto AllocateDateRange { get; init; }

    /// <summary>
    /// 商标代理名义
    /// trademarkDeliveryAgencyIds
    /// </summary>
    public IEnumerable<string> TrademarkDeliveryAgencyIds { get; init; } = Array.Empty<string>();


    private readonly IEnumerable<string> _caseTypes = Array.Empty<string>();

    /// <summary>
    /// 案件类型
    /// case_info
    /// case_type_id
    /// </summary>
    public IEnumerable<string> CaseTypes
    {
        get => !_caseTypes.Any() ? CaseType.AllTypes : _caseTypes;
        init => _caseTypes = value;
    }

    /// <summary>
    /// 案件流向
    /// case_info
    /// case_direction
    /// </summary>
    public IEnumerable<string> CaseDirections { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 费项类型
    /// case_fee_IEnumerable
    /// fee_class
    /// </summary>
    public IEnumerable<string> FeeClasses { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 申请类型
    /// case_info
    /// apply_type_id
    /// </summary>
    public IEnumerable<string> ApplyTypes { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 客户文号
    /// case_info
    /// customer_case_no
    /// </summary>
    public IEnumerable<string> CustomerCaseNumbers { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 管理分所id
    /// case_info
    /// manage_company
    /// </summary>
    public IEnumerable<string> ManageCompanyIds { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 请款单号
    /// case_fee_IEnumerable
    /// bill_no
    /// </summary>
    public IEnumerable<string> BillNos { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 外所账单号
    /// case_fee_IEnumerable
    /// pay_bill_no
    /// </summary>
    public IEnumerable<string> PayBillNos { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 缴费状态
    /// case_fee_IEnumerable
    /// officer_status
    /// </summary>
    public IEnumerable<string> OfficerStatus { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 到款状态
    /// case_fee_IEnumerable
    /// receive_status
    /// </summary>
    public IEnumerable<string> ReceiverStatus { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 付款状态
    /// case_fee_IEnumerable
    /// pay_status
    /// </summary>
    public IEnumerable<string> PayStatus { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 案件内部状态
    /// </summary>
    public IEnumerable<string> CaseStatus { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 缴费名称
    /// 官方名称
    /// case_fee_IEnumerable
    /// officer_name
    /// </summary>
    public string? OfficerName { get; init; }

    /// <summary>
    /// 缴费名义
    /// case_fee_IEnumerable
    /// payment_name
    /// </summary>
    public IEnumerable<string> PaymentNames { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 申请人
    /// case_info
    /// applicant_id
    /// </summary>
    public IEnumerable<string> Applicants { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 案源分所
    /// case_info
    /// belong_company
    /// </summary>
    public IEnumerable<string> CaseSourceCompanyIds { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 费用名称
    /// case_fee_IEnumerable
    /// fee_type_name_id
    /// </summary>
    public IEnumerable<string> FeeTypeNameIds { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 任务名称
    /// case_proc_info
    /// ctrl_proc_id
    /// </summary>
    public IEnumerable<string> CtrlProcIds { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 业务类型
    /// 专利 case_info business_type_id
    /// 商标 case_proc_info bus_type_id
    /// </summary>
    public IEnumerable<string> BusinessTypeIds { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 备注
    /// case_fee_IEnumerable
    /// remark
    /// </summary>
    public string? Remark { get; init; }

    /// <summary>
    /// （任务）承办人id
    /// case_proc_info
    /// undertake_user_id
    /// </summary>
    public IEnumerable<string> UndertakerIds { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 发票号码
    /// case_fee_IEnumerable
    /// invoice_no
    /// </summary>
    public IEnumerable<string> InvoiceNos { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 注册号
    /// case_info
    /// register
    /// </summary>
    public IEnumerable<string> RegisterNos { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 案源人
    /// case_info
    /// sales_user_id
    /// </summary>
    public IEnumerable<string> SalesIds { get; init; } = Array.Empty<string>();
    
    /// <summary>
    /// （案件）主承办人
    /// case_info
    /// undertake_main_user_id
    /// case_proc_info
    /// proc_undertake_main_user_id
    /// </summary>
    public IEnumerable<string> MainUndertakerIds { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 缴费期限
    /// case_fee_IEnumerable
    /// pay_officer_legal_date
    /// </summary>
    public PeriodQueryDto PayOfficerLegalDatePeriod { get; init; }

    /// <summary>
    /// 缴费日期
    /// case_fee_IEnumerable
    /// pay_officer_date
    /// </summary>
    public PeriodQueryDto PayOfficerDatePeriod { get; init; }

    /// <summary>
    /// 到款日期
    /// case_fee_IEnumerable
    /// receive_date
    /// </summary>
    public PeriodQueryDto ReceiveDatePeriod { get; init; }
    
    /// <summary>
    /// 创建日期
    /// case_fee_IEnumerable
    /// create_time
    /// </summary>
    public PeriodQueryDto CreationTimePeriod { get; init; }

    /// <summary>
    /// 请款日期
    /// case_fee_IEnumerable
    /// pre_request_date
    /// </summary>
    public PeriodQueryDto RequestTimePeriod { get; init; }

    /// <summary>
    /// 完成日期
    /// case_proc_info
    /// finish_date
    /// </summary>
    public PeriodQueryDto CompletionTimePeriod { get; init; }

    /// <summary>
    /// 申请日
    /// case_info app_date
    /// case_proc_info proc_app_date
    /// </summary>
    public PeriodQueryDto ApplyTimePeriod { get; init; }

    /// <summary>
    /// 委案日期
    /// case_info entrust_date
    /// case_proc_info entrust_date
    /// </summary>
    public PeriodQueryDto EntrustTimePeriod { get; init; }

    /// <summary>
    /// 送官方日
    /// case_proc_info
    /// send_official_date
    /// </summary>
    public PeriodQueryDto SendOfficialDatePeriod { get; init; }

    /// <summary>
    /// 只查询成案的费项
    /// </summary>
    public bool CaseOnly { get; init; } = true;

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool? Enabled { get; init; } = true;

    /// <summary>
    /// 只查询开案的费项
    /// </summary>
    public bool ApplyCaseOnly { get; init; } = false;

    /// <summary>
    /// 查询所有案件的费项
    /// </summary>
    public bool AllCases => ApplyCaseOnly && CaseOnly;
    
    /// <summary>
    /// 是否带出代理费
    /// </summary>
    public bool AgentFees { get; set; }

    /// <summary>
    /// 排序条件<br/>
    /// 我方文号：CaseProcInfo.CaseInfo.Id<br/>
    /// 申请号：CaseProcInfo.CaseInfo.Id<br/>
    /// 客户名称：CaseProcInfo.CaseInfo.CustomerId<br/>
    /// 任务名称：CaseProcInfo.CtrlProcId<br/>
    /// 费用名称：FeeTypeNameId<br/>
    /// 缴费日期：PayOfficerDate<br/>
    /// 送官方日：CaseProcInfo.SendOfficialDate<br/>
    /// </summary>
    public string SortCondition { get; set; } = nameof(CaseFeeList.FeeId);

    /// <summary>
    /// 排序次序
    /// </summary>
    public SortOrder SortOrder { get; set; } = SortOrder.Ascending;

    /// <summary>
    /// 页码
    /// </summary>
    public int? Page { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int? PageIndex => Page;

    /// <summary>
    /// 页面大小
    /// </summary>
    public int? PageSize { get; init; }

    /// <summary>
    /// 总数
    /// </summary>
    public long? Total { get; set; }

    /// <summary>
    /// 忽略权限校验
    /// </summary>
    public bool IgnoreAuthorization { get; set; }

    /// <summary>
    /// 任务编号
    /// </summary>
    public IEnumerable<string> ProcNos { get; set; } = [];

    /// <summary>
    /// 管控标识
    /// </summary>
    public IEnumerable<string> CustomerControlIdentifiers { get; set; } = [];

    /// <summary>
    /// 缴费发文日
    /// </summary>
    public PeriodQueryDto OfficialPaymentPublicationDate { get; set; }

    /// <summary>
    /// 官费标识
    /// </summary>
    public OfficialFeeMark? OfficialFeeMark { get; set; }

    /// <summary>
    /// 缴官费通知书
    /// </summary>
    public bool? OfficialNotificationChecked { get; set; }

    /// <summary>
    /// 待支付清单核查
    /// </summary>
    public bool? PaymentListChecked { get; set; }

    /// <summary>
    /// 外所账单号
    /// </summary>
    public IEnumerable<string> ForeignBillNumbers { get; init; } = [];

    /// <summary>
    /// 银行账号
    /// 外所账号
    /// </summary>
    public IEnumerable<string> BankAccountNumbers { get; init; } = [];

    /// <summary>
    /// 外所账单付款状态
    /// </summary>
    public IEnumerable<string> ForeignBillPaymentStatus { get; init; } = [];

    /// <summary>
    /// 外所账单应付日期
    /// </summary>
    public PeriodQueryDto ForeignBillPaymentDueDate { get; set; }

    /// <summary>
    /// 外所账单申请批次名称
    /// </summary>
    public string? ForeignApplyBatchName { get; set; }

    /// <summary>
    /// 外所账单付款日期
    /// </summary>
    public PeriodQueryDto ForeignBillPaymentDate { get; set; }

    /// <summary>
    /// 外所账单案件类型
    /// </summary>
    public string? ForeignBillCaseType { get; init; }

    /// <summary>
    /// 外所账单所属分所
    /// </summary>
    public string? ForeignDistrict { get; set; }

    /// <summary>
    /// 外所账单币别
    /// </summary>
    public string? ForeignBillCurrency { get; set; }

    /// <summary>
    /// 外所账单
    /// </summary>
    public PeriodQueryDto ForeignBillReceiveDate { get; set; }

    /// <summary>
    /// 外所账单日期
    /// </summary>
    public PeriodQueryDto ForeignBillDate { get; set; }

    /// <summary>
    /// 外所账单付款批次名称
    /// </summary>
    public string? ForeignPaymentBatchName { get; set; }
}