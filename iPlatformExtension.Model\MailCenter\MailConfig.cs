﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_config", DisableSyncStructure = true)]
	public partial class MailConfig {

		[Column(Name = "config_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ConfigId { get; set; }

		/// <summary>
		/// 显示名称
		/// </summary>
		[Column(Name = "config_name", StringLength = 200)]
		public string ConfigName { get; set; }

		/// <summary>
		/// 办理意见
		/// </summary>
		[Column(Name = "config_remark", StringLength = -1)]
		public string ConfigRemark { get; set; }

		/// <summary>
		/// 分拣类型,finish:分拣到承办人,allot:移入待分拣,ignore:忽略
		/// </summary>
		[Column(Name = "config_type", StringLength = 50)]
		public string ConfigType { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[Column(Name = "create_user", StringLength = 50)]
		public string CreateUser { get; set; }

		/// <summary>
		/// 分拣人
		/// </summary>
		[Column(Name = "hand_user", StringLength = 50)]
		public string HandUser { get; set; }

		/// <summary>
		/// 处理人类型
		/// </summary>
		[Column(Name = "hand_user_type", StringLength = 50)]
		public string HandUserType { get; set; }

		/// <summary>
		/// 邮箱id
		/// </summary>
		[Column(Name = "host_id", StringLength = -1)]
		public string HostId { get; set; }

		/// <summary>
		/// 忽略人
		/// </summary>
		[Column(Name = "ignore_user", StringLength = 50)]
		public string IgnoreUser { get; set; }

		/// <summary>
		/// 是否生效
		/// </summary>
		[Column(Name = "is_enabled", DbType = "int")]
		public int? IsEnabled { get; set; }

		/// <summary>
		/// 阅读人
		/// </summary>
		[Column(Name = "read_user", StringLength = -1)]
		public string ReadUser { get; set; }

		/// <summary>
		/// 阅读人类型
		/// </summary>
		[Column(Name = "read_user_type", StringLength = -1)]
		public string ReadUserType { get; set; }

		/// <summary>
		/// 备注
		/// </summary>
		[Column(Name = "remark", StringLength = -1)]
		public string Remark { get; set; }

		/// <summary>
		/// 规则编号
		/// </summary>
		[Column(Name = "rule_number", StringLength = 50)]
		public string RuleNumber { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[Column(Name = "seq", DbType = "int")]
		public int? Seq { get; set; }

		/// <summary>
		/// 承办人
		/// </summary>
		[Column(Name = "undertake_user", StringLength = 50)]
		public string UndertakeUser { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[Column(Name = "update_user", StringLength = 50)]
		public string UpdateUser { get; set; }

	}

}
