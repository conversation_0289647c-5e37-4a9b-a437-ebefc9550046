using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Microsoft.AspNetCore.Http.Json;

namespace iPlatformExtension.Common.Clients.Phoenix;

public sealed class PhoenixClientOptions
{
    [Required]
    public string AppKey { get; set; } = default!;

    [Required]
    public string SecretKey { get; set; } = default!;
    
    public string SighMethod { get; set; } = "md5";

    public string Version { get; set; } = "1.0";

    [Required]
    public string UserName { get; set; } = default!;

    [Required]
    public string UserId { get; set; } = default!;

    [Required]
    public string Executor { get; set; } = default!;

    [Required]
    public string HostAddress { get; set; } = default!;

    public TimeSpan RequestTimeout { get; set; }

    public string SaToken { get; set; } = default!;

    public string OrganizationId { get; set; } = default!;

    public JsonSerializerOptions SerializerOptions { get; } = new (JsonSerializerDefaults.Web);
}