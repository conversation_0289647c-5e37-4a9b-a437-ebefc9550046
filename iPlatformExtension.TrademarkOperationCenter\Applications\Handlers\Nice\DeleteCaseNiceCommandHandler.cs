﻿using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Nice;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Nice;

internal sealed class DeleteCaseNiceCommandHandler(ICaseNiceCategoryRepository caseNiceCategory)
    : IRequestHandler<DeleteCaseNiceCommand>
{
    public Task Handle(DeleteCaseNiceCommand request, CancellationToken cancellationToken)
    {
        return caseNiceCategory.DeleteAsync(category => category.CaseId == request.CaseId, cancellationToken);
    }
}