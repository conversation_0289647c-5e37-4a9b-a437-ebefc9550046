using iPlatformExtension.Commission.Infrastructure.Models;
using MediatR;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;

/// <summary>
/// 国内商标提成权值创建命令
/// </summary>
public sealed record CreateDomesticCommissionWeightCommand : IRequest, IBackgroundTracingCommand
{
    /// <inheritdoc />
    public string TraceParentId { get; set; } = null!;

    /// <inheritdoc />
    public string OperationName { get; set; } = "国内商标权值固化";
}