using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_case_tech_field", DisableSyncStructure = true)]
	public partial class AppCaseTechField {

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "tech_field_id", StringLength = 50)]
		public string TechFieldId { get; set; }

	}

}
