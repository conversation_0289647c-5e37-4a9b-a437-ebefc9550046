﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 团队任务数统计
    /// </summary>
    public class TeamCountQueryHandler : IRequestHandler<TeamCountQuery, IEnumerable<TeamCountDto>>
    {
        private readonly IFreeSql _freeSql;

        /// <summary>
        /// 团队任务数统计处理者构造函数
        /// </summary>
        /// <param name="freeSql"></param>
        public TeamCountQueryHandler(IFreeSql freeSql) => _freeSql = freeSql;

        /// <summary>
        /// 处理函数
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<TeamCountDto>> Handle(TeamCountQuery request, CancellationToken cancellationToken)
        {
            return await _freeSql.Select<SysFlowActivity, CaseProcInfo, CaseInfo>()
                .LeftJoin(it => it.t1.ObjId == it.t2.ProcId)
                .LeftJoin(it => it.t2.CaseId == it.t3.Id)
                .WhereIf(request.FlowType is not null, it => it.t1.FlowType == request.FlowType)
                .WhereIf(request.CaseType is not null, it => it.t3.CaseTypeId == request.CaseType)
                .WhereIf(request.FlowStatus is not null, it => it.t1.Status == request.FlowStatus)
                .WhereIf(request.FlowSubType is not null, it => it.t1.FlowSubType == request.FlowSubType)
                .WhereIf(request.UserId is not null && request.UserId.Any(), it => request.UserId.Contains(it.t1.CurUserId))
                .GroupBy(it => it.t1.CurUserId)
                .ToListAsync(it => new TeamCountDto(it.Key, it.Count()), cancellationToken);
        }
    }
}

