﻿using iPlatformExtension.Commission.Application.Notifications.WinningReward;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class CtrlProcRuleResultHandler(IBaseCtrlProcRepository baseCtrlProcRepository) 
    : INotificationHandler<RewardRuleDisplayResultNotification>
{
    public async Task Handle(RewardRuleDisplayResultNotification notification, CancellationToken cancellationToken)
    {
        var results = notification.Results;
        foreach (var ruleDisplayDto in results)
        {
            ruleDisplayDto.ProcName = await baseCtrlProcRepository.GetTextValueAsync(ruleDisplayDto.ProcName) ?? string.Empty;
        }
    }
}