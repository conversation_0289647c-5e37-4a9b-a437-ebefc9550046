﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Repository.Interface;


public interface IFileDescriptionRepository : IBaseRepository<BasFileDesc, string>, 
    IRedisCacheableRepository<FileDescriptionKey, BasFileDesc>, 
    IRedisCacheableRepository<string, BasFileDesc>,
    IScopeDependency
{

    Task<BasFileDesc?> ICacheableRepository<FileDescriptionKey, BasFileDesc>.GetValueFromDbAsync(FileDescriptionKey key, CancellationToken cancellationToken)
    {
        return Select.Where(desc => desc.FileDescZhCn == key.FileDescription && desc.FileType!.FileTypeZhCn == key.FileType)
            .ToOneAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasFileDesc>> ICacheableRepository<FileDescriptionKey, BasFileDesc>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.Include(description => description.FileType).Limit(100)
            .OrderByDescending(desc => desc.CreateTime).ToListAsync(cancellationToken);
    }

    FileDescriptionKey ICacheableRepository<FileDescriptionKey, BasFileDesc>.GenerateKey(BasFileDesc value)
    {
        return new FileDescriptionKey(value.FileType?.FileTypeZhCn ?? string.Empty, value.FileDescZhCn);
    }

    Task<BasFileDesc?> ICacheableRepository<string, BasFileDesc>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Select.Where(desc => desc.FileDescId == key).ToOneAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasFileDesc>> ICacheableRepository<string, BasFileDesc>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.Limit(100).ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasFileDesc>.GenerateKey(BasFileDesc value)
    {
        return value.FileDescId;
    }
}