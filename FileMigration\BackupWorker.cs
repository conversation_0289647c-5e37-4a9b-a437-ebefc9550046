﻿using System.Threading.Channels;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using StackExchange.Redis;

namespace FileMigration;

public sealed class BackupWorker : BackgroundService
{
    private readonly Worker _worker;

    public const string BackupKey = nameof(BackupKey);
    
    private readonly DefaultRedisCache _defaultRedisCache;

    private readonly Channel<int> _messageQueue;

    private readonly IFreeSql _freeSql;
    
    public BackupWorker(DefaultRedisCache defaultRedisCache, Channel<int> messageQueue, Worker worker, IFreeSql freeSql)
    {
        _defaultRedisCache = defaultRedisCache;
        _messageQueue = messageQueue;
        _worker = worker;
        _freeSql = freeSql;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var database = await _defaultRedisCache.CurrentDatabaseAsync;
        var entry = await database.SortedSetPopAsync(BackupKey);

        while (entry.HasValue)
        {
            _messageQueue.Writer.TryWrite((int) entry.Value.Element);
            entry = await database.SortedSetPopAsync(BackupKey);
        }


        while (stoppingToken.CanBeCanceled)
        {
            var reader = _messageQueue.Reader;
            while (await reader.WaitToReadAsync(stoppingToken))
            {
                if (!reader.TryRead(out var fileId)) continue;
                
                var fileInfo = await _freeSql.Select<FileListA>().WithLock().Where(a => a.Id == fileId)
                    .FirstAsync(stoppingToken);

                if (await _worker.UploadFileAsync(fileInfo, stoppingToken) < 0)
                {
                    await database.SortedSetAddAsync(BackupKey, fileId, fileId, When.NotExists);
                }
                else
                {
                    await database.SortedSetRemoveAsync(BackupKey, fileId);
                }
            }
        }
    }
}