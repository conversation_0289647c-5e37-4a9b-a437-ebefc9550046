﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Flow;
using iPlatformExtension.Public.Applications.Queries.Flow;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Flow
{
    /// <summary>
    /// 
    /// </summary>
    internal sealed class UrgentDraftHistoryQueryHandler(IFreeSql freeSql, IUserInfoRepository userInfoRepository)
        : IRequestHandler<UrgentDraftHistoryQuery, IEnumerable<UrgentDraftHistoryDto>>
    {
        private readonly IFreeSql _freeSql = freeSql;

        public async Task<IEnumerable<UrgentDraftHistoryDto>> Handle(UrgentDraftHistoryQuery request, CancellationToken cancellationToken)
        {
            var urgentDraftHistoryList = await freeSql.Select<UrgentDraftHistory>()
                .Where(it => it.ProcId == request.ProcId && it.DateType == request.DateType)
                .ToListAsync(it => new UrgentDraftHistoryDto(it.UrgentUserId, it.ReminderId, it.CreateTime, it.DateType), cancellationToken);
            return await urgentDraftHistoryList.ToAsyncEnumerable().SelectAwait(async urgent =>
                        {
                            if (!string.IsNullOrWhiteSpace(urgent.ReminderId))
                            {
                                var reminder = await userInfoRepository.GetTextValueAsync(urgent.ReminderId); ;
                                urgent.Reminder = reminder;
                            }
                            if (!string.IsNullOrWhiteSpace(urgent.UrgentUserId))
                            {
                                var urgentUserName = await userInfoRepository.GetTextValueAsync(urgent.UrgentUserId); ;
                                urgent.UrgentUserName = urgentUserName;
                            }
                            return urgent;
                        }).ToListAsync(cancellationToken: cancellationToken);

        }
    }
}

