﻿namespace iPlatformExtension.Model.Attributes;

/// <summary>
/// 键值对特性
/// </summary>
[AttributeUsage(AttributeTargets.Property)]
public sealed class KeyValueDisplayAttribute : Attribute
{
    /// <summary>
    /// 显示值得来源类型
    /// </summary>
    public Type? DisplayValueSourceType { get; set; }
    
    /// <summary>
    /// 键值对是否来源于字典
    /// </summary>
    public bool IsSystemDictionary { get; set; } = false;

    /// <summary>
    /// 字典名称
    /// </summary>
    public string DictionaryName { get; set; } = string.Empty;
}