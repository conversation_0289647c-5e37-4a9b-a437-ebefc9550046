﻿using iPlatformExtension.Commission.Application.Notifications.WinningReward;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class UserInfoRewardResultHandler(
    ICompanyRepository companyRepository,
    IUserInfoRepository userInfoRepository,
    IDistrictRepository districtRepository,
    IDepartmentInfoRepository departmentInfoRepository
    ) : INotificationHandler<RewardResultNotification>
{
    public async Task Handle(RewardResultNotification notification, CancellationToken cancellationToken)
    {
        var proc = notification.RewardProc;
        var beneficiaries = proc.Beneficiaries;
        ICacheableRepository<string, SysUserInfo> userCacheableRepository = userInfoRepository;
        
        foreach (var (_, winningRewardUser) in beneficiaries)
        {
            var userInfo = await userCacheableRepository.GetCacheValueAsync(winningRewardUser.UserId, mustExist:true, cancellationToken:cancellationToken);
            winningRewardUser.UserId = userInfo!.UserId;
            winningRewardUser.UserName = userInfo.UserName;
            winningRewardUser.CnName = userInfo.CnName;
            winningRewardUser.DeptId = userInfo.DeptId;

            var companyInfo = await companyRepository.GetCacheValueAsync(userInfo.ManageCompany ?? string.Empty,
                cancellationToken: cancellationToken);
            var departmentInfo = await departmentInfoRepository.GetCacheValueAsync(userInfo.DeptId,
                cancellationToken: cancellationToken);
            winningRewardUser.DeptName = departmentInfo?.FullName ?? string.Empty;
            winningRewardUser.DistrictCode = companyInfo?.DistrictId ?? string.Empty;
            winningRewardUser.DistrictName = await districtRepository.GetTextValueAsync(winningRewardUser.DistrictCode) ?? string.Empty;
        }
    }
}