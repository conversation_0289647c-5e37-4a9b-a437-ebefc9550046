﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using MailHost = iPlatformExtension.Model.MailCenter.MailHost;

namespace iPlatformExtension.MailCenterRepository.Interface;

public interface IMailHostRepository : IBaseRepository<MailHost, string>,
    IScopeDependency,
    IStringKeyCacheableRepository<MailHost>,
    IRedisCacheableRepository<string, MailHost>
{
    Task<MailHost?> ICacheableRepository<string, MailHost>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Orm.Select<MailHost>().WithLock().Where(mailHost => mailHost.HostId == key).FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<MailHost>> ICacheableRepository<string, MailHost>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Orm.Select<MailHost>().WithLock().ToListAsync(cancellationToken);
    }

    string IStringKeyCacheableRepository<MailHost>.GetCacheTextValue(MailHost value)
    {
        return value.ShowName;
    }

    string ICacheableRepository<string, MailHost>.GenerateKey(MailHost value)
    {
        return value.HostId;
    }
}