﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;
using iPlatformExtension.Commission.Infrastructure.Logging;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class ForeignCommissionPushedResultCommandHandler(
    ILogger<ForeignCommissionPushedResultCommandHandler> logger,
    IForeignTrademarkBonusRepository foreignTrademarkBonusRepository) 
    : IRequestHandler<ForeignCommissionPushedResultCommand>
{
    public async Task Handle(ForeignCommissionPushedResultCommand request, CancellationToken cancellationToken)
    {
        var messageResult = request.MessageResult;
        logger.LogMessageResult(messageResult);
        
        var procId = messageResult.Data.UnpackToString();
        var bonus = await foreignTrademarkBonusRepository.GetAsync(procId, cancellationToken);

        if (bonus is null)
        {
            return;
        }

        if (!messageResult.Success)
        {
            logger.LogForeignTrademarkWeightPushedFail(procId, messageResult.Message);
            return;
        }

        bonus.Pushed = true;
        bonus.UpdateTime = DateTime.Now;
        bonus.Updater = UserIds.Administrator;
            
        await foreignTrademarkBonusRepository.UpdateAsync(bonus, cancellationToken);
            
        logger.LogForeignTrademarkWeightPushedSuccess(procId);
    }
}