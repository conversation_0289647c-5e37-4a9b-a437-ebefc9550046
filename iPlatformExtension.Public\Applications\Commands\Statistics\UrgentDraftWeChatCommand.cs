﻿using iPlatformExtension.Public.Applications.Models.Flow;
using MediatR;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;

namespace iPlatformExtension.Public.Applications.Commands.Statistics;

/// <summary>
/// 催稿微信通知
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="DateType">数据类型</param>
/// <param name="UrgentUserList">通知用户列表</param>
/// <param name="Warning">警告语</param>
public record UrgentDraftWeChatCommand([Required(ErrorMessage = "任务id是必须的")] List<string> ProcId, int DateType, List<GetUrgentDraftUserListDto> UrgentUserList, string? Warning) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

