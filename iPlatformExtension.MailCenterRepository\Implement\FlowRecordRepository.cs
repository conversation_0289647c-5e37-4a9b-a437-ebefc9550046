﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;

namespace iPlatformExtension.MailCenterRepository.Implement;

internal class FlowRecordRepository(
    IFreeSql<MailCenterFreeSql> fsql,
    UnitOfWorkManage<MailCenterFreeSql> manager)
    : DefaultRepository<FlowRecord, string>(fsql, manager), IFlowRecordRepository;
