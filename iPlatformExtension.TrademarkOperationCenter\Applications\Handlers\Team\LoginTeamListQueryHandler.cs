﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 登陆用户团队列表
    /// </summary>
    public class LoginTeamListQueryHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor) : IRequestHandler<LoginTeamListQuery, IEnumerable<LoginTeamListDto>>
    {
        public async Task<IEnumerable<LoginTeamListDto>> Handle(LoginTeamListQuery request, CancellationToken cancellationToken)
        {
            //获取操作用户信息
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userId);
            var loginTeamListDtos = freeSql.Select<SysTeamAssociateMember, SysTeam>()
                .LeftJoin(it => it.t1.TeamId == it.t2.TeamId).Where(it => it.t1.UserId == userId)
                .ToList(it => new LoginTeamListDto(it.t2.TeamId, it.t2.TeamName));
            return loginTeamListDtos;
        }
    }
}

