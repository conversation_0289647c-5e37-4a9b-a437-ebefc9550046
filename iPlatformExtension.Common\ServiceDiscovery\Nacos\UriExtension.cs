﻿namespace iPlatformExtension.Common.ServiceDiscovery.Nacos;

public static class UriExtension
{
    public static Uri GetServiceEndpointUri(this NacosClusterId nacosClusterId, string scheme)
    {
        var (serviceName, groupName) = nacosClusterId;
        var uri = new Uri($"{scheme}://{groupName}__{serviceName}");
        NacosServiceEndpointProviderFactory.nacosClusterIds.TryAdd(uri.GetLeftPart(UriPartial.Authority), nacosClusterId);

        return uri;
    }
}