﻿using System.ComponentModel.DataAnnotations;
using System.Threading.Channels;
using iPlatformExtension.Commission.Application.Commands.Patent;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Commission.Controllers;

/// <summary>
/// 专利提成控制器
/// </summary>
[ApiController]
[Route("patent-commission")]
[Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
public sealed class PatentCommissionController : ControllerBase
{
    /// <summary>
    /// 推送专利提成信息到提成服务
    /// </summary>
    /// <param name="channel">推送队列</param>
    /// <param name="year">年</param>
    /// <param name="month">月</param>
    /// <returns>统一响应返回</returns>
    [HttpPost("/commission-service/patent-commission/commission-weight/{year:int}/{month:int}")]
    [Authorize(Roles = "系统管理员, IT测试员")]
    public ValueTask PushPatentCommissionAsync(
        [FromServices] Channel<PushPatentCommissionWeightCommand> channel,
        [Required] int year,
        [Required] int month)
    {
        return channel.Writer.WriteAsync(new PushPatentCommissionWeightCommand(year, month));
    }
}