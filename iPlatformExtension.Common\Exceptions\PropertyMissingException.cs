using System.Runtime.CompilerServices;

namespace iPlatformExtension.Common.Exceptions;

/// <summary>
/// 属性缺失异常
/// </summary>
public class PropertyMissingException(
    object objectId,
    object? property,
    string objectType = "对象",
    [CallerArgumentExpression("property")] string? propertyName = null)
    : ApplicationException
{
    public override string Message => $"{objectType}[{objectId}]缺少属性{propertyName}";

    public static void ThrowIfNull(
        object objectId,
        object? property,
        string objectType = "对象",
        [CallerArgumentExpression("property")] string? propertyName = null)
    {
        if (property is null)
        {
            throw new PropertyMissingException(objectId, property, objectType, propertyName);
        }
    }
}