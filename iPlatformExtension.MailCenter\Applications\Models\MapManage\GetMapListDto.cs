﻿namespace iPlatformExtension.MailCenter.Applications.Models.MapManage;

/// <summary>
/// 映射列表
/// </summary>
/// <param name="MapId">映射id</param>
/// <param name="CreateTime">创建时间</param>
/// <param name="CreateUser">创建用户</param>
/// <param name="CtrlProcId">任务id</param>
/// <param name="IsEnabled">是否生效</param>
/// <param name="MapNo">映射编号</param>
/// <param name="MapValue">映射值</param>
/// <param name="Scope">作用范围</param>
/// <param name="UpdateTime">更新时间</param>
/// <param name="UpdateUser">更新用户</param>
public record GetMapListDto(
    string MapId,
    DateTime? CreateTime,
    string CreateUserTemp,
    string CtrlProcId,
    int? IsEnabled,
    string MapNo,
    string MapValue,
    string ScopeTemp,
    DateTime? UpdateTime,
    string UpdateUserTemp)
{
    public object CreateUser { get; set; }
    public object UpdateUser { get; set; }
    public object CtrlProc { get; set; }
    public object Scope { get; set; }
};

