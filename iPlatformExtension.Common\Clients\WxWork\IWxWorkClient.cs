using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.Clients.WxWork
{
    public interface IWxWorkClient
    {
        public WxConfig wxConfig { get; set; }

        public Task<WxResponseMessage> SendMarkdownMessageAsync(string UserName, WxMarkdown content);
        public string GetTicket();
        public Task<WxResponseMessage> GetUserInfoAsync(string code);

        public Task<WxResponseMessage> SendTodoMessage(WxMessageContent wx);
        public Task<WxResponseMessage> SendTextCardMessage(WxMessageContent wx);

        public Task<WxResponseMessage> SendCardMessageAsync(string UserName, TextCardContent content, RedirectConfig redirectConfig = null);

        /// <summary>
        /// 发送消息到企业微信机器人
        /// </summary>
        /// <param name="Message">消息主体</param>
        /// <param name="Scope">生效范围</param>
        public Task SentRobotMessageAsync(string Message, string Scope);

    }
}
