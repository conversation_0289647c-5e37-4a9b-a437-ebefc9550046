using System.Net;
using Nacos.V2.Naming.Dtos;

namespace iPlatformExtension.Common.ServiceDiscovery.Nacos;

internal static class ServiceEndpointExtension
{
    internal static EndPoint GetEndPoint(this Instance instance)
    {
        EndPoint endPoint;
        if (IPAddress.TryParse(instance.Ip, out var ipAddress))
        {
            endPoint = new IPEndPoint(ipAddress, instance.Port);
        }
        else
        {
            endPoint = new DnsEndPoint(instance.Ip, instance.Port);
        }

        return endPoint;
    }

    internal static IEnumerable<EndPoint> GetEndPoints(this Instance instance, params string[] ports)
    {
        for (var i = 0; i < ports.Length; i++)
        {
            if (!int.TryParse(ports[i], out var port)) continue;
            
            EndPoint endPoint;
            if (IPAddress.TryParse(instance.Ip, out var ipAddress))
            {
                endPoint = new IPEndPoint(ipAddress, port);
            }
            else
            {
                endPoint = new DnsEndPoint(instance.Ip, port);
            }
        
            yield return endPoint;
        }
    }
}