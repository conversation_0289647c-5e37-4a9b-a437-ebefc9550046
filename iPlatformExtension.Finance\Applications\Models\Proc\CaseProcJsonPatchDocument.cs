﻿using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Newtonsoft.Json.Serialization;

namespace iPlatformExtension.Finance.Applications.Models.Proc;

/// <summary>
/// 案件任务补丁更新
/// </summary>
public sealed class CaseProcJsonPatchDocument(List<Operation<CaseProcPatchDto>> operations) 
    : JsonPatchDocument<CaseProcPatchDto>(operations, contractResolver)
{
    private static readonly IContractResolver contractResolver = new CamelCasePropertyNamesContractResolver();

    /// <summary>
    /// 任务id
    /// </summary>
    [Required]
    public string ProcId { get; set; } = null!;
}