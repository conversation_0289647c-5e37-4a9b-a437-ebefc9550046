﻿using FreeSql.Internal.Model;
using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Commission.Application.Queries.Trademark.Domestic;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class DomesticCommissionQueryHandler(
    IFreeSql freeSql,
    IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<DomesticCommissionQuery, IEnumerable<UserCommissionWeightStatistics>>
{
    public async Task<IEnumerable<UserCommissionWeightStatistics>> Handle(DomesticCommissionQuery request, CancellationToken cancellationToken)
    {
        var (dateRange, districtCode, keyword, deptIds, pageIndex, pageSize) = request;
        var userId = (httpContextAccessor.HttpContext?.User).GetUserId();
        
        DateTime?[] dateArray = [dateRange.StartDate?.ToDateTime(TimeOnly.MinValue), dateRange.EndDate?.AddDays(1).ToDateTime(TimeOnly.MinValue)];
        var dateRangeFilter = new DynamicFilterInfo()
        {
            Field = nameof(DomesticTrademarkCommission.CommissionDate),
            Logic = DynamicFilterLogic.Or,
            Operator = DynamicFilterOperator.DateRange,
            Value = dateArray,
            Filters = [userId.BuildEqualsDynamicFilter(nameof(DomesticTrademarkCommission.ProcMainUndertakerId))]
        };
        
        if (deptIds.Any())
        {
            dateRangeFilter.Filters.Add(deptIds.BuildContainsDynamicFilter(nameof(DomesticTrademarkCommission.DeptId)));
        }

        var query = freeSql.Select<DomesticTrademarkCommission>().WithLock()
            .WhereDynamicFilter(dateRangeFilter)
            .WhereIf(!string.IsNullOrWhiteSpace(keyword),
                commission => commission.CnName.Contains(keyword!) || commission.UserName.Contains(keyword!))
            .WhereIf(!string.IsNullOrWhiteSpace(districtCode), commission => commission.DistrictCode == districtCode)
            .GroupBy(commission => new UserCommissionWeightStatistics
            {
                DeptName = commission.DeptName,
                District = commission.DistrictName,
                Name = commission.CnName,
                UserId = commission.ProcMainUndertakerId,
                Username = commission.UserName,
                Month = commission.Month,
                Year = commission.Year
            });
        
        var totalCountTask = query.CountAsync(cancellationToken);
        
        var pagingQuery = freeSql.Select<DomesticTrademarkCommission>().WithLock()
            .WhereDynamicFilter(dateRangeFilter)
            .WhereIf(!string.IsNullOrWhiteSpace(keyword),
                commission => commission.CnName.Contains(keyword!) || commission.UserName.Contains(keyword!))
            .WhereIf(!string.IsNullOrWhiteSpace(districtCode), commission => commission.DistrictCode == districtCode)
            .GroupBy(commission => new UserCommissionWeightStatistics
            {
                DeptName = commission.DeptName,
                District = commission.DistrictName,
                Name = commission.CnName,
                UserId = commission.ProcMainUndertakerId,
                Username = commission.UserName,
                Month = commission.Month,
                Year = commission.Year
            });
        
        var result = await pagingQuery.OrderBy(aggregate => aggregate.Key.UserId).Page(pageIndex, pageSize)
            .ToListAsync(aggregate => new UserCommissionWeightStatistics
            {
                UserId = aggregate.Key.UserId,
                Username = aggregate.Key.Username,
                Name = aggregate.Key.Name,
                District = aggregate.Key.District,
                ProcCount = aggregate.Count(),
                TotalWeightPoint = aggregate.Sum(aggregate.Value.EditedProcPoint ?? aggregate.Value.ProcPoint),
                DeptName = aggregate.Key.DeptName,
                Month = aggregate.Key.Month,
                Year = aggregate.Key.Year
            }, cancellationToken);

        return new PageResult<UserCommissionWeightStatistics>
        {
            Page = pageIndex,
            PageSize = pageSize,
            Total = await totalCountTask,
            Data = result.Select((statistics, i) =>
            {
                statistics.SerialNumber = i + 1;
                return statistics;
            }).ToList()
        };
    }
}