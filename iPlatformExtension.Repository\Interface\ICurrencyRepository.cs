﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface ICurrencyRepository : 
    IBaseRepository<BasCurrency, string>, 
    IRedisCacheableRepository<string, BasCurrency>, 
    IScopeDependency, 
    IStringKeyCacheableRepository<BasCurrency>
{
    Task<BasCurrency?> ICacheableRepository<string, BasCurrency>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Where(currency => currency.CurrencyId == key).ToOneAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasCurrency>> ICacheableRepository<string, BasCurrency>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasCurrency>.GenerateKey(BasCurrency value)
    {
        return value.CurrencyId;
    }

    string? IStringKeyCacheableRepository<BasCurrency>.GetCacheTextValue(BasCurrency value)
    {
        return value.CurrencyZhCn;
    }
}