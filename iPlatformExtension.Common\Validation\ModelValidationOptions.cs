﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using System.Reflection;
using FluentValidation;
using FluentValidation.AspNetCore;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Validation;

public class ModelValidationOptions
{
    /// <summary>
    /// 验证器的生命周期
    /// </summary>
    public ServiceLifetime Lifetime { get; set; }  = ServiceLifetime.Singleton;

    /// <summary>
    /// 需要额外加载的程序集
    /// </summary>
    public IList<Assembly> Assemblies { get; } = new List<Assembly>();

    /// <summary>
    /// 是否支持内部类型
    /// </summary>
    public bool IncludeInternalTypes { get; set; } = true;

    /// <summary>
    /// 验证器全局配置
    /// </summary>
    public Action<ValidatorConfiguration>? ConfigGlobalValidatorOptions { get; set; } = configuration =>
    {
        configuration.DisplayNameResolver = (_, propertyInfo, expression) =>
        {
            if (expression?.Body is MemberExpression propertyExpression)
            {
                propertyInfo ??= propertyExpression.Member;
            }

            if (propertyInfo is null) return null;
            
            var displayNameAttribute = propertyInfo.GetCustomAttribute<DisplayNameAttribute>();
            if (displayNameAttribute != null)
            {
                return displayNameAttribute.DisplayName;
            }

            var displayAttribute = propertyInfo.GetCustomAttribute<DisplayAttribute>();
            return displayAttribute?.Name;

        };
    };
    
    /// <summary>
    /// 参数绑定
    /// </summary>
    public Type? ParameterBinder
    {
        get;
        init
        {
            if (value?.IsSubclassOf(typeof(ParameterBinder)) ?? false)
                field = value;
            else
                throw new TypeAccessException($"ParameterBinder must be a subclass of {nameof(ParameterBinder)}");
        }
    }
    
    /// <summary>
    /// fluent-validation的其他设置选项
    /// </summary>
    public Action<FluentValidationAutoValidationConfiguration>? AutoValidationConfiguration { get; set; }

    /// <summary>
    /// 程序集过滤器
    /// </summary>
    public Func<AssemblyScanner.AssemblyScanResult, bool>? Filter { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Action<ApiBehaviorOptions> ConfigApiBehaviorOptions { get; set; } = options =>
    {
        options.InvalidModelStateResponseFactory = context =>
        {
            var invalidModelStateResponseFactory =
                context.HttpContext.RequestServices.GetRequiredService<InvalidModelResponseFactory>();
            return invalidModelStateResponseFactory.CreateInvalidModelResponse<ResultData>(context);
        };
    };
}