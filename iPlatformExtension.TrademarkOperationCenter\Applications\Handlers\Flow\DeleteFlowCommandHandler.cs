﻿using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;
using System.Diagnostics;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow;

internal sealed class DeleteFlowCommandHandler(
    IFlowActivityRepository flowActivityRepository,
    IFlowHistoryRepository flowHistoryRepository,
    IMediator mediator)
    : IRequestHandler<DeleteFlowCommand>
{
    public async Task Handle(DeleteFlowCommand request, CancellationToken cancellationToken)
    {
        await flowActivityRepository.DeleteAsync(
            activity => activity.FlowId == request.FlowId && activity.ObjId == request.ObjectId, cancellationToken);
        await flowHistoryRepository.DeleteAsync(
            history => history.ObjId == request.ObjectId && history.FlowType == FlowType.Delivery , cancellationToken);

        await mediator.Send(new UpdateCaseProcInfoCommand(new UpdateCaseProcInfoDto { ProcId = request.ObjectId, ProcStatus = "TCLZ", SubProcStatus = "t_in_process" }), cancellationToken);


    }
}