using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_trademark_items", DisableSyncStructure = true)]
	public partial class VwTrademarkItems {

		[ Column(Name = "cur_id", StringLength = 50, IsNullable = false)]
		public string CurId { get; set; }

		[ Column(Name = "end_date")]
		public DateTime? EndDate { get; set; }

		[ Column(Name = "fpid", StringLength = 50)]
		public string Fpid { get; set; }

		[ Column(Name = "history_id", StringLength = 50, IsNullable = false)]
		public string HistoryId { get; set; }

		[ Column(Name = "id", StringLength = 50)]
		public string Id { get; set; }

		[ Column(Name = "is_default")]
		public bool? IsDefault { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; }

		[ Column(Name = "is_open")]
		public bool IsOpen { get; set; }

		[ Column(Name = "item_type", StringLength = 50)]
		public string ItemType { get; set; }

		[ Column(Name = "level")]
		public int? Level { get; set; }

		[ Column(Name = "lid", StringLength = 50, IsNullable = false)]
		public string Lid { get; set; }

		[ Column(Name = "pid", StringLength = 50)]
		public string Pid { get; set; }

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "start_date")]
		public DateTime? StartDate { get; set; }

		[ Column(Name = "text_en_us", StringLength = 4000)]
		public string TextEnUs { get; set; }

		[ Column(Name = "text_ja_jp", StringLength = 4000)]
		public string TextJaJp { get; set; }

		[ Column(Name = "text_zh_cn", StringLength = 4000)]
		public string TextZhCn { get; set; }

		[ Column(Name = "version_id", StringLength = 50, IsNullable = false)]
		public string VersionId { get; set; }

		[ Column(Name = "version_name", StringLength = 50, IsNullable = false)]
		public string VersionName { get; set; }

	}

}
