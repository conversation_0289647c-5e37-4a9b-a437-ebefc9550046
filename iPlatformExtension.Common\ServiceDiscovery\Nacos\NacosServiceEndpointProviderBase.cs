﻿using Microsoft.Extensions.ServiceDiscovery;
using Nacos.V2;

namespace iPlatformExtension.Common.ServiceDiscovery.Nacos;

internal abstract class NacosServiceEndpointProviderBase(
    string groupName,
    string serviceName,
    NacosServiceEndpointReloadToken changeToken,
    INacosNamingService namingService)
    : IServiceEndpointProvider, IEventListener
{
    protected readonly string _groupName = groupName;
    
    protected readonly string _serviceName = serviceName;
    
    protected readonly NacosServiceEndpointReloadToken _changeToken = changeToken;
    
    protected readonly INacosNamingService _namingService = namingService;

    public ValueTask DisposeAsync()
    {
        return ValueTask.CompletedTask;
    }

    public async ValueTask PopulateAsync(IServiceEndpointBuilder endpoints, CancellationToken cancellationToken)
    {
        await _namingService.Subscribe(_serviceName, _groupName, this);
        _changeToken.Refresh();
        
        await foreach (var serviceEndpoint in ProvideEndpointsAsync(cancellationToken))
        {
            endpoints.Endpoints.Add(serviceEndpoint);
        }
        
        endpoints.AddChangeToken(_changeToken);
    }

    public Task OnEvent(IEvent @event)
    {
        _changeToken.OnReload();
        return Task.CompletedTask;
    }

    protected abstract IAsyncEnumerable<ServiceEndpoint> ProvideEndpointsAsync(CancellationToken cancellationToken);
}