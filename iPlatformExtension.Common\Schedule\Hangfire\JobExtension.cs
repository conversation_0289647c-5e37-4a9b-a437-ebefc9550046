﻿using System.Linq.Expressions;
using Hangfire;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Schedule.Hangfire;

public static class JobExtension
{
    public static void AddRecurringJob<TJob>(this IApplicationBuilder app, string jobName, Expression<Func<TJob, Task>> executeJob,
        string cronExpression, RecurringJobOptions jobOptions)
    {
        ArgumentNullException.ThrowIfNull(app);
        var jobManager = app.ApplicationServices.GetRequiredService<IRecurringJobManager>();
        jobManager.AddOrUpdate(jobName, executeJob, cronExpression, jobOptions);
    }
    
    public static void AddRecurringJob<TJob>(this IApplicationBuilder app, string jobName, string queue, Expression<Func<TJob, Task>> executeJob,
        string cronExpression, RecurringJobOptions jobOptions)
    {
        ArgumentNullException.ThrowIfNull(app);
        var jobManager = app.ApplicationServices.GetRequiredService<IRecurringJobManager>();
        jobManager.AddOrUpdate(jobName, queue.ToLower(), executeJob, cronExpression, jobOptions);
    }
}