﻿using iPlatformExtension.Model.Pooled;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Common.ObjectPools.ApiResult;

public class ApiResultPool<T> : DefaultObjectPool<T> where T : class, IPooledObject, new()
{
    public ApiResultPool(ApiResultPooledObjectPolicy<T> policy) : base(policy)
    {
    }

    public override T Get()
    {
        var result = base.Get();
        result.OnGet();

        return result;
    }
}