﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class DeliveryNiceCategoryRepository 
    : DefaultRepository<DeliveryNiceCategory, long>, 
        IDeliveryNiceCategoryRepository
{
    public DeliveryNiceCategoryRepository(IFreeSql freeSql, UnitOfWorkManager uowManger) : base(freeSql, uowManger)
    {
    }
}