﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;

/// <summary>
/// 案件尼斯分类信息
/// </summary>
/// <param name="Id">数据ID</param>
/// <param name="GrandCategoryId">类别id</param>
/// <param name="GrandNumber">类别号码</param>
/// <param name="GrandName">类别名称</param>
/// <param name="ParentCategoryId">类似群id</param>
/// <param name="ParentNumber">类似群编号</param>
/// <param name="ParentName">类似群名称</param>
/// <param name="CategoryId">商品id</param>
/// <param name="CategoryNumber">商品编号</param>
/// <param name="CategoryName">商品描述</param>
/// <param name="IsStandard">是否标准项目</param>
/// <param name="Order">排序</param>
public record StandardCategoryDto(
    long Id,
    string GrandCategoryId,
    string GrandNumber,
    string GrandName,
    string ParentCategoryId,
    string ParentNumber,
    string ParentName,
    string CategoryId,
    string CategoryNumber, 
    string CategoryName, 
    bool IsStandard,
    int Order);