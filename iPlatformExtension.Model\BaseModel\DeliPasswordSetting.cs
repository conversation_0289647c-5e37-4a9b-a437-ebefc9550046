using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_password_setting", DisableSyncStructure = true)]
	public partial class DeliPasswordSetting {

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "pwd_id", StringLength = 50, IsNullable = false)]
		public string PwdId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "pwd_remark", StringLength = 500)]
		public string PwdRemark { get; set; }

		[ Column(Name = "pwd_type", StringLength = 50, IsNullable = false)]
		public string PwdType { get; set; }

		[ Column(Name = "pwd_word", StringLength = 50, IsNullable = false)]
		public string PwdWord { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
