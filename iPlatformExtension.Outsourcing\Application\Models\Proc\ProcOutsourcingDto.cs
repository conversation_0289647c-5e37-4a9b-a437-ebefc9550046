﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;

namespace iPlatformExtension.Outsourcing.Application.Models.Proc;

/// <summary>
/// 任务委外信息
/// </summary>
[Description("任务委外信息")]
public class ProcOutsourcingDto
{
    /// <summary>
    /// 供应商id
    /// </summary>
    [JsonIgnore]
    public string? SupplierId { get; set; }
    
    /// <summary>
    /// 境外代理信息
    /// </summary>
    [Description("境外代理信息")]
    public ForeignAgencyInfo? AgencyInfo { get; set; }

    /// <summary>
    /// 外所文号
    /// </summary>
    [Description("外所文号")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ForeignNumber
    {
        get => Model.Constants.CaseType.IsBelongPatent(CaseType) ? field : null;
        set;
    } = string.Empty;

    /// <summary>
    /// 联系人id集合
    /// </summary>
    [Description("联系人id集合")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public IEnumerable<string>? ContactIds
    {
        get => Model.Constants.CaseType.IsBelongPatent(CaseType) ? field : null;
        set;
    } = [];

    /// <summary>
    /// 选所备注
    /// </summary>
    [Description("选所备注")]
    public string ForeignSupplierRemark { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否可以编辑
    /// </summary>
    [Description("是否可以编辑")]
    [Required]
    public bool Editable { get; set; }

    /// <summary>
    /// 案件类型
    /// </summary>
    [JsonIgnore]
    public string CaseType { get; set; } = null!;

    /// <summary>
    /// 版本号
    /// </summary>
    [Description("版本号")]
    [Required]
    public int Version { get; set; }
}