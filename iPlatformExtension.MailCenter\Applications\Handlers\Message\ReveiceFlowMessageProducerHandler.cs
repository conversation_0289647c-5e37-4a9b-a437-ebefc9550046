﻿using Confluent.Kafka;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.MailCenter.Infrastructure.MQ;
using iPlatformExtension.Model.Dto.MailCenter;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using Microsoft.AspNetCore.Http;
using MimeKit;
using Nacos.V2.Utils;
using static iPlatformExtension.Model.Enum.SysEnum;
using iPlatformExtension.Common.MQ.MailMQ;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Message
{
    public class ReveiceFlowMessageProducerHandler(
        KafkaProducerService<Null, MailCenterMessageContent> producer,
        IHttpContextAccessor httpContextAccessor,
        IFreeSql<MailCenterFreeSql> sql
        ) : IRequestHandler<ReceiveFlowMessageQuery>
    {
        public async Task Handle(ReceiveFlowMessageQuery request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;

            List<MailReceive> mailList = await sql.Select<MailReceive>().WithLock().
                Where(o => request.flowRecords.Any(f => f.MailId == o.MailId)).ToListAsync(cancellationToken);

            request.flowRecords.Where(o => o.IsCurrent == MailFlowActionStatus.Enable.GetHashCode()).ToList().ForEach(async flow =>
            {

                var auditRemark = request.flowRecords.FirstOrDefault(o => o.Id == flow.PreRecordId)?.AuditRemark;
                if (string.IsNullOrEmpty(auditRemark))
                {
                    auditRemark = await sql.Select<FlowRecord>().Where(o => o.Id == flow.PreRecordId).ToOneAsync(o => o.AuditRemark, cancellationToken);
                }
                var mailInfo = mailList.FirstOrDefault(o => o.MailId == flow.MailId);
                MailCenterMessageContent content = new MailCenterMessageContent()
                {
                     MailId = mailInfo.MailId,
                    MailTitle = mailInfo.MailSubject,
                    MailNo = mailInfo.MailNo,
                    OperatorUser = userId,
                    DateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    RecipientBy = flow.AuditUser,
                    OperationType = request.OperationType,
                    Remark = auditRemark
                };
                var res = await producer.SendMessageAsync(content, "MailCenterProducer", cancellationToken);
            });
        }
    }
}
