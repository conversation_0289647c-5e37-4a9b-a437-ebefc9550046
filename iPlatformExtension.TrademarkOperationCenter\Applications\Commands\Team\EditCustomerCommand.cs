﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;

/// <summary>
/// 编辑客户命令
/// </summary>
/// <param name="CustomerId">客户id</param>
/// <param name="TeamId">团队id</param>
public record EditCustomerCommand(List<string>? CustomerId, string TeamId) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

