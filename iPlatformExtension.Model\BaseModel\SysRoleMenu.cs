using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_role_menu", DisableSyncStructure = true)]
	public partial class SysRoleMenu {

		[ Column(Name = "authority", StringLength = 50, IsNullable = false)]
		public string Authority { get; set; }

		[ Column(Name = "menu_id", StringLength = 50, IsNullable = false)]
		public string MenuId { get; set; }

		[ Column(Name = "role_id", StringLength = 50, IsNullable = false)]
		public string RoleId { get; set; }

	}

}
