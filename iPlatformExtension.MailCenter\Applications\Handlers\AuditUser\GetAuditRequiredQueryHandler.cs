using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.AuditUser;
using iPlatformExtension.MailCenter.Applications.Queries.AuditUser;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AuditUser;

/// <summary>
/// 获取审核必选人查询处理程序
/// </summary>
internal sealed class GetAuditRequiredQueryHandler(
    IFreeSql<PlatformFreeSql> platformFreeSql,
    IFlowRequireAuditRepository flowRequireAuditRepository,
    IUserInfoRepository userInfoRepository) : IRequestHandler<GetAuditRequiredQuery, GetAuditRequiredDto>
{
    public async Task<GetAuditRequiredDto> Handle(GetAuditRequiredQuery request, CancellationToken cancellationToken)
    {
        var result = new GetAuditRequiredDto
        {
            IsRequired = false
        };

        // 设置是否必选人
        if (string.IsNullOrEmpty(request.UserId))
        {
            return result;
        }

        // 直接查询是否有包含该用户ID的必选人配置
        var flowRequireAudits = await flowRequireAuditRepository
            .Where(x => x.RequireAuditUser.Contains(request.UserId))
            .ToListAsync(cancellationToken);

        result.IsRequired = flowRequireAudits.Any();

        // 查询指定审核人列表
        var designatedAuditUsers = new List<Models.AuditUser.UserInfoDto>();

        // 使用缓存获取用户信息
        ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;

        // 如果有必选人配置，获取指定审核人
        if (flowRequireAudits != null && flowRequireAudits.Count() == 1)
        {
            // 指定审核人可能有多个，使用分号分隔
            var auditUserIds = flowRequireAudits.First().AuditUserId.Split(';', StringSplitOptions.RemoveEmptyEntries);

            foreach (var userId in auditUserIds)
            {
                // 获取用户基本信息
                var userInfo =
                    await userBaseInfoRepository.GetCacheValueAsync(userId, cancellationToken: cancellationToken);
                if (userInfo != null)
                {
                    // 获取用户完整信息
                    var fullUserInfo = await platformFreeSql.Select<SysUserInfo>()
                        .Where(u => u.UserId == userId)
                        .FirstAsync(cancellationToken);

                    // 获取部门信息
                    var deptId = fullUserInfo?.DeptId ?? string.Empty;
                    var deptInfo = !string.IsNullOrEmpty(deptId)
                        ? await platformFreeSql.Select<SysDeptInfo>()
                            .Where(d => d.DeptId == deptId)
                            .FirstAsync(cancellationToken)
                        : null;

                    var department = deptInfo?.FullName ?? string.Empty;

                    // 添加到指定审核人列表
                    designatedAuditUsers.Add(new Models.AuditUser.UserInfoDto
                    {
                        UserId = userId,
                        Name = userInfo.CnName ?? string.Empty,
                        Department = department
                    });
                }
            }
        }

        result.DesignatedAuditUsers = designatedAuditUsers;
        return result;
    }
}