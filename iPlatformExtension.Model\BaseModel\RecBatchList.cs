using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rec_batch_list", DisableSyncStructure = true)]
	public partial class RecBatchList {

		[ Column(Name = "batch_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string BatchId { get; set; }

		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		[ Column(Name = "batch_no", StringLength = 50)]
		public string BatchNo { get; set; }

		[ Column(Name = "batch_status", StringLength = 50)]
		public string BatchStatus { get; set; }

		[ Column(Name = "batch_title", StringLength = 200)]
		public string BatchTitle { get; set; }

		[ Column(Name = "belong_company", StringLength = 50)]
		public string BelongCompany { get; set; }

		[ Column(Name = "chupiao_date")]
		public DateTime? ChupiaoDate { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "file_pages", StringLength = 50)]
		public string FilePages { get; set; }

		[ Column(Name = "is_pct")]
		public bool IsPct { get; set; } = false;

		[ Column(Name = "jiaofei_date")]
		public DateTime? JiaofeiDate { get; set; }

		[ Column(Name = "key_id", StringLength = 50, IsNullable = false)]
		public string KeyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "pay_sipo_way", StringLength = 50)]
		public string PaySipoWay { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "scan_type", StringLength = 50)]
		public string ScanType { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
