﻿using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;
using Xunit.Abstractions;

namespace CommonTest;

public class PhoenixClientTest
{
    private readonly IServiceProvider _serviceProvider;

    private readonly ITestOutputHelper _testOutputHelper;

    public PhoenixClientTest(ITestOutputHelper testOutputHelper)
    {
        _testOutputHelper = testOutputHelper;

        var configuration = new ConfigurationManager();
        configuration.AddJsonFile("clients.Local.json", true, true);

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration)
            .AddValidation()
            .AddObjectPools()
            .AddLogging(builder => builder.AddConsole().AddFilter<ConsoleLoggerProvider>(level => level >= LogLevel.Debug))
            .AddPhoenixClient("AcipKey1");

        _serviceProvider = services.BuildServiceProvider();
    }

    [InlineData("AcipKey1")]
    [InlineData("AcipKey2")]
    [Theory]
    public void TestPhoenixClientFactory(string clientName)
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;

        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient(clientName);

        Assert.Equal(clientName, client.ClientName);
    }

    // [Fact]
    // public async Task TestNiceCategoriesAsync()
    // {
    //     var scope = _serviceProvider.CreateScope();
    //     var provider = scope.ServiceProvider;
    //
    //     var client = provider.GetRequiredService<PhoenixClient>();
    //     var response = await client.GetNiceCategoriesAsync(PhoenixRequestParameters.Default);
    //
    //     Assert.NotNull(response);
    //     Assert.Equal(9091, response.Code);
    // }

    [Fact]
    public async Task TestTrademarkRegistrationOrderAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");
        //var client = provider.GetRequiredService<PhoenixClient>();
        var order = new TrademarkRegistrationOrder()
        {
            BrandExplain = "测试",
            OwnerType = "1",
            Country = "中国大陆",
            Code = "100000",
            IdCard = "111111111111111111",
            PrincipalTel = "15195026071",
            AgentOrganConName = "王雪锋",
            ContactTel = "1761xxx2682",
            SubjectType = "1",
            DomesticReceiverAddress = "北京市昌平区TBD云集中心3号楼12层B座",
            AgentOrganTel = "1761xxx1234",
            ContactEmail = "<EMAIL>",
            ApplicantAddress = "北京市昌平区TBD云集中心3号楼12层B座",
            ContactName = "王雪锋",
            DomesticReceiverCode = "100000",
            PrincipalName = "发v",
            ApplicantName = "测试申请人",
            BookType = "1",
            CertificatesType = "0",
            BrandFile = "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
            PowerOfAttorneyFile = "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
            Attachments =
            [
                new ApplicantAttachment(name:"XXX公司营业执照",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseSubjectQualification)
            ],
            BrandName = "王雪锋",
            BrandType = TrademarkType.Text,
            Number = 1,
            OrderToken = "abc123",
            NiceCategories =
            [
                new NiceClass()
                {
                    Id = 1,
                    Number = "01",
                    Name = "化学原料",
                    Children = new NiceClass[]
                    {
                        new()
                        {
                            Id = 2,
                            Number = "0101",
                            Name = "工业气体，单质",
                            Children =
                            [
                                new NiceCategory
                                {
                                    Id = 23,
                                    Number = "010039",
                                    Name = "碱土金属"
                                },
                                new NiceCategory
                                {
                                    Id = 3,
                                    Number = "010061",
                                    Name = "氨"
                                },
                                new NiceCategory
                                {
                                    Id = 4,
                                    Number = "010066",
                                    Name = "无水氨"
                                },
                                new NiceCategory
                                {
                                    Id = 24,
                                    Number = "010074",
                                    Name = "锑"
                                },
                                new NiceCategory
                                {
                                    Id = 5,
                                    Number = "010082",
                                    Name = "氩"
                                },
                                new NiceCategory
                                {
                                    Id = 25,
                                    Number = "010084",
                                    Name = "砷"
                                }
                            ]
                        }
                    }
                }
            ]
        };
        var response = await client.CreateOrderAsync(PhoenixUri.CreateOrder, order);

        Assert.NotNull(response?.Data?.OrderNo);
    }

    /// <summary>
    /// 商标注销
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task TestCancelTrademarkAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var cancelTrademark = new CancelTrademarkParameters
        {
            AgentOrganConName = "陈俐俐",
            AgentOrganId = "44224",
            AgentOrganName = "华进联合专利商标代理有限公司",
            AgentOrganTel = "020-87323188",
            ApplicantAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantEnglishAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantName = "深圳市渔米粥饮食管理有限公司",
            AttachmentsList =
            [
                new ApplicantAttachment(name:"xx许可人委托书",
                uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                attachmentType:ApplicantAttachmentType.OfficialAttachment,
                ApplicantAttachmentSubType.Attorney),
                new ApplicantAttachment(name:"xxx身份证",
                uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                attachmentType:ApplicantAttachmentType.OfficialAttachment,
                ApplicantAttachmentSubType.ChineseIdentification)
            ],
            BookType = 1,
            BrandTransferParamList = new List<BrandTransferParam>()
            {
                new BrandTransferParam
                {
                    brandRegisterNo = "28326310",
                    firstCgNo = 33,
                    brandName = "青莲诗仙魂",
                    applicantName = "蒋可",
                    categoryFlowStateName = "已注册",
                    intCls = 33,
                    firstCgName = "酒",
                    brandTeam =
                    [
                        new Brandteam {
                            code =  "3301",
                            name = "威士忌",
                            status = "0"
                        }
                    ],
                    imageUrl = "eea17a11/c8cd16e3/fe4f5508/7166a9e5/logo_middle.jpg",
                    number = 10
                }
            },
            CertificatesType = 3,
            ChangeType = 0,
            Code = "410000",
            ContactEmail = "<EMAIL>",
            ContactName = "邹永坤",
            ContactTel = "13689595233",
            Country = "中国",
            IdCard = "685373386",
            IsChange = 0,
            OrderToken = Guid.NewGuid().ToString(),
            OwnerType = 0,
            PrincipalName = "李白",
            PrincipalTel = "010-52596009",
            SubjectType = 1,
        };
        var response = await client.ControlTrademarkAsync(PhoenixUri.CancelTrademark, cancelTrademark);



        Assert.NotNull(response?.Data?.OrderNo);
    }

    /// <summary>
    /// 商标转让申请
    /// </summary>
    [Fact]
    public async Task TestTrademarkTransferOrderAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var order = new TrademarkTransferOrder()
        {
            TransferType = "1",
            SellerInfo = new DeliveryApplicantInfo()
            {
                ApplicantAddress = "北京市朝阳区广顺北大街五号院内32号内5017",
                ApplicantName = "北京梦知网科技有限公司",
                BookType = "1",
                CertificatesType = "1",
                Code = "100000",
                Country = "中国大陆",
                SubjectType = "1",
                OwnerType = "0",
                IdCard = "111111111111111111"
            },
            BuyerInfo = new DeliveryApplicantInfo()
            {
                ApplicantAddress = "北京市朝阳区广顺北大街五号院内32号内5017",
                ApplicantName = "常州兰利电器科技有限公司",
                BookType = "1",
                CertificatesType = "1",
                Code = "100000",
                Country = "中国大陆",
                SubjectType = "1",
                OwnerType = "0",
                IdCard = "111111111111111111",
                DomesticReceiverAddress = "北京市朝阳区广顺北大街五号院内32号内5017",
                DomesticReceiverCode = "476600",
                DomesticReceiverEmail = "<EMAIL>",
                DomesticReceiverName = "市场"
            },
            OrderInfo = new OrderInfo()
            {
                AgentOrganConName = "王雪锋",
                AgentOrganId = "44224",
                AgentOrganName = "华进联合专利商标代理有限公司",
                AgentOrganTel = "020-87323188",
                ContactEmail = "<EMAIL>",
                ContactName = "wxf",
                ContactTel = "17610532682",
                PrincipalName = "发v",
                PrincipalTel = "15195026071"
            },
            Attachments =
            [
                new ApplicantAttachment(name:"xx许可人委托书",
                uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                attachmentType:ApplicantAttachmentType.OfficialAttachment,
                ApplicantAttachmentSubType.Attorney),
                new ApplicantAttachment(name:"xxx身份证",
                uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                attachmentType:ApplicantAttachmentType.OfficialAttachment,
                ApplicantAttachmentSubType.ChineseIdentification),
                new ApplicantAttachment(name:"xxx公司资格证明文件",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),
                new ApplicantAttachment(name:"xxx受让人被委托书",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorneyed),
                new ApplicantAttachment(name:"xxx转让声明文件",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransferDeclarationDocument),
                new ApplicantAttachment(name:"xxx受让人身份证明文件",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.AssignChineseIdentification)
            ],
            BrandInfos = new List<BrandInfo>()
            {
                new BrandInfo()
                {
                    BrandRegisterNo = "45896679",
                    BrandName = "京瑞",
                    CategoryFlowStateName = "商标注册申请 | 申请收文",
                    FirstCgName = "社会服务",
                    FirstCgNo = "45",
                    Number = 1,
                    Info = new GoodsInfo()
                    {
                        Address = "北京市朝阳区广顺北大街五号院内32号内5017",
                        ReceiptNo = "45896679",
                        BrandRegisterNo = "45896679",
                        LogoUrl = "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
                        FirstCgNo = "45",
                        BrandName = "京瑞",
                        ApplicantName = "北京梦知网科技有限公司",
                        FlowListName = "商标注册申请 | 申请收文",
                        TypeCode = "45",
                        CategoryFlowStateName = "商标注册申请 | 申请收文",
                        FirstCgName = "社会服务",
                        GoodsList =
                        [
                            new GoodsItem {
                                Code =  "4506",
                                Name = "为法律咨询目的监控知识产权",
                                Status = "0"
                            }
                        ],
                        GoodsText = "4506-为法律咨询目的监控知识产权 4506-向发明人提供知识产权咨询服务 4506-商标代理服务 4506-有关知识产权的顾问服务 4506-法律咨询 4506-版权管理 4506-知识产权代理服务 4506-知识产权咨询 4506-计算机软件许可咨询 4506-通过网站提供法律服务信息",
                        GoodsTextShow = "4506-为法律咨询目的监控知识产权 4506-向发明人提供知识产权咨询服务 4506-商标代理服务 ...",
                        ProcessName = "已注册",
                    }
                }
            },

            OrderToken = Guid.NewGuid().ToString(),

        };
        var response = await client.CreateOrderAsync(PhoenixUri.TransferOrder, order);

        Assert.NotNull(response?.Data?.OrderNo);
        _testOutputHelper.WriteLine("订单号：{0}", response.Data.OrderNo);
    }

    /// <summary>
    /// 商标续展申请
    /// </summary>
    [Fact]
    public async Task TestTrademarkRenewalOrderAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var order = new TrademarkRenewalOrder()
        {
            DeliveryApplicantInfo = new DeliveryApplicantInfo()
            {
                ApplicantAddress = "北京市朝阳区广顺北大街五号院内32号内5017",
                ApplicantName = "北京梦知网科技有限公司",
                BookType = "1",
                CertificatesType = "1",
                Code = "100000",
                Country = "中国大陆",
                ApplicantEnglishAddress = string.Empty,
                SubjectType = "1",
                OwnerType = "0",
                IdCard = "111111111111111111"
            },
            OrderInfo = new OrderInfo()
            {
                AgentOrganConName = "王雪锋",
                AgentOrganId = "44224",
                AgentOrganName = "华进联合专利商标代理有限公司",
                AgentOrganTel = "020-87323188",
                ContactEmail = "<EMAIL>",
                ContactName = "wxf",
                ContactTel = "17610532682",
                PrincipalName = "发v",
                PrincipalTel = "15195026071"
            },
            Attachments =
            [
                new ApplicantAttachment(name:"xx许可人委托书",
                uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                attachmentType:ApplicantAttachmentType.OfficialAttachment,
                ApplicantAttachmentSubType.Attorney),
                new ApplicantAttachment(name:"xxx身份证",
                uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                attachmentType:ApplicantAttachmentType.OfficialAttachment,
                ApplicantAttachmentSubType.ChineseIdentification)
            ],
            BrandInfos = new List<BrandInfo>()
            {
                new BrandInfo()
                {
                    BrandRegisterNo = "45896679",
                    BrandName = "京瑞",
                    CategoryFlowStateName = "商标注册申请 | 申请收文",
                    FirstCgName = "社会服务",
                    FirstCgNo = "45",
                    Number = 1,
                    Info = new GoodsInfo()
                    {
                        Address = "北京市朝阳区广顺北大街五号院内32号内5017",
                        ReceiptNo = "45896679",
                        BrandRegisterNo = "45896679",
                        LogoUrl = "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
                        FirstCgNo = "45",
                        BrandName = "京瑞",
                        ApplicantName = "北京梦知网科技有限公司",
                        FlowListName = "商标注册申请 | 申请收文",
                        TypeCode = "45",
                        CategoryFlowStateName = "商标注册申请 | 申请收文",
                        FirstCgName = "社会服务",
                        GoodsList =
                        [
                            new GoodsItem {
                                Code =  "4506",
                                Name = "为法律咨询目的监控知识产权",
                                Status = "0"
                            }
                        ],
                        GoodsText = "4506-为法律咨询目的监控知识产权 4506-向发明人提供知识产权咨询服务 4506-商标代理服务 4506-有关知识产权的顾问服务 4506-法律咨询 4506-版权管理 4506-知识产权代理服务 4506-知识产权咨询 4506-计算机软件许可咨询 4506-通过网站提供法律服务信息",
                        GoodsTextShow = "4506-为法律咨询目的监控知识产权 4506-向发明人提供知识产权咨询服务 4506-商标代理服务 ...",
                        ProcessName = "已注册",
                    }
                }
            },

            OrderToken = Guid.NewGuid().ToString(),

        };
        var response = await client.CreateOrderAsync(PhoenixUri.TrademarkRenewal, order);

        Assert.NotNull(response?.Data?.OrderNo);
        _testOutputHelper.WriteLine("订单号：{0}", response.Data.OrderNo);
    }

    /// <summary>
    /// 商标异议申请
    /// </summary>
    [Fact]
    public async Task TestTrademarkObjectionsOrderAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var order = new TrademarkObjectionsOrder()
        {
            ApplicantAddress = "湖南省长沙市雨花区新建西路139号中江国际花城1栋113号门面",
            ApplicantName = "北京智果科技有限公司",
            AgentOrganTel = "020-87323188",
            AgentOrganConName = "王雪锋",
            ApplicantAttachments = new ApplicantAttachment[]
            {
                new(name:"委托证明.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name:"营业执照.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseSubjectQualification),
                new(name:"相关说明文件.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.RelevantNotes),
                new(name:"其他委托书.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.OtherAttorney),
            },
            BrandInfos = new BrandInfo[]
            {
                new BrandInfo
                {
                    BrandRegisterNo = "22021485",
                    PreBrandRegisterNo = "8745448",
                    BrandName = "闻曦",
                    DeleteType = 1,
                    LogoUrl = "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
                    CategoryFlowStateName = "已注册",
                    FirstCgNo = "41",
                    FirstCgName = "教育娱乐",
                    Number = 10,
                    GoodsItems =
                    [
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "培训",
                            Status = "0"
                        },
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "教育",
                            Status = "0"
                        },
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "安排和组织会议",
                            Status = "0"
                        }
                    ]
                }
            },
            BookType = "1",
            CertificatesType = "0",
            Code = "101206",
            ContactEmail = "<EMAIL>",
            ContactName = "刘云金",
            ContactTel = "13593165788",
            Country = "中国大陆",
            IdCard = "9111234579902714XB",
            LawBasisList = new LawBasis[]
            {
                new LawBasis()
                {
                    Reason = "实时理由描写",
                    LawId = 2,
                    FileName = "法律文件",
                    LawName = "《商标法》第十五条",
                    FileUrl = "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf"
                },
                new LawBasis()
                {
                    Reason = "实时理由2",
                    LawId = 6,
                    FileName = "理由描述.jpg",
                    LawName = "《商标法》第十三条 第二款",
                    FileUrl = "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf"
                }
            },
            OrderToken = Guid.NewGuid().ToString(),
            OwnerType = "1",
            PrincipalName = "金羽童",
            PrincipalTel = "010-52596009",
            QuoteRegistrationNumbers = "29572253,28478196,29564540",
            SubjectType = "1"
        };
        
        var response = await client.CreateOrderAsync(PhoenixUri.TrademarkObjections, order);

        Assert.NotNull(response?.Data?.OrderNo);
        _testOutputHelper.WriteLine("订单号：{0}", response.Data.OrderNo);
    }


    /// <summary>
    /// 商标变更代理人，收件人
    /// </summary>
    [Fact]
    public async Task TestTrademarkChangeAgentReceiverOrderAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var order = new TrademarkChangeAgentReceiverOrder()
        {
            CurrentAgencyName = "天津梦知网",
            CurrentReceiverAddress = "北京市昌平区",
            CurrentReceiverName = "张三",
            CurrentPostCode = "100010",
            ApplicantAddress = "湖南省长沙市雨花区新建西路139号中江国际花城1栋113号门面",
            ApplicantName = "北京智果科技有限公司",
            AgentOrganTel = "020-87323188",
            AgentOrganConName = "王雪锋",
            ApplicantAttachments = new ApplicantAttachment[]
            {
                new(name:"委托证明.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name:"营业执照.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseSubjectQualification),
                new(name:"许可人身份证.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification),
                new(name:"被许可人营业执照.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),
                new(name:"变更证明文件.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChangeLicensor),
            },
            BrandInfos = new BrandInfo[]
            {
                new BrandInfo
                {
                    ApplicantName = "北京智果科技有限公司",
                    BrandRegisterNo = "22021485",
                    BrandName = "闻曦",
                    ImageUrl = "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
                    CategoryFlowStateName = "已注册",
                    FirstCgNo = "41",
                    CategoryNumber = "41",
                    FirstCgName = "教育娱乐",
                    Number = 10,
                    GoodsItems = new GoodsItem[]
                    {
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "培训",
                            Status = "0"
                        },
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "教育",
                            Status = "0"
                        },
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "安排和组织会议",
                            Status = "0"
                        },
                    }
                }
            },
            BookType = "1",
            CertificatesType = "0",
            Code = "101206",
            ContactEmail = "<EMAIL>",
            ContactName = "刘云金",
            ContactTel = "13593165788",
            Country = "中国大陆",
            IdCard = "9111234579902714XB",
            IsChangeAgencyName = 1,
            OrderToken = Guid.NewGuid().ToString(),
            OwnerType = "1",
            PrincipalName = "金羽童",
            PrincipalTel = "010-52596009",
            SubjectType = "1",
            SubmitType = 1
        };
        
        var response = await client.CreateOrderAsync(PhoenixUri.ChangeAgentReceiverOrder, order);

        Assert.NotNull(response?.Data?.OrderNo);
        _testOutputHelper.WriteLine("订单号：{0}", response.Data.OrderNo);
    }

    /// <summary>
    /// 商标无效宣告
    /// </summary>
    [Fact]
    public async Task TestTrademarkAnnulmentOrderAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var order = new TrademarkAnnulmentOrder()
        {
            ApplicantAddress = "湖南省长沙市雨花区新建西路139号中江国际花城1栋113号门面",
            ApplicantName = "北京智果科技有限公司",
            AgentOrganTel = "020-87323188",
            AgentOrganConName = "王雪锋",
            AgentPerson = "王雪峰",
            ApplicantAttachments = new ApplicantAttachment[]
            {
                new(name:"委托证明.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name:"营业执照.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseSubjectQualification),
                new(name:"相关说明文件.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.RelevantNotes),
                new(name:"其他委托书.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.OtherAttorney),
                new(name:"理由书.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Justification),
            },
            BrandInfos = new BrandInfo[]
            {
                new BrandInfo
                {
                    BrandRegisterNo = "22021485",
                    PreBrandRegisterNo = "8745448",
                    BrandName = "闻曦",
                    DeleteType = 1,
                    LogoUrl = "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
                    CategoryFlowStateName = "已注册",
                    FirstCgNo = "41",
                    FirstCgName = "教育娱乐",
                    Number = 10,
                    GoodsItems = new GoodsItem[]
                    {
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "培训",
                            Status = "0"
                        },
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "教育",
                            Status = "0"
                        },
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "安排和组织会议",
                            Status = "0"
                        },
                    }
                }
            },
            BookType = "1",
            CertificatesType = "0",
            Code = "101206",
            ContactEmail = "<EMAIL>",
            ContactName = "刘云金",
            ContactTel = "13593165788",
            Country = "中国大陆",
            IdCard = "9111234579902714XB",
            IsAgreeAddress = 0,
            IsAbsoluteReason = 0,
            IsSuppleEvidence = 0,
            LawProvisionList = new LawProvision[]
            {
                new LawProvision()
                {
                    LawId = 2,
                    LawName = "《商标法》第十五条",
                },
                new LawProvision()
                {
                    LawId = 6,
                    LawName = "《商标法》第十三条 第二款",
                }
            },
            OrderToken = Guid.NewGuid().ToString(),
            OwnerType = "1",
            PrincipalName = "金羽童",
            PrincipalTel = "010-52596009",
            QuoteBrandInfos = new QuoteBrandInfo[]
            {
                new QuoteBrandInfo()
                {
                    RegisterNumber = "16628106",
                    FirstCategoryNumber = "41",
                    ContactName = "北京优学教育科技有限公司",
                    ContactAddress = "北京市朝阳区西坝河西里28号英特公寓B1802室"
                },
                new QuoteBrandInfo()
                {
                    RegisterNumber = "16628100",
                    FirstCategoryNumber = "42",
                    ContactName = "北京优学教育科技有限公司",
                    ContactAddress = "北京市朝阳区西坝河西里28号英特公寓B1802室"
                }
            },
            SubjectType = "1"
        };
        
        var response = await client.CreateOrderAsync(PhoenixUri.InvalidationOrder, order);

        Assert.NotNull(response?.Data?.OrderNo);
        _testOutputHelper.WriteLine("订单号：{0}", response.Data.OrderNo);
    }

    /// <summary>
    /// 商标撤销3年
    /// </summary>
    [Fact]
    public async Task TestTrademarkWithdraw3OrderAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var order = new TrademarkWithdraw3Order()
        {
            ApplicantAddress = "湖南省长沙市雨花区新建西路139号中江国际花城1栋113号门面",
            ApplicantName = "北京智果科技有限公司",
            AgentOrganTel = "020-87323188",
            AgentOrganConName = "王雪锋",
            ApplicantAttachments = new ApplicantAttachment[]
            {
                new(name:"委托证明.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name:"营业执照.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseSubjectQualification),
                new(name:"理由书.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Justification),
                new(name:"证据目录.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.EvidenceLists),
                new(name:"证据内容.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.EvidenceContent),
            },
            BrandInfos = new BrandInfo[]
            {
                new BrandInfo
                {
                    BrandRegisterNo = "22021485",
                    PreBrandRegisterNo = "8745448",
                    BrandName = "闻曦",
                    DeleteType = 1,
                    LogoUrl = "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
                    CategoryFlowStateName = "已注册",
                    FirstCgNo = "41",
                    FirstCgName = "教育娱乐",
                    Number = 10,
                    GoodsItems = new GoodsItem[]
                    {
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "培训",
                            Status = "0"
                        },
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "教育",
                            Status = "0"
                        },
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "安排和组织会议",
                            Status = "0"
                        },
                    }
                }
            },
            BookType = "1",
            CertificatesType = "0",
            Code = "101206",
            ContactEmail = "<EMAIL>",
            ContactName = "刘云金",
            ContactTel = "13593165788",
            Country = "中国大陆",
            IdCard = "9111234579902714XB",
            OrderToken = Guid.NewGuid().ToString(),
            OwnerType = "1",
            PrincipalName = "金羽童",
            PrincipalTel = "010-52596009",
            SubjectType = "1"
        };
        
        var response = await client.CreateOrderAsync(PhoenixUri.WithdrawThreeYears, order);

        Assert.NotNull(response?.Data?.OrderNo);
        _testOutputHelper.WriteLine("订单号：{0}", response.Data.OrderNo);
    }

    /// <summary>
    /// 撤回商标注册申请
    /// </summary>
    [Fact]
    public async Task TestTrademarkWithdrawRegistrationOrderAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var order = new TrademarkWithdrawRegistrationOrder()
        {
            ApplicantAddress = "湖南省长沙市雨花区新建西路139号中江国际花城1栋113号门面",
            ApplicantName = "北京智果科技有限公司",
            AgentOrganTel = "020-87323188",
            AgentOrganConName = "王雪锋",
            ApplicantAttachments = new ApplicantAttachment[]
            {
                new(name:"委托证明.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name:"营业执照.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseSubjectQualification),
                new(name:"理由书.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Justification),
                new(name:"证据目录.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.EvidenceLists),
                new(name:"证据内容.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.EvidenceContent),
            },
            BrandInfos = new BrandInfo[]
            {
                new BrandInfo
                {
                    BrandRegisterNo = "22021485",
                    PreBrandRegisterNo = "8745448",
                    BrandName = "闻曦",
                    DeleteType = 1,
                    LogoUrl = "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
                    CategoryFlowStateName = "已注册",
                    FirstCgNo = "41",
                    FirstCgName = "教育娱乐",
                    Number = 10,
                    GoodsItems = new GoodsItem[]
                    {
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "培训",
                            Status = "0"
                        },
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "教育",
                            Status = "0"
                        },
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "安排和组织会议",
                            Status = "0"
                        },
                    }
                }
            },
            BookType = "1",
            CertificatesType = "0",
            Code = "101206",
            ContactEmail = "<EMAIL>",
            ContactName = "刘云金",
            ContactTel = "13593165788",
            Country = "中国大陆",
            IdCard = "9111234579902714XB",
            OrderToken = Guid.NewGuid().ToString(),
            OwnerType = "1",
            PrincipalName = "金羽童",
            PrincipalTel = "010-52596009",
            SubjectType = "1"
        };
        
        var response = await client.CreateOrderAsync(PhoenixUri.WithdrawRegistrationOrder, order);

        Assert.NotNull(response?.Data?.OrderNo);
        _testOutputHelper.WriteLine("订单号：{0}", response.Data.OrderNo);
    }

    /// <summary>
    /// 商标补发证书
    /// </summary>
    [Fact]
    public async Task TestTrademarkCertificationOrderAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var order = new TrademarkCertificationOrder()
        {
            ApplyReason = "申请补正理由",
            ApplicantAddress = "湖南省长沙市雨花区新建西路139号中江国际花城1栋113号门面",
            ApplicantName = "北京智果科技有限公司",
            AgentOrganTel = "020-87323188",
            AgentOrganConName = "王雪锋",
            ApplicantAttachments = new ApplicantAttachment[]
            {
                new(name:"委托证明.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name:"营业执照.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseSubjectQualification),
                new(name:"理由书.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),
                new(name:"证据目录.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChangeLicensor),
                new(name:"证据内容.pdf",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification),
            },
            BrandInfos = new BrandInfo[]
            {
                new BrandInfo
                {
                    BrandRegisterNo = "22021485",
                    PreBrandRegisterNo = "8745448",
                    BrandName = "闻曦",
                    DeleteType = 1,
                    LogoUrl = "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
                    CategoryFlowStateName = "已注册",
                    FirstCgNo = "41",
                    FirstCgName = "教育娱乐",
                    Number = 10,
                    GoodsItems = new GoodsItem[]
                    {
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "培训",
                            Status = "0"
                        },
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "教育",
                            Status = "0"
                        },
                        new GoodsItem()
                        {
                            Code = "4101",
                            Name = "安排和组织会议",
                            Status = "0"
                        },
                    }
                }
            },
            BookType = "1",
            CertificatesType = "0",
            Code = "101206",
            ContactEmail = "<EMAIL>",
            ContactName = "刘云金",
            ContactTel = "13593165788",
            Country = "中国大陆",
            IdCard = "9111234579902714XB",
            OrderToken = Guid.NewGuid().ToString(),
            OwnerType = "1",
            PrincipalName = "金羽童",
            PrincipalTel = "010-52596009",
            SubjectType = "1"
        };
        
        var response = await client.CreateOrderAsync(PhoenixUri.ReplenishCertificationOrder, order);

        Assert.NotNull(response?.Data?.OrderNo);
        _testOutputHelper.WriteLine("订单号：{0}", response.Data.OrderNo);
    }
    
     /// <summary>
    /// 商标变更下单
    /// </summary>
    [Fact]
    public async Task TestTrademarkChangeOrderAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var order = new TrademarkChangeOrder()
        {
            TransferType = "3",
            FormerApplicant = new DeliveryApplicantInfo()
            {
                ApplicantAddress = "北京市大萨达",
                ApplicantName = "北京梦知网科技有限公司",
                BookType = "1",
                CertificatesType = "1",
                Code = "100000",
                Country = "中国大陆",
                ApplicantEnglishAddress = string.Empty,
                SubjectType = "1",
                OwnerType = "0",
                IdCard = "111111111111111111",
            },
            CurrentApplicant = new DeliveryApplicantInfo()
            {
                ApplicantAddress = "北京市大萨达##范德萨发生的复审##范德萨发生的",
                ApplicantName = "常州兰利电器科技有限公司",
                BookType = "1",
                CertificatesType = "1",
                Code = "100000",
                Country = "中国大陆",
                SubjectType = "1",
                OwnerType = "0",
                IdCard = "111111111111111111",
            },
            OrderInfo = new OrderInfo()
            {
                AgentOrganConName = "王雪锋",
                AgentOrganId = "44224",
                AgentOrganName = "华进联合专利商标代理有限公司",
                AgentOrganTel = "020-87323188",
                ContactEmail = "<EMAIL>",
                ContactName = "wxf",
                ContactTel = "17610532682",
                PrincipalName = "发v",
                PrincipalTel = "15195026071",
                DomesticReceiverAddress = "h'j'mhjm和",
                DomesticReceiverCode = "476600",
                DomesticReceiverName = "市场"
            },
            Attachments = new ApplicantAttachment[]
            {
                new(name:"xx许可人委托书",
                uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                attachmentType:ApplicantAttachmentType.OfficialAttachment,
                ApplicantAttachmentSubType.Attorney),

                new(name:"xxx身份证",
                uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                attachmentType:ApplicantAttachmentType.OfficialAttachment,
                ApplicantAttachmentSubType.ChineseSubjectQualification),

                new(name:"XXX公司变更证明文件",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChangeLicensor),

                new(name:"XXX公司身份证明文件",
                    uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType:ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification),

            },
            BrandInfos = new List<BrandInfo>()
            {
                new BrandInfo()
                {
                    BrandRegisterNo = "45896679",
                    BrandName = "京瑞",
                    CategoryFlowStateName = "商标注册申请 | 申请收文",
                    FirstCgName = "社会服务",
                    FirstCgNo = "45",
                    Number = 1,
                    Info = new GoodsInfo()
                    {
                        Address = "北京市朝阳区广顺北大街五号院内32号内5017",
                        ReceiptNo = "45896679",
                        BrandRegisterNo = "45896679",
                        LogoUrl = "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
                        FirstCgNo = "45",
                        BrandName = "京瑞",
                        ApplicantName = "北京梦知网科技有限公司",
                        FlowListName = "商标注册申请 | 申请收文",
                        TypeCode = "45",
                        CategoryFlowStateName = "商标注册申请 | 申请收文",
                        FirstCgName = "社会服务",
                        GoodsList = new GoodsItem[]
                        {
                            new(){
                                Code =  "4506",
                                Name = "为法律咨询目的监控知识产权",
                                Status = "0"
                            }
                        },
                        GoodsText = "4506-为法律咨询目的监控知识产权 4506-向发明人提供知识产权咨询服务 4506-商标代理服务 4506-有关知识产权的顾问服务 4506-法律咨询 4506-版权管理 4506-知识产权代理服务 4506-知识产权咨询 4506-计算机软件许可咨询 4506-通过网站提供法律服务信息",
                        GoodsTextShow = "4506-为法律咨询目的监控知识产权 4506-向发明人提供知识产权咨询服务 4506-商标代理服务 ...",
                        ProcessName = "已注册",
                    }
                }
            },

            OrderToken = Guid.NewGuid().ToString(),

        };
        var response = await client.CreateOrderAsync(PhoenixUri.ChangeOrder, order);

        Assert.NotNull(response?.Data?.OrderNo);
        _testOutputHelper.WriteLine("订单号：{0}", response.Data.OrderNo);
    }

    /// <summary>
    /// 商标驳回复审申请
    /// </summary>
    [Fact]
    public async Task TestTrademarkRefuseReexaminationOrderAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var order = new TrademarkRefuseReexaminationOrder()
        {
            SubmitType = 1,
            IsReplenish = 1,
            AgentOrganName = "王雪锋",
            FactReason = "阐述事实与理由",
            AgentOrganTel = "020-87323188",
            AgentPerson = "代理机构签名",
            AgentOrganConName = "王雪锋",
            OrderToken = Guid.NewGuid().ToString(),
            RefuseNotification = new Attachments()
            {
                AttachmentType = 2,
                SubName = "驳回通知书",
                AttachmentPic =
                    "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                AttachmentName = "交官",
                SubType = 203
            },
            ApplicationInfo = new RefuseReexaminationApplicationInfo()
            {
                BookType = "1",
                CertificatesType = "0",
                ApplicantAddress = "新疆省伊犁州霍尔果斯市友谊西路24号亚欧国际小区2幢2221室-423",
                IdCard = "91654004MA778X43XF",
                ContactEmail = "<EMAIL>",
                ContactName = "王雪锋",
                ContactTel = "17610532682",
                Code = "835000",
                OwnerType = "1",
                Country = "中国大陆",
                IsChangeName = "7",
                ChangeName = "tester",
                ApplicantName = "霍尔果斯钽云信息科技有限公司"
            },
            BrandInfos = new BrandInfo[]
            {
                new BrandInfo
                {
                    BrandRegisterNo = "30210700",
                    BrandName = "权大师",
                    LogoUrl = "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
                    ApplicantName = "霍尔果斯钽云信息科技有限公司",
                    CategoryFlowStateName = "初审公告",
                    FirstCgNo = "6",
                    FirstCgName = "金属材料"
                }
            },
            ApplicantAttachments = new ApplicantAttachment[]
            {
                new(name: "xx许可人委托书",
                    uri: "https://files.quandashi.com/proxy/2022/08/05/c50303ab-70c5-473b-a50d-b10b8d79ddd4.jpg",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),

                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseSubjectQualification),

                new(name: "变更证明文件",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChangeLicensor),

                new(name: "事实与理由",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Justification),

                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseSubjectQualification),

                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseSubjectQualification),

                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseSubjectQualification),
            },
        };


        var response = await client.CreateOrderAsync(PhoenixUri.TrademarkRefuseReexamination, order);

        Assert.NotNull(response?.Data?.OrderNo);

        _testOutputHelper.WriteLine("订单号：{0}", response.Data.OrderNo);
    }

    /// <summary>
    /// 撤回转让/转移
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task TestWithdrawTrademarkAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var withdrawTrademark = new WithdrawTrademarkParameters
        {
            AgentOrganConName = "陈俐俐",
            AgentOrganId = "44224",
            AgentOrganName = "华进联合专利商标代理有限公司",
            AgentOrganTel = "020-87323188",
            ApplicantAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantEnglishAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantName = "深圳市渔米粥饮食管理有限公司",
            ApplyProve = "没有任何理由",
            AttachmentsList = new ApplicantAttachment[]
            {
                new(name: "xx许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification)
                ,new(name: "xx被许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorneyed),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),
                new(name: "同意撤回声明文件",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.AgreeWithdrawDeclarationDocument)
            },
            BookType = ApplicantBookType.ChinaMainland.Code,
            BrandTransferParamList = new List<BrandTransferParam>()
            {
                new BrandTransferParam
                {
                    brandRegisterNo = "28326310",
                    firstCgNo = 33,
                    brandName = "青莲诗仙魂",
                    applicantName = "蒋可",
                    categoryFlowStateName = "已注册",
                    intCls = 33,
                    firstCgName = "酒",
                    brandTeam = new Brandteam[]
                    {
                        new()
                        {
                            code = "3301",
                            name = "威士忌",
                            status = "0"
                        }
                    },
                    imageUrl = "eea17a11/c8cd16e3/fe4f5508/7166a9e5/logo_middle.jpg",
                    number = 10
                }
            },
            CertificatesType = 1,
            Code = "410000",
            ContactEmail = "<EMAIL>",
            ContactName = "邹永坤",
            ContactTel = "13689595233",
            Country = "中国大陆",
            IdCard = "******************",
            OrderToken = Guid.NewGuid().ToString(),
            OwnerType = 0,
            PrincipalName = "李白",
            PrincipalTel = "010-52596009",
            SubjectType = 1,
            AssigneeInfoList = new AssigneeInfo()
            {

                country = "中国大陆",
                ownerType = 1,
                bookType = ApplicantBookType.ChinaMainland.Code,
                applicantName = "福建尚田贸易有限公司",
                applicantEnglishName = "",
                applicantAddress = "福建省宁德市霞浦县松港街道东兴社区长溪路8号九龙街D幢D004号",
                applicantEnglishAddress = "",
                prov = 1,
                city = 64,
                area = 1,
                code = "102200",
                certificatesType = 1,
                newApplicantName = "",
                idCard = "",
                unifiedSocialCreditCode = "91350921050303325J",
                post = "asdfsdf",
                legalPerson = "商标局改版",
                subjectType = 1
            },
            BizSubjectType = 1,
        };
        var response = await client.ControlTrademarkAsync(PhoenixUri.WithdrawTrademark, withdrawTrademark);

        Assert.NotNull(response?.Data?.OrderNo);
    }

    /// <summary>
    /// 商标更正
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task TestCorrectionTrademarkAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var CorrectionTrademark = new CorrectionTrademarkParameters()
        {
            AgentOrganConName = "陈俐俐",
            AgentOrganId = "44224",
            AgentOrganName = "华进联合专利商标代理有限公司",
            AgentOrganTel = "020-87323188",
            ApplicantAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantEnglishAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantName = "深圳市渔米粥饮食管理有限公司",
            AttachmentsList = new ApplicantAttachment[]{
                 new(name:"xx许可人委托书",
                 uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                 attachmentType:ApplicantAttachmentType.OfficialAttachment,
                 ApplicantAttachmentSubType.Attorney),
                 new(name:"xxx身份证",
                 uri:"https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                 attachmentType:ApplicantAttachmentType.OfficialAttachment,
                 ApplicantAttachmentSubType.ChineseIdentification)},
            BookType = ApplicantBookType.ChinaMainland.Code,
            BrandTransferParamList = new List<BrandTransferParam>()
             {
                 new BrandTransferParam
                 {
                     brandRegisterNo = "28326310",
                     firstCgNo = 33,
                     brandName = "青莲诗仙魂",
                     applicantName = "蒋可",
                     categoryFlowStateName = "已注册",
                     intCls = 33,
                     firstCgName = "酒",
                     brandTeam = new Brandteam[]
                     {
                         new(){
                             code =  "3301",
                             name = "威士忌",
                             status = "0"
                         }
                     },
                     imageUrl = "eea17a11/c8cd16e3/fe4f5508/7166a9e5/logo_middle.jpg",
                     number = 10
                 }
             },
            CertificatesType = 3,
            Code = "410000",
            ContactEmail = "<EMAIL>",
            ContactName = "邹永坤",
            ContactTel = "13689595233",
            Country = "中国",
            IdCard = "685373386",
            OrderToken = Guid.NewGuid().ToString(),
            OwnerType = 0,
            PrincipalName = "李白",
            PrincipalTel = "010-52596009",
            SubjectType = 1,
        };
        var response = await client.ControlTrademarkAsync(PhoenixUri.CorrectionTrademark, CorrectionTrademark);

        Assert.NotNull(response?.Data?.OrderNo);
    }

    /// <summary>
    /// 撤回许可备案
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task TestWithdrawalLicenseFilingAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var WithdrawalLicenseFiling = new WithdrawalLicenseFilingParameters
        {
            AgentOrganConName = "陈俐俐",
            AgentOrganId = "44224",
            AgentOrganName = "华进联合专利商标代理有限公司",
            AgentOrganTel = "020-87323188",
            ApplicantAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantEnglishAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantName = "深圳市渔米粥饮食管理有限公司",
            ApplyProve = "没有任何理由",
            AttachmentsList = new ApplicantAttachment[]
            {
                new(name: "xx许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification)
                ,new(name: "xx被许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorneyed),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),

            },
            BookType = ApplicantBookType.ChinaMainland.Code,
            BrandTransferParamList = new List<BrandTransferParam>()
            {
                new BrandTransferParam
                {
                    brandRegisterNo = "28326310",
                    firstCgNo = 33,
                    brandName = "青莲诗仙魂",
                    applicantName = "蒋可",
                    categoryFlowStateName = "已注册",
                    intCls = 33,
                    firstCgName = "酒",
                    brandTeam = new Brandteam[]
                    {
                        new()
                        {
                            code = "3301",
                            name = "威士忌",
                            status = "0"
                        }
                    },
                    imageUrl = "eea17a11/c8cd16e3/fe4f5508/7166a9e5/logo_middle.jpg",
                    number = 10
                }
            },
            CertificatesType = 1,
            Code = "410000",
            ContactEmail = "<EMAIL>",
            ContactName = "邹永坤",
            ContactTel = "13689595233",
            Country = "中国大陆",
            IdCard = "******************",
            OrderToken = Guid.NewGuid().ToString(),
            OwnerType = 0,
            PrincipalName = "李白",
            PrincipalTel = "010-52596009",
            SubjectType = 1,
            AssigneeInfoList = new AssigneeInfo()
            {

                country = "中国大陆",
                ownerType = 1,
                bookType = ApplicantBookType.ChinaMainland.Code,
                applicantName = "福建尚田贸易有限公司",
                applicantEnglishName = "",
                applicantAddress = "福建省宁德市霞浦县松港街道东兴社区长溪路8号九龙街D幢D004号",
                applicantEnglishAddress = "",
                prov = 1,
                city = 64,
                area = 1,
                code = "102200",
                certificatesType = 1,
                newApplicantName = "",
                idCard = "",
                unifiedSocialCreditCode = "91350921050303325J",
                post = "asdfsdf",
                legalPerson = "商标局改版",
                subjectType = 1

            },
        };
        var response = await client.ControlTrademarkAsync(PhoenixUri.WithdrawalLicenseFiling, WithdrawalLicenseFiling);

        Assert.NotNull(response?.Data?.OrderNo);
    }

    /// <summary>
    /// 撤回变更代理人
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task TestWithdrawalChangeAgentAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var WithdrawalChangeAgent = new WithdrawalChangeAgentParameters
        {
            AgentOrganConName = "陈俐俐",
            AgentOrganId = "44224",
            AgentOrganName = "华进联合专利商标代理有限公司",
            AgentOrganTel = "020-87323188",
            ApplicantAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantEnglishAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantName = "深圳市渔米粥饮食管理有限公司",
            ApplyProve = "没有任何理由",
            AttachmentsList = new ApplicantAttachment[]
            {
                new(name: "xx许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification)
                ,new(name: "xx被许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorneyed),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),

            },
            BookType = ApplicantBookType.ChinaMainland.Code,
            BrandTransferParamList = new List<BrandTransferParam>()
            {
                new BrandTransferParam
                {
                    brandRegisterNo = "28326310",
                    firstCgNo = 33,
                    brandName = "青莲诗仙魂",
                    applicantName = "蒋可",
                    categoryFlowStateName = "已注册",
                    intCls = 33,
                    firstCgName = "酒",
                    brandTeam = new Brandteam[]
                    {
                        new()
                        {
                            code = "3301",
                            name = "威士忌",
                            status = "0"
                        }
                    },
                    imageUrl = "eea17a11/c8cd16e3/fe4f5508/7166a9e5/logo_middle.jpg",
                    number = 10
                }
            },
            CertificatesType = 1,
            Code = "410000",
            ContactEmail = "<EMAIL>",
            ContactName = "邹永坤",
            ContactTel = "13689595233",
            Country = "中国大陆",
            IdCard = "******************",
            OrderToken = Guid.NewGuid().ToString(),
            OwnerType = 0,
            PrincipalName = "李白",
            PrincipalTel = "010-52596009",
            SubjectType = 1,
        };
        var response = await client.ControlTrademarkAsync(PhoenixUri.WithdrawalChangeAgent, WithdrawalChangeAgent);

        Assert.NotNull(response?.Data?.OrderNo);
    }

    /// <summary>
    /// 变更许可人
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task TestChangeLicensorAgentAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var ChangeLicensor = new ChangeLicensorParameters
        {
            AgentOrganConName = "陈俐俐",
            AgentOrganId = "44224",
            AgentOrganName = "华进联合专利商标代理有限公司",
            AgentOrganTel = "020-87323188",
            ApplicantAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantEnglishAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantName = "深圳市渔米粥饮食管理有限公司",
            AssigneeInfoList = new AssigneeInfo()
            {
                country = "中国大陆",
                ownerType = 1,
                bookType = ApplicantBookType.ChinaMainland.Code,
                applicantName = "福建尚田贸易有限公司",
                applicantEnglishName = "",
                applicantAddress = "福建省宁德市霞浦县松港街道东兴社区长溪路8号九龙街D幢D004号",
                applicantEnglishAddress = "",
                prov = 1,
                city = 64,
                area = 1,
                code = "102200",
                certificatesType = 1,
                newApplicantName = "",
                idCard = "",
                unifiedSocialCreditCode = "91350921050303325J",
                post = "asdfsdf",
                legalPerson = "商标局改版",
                subjectType = 1
            },
            BeforeChangeLicenseeNameCh = "小学生",
            BeforeChangeLicenseeNameEn = "sm",
            BeforeChangeLicensorNameCh = "小学生",
            BeforeChangeLicensorNameEn = "sm",
            AttachmentsList = new ApplicantAttachment[]
            {
                new(name: "xx许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification),
                new(name: "xx被许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorneyed),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),
                new(name: "变更证明文件",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChangeLicensor),

            },
            BookType = ApplicantBookType.ChinaMainland.Code,
            BrandTransferParamList = new List<BrandTransferParam>()
            {
                new BrandTransferParam
                {
                    brandRegisterNo = "28326310",
                    firstCgNo = 33,
                    brandName = "青莲诗仙魂",
                    applicantName = "蒋可",
                    categoryFlowStateName = "已注册",
                    intCls = 33,
                    firstCgName = "酒",
                    brandTeam = new Brandteam[]
                    {
                        new()
                        {
                            code = "3301",
                            name = "威士忌",
                            status = "0"
                        }
                    },
                    imageUrl = "eea17a11/c8cd16e3/fe4f5508/7166a9e5/logo_middle.jpg",
                    number = 10
                }
            },
            AfterChangeLicenseeCardId = "大学生",
            AfterChangeLicenseeCountry = "大学生",
            AfterChangeLicenseeNameCh = "大学生",
            AfterChangeLicenseeNameEn = "大学生",
            CertificatesType = 1,
            Code = "410000",
            ContactEmail = "<EMAIL>",
            ContactName = "邹永坤",
            ContactTel = "13689595233",
            Country = "中国大陆",
            IdCard = "******************",
            LicenceChangeType = "1,2",
            OrderToken = Guid.NewGuid()
                .ToString(),
            OwnerType = 0,
            PrincipalName = "李白",
            PrincipalTel = "010-52596009",
            SubjectType = 1,
            SubmitType = 0,
        };
        var response = await client.ControlTrademarkAsync(PhoenixUri.ChangeLicensor, ChangeLicensor);

        Assert.NotNull(response?.Data?.OrderNo);
    }

    /// <summary>
    /// 删减商品项或服务项目申请
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task TestDeleteProductAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var DeleteProduct = new DeleteProductParameters
        {
            AgentOrganConName = "陈俐俐",
            AgentOrganId = "44224",
            AgentOrganName = "华进联合专利商标代理有限公司",
            AgentOrganTel = "020-87323188",
            ApplicantAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantEnglishAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantName = "深圳市渔米粥饮食管理有限公司",
            AttachmentsList = new ApplicantAttachment[]
            {
                new(name: "xx许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification),
                new(name: "xx被许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorneyed),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),
                new(name: "变更证明文件",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChangeLicensor),

            },
            BookType = ApplicantBookType.ChinaMainland.Code,
            BrandTransferParamList = new List<BrandTransferParam>()
            {
                new BrandTransferParam
                {
                    brandRegisterNo = "28326310",
                    firstCgNo = 33,
                    brandName = "青莲诗仙魂",
                    applicantName = "蒋可",
                    categoryFlowStateName = "已注册",
                    intCls = 33,
                    firstCgName = "酒",
                    brandTeam = new Brandteam[]
                    {
                        new()
                        {
                            code = "3301",
                            name = "威士忌",
                            status = "0"
                        }
                    },
                    imageUrl = "eea17a11/c8cd16e3/fe4f5508/7166a9e5/logo_middle.jpg",
                    number = 10
                }
            },
            CertificatesType = 1,
            Code = "410000",
            ContactEmail = "<EMAIL>",
            ContactName = "邹永坤",
            ContactTel = "13689595233",
            Country = "中国大陆",
            IdCard = "******************",
            OrderToken = Guid.NewGuid()
                .ToString(),
            OwnerType = 0,
            PrincipalName = "李白",
            PrincipalTel = "010-52596009",
            SubjectType = 1,
        };
        var response = await client.ControlTrademarkAsync(PhoenixUri.DeleteProduct, DeleteProduct);

        Assert.NotNull(response?.Data?.OrderNo);
    }

    /// <summary>
    /// 撤回商标续展申请
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task TestWithdrawalTrademarkRenewalAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var WithdrawalTrademarkRenewal = new WithdrawalTrademarkRenewalParameters
        {
            AgentOrganConName = "陈俐俐",
            AgentOrganId = "44224",
            AgentOrganName = "华进联合专利商标代理有限公司",
            AgentOrganTel = "020-87323188",
            ApplicantAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantEnglishAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantName = "深圳市渔米粥饮食管理有限公司",
            AttachmentsList = new ApplicantAttachment[]
            {
                new(name: "xx许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification),
                new(name: "xx被许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorneyed),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),
                new(name: "变更证明文件",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChangeLicensor),

            },
            BookType = ApplicantBookType.ChinaMainland.Code,
            BrandTransferParamList = new List<BrandTransferParam>()
            {
                new BrandTransferParam
                {
                    brandRegisterNo = "28326310",
                    firstCgNo = 33,
                    brandName = "青莲诗仙魂",
                    applicantName = "蒋可",
                    categoryFlowStateName = "已注册",
                    intCls = 33,
                    firstCgName = "酒",
                    brandTeam = new Brandteam[]
                    {
                        new()
                        {
                            code = "3301",
                            name = "威士忌",
                            status = "0"
                        }
                    },
                    imageUrl = "eea17a11/c8cd16e3/fe4f5508/7166a9e5/logo_middle.jpg",
                    number = 10
                }
            },
            CertificatesType = 1,
            Code = "410000",
            ContactEmail = "<EMAIL>",
            ContactName = "邹永坤",
            ContactTel = "13689595233",
            Country = "中国大陆",
            IdCard = "******************",
            OrderToken = Guid.NewGuid()
                .ToString(),
            OwnerType = 0,
            PrincipalName = "李白",
            PrincipalTel = "010-52596009",
            SubjectType = 1,
            ApplyProve = "没有理由的"
        };
        var response = await client.ControlTrademarkAsync(PhoenixUri.WithdrawalTrademarkRenewal, WithdrawalTrademarkRenewal);

        Assert.NotNull(response?.Data?.OrderNo);
    }

    /// <summary>
    /// 补发变更/转让/续展证明申请
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task TestReissueChangeCertificateAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var ReissueChangeCertificateParameters = new ReissueChangeCertificateParameters
        {
            AgentOrganConName = "陈俐俐",
            AgentOrganId = "44224",
            AgentOrganName = "华进联合专利商标代理有限公司",
            AgentOrganTel = "020-87323188",
            ApplicantAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantEnglishAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantName = "深圳市渔米粥饮食管理有限公司",
            AttachmentsList = new ApplicantAttachment[]
            {
                new(name: "xx许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification),
                new(name: "xx被许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorneyed),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),
                new(name: "变更证明文件",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChangeLicensor),

            },
            BookType = ApplicantBookType.ChinaMainland.Code,
            BrandRenewalStopTime = null,
            BrandTransferParamList = new List<BrandTransferParam>()
            {
                new BrandTransferParam
                {
                    brandRegisterNo = "28326310",
                    firstCgNo = 33,
                    brandName = "青莲诗仙魂",
                    applicantName = "蒋可",
                    categoryFlowStateName = "已注册",
                    intCls = 33,
                    firstCgName = "酒",
                    brandTeam = new Brandteam[]
                    {
                        new()
                        {
                            code = "3301",
                            name = "威士忌",
                            status = "0"
                        }
                    },
                    imageUrl = "eea17a11/c8cd16e3/fe4f5508/7166a9e5/logo_middle.jpg",
                    number = 10
                }
            },
            CertificatesType = 1,
            Code = "410000",
            ContactEmail = "<EMAIL>",
            ContactName = "邹永坤",
            ContactTel = "13689595233",
            Country = "中国大陆",
            IdCard = "******************",
            OrderToken = Guid.NewGuid()
                .ToString(),
            OwnerType = 0,
            PrincipalName = "李白",
            PrincipalTel = "010-52596009",
            ReplacementType = 2,
            SubjectType = 1,
            ToName = "陈受让",
            FromName = "陈转让"
        };
        var response = await client.ControlTrademarkAsync(PhoenixUri.ReissueChangeCertificate, ReissueChangeCertificateParameters);

        Assert.NotNull(response?.Data?.OrderNo);
    }

    /// <summary>
    /// 许可提前终止备案
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task TestEarlyTerminationLicenseAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var EarlyTerminationLicense = new EarlyTerminationLicenseParameters
        {
            AgentOrganConName = "陈俐俐",
            AgentOrganId = "44224",
            AgentOrganName = "华进联合专利商标代理有限公司",
            AgentOrganTel = "020-87323188",
            ApplicantAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantEnglishAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantName = "深圳市渔米粥饮食管理有限公司",
            AssigneeInfoList = new AssigneeInfo()
            {
                country = "中国大陆",
                ownerType = 1,
                bookType = ApplicantBookType.ChinaMainland.Code,
                applicantName = "福建尚田贸易有限公司",
                applicantEnglishName = "",
                applicantAddress = "福建省宁德市霞浦县松港街道东兴社区长溪路8号九龙街D幢D004号",
                applicantEnglishAddress = "",
                prov = 1,
                city = 64,
                area = 1,
                code = "102200",
                certificatesType = 1,
                newApplicantName = "",
                idCard = "",
                unifiedSocialCreditCode = "91350921050303325J",
                post = "asdfsdf",
                legalPerson = "商标局改版",
                subjectType = 1
            },
            AttachmentsList = new ApplicantAttachment[]
            {
                new(name: "xx许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification),
                new(name: "xx被许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorneyed),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),
                new(name: "变更证明文件",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChangeLicensor),

            },
            BookType = ApplicantBookType.ChinaMainland.Code,
            BrandTransferParamList = new List<BrandTransferParam>()
            {
                new BrandTransferParam
                {
                    brandRegisterNo = "28326310",
                    firstCgNo = 33,
                    brandName = "青莲诗仙魂",
                    applicantName = "蒋可",
                    categoryFlowStateName = "已注册",
                    intCls = 33,
                    firstCgName = "酒",
                    brandTeam = new Brandteam[]
                    {
                        new()
                        {
                            code = "3301",
                            name = "威士忌",
                            status = "0"
                        }
                    },
                    imageUrl = "eea17a11/c8cd16e3/fe4f5508/7166a9e5/logo_middle.jpg",
                    number = 10
                }
            },
            CertificatesType = 1,
            Code = "410000",
            ContactEmail = "<EMAIL>",
            ContactName = "邹永坤",
            ContactTel = "13689595233",
            Country = "中国大陆",
            EarlyEndDate = "2023-03-06",
            EarlyEndReasons = "没有一点点理由",
            IdCard = "******************",
            OrderToken = Guid.NewGuid().ToString(),
            OwnerType = 0,
            PrincipalName = "李白",
            PrincipalTel = "010-52596009",
            SubjectType = 1,
            SubmitType = 0,
        };
        var response = await client.ControlTrademarkAsync(PhoenixUri.EarlyTerminationLicense, EarlyTerminationLicense);

        Assert.NotNull(response?.Data?.OrderNo);
    }

    /// <summary>
    /// 商标许可备案
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task TestTrademarkLicenseFilingAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var order = new TrademarkLicenseFilingOrder
        {
            AgentOrganConName = "谢福光",
            AgentOrganId = "44224",
            AgentOrganName = "华进联合专利商标代理有限公司",
            AgentOrganTel = "020-87323188",
            ApplicantAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantEnglishAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantName = "深圳市渔米粥饮食管理有限公司",
            Licensee = new DeliveryApplicantInfo()
            {
                Country = "中国大陆",
                OwnerType = "1",
                BookType = ApplicantBookType.ChinaMainland.Code.ToString(),
                ApplicantName = "福建尚田贸易有限公司",
                ApplicantAddress = "福建省宁德市霞浦县松港街道东兴社区长溪路8号九龙街D幢D004号",
                Code = "102200",
                CertificatesType = "1",
                IdCard = "",
                UnifiedSocialCreditCode = "91350921050303325J",
                Post = "asdfsdf",
                LegalPerson = "商标局改版",
                SubjectType = "1"
            },
            ApplicantAttachments = new ApplicantAttachment[]
            {
                new(name: "xx许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification),
                new(name: "xx被许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorneyed),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),
                new(name: "变更证明文件",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChangeLicensor),

            },
            BookType = ApplicantBookType.ChinaMainland.Code.ToString(),
            BrandInfos = new List<BrandInfo>()
            {
                new BrandInfo
                {
                    BrandRegisterNo = "28326310",
                    FirstCgNo = "33",
                    BrandName = "青莲诗仙魂",
                    ApplicantName = "蒋可",
                    CategoryFlowStateName = "已注册",
                    CategoryNumber = "33",
                    FirstCgName = "酒",
                    GoodsItems = new GoodsItem[]
                    {
                        new()
                        {
                            Code = "3301",
                            Name = "威士忌",
                            Status = "0"
                        }
                    },
                    ImageUrl = "eea17a11/c8cd16e3/fe4f5508/7166a9e5/logo_middle.jpg",
                    Number = 10
                }
            },
            CertificatesType = "1",
            Code = "410000",
            ContactEmail = "<EMAIL>",
            ContactName = "邹永坤",
            ContactTel = "13689595233",
            ContractEffectiveDate = DateOnly.FromDateTime(DateTime.Now),
            ContractTerminationDate = DateOnly.FromDateTime(DateTime.Now.AddDays(1)),
            Country = "中国大陆",
            IdCard = "******************",
            OrderToken = Guid.NewGuid()
                .ToString(),
            OwnerType = "0",
            PrincipalName = "李白",
            PrincipalTel = "010-52596009",
            SubjectType = "1",
        };
        var response = await client.CreateOrderAsync(PhoenixUri.TrademarkLicenseFiling, order);

        Assert.NotNull(response?.Data?.OrderNo);
    }

    /// <summary>
    /// 出具优先权证明申请
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task TestIssuePriorityCertificateAsync()
    {
        var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var clientFactory = provider.GetRequiredService<PhoenixClientFactory>();
        var client = clientFactory.CreateClient("AcipKey1");

        var IssuePriorityCertificate = new IssuePriorityCertificateParameters
        {
            AgentOrganConName = "陈俐俐",
            AgentOrganId = "44224",
            AgentOrganName = "华进联合专利商标代理有限公司",
            AgentOrganTel = "020-87323188",
            ApplicantAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantEnglishAddress = "罗湖区春风路置地逸轩裙楼一层A11",
            ApplicantName = "深圳市渔米粥饮食管理有限公司",
            ApplyProve = "申请补证理由",
            AttachmentsList = new ApplicantAttachment[]
            {
                new(name: "xx许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorney),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification),
                new(name: "xx被许可人委托书",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.Attorneyed),
                new(name: "xxx身份证",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.TransfereeSubjectQualification),
                new(name: "变更证明文件",
                    uri: "https://files.quandashi.com/license/2022/06/27/010d0e71-9edc-4c31-abec-d0e99359b924.pdf",
                    attachmentType: ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChangeLicensor),

            },
            BookType = ApplicantBookType.ChinaMainland.Code,
            BrandTransferParamList = new List<BrandTransferParam>()
            {
                new BrandTransferParam
                {
                    brandRegisterNo = "28326310",
                    firstCgNo = 33,
                    brandName = "青莲诗仙魂",
                    applicantName = "蒋可",
                    categoryFlowStateName = "已注册",
                    intCls = 33,
                    firstCgName = "酒",
                    brandTeam = new Brandteam[]
                    {
                        new()
                        {
                            code = "3301",
                            name = "威士忌",
                            status = "0"
                        }
                    },
                    imageUrl = "eea17a11/c8cd16e3/fe4f5508/7166a9e5/logo_middle.jpg",
                    number = 10
                }
            },
            CertificatesType = 1,
            Code = "410000",
            ContactEmail = "<EMAIL>",
            ContactName = "邹永坤",
            ContactTel = "13689595233",
            Country = "中国大陆",
            IdCard = "******************",
            OrderToken = Guid.NewGuid()
                .ToString(),
            OwnerType = 0,
            PrincipalName = "李白",
            PrincipalTel = "010-52596009",
            SubjectType = 1,
            SubmitType = 0,
        };
        var response = await client.ControlTrademarkAsync(PhoenixUri.IssuePriorityCertificate, IssuePriorityCertificate);

        Assert.NotNull(response?.Data?.OrderNo);
    }


}