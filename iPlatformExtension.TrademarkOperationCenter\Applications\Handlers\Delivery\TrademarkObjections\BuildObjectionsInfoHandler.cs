﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkObjections;

internal sealed class BuildObjectionsInfoHandler(
    IDeliveryOtherInfoRepository otherInfoRepository,
    IDeliveryLawBasisRepository lawBasisRepository,
    IHttpContextAccessor httpContextAccessor,
    IMapper mapper) : 
    IMatchTrademarkProcCommandHandler<BuildOtherInfoCommand>
{
    public async Task HandleAsync(BuildOtherInfoCommand notification, CancellationToken cancellationToken)
    {
        var procInfo = notification.ProcInfo;
        var operatorId = (httpContextAccessor.HttpContext?.User).GetUserId();

        var otherInfo = await otherInfoRepository.GetAsync(procInfo.ProcId, cancellationToken) ?? new DeliOtherInfo()
        {
            ProcId = procInfo.ProcId,
            CreationTime = DateTime.Now,
            Creator = operatorId
        };

        otherInfo.ReservationOfSupplementaryMaterial = procInfo.ReservationOfSupplementaryMaterial;
        otherInfo.TrademarkNiceClasses = procInfo.TrademarkNiceClasses;
        otherInfo.CitedRegisterNumbers = procInfo.CitedRegisterNumbers;
        otherInfo.CtrlProcMark = procInfo.CtrlProcMark;
        otherInfo.Updater = operatorId;
        otherInfo.UpdateTime = DateTime.Now;

        await otherInfoRepository.InsertOrUpdateAsync(otherInfo, cancellationToken);

        var procLawBasis = procInfo.LawBasis ?? Array.Empty<TrademarkLawBasis>();
        if (procLawBasis.Count > 0)
        {
            var deliveryLawBasis = mapper.Map<List<DeliveryLawBasis>>(procLawBasis);
            for (var i = 0; i < deliveryLawBasis.Count; i++)
            {
                var basis = deliveryLawBasis[i];
                basis.CreationTime = DateTime.Now;
                basis.Creator = operatorId;
                basis.Updater = operatorId;
                basis.UpdateTime = DateTime.Now;
            }

            await lawBasisRepository.InsertAsync(deliveryLawBasis, cancellationToken);
        }
    }

    public string CtrlProcId => CtrlProcIds.TrademarkObjections;

    public IEnumerable<string> CaseDirections => [CaseDirection.II];
}