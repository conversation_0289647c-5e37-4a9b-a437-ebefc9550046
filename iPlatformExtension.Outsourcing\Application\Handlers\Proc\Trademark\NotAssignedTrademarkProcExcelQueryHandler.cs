﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc.Trademark;

internal sealed class NotAssignedTrademarkProcExcelQueryHandler(
    IPublisher publisher,
    IFreeSql<PlatformFreeSql> freeSql,
    NotAssignedProcQueryContextFactory contextFactory)
    : IRequestHandler<NotAssignedTrademarkProcExcelQuery, IEnumerable<NotAssignedTrademarkProcExportDto>>
{
    public async Task<IEnumerable<NotAssignedTrademarkProcExportDto>> Handle(NotAssignedTrademarkProcExcelQuery request, CancellationToken cancellationToken)
    {
        var (authorizationType, keyword) = request;

        var context = await contextFactory.CreateAsync(authorizationType, cancellationToken);
        if (context is null)
        {
            return [];
        }
        await publisher.Publish(context, cancellationToken);
        
        var caseIdSet = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        if (!string.IsNullOrWhiteSpace(keyword))
        {
            caseIdSet.UnionWith(await freeSql.Select<CaseInfo>().WithLock()
                .Where(info => info.Volume.StartsWith(keyword))
                .ToListAsync(info => info.Id, cancellationToken));
            caseIdSet.UnionWith(await freeSql.Select<CaseInfo>().WithLock()
                .Where(info => info.RegisterNo.StartsWith(keyword))
                .ToListAsync(info => info.Id, cancellationToken));
            caseIdSet.UnionWith(await freeSql.Select<CaseInfo>().WithLock()
                .Where(info => info.AppNo.StartsWith(keyword))
                .ToListAsync(info => info.Id, cancellationToken));
            caseIdSet.UnionWith(await freeSql.Select<CaseInfo>().WithLock()
                .Where(info => info.Customer.CustomerFullName.Contains(keyword))
                .ToListAsync(info => info.Id, cancellationToken));
        }
        
        var ctrlProcIdSet = context.CtrlProcIds;
        var ctrlProcIds = await freeSql.Select<BasCtrlProc>()
            .Where(basCtrlProc => basCtrlProc.CtrlProcZhCn.Contains(keyword!))
            .ToListAsync(basCtrlProc => basCtrlProc.CtrlProcId, cancellationToken);
        ctrlProcIdSet.IntersectWith(ctrlProcIds);

        var results = await context.Query
            .WhereIf(!string.IsNullOrWhiteSpace(keyword) && caseIdSet.Count <= 0, info => ctrlProcIdSet.Contains(info.CtrlProcId))
            .WhereIf(caseIdSet.Count > 0, info => caseIdSet.Contains(info.CaseId))
            .ToListAsync(info => new NotAssignedTrademarkProcExportDto
            {
                ProcId = info.ProcId,
                Volume = info.CaseInfo.Volume,
                ProcNo = info.ProcNo,
                ProcName = info.CtrlProcId,
                Undertaker = info.UndertakeUserId ?? string.Empty,
                CaseName = info.CaseInfo.CaseName,
                ApplicationChannel = info.CaseInfo.ApplyChannel ?? string.Empty,
                CaseDirection = info.CaseInfo.CaseDirection,
                Country = info.CaseInfo.CountryId ?? string.Empty,
                CustomerId = info.CaseInfo.CustomerId,
            }, cancellationToken);

        await publisher.Publish(new NotAssignedProcExportResultNotification(results), cancellationToken);

        return results;
    }
}