﻿using System.Text.Json.Serialization;
using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;

/// <summary>
/// 商标变更申请
/// </summary>
public sealed class TrademarkChangeOrder : PhoenixOrderRequestParameters
{
    /// <summary>
    /// 申请人信息（变更前）
    /// </summary>
    public DeliveryApplicantInfo FormerApplicant { get; set; } = default!;

    /// <summary>
    /// 序列化后的变更前的申请人信息
    /// </summary>
    [JsonSerializationSource(nameof(FormerApplicant))]
    public string? BeforeApplicant { get; private set; }

    /// <summary>
    /// 申请人信息（当前）
    /// </summary>
    public DeliveryApplicantInfo CurrentApplicant { get; set; } = default!;

    /// <summary>
    /// 变更后的申请人信息
    /// </summary>
    [JsonSerializationSource(nameof(CurrentApplicant))]
    public string? AfterApplicant { get; private set; }

    /// <summary>
    /// 商标和商品信息
    /// </summary>
    public IEnumerable<BrandInfo> BrandInfos { get; set; } = Array.Empty<BrandInfo>();

    /// <summary>
    /// 序列化后的商标商品信息
    /// </summary>
    [JsonSerializationSource(nameof(BrandInfos))]
    public string? BrandTeam { get; private set; }

    /// <summary>
    /// 订单信息
    /// </summary>
    public OrderInfo OrderInfo { get; set; } = default!;

    /// <summary>
    /// 序列化后的订单信息
    /// </summary>
    [JsonSerializationSource(nameof(OrderInfo))]
    public string? Order { get; private set; }

    /// <summary>
    /// 附件信息
    /// </summary>
    public IEnumerable<ApplicantAttachment> Attachments { get; set; } = Array.Empty<ApplicantAttachment>();

    /// <summary>
    /// 序列化后的申请人文件信息
    /// </summary>
    [JsonSerializationSource(nameof(Attachments))]
    public string? ApplicantAttachments { get; private set; }

    /// <summary>
    /// 变更类型
    /// </summary>
    public string TransferType { get; set; } = default!;

}