﻿using AutoMapper;
using FluentValidation;
using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class UpdateRuleCommandHandler(
    IMapper mapper,
    IValidator<RulePatchDto> validator,
    IHttpContextAccessor httpContextAccessor,
    IWinningRewardRuleRepository ruleRepository) : IRequestHandler<UpdateRuleCommand>
{
    public async Task Handle(UpdateRuleCommand request, CancellationToken cancellationToken)
    {
        var (ruleId, patchDocument) = request;
        
        var rule = await ruleRepository.GetAsync(ruleId, cancellationToken);
        if (rule is null)
        {
            throw new NotFoundException(ruleId, "胜诉奖励规则");
        }
        
        var dto = mapper.Map<RulePatchDto>(rule);
        patchDocument.ApplyTo(dto);
        
        await validator.ValidateAndThrowAsync(dto, cancellationToken);

        var userId = (httpContextAccessor.HttpContext?.User).GetUserId();
        mapper.Map(dto, rule);
        rule.Updater = userId;
        rule.UpdateTime = DateTime.Now;
        
        await ruleRepository.UpdateAsync(rule, cancellationToken);
    }
}