﻿using FluentValidation;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Validators;

internal sealed class DeliveryResultValidator : AbstractValidator<DeliveryResultDto>
{
    public DeliveryResultValidator()
    {
        RuleFor(dto => dto.DisplayJson).NotEqual("{}", StringComparer.CurrentCultureIgnoreCase)
            .When(dto => dto.Success && dto.Operation == TrademarkDeliveryOperation.SubmitOfficial)
            .WithMessage("递交官方成功的回调信息请提供打印json");
        
        RuleFor(dto => dto.DeliveryDate).NotNull()
            .When(dto => dto.Success && dto.Operation == TrademarkDeliveryOperation.SubmitOfficial)
            .WithMessage("递交官方成功的回调信息请提供打印递交日期");
    }
}