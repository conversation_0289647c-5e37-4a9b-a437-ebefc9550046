using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_bank", DisableSyncStructure = true)]
	public partial class CusBank {

		[ Column(Name = "account_number", StringLength = 100, IsNullable = false)]
		public string AccountNumber { get; set; }

		/// <summary>
		/// 真实银行地址
		/// </summary>
		[ Column(Name = "bank_address_real", StringLength = 2000)]
		public string BankAddressReal { get; set; }

		[ Column(Name = "bank_id", StringLength = 50, IsNullable = false, IsPrimary = true)]
		public string BankId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 真实银行名称
		/// </summary>
		[ Column(Name = "bank_name_real", StringLength = 500, IsNullable = false)]
		public string BankNameReal { get; set; }

		/// <summary>
		/// 收款人地址
		/// </summary>
		[ Column(Name = "beneficiary_address_real", StringLength = 500)]
		public string BeneficiaryAddressReal { get; set; }

		/// <summary>
		/// 收款人名称
		/// </summary>
		[ Column(Name = "beneficiary_name", StringLength = 200)]
		public string BeneficiaryName { get; set; }

		/// <summary>
		/// 收款银行之代理银行名称
		/// </summary>
		[ Column(Name = "correspondent_bank", StringLength = 200)]
		public string CorrespondentBank { get; set; }

		/// <summary>
		/// 收款银行之代理银行地址
		/// </summary>
		[ Column(Name = "correspondent_bank_address", StringLength = 500)]
		public string CorrespondentBankAddress { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string? CountryId { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string? CreateUserId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "iban_code", StringLength = 50)]
		public string IbanCode { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "swift_code", StringLength = 50, IsNullable = false)]
		public string SwiftCode { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string? UpdateUserId { get; set; }

	}

}
