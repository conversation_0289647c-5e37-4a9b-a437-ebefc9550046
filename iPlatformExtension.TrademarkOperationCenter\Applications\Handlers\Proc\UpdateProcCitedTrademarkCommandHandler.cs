﻿using AutoMapper;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class UpdateProcCitedTrademarkCommandHandler(
    IMapper mapper,
    ICitedTrademarkRepository citedTrademarkRepository,
    IHttpContextAccessor httpContextAccessor) : IRequestHandler<UpdateProcCitedTrademarkCommand>
{
    public async Task Handle(UpdateProcCitedTrademarkCommand request, CancellationToken cancellationToken)
    {
        var (id, dto) = request;
        var userId = (httpContextAccessor.HttpContext?.User).GetUserId();
        
        var citedTrademark = await citedTrademarkRepository.GetAsync(id, cancellationToken);
        if (citedTrademark is null)
        {
            throw new NotFoundException(id, "引证商标注册信息");
        }

        mapper.Map(dto, citedTrademark);
        citedTrademark.Updater = userId;
        citedTrademark.UpdateTime = DateTime.Now;

        await citedTrademarkRepository.UpdateAsync(citedTrademark, cancellationToken);
    }
}