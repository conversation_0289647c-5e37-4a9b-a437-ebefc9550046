﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto
{
    /// <summary>
    /// 客户地址返回model
    /// </summary>
    public class CustomerAddressReDto
    {
        public string AddressCn { get; set; }


        public string AddressEn { get; set; }


        public string AddressId { get; set; }


        public string AddressTypeText { get; set; }


        public string ApplicantNameCn { get; set; }


        public string CaseDirectionText { get; set; }


        public string CaseTypeText { get; set; }


        public string IsDefault { get; set; }


        public string IsEnabled { get; set; }

    }
}
