using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;

namespace ConsoleScript;

public class ManyToManyJoinTask
{
    public async Task RunAsync()
    {
        var freeSql = new FreeSqlBuilder().UseConnectionString(DataType.SqlServer,
                "data source=***************,7433;initial catalog=acip_iplatform_1009;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true")
            .UseMonitorCommand(command => Console.WriteLine(command.CommandText))
            .UseLazyLoading(true)
            .Build();

        var feesQuery = freeSql.Select<CaseFeeList>();

        var billQuery = feesQuery.From<BillRecordForeignCase, BillRecordForeign>()
            .InnerJoin((list, billFee, foreignBill) => list.FeeId == billFee.FeeId)
            .InnerJoin((list, billFee, foreignBill) => billFee.BillId == foreignBill.BillId)
            .Where((list, billFee, foreignBill) => foreignBill.ApplyBatchTitle == "付款 2019.6.18 修改Ladas信息")
            .InnerJoin<CaseProcInfo>((list, info) => list.ProcId == info.ProcId);

        Console.WriteLine(billQuery.ToSql());

        var feeIds = await feesQuery.GroupBy(list => list.FeeId).Take(10).ToListAsync(aggregate => aggregate.Key);
    }
}