using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_mail_config_filter", DisableSyncStructure = true)]
	public partial class SysMailConfigFilter {

		[ Column(Name = "filter_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FilterId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "config_id", StringLength = 50)]
		public string ConfigId { get; set; }

		[ Column(Name = "filter_head", StringLength = 50)]
		public string FilterHead { get; set; }

		[ Column(Name = "filter_type", StringLength = 50)]
		public string FilterType { get; set; }

		[ Column(Name = "filter_value", StringLength = 500)]
		public string FilterValue { get; set; }

		[ Column(Name = "seq", StringLength = 50)]
		public string Seq { get; set; }

	}

}
