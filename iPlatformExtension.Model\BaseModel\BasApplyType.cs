using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_apply_type", DisableSyncStructure = true)]
	public partial class BasApplyType {

		/// <summary>
		/// 申请类型主键ID
		/// </summary>
		[ Column(Name = "apply_type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ApplyTypeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 申请类型简称
		/// </summary>
		[ Column(Name = "apply_type_code", StringLength = 50)]
		public string ApplyTypeCode { get; set; }

		/// <summary>
		/// 申请类型简称对应序号
		/// </summary>
		[ Column(Name = "apply_type_code_no", StringLength = 50)]
		public string ApplyTypeCodeNo { get; set; }

		/// <summary>
		/// 申请类型英语
		/// </summary>
		[ Column(Name = "apply_type_en_us", StringLength = 50)]
		public string ApplyTypeEnUs { get; set; }

		/// <summary>
		/// 申请类型日语
		/// </summary>
		[ Column(Name = "apply_type_ja_jp", StringLength = 50)]
		public string ApplyTypeJaJp { get; set; }

		/// <summary>
		/// 申请类型中文
		/// </summary>
		[ Column(Name = "apply_type_zh_cn", StringLength = 50)]
		public string ApplyTypeZhCn { get; set; }

		/// <summary>
		/// 案件类型ID
		/// </summary>
		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		/// <summary>
		/// 国家ID
		/// </summary>
		[ Column(Name = "country_id", StringLength = 1000)]
		public string CountryId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户ID
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "fee_reduce_year", StringLength = 50)]
		public string FeeReduceYear { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户ID
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
