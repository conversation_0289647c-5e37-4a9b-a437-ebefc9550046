﻿using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Service.Interface;

public interface IInventorQueryService : ITransientDependency, IFreeSqlQueryService
{
    async Task<Dictionary<string, IEnumerable<InventorInfo>>> GetInventorsByCaseIdsAsync(IEnumerable<string> caseIds)
    {
        if (!caseIds.Any())
        {
            return new Dictionary<string, IEnumerable<InventorInfo>>();
        }

        var inventors = await DbQuery.Select<BasInventor>().WithLock().From<CaseInventorList>()
            .<PERSON><PERSON>oin((inventor, caseInventor) => caseInventor.InventorId == inventor.InventorId && inventor.IsEnabled == true)
            .Where(table => caseIds.Contains(table.t2.CaseId))
            .ToListAsync((inventor, caseInventor) =>
                new KeyValuePair<string, InventorInfo>(caseInventor.CaseId,
                    new InventorInfo(inventor.InventorId, inventor.InventorNameCn, inventor.InventorNameEn)));

        return inventors.GroupBy(inventorInfo => inventorInfo.Key, inventorInfo => inventorInfo.Value)
            .ToDictionary(group => group.Key, group => group as IEnumerable<InventorInfo>);
    }
}