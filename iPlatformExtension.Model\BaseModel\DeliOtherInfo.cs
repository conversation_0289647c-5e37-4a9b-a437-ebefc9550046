﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 递交其他额外信息
/// </summary>
[Table(Name = "deli_other_info", DisableSyncStructure = true)]
public sealed class DeliOtherInfo
{

	/// <summary>
	/// 任务id。主键
	/// </summary>
	[Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
	public string ProcId { get; set; } = default!;

	/// <summary>
	/// 任务标识
	/// </summary>
	[Column(Name = "ctrl_proc_mark")]
	public string? CtrlProcMark { get; set; }

	/// <summary>
	/// 创建时间
	/// </summary>
	[Column(Name = "creation_time", InsertValueSql = "getdate()")]
	public DateTime CreationTime { get; set; }

	/// <summary>
	/// 创建者
	/// </summary>
	[Column(Name = "creator", StringLength = 50, IsNullable = false)]
	public string Creator { get; set; } = "";

	/// <summary>
	/// 是否分割
	/// </summary>
	[Column(Name = "is_split")]
	public bool? IsSplit { get; set; }

	/// <summary>
	/// 名义变更类型
	/// </summary>
	[Column(Name = "nominal_changes_type", DbType = "char(1)")]
	public string? NominalChangesType { get; set; }

	/// <summary>
	/// 申请类别
	/// </summary>
	[Column(Name = "trademark_nice_classes")]
	public string? TrademarkNiceClasses { get; set; }

	/// <summary>
	/// 引证注册号
	/// </summary>
	[Column(Name = "cited_register_numbers")]
	public string? CitedRegisterNumbers { get; set; }
	
	/// <summary>
	/// 法律条款
	/// </summary>
	[Column(Name = "law_provisions")]
	public string LawProvisions { get; set; } = string.Empty;

	/// <summary>
	/// 是否涉及绝对理由
	/// </summary>
	[Column(Name = "has_absolute_reason")]
	public bool? HasAbsoluteReason { get; set; }
	
	/// <summary>
	/// 同意地址延及后续
	/// </summary>
	[Column(Name = "extend_to_same_address")]
	public bool? ExtendToSameAddress { get; set; }
		
	/// <summary>
	/// 许可类型
	/// </summary>
	[Column(Name = "license_type")]
	public string LicenseType { get; set; } = string.Empty;

	/// <summary>
	/// 合同生效日期
	/// </summary>
	[Column(Name = "contract_effective_date", MapType = typeof(DateTime?))]
	public DateOnly? ContractEffectiveDate { get; set; }

	/// <summary>
	/// 合同终止日期
	/// </summary>
	[Column(Name = "contract_termination_date", MapType = typeof(DateTime?))]
	public DateOnly? ContractTerminationDate { get; set; }
	
	/// <summary>
	/// 是否保留补充材料权利
	/// </summary>
	[Column(Name = "reservation_of_supplementary_material")]
	public bool? ReservationOfSupplementaryMaterial { get; set; }

	/// <summary>
	/// 交官名称
	/// </summary>
	[Column(Name = "official_name")]
	public string? OfficialName { get; set; }

	/// <summary>
	/// 更新时间
	/// </summary>
	[Column(Name = "update_time", InsertValueSql = "getdate()")]
	public DateTime UpdateTime { get; set; }

	/// <summary>
	/// 更新者
	/// </summary>
	[Column(Name = "updater", StringLength = 50, IsNullable = false)]
	public string Updater { get; set; } = "";

}