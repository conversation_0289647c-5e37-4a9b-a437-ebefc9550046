FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
ENV ASPNETCORE_URLS=http://+:8087
ENV ASPNETCORE_ENVIRONMENT=Development
ENV TZ=Asia/Shanghai
ENV DOTNET_GCHeapHardLimit=80000000
WORKDIR /app
EXPOSE 8087

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["obs_net/esdk_obs_.net_core.csproj", "obs_net/"]
COPY ["iPlatformExtension.TrademarkOperationCenter/iPlatformExtension.TrademarkOperationCenter.csproj", "iPlatformExtension.TrademarkOperationCenter/"]
COPY ["iPlatformExtension.Common/iPlatformExtension.Common.csproj", "iPlatformExtension.Common/"]
COPY ["iPlatformExtension.Model/iPlatformExtension.Model.csproj", "iPlatformExtension.Model/"]
COPY ["iPlatformExtension.Repository/iPlatformExtension.Repository.csproj", "iPlatformExtension.Repository/"]
RUN dotnet restore "iPlatformExtension.TrademarkOperationCenter/iPlatformExtension.TrademarkOperationCenter.csproj" -s "https://nuget.cdn.azure.cn/v3/index.json" --disable-parallel
COPY . .
WORKDIR "/src/iPlatformExtension.TrademarkOperationCenter"
RUN dotnet build "iPlatformExtension.TrademarkOperationCenter.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "iPlatformExtension.TrademarkOperationCenter.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
RUN mkdir "logs"
RUN mkdir "temp"
ENTRYPOINT ["dotnet", "iPlatformExtension.TrademarkOperationCenter.dll"]
