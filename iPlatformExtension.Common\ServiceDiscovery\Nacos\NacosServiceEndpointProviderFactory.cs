﻿using System.Collections.Concurrent;
using iPlatformExtension.Common.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.ServiceDiscovery;
using Nacos.V2;
#pragma warning disable CS8767 // Nullability of reference types in type of parameter doesn't match implicitly implemented member (possibly because of nullability attributes).

namespace iPlatformExtension.Common.ServiceDiscovery.Nacos;

internal sealed class NacosServiceEndpointProviderFactory(
    INacosNamingService namingService, 
    IOptionsMonitor<NacosServiceEndpointOptions> options,
    ILogger<NacosServiceEndpointProviderFactory> logger) 
    : IServiceEndpointProviderFactory
{
    internal static readonly ConcurrentDictionary<string, NacosClusterId> nacosClusterIds = 
        new(Environment.ProcessorCount, 11);

    private readonly ConcurrentDictionary<ServiceEndpointQuery, NacosServiceEndpointProviderBase?> _providers = 
        new(Environment.ProcessorCount, 11, new ServiceEndpointQueryEqualityComparer());
    
    public bool TryCreateProvider(ServiceEndpointQuery query, out IServiceEndpointProvider? provider)
    {
        provider = _providers.GetOrAdd(query, CreateProvider);

        if (provider is null)
        {
            logger.LogResolveNacosServiceQueryFailed(query);
        }

        return provider is not null;
    }

    private NacosServiceEndpointProviderBase? CreateProvider(ServiceEndpointQuery query)
    {
        var queryKey = query.ToString() ?? string.Empty;
        try
        {
            if (!nacosClusterIds.TryGetValue(queryKey, out var nacosClusterId))
            {
                return null;
            }
            
            var (serviceName, groupName) = nacosClusterId;
            var includeSchemes = query.IncludedSchemes;
            var endpointOptions = options.CurrentValue;

            if (!endpointOptions.Groups.Contains(groupName))
            {
                return null;
            }

            if (includeSchemes.All(Uri.UriSchemeHttp.Equals))
            {
                return new NacosHttpServiceEndpointProvider(serviceName, groupName, namingService,
                    new NacosServiceEndpointReloadToken());
            }
            if (includeSchemes.All(Uri.UriSchemeHttps.Equals))
            {
                return new NacosHttpsServiceEndpointProvider(serviceName, groupName, namingService,
                    new NacosServiceEndpointReloadToken());
            }

            return null;
        }
        catch (Exception e)
        {
            logger.LogResolveNacosServiceQueryFailed(queryKey, e);
            return null;
        }
    }
}