using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.MailCenter.Applications.Models.AuditUser;
using iPlatformExtension.MailCenter.Applications.Queries.AuditUser;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AuditUser
{
    /// <summary>
    /// 获取发件审核人列表查询处理程序
    /// </summary>
    internal sealed class GetFlowAuditUserListQueryHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IFreeSql<PlatformFreeSql> platformFreeSql,
        IUserInfoRepository userInfoRepository) : IRequestHandler<GetFlowAuditUserListQuery, IEnumerable<GetFlowAuditUserListDto>>
    {
        public async Task<IEnumerable<GetFlowAuditUserListDto>> Handle(GetFlowAuditUserListQuery request, CancellationToken cancellationToken)
        {
            // 查询用户信息，用于筛选条件
            var userInfoQuery = platformFreeSql.Select<SysUserInfo>();

            // 如果有审核人名称筛选条件
            List<string>? auditingUserIds = null;
            if (!string.IsNullOrWhiteSpace(request.AuditingName))
            {
                auditingUserIds = await userInfoQuery
                    .WithLock()
                    .Where(u => u.CnName.Contains(request.AuditingName))
                    .ToListAsync(u => u.UserId, cancellationToken);

                // 如果没有找到匹配的用户，返回空结果
                if (auditingUserIds.Count == 0)
                {
                    return Enumerable.Empty<GetFlowAuditUserListDto>();
                }
            }

            // 查询发件审核人列表
            var auditUsersQuery = freeSql.Select<FlowAuditUser>()
                .WithLock();

            // 应用筛选条件
            if (auditingUserIds != null && auditingUserIds.Count > 0)
            {
                // 审核人字段包含多个ID，使用分号分隔
                auditUsersQuery = auditUsersQuery.Where(audit =>
                    auditingUserIds.Any(id => audit.AuditingId != null && audit.AuditingId.Contains(id)));
            }

            // 获取结果
            var auditUsers = await auditUsersQuery
                .OrderByDescending(audit => audit.CreateTime)
                .ToListAsync(cancellationToken);

            // 构建结果
            var result = new List<GetFlowAuditUserListDto>();

            // 使用缓存获取用户信息
            ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;

            foreach (var item in auditUsers)
            {
                var auditingUserId = item.AuditingId;

                if (string.IsNullOrEmpty(auditingUserId))
                {
                    continue;
                }

                // 获取审核人信息
                var userInfo = await userBaseInfoRepository.GetCacheValueAsync(auditingUserId, cancellationToken: cancellationToken);
                if (userInfo == null)
                {
                    continue;
                }

                // 获取用户完整信息
                var fullUserInfo = await platformFreeSql.Select<SysUserInfo>()
                    .Where(u => u.UserId == auditingUserId)
                    .FirstAsync(cancellationToken);

                // 获取部门信息
                string deptId = fullUserInfo?.DeptId ?? string.Empty;
                var deptInfo = !string.IsNullOrEmpty(deptId) ? await platformFreeSql.Select<SysDeptInfo>()
                    .Where(d => d.DeptId == deptId)
                    .FirstAsync(cancellationToken) : null;

                var department = deptInfo?.FullName ?? string.Empty;

                // 获取在职状态
                var status = fullUserInfo?.IsEnabled == true ? "在职" : "离职";

                result.Add(new GetFlowAuditUserListDto
                {
                    Id = item.Id,
                    UserName = userInfo.UserName,
                    AuditingUser = new { userId = auditingUserId, name = userInfo.CnName ?? string.Empty },
                    Department = department,
                    Status = status,
                    CreateTime = item.CreateTime,
                    CreateUser =  new { userId = item.CreateBy, name = (await userInfoRepository.GetChineseKeyValueAsync(item.CreateBy)).Value ?? string.Empty },
                });
            }

            return result;
        }
    }
}
