﻿using AutoMapper;
using Google.Protobuf;
using iPlatformExtension.Common.GrpcServices;
using iPlatformExtension.Messages.Mails;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Public.Applications.Models.Statistics;
using iPlatformExtension.Public.Applications.Queries.Statistics;
using MediatR;
using MiniExcelLibs;
using MongoDB.Bson;

namespace iPlatformExtension.Public.Applications.Handlers.Statistics
{
    /// <summary>
    /// 2.2我的绝限任务15天
    /// </summary>
    internal sealed class MineDeadlineExpired15QueryHandler(
        IMediator mediator,
        IMapper mapper,
        Notification.NotificationClient notificationClient,
        ILogger<MineDeadlineExpired15Query> logger,
        IHostEnvironment hostEnvironment
    ) : IRequestHandler<MineDeadlineExpired15Query, IEnumerable<MineDeadlineExpired15Dto>>
    {
        public async Task<IEnumerable<MineDeadlineExpired15Dto>> Handle(
            MineDeadlineExpired15Query request,
            CancellationToken cancellationToken
        )
        {
            var legalDueDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.LegalDueDate, 14),
                cancellationToken
            );
            var legalDueDateWarning = mapper.Map<List<LegalDueDateWarning>>(
                legalDueDateWarningDtos
            );

            var underTakeUserList = legalDueDateWarningDtos
                .Select(it => new { it.UndertakeUserId, it.UndertakeUserName })
                .ToList();
            var asyncDuplexStreamingCall = notificationClient.NotifyAsync();

            underTakeUserList = hostEnvironment.IsProduction()
                ? underTakeUserList.Distinct().ToList()
                : underTakeUserList.Distinct().Where(it => new[]
                {
                    "ea937da8-85fe-498d-9843-17e7d616c692",
                    "c1597c51-781b-4682-81c7-e01dc23431ce",
                }.Contains(it.UndertakeUserId)).ToList();

            foreach (var underTakeUser in underTakeUserList)
            {
                var dictionary = new Dictionary<string, object>()
                {
                    ["官方期限"] = legalDueDateWarning.Where(it =>
                        it.UndertakeUserId == underTakeUser.UndertakeUserId
                    ),
                };
                var notificationMail = new NotificationMail
                {
                    Sender = "【正式版本】系统邮箱",
                    MessageId = Guid.NewGuid().ToString(),
                    BodyTemplate = BodyTemplate.MineDeadlineExpired15Days,
                };
                using (var stream = new MemoryStream())
                {
                    await stream.SaveAsAsync(
                        dictionary,
                        excelType: ExcelType.XLSX,
                        cancellationToken: cancellationToken
                    );
                    stream.Seek(0, SeekOrigin.Begin);

                    notificationMail.Attachments.Add(
                        new MailAttachment
                        {
                            FileName = "我的绝限任务到期提醒(15天内,含当天).xlsx",
                            Data = await ByteString.FromStreamAsync(stream, cancellationToken),
                        }
                    );
                    var receiverIds = hostEnvironment.IsProduction()
                        ? [underTakeUser.UndertakeUserId]
                        : new List<string>
                        {
                            "ea937da8-85fe-498d-9843-17e7d616c692",
                            "c1597c51-781b-4682-81c7-e01dc23431ce",
                        };
                    notificationMail.Receivers.AddRange(receiverIds);
                    logger.LogInformation(
                        notificationMail.ToJson() + notificationMail.Receivers.ToJson()
                    );

                    await asyncDuplexStreamingCall.RequestStream.WriteAsync(
                        notificationMail,
                        cancellationToken
                    );
                }
            }
            await asyncDuplexStreamingCall.RequestStream.CompleteAsync();
            return new List<MineDeadlineExpired15Dto>();
        }
    }
}
