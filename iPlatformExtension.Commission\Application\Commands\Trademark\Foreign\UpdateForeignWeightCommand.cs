﻿using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;

internal sealed record UpdateForeignWeightCommand(string ProcId, JsonPatchDocument<ForeignWeightPatchDto> ForeignWeightPatch) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;