using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_dictionary", DisableSyncStructure = true)]
	public partial class SysDictionary {

		/// <summary>
		/// 数据字典ID
		/// </summary>
		[ Column(Name = "dictionary_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DictionaryId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "crm_value", StringLength = 50)]
		public string CrmValue { get; set; }

		/// <summary>
		/// 字典描述
		/// </summary>
		[ Column(Name = "dictionary_desc", StringLength = 200)]
		public string DictionaryDesc { get; set; }

		/// <summary>
		/// 字典编号
		/// </summary>
		[ Column(Name = "dictionary_name", StringLength = 100)]
		public string DictionaryName { get; set; }

		/// <summary>
		/// 是否默认
		/// </summary>
		[ Column(Name = "is_default")]
		public bool? IsDefault { get; set; } = false;

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		/// <summary>
		/// 英文名称
		/// </summary>
		[ Column(Name = "text_en_us", StringLength = 100)]
		public string TextEnUs { get; set; }

		/// <summary>
		/// 日文名称
		/// </summary>
		[ Column(Name = "text_ja_jp", StringLength = 100)]
		public string TextJaJp { get; set; }

		/// <summary>
		/// 中文名称
		/// </summary>
		[ Column(Name = "text_zh_cn", StringLength = 100)]
		public string TextZhCn { get; set; }

		/// <summary>
		/// 标识码
		/// </summary>
		[ Column(Name = "value", StringLength = 50)]
		public string Value { get; set; }

	}

}
