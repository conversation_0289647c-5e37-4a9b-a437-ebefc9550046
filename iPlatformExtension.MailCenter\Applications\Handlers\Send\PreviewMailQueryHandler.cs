using HtmlAgilityPack;
using iPlatformExtension.MailCenter.Applications.Queries.Send;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using System.Text;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send;

/// <summary>
/// 预览邮件查询处理器
/// </summary>
internal sealed class PreviewMailQueryHandler(
    IMailSendRepository mailSendRepository,
    IMailUserRepository mailUserRepository) : IRequestHandler<PreviewMailQuery, PreviewMailResult>
{
    public async Task<PreviewMailResult> Handle(PreviewMailQuery request, CancellationToken cancellationToken)
    {
        // 获取邮件信息
        var mailSend = await mailSendRepository
            .Where(it => it.MailId == request.MailId)
            .FirstAsync(cancellationToken)
            ?? throw new ApplicationException("邮件不存在");

        // 获取邮件用户信息
        var mailUsers = await mailUserRepository
            .Where(it => it.MailId == request.MailId)
            .ToListAsync(cancellationToken);

        // 处理HTML正文
        var processedHtml = ProcessHtmlBody(mailSend.MailHtmlBody);

        // 构建结果
        var result = new PreviewMailResult
        {
            Subject = mailSend.MailSubject,
            HtmlBody = processedHtml,
            OriginalHtmlBody = mailSend.MailHtmlBody,
            MailFrom = GetUserDisplayString(mailUsers.Where(u => u.AddressType == "from").ToList()),
            MailTo = GetUserDisplayString(mailUsers.Where(u => u.AddressType == "to").ToList()),
            MailCc = GetUserDisplayString(mailUsers.Where(u => u.AddressType == "cc").ToList()),
            MailBcc = GetUserDisplayString(mailUsers.Where(u => u.AddressType == "bcc").ToList())
        };

        return result;
    }

    /// <summary>
    /// 处理HTML正文
    /// </summary>
    /// <param name="htmlBody">原始HTML正文</param>
    /// <returns>处理后的HTML正文</returns>
    private string ProcessHtmlBody(string htmlBody)
    {
        if (string.IsNullOrEmpty(htmlBody))
        {
            return string.Empty;
        }

        try
        {
            // 创建HTML文档
            var doc = new HtmlDocument();
            doc.LoadHtml(htmlBody);

            // 处理图片节点，添加样式限制最大宽度
            // var imgNodes = doc.DocumentNode.SelectNodes("//img");
            // if (imgNodes != null)
            // {
            //     foreach (var img in imgNodes)
            //     {
            //         // 设置图片最大宽度为100%，防止溢出
            //         img.SetAttributeValue("style", "max-width: 100%; height: auto;");

            //         // 确保图片有alt属性
            //         if (!img.Attributes.Contains("alt"))
            //         {
            //             img.SetAttributeValue("alt", "邮件图片");
            //         }
            //     }
            // }

            // // 处理链接，添加target="_blank"属性，使链接在新窗口打开
            // var linkNodes = doc.DocumentNode.SelectNodes("//a");
            // if (linkNodes != null)
            // {
            //     foreach (var link in linkNodes)
            //     {
            //         link.SetAttributeValue("target", "_blank");

            //         // 添加rel="noopener noreferrer"以提高安全性
            //         link.SetAttributeValue("rel", "noopener noreferrer");
            //     }
            // }

            // // 处理表格，确保响应式显示
            // var tableNodes = doc.DocumentNode.SelectNodes("//table");
            // if (tableNodes != null)
            // {
            //     foreach (var table in tableNodes)
            //     {
            //         table.SetAttributeValue("style", "width: 100%; max-width: 100%; border-collapse: collapse;");
            //     }
            // }

            // 批量处理特定的 data-value 节点，根据规则进行替换
            var replacementRules = new Dictionary<string, string>
            {
                { "CASE_COUNTRY", "中国" },
                { "VOLUME", "AP25115363GZ" },
                { "CASE_PROVINCE", "北京市" },
                { "CUSTOMER_NAME", "测试客户" },
                { "UNDERTAKER", "何冲" },
                // 可以根据需要添加更多替换规则...
            };

            foreach (var rule in replacementRules)
            {
                // 选择带有特定 data-value 属性的节点
                // 处理两种情况：直接带data-value的节点和带class="mce-mergetag"的节点
                var directNodes = doc.DocumentNode.SelectNodes($"//*[@data-value='{rule.Key}']");
                var mergetagNodes = doc.DocumentNode.SelectNodes($"//span[@class='mce-mergetag'][@data-value='{rule.Key}']");

                // 处理直接带data-value的节点
                if (directNodes != null)
                {
                    foreach (var node in directNodes)
                    {
                        // 如果节点不是 mce-mergetag，直接替换内容
                        if (node.GetAttributeValue("class", "") != "mce-mergetag")
                        {
                            node.InnerHtml = rule.Value;
                        }
                    }
                }

                // 处理带class="mce-mergetag"的节点
                if (mergetagNodes != null)
                {
                    foreach (var node in mergetagNodes)
                    {
                        // 找到内部的contenteditable="true"的span节点
                        var editableSpan = node.SelectSingleNode("./span[@contenteditable='true']");
                        if (editableSpan != null)
                        {
                            editableSpan.InnerHtml = rule.Value;
                        }
                        else
                        {
                            // 如果没有找到可编辑的span，则替换整个节点的内容
                            node.InnerHtml = rule.Value;
                        }
                    }
                }
            }

            // 添加邮件预览样式
            // var head = doc.DocumentNode.SelectSingleNode("//head");
            // if (head == null)
            // {
            //     // 如果没有head节点，创建一个
            //     head = doc.CreateElement("head");
            //     var html = doc.DocumentNode.SelectSingleNode("//html");
            //     if (html != null)
            //     {
            //         html.PrependChild(head);
            //     }
            //     else
            //     {
            //         // 如果没有html节点，创建完整的HTML结构
            //         var newHtml = doc.CreateElement("html");
            //         newHtml.AppendChild(head);

            //         var bodyElement = doc.CreateElement("body");
            //         // 将原始内容移动到新的body中
            //         bodyElement.InnerHtml = doc.DocumentNode.InnerHtml;
            //         newHtml.AppendChild(bodyElement);

            //         doc.DocumentNode.RemoveAllChildren();
            //         doc.DocumentNode.AppendChild(newHtml);
            //     }
            // }

            // 将body内容包装在一个容器div中
            // var body = doc.DocumentNode.SelectSingleNode("//body");
            // if (body != null)
            // {
            //     var originalContent = body.InnerHtml;
            //     body.InnerHtml = $"<div class=\"mail-preview-container\">{originalContent}</div>";
            // }

            // 返回处理后的HTML
            return doc.DocumentNode.OuterHtml;
        }
        catch (Exception ex)
        {
            // 如果处理过程中出现异常，返回原始HTML
            return $"<!-- HTML处理异常: {ex.Message} -->\n{htmlBody}";
        }
    }

    /// <summary>
    /// 获取用户显示字符串
    /// </summary>
    /// <param name="users">用户列表</param>
    /// <returns>用户显示字符串</returns>
    private static string GetUserDisplayString(List<MailUser> users)
    {
        if (users == null || users.Count == 0)
        {
            return string.Empty;
        }

        var sb = new StringBuilder();
        foreach (var user in users)
        {
            if (sb.Length > 0)
            {
                sb.Append("; ");
            }

            if (!string.IsNullOrEmpty(user.DisplayName) && user.DisplayName != user.MailAddress)
            {
                sb.Append($"{user.DisplayName} <{user.MailAddress}>");
            }
            else
            {
                sb.Append(user.MailAddress);
            }
        }

        return sb.ToString();
    }
}
