﻿using System.Diagnostics;
using Grpc.Core;
using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Messages;
using iPlatformExtension.Outsourcing.Application.Commands.Supplier;
using iPlatformExtension.Outsourcing.Messages;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace iPlatformExtension.Outsourcing.GrpcServices;

[Authorize(AuthenticationSchemes = BladeAuthOptions.SchemeName)]
public class ForeignSupplierService(ISender sender) : Grpc.SupplierService.SupplierServiceBase
{
    public override async Task<MessageResult> PutSupplier(Supplier request, ServerCallContext context)
    {
        await sender.Send(new SynchronizedSupplierCommand(request), context.CancellationToken);
        return new MessageResult()
        {
            Success = true,
            ResultCode = ResultCode.Success,
            Message = "操作成功",
            Data = request.Pack(),
            TraceId = Activity.Current?.TraceId.ToString()
        };
    }
}