﻿using iPlatformExtension.Commission.Application.Notifications.WinningReward;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class SystemDictionaryRuleResultHandler(ISystemDictionaryRepository dictionaryRepository) 
    : INotificationHandler<RewardRuleDisplayResultNotification>
{
    public async Task Handle(RewardRuleDisplayResultNotification notification, CancellationToken cancellationToken)
    {
        var results = notification.Results;
        foreach (var ruleDisplayDto in results)
        {
            ruleDisplayDto.RulingResult =
                await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.RulingResult,
                    ruleDisplayDto.RulingResult);
            ruleDisplayDto.CaseDirection =
                await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.CaseDirection,
                    ruleDisplayDto.CaseDirection);
            ruleDisplayDto.DateType =
                await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.WinningRewardDateType, 
                    ruleDisplayDto.DateType);
        }
    }
}