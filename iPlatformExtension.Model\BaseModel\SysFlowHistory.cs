using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_flow_history", DisableSyncStructure = true)]
	public partial class SysFlowHistory {

		[ Column(Name = "history_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string HistoryId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "audit_cn_name", StringLength = 50)]
		public string AuditCnName { get; set; }

		[ Column(Name = "audit_time")]
		public DateTime? AuditTime { get; set; }

		[ Column(Name = "audit_type_id", StringLength = 50)]
		public string AuditTypeId { get; set; }

		[ Column(Name = "audit_user_id", StringLength = 50)]
		public string AuditUserId { get; set; }

		[ Column(Name = "feedback_date")]
		public DateTime? FeedbackDate { get; set; }

		[ Column(Name = "flow_sub_type", StringLength = 50)]
		public string FlowSubType { get; set; }

		[ Column(Name = "flow_type", StringLength = 50)]
		public string FlowType { get; set; }

		[ Column(Name = "next", StringLength = 50)]
		public string Next { get; set; }

		[ Column(Name = "node_id", StringLength = 50, IsNullable = false)]
		public string NodeId { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string? Remark { get; set; }

        /// <summary>
        ///  ������Ϣ
        /// </summary>
        [Navigate(nameof(ObjId))]
        public virtual CaseInfo CaseInfo { get; set; }

        /// <summary>
        ///  ������Ϣ
        /// </summary>
        [Navigate(nameof(ObjId))]
        public virtual CaseProcInfo CaseProcInfo { get; set; }

        /// <summary>
        ///  ���̽ڵ���Ϣ
        /// </summary>
        [Navigate(nameof(NodeId))]
        public virtual SysFlowNode FlowNode { get; set; }

        /// <summary>
        ///  ���̴�������Ϣ
        /// </summary>
        [Navigate(nameof(AuditUserId))]
        public virtual SysUserInfo NodeUserInfo { get; set; }

        /// <summary>
        ///  ��ǰ������Ϣ
        /// </summary>
        [Navigate(nameof(ObjId), TempPrimary = nameof(SysFlowActivity.ObjId))]
        public virtual SysFlowActivity CurFlowActivity { get; set; }

    }

}
