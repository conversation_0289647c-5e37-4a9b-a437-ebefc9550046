using AutoMapper;
using iPlatformExtension.Finance.Applications.Commands.Public;
using iPlatformExtension.Finance.Applications.Queries.Public;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Public;

/// <summary>
/// 请款对象处理者
/// </summary>
public sealed class RequestObjectQueryHandler : IRequestHandler<RequestObjectQuery, IEnumerable<RequestObjectReDto>>
{
    private readonly IMediator _mediator;

    private readonly IMapper _mapper;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="mediator">中介者</param>
    /// <param name="mapper">对象映射</param>
    public RequestObjectQueryHandler(IMediator mediator, IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    /// <summary>
    /// 查询请款对象
    /// </summary>
    /// <param name="request">查询参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>请款对象信息</returns>
    public async Task<IEnumerable<RequestObjectReDto>> Handle(RequestObjectQuery request, CancellationToken cancellationToken)
    {
        var command = _mapper.Map<BuildRequestObjectQueryCommand>(request);
        var requestObjectQuery = await _mediator.Send(command, cancellationToken);
        
        var requestObjectInfos = await requestObjectQuery.ToListAsync(requestObject => new RequestObjectReDto()
        {
            IsGeneralTaxpayer = requestObject.IsGeneralTaxpayer,
            SettlementCurrency = requestObject.SettlementCurrency,
            BillLanguage = requestObject.BillLanguage,
            Value = requestObject.RequestObjectId,
            TextZhCn =requestObject.RequestObjectName,
            TextEnUs = requestObject.RequestObjectName,
            TextJaJp = requestObject.RequestObjectName,
            InvoicesTitle = requestObject.InvoicesTitle,
            IdentifyNumber = requestObject.IdentifyNumber,
            AddressCn = requestObject.AddressCn,
            BankName = requestObject.BankName,
            InvoicesCode = requestObject.InvoicesCode,
            IsEnabled = requestObject.IsEnabled,
            AccountNo = requestObject.AccountNo,
            Tel = requestObject.Tel,
            IsOutbound = requestObject.IsOutbound
        }, cancellationToken);

        return requestObjectInfos.DistinctBy(info => info.Value).ToArray();
    }
}