﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto
{
    /// <summary>
    /// 入参基类
    /// </summary>
    public class PageParam
    {
        public string SearchKeyId { get; set; }
        public string SearchKey { get; set; }
        public int PageSize { get; set; }
        public int PageIndex { get; set; }
        public long Total { get; set; }
        public string Sort { get; set; }

        public string Language { get; set; }

        /// <summary>
        /// 待查询的数据的有效性，true:可用数据，false:所有数据
        /// </summary>
        public bool isEnabled { get; set; }
        /// <summary>
        /// 真假值不能满足需要，增加以字符串做为业务代码的筛选条件
        /// </summary>
        public string multiple { get; set; }


    }
}
