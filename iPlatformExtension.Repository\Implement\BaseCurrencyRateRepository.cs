﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class BaseCurrencyRateRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    CacheExpirationToken<BasCurrencyRate> expirationToken,
    DefaultRedisCache redisCache)
    : BaseRepository<BasCurrencyRate, string>(freeSql), IBaseCurrencyRateRepository
{
    IMemoryCache ICacheableRepository<CurrencyRateKey, BasCurrencyRate>.MemoryCache => memoryCache;

    CacheExpirationToken<BasCurrencyRate> ICacheableRepository<CurrencyRateKey, BasCurrencyRate>.ExpirationToken => expirationToken;
    
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}