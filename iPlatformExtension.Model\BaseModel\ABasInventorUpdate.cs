using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_bas_inventor_update", DisableSyncStructure = true)]
	public partial class ABasInventorUpdate {

		[ Column(Name = "address_cn", StringLength = 200)]
		public string AddressCn { get; set; }

		[ Column(Name = "card_no", StringLength = 50)]
		public string CardNo { get; set; }

		[ Column(Name = "card_type", StringLength = 50)]
		public string CardType { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "inventor_id", StringLength = 50, IsNullable = false)]
		public string InventorId { get; set; }

		[ Column(Name = "inventor_name_cn", StringLength = 200)]
		public string InventorNameCn { get; set; }

		[ Column(Name = "inventor_name_en", StringLength = 200)]
		public string InventorNameEn { get; set; }

		[ Column(Name = "is_del")]
		public bool? IsDel { get; set; } = false;

		[ Column(Name = "is_do")]
		public bool? IsDo { get; set; } = false;

		[ Column(Name = "new_inventor_id", StringLength = 50)]
		public string NewInventorId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

	}

}
