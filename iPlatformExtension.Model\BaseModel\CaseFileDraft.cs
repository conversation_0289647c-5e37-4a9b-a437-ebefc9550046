using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_file_draft", DisableSyncStructure = true)]
	public partial class CaseFileDraft {

		[ Column(Name = "file_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FileId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "desc_id", StringLength = 50)]
		public string DescId { get; set; }

		[ Column(Name = "file_ex", StringLength = 1000)]
		public string FileEx { get; set; }

		/// <summary>
		/// 文件名称
		/// </summary>
		[ Column(Name = "file_name", StringLength = 500)]
		public string FileName { get; set; }

		/// <summary>
		/// 文件编号
		/// </summary>
		[ Column(Name = "file_no", StringLength = 50)]
		public string FileNo { get; set; }

		/// <summary>
		/// 文件大小
		/// </summary>
		[ Column(Name = "file_size")]
		public long? FileSize { get; set; }

		/// <summary>
		/// 管制事项,提案,以及其他ID
		/// </summary>
		[ Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

	}

}
