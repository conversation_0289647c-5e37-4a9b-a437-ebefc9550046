using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_templet", DisableSyncStructure = true)]
	public partial class MailTemplet {

		/// <summary>
		/// 模板主键ID
		/// </summary>
		[ Column(Name = "templet_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string TempletId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 邮件模板英文内容
		/// </summary>
		[ Column(Name = "body_en_us", DbType = "ntext")]
		public string BodyEnUs { get; set; }

		/// <summary>
		/// 邮件模板日文内容
		/// </summary>
		[ Column(Name = "body_ja_jp", DbType = "ntext")]
		public string BodyJaJp { get; set; }

		/// <summary>
		/// 邮件模板中文内容
		/// </summary>
		[ Column(Name = "body_zh_cn", DbType = "ntext")]
		public string BodyZhCn { get; set; }

		/// <summary>
		/// 邮件编码
		/// </summary>
		[ Column(Name = "code", StringLength = 50)]
		public string Code { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 流程子类型
		/// </summary>
		[ Column(Name = "flow_sub_type", StringLength = 50)]
		public string FlowSubType { get; set; }

		/// <summary>
		/// 流程类型
		/// </summary>
		[ Column(Name = "flow_type", StringLength = 50)]
		public string FlowType { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		/// <summary>
		/// 邮件地址
		/// </summary>
		[ Column(Name = "mail_address", StringLength = 500)]
		public string MailAddress { get; set; }

		/// <summary>
		/// 模板名称
		/// </summary>
		[ Column(Name = "name", StringLength = 50)]
		public string Name { get; set; }

		/// <summary>
		/// 脚本
		/// </summary>
		[ Column(Name = "sql", StringLength = 4000)]
		public string Sql { get; set; }

		/// <summary>
		/// 邮件标题英文
		/// </summary>
		[ Column(Name = "title_en_us", StringLength = 500)]
		public string TitleEnUs { get; set; }

		/// <summary>
		/// 邮件标题日文
		/// </summary>
		[ Column(Name = "title_ja_jp", StringLength = 500)]
		public string TitleJaJp { get; set; }

		/// <summary>
		/// 邮件标题
		/// </summary>
		[ Column(Name = "title_zh_cn", StringLength = 500)]
		public string TitleZhCn { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
