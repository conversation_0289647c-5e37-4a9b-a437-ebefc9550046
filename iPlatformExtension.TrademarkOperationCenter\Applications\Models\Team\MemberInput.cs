﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;

/// <summary>
/// 团队成员模型
/// </summary>
/// <param name="UserId">员工id</param>
/// <param name="UserName">员工名称</param>
/// <param name="RoleId">角色id</param>
public record MemberInput(
    [Required(ErrorMessage = "员工Id不能为空")] string UserId,
    [Required(ErrorMessage = "员工名称不能为空")] string UserName,
    [Required(ErrorMessage = "角色Id不能为空")] string RoleId);
