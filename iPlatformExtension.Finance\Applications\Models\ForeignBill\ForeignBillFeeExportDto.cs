using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Finance.Applications.Models.ForeignBill;

internal sealed class ForeignBillFeeExportDto
{
    [ExcelIgnore]
    public required string BillId { get; set; }
    
    [ExcelColumn(Name = "我方文号")]
    public string? Volume { get; set; } = string.Empty;

    /// <summary>
    /// 外所文号
    /// CaseInfo
    /// </summary>
    [ExcelColumn(Name = "外所文号")]
    public string ForeignCaseNo { get; set; } = string.Empty;

    [ExcelColumn(Name = "任务名称")]
    public string? ProcName { get; set; } = string.Empty;

    [ExcelColumn(Name = "费用类别")]
    public string FeeClass { get; set; } = string.Empty;

    [ExcelColumn(Name = "费用名称")]
    public string? FeeName { get; set; } = string.Empty;

    [ExcelColumn(Name = "金额")]
    public decimal Amount { get; set; }

    [ExcelColumn(Name = "币别")]
    public string? Currency { get; set; } = string.Empty;

    [ExcelColumn(Name = "到款日期", Format = "yyyy-MM-dd")]
    public DateTime? CaseFeeReceiveDate { get; set; }

    [ExcelColumn(Name = "请款单号")]
    public string BillNo { get; set; } = string.Empty;

    [ExcelColumn(Name = "销售")]
    public string? CaseSales { get; set; } = string.Empty;

    [ExcelColumn(Name = "账单编号")]
    public string ForeignBillNo { get; set; } = string.Empty;

    [ExcelColumn(Name = "客户名称")]
    public string? CaseCustomer { get; set; } = string.Empty;

    /// <summary>
    /// bill_record_foreign
    /// fee_type
    /// </summary>
    [ExcelColumn(Name = "外所费用名称")]
    public string ForeignFeeName { get; set; } = string.Empty;
    
    /// <summary>
    /// bill_record_foreign
    /// bill_currency
    /// </summary>
    [ExcelColumn(Name = "账单币别")]
    public string? BillCurrency { get; set; } = string.Empty;

    /// <summary>
    /// bill_record_foreign
    /// bill_amount
    /// </summary>
    [ExcelColumn(Name = "账单金额")]
    public decimal BillAmount { get; set; }

    /// <summary>
    /// bill_record_foreign
    /// exchange_rate_bill_to_domestic
    /// </summary>
    [ExcelColumn(Name = "原币对本币汇率")]
    public decimal? ExchangeRateBillToDomestic { get; set; }

    /// <summary>
    /// bill_record_foreign
    /// exchange_rate_bill_to_pay
    /// </summary>
    [ExcelColumn(Name = "原币对中转汇率")]
    public decimal? ExchangeRateBillToPay { get; set; }

    /// <summary>
    /// bill_record_foreign
    /// exchange_rate_pay_to_domestic
    /// </summary>
    [ExcelColumn(Name = "中转对本币汇率")]
    public decimal? ExchangeRatePayToDomestic { get; set; }

    [ExcelColumn(Name = "付款日期", Format = "yyyy-MM-dd")]
    public DateTime? PaymentDate { get; set; }

    [ExcelColumn(Name = "付款币别")]
    public string? PaidCurrency { get; set; } = string.Empty;

    [ExcelColumn(Name = "付款金额")]
    public decimal? PaidAmount { get; set; }

    [ExcelColumn(Name = "本币金额")]
    public decimal? DomesticAmount { get; set; }

    [ExcelColumn(Name = "银行手续费币别")] 
    public string? RemittanceChargesCurrency { get; set; } = string.Empty;

    [ExcelColumn(Name = "银行手续费")]
    public decimal? RemittanceCharges { get; set; }     

    [ExcelColumn(Name = "汇款费用分摊方式")]
    public string RemittanceFeeSharingMethod { get; set; } = string.Empty;

    [ExcelColumn(Name = "汇款人名称")]
    public string RemittanceCompany { get; set; } = string.Empty;

    [ExcelColumn(Name = "我方银行名称")]
    public string PaidBank { get; set; } = string.Empty;

    [ExcelColumn(Name = "境外代理名称")]
    public string? ForeignBillCustomer { get; set; } = string.Empty;

    [ExcelColumn(Name = "所属国家")]
    public string BelongCountry { get; set; } = string.Empty;

    [ExcelColumn(Name = "备注")]
    public string Remark { get; set; } = string.Empty;

    [ExcelColumn(Name = "付款状态")]
    public string PaymentStatus { get; set; } = string.Empty;

    [ExcelColumn(Name = "案件类型")]
    public string CaseType { get; set; } = string.Empty;

    [ExcelColumn(Name = "账单日期", Format = "yyyy-MM-dd")]
    public DateTime? ForeignBillDate { get; set; }

    [ExcelColumn(Name = "收到日期", Format = "yyyy-MM-dd")]
    public DateTime? ForeignReceiveDate { get; set; }

    [ExcelColumn(Name = "应付日期", Format = "yyyy-MM-dd")]
    public DateTime? ForeignPaymentDueDate { get; set; }

    [ExcelColumn(Name = "所属分所")]
    public string? District { get; set; } = string.Empty;

    [ExcelColumn(Name = "申请批次名称")]
    public string ApplyBatchTitle { get; set; } = string.Empty;

    [ExcelColumn(Name = "付款批次名称")]
    public string PaymentBatchTitle { get; set; } = string.Empty;
}