﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class InitializeDeliveryFilesCommandHandler(IMediator mediator, IDeliveryFilesRepository deliveryFilesRepository) : 
    IRequestHandler<InitializeDeliveryFilesCommand>
{
    public async Task Handle(InitializeDeliveryFilesCommand request, CancellationToken cancellationToken)
    {
        var (procInfo, applicants, isFirstTime) = request;
        var files = new List<DeliFiles>();
        
        var applicant = applicants.SingleOrDefault(deliApplicant => deliApplicant.IsRepresent ?? false);
        if (applicant is not null && isFirstTime)
        {
            await mediator.Publish(
                new BuildApplicantIdentityCommand(applicant, files, procInfo.CaseInfo.CaseDirection,
                    procInfo.CtrlProcId), cancellationToken);
        }
        
        await mediator.Publish(new BuildDeliveryInitializeFilesCommand(procInfo, applicants, isFirstTime, files),
            cancellationToken);

        await deliveryFilesRepository.InsertAsync(files, cancellationToken);
    }
}