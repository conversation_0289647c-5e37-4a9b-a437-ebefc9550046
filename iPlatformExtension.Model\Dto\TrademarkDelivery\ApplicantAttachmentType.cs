﻿using System.ComponentModel;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 申请人附件类型
/// </summary>
public sealed class ApplicantAttachmentType
{

    /// <summary>
    /// 交官附件
    /// </summary>
    public static readonly ApplicantAttachmentType OfficialAttachment = new(2, "交官附件");
    
    private ApplicantAttachmentType(int code, string description)
    {
        Code = code;
        Description = description;
    }
    
    /// <summary>
    /// 编码
    /// </summary>
    public int Code { get; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; }
}