﻿using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DeliveryButtonsQueryHandler(
    IMediator mediator,
    IFlowNodeResourcesRepository nodeResourcesRepository)
    : IRequestHandler<DeliveryButtonsQuery, IReadOnlyDictionary<string, bool>>
{
    public async Task<IReadOnlyDictionary<string, bool>> Handle(DeliveryButtonsQuery request, CancellationToken cancellationToken)
    {
        var buttons = new Dictionary<string, bool>(4)
        {
            [nameof(DeliveryButton.StartUpDelivery)] = false,
            [nameof(DeliveryButton.StopDelivery)] = false,
            [nameof(DeliveryButton.WithdrawDelivery)] = false,
            [nameof(DeliveryButton.ConfirmDelivery)] = false
        };

        var flowInfo = request.FlowInfo;

        if (request.CurrentUserId != flowInfo?.CurAuditUserID)
        {
            return buttons;
        }

        if (string.IsNullOrWhiteSpace(flowInfo.CurNodeId))
        {
            return buttons;
        }

        var resources =
            (await nodeResourcesRepository.GetCacheValueAsync(flowInfo.CurNodeId))
            ?.Select(resource => resource.Resource).ToList() ?? [];

        var deliveryInfo = request.DeliveryInfo;
        buttons[nameof(DeliveryButton.StartUpDelivery)] =
            await mediator.Send(new StartupDeliveryButtonQuery(deliveryInfo), cancellationToken) &&
            resources.Contains(nameof(DeliveryButton.StartUpDelivery));
        buttons[nameof(DeliveryButton.StopDelivery)] =
            await mediator.Send(new StopDeliveryButtonQuery(deliveryInfo), cancellationToken);
        buttons[nameof(DeliveryButton.WithdrawDelivery)] =
            await mediator.Send(new WithdrawDeliveryButtonQuery(deliveryInfo), cancellationToken) && !request.Locked;
        buttons[nameof(DeliveryButton.ConfirmDelivery)] =
            await mediator.Send(new ConfirmDeliveryButtonQuery(deliveryInfo), cancellationToken) &&
            resources.Contains(nameof(DeliveryButton.ConfirmDelivery)) && !request.Locked;

        return buttons;
    }
}