﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 共同申请人编号
/// </summary>
public sealed class OtherApplicantInfoSnapshot
{
    /// <summary>
    /// 申请人中文名
    /// </summary>
    public string? ApplicantNameZh { get; set; }

    /// <summary>
    /// 申请人英文名
    /// </summary>
    public string? ApplicantNameEn { get; set; }

    /// <summary>
    /// 申请人证件类型
    /// </summary>
    public string? CertificateType { get; set; }

    /// <summary>
    /// 申请人证件编号
    /// </summary>
    public string? CertificateNumber { get; set; }
}