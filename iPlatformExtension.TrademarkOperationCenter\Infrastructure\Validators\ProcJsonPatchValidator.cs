﻿using FluentValidation;
using iPlatformExtension.Model.BaseModel;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Validators;

internal sealed class ProcJsonPatchValidator : AbstractValidator<JsonPatchDocument<CaseProcInfo>>
{
    public ProcJsonPatchValidator()
    {
        RuleFor(document => document.Operations)
            .Must(operations => operations.All(operation => operation.OperationType == OperationType.Replace))
            .WithMessage("jsonPatch只能做替换操作");
        
        RuleFor(document => document.Operations).Must(operations => operations
                .Where(operation =>
                    StringComparer.OrdinalIgnoreCase.Equals(operation.path, $"/{nameof(CaseProcInfo.TrademarkNiceClasses)}"))
                .All(operation =>
                    {
                        if (operation.value is not string operationValue || string.IsNullOrEmpty(operationValue))
                            return false;
                        return operationValue
                            .Split(';', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries)
                            .All(niceClass => int.TryParse(niceClass, out _));
                    })).WithName(nameof(CaseProcInfo.TrademarkNiceClasses))
            .When(document => document.Operations.Any(operation =>
                StringComparer.OrdinalIgnoreCase.Equals(operation.path,
                    $"/{nameof(CaseProcInfo.TrademarkNiceClasses)}")))
            .WithMessage("申请类别不能为空。或者必须以数字通过英文半角「;」拼接");
    }
}