using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_notice_convey_title_unit", DisableSyncStructure = true)]
	public partial class BasNoticeConveyTitleUnit {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "default_value", StringLength = 50)]
		public string DefaultValue { get; set; }

		[ Column(Name = "notice_code", StringLength = 50)]
		public string NoticeCode { get; set; }

		[ Column(Name = "remark", StringLength = 200)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "type_code", StringLength = 50)]
		public string TypeCode { get; set; }

		[ Column(Name = "unit_id", StringLength = 50, IsNullable = false)]
		public string UnitId { get; set; }

		[ Column(Name = "unit_name_en_us", StringLength = 50)]
		public string UnitNameEnUs { get; set; }

		[ Column(Name = "unit_name_ja_jp", StringLength = 50)]
		public string UnitNameJaJp { get; set; }

		[ Column(Name = "unit_name_zh_cn", StringLength = 50)]
		public string UnitNameZhCn { get; set; }

	}

}
