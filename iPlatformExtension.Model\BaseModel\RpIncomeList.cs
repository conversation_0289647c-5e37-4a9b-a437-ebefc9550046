using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_income_list", DisableSyncStructure = true)]
	public partial class RpIncomeList {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "apply_type_zh_cn", StringLength = 50)]
		public string ApplyTypeZhCn { get; set; }

		[ Column(Name = "bill_no", StringLength = 50)]
		public string BillNo { get; set; }

		[ Column(Name = "business_dept", StringLength = 50)]
		public string BusinessDept { get; set; }

		[ Column(Name = "business_dept_seq")]
		public int? BusinessDeptSeq { get; set; }

		[ Column(Name = "business_type_zh_cn", StringLength = 50)]
		public string BusinessTypeZhCn { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "cn_name", StringLength = 50)]
		public string CnName { get; set; }

		[ Column(Name = "district_id", StringLength = 50)]
		public string DistrictId { get; set; }

		[ Column(Name = "district_seq")]
		public int? DistrictSeq { get; set; }

		[ Column(Name = "fee_amount", DbType = "money")]
		public decimal? FeeAmount { get; set; }

		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		[ Column(Name = "flow_type", StringLength = 50)]
		public string FlowType { get; set; }

		[ Column(Name = "flow_update_time")]
		public DateTime? FlowUpdateTime { get; set; }

		[ Column(Name = "manage_company", StringLength = 50)]
		public string ManageCompany { get; set; }

		[ Column(Name = "month")]
		public int? Month { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

		[ Column(Name = "year")]
		public int? Year { get; set; }

	}

}
