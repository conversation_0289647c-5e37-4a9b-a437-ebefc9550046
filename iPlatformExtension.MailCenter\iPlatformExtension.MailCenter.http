@MailCenter_HostAddress = http://localhost:8081
@TrademarkOperationCenter_HostAddress = http://localhost:5098
GET {{MailCenter_HostAddress}}/Platform/GetProcList?ProcId=0eb10ac0-cec6-4131-80ee-6974d31cb61c
Accept: application/json
###
GET {{MailCenter_HostAddress}}/Platform/PlatformRelateCustomer?CustomerId=6bdcbdbd-4e9a-4347-a38b-112d8166396f&PageSize=999&PageIndex=1
Accept: application/json
###
GET {{MailCenter_HostAddress}}/Receive/GetRelateCustomer?MailId=6bdcbdbd-4e9a-4347-a38b-112d8166396f&PageSize=999&PageIndex=1
Accept: application/json
###
GET {{MailCenter_HostAddress}}/Receive/GetReceiveDetail?MailId=ed098dc5-b81a-4891-9f41-364ddc2b40e6
Accept: application/json
###
GET {{MailCenter_HostAddress}}/Receive/GetReceiveList?PageIndex=1&PageSize=10&MailSubject=AP25101227SU
Accept: application/json
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDQwMDgwNTUsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6IjRkYWUwMjk3LTllNDEtNDFiZC1hMGJlLWViOTZjOGZiYzU1YiIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.3aYB5FBAJSlvIZVQTWlbzNe1Llqd7IUGtqvettJIhRo

###
GET {{MailCenter_HostAddress}}/Platform/GetRelateCaseList?CaseId=630dc2ec-44b1-49ca-8498-20cd2ddb90a1
Accept: application/json
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDQwMDgwNTUsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6IjRkYWUwMjk3LTllNDEtNDFiZC1hMGJlLWViOTZjOGZiYzU1YiIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.3aYB5FBAJSlvIZVQTWlbzNe1Llqd7IUGtqvettJIhRo


###
POST {{MailCenter_HostAddress}}/Platform/GetProcList
Content-Type: application/json

{
  "procId": "0eb10ac0-cec6-4131-80ee-6974d31cb61c"
}

###
POST {{MailCenter_HostAddress}}/Signature/SaveSignature
Content-Type: application/json

{"name":"","content":"","id":"6be76805-596c-438f-8b3b-439592ac3209"}

###
POST {{MailCenter_HostAddress}}/Receive/SetRelate
Content-Type: application/json

{"mailId":"6bdcbdbd-4e9a-4347-a38b-112d8166396f","correlateType":"Customer","relateIdList":["bca428ce-cb0a-48ab-8012-f4299b9d7534"]}

###
POST {{MailCenter_HostAddress}}/AnalysisRule/AnalysisRule
Content-Type: application/json

{"mailId":["522bf045-027d-492d-b82e-2be0716f8b33","4195a5b4-2b64-41cd-9f48-3abef2a4b7d6"]}
###
POST {{MailCenter_HostAddress}}/Receive/SetSendName
Content-Type: application/json

{"mailId":"d9a5da56-a34b-40c8-9ff4-4fef935fc430","SendName":"这是邮件名称"}

###
GET {{MailCenter_HostAddress}}/Count
Accept: application/json
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDM2NDUyODYsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6ImMxNTk3YzUxLTc4MWItNDY4Mi04MWM3LWUwMWRjMjM0MzFjZSIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.mJPGiW7avRF9scj42DlXK1UQryvO0DWkq7a0CqQQ4gE

###
GET {{MailCenter_HostAddress}}/Receive/GetActionUserList?MailId=1231ddd2-e140-4b43-91cc-de7f868b0ae7&Action=Submit&Flow=Send
Accept: application/json
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDM2NDUyODYsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6ImMxNTk3YzUxLTc4MWItNDY4Mi04MWM3LWUwMWRjMjM0MzFjZSIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.mJPGiW7avRF9scj42DlXK1UQryvO0DWkq7a0CqQQ4gE


###
GET  {{MailCenter_HostAddress}}/Customer/GetCustomerList
Content-Type: application/json

{"PageIndex":1,"PageSize":10}
###

###
PUT  {{TrademarkOperationCenter_HostAddress}}/Count
Accept: application/json
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDM2NDUyODYsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6ImMxNTk3YzUxLTc4MWItNDY4Mi04MWM3LWUwMWRjMjM0MzFjZSIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.mJPGiW7avRF9scj42DlXK1UQryvO0DWkq7a0CqQQ4gE

{"teamId":"228929697877735","teamName":"OPPO团队","isExclusive":true,"isEffect":true,"teamDescription":"op","AuthorizeUser":"5da0647e-5286-4779-9198-a16cc06cc1b8;bd4c7f7d-6d4b-4035-9c0d-e925c6fc9045","createTime":"2024-04-03 15:34:57","seq":4,"customerId":["6B3A0677-789C-4F21-9994-591549D4E843"],"members":[{"teamMemberId":"228929697896152","userId":"48577a67-b9af-4308-b747-968ba3afe385","userName":"吴春晓","roleId":"03804687-683a-43d8-ad23-c652fc8ed6ca","roleName":"团队负责人","deptName":"国内商标交付二组"},{"teamMemberId":"229989531012256","userId":"ff9cf8db-96f2-4f70-9764-ad6bae7a3cea","userName":"张丽莹","roleId":"b1c67ef7-9170-49fa-93ab-9215e5d8c5e9","roleName":"导师","deptName":"国内商标交付二组"},{"teamMemberId":"229989531067202","userId":"f6f4e679-6a35-43d5-9ae3-49aff2f6901f","userName":"王福英","roleId":"b12b0dcb-e656-4345-93ff-fca7f75204fa","roleName":"代理人","deptName":"国内商标交付二组"},{"teamMemberId":"229989531075045","userId":"e8cf5d42-6c1f-4555-abb7-acad09626efc","userName":"王斌","roleId":"b12b0dcb-e656-4345-93ff-fca7f75204fa","roleName":"代理人","deptName":"国内商标交付二组"},{"teamMemberId":"244399686984109","userId":"bc70f722-ad61-4f2f-b7f7-e46699e7079e","userName":"陈睿祺","roleId":"b12b0dcb-e656-4345-93ff-fca7f75204fa","roleName":"代理人","deptName":"国内商标交付二组"}]}
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDQ0MjI3MzgsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6ImMxNTk3YzUxLTc4MWItNDY4Mi04MWM3LWUwMWRjMjM0MzFjZSIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.gOkmsSctBhj2q72PvVqW6S_yctcgapNZrABSWLe-ZyI
