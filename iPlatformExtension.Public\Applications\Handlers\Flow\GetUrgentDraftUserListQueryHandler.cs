﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models.Flow;
using iPlatformExtension.Public.Applications.Queries.Flow;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Flow
{
    /// <summary>
    /// 获取被催稿人名单
    /// </summary>
    internal sealed class GetUrgentDraftUserListQueryHandler(IFreeSql freeSql, IMediator mediator)
        : IRequestHandler<GetUrgentDraftUserListQuery, IEnumerable<GetUrgentDraftUserListDto>>
    {
        public async Task<IEnumerable<GetUrgentDraftUserListDto>> Handle(GetUrgentDraftUserListQuery request, CancellationToken cancellationToken)
        {
            var processInformationList = await mediator.Send(new ProcessInformationQuery([request.ProcId], null), cancellationToken);
            var procName = processInformationList.FirstOrDefault();
            var caseProcInfo = await freeSql.Select<CaseProcInfo>().Where(it => it.ProcId == request.ProcId)
                .ToOneAsync(it => new GetUrgentDraftUserListDto(it.ProcId, it.UndertakeUserId, it.UndertakeUserInfo.CnName, "'承办人'", it.UndertakeUserInfo.UserName), cancellationToken);
            var getUrgentDraftUserListDtos = new List<GetUrgentDraftUserListDto>()
            {
                caseProcInfo
            };
            if (procName is not null)
            {
                getUrgentDraftUserListDtos.Add(new(procName.ProcId, procName.CurUserId, procName.CurUserName, procName.FlowName, procName.UserCode));
            }
            return getUrgentDraftUserListDtos;
        }
    }
}

