﻿using System.ComponentModel;
using System.Reflection;

namespace iPlatformExtension.Common.Extensions;

/// <summary>
/// 枚举扩展方法
/// </summary>
public static class EnumExtension
{
    /// <summary>
    /// 获取枚举的描述
    /// </summary>
    /// <param name="enum"></param>
    /// <returns></returns>
    public static string GetDescription(this Enum @enum)
    {
        var fieldName = @enum.ToString();
        var field = @enum.GetType().GetField(fieldName);
        if (field == null)
        {
            return string.Empty;
        }
        var descriptionAttribute = field.GetCustomAttribute<DescriptionAttribute>();
        return descriptionAttribute == null ? string.Empty : descriptionAttribute.Description;
    }

    /// <summary>
    /// 获取枚举整形值
    /// </summary>
    /// <param name="enum">枚举值</param>
    /// <param name="offset">偏移量</param>
    /// <returns></returns>
    public static string? GetEnumIndex(this Enum @enum, int offset = 0)
    {
        var array = Enum.GetValues(@enum.GetType());
        List<int> list = [];
        foreach (var item in array)
        {
            list.Add(item.GetHashCode());
        }
        var data2 = Array.BinarySearch(list.ToArray(), @enum.GetHashCode());
        return data2 + offset >= list.FindLastIndex(_ => true)
            ? Enum.GetName(@enum.GetType(), list[list.FindLastIndex(_ => true)])
            : Enum.GetName(@enum.GetType(), list[data2 + offset]);
    }

    /// 获取枚举的特定特性值
    /// </summary>
    /// <typeparam name="T">特性类型</typeparam>
    /// <param name="value">枚举值</param>
    /// <returns>特性值，如果没有则返回null</returns>
    public static string? GetTableName<T>(this Enum value)
        where T : Attribute
    {
        var field = value.GetType().GetField(value.ToString());
        var attribute = field?.GetCustomAttributes(typeof(T), false).FirstOrDefault() as T;
        return attribute?.GetType().GetProperty("Name")?.GetValue(attribute)?.ToString();
    }
}
