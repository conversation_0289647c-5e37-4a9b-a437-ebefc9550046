﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 递交文件快照
/// </summary>
public class FileInfoSnapshot
{
    /// <summary>
    /// 文件id
    /// </summary>
    public string FileId { get; set; } = default!;

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = default!;

    /// <summary>
    /// 文件类型
    /// </summary>
    public string? FileType { get; set; }

    /// <summary>
    /// 文件描述
    /// </summary>
    public string? FileDescription { get; set; }

    /// <summary>
    /// 上传者
    /// </summary>
    public string? Uploader { get; set; }

    /// <summary>
    /// 上传时间
    /// </summary>
    public DateTime UploadTime { get; set; }

    /// <summary>
    /// 下载url
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 是否是身份证明
    /// </summary>
    public bool IsIdentity { get; set; }

    /// <summary>
    /// 案件文件id
    /// </summary>
    public string CaseFileId { get; set; } = string.Empty;
}