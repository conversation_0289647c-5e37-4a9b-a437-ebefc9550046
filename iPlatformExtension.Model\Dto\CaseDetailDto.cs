using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 
/// </summary>
public class CaseDetailDto
{
    /// <summary>
    /// 我方文号
    /// </summary>
    [JsonPropertyName("volume")]
    public string Volume { get; set; }

    /// <summary>
    /// 案件名称
    /// </summary>
    [JsonPropertyName("caseName")]
    public string CaseName { get; set; }

    /// <summary>
    /// 案件类型
    /// </summary>
    [JsonPropertyName("caseType")]
    public string CaseTypeId { get; set; }

    /// <summary>
    /// 业务类型
    /// 关联bas_business_type
    /// </summary>
    [JsonPropertyName("businessType")]
    public string BusinessTypeId { get; set; }

    /// <summary>
    /// 申请类型
    /// 关联bas_apply_type
    /// </summary>
    [JsonPropertyName("applyType")]
    public string ApplyTypeId { get; set; }

    /// <summary>
    /// 技术领域
    /// 关联bas_tech_field
    /// </summary>
    [JsonPropertyName("techType")]
    public string TechFieldId { get; set; }

    /// <summary>
    /// 案件流向
    /// </summary>
    [JsonPropertyName("caseDirection")]
    public string CaseDirection { get; set; }

    /// <summary>
    /// 国家ID
    /// </summary>
    [JsonPropertyName("country")]
    public string CountryId { get; set; }

    /// <summary>
    /// 联系人id
    /// 关联表case_contact_list
    /// </summary>
    [JsonPropertyName("caseContact")]
    public string ContactList { get; set; }

    /// <summary>
    /// 申请人列表
    /// 连表case_applicant和cus_contact
    /// </summary>
    [JsonPropertyName("caseApplicant")]
    public string ApplicantList { get; set; }

    /// <summary>
    /// 发明人列表
    /// 连表case_inventor
    /// </summary>
    [JsonPropertyName("caseInventor")]
    public string InventorList { get; set; }

    /// <summary>
    /// 境内代理
    /// </summary>
    [JsonPropertyName("agent")]
    public string AgencyId { get; set; }

    /// <summary>
    /// 第一代理
    /// </summary>
    [JsonPropertyName("firstAgency")]
    public string FirstAgencyUser { get; set; }

    /// <summary>
    /// 第二代里
    /// </summary>
    [JsonPropertyName("secondAgency")]
    public string SecondAgencyUser { get; set; }

    /// <summary>
    /// 客户文号
    /// </summary>
    [JsonPropertyName("customerCaseCode")]
    public string CustomerCaseNo { get; set; }
    
    /// <summary>
    /// 项目编号
    /// </summary>
    [JsonPropertyName("projectCode")]
    public string ProjectNo { get; set; }

    /// <summary>
    /// 案件级别
    /// </summary>
    [JsonPropertyName("customerCaseLevel")]
    public string CaseLevelId { get; set; }
    
    /// <summary>
    /// 申请方式
    /// </summary>
    [JsonPropertyName("applyWay")]
    public string FilingType { get; set; }

    /// <summary>
    /// PCT进入
    /// </summary>
    [JsonPropertyName("ifPctEntersChina")]
    public bool PctEnter { get; set; } = false;
    
    /// <summary>
    /// PCT申请日
    /// </summary>
    [JsonPropertyName("pctApplicationDate")]
    public DateTime? PctAppDate { get; set; }

    /// <summary>
    /// PCT申请号
    /// </summary>
    [JsonPropertyName("pctApplicationNo")]
    public string PctAppNo { get; set; }
    
    /// <summary>
    /// PCT递交语言
    /// </summary>
    [JsonPropertyName("pctSubmissionLanguage")]
    public string PctLanguage { get; set; }
    
    /// <summary>
    /// PCT公布语言
    /// </summary>
    [JsonPropertyName("pctPublishedLanguage")]
    public string PctPubLanguage { get; set; }
    
    /// <summary>
    /// PCT公布日
    /// </summary>
    [JsonPropertyName("pctPublication")]
    public DateTime? PctPubDate { get; set; }
    
    /// <summary>
    /// PCT公布号
    /// </summary>
    [JsonPropertyName("pctReleased")]
    public string PctPubNo { get; set; }

    /// <summary>
    /// 国际检索局
    /// 关联表bas_international_search
    /// </summary>
    [JsonPropertyName("internationalSearchingAuthority")]
    public string PctSearchUnit { get; set; }
    
    /// <summary>
    /// 急案标识
    /// </summary>
    [JsonPropertyName("urgencyFlag")]
    public string CaseEmergentId { get; set; }

    /// <summary>
    /// 本案客户要求
    /// </summary>
    [JsonPropertyName("individualRequirements")]
    public string RequestRemark { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [JsonPropertyName("note")]
    public string Remark { get; set; }
    
    /// <summary>
    /// 是否实质审查
    /// 是否同时提实审
    /// </summary>
    [JsonPropertyName("applySubstantiveExaminationMeanwhile")]
    public bool? IsEssenceExam { get; set; } = false;
    
    /// <summary>
    /// 提前公布
    /// </summary>
    [JsonPropertyName("announcedInAdvance")]
    public bool? IsAheadPub { get; set; } = false;
    
    
    /// <summary>
    /// 是否同日申请
    /// 1+1套案申请
    /// </summary>
    [JsonPropertyName("onePlusOneCaseApplication")]
    public bool? IsSameDay { get; set; } = false;
    
    
    /// <summary>
    /// PPH申请
    /// </summary>
    [JsonPropertyName("pphRequest")]
    public bool? IsPph { get; set; }
    
    
    /// <summary>
    /// 请求DAS
    /// </summary>
    [JsonPropertyName("requestTheDas")]
    public bool? IsRequestDas { get; set; } = false;
    
    
    /// <summary>
    /// 预审
    /// </summary>
    [JsonPropertyName("preTrial")]
    public bool? IsAdvanceCheck { get; set; } = false;
    
    /// <summary>
    /// 是否优先审查
    /// </summary>
    [JsonPropertyName("priorityReview")]
    public bool? IsPriorityReview { get; set; }
    
    /// <summary>
    /// 请求宽限（32个月）
    /// </summary>
    [JsonPropertyName("requestForGrace32Months")]
    public bool IsGrace { get; set; } = false;
    
    /// <summary>
    /// 主动修改
    /// </summary>
    [JsonPropertyName("activeModification")]
    public bool? IsActive { get; set; }
    
    
    /// <summary>
    /// 申请日
    /// </summary>
    [JsonPropertyName("applicationDate")]
    public DateTime? AppDate { get; set; }

    /// <summary>
    /// 申请号
    /// </summary>
    [JsonPropertyName("applicationNo")]
    public string? AppNo { get; set; }
    
    /// <summary>
    /// 办登年费阶段
    /// 首缴年度
    /// </summary>
    [JsonPropertyName("firstAnnual")]
    public string? FirstPayAnnual { get; set; }

    /// <summary>
    /// 案件状态ID
    /// 案件内部状态
    /// </summary>
    [JsonPropertyName("caseStatus")]
    public string CaseStatusId { get; set; } = null!;
    
    /// <summary>
    /// 分案属性
    /// </summary>
    public string DivisionProperty { get; set; }
    
    /// <summary>
    /// 原案申请日,母案申请日
    /// </summary>
    [JsonPropertyName("parentAppDate")]
    public DateTime? InitialAppDate { get; set; }

    /// <summary>
    /// 原案申请号,母案申请号
    /// </summary>
    [JsonPropertyName("parentAppNo")]
    public string InitialAppNo { get; set; }

    /// <summary>
    /// 相关案
    /// </summary>
    [JsonPropertyName("caseRelateList")]
    public List<CaseRelatedInfo> RelatedCases { get; set; }

    /// <summary>
    /// 生物保藏
    /// </summary>
    [JsonPropertyName("biologies")]
    public List<CaseBiologyDto> Biologies { get; set; } = new();

    /// <summary>
    /// 优先权
    /// </summary>
    [JsonPropertyName("priorities")]
    public List<CasePriorityInfoDto> Priorities { get; set; } = new();
}