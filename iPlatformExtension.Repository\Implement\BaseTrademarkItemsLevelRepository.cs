﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class BaseTrademarkItemsLevelRepository : DefaultRepository<BasTrademarkItemsLevel, string>, 
    IBaseTrademarkItemsLevelRepository
{
    public BaseTrademarkItemsLevelRepository(IFreeSql freeSql, UnitOfWorkManager uowManger) : base(freeSql, uowManger)
    {
    }
}