using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_address_default", DisableSyncStructure = true)]
	public partial class CusAddressDefault {

		[ Column(Name = "address_id", StringLength = 50, IsNullable = false)]
		public string AddressId { get; set; }

		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

	}

}
