﻿﻿namespace iPlatformExtension.MailCenter.Applications.Models.Process;

/// <summary>
/// 发件待审清单Dto
/// </summary>
/// <param name="MailId">邮件id</param>
/// <param name="MailNo">发件编号</param>
/// <param name="MailSubject">邮件主题</param>
/// <param name="MailTo">收件人</param>
/// <param name="MailFromTemp">发件人ID</param>
/// <param name="IsRead">是否要求已读回执</param>
/// <param name="SendTime">要求发送时间</param>
/// <param name="Status">邮件状态-1:取消,0:草稿,1:审核中,2:定时发送,3:待发送,4:发送失败,5:已经发送,6:已作废;500:出错</param>
/// <param name="Remark">备注</param>
/// <param name="CurNodeId">当前节点</param>
/// <param name="AuditRemark">办理建议</param>
/// <param name="AuditTime">到达时间</param>
/// <param name="AuditType">到达方式</param>
/// <param name="UndertakeUserTemp">承办人ID</param>
/// <param name="AuditUserTemp">审核人ID</param>
public record SendProcessListDto(
    string MailId,
    string MailNo,
    string MailSubject,
    string MailTo,
    string MailFromTemp,
    bool IsRead,
    DateTime? SendTime,
    int? Status,
    string Remark,
    string CurNodeId,
    string AuditRemark,
    DateTime? AuditTime,
    string AuditType,
    string UndertakeUserTemp,
    string AuditUserTemp)
{
    public object MailFrom { get; set; }
    public object UndertakeUser { get; set; }
    public object AuditUser { get; set; }
};
