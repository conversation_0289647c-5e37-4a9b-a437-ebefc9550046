﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using MediatR;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 获取关联案件处理者
    /// </summary>
    internal sealed class GetRelateCaseQueryHandler(IFreeSql<MailCenterFreeSql> freeSql, IFreeSql<PlatformFreeSql> platformFreeSql
        , IUserInfoRepository userInfoRepository, IBaseCtrlProcRepository baseCtrlProcRepository) : IRequestHandler<GetRelateCaseQuery, IEnumerable<GetRelateCaseDto>>
    {
        public async Task<IEnumerable<GetRelateCaseDto>> Handle(GetRelateCaseQuery request, CancellationToken cancellationToken)
        {
            var mailCorrelatives = await freeSql.Select<MailCorrelative>().WithLock().Where(it => it.MailId == request.MailId && it.CorrelateType == SysEnum.CorrelateType.Case.ToString())
                .ToListAsync(it => new { it.Id, it.ObjId, it.CreateBy, it.CreateTime }, cancellationToken);
            var caseList = mailCorrelatives.Select(it => it.ObjId);
            var getRelateProcDtos = await platformFreeSql.Select<CaseInfo>().WithLock().Where(it => caseList.Contains(it.Id))
                .OrderByDescending(it => it.CreateTime)
                .Page(request.PageIndex.Value, request.PageSize.Value).Count(out var count)
                .ToListAsync(it => new GetRelateCaseDto(it.Id, it.CaseTypeId, it.Volume, it.CaseName, it.CaseDirection, new { it.ApplyType.ApplyTypeId, it.ApplyType.ApplyTypeZhCn },
new { it.Agency.AgencyId, it.Agency.AgencyNameCn }, it.Customer.CustomerName), cancellationToken);
            var data = await getRelateProcDtos.ToAsyncEnumerable().SelectAwait(async it =>
            {

                var relate = mailCorrelatives.FirstOrDefault(x => x.ObjId == it.CaseId);
                if (relate is not null)
                {
                    it.CreateBy = new
                    {
                        CnName = await userInfoRepository.GetTextValueAsync(relate.CreateBy),
                        UserId = relate.CreateBy
                    };
                    it.CreateTime = relate.CreateTime;
                    it.RelateId = relate.Id;

                }
                return it;
            }).ToListAsync(cancellationToken: cancellationToken);
            return new PageResult<GetRelateCaseDto>()
            {
                Data = data,
                Page = request.PageIndex.Value,
                PageSize = request.PageSize.Value,
                Total = count
            };
        }
    }
}

