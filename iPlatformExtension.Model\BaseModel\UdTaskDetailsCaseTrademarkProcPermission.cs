using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ud_task_details_case_trademark_proc_permission", DisableSyncStructure = true)]
	public partial class UdTaskDetailsCaseTrademarkProcPermission {

		[ Column(StringLength = 1000)]
		public string 案件任务ID { get; set; }

		[ Column(StringLength = 1000)]
		public string 备案号 { get; set; }

		[ Column(StringLength = 100)]
		public string 备案日期 { get; set; }

		[ Column(StringLength = 1000)]
		public string 被许可人 { get; set; }

		[ Column(StringLength = 1000)]
		public string 被许可人地址 { get; set; }

		[ Column(StringLength = 100)]
		public string 任务编号 { get; set; }

		[ Column(StringLength = 100)]
		public string 任务名称 { get; set; }

		[ Column(StringLength = 1000)]
		public string 使用范围 { get; set; }

		[ Column(StringLength = 100)]
		public string 我方文号 { get; set; }

		[ Column(StringLength = 1000)]
		public string 许可类别 { get; set; }

		[ Column(StringLength = 100)]
		public string 许可生效日 { get; set; }

		[ Column(StringLength = 1000)]
		public string 许可项目数 { get; set; }

		[ Column(StringLength = 100)]
		public string 许可终止日 { get; set; }

		[ Column(StringLength = 1000)]
		public string 许可ID { get; set; }

		[ Column(Name = "case_date")]
		public DateTime? CaseDate { get; set; }

		[ Column(Name = "case_no", StringLength = 100)]
		public string CaseNo { get; set; }

		[ Column(Name = "error_columns", StringLength = 2000)]
		public string ErrorColumns { get; set; }

		[ Column(Name = "error_message", StringLength = -2)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "permission_address", StringLength = 1000)]
		public string PermissionAddress { get; set; }

		[ Column(Name = "permission_begin_date")]
		public DateTime? PermissionBeginDate { get; set; }

		[ Column(Name = "permission_date")]
		public DateTime? PermissionDate { get; set; }

		[ Column(Name = "permission_id", StringLength = 100)]
		public string PermissionId { get; set; }

		[ Column(Name = "permission_items", StringLength = 50)]
		public string PermissionItems { get; set; }

		[ Column(Name = "permission_tier", StringLength = 1000)]
		public string PermissionTier { get; set; }

		[ Column(Name = "permission_user", StringLength = 400)]
		public string PermissionUser { get; set; }

		[ Column(Name = "proc_id", StringLength = 100)]
		public string ProcId { get; set; }

		[ Column(Name = "task_id", StringLength = 50)]
		public string TaskId { get; set; }

		[ Column(Name = "ud_create_time", InsertValueSql = "getdate()")]
		public DateTime? UdCreateTime { get; set; }

		[ Column(Name = "use_range_id", StringLength = 4000)]
		public string UseRangeId { get; set; }

	}

}
