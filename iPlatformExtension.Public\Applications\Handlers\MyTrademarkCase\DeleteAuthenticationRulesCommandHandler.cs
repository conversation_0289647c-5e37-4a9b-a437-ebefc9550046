﻿using iPlatformExtension.Public.Applications.Commands.MyTrademarkCase;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.MyTrademarkCase
{
    /// <summary>
    /// 
    /// </summary>
    internal sealed class DeleteAuthenticationRulesCommandHandler(IRecognizedCertificateConfigRepository recognizedCertificateConfigRepository)
        : IRequestHandler<DeleteAuthenticationRulesCommand>
    {
        public async Task Handle(DeleteAuthenticationRulesCommand request, CancellationToken cancellationToken)
        {
            await recognizedCertificateConfigRepository.DeleteAsync(it => it.Id == request.Id, cancellationToken);
        }
    }
}

