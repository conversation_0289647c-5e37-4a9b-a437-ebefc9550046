﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.TrademarkDelivery.Applications.Models;

public class NextFlowParameters
{
    /// <summary>
    /// 任务ID
    /// </summary>

    public string ProcId { get; set; } = default!;

    /// <summary>
    /// 流程状态
    /// </summary>
    [JsonPropertyName("fStatus")]
    public int Status { get; set; }

    /// <summary>
    /// 商标案件流程ID
    /// </summary>'
    [JsonPropertyName("fFlowID")]
    public string FlowId { get; set; } = default!;

    /// <summary>
    /// 当前节点
    /// </summary>
    [JsonPropertyName("fCurNodeID")]
    public string CurrentNodeId { get; set; } = default!;

    /// <summary>
    /// 下个节点
    /// </summary>
    [JsonPropertyName("fNextNodeID")]
    public string NextNodeId { get; set; } = default!;

    /// <summary>
    /// 审核人ID
    /// </summary>
    [JsonPropertyName("fNextUserID")]
    public string NextUserId { get; set; } = default!;

    /// <summary>
    /// 流程提交类型
    /// submit
    /// reject
    /// </summary>
    [JsonPropertyName("fAuditTypeID")]
    public string AuditTypeId { get; set; } = default!;

    /// <summary>
    /// 流程备注
    /// </summary>
    [JsonPropertyName("fRemark")]
    public string Remark { get; set; } = string.Empty;

    /// <summary>
    /// 流程类型
    /// </summary>

    public string FlowType { get; set; } = default!;

    /// <summary>
    /// 允许编辑
    /// </summary>
    
    public bool AllowEdit { get; set; } = false;

    /// <summary>
    /// 案件类型
    /// </summary>
    [JsonPropertyName("fFlowSubType")]
    public string FlowSubType { get; set; } = default!;
}