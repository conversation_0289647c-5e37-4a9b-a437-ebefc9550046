﻿using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultCaseStatusQueryHandler(IBaseCaseStatusRepository baseCaseStatusRepository)
    : IRequestHandler<FeeResultCaseStatusQuery>
{
    public async Task Handle(FeeResultCaseStatusQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return;

        foreach (var feeItem in request.FeeResults)
        {
            feeItem.CaseStatus = await baseCaseStatusRepository.GetChineseKeyValueAsync(feeItem.CaseStatusId);
        }
    }
}