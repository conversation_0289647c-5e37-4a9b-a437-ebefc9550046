﻿using iPlatformExtension.Model.Enum;
using iPlatformExtension.Public.Applications.Commands.Statistics;
using iPlatformExtension.Public.Applications.Queries.Statistics;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers
{
    /// <summary>
    /// 统计控制器
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class StatisticsController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mediator"></param>
        public StatisticsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 1官方期限时间异常提醒
        /// </summary>
        [HttpGet]
        public async Task DueTimeErrorWarning()
        {
            await _mediator.Send(new DueTimeErrorWarningQuery(),HttpContext.RequestAborted);
        }

        /// <summary>
        /// 2.1代理人（承办人本人）端邮件模板-（周一）
        /// </summary>
        [HttpGet("Send15DeadlineMsgWarning")]
        public async Task Send15DeadlineMsgWarning()
        {
            await _mediator.Send(new Send15DeadlineMsgQuery(), HttpContext.RequestAborted);
        }

        /// <summary>
        /// 2.2我的绝限任务15天
        /// </summary>
        [HttpGet("MineDeadlineExpired15")]
        public async Task MineDeadlineExpired15()
        {
            await _mediator.Send(new MineDeadlineExpired15Query(), HttpContext.RequestAborted);
        }

        /// <summary>
        /// 2.3我的团队任务到期提醒（15天内，含当天）
        /// </summary>
        [HttpGet("Send15DTeamWarning")]
        public async Task Send15DTeamWarning()
        {
            await _mediator.Send(new Send15DTeamWarningQuery(), HttpContext.RequestAborted);
        }

        /// <summary>
        /// 4.内部期限超过7天汇总表
        /// </summary>
        [HttpGet("InternalDeadlineExpiredDays")]
        public async Task InternalDeadlineExpiredDays([FromQuery] InternalDeadlineExpiredDaysQuery query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 5.1应启动而未启动递交流程或管制完成的代理师任务的排查情况
        /// </summary>
        [HttpGet("CheckDelivery")]
        public async Task CheckDelivery([FromQuery] CheckDeliveryQuery query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        ///  2.4我的团队的绝限任务到期提醒（15天内，含当天）
        /// </summary>
        [HttpGet("MyTeamDeadlineExpired15DaysWarning")]
        public async Task MyTeamDeadlineExpired15DaysWarning([FromQuery] MyTeamDeadlineExpired15DaysWarningQuery query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 3.1 1天微信通知
        /// </summary>
        [HttpGet("WeChatWarning")]
        public async Task WeChatWarning([FromQuery] WeChatWarningQuery query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 3.2 1天微信通知
        /// </summary>
        [HttpGet("WeChatTeamAllDeadLineWarning")]
        public async Task WeChatTeamAllDeadLineWarning([FromQuery] WeChatTeamAllDeadLineWarningQuery query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 3.3 1天微信通知
        /// </summary>
        [HttpGet("WeChatTeamOutDeadLineWarning")]
        public async Task WeChatTeamOutDeadLineWarning([FromQuery] WeChatTeamOutDeadLineWarningQuery query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 催稿通知
        /// </summary>
        [HttpPost("UrgentDraftCommand")]
        public async Task UrgentDraftCommand([FromBody] UrgentDraftCommand query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }
    }
}
