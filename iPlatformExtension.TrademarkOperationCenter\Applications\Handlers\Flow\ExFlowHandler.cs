﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;
using System.Collections.Generic;
using System.Xml.Linq;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    /// <summary>
    /// 核稿特殊逻辑处理
    /// </summary>
    public sealed class ExFlowHandler(IMediator _mediator, ICaseProcFlowRepository _caseProcFlowRepository,
        IFlowActivityRepository _sysFlowActivityRepository, ISysFlowNodeRepository _sysFlowNodeRepository
        ) : IRequestHandler<ExFlowCommand, SysFlowActivity>
    {
        public async Task<SysFlowActivity> Handle(ExFlowCommand request, CancellationToken cancellationToken)
        {
            var userInfo = request.CurrentUser;
            var userid = userInfo.UserId;
            var cnName = userInfo.CnName;

            CaseProcFlow procFlow = null;
            procFlow = await _caseProcFlowRepository.Where(o => (o.ProcId == request.FlowInfo.ProcID) && o.FlowType == ProcFlowEnum.EX).ToOneAsync();
            if (procFlow == null)
            {
                var procFlowId = Guid.NewGuid().ToString();
                request.FlowInfo.ObjId = procFlowId;
                request.FlowInfo.ProcFlowId = procFlowId;
                procFlow = new CaseProcFlow { ProcFlowId = request.FlowInfo.ProcFlowId, ProcId = request.FlowInfo.ProcID, FlowType = ProcFlowEnum.EX };
                await _caseProcFlowRepository.InsertAsync(procFlow);
            }
            else
            {
                request.FlowInfo.ProcFlowId = procFlow.ProcFlowId.ToString();
                request.FlowInfo.ObjId = procFlow.ProcFlowId.ToString();
                var nodeInfo = await _sysFlowNodeRepository.GetAsync(request.FlowInfo.FCurNodeID, cancellationToken);
                if (nodeInfo.NodeCode == "END")
                {
                    procFlow.ExamineFinishDate = DateTime.Now;
                }
                procFlow.FlowStatusCode = request.FlowInfo.FStatus.ToString();
                procFlow.FlowCurUserId = request.FlowInfo.FNextUserID;
                procFlow.FlowCurNodeId = request.FlowInfo.FNextNodeID;
                procFlow.FlowUpdateTime = DateTime.Now;
                await _caseProcFlowRepository.UpdateAsync(procFlow);
            }
            return await _mediator.Send(new FlowActivityCommand(request.FlowInfo, request.CurrentUser));
        }
    }
}
