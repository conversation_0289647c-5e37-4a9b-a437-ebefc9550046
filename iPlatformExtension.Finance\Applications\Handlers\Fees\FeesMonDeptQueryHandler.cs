﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeesMonDeptQueryHandler(IFreeSql freeSql, ISystemDictionaryRepository dictionaryRepository)
    : IRequestHandler<FeesMonDeptQuery, IEnumerable<FeeMonDeptReDto>>
{
    public async Task<IEnumerable<FeeMonDeptReDto>> Handle(FeesMonDeptQuery request, CancellationToken cancellationToken)
    {
        var feeIds = request.FeeIds;
        var feeMonDeptReDtoList = await freeSql.Select<CaseFeeList>().WithLock()
            .From<CaseProcInfo, CaseInfo, BasApplyType, BasBusinessType, BasBusinessType>(
                (feeList, procInfo, caseInfo, basApplyType, caseBusinessType, procBusinessType) =>
                    feeList.LeftJoin(fee => fee.ProcId == procInfo.ProcId)
                        .LeftJoin(fee => procInfo.CaseId == caseInfo.Id)
                        .LeftJoin(fee => caseInfo.ApplyTypeId == basApplyType.ApplyTypeId)
                        .LeftJoin(fee => caseInfo.BusinessTypeId == caseBusinessType.BusinessTypeId)
                        .LeftJoin(fee => procInfo.BusTypeId == procBusinessType.BusinessTypeId))
            .Where(tables => feeIds.Contains(tables.t1.FeeId))
            .ToListAsync(
                (feeList, procInfo, caseInfo, basApplyType,  caseBusinessType, procBusinessType) => new FeeMonDeptReDto()
                {
                    FeeId = feeList.FeeId,
                    CaseType = caseInfo.CaseTypeId,
                    CaseDirection = caseInfo.CaseDirection,
                    ApplyTypeId = basApplyType.ApplyTypeZhCn,
                    BusinessTypeId = caseInfo.CaseTypeId == CaseType.Trade ? procBusinessType.BusinessTypeZhCn : caseBusinessType.BusinessTypeZhCn
                }, cancellationToken);

        ICacheableRepository<SystemDictionaryTextKey, SysDictionary> systemDictionaryRepository =
            dictionaryRepository;
        ArgumentNullException.ThrowIfNull(systemDictionaryRepository);

        foreach (var feeMonDeptReDto in feeMonDeptReDtoList)
        {
            if (feeMonDeptReDto.CaseType == "T")
            {
                if (feeMonDeptReDto.CaseDirection == "II")
                {
                    if (feeMonDeptReDto.ApplyTypeId == "版权" || feeMonDeptReDto.BusinessTypeId == "双软")
                    {
                        feeMonDeptReDto.MonDept = "CII";
                    }
                    else
                    {
                        feeMonDeptReDto.MonDept = "TII";
                    }
                }
                else
                {
                    feeMonDeptReDto.MonDept = "TIO";
                }
            }
            else if (feeMonDeptReDto.CaseType == "P")
            {
                if (feeMonDeptReDto.BusinessTypeId != "无效")
                {
                    feeMonDeptReDto.MonDept = feeMonDeptReDto.CaseDirection == "II" ? "PII" : "PIO";
                }
                else
                {
                    feeMonDeptReDto.MonDept = "WX";
                }
            }
            else if (feeMonDeptReDto.CaseType == "X")
            {
                feeMonDeptReDto.MonDept = (await systemDictionaryRepository.GetCacheValueAsync(
                    new SystemDictionaryTextKey(SystemDictionaryName.MonDept, feeMonDeptReDto.BusinessTypeId)))?.Value ?? "OTHER";
            }
            if (string.IsNullOrEmpty(feeMonDeptReDto.MonDept))
            {
                feeMonDeptReDto.MonDept = "OTHER";
            }
        }
        return feeMonDeptReDtoList;
    }
}