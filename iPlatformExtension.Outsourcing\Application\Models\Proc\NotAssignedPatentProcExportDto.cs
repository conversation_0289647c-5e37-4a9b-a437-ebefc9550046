﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Outsourcing.Application.Models.Proc;

public class NotAssignedPatentProcExportDto
{
    /// <summary>
    /// 我方文号
    /// </summary>
    [ExcelColumnName("我方文号")]
    public string Volume { get; set; }=string.Empty;
    

    /// <summary>
    /// 案件名称
    /// </summary>
    [ExcelColumnName("案件名称")]
    public string CaseName { get; set; } = string.Empty;
    
    /// <summary>
    /// 申请类型
    /// </summary>
    [ExcelColumnName("申请类型")]
    public string ApplyType { get; set; } = string.Empty;
    
    /// <summary>
    /// 任务名称
    /// </summary>
    [ExcelColumnName("任务名称")]
    public string ProcName { get; set; } = string.Empty;

    /// <summary>
    /// 案件流向
    /// </summary>
    [ExcelColumnName("案件流向")]
    public string CaseDirection { get; set; } = string.Empty;
    
    /// <summary>
    /// 国家
    /// </summary>
    [ExcelColumnName("国家/地区")]
    public string Country { get; set; } = string.Empty;
    
    /// <summary>
    /// 客户名称
    /// </summary>
    [ExcelColumnName("客户名称")]
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// 客户id
    /// </summary>
    [ExcelIgnore]
    public string CustomerId { get; set; } = string.Empty;
    
    /// <summary>
    /// 官方期限
    /// </summary>
    [ExcelColumnName("官方期限")]
    public DateTime? OfficialDeadline { get; set; }

    /// <summary>
    /// PCT申请号
    /// </summary>
    [ExcelColumnName("PCT申请号")]
    public string PctAppNo { get; set; } = string.Empty;
    
    
}