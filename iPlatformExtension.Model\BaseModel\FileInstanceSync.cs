using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "file_instance_sync", DisableSyncStructure = true)]
	public partial class FileInstanceSync {

		[ Column(Name = "district", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string District { get; set; }

		[ Column(Name = "instance_id", IsPrimary = true)]
		public int InstanceId { get; set; }

		[ Column(Name = "remark", StringLength = 50)]
		public string Remark { get; set; }

	}

}
