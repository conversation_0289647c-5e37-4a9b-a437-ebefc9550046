using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_fee_list_正式", DisableSyncStructure = true)]
	public partial class CaseFeeList正式 {

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "auto_rule_id", StringLength = 50)]
		public string AutoRuleId { get; set; }

		[ Column(Name = "auto_type", StringLength = 50, IsNullable = false)]
		public string AutoType { get; set; }

		[ Column(Name = "balance_way", StringLength = 50)]
		public string BalanceWay { get; set; }

		[ Column(Name = "base_type", StringLength = 50)]
		public string BaseType { get; set; }

		[ Column(Name = "bill_date")]
		public DateTime? BillDate { get; set; }

		[ Column(Name = "bill_no", StringLength = 50)]
		public string BillNo { get; set; }

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "coefficient")]
		public int? Coefficient { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "date_day")]
		public int DateDay { get; set; }

		[ Column(Name = "date_month")]
		public int DateMonth { get; set; }

		[ Column(Name = "date_type", StringLength = 50)]
		public string DateType { get; set; }

		[ Column(Name = "date_year")]
		public int DateYear { get; set; }

		[ Column(Name = "discount", StringLength = 50)]
		public string Discount { get; set; }

		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		[ Column(Name = "fee_id", StringLength = 50, IsNullable = false)]
		public string FeeId { get; set; }

		[ Column(Name = "fee_reduce_amount")]
		public double? FeeReduceAmount { get; set; }

		[ Column(Name = "fee_reduce_type", StringLength = 50)]
		public string FeeReduceType { get; set; }

		[ Column(Name = "fee_type_name_id", StringLength = 50)]
		public string FeeTypeNameId { get; set; }

		[ Column(Name = "head_user_type", StringLength = 50)]
		public string HeadUserType { get; set; }

		[ Column(Name = "invoice_no", StringLength = 300)]
		public string InvoiceNo { get; set; }

		[ Column(Name = "invoice_type", StringLength = 50)]
		public string InvoiceType { get; set; }

		[ Column(Name = "is_auto")]
		public bool IsAuto { get; set; }

		[ Column(Name = "is_edit")]
		public bool IsEdit { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "is_first_pay_annual")]
		public bool IsFirstPayAnnual { get; set; }

		[ Column(Name = "is_income")]
		public bool? IsIncome { get; set; }

		[ Column(Name = "is_officer")]
		public bool IsOfficer { get; set; }

		[ Column(Name = "is_request")]
		public bool IsRequest { get; set; }

		[ Column(Name = "item_value", StringLength = 50)]
		public string ItemValue { get; set; }

		[ Column(Name = "n_FeeID", DbType = "numeric(18,0)")]
		public decimal? NFeeID { get; set; }

		[ Column(Name = "n_StatusID", StringLength = 50)]
		public string NStatusID { get; set; }

		[ Column(Name = "n_YearFeeID", DbType = "numeric(18,0)")]
		public decimal? NYearFeeID { get; set; }

		[ Column(Name = "notice_date")]
		public DateTime? NoticeDate { get; set; }

		[ Column(Name = "office_name", StringLength = 2000)]
		public string OfficeName { get; set; }

		[ Column(Name = "office_name_en_us", StringLength = 200)]
		public string OfficeNameEnUs { get; set; }

		[ Column(Name = "office_name_zh_cn", StringLength = 200)]
		public string OfficeNameZhCn { get; set; }

		[ Column(Name = "officer_lock_del")]
		public bool OfficerLockDel { get; set; }

		[ Column(Name = "officer_status", StringLength = 50, IsNullable = false)]
		public string OfficerStatus { get; set; }

		[ Column(Name = "pay_bill_no", StringLength = 50)]
		public string PayBillNo { get; set; }

		[ Column(Name = "pay_cooperation_date")]
		public DateTime? PayCooperationDate { get; set; }

		[ Column(Name = "pay_date")]
		public DateTime? PayDate { get; set; }

		[ Column(Name = "pay_due_date")]
		public DateTime? PayDueDate { get; set; }

		[ Column(Name = "pay_lock_del")]
		public bool PayLockDel { get; set; }

		[ Column(Name = "pay_office", StringLength = 50)]
		public string PayOffice { get; set; }

		[ Column(Name = "pay_office_company", StringLength = 50)]
		public string PayOfficeCompany { get; set; }

		[ Column(Name = "pay_office_serial", StringLength = 50)]
		public string PayOfficeSerial { get; set; }

		[ Column(Name = "pay_officer_date")]
		public DateTime? PayOfficerDate { get; set; }

		[ Column(Name = "pay_officer_due_date")]
		public DateTime? PayOfficerDueDate { get; set; }

		[ Column(Name = "pay_officer_legal_date")]
		public DateTime? PayOfficerLegalDate { get; set; }

		[ Column(Name = "pay_status", StringLength = 50, IsNullable = false)]
		public string PayStatus { get; set; }

		[ Column(Name = "pay_status_id", StringLength = 50)]
		public string PayStatusId { get; set; }

		[ Column(Name = "pay_third_due_date")]
		public DateTime? PayThirdDueDate { get; set; }

		[ Column(Name = "pay_type")]
		public bool? PayType { get; set; }

		[ Column(Name = "pay_way", StringLength = 50)]
		public string PayWay { get; set; }

		[ Column(Name = "payment_agency", StringLength = 500)]
		public string PaymentAgency { get; set; }

		[ Column(Name = "payment_name", StringLength = 50)]
		public string PaymentName { get; set; }

		[ Column(Name = "pre_request_date")]
		public DateTime? PreRequestDate { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "proc_property", StringLength = 100)]
		public string ProcProperty { get; set; }

		[ Column(Name = "receive_date")]
		public DateTime? ReceiveDate { get; set; }

		[ Column(Name = "receive_due_date")]
		public DateTime? ReceiveDueDate { get; set; }

		[ Column(Name = "receive_rule", StringLength = 50)]
		public string ReceiveRule { get; set; }

		[ Column(Name = "receive_status", StringLength = 50, IsNullable = false)]
		public string ReceiveStatus { get; set; }

		[ Column(Name = "recieve_amount", DbType = "money")]
		public decimal? RecieveAmount { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "request_date")]
		public DateTime? RequestDate { get; set; }

		[ Column(Name = "request_id", StringLength = 50)]
		public string RequestId { get; set; }

		[ Column(Name = "request_lock_del")]
		public bool RequestLockDel { get; set; }

		[ Column(Name = "request_no", StringLength = 50)]
		public string RequestNo { get; set; }

		[ Column(Name = "sales_user_id", StringLength = 50)]
		public string SalesUserId { get; set; }

		[ Column(Name = "seq")]
		public int Seq { get; set; }

		[ Column(Name = "standard_amount", DbType = "money")]
		public decimal? StandardAmount { get; set; }

		[ Column(Name = "tax_rate", StringLength = 50)]
		public string TaxRate { get; set; }

		[ Column(Name = "track_user_id", StringLength = 50)]
		public string TrackUserId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
