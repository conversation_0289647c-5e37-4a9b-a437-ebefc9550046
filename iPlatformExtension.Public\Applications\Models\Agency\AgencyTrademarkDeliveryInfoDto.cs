﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Public.Applications.Models.Agency;

/// <summary>
/// 代理机构商标递交信息
/// </summary>
public sealed class AgencyTrademarkDeliveryInfoDto
{
    /// <summary>
    /// 代理机构id
    /// </summary>
    public string AgencyId { get; set; } = null!;

    /// <summary>
    /// 代理机构中文名
    /// </summary>
    public string? AgencyNameCn { get; set; }

    /// <summary>
    /// 代理机构英文名
    /// </summary>
    public string? AgencyNameEn { get; set; }

    /// <summary>
    /// 国内商标递交联系人
    /// </summary>
    public string? TrademarkDeliveryContactor { get; set; }

    /// <summary>
    /// 国内商标递交邮箱
    /// </summary>
    public string? TrademarkMailbox { get; set; }

    /// <summary>
    /// 国内商标联系电话
    /// </summary>
    public string? TrademarkTel { get; set; }

    /// <summary>
    /// 中文地址
    /// </summary>
    public string? AddressCn { get; set; }

    /// <summary>
    /// 英文地址
    /// </summary>
    public string? AddressEn { get; set; }

    /// <summary>
    /// 递交用key值
    /// </summary>
    [JsonIgnore]
    public string? DeliverKey { get; set; }

    /// <summary>
    /// 递交用key
    /// </summary>
    public KeyValuePair<string, string>[] DeliveryKeys { get; set; } = [];

    /// <summary>
    /// 联系地址邮编
    /// </summary>
    public string? PostCode { get; set; }
}