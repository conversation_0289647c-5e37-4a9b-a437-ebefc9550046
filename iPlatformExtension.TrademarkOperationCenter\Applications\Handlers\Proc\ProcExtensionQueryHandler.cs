﻿using System.Collections.ObjectModel;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class ProcExtensionQueryHandler(IFreeSql freeSql, IMediator mediator)
    : IRequestHandler<ProcExtensionQuery, IReadOnlyDictionary<ProcExtensionKey, ProcExtensionItem>>
{
    public async Task<IReadOnlyDictionary<ProcExtensionKey, ProcExtensionItem>> Handle(ProcExtensionQuery request, CancellationToken cancellationToken)
    {
        var procInfo = await freeSql.Select<CaseProcInfo>(request.ProcId).Include(procInfo => procInfo.CaseInfo).WithLock()
            .ToOneAsync(info => new CaseProcInfo()
            {
                ProcId = info.ProcId,
                CtrlProcId = info.CtrlProcId,
                ProcUndertakeMainUserId = info.ProcUndertakeMainUserId,
                CaseInfo = new CaseInfo()
                {
                    Id = info.CaseInfo.Id,
                    CaseDirection = info.CaseInfo.CaseDirection
                }
            }, cancellationToken);

        if (procInfo is null)
        {
            throw new NotFoundException(request.ProcId, "案件任务信息");
        }

        var info = procInfo;
        var dynamicConfig = await freeSql.Select<ProcPropertiesDynamicConfig>().WithLock()
            .Where(config => config.CtrlProcId == info.CtrlProcId && config.CaseDirection == info.CaseInfo.CaseDirection).ToListAsync(cancellationToken);

        procInfo = await mediator.Send(new ProcExtensionDynamicQuery(info.ProcId, dynamicConfig),
            cancellationToken);

        if (procInfo is null)
        {
            return ReadOnlyDictionary<ProcExtensionKey, ProcExtensionItem>.Empty;
        }
        
        procInfo.ProcId = info.ProcId;
        procInfo.CtrlProcId = info.CtrlProcId;

        var userReadonly = await mediator.Send(new ProcInfoAuthorizationCommand(info), cancellationToken);
        
        return await mediator.Send(new BuildDynamicItemsCommand(procInfo, dynamicConfig, userReadonly, true), cancellationToken);

    }
}