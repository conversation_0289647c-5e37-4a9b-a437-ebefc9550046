using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_files", DisableSyncStructure = true)]
	public partial class DeliFiles {

		[ Column(Name = "file_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FileId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "file_code", StringLength = 50)]
		public string? FileCode { get; set; }

		[ Column(Name = "file_count")]
		public int? FileCount { get; set; }

		[ Column(Name = "file_desc", StringLength = 200)]
		public string? FileDesc { get; set; }

		[ Column(Name = "file_ex", StringLength = 50)]
		public string FileEx { get; set; }

		[ Column(Name = "file_name", StringLength = 200)]
		public string FileName { get; set; }

		[ Column(Name = "file_no", StringLength = 50)]
		public string FileNo { get; set; }

		[ Column(Name = "file_page")]
		public int? FilePage { get; set; }

		[ Column(Name = "file_type")]
		public int? FileType { get; set; }

		[ Column(Name = "is_list")]
		public bool? IsList { get; set; } = false;

		[ Column(Name = "is_show")]
		public bool? IsShow { get; set; } = true;

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		/// <summary>
		/// 文件类型
		/// </summary>
		[Column(Name = "base_file_type")]
		public string? BaseFileType { get; set; }

		/// <summary>
		/// 是否身份证明文件
		/// </summary>
		[Column(Name = "is_identity")]
		public bool IsIdentity { get; set; } = false;

		/// <summary>
		/// 下载url
		/// </summary>
		[Column(Name = "url")]
		public string? Url { get; set; }

		/// <summary>
		/// 上传时间
		/// </summary>
		[Column(Name = "upload_time")]
		public DateTime? UploadTime { get; set; }

		/// <summary>
		/// 上传者
		/// </summary>
		[Column(Name = "uploader")]
		public string Uploader { get; set; } = string.Empty;

		/// <summary>
		/// 案件文件id
		/// </summary>
		[Column(Name = "case_file_id")]
		public string CaseFileId { get; set; } = string.Empty;

		/// <summary>
		/// file_list_a关联id
		/// </summary>
		[Column(Name = "id")]
		public int Id { get; set; } = -1;

	}

}
