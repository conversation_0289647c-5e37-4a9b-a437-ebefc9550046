﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto
{
    /// <summary>
    /// 客户案源人和跟案人model
    /// </summary>
    public class CustomerFollowReDto
    {
        /// <summary>
        /// 员工id
        /// </summary>
        public string UserId { get; init; } = default!;

        /// <summary>
        /// 员工姓名
        /// </summary>
        public string UserName { get; init; } = default!;

        /// <summary>
        /// 员工工号
        /// </summary>
        public string UserNum { get; init; } = default!;

        /// <summary>
        /// 案件类型
        /// </summary>
        public string CaseType { get; set; } = default!;

        /// <summary>
        /// 案件流向
        /// </summary>
        public string CaseDirection { get; set; } = default!;

        /// <summary>
        /// 人员类别
        /// </summary>
        public string CustomerUserType { get; set; } = default!;
    }
}
