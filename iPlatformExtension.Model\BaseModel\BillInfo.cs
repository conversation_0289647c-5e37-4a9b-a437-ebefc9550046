using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_info", DisableSyncStructure = true)]
	public partial class BillInfo {

		[ Column(Name = "bill_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string BillId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 账单地址
		/// </summary>
		[ Column(Name = "address_cn", StringLength = 2000)]
		public string AddressCn { get; set; }

		/// <summary>
		/// 请款主体
		/// </summary>
		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		/// <summary>
		/// 计税总额
		/// </summary>
		[ Column(Name = "amount_with_tax", DbType = "money")]
		public decimal? AmountWithTax { get; set; }

		/// <summary>
		/// 官费总额
		/// </summary>
		[ Column(Name = "annual_amount", DbType = "money")]
		public decimal? AnnualAmount { get; set; }

		[ Column(Name = "belong_district", StringLength = 50)]
		public string BelongDistrict { get; set; }

		/// <summary>
		/// 请款总金额
		/// </summary>
		[ Column(Name = "bill_amount", DbType = "money")]
		public decimal? BillAmount { get; set; }

		[ Column(Name = "bill_amount_difference", DbType = "money")]
		public decimal? BillAmountDifference { get; set; }

		/// <summary>
		/// 账单编号
		/// </summary>
		[ Column(Name = "bill_no", StringLength = 50)]
		public string BillNo { get; set; }

		/// <summary>
		/// 账单状态
		/// </summary>
		[ Column(Name = "bill_status", StringLength = 50)]
		public string BillStatus { get; set; }

		[ Column(Name = "bill_type", StringLength = 50)]
		public string BillType { get; set; }

		[ Column(Name = "claim_time")]
		public DateTime? ClaimTime { get; set; }

		[ Column(Name = "claim_user", StringLength = 50)]
		public string ClaimUser { get; set; }

		[ Column(Name = "claim_user_id", StringLength = 50)]
		public string ClaimUserId { get; set; }

		[ Column(Name = "company_id", StringLength = 50)]
		public string CompanyId { get; set; }

		/// <summary>
		/// 账单联系人
		/// </summary>
		[ Column(Name = "contact_id", StringLength = 50)]
		public string ContactId { get; set; }

		/// <summary>
		/// 账单联系人导航属性
		/// </summary>
		[Navigate(nameof(CusContact.ContactId))]
		public virtual CusContact ContactUser { get; set; } = default!;

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "crm_bill_id", StringLength = 50)]
		public string CrmBillId { get; set; }

		/// <summary>
		/// 账单币别
		/// </summary>
		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		/// <summary>
		/// 客户ID
		/// </summary>
		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "flow_cur_user_id", StringLength = 50)]
		public string FlowCurUserId { get; set; }

		/// <summary>
		/// 请款流程ID
		/// </summary>
		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		[ Column(Name = "flow_update_time")]
		public DateTime? FlowUpdateTime { get; set; }

		[ Column(Name = "has_invoice")]
		public bool? HasInvoice { get; set; } = true;

		[ Column(Name = "has_made_invoice")]
		public bool? HasMadeInvoice { get; set; }

		[ Column(Name = "i_invoices_type", StringLength = 50)]
		public string IInvoicesType { get; set; }

		[ Column(Name = "invoice_amount_gs", StringLength = 50)]
		public string InvoiceAmountGs { get; set; }

		[ Column(Name = "invoice_amount_p", StringLength = 50)]
		public string InvoiceAmountP { get; set; }

		[ Column(Name = "invoice_amount_ps", StringLength = 50)]
		public string InvoiceAmountPs { get; set; }

		[ Column(Name = "invoice_amount_z", StringLength = 50)]
		public string InvoiceAmountZ { get; set; }

		[ Column(Name = "invoice_status", StringLength = 50)]
		public string? InvoiceStatus { get; set; }

		[ Column(Name = "is_invoice")]
		public bool? IsInvoice { get; set; } = false;

		/// <summary>
		/// 账单语言
		/// </summary>
		[ Column(Name = "language_id", StringLength = 50)]
		public string LanguageId { get; set; }

		[ Column(Name = "nopay_reason", StringLength = 50)]
		public string NopayReason { get; set; }

		[ Column(Name = "nopay_remark", StringLength = 500)]
		public string NopayRemark { get; set; }

		[ Column(Name = "nopay_update_time")]
		public DateTime? NopayUpdateTime { get; set; }

		/// <summary>
		/// 付款条件
		/// </summary>
		[ Column(Name = "payment_term", StringLength = 50)]
		public string PaymentTerm { get; set; }

		[ Column(Name = "print_time")]
		public DateTime? PrintTime { get; set; }

		[ Column(Name = "print_user_id", StringLength = 50)]
		public string PrintUserId { get; set; }

		/// <summary>
		/// 收款银行账号
		/// </summary>
		[ Column(Name = "receive_bank_id", StringLength = 50)]
		public string ReceiveBankId { get; set; }

		[ Column(Name = "receive_date")]
		public DateTime? ReceiveDate { get; set; }

		[ Column(Name = "receive_due_date")]
		public DateTime? ReceiveDueDate { get; set; }

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		/// <summary>
		/// 备注to FA
		/// </summary>
		[ Column(Name = "remark_to_fa", StringLength = 2000)]
		public string RemarkToFa { get; set; }

		[ Column(Name = "request_invoice_time")]
		public DateTime? RequestInvoiceTime { get; set; }

		/// <summary>
		/// 请款对象ID
		/// </summary>
		[ Column(Name = "request_object_id", StringLength = 50)]
		public string RequestObjectId { get; set; }

		[ Column(Name = "sales_user_id", StringLength = 50)]
		public string SalesUserId { get; set; }

		/// <summary>
		/// 账单寄送日期
		/// </summary>
		[ Column(Name = "send_date")]
		public DateTime? SendDate { get; set; }

		/// <summary>
		/// 服务费总额
		/// </summary>
		[ Column(Name = "service_amount", DbType = "money")]
		public decimal? ServiceAmount { get; set; }

		/// <summary>
		/// 税额
		/// </summary>
		[ Column(Name = "tax", DbType = "money")]
		public decimal? Tax { get; set; }

		[ Column(Name = "tax_calculate_type", StringLength = 50)]
		public string TaxCalculateType { get; set; } = "1";

		[ Column(Name = "tax_invoices_type", StringLength = 50)]
		public string TaxInvoicesType { get; set; }

		/// <summary>
		/// 税率
		/// </summary>
		[ Column(Name = "tax_rate", DbType = "money")]
		public decimal? TaxRate { get; set; }

		/// <summary>
		/// 第三方费用
		/// </summary>
		[ Column(Name = "third_party_amount", DbType = "money")]
		public decimal? ThirdPartyAmount { get; set; }

		/// <summary>
		/// 账单主旨
		/// </summary>
		[ Column(Name = "title", StringLength = 500)]
		public string Title { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// 费项信息
		/// </summary>
		[Navigate(nameof(BillFeeList.BillId))]
		public virtual ICollection<BillFeeList> BillFees { get; set; } = default!;

		/// <summary>
		/// 费项金额统计数据
		/// </summary>
		[Navigate(nameof(BillFeeSubtotal.BillId))]
		public virtual ICollection<BillFeeSubtotal> FeeSubtotals { get; set; } = default!;

		/// <summary>
		/// 汇率数据
		/// </summary>
		[Navigate(nameof(BillCurrencyRateList.BillId))]
		public virtual ICollection<BillCurrencyRateList> CurrencyRateList { get; set; } = default!;

		/// <summary>
		/// 发票信息
		/// </summary>
		[Navigate(nameof(BillInvoiceList.BillId))]
		public virtual ICollection<BillInvoiceList>? Invoices { get; set; }

		/// <summary>
		/// 请款对象导航属性
		/// </summary>
		[Navigate(nameof(RequestObjectId))] 
		public virtual CusRequestObject RequestObject { get; set; } = default!;

		/// <summary>
		/// 是否已同步
		/// </summary>
		[Column(Name = "is_sync")]
		public bool IsSync { get; set; }

	}

}
