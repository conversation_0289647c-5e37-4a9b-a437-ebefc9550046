﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Public.Applications.Models.Customer.Contract;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;

namespace iPlatformExtension.Public.Applications.Commands.Customer.Contract;

internal sealed record UpdateContractCommand(string CrmContractId, JsonPatchDocument<CrmContractPatchDto> Document) 
    : IFreeSqlUnitOfWorkCommand<PlatformFreeSql>, IRequest;