﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class SaveDeliveryResultCommandHandler(
    IMediator mediator,
    IDeliveryInfoRepository deliveryInfoRepository)
    : IRequestHandler<SaveDeliveryResultCommand, bool>
{
    public async Task<bool> Handle(SaveDeliveryResultCommand request, CancellationToken cancellationToken)
    {
        var operatorId = request.OperatorId;
        var result = request.Result;

        // var deliverInfo = await _mediator.Send(new UpdateDeliveryCommand(request.ProcId, request.DeliveryInfoPatch, operatorId), cancellationToken);

        var deliveryInfo = await deliveryInfoRepository.Select.Where(info => info.ProcId == request.ProcId).ToOneAsync(info =>
            new DeliInfo()
            {
                ProcId = info.ProcId,
                Version = info.Version,
                Status = info.Status,
                DisplayJson = info.DisplayJson,
                OperationResult = info.OperationResult,
                IsAuto = info.IsAuto,
                UpdateTime = info.UpdateTime,
                UpdateUserId = info.UpdateUserId,
                DeliveryDate = info.DeliveryDate,
                DeliveryKey = info.DeliveryKey,
                OrderNo = info.OrderNo
            }, cancellationToken);
        if (deliveryInfo is null)
        {
            throw new NotFoundException(request.ProcId, "递交任务");
        }

        IUpdateDeliveryResultCommand command = result.Operation switch
        {
            TrademarkDeliveryOperation.WithdrawDelivery => new UpdateWithdrawResultCommand(deliveryInfo, operatorId,
                result, request.IsAuto),
            TrademarkDeliveryOperation.SubmitOfficial => new UpdateOfficialResultCommand(deliveryInfo, operatorId,
                result),
            TrademarkDeliveryOperation.Submitting => new UpdateSubmittingResultCommand(deliveryInfo, operatorId,
                result),
            _ => throw new ArgumentOutOfRangeException(nameof(result.Operation), result.Operation, "操作类型超出给定范围")
        };
        
        return await mediator.Send(command, cancellationToken);
        
    }
}