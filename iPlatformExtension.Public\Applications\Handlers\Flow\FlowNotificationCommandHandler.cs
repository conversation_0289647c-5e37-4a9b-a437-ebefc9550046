﻿using System.Text.Json;
using Confluent.Kafka;
using iPlatformExtension.Common.Helper;
using iPlatformExtension.Public.Applications.Commands.Flow;
using iPlatformExtension.Public.Hubs;
using MediatR;
using Microsoft.AspNetCore.SignalR;
using MongoDB.Bson;

namespace iPlatformExtension.Public.Applications.Handlers.Flow;

internal sealed class FlowNotificationCommandHandler(IHubContext<NotificationHub> hubContext)
    : IRequestHandler<FlowNotificationCommand>
{
    public Task Handle(FlowNotificationCommand request, CancellationToken cancellationToken)
    {
        var messages = request.FlowMessages;
        var tasks = new List<Task>(messages.Length);
        tasks.AddRange(messages.GroupBy(dto => dto.UserId)
            .Select(userMessages => hubContext.Clients.User(userMessages.Key)
                .SendAsync(request.Method, userMessages, cancellationToken)));

        return Task.WhenAll(tasks);
    }
}