﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.File
{
    /// <summary>
    /// 添加递交文件记录
    /// </summary>
    public class AddDeliFileDto
    {
        /// <summary>
        /// 任务id
        /// </summary>
        [Required]
        public string ProcId { get; set; } = default!;

        /// <summary>
        /// 文件编号（调用上传文件接口后的返回值）
        /// </summary>
        [Required]
        public int Id { get; set; } = -1;

        /// <summary>
        /// 文件名称
        /// </summary>
        [Required]
        public string FileName { get; set; } = default!;

        /// <summary>
        /// 文件描述
        /// </summary>
        [Required(ErrorMessage = "缺少文件描述")]
        public string FileDescription { get; set; } = default!;

        /// <summary>
        /// 文件类型
        /// </summary>
        [Required(ErrorMessage = "缺少文件类型")]
        public string BaseFileType { get; set; } = default!;

        /// <summary>
        /// 文件描述id
        /// </summary>
        [Required(ErrorMessage = "缺少文件描述Id")]
        public string FileDescriptionId { get; set; } = default!;

        /// <summary>
        /// 是否身份证明文件
        /// </summary>
        public bool IsIdentity { get; set; }
    }
}
