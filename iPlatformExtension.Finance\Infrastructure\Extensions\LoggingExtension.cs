﻿namespace iPlatformExtension.Finance.Infrastructure.Extensions;

/// <summary>
/// 日记记录扩展
/// </summary>
public static partial class LoggingExtension
{
    /// <summary>
    /// 记录更新任务失败
    /// </summary>
    /// <param name="logger">日志组件</param>
    /// <param name="exception">异常</param>
    /// <param name="procId">任务id</param>
    [LoggerMessage(LogLevel.Error, EventId = -1, Message = "更新案件任务失败！任务Id：[{ProcId}]")]
    public static partial void LogUpdateProcInfoFailed(this ILogger logger, Exception exception, string procId);

    /// <summary>
    /// 记录不存在的案件任务
    /// </summary>
    /// <param name="logger">日志组件</param>
    /// <param name="procId">任务id</param>
    [LoggerMessage(LogLevel.Error, EventId = -1, Message = "案件任务：[{ProcId}]不存在")]
    public static partial void LogProcInfoNotFound(this ILogger logger, string procId);
}