﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.LicenseFiling;

internal sealed class BuildLicenseFilingInfoCommandHandler(
    IHttpContextAccessor httpContextAccessor, IDeliveryOtherInfoRepository otherInfoRepository) 
    : IMatchTrademarkProcCommandHandler<BuildOtherInfoCommand>
{
    public async Task HandleAsync(BuildOtherInfoCommand notification, CancellationToken cancellationToken)
    {
        var procInfo = notification.ProcInfo;
        var operatorId = (httpContextAccessor.HttpContext?.User).GetUserId();
        
        var otherInfo = await otherInfoRepository.GetAsync(procInfo.ProcId, cancellationToken) ?? new DeliOtherInfo()
        {
            ProcId = procInfo.ProcId,
            CreationTime = DateTime.Now,
            Creator = operatorId
        };

        otherInfo.CtrlProcMark = procInfo.CtrlProcMark;
        otherInfo.ContractEffectiveDate = procInfo.ContractEffectiveDate;
        otherInfo.ContractTerminationDate = procInfo.ContractTerminationDate;
        otherInfo.LicenseType = procInfo.LicenseType;
        otherInfo.Updater = operatorId;
        otherInfo.UpdateTime = DateTime.Now;
        otherInfo.TrademarkNiceClasses = procInfo.TrademarkNiceClasses;
        
        await otherInfoRepository.InsertOrUpdateAsync(otherInfo, cancellationToken);
    }

    public string CtrlProcId => CtrlProcIds.LicenseFiling;

    public IEnumerable<string> CaseDirections => [CaseDirection.II];
}