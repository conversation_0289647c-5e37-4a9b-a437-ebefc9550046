using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 企业微信通知结果
/// </summary>
public class EnterpriseWechatNotificationResult
{
    /// <summary>
    /// 结编码
    /// </summary>
    [JsonPropertyName("errcode")]
    public int Code { get; set; }

    /// <summary>
    /// 返回提示语
    /// </summary>
    [JsonPropertyName("errmsg")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 消息id
    /// </summary>
    [JsonPropertyName("msgid")] 
    public string MessageId { get; set; } = string.Empty;
}