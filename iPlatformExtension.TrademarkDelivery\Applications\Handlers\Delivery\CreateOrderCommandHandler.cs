﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery;

public class CreateOrderCommandHandler<TCommand>(IMediator mediator)
    : IRequestHandler<CreateOrderCommand<TCommand>, DeliInfo?>
    where TCommand : OrderCommandBase, new()
{
    public async Task<DeliInfo?> Handle(CreateOrderCommand<TCommand> request, CancellationToken cancellationToken)
    {
        var (procId, version) = request;
        var deliverInfo = await mediator.Send(new CancelOrderCommand(procId, version, null, null), cancellationToken);
        deliverInfo =  await mediator.Send(request.CreateCommand(deliverInfo), cancellationToken);
        deliverInfo = await mediator.Send(new SubmitOrderCommand(procId, deliverInfo?.Version ?? version), cancellationToken);
        return deliverInfo;
    }
}