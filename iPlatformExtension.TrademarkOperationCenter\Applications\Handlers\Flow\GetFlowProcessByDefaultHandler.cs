﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;
using System.Linq.Expressions;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    /// <summary>
    /// 普通待办查询
    /// </summary>
    /// <param name="freeSql"></param>
    public class GetFlowProcessByDefaultHandler(IFreeSql freeSql) : IRequestHandler<FlowProcessQuery, PageResult<GetFlowProcessDto>>
    {
        public async Task<PageResult<GetFlowProcessDto>> Handle(FlowProcessQuery request, CancellationToken cancellationToken)
        {
            var totalCount = 0L;
            var list = new List<GetFlowProcessDto>();
            var sql = freeSql.Select<SysFlowActivity>().WithLock();
            list = await sql.Where(request.whereCommon).Where(request.whereTodo)
                    .Count(out totalCount)
                    .ToListAsync(fa => new GetFlowProcessDto()
                    {
                        ActivityId = fa.ActivityId,
                        procNo = fa.CaseProcInfo.ProcNo,
                        caseName = fa.CaseProcInfo.CaseInfo.CaseName,
                        UndertakeUserName = fa.CaseProcInfo.UndertakeUserInfo.CnName,
                        ctrlProcName = fa.CaseProcInfo.BasCtrlProc.CtrlProcZhCn,
                        appNo = fa.CaseProcInfo.CaseInfo.AppNo,
                        TrademarkClass = fa.CaseProcInfo.CaseInfo.TrademarkClass,
                        LegalDueDate = fa.CaseProcInfo.LegalDueDate,
                        Status = fa.Status,
                        CurNode = fa.CurFlowNode.NameZhCn,
                        CurNodeId = fa.CurNodeId,
                        PreNode = fa.PreFlowNode.NameZhCn,
                        SubmitType = freeSql.Select<SysDictionary>().Where(d => d.Value == fa.PrevAuditTypeId && d.DictionaryName == "audit_type").ToOne(o => o.TextZhCn),
                        UpdateTime = fa.UpdateTime,
                        CurHandler = fa.CurNodeUserInfo.CnName,
                        ObjectId = fa.ObjId,
                        Country = fa.CaseProcInfo.CaseInfo.Country.CountryZhCn,
                        ApplyTypeId = fa.CaseProcInfo.CaseInfo.ApplyType.ApplyTypeZhCn,
                        RequirementSubmitDate = fa.CaseProcInfo.RequirementSubmitDate,
                        SubProcStatus = fa.CaseProcInfo.SubProcStatus.TextZhCn,
                        AgencyName = fa.CaseProcInfo.Agency.AgencyNameCn,
                        TeamId = fa.CaseProcInfo.TeamId,
                        ReturnDocDate = fa.CaseProcInfo.ReturnDocDate,
                        Volume = fa.CaseProcInfo.CaseInfo.Volume,
                        Version = fa.CaseProcInfo.DeliInfo.Version,
                        ApplicantName = !string.IsNullOrEmpty(fa.CaseProcInfo.CaseInfo.ApplicantId) ? freeSql.Select<CaseApplicantList>().Where(d => d.ApplicantId == fa.CaseProcInfo.CaseInfo.ApplicantId && d.IsRepresent == true).ToOne(o => o.CusApplicant.ApplicantNameCn) : freeSql.Select<CaseApplicantList>().Where(d => d.CaseId == fa.CaseProcInfo.CaseInfo.Id).OrderBy(o => o.Seq).ToOne(o => o.CusApplicant.ApplicantNameCn),
                        RemainingDays = (fa.CaseProcInfo.BasCtrlProc.TrademarkTab == "0" || fa.CaseProcInfo.BasCtrlProc.TrademarkTab == "3") ? (!fa.CaseProcInfo.ReturnDocDate.HasValue ? null : (fa.CaseProcInfo.ReturnDocDate.Value - DateTime.Now.Date).Days + 1) :
                        (fa.CaseProcInfo.CtrlProcId == "DD886CBE-D8D4-4BE0-97F7-FD521674269A" ? (!fa.CaseProcInfo.RequirementSubmitDate.HasValue ? null : (fa.CaseProcInfo.RequirementSubmitDate.Value - DateTime.Now.Date).Days + 1) :
                        (!fa.CaseProcInfo.LegalDueDate.HasValue ? null : (fa.CaseProcInfo.LegalDueDate.Value - DateTime.Now.Date).Days + 1)),
                        CustomerName = fa.CaseProcInfo.CaseInfo.Customer.CustomerName,
                        CtrlProcMark = freeSql.Select<SysDictionary>().Where(o => o.DictionaryName == "ctrl_proc_mark" && o.Value == fa.CaseProcInfo.CtrlProcMark).ToOne(o => o.TextZhCn),
                        Saler = freeSql.Select<CusFollowList, SysUserInfo>().InnerJoin((a, b) => a.TrackUser == b.UserId).Where((a, b) => a.IsEnabled == true && a.CustomerId == fa.CaseProcInfo.CaseInfo.CustomerId && a.CaseDirection == "II" && a.CaseType == fa.CaseProcInfo.CaseInfo.CaseTypeId).OrderByDescending((a, b) => a.CustomerUserType).ToOne((a, b) => b.CnName),
                        ProcID = fa.CaseProcInfo.ProcId,
                        ImportantTrademark = fa.CaseProcInfo.CaseInfo.ImportantTrademark,
                        Prioritization = fa.CaseProcInfo.CaseInfo.Prioritization,
                        HighCosts = fa.CaseProcInfo.CaseInfo.HighCosts,
                        RegisterNo = fa.CaseProcInfo.CaseInfo.RegisterNo
                    }, cancellationToken);

            var res = list.CustomSort<GetFlowProcessDto>(request.Sort, request.IsAscending)
                .Skip(request.PageSize * (request.PageIndex - 1)).Take(request.PageSize).ToList();

            return new PageResult<GetFlowProcessDto>()
            {
                Data = res,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };
        }
    }
}
