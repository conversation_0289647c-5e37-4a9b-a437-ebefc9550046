﻿using System.Text.Json;
using CommonTest.Models;
using iPlatformExtension.Model.BaseModel;
using RulesEngine.Models;

namespace CommonTest;

public class IPlatformCommissionTest
{
    // private static readonly object[] parameters1 =
    // [
    //     new BillBonusCaseList()
    //     {
    //         IsAdd = false,
    //         BonusType = "quality"
    //     },
    //     new CommissionWeight()
    //     {
    //         Weight = new UserWeight()
    //         {
    //             DeptName = "专利质量管理部"
    //         }
    //     }
    // ];
  
    [Fact]
    public async Task TestExcludeRulesAsync()
    {
        var list = new BillBonusCaseList()
        {
            IsAdd = false,
            BonusType = "quality"
        };
        var commissionWeight = new CommissionWeight()
        {
            Weight = new UserWeight()
            {
              DeptName = "专利质量管理部"
            }
        };
        const string workFlows = """
                                 [
                                   {
                                     "WorkflowName": "专利质量管理部质检提成排除规则",
                                     "Rules": [
                                       {
                                         "RuleName": "是否手动录入",
                                         "Expression": "list.IsAdd == true"
                                       },
                                       {
                                         "RuleName": "是否为质检提成",
                                         "Expression": "list.BonusType == \"quality\""
                                       },
                                       {
                                         "RuleName": "是否为专利质量管理部",
                                         "Expression": "commissionWeight.Weight.DeptName.Contains(\"专利质量管理部\")"
                                       }
                                     ]
                                   }
                                 ]
                                 """;
        var workflows = JsonSerializer.Deserialize<Workflow[]>(workFlows);
        var engine = new RulesEngine.RulesEngine(workflows);
  
        var listParameter = new RuleParameter("list", list);
        var weightParameter = new RuleParameter("commissionWeight", commissionWeight);
        var resultTrees = await engine.ExecuteAllRulesAsync("专利质量管理部质检提成排除规则", listParameter, weightParameter);
        
        Assert.True(resultTrees.All(tree => tree.IsSuccess));
    }
}