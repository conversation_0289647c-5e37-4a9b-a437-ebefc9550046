﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 团队客户列表查询
    /// </summary>
    public class TeamCustomerQueryHandler : IRequestHandler<TeamCustomerQuery, IEnumerable<TeamCustomerDto>>
    {
        private readonly IFreeSql _freeSql;

        public TeamCustomerQueryHandler(IFreeSql freeSql)
        {
            _freeSql = freeSql;
        }

        public async Task<IEnumerable<TeamCustomerDto>> Handle(TeamCustomerQuery request, CancellationToken cancellationToken)
        {
            var dbSelect = _freeSql.Select<SysTeamCustomer, CusCustomer, SysUserInfo>()
                .LeftJoin((x, c, u) => x.CustomerId == c.CustomerId)
                .LeftJoin((x, c, u) => c.BusiUserId == u.UserId)
                .WhereIf(request.TeamId != null, (x, c, u) => x.TeamId == request.TeamId);
            if (request.PageIndex is not null && request.PageSize is not null)
            {
                dbSelect = dbSelect.Page(request.PageIndex.Value, request.PageSize.Value).Count(out var totalCount);
                var result = await dbSelect.WithLock().OrderBy(it => it.t1.SysTeamCustomerId).ToListAsync((x, c, u) =>
                    new TeamCustomerDto(x.SysTeamCustomerId, c.BusiUserId, u.CnName, c.CustomerId, (c.CustomerName ?? x.CustomerName ?? "") + (c.IsEnabled == false ? "(无效)" : "")), cancellationToken);
                return new PageResult<TeamCustomerDto>()
                {
                    Data = result,
                    Page = request.PageIndex.Value,
                    PageSize = request.PageSize.Value,
                    Total = totalCount
                };
            }
            return await dbSelect.WithLock().ToListAsync(
                (x, c, u) => new TeamCustomerDto(x.SysTeamCustomerId, c.BusiUserId, u.CnName, c.CustomerId, (c.CustomerName ?? x.CustomerName ?? "") + (c.IsEnabled == false ? "(无效)" : "")), cancellationToken);
        }
    }
}

