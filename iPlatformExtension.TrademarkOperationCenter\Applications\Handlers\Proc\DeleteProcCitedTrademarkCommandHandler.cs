﻿using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class DeleteProcCitedTrademarkCommandHandler(ICitedTrademarkRepository citedTrademarkRepository) 
    : IRequestHandler<DeleteProcCitedTrademarkCommand>
{
    public Task Handle(DeleteProcCitedTrademarkCommand request, CancellationToken cancellationToken)
    {
        return citedTrademarkRepository.DeleteAsync(request.Id, cancellationToken);
    }
}