﻿using iPlatformExtension.Common.Extensions;
using Microsoft.AspNetCore.SignalR.Client;

namespace iPlatformExtension.MailService.Infrastructure.SignalR
{
    public static class SignalRConfig
    {
        public static void InitSignalRService(this IServiceCollection services, IConfiguration configuration)
        {
            //services.AddSignalR();
            //services.AddCors(options =>
            //{
            //    options.AddPolicy("signalR", policy =>
            //    {
            //        policy.WithOrigins(
            //            "https://ipr.aciplaw.com",
            //            "https://ipr-test.aciplaw.com",
            //            "http://ipr-test.aciplaw.com",
            //            "http://localhost:5166");
            //        policy.DisallowCredentials();
            //        policy.AllowAnyMethod();
            //        policy.AllowAnyHeader();
            //    });
            //});
            services.AddSingleton((p) =>
            {
                var connection = new HubConnectionBuilder()
                    .WithUrl(new Uri(configuration["ConnectionStrings:SignalR"] ?? throw new ArgumentNullException("ConnectionStrings:SignalR", "缺少SignalR连接")))
                    .WithAutomaticReconnect(new RetryPolicy())
                    .Build();
                connection.StartAsync();
                return connection;
            });
        }
    }
}
