using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_user_rank", DisableSyncStructure = true)]
	public partial class BasUserRank {

		/// <summary>
		/// 代理人等级主键ID
		/// </summary>
		[ Column(Name = "rank_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string RankId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 提成比例
		/// </summary>
		[ Column(Name = "bonus_percentage", DbType = "money")]
		public decimal? BonusPercentage { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 是否核准
		/// </summary>
		[ Column(Name = "is_check")]
		public bool? IsCheck { get; set; }

		/// <summary>
		/// 等级简称
		/// </summary>
		[ Column(Name = "rank_code", StringLength = 50)]
		public string RankCode { get; set; }

		/// <summary>
		/// 英文名称
		/// </summary>
		[ Column(Name = "rank_en_us", StringLength = 50)]
		public string RankEnUs { get; set; }

		/// <summary>
		/// 日文名称
		/// </summary>
		[ Column(Name = "rank_ja_jp", StringLength = 50)]
		public string RankJaJp { get; set; }

		/// <summary>
		/// 使用类型
		/// </summary>
		[ Column(Name = "rank_type", StringLength = 50)]
		public string RankType { get; set; }

		/// <summary>
		/// 中文名称
		/// </summary>
		[ Column(Name = "rank_zh_cn", StringLength = 50)]
		public string RankZhCn { get; set; }

		[ Column(Name = "real_point_initial", DbType = "money")]
		public decimal? RealPointInitial { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
