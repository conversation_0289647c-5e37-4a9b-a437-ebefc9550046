﻿using System.Runtime.InteropServices;
using Confluent.Kafka;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.Common.MQ.KafKa.Route;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.MQ.KafKa.Consumer;

internal sealed class ConsumerService<TMessage>(
    IOptions<ConsumerOptions<TMessage>> options,
    IServiceScopeFactory scopeFactory,
    ILogger<ConsumerService<TMessage>> logger,
    ConsumeDelegates<TMessage> delegates)
    : BackgroundService
{
    private readonly List<IConsumer<MessageKey, TMessage>> _consumers = new (options.Value.Topics.Count());

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var options1 = options.Value;
        var builder = options1.ConsumerBuilder ?? new ConsumerBuilder<MessageKey, TMessage>(options1.ServerOptions);
        var consumeTasks = new List<Task>(options1.Topics.Count());
        var channel = options1.BackgroundConsumeChannel;
        
        foreach (var topic in options1.Topics)
        {
            var consumer = builder.Build();
            consumer.Subscribe(topic);
            _consumers.Add(consumer);
            
            var consumerTask = Task.Factory.StartNew(async  state =>
            {
                if (state is not IConsumer<MessageKey, TMessage> consumer1)
                {
                    return;
                }

                while (!stoppingToken.IsCancellationRequested)
                {
                    var messageResult = consumer1.Consume(stoppingToken);
                    logger.LogInformation("topic: {Topic}; Partition: {Partition}; message offset: {Offset}",
                        messageResult.Topic, messageResult.Partition.Value, messageResult.Offset.Value);

                    if (options1 is {ConcurrentConsume: true, ConcurrentCount: > 0})
                    {
                        var writer = channel.Writer;
                        await writer.WriteAsync(messageResult, stoppingToken).ConfigureAwait(false);
                        consumer1.Commit(messageResult);
                    }
                    else
                    {
                        if (await delegates.TryConsumerAsync(scopeFactory, messageResult))
                        {
                            consumer1.Commit(messageResult);
                        }
                    }
                }
            }, consumer, TaskCreationOptions.LongRunning);
            
            consumeTasks.Add(consumerTask);
        }

        return Task.WhenAll(consumeTasks);
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        var consumerOptions = options.Value;
        var channel = consumerOptions.BackgroundConsumeChannel;
        if (consumerOptions is {ConcurrentConsume:true, ConcurrentCount: > 0})
        {
            channel.Writer.Complete();
        }
        return base.StopAsync(cancellationToken);
    }

    public override void Dispose()
    {
        foreach (var consumer in CollectionsMarshal.AsSpan(_consumers))
        {
            consumer.Close();
            consumer.Dispose();
        }
    }
}