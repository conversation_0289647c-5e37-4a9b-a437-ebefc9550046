﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class AddProcSupplierContactCommandHandler(
    IHttpContextAccessor httpContextAccessor,
    ICaseProcInfoRepository caseProcInfoRepository,
    IProcForeignSupplierContactRepository procForeignSupplierContactRepository) 
    : IRequestHandler<AddProcSupplierContactCommand>
{
    public async Task Handle(AddProcSupplierContactCommand request, CancellationToken cancellationToken)
    {
        var (procId, supplierId, contactIds, version) = request;
        var userId = httpContextAccessor.HttpContext?.User.GetUserId() ?? string.Empty;
        
        var procInfo = await caseProcInfoRepository.GetAsync(procId, cancellationToken);
        if (procInfo == null)
        {
            throw new NotFoundException(procId, "案件任务");
        }
        
        procInfo.ValidateVersion(version);

        if (procInfo.ForeginAgencyId != supplierId)
        {
            throw new ApplicationException("案件任务境外代理不一致！请刷新页面");
        }

        var supplier = await caseProcInfoRepository.Orm.Select<ForeignSupplier>(supplierId)
            .ToOneAsync(cancellationToken);
        if (supplier == null)
        {
            throw new NotFoundException(supplierId, "境外代理");
        }

        var existsContactIds = await procForeignSupplierContactRepository.Where(contact => contact.ProcId == procId)
            .Where(contact => contactIds.Contains(contact.ContactId))
            .ToListAsync(contact => contact.ContactId, cancellationToken);
        
        contactIds.ExceptWith(existsContactIds);

        var procContacts = contactIds.Select(contactId => new ProcForeignSupplierContact
        {
            ContactId = contactId,
            ProcId = procId,
            CreationTime = DateTime.Now,
            UpdateTime = DateTime.Now,
            Updater = userId
        }).ToList();
        
        await procForeignSupplierContactRepository.InsertAsync(procContacts, cancellationToken);
        
        procInfo.UpdateTime = DateTime.Now;
        procInfo.UpdateUserId = userId;
        
        await caseProcInfoRepository.UpdateAsync(procInfo, cancellationToken);
    }
}