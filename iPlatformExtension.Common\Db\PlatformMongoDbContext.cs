using iPlatformExtension.Common.Db.Mongo;
using iPlatformExtension.Model.BaseModel;
using Microsoft.Extensions.Options;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Driver;
using RulesEngine.Models;

namespace iPlatformExtension.Common.Db;

public class PlatformMongoDbContext : MongoDbContext<PlatformMongoDbContext>
{
    static PlatformMongoDbContext()
    {
        BsonClassMap.RegisterClassMap<Workflow>(map =>
        {
            map.AutoMap();
            map.MapIdProperty(workflow => workflow.WorkflowName).SetElementName("workflow_name");
            map.MapMember(workflow => workflow.WorkflowsToInject).SetElementName("workflows_to_inject");
            map.MapMember(workflow => workflow.RuleExpressionType).SetElementName("rule_expression_type")
                .SetSerializer(new EnumSerializer<RuleExpressionType>(BsonType.String));
            map.MapMember(workflow => workflow.GlobalParams).SetElementName("global_params");
            map.MapMember(workflow => workflow.Rules).SetElementName("rules");
        });

        BsonClassMap.RegisterClassMap<Rule>(map =>
        {
            map.AutoMap();
            map.MapMember(rule => rule.RuleName).SetElementName("rule_name");
            map.MapMember(rule => rule.RuleExpressionType).SetElementName("rule_expression_type")
                .SetSerializer(new EnumSerializer<RuleExpressionType>(BsonType.String));
            map.MapMember(rule => rule.Properties).SetElementName("properties");
            map.MapMember(rule => rule.Operator).SetElementName("operator");
            map.MapMember(rule => rule.ErrorMessage).SetElementName("error_message");
        });
    }
    
    public PlatformMongoDbContext(IOptions<MongoDbContextOptions<PlatformMongoDbContext>> options) : base(options)
    {
        EntityChangeLogs = Database.GetCollection<EntityChangeLog>(nameof(EntityChangeLog));
        Workflows = Database.GetCollection<Workflow>(nameof(Workflow));
    }
    
    public IMongoCollection<EntityChangeLog> EntityChangeLogs { get; }
    
    public IMongoCollection<Workflow> Workflows { get; }
}