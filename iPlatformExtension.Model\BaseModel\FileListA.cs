using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "file_list_a", DisableSyncStructure = true)]
	public partial class FileListA {

		[ Column(Name = "id", IsPrimary = true, IsIdentity = true)]
		public int Id { get; set; }

		// [ Column(Name = "file_first_path", StringLength = 250)]
		// public string FileFirstPath { get; set; }

		[ Column(Name = "file_name", StringLength = 100)]
		public string FileName { get; set; }

		[ Column(Name = "file_size")]
		public long? FileSize { get; set; }

		[ Column(Name = "import_remark", StringLength = 50)]
		public string ImportRemark { get; set; }

		[ Column(Name = "input_time")]
		public DateTime? InputTime { get; set; }

		[ Column(Name = "instance_id")]
		public int? InstanceId { get; set; }

		[ Column(Name = "real_name", StringLength = 200)]
		public string RealName { get; set; }

		[ Column(Name = "server_path")]
		public string ServerPath { get; set; }

		/// <summary>
		/// 是否已经迁移
		/// </summary>
		[Column(Name = "migration")]
		public bool Migration { get; set; }

		/// <summary>
		/// 迁移备注
		/// </summary>
		[Column(Name = "migration_remark")]
		public string MigrationRemark { get; set; } = string.Empty;


        /// <summary>
        /// 华为云对应的桶
        /// </summary>
        [Column(Name = "bucket")]
		public string? Bucket { get; set; }

		/// <summary>
		/// 获取华为云OBS的对象名称
		/// </summary>
		/// <returns></returns>
		public string GetObjectName() => Path.Combine(ServerPath, FileName).Replace('\\', '/');
	}

}
