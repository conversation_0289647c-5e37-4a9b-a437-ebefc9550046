﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class CreateDomesticCommissionExceptionHandler(ILogger<CreateDomesticCommissionCommand> logger)
    : IRequestExceptionHandler<CreateDomesticCommissionCommand, Unit, Exception>
{
    public Task Handle(CreateDomesticCommissionCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        logger.LogInsertDomesticTrademarkWeightDataError(request.ProcId, exception);
        if (request.WeightDto is null)
        {
            state.SetHandled(Unit.Value);
        }
        return Task.CompletedTask;
    }
}