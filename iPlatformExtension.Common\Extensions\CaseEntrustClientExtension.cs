﻿using iPlatformExtension.Common.Clients.OrderCaseEntrust;
using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Extensions;

/// <summary>
/// 
/// </summary>
public static class CaseEntrustClientExtension
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddCaseEntrustClient(this IServiceCollection services)
    {
        ArgumentNullException.ThrowIfNull(services);
        services.AddHttpClient<CaseEntrustHttpClient>("CaseEntrust");
        return services;
    }
}