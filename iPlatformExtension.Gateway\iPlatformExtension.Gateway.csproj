<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <LangVersion>13</LangVersion>
    </PropertyGroup>

<!--    <PropertyGroup>-->
<!--        <ContainerBaseImage>mcr.microsoft.com/dotnet/aspnet:8.0</ContainerBaseImage>-->
<!--        <ContainerRuntimeIdentifier>linux-x64</ContainerRuntimeIdentifier>-->
<!--        <ContainerRegistry>nexus.aciplaw.com</ContainerRegistry>-->
<!--        <ContainerRepository>test/iplatform.gateway</ContainerRepository>-->
<!--        <ContainerImageTag>latest</ContainerImageTag>-->
<!--        <ContainerUser>root</ContainerUser>-->
<!--        <PublishProfile>DefaultContainer</PublishProfile>-->
<!--    </PropertyGroup>-->

<!--    <ItemGroup>-->
<!--        <ContainerPort Include="7879" Type="tcp" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_HTTP_PORTS" Value="7879" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_URLS" Value="http://+:7879" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_ENVIRONMENT" Value="Staging" />-->
<!--        <ContainerEnvironmentVariable Include="TZ" Value="Asia/Shanghai" />-->
<!--    </ItemGroup>-->

    <ItemGroup>
      <PackageReference Include="nacos-sdk-csharp.AspNetCore" Version="1.3.9" />
      <PackageReference Include="nacos-sdk-csharp.Extensions.Configuration" Version="1.3.9" />
      <PackageReference Include="Yarp.ReverseProxy" Version="2.2.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\iPlatformExtension.Common\iPlatformExtension.Common.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

</Project>
