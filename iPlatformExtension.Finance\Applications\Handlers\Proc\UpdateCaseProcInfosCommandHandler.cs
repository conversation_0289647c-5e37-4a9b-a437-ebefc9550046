﻿using iPlatformExtension.Finance.Applications.Commands.Proc;
using iPlatformExtension.Finance.Applications.Models.Proc;
using iPlatformExtension.Finance.Infrastructure.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Proc;

internal sealed class UpdateCaseProcInfosCommandHandler(
    IMediator mediator,
    ICaseProcInfoRepository caseProcInfoRepository, 
    ILogger<UpdateCaseProcInfosCommandHandler> logger) 
    : IRequestHandler<UpdateCaseProcInfosCommand, IEnumerable<UpdateCaseProcResult>>
{
    public async Task<IEnumerable<UpdateCaseProcResult>> Handle(UpdateCaseProcInfosCommand request, CancellationToken cancellationToken)
    {
        var documents = request.Documents;
        var procIds = documents.Select(document => document.ProcId).ToList();
        var procInfos = await caseProcInfoRepository.Where(info => procIds.Contains(info.ProcId)).ToListAsync(info =>
            new CaseProcInfo()
            {
                ProcId = info.ProcId,
                DeliveryKey = info.DeliveryKey,
                UpdateUserId = info.UpdateUserId,
                UpdateTime = info.UpdateTime,
                Version = info.Version
            }, cancellationToken);

        var caseProcInfos = procInfos.ToDictionary(info => info.ProcId);
        var updateResults = new List<UpdateCaseProcResult>(procInfos.Count);
        
        foreach (var jsonPatchDocument in documents)
        {
            var procId = jsonPatchDocument.ProcId;
            if (!caseProcInfos.TryGetValue(procId, out var procInfo))
            {
                logger.LogProcInfoNotFound(procId);
                updateResults.Add(new UpdateCaseProcResult(procId, false, "案件任务不存在"));
                continue;
            }
            
            try
            {
                await mediator.Send(new UpdateCaseProcCommand(jsonPatchDocument, procInfo), cancellationToken);
            }
            catch (Exception e)
            {
                logger.LogUpdateProcInfoFailed(e, procId);
                updateResults.Add(new UpdateCaseProcResult(procId, false, e.Message));
            }
        }

        return updateResults;
    }
}