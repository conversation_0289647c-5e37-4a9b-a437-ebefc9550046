﻿using FreeSql.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.IdGenerators;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 实体更改日志
/// </summary>
[Table(Name = "entity_change_log")]
public class EntityChangeLog
{
    /// <summary>
    /// 主键
    /// </summary>
    [Column(Name = "id", IsPrimary = true, IsIdentity = true)]
    public uint Id { get; set; }

    /// <summary>
    /// mongoDB的主键
    /// </summary>
    [BsonId(IdGenerator = typeof(ObjectIdGenerator))]
    [BsonElement("id")]
    public ObjectId ObjectId { get; set; }

    /// <summary>
    /// 表名
    /// </summary>
    [Column(Name = "table_name")]
    [BsonElement("table_name")]
    public string TableName { get; set; } = default!;

    /// <summary>
    /// 实体表对应的主键
    /// </summary>
    [Column(Name = "entity_id")]
    [BsonElement("entity_id")]
    public string EntityId { get; set; } = default!;

    /// <summary>
    /// 记录时间
    /// </summary>
    [Column(Name = "log_time")]
    [BsonElement("log_time")]
    public DateTime LogTime { get; set; }

    /// <summary>
    /// 信息
    /// </summary>
    [Column(Name = "message")]
    [BsonElement("message")]
    public string Message { get; set; } = default!;

    /// <summary>
    /// 变化类型
    /// </summary>
    [Column(Name = "change_type")]
    [BsonElement("change_type")]
    public int ChangeType { get; set; }

    /// <summary>
    /// 操作人员
    /// </summary>
    [Column(Name = "operator")] 
    [BsonElement("operator")]
    public string Operator { get; set; } = default!;

    /// <summary>
    /// 请求追踪id
    /// </summary>
    [Column(Name = "trace_id")] 
    [BsonElement("trace_id")]
    public string TraceId { get; set; } = default!;
    
}