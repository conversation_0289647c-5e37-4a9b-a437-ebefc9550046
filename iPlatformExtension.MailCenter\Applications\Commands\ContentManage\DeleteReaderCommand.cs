﻿using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.ContentManage
{
    public class DeleteReaderCommand : IRequest, IUnitOfWorkCommandMysql
    {
        /// <summary>
        /// 邮件ID
        /// </summary>
        public string MailId { get; set; }

        /// <summary>
        /// 阅读人Id列表
        /// </summary>
        public string[] UserIds { get; set; }
    }
}
