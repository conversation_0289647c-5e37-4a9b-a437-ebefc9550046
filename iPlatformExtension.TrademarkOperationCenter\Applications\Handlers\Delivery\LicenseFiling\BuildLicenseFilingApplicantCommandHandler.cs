﻿using AutoMapper;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.LicenseFiling;

internal sealed class BuildLicenseFilingApplicantCommandHandler(IMapper mapper) : BuildProcApplicantCommandHandler(mapper)
{
    public override string CtrlProcId => CtrlProcIds.LicenseFiling;

    public override IEnumerable<string> CaseDirections => [CaseDirection.II];
}