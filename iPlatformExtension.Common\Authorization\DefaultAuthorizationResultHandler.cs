﻿using System.Text;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Policy;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Common.Authorization;

public sealed class DefaultAuthorizationResultHandler : IAuthorizationMiddlewareResultHandler
{
    private readonly AuthorizationMiddlewareResultHandler _handler = new ();
    
    public Task HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy,
        PolicyAuthorizationResult authorizeResult)
    {
        if (authorizeResult is {Forbidden: true, AuthorizationFailure: not null})
        {
            var reasons = authorizeResult.AuthorizationFailure.FailureReasons;
            if (reasons.Any())
            {
                var services = context.RequestServices;
                var stringBuilderPool = services.GetService<ObjectPool<StringBuilder>>();
                var stringBuilder = stringBuilderPool?.Get() ?? new StringBuilder();

                var responseResult = new ResultData()
                {
                    Code = ResultCode.Failure,
                    Success = false,
                    Message = reasons.Aggregate(stringBuilder, (current, reason) => current.AppendLine(reason.Message))
                        .ToString()
                };
                
                stringBuilderPool?.Return(stringBuilder);

                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                return context.Response.WriteAsJsonAsync(responseResult);
            }
        }
        
        return _handler.HandleAsync(next, context, policy, authorizeResult);
    }
}