﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface IBaseSubProcStatusRepository 
    : IBaseRepository<BasSubProcStatus, string>, 
        IScopeDependency, 
        IRedisCacheableRepository<string, BasSubProcStatus>
{
    Task<BasSubProcStatus?> ICacheableRepository<string, BasSubProcStatus>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Select.Where(status => status.Code == key).ToOneAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasSubProcStatus>> ICacheableRepository<string, BasSubProcStatus>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasSubProcStatus>.GenerateKey(BasSubProcStatus value)
    {
        return value.Code;
    }
}