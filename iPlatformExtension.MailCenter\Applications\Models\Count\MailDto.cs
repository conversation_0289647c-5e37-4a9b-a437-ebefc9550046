﻿namespace iPlatformExtension.MailCenter.Applications.Models.Count;

/// <summary>
/// 邮件数量统计
/// </summary>
/// <param name="Name">Allot:分拣,Handle:收件办理,ReaderList:阅读人</param>
/// <param name="Count">数量</param>
/// <returns></returns>
public record MailDto(string? Name, int Count);

public class MailCount()
{
    public string Type { get; set; }
    public Dictionary<string,int> Dictionary { get; set; }
}