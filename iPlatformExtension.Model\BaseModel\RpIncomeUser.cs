using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_income_user", DisableSyncStructure = true)]
	public partial class RpIncomeUser {

		[ Column(Name = "cn_name", StringLength = 50)]
		public string CnName { get; set; }

		[ Column(Name = "dept_id", StringLength = 50)]
		public string DeptId { get; set; }

		[ Column(Name = "dept_name", StringLength = 500)]
		public string DeptName { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = false;

		[ Column(Name = "user_area", StringLength = 50)]
		public string UserArea { get; set; }

		[ Column(Name = "user_district", StringLength = 50)]
		public string UserDistrict { get; set; }

	}

}
