using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Finance.Applications.Queries.Public;
using iPlatformExtension.Model.Dto;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Finance.Controllers;

/// <summary>
/// 请款对象控制器
/// </summary>
[Route("api/[controller]")]
[ApiController]
public sealed class RequestObjectController : ControllerBase
{
    private readonly IMediator _mediator;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="mediator">中介者</param>
    public RequestObjectController(IMediator mediator)
    {
        _mediator = mediator;
    }


    /// <summary>
    /// 查询请款对象详细信息
    /// </summary>
    /// <param name="requestObjectId">请款对象id</param>
    /// <returns>请款对象详细信息</returns>
    [HttpGet("{requestObjectId}")]
    public Task<RequestObjectInfoDto?> GetRequestObjectInfoAsync([Required]string requestObjectId)
    {
        return _mediator.Send(new RequestObjectDetailQuery(requestObjectId));
    }

    /// <summary>
    /// 根据请款对象名称查询请款对象
    /// </summary>
    /// <param name="requestObjectName">请款对象名称</param>
    /// <returns></returns>
    [HttpGet("name/{requestObjectName}")]
    public Task<IEnumerable<RequestObjectReDto>> GetRequestObjectsAsync([Required]string requestObjectName)
    {
        return _mediator.Send(new RequestObjectQuery
        {
            Name = requestObjectName
        });
    }
}
