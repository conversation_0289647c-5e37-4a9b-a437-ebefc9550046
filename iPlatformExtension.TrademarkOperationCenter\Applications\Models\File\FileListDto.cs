﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;

/// <summary>
/// 选取文件列表的模型
/// </summary>
public class FileListDto
{
    /// <summary>
    /// caseFile文件id
    /// </summary>
    public string CaseFileId { get; set; } = default!;

    /// <summary>
    /// file_list_a文件id
    /// </summary>
    public int FileId { get; set; }
    
    /// <summary>
    /// 文件编号
    /// </summary>
    [JsonIgnore]
    public string FileNo { get; set; } = string.Empty;

    /// <summary>
    /// 关联的对象id
    /// </summary>
    public string ObjectId { get; set; } = default!;
    
    /// <summary>
    /// 文件类型
    /// </summary>
    public string? FileType { get; set ;}

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = default!;
    
    /// <summary>
    /// 文件描述
    /// </summary>
    public string? FileDescription { get; set ;}

    /// <summary>
    /// 文件描述id
    /// </summary>
    public string? FileDescriptionId { get; set; }
    
    /// <summary>
    /// 上传时间
    /// </summary>
    public DateTime UploadTime { get; set; }
    
    /// <summary>
    /// 上传者
    /// </summary>
    public string Uploader { get; set; } = default!;

    /// <summary>
    /// 文件地址
    /// </summary>
    public string? Url { get; set; }
}