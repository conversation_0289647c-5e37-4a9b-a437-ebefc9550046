﻿using System.ComponentModel;

namespace iPlatformExtension.Public.Applications.Models.Customer.Contract;

/// <summary>
/// crm合同主表数据更新
/// </summary>
[Description("crm合同主表数据更新")]
public class CrmContractPatchDto
{
    /// <summary>
    /// 合同标题
    /// </summary>
    [Description("合同标题")]
    public string ContractTitle { get; set; } = string.Empty;

    /// <summary>
    /// 签订日期
    /// </summary>
    [Description("签订日期")]
    public DateOnly SigningDate { get; set; }
    
    /// <summary>
    /// 生效日期
    /// </summary>
    [Description("生效日期")]
    public DateOnly EffectiveDate { get; set; }

    /// <summary>
    /// 截至日期
    /// </summary>
    [Description("截至日期")]
    public DateOnly EndDate { get; set; }

    /// <summary>
    /// 我方签约主体
    /// </summary>
    [Description("我方签约主体")]
    public string MyContractingEntity { get; set; } = string.Empty;

    /// <summary>
    /// 对方签约主体
    /// </summary>
    [Description("对方签约主体")]
    public string CustomerContractingEntity { get; set; } = string.Empty;

    /// <summary>
    /// 操作用户编号
    /// </summary>
    [Description("操作用户编号")]
    public string Operator { get; set; } = string.Empty;

    /// <summary>
    /// 是否作废
    /// </summary>
    [Description("是否作废")]
    public bool IsVoid { get; set; }

    /// <summary>
    /// 作废原因
    /// </summary>
    [Description("作废原因")]
    public string VoidReason { get; set; } = string.Empty;
    
}