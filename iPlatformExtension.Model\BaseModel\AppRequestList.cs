using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_request_list", DisableSyncStructure = true)]
	public partial class AppRequestList {

		[ Column(Name = "apply_id", StringLength = 50, IsNullable = false)]
		public string ApplyId { get; set; }

		[ Column(Name = "request_id", StringLength = 50, IsNullable = false)]
		public string RequestId { get; set; }

	}

}
