﻿using iPlatformExtension.Commission.Application.Notifications.Proc;
using iPlatformExtension.Commission.Application.Queries.Proc;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Proc;

internal sealed class ProcMentorQueryHandler(
    IFreeSql freeSql, 
    IPublisher publisher,
    ICacheableRepository<string, SysUserInfo> userInfoCache) : IRequestHandler<ProcMentorQuery, KeyValuePair<string, string>>
{
    public async Task<KeyValuePair<string, string>> <PERSON>le(ProcMentorQuery request, CancellationToken cancellationToken)
    {
        var procId = request.ProcId;
        var procInfo = await freeSql.Select<CaseProcInfo>(procId).WithLock()
            .ToOneAsync(info => new CaseProcInfo
            {
                ProcId = info.ProcId,
                CaseInfo = new CaseInfo()
                {
                    Id = info.CaseInfo.Id,
                    CaseDirection = info.CaseInfo.CaseDirection,
                    CaseTypeId = info.CaseInfo.CaseTypeId,
                }
            }, cancellationToken);
        
        var notification = new ProcMentorQueryNotification(procId, procInfo.CaseInfo.CaseTypeId, procInfo.CaseInfo.CaseDirection);
        await publisher.Publish(notification, cancellationToken);

        var userInfo =
            await userInfoCache.GetCacheValueAsync(notification.MentorId, cancellationToken: cancellationToken);
        
        return KeyValuePair.Create(notification.MentorId, userInfo?.CnName ?? string.Empty);
    }
}