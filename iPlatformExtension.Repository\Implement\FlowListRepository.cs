﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class FlowListRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    CacheExpirationToken<IEnumerable<FlowListNode>> expirationToken)
    : BaseRepository<SysFlowList, string>(freeSql), IFlowListRepository
{
    private static readonly FlowListKeyEqualityComparer equalityComparer = new FlowListKeyEqualityComparer();

    public IMemoryCache MemoryCache { get; } = memoryCache;

    public CacheExpirationToken<IEnumerable<FlowListNode>> ExpirationToken { get; } = expirationToken;

    IEqualityComparer<FlowListKey>? ICacheableRepository<FlowListKey, IEnumerable<FlowListNode>>.KeyEqualityComparer =>
        equalityComparer;


    private sealed class FlowListKeyEqualityComparer : IEqualityComparer<FlowListKey>
    {
        public bool Equals(FlowListKey? x, FlowListKey? y)
        {
            if (x is null || y is null)
                return false;

            return x.Equals(y);
        }

        public int GetHashCode(FlowListKey obj)
        {
            var (flowType, flowSubType, departmentId) = obj;
            return HashCode.Combine(flowType, flowSubType, departmentId);
        }
    }
}