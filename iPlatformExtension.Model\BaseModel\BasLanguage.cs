using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_language", DisableSyncStructure = true)]
	public partial class BasLanguage {

		/// <summary>
		/// 语言主键ID
		/// </summary>
		[ Column(Name = "language_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string LanguageId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 语言名称编码
		/// </summary>
		[ Column(Name = "language_code", StringLength = 20)]
		public string LanguageCode { get; set; }

		/// <summary>
		/// 语言英文名称
		/// </summary>
		[ Column(Name = "language_en_us", StringLength = 100)]
		public string LanguageEnUs { get; set; }

		/// <summary>
		/// 语言日文名称
		/// </summary>
		[ Column(Name = "language_ja_jp", StringLength = 100)]
		public string LanguageJaJp { get; set; }

		/// <summary>
		/// 语言中文名称
		/// </summary>
		[ Column(Name = "language_zh_cn", StringLength = 50)]
		public string LanguageZhCn { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
