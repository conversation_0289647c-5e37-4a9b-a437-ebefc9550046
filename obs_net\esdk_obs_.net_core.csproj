<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>netcoreapp2.0</TargetFramework>
    <ApplicationIcon />
    <StartupObject />
    <Version>3.0.3</Version>
    <Configurations>Debug;Release;test</Configurations>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
	<DefineConstants>donetcore</DefineConstants>
    <DocumentationFile>bin\Release\netcoreapp2.0\esdk_obs_.net_core.xml</DocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	 <DefineConstants>donetcore</DefineConstants>
    <DocumentationFile>bin\Debug\netcoreapp2.0\esdk_obs_.net_core.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="demo\**" />
    <Compile Remove="packages\**" />
    <EmbeddedResource Remove="demo\**" />
    <EmbeddedResource Remove="packages\**" />
    <None Remove="demo\**" />
    <None Remove="packages\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Properties\AssemblyInfo.cs" />
    <Compile Remove="TestClient.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove=".gitignore" />
    <None Remove="app.config" />
    <None Remove="build.bat" />
    <None Remove="Log4Net.config" />
    <None Remove="packages.config" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Properties\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="log4net" Version="2.0.15" />
  </ItemGroup>

</Project>
