﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Service.Interface;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Finance.Controllers;

/// <summary>
/// 收款主体控制器
/// </summary>
[ApiController]
[Route("/api/[controller]")]
public sealed class PayeeController : ControllerBase
{
    private readonly IPayeeQueryService _payeeQuery;

    /// <summary>
    /// 构造函数注入
    /// </summary>
    /// <param name="payeeQuery">收款主体查询服务</param>
    public PayeeController(IPayeeQueryService payeeQuery)
    {
        _payeeQuery = payeeQuery;
    }

    /// <summary>
    /// 收款主体详细信息
    /// </summary>
    /// <param name="companyId">公司id</param>
    /// <returns>收款主体详细信息</returns>
    [HttpGet("{companyId}")]
    [ResponseCache(Duration = 600)]
    public async Task<PayeeDetailInfoDto?> GetPayeeDetailInfoAsync([Required] string companyId)
    {
        return await _payeeQuery.GetPayeeDetailInfoAsync(companyId);
    }
}