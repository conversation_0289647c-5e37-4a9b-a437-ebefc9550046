﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_attachments", DisableSyncStructure = true)]
	public partial class MailAttachments {

		[Column(Name = "attachment_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string AttachmentId { get; set; }

		/// <summary>
		/// obs桶名
		/// </summary>
		[Column(Name = "bucket", StringLength = 50)]
		public string Bucket { get; set; }

		/// <summary>
		/// 文件后缀
		/// </summary>
		[Column(Name = "extension", StringLength = 10)]
		public string Extension { get; set; }

		/// <summary>
		/// 存储文件名称
		/// </summary>
		[Column(Name = "file_name", StringLength = 100)]
		public string FileName { get; set; }

		/// <summary>
		/// 文件大小
		/// </summary>
		[Column(Name = "file_size")]
		public double? FileSize { get; set; }

		/// <summary>
		/// 文件类型:1.收件附件,2.发件附件,3.签名
		/// </summary>
		[Column(Name = "file_type", StringLength = 50)]
		public string FileType { get; set; }

		[Column(Name = "input_time", DbType = "datetime")]
		public DateTime? InputTime { get; set; }

		/// <summary>
		/// 邮件id
		/// </summary>
		[Column(Name = "mail_id", StringLength = 50, IsNullable = false)]
		public string MailId { get; set; }

		/// <summary>
		/// 真实文件名称
		/// </summary>
		[Column(Name = "real_name", StringLength = 100)]
		public string RealName { get; set; }

		/// <summary>
		/// 存放路径
		/// </summary>
		[Column(Name = "server_path")]
		public string ServerPath { get; set; }

	}

}
