﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Commands.Customer.Contract;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Customer.Contract;

internal sealed class SaveContractDetailCommandHandler(
    IMapper mapper, 
    IUserInfoRepository userInfoRepository,
    IContractDetailRepository contractDetailRepository) : IRequestHandler<SaveContractDetailCommand>
{
    public async Task Handle(SaveContractDetailCommand request, CancellationToken cancellationToken)
    {
        var (contractId, dto) = request;
        var newContractDetail = mapper.Map<ContractDetail>(dto);
        var updateUserId = await userInfoRepository.GetUserIdByUserNameAsync(dto.Updater) ?? string.Empty;
        var preSalesSupportId = await userInfoRepository.GetUserIdByUserNameAsync(dto.PreSalesUser ?? string.Empty) ??
                                string.Empty;
        newContractDetail.UpdaterId = updateUserId;
        newContractDetail.PreSalesSupportId = preSalesSupportId;
        newContractDetail.UpdateTime = DateTime.Now;
        newContractDetail.ContractId = contractId;
        
        
        var oldContractDetail = await contractDetailRepository
            .Where(detail => detail.ContractBusinessType == dto.BusinessType)
            .Where(detail => detail.ContractId == contractId)
            .Where(detail => detail.IsEnable == true)
            .ToOneAsync(cancellationToken);

        if (oldContractDetail is not null)
        {
            oldContractDetail.IsEnable = false;
            oldContractDetail.EndDate = newContractDetail.StartDate;
            oldContractDetail.Updater = dto.Updater;
            oldContractDetail.UpdaterId = updateUserId;
            oldContractDetail.UpdateTime = DateTime.Now;
            await contractDetailRepository.UpdateAsync(oldContractDetail, cancellationToken);
        }

        await contractDetailRepository.InsertAsync(newContractDetail, cancellationToken);
    }
}