﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;

namespace iPlatformExtension.MailCenterRepository.Implement;

internal class MailReceiveFlowRepository(
    IFreeSql<MailCenterFreeSql> fsql,
    UnitOfWorkManage<MailCenterFreeSql> manager)
    : DefaultRepository<MailReceiveFlow, string>(fsql, manager), IMailReceiveFlowRepository;
