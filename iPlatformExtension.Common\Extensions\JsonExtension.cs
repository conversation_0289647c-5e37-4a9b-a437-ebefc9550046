﻿using System.Collections.Concurrent;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace iPlatformExtension.Common.Extensions;

public static class JsonExtension
{
    delegate object? ReadDelegate(JsonConverter converter, ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions serializerOptions);

    private static readonly ConcurrentDictionary<Type, Lazy<ReadDelegate>> readDelegates = new();

    private static ReadDelegate CreateReadDelegate(Type converterType)
    {
        var readerType = typeof(Utf8JsonReader).MakeByRefType();
        var typeOfType = typeof(Type);
        var serializerOptionsType = typeof(JsonSerializerOptions);
        
        var converter = Expression.Parameter(typeof(JsonConverter), "converter");
        var reader = Expression.Parameter(readerType, "reader");
        var convertType = Expression.Parameter(typeOfType, "typeToConvert");
        var serializerOptions = Expression.Parameter(serializerOptionsType, "serializerOptions");
        var converterConvert = Expression.Convert(converter, converterType);

        var method = converterType.GetMethod("Read", BindingFlags.Public | BindingFlags.Instance,
            [readerType, typeOfType, serializerOptionsType]);
        if (method is null)
        {
            throw new MissingMethodException(converterType.FullName, "Read");
        }

        var body = Expression.Call(converterConvert, method, reader, convertType, serializerOptions);
        var returnCast = Expression.Convert(body, typeof(object));

        return Expression.Lambda<ReadDelegate>(returnCast, converter, reader, convertType, serializerOptions).Compile();
    }

    private static ReadDelegate GetReadDelegate(Type converterType)
    {
        return readDelegates.GetOrAdd(converterType, static jsonConverterType =>
            new Lazy<ReadDelegate>(() => CreateReadDelegate(jsonConverterType))).Value;
    }

    public static object? Read(this JsonConverter jsonConverter, ref Utf8JsonReader reader, Type typeToConvert,
        JsonSerializerOptions serializerOptions)
    {
        var readDelegate = GetReadDelegate(jsonConverter.GetType());
        return readDelegate.Invoke(jsonConverter, ref reader, typeToConvert, serializerOptions);
    }
    
    private static readonly
        ConcurrentDictionary<Type, Lazy<Action<JsonConverter, Utf8JsonWriter, object, JsonSerializerOptions>>>
        writeConvertDelegates = new();

    private static Action<JsonConverter, Utf8JsonWriter, object, JsonSerializerOptions> CreateWriteConvertDelegate(Type jsonConverterType, Type valueType)
    {
        var writeMethod = jsonConverterType.GetMethod("Write", BindingFlags.Public | BindingFlags.Instance,
            [typeof(Utf8JsonWriter), valueType, typeof(JsonSerializerOptions)]);
        if (writeMethod is null)
        {
            throw new MissingMethodException(jsonConverterType.FullName, "Write");
        }
        
        var converter = Expression.Parameter(typeof(JsonConverter), "converter");
        var writer = Expression.Parameter(typeof(Utf8JsonWriter), "writer");
        var value = Expression.Parameter(typeof(object), "value");
        var serializerOptions = Expression.Parameter(typeof(JsonSerializerOptions), "serializerOptions");

        var converterConvert = Expression.Convert(converter, jsonConverterType);
        var valueConvert = Expression.Convert(value, valueType);
        var body = Expression.Call(converterConvert, writeMethod, writer, valueConvert, serializerOptions);
        return Expression
            .Lambda<Action<JsonConverter, Utf8JsonWriter, object, JsonSerializerOptions>>(body, converter, writer,
                value, serializerOptions).Compile();
    }

    private static Action<JsonConverter, Utf8JsonWriter, object, JsonSerializerOptions> GetWriteConvertDelegate(
        Type jsonConverterType, Type valueType)
    {
        return writeConvertDelegates.GetOrAdd(jsonConverterType, static (converterType, objectType) =>
            new Lazy<Action<JsonConverter, Utf8JsonWriter, object, JsonSerializerOptions>>(() =>
                CreateWriteConvertDelegate(converterType, objectType)), valueType).Value;
    }

    public static void Write(this JsonConverter jsonConverter, Utf8JsonWriter writer, object value,
        JsonSerializerOptions serializerOptions)
    {
        var write = GetWriteConvertDelegate(jsonConverter.GetType(), value.GetType());
        write(jsonConverter, writer, value, serializerOptions);
    }
    
    public static string JsonPropertyName(this string propertyName, JsonSerializerOptions options)
    {
        return options.PropertyNamingPolicy?.ConvertName(propertyName) ?? propertyName;
    }
}