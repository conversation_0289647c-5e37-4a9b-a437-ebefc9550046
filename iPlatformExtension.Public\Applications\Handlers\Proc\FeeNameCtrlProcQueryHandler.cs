﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Public.Applications.Models.Proc;
using iPlatformExtension.Public.Applications.Queries.Proc;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Proc;

internal sealed class FeeNameCtrlProcQueryHandler(
    IFreeSql<PlatformFreeSql> freeSql,
    IBaseCtrlProcRepository baseCtrlProcRepository,
    ISystemDictionaryRepository systemDictionaryRepository
    ) : IRequestHandler<FeeNameCtrlProcQuery, IEnumerable<FeeNameCtrlProcInfo>>
{
    public async Task<IEnumerable<FeeNameCtrlProcInfo>> Handle(FeeNameCtrlProcQuery request, CancellationToken cancellationToken)
    {
        var basCtrlProc = await freeSql.Select<BasCtrlProc, FeeNameCtrlProc, BasCtrlProcDirection>().WithLock()
            .InnerJoin((proc, info, direction) => proc.CtrlProcId == info.CtrlProcId)
            .LeftJoin((proc, info, direction) => proc.CtrlProcId == direction.CtrlProcId)
            .Where((proc, info, direction) => info.FeeNameId == request.FeeNameId)
            .WhereIf(!string.IsNullOrWhiteSpace(request.Keyword), (proc, info, direction) => proc.CtrlProcZhCn.Contains(request.Keyword))
            .ToListAsync((proc, info, direction) => new FeeNameCtrlProcInfo()
            {
                CtrlProcId = proc.CtrlProcId,
                Attribute = proc.AttributeId,
                Tab = proc.TrademarkTab,
                CaseDirections = direction.CaseDirection,
                IsEnabled = SqlExt.Case().When(proc.IsEnabled == true, "是").Else("否").End()
            }, cancellationToken);


        return await basCtrlProc.GroupBy(x => x.CtrlProcId).ToAsyncEnumerable().SelectAwaitWithCancellation(
            async (group, token) =>
            {
                var ctrlProc = group.First();
                var feeNameCtrlProcInfo = new FeeNameCtrlProcInfo()
                {
                    CtrlProcId = group.Key,
                    Attribute = await systemDictionaryRepository.GetChineseValueAsync(SystemDictionaryName.Attribute,
                        ctrlProc.Attribute),
                    Tab = await systemDictionaryRepository.GetChineseValueAsync(SystemDictionaryName.TrademarkTab,
                        ctrlProc.Tab),
                    CaseDirections = string.Join(",",
                        await group.Where(x => !string.IsNullOrEmpty(x.CaseDirections))
                            .Select(x => x.CaseDirections).ToAsyncEnumerable()
                            .SelectAwait(direction =>
                                systemDictionaryRepository.GetChineseValueAsync(SystemDictionaryName.CaseDirection,
                                    direction))
                            .ToArrayAsync(token)),
                    ProcName = await baseCtrlProcRepository.GetChineseValueAsync(group.Key) ?? string.Empty,
                    IsEnabled = ctrlProc.IsEnabled
                };

                return feeNameCtrlProcInfo;
            }).ToArrayAsync(cancellationToken);
    }
}