﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class CaseForeignContactRepository(
    IFreeSql<PlatformFreeSql> freeSql, UnitOfWorkManage<PlatformFreeSql> unitOfWorkManage) 
    : DefaultRepository<CaseForeignContactList, string>(freeSql, unitOfWorkManage), ICaseForeignContactRepository;