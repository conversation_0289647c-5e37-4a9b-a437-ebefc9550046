﻿using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 递交信息预览
/// </summary>
public record TrademarkDeliveryDto
{

	/// <summary>
	/// 申请人信息快照
	/// </summary>
	public ApplicantInfoSnapshot? ApplicantInfoSnapshot { get; set; }

	/// <summary>
	/// 任务申请人信息快照
	/// </summary>
	public ApplicantInfoSnapshot? ProcApplicantInfoSnapshot { get; set; }

	/// <summary>
	/// 联系人快照信息
	/// </summary>
	public ContactInfoSnapshot? ContactInfoSnapshot { get; set; }

	/// <summary>
	/// 优先权快照信息
	/// </summary>
	public IEnumerable<PriorityInfoSnapshot> PriorityInfoSnapshots { get; set; } = Array.Empty<PriorityInfoSnapshot>();

	/// <summary>
	/// 共同申请人快照信息
	/// </summary>
	public IEnumerable<OtherApplicantInfoSnapshot> OtherApplicantInfoSnapshots { get; set; } =
		Array.Empty<OtherApplicantInfoSnapshot>();

	/// <summary>
	/// 递交文件快照信息
	/// </summary>
	public IEnumerable<FileInfoSnapshot> FileInfoSnapshots { get; set; } = Array.Empty<FileInfoSnapshot>();

	/// <summary>
	/// 商标快照信息
	/// </summary>
	public TrademarkInfoSnapshot TrademarkInfoSnapshot { get; set; } = default!;
	
	/// <summary>
	/// 创建时间
	/// </summary>
	public DateTime CreationTime { get; set; }

	/// <summary>
	/// 创建用户id
	/// </summary>
	public string CreatorId { get; set; } = "";

	/// <summary>
	/// 任务id
	/// </summary>
	public string ProcId { get; set; } = default!;



	/// <summary>
	/// 承办人Id
	/// </summary>
	public string UndertakerId { get; set; } = "";

	/// <summary>
	/// 承办人名字
	/// </summary>
	public string UndertakerName { get; set; } = string.Empty;

	/// <summary>
	/// 更新时间
	/// </summary>
	public DateTime UpdateTime { get; set; }

	/// <summary>
	/// 更新用户id
	/// </summary>
	public string UpdaterId { get; set; } = "";

	/// <summary>
	/// 案件id
	/// </summary>
	public string CaseId { get; set; } = default!;

	/// <summary>
	/// 递交key
	/// </summary>
	public string DeliveryKey { get; set; } = string.Empty;

	/// <summary>
	/// 申请分类
	/// </summary>
	public string TrademarkNiceClasses { get; set; } = string.Empty;

	/// <summary>
	/// 尼斯分类
	/// </summary>
	public IEnumerable<DeliveryNiceCategory> NiceCategories { get; set; } =
		Array.Empty<DeliveryNiceCategory>();
	
	/// <summary>
	/// 其他信息
	/// </summary>
	public OtherInfoSnapshot OtherInfoSnapshot { get; set; } = default!;

	/// <summary>
	/// 权大师订单号
	/// </summary>
	public string? OrderNo { get; set; }
}