﻿using AutoMapper;
using Google.Protobuf;
using Grpc.Core;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.GrpcServices;
using iPlatformExtension.Messages.Mails;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Public.Applications.Models.Statistics;
using iPlatformExtension.Public.Applications.Queries.Statistics;
using MediatR;
using MiniExcelLibs;
using MongoDB.Bson;

namespace iPlatformExtension.Public.Applications.Handlers.Statistics
{
    /// <summary>
    /// 2.3我的团队任务到期提醒（15天内，含当天）
    /// </summary>
    internal sealed class Send15DTeamWarningQueryHandler(
        IFreeSql freeSql,
        IMediator mediator,
        Notification.NotificationClient notificationClient,
        IMapper mapper,
        ILogger<Send15DTeamWarningQuery> logger,
        IHostEnvironment hostEnvironment
    ) : IRequestHandler<Send15DTeamWarningQuery, IEnumerable<Send15DTeamWarningDto>>
    {
        private static readonly string[] DateLineTeamRoles = ["date_line_team"];

        public async Task<IEnumerable<Send15DTeamWarningDto>> Handle(
            Send15DTeamWarningQuery request,
            CancellationToken cancellationToken
        )
        {
            var deptRoleCode = SysEnum.DeptRoleCode.DR.ToString();
            var sysDeptUsers = await freeSql
                .Select<SysDeptUser>()
                .WithLock()
                .Where(it =>
                    (it.DeptRole.RoleCode == deptRoleCode && it.IsDefault) || it.IsDefault == false
                )
                .Where(it => it.UserInfo.Roles.Any(x => DateLineTeamRoles.Contains(x.RoleCode)))
                .Where(it => it.DeptInfo.IsEnabled)
                .WithTempQuery(it => new { it.UserId, it.DeptId })
                .Distinct()
                .ToListAsync(cancellationToken);
            var intFirstDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.IntFirstDate, 14),
                cancellationToken
            );
            var cusFirstDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.CusFirstDate, 14),
                cancellationToken
            );
            var intFinishDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.IntFinishDate, 14),
                cancellationToken
            );
            var cusFinishDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.CusFinishDate, 14),
                cancellationToken
            );
            var legalDueDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.LegalDueDate, 14),
                cancellationToken
            );
            var intFirstDateWarning = mapper.Map<List<IntFirstDateWarning>>(
                intFirstDateWarningDtos
            );
            var cusFirstDateWarning = mapper.Map<List<CusFirstDateWarning>>(
                cusFirstDateWarningDtos
            );
            var intFinishDateWarning = mapper.Map<List<IntFinishDateWarning>>(
                intFinishDateWarningDtos
            );
            var cusFinishDateWarning = mapper.Map<List<CusFinishDateWarning>>(
                cusFinishDateWarningDtos
            );
            var legalDueDateWarning = mapper.Map<List<LegalDueDateWarning>>(
                legalDueDateWarningDtos
            );
            var asyncDuplexStreamingCall = notificationClient.NotifyAsync();
            var readTask = Task.Factory.StartNew(async () =>
            {
                await foreach (
                    var response in asyncDuplexStreamingCall.ResponseStream.ReadAllAsync()
                )
                {
                    if (response is null)
                        continue;

                    var messageId = response.Data.UnpackToString();
                    if (response.Success)
                    {
                        logger.LogInformation(
                            "追踪Id：{TraceId}。邮件Id：{MessageId}。信息：{Message}",
                            response.TraceId,
                            messageId,
                            response.Message
                        );
                    }
                    else
                    {
                        logger.LogError(
                            "追踪Id：{TraceId}。邮件Id：{MessageId}。信息：{Message}",
                            response.TraceId,
                            messageId,
                            response.Message
                        );
                    }
                }
            });
            foreach (var underTakeUser in sysDeptUsers.Select(it => it.UserId).Distinct())
            {
                var ownDeptList = sysDeptUsers
                    .Where(it => it.UserId == underTakeUser)
                    .Select(it => it.DeptId);
                var dictionary = new Dictionary<string, object>()
                {
                    ["官方期限"] = legalDueDateWarning.Where(it => ownDeptList.Contains(it.DeptId)),
                    ["初稿期限(外)"] = cusFirstDateWarning.Where(it =>
                        ownDeptList.Contains(it.DeptId)
                    ),
                    ["初稿期限(内)"] = intFirstDateWarning.Where(it =>
                        ownDeptList.Contains(it.DeptId)
                    ),
                    ["定稿期限(外)"] = cusFinishDateWarning.Where(it =>
                        ownDeptList.Contains(it.DeptId)
                    ),
                    ["定稿期限(内)"] = intFinishDateWarning.Where(it =>
                        ownDeptList.Contains(it.DeptId)
                    ),
                };
                var notificationMail = new NotificationMail
                {
                    Sender = "【正式版本】系统邮箱",
                    MessageId = Guid.NewGuid().ToString(),
                    BodyTemplate = BodyTemplate.MyTeamExpired15Days,
                };
                using (var stream = new MemoryStream())
                {
                    await stream.SaveAsAsync(
                        dictionary,
                        excelType: ExcelType.XLSX,
                        cancellationToken: cancellationToken
                    );
                    stream.Seek(0, SeekOrigin.Begin);

                    notificationMail.Attachments.Add(
                        new MailAttachment
                        {
                            FileName = "我的团队任务到期提醒(15天内,含当天).xlsx",
                            Data = await ByteString.FromStreamAsync(stream, cancellationToken),
                        }
                    );
                    //notificationMail.Receivers.AddRange(new List<string>() { "ea937da8-85fe-498d-9843-17e7d616c692", "c1597c51-781b-4682-81c7-e01dc23431ce" });
                    var receiverIds = hostEnvironment.IsProduction()
                        ? [underTakeUser]
                        : new List<string>
                        {
                            "ea937da8-85fe-498d-9843-17e7d616c692",
                            "c1597c51-781b-4682-81c7-e01dc23431ce",
                        };
                    notificationMail.Receivers.AddRange(receiverIds);
                    logger.LogInformation(
                        notificationMail.ToJson() + notificationMail.Receivers.ToJson()
                    );

                    await asyncDuplexStreamingCall.RequestStream.WriteAsync(
                        notificationMail,
                        cancellationToken
                    );
                }
            }
            await asyncDuplexStreamingCall.RequestStream.CompleteAsync();

            await readTask;
            return new List<Send15DTeamWarningDto>();
        }
    }
}
