﻿using iPlatformExtension.Common.Authentication;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.AuditUser;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AuditUser
{
    /// <summary>
    /// 保存发件必审人命令处理程序
    /// </summary>
    internal sealed class SaveFlowAuditUserCommandHandler(
        IFlowRequireAuditRepository flowRequireAuditRepository,
        IHttpContextAccessor httpContextAccessor) : IRequestHandler<SaveFlowAuditUserCommand, string>
    {
        public async Task<string> Handle(SaveFlowAuditUserCommand request, CancellationToken cancellationToken)
        {
            // 获取当前用户ID
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var now = DateTime.Now;

            // 将必审人和指定审核人列表转换为分号分隔的字符串
            var requireAuditUserIds = string.Join(";", request.RequireAuditUserIds);
            var designatedAuditUserIds = string.Join(";", request.DesignatedAuditUserIds);

            // 检查必审人是否已经存在于历史数据中
            var existingRecords = await flowRequireAuditRepository
                .Where(it => it.Id != request.Id || string.IsNullOrEmpty(request.Id)) // 排除当前记录
                .ToListAsync(cancellationToken);

            // 检查每个必审人是否已经存在
            foreach (var requireUserId in request.RequireAuditUserIds)
            {
                foreach (var record in existingRecords)
                {
                    if (!string.IsNullOrEmpty(record.RequireAuditUser) &&
                        record.RequireAuditUser.Split(';').Contains(requireUserId))
                    {
                        throw new ApplicationException($"必审人 ID {requireUserId} 已经存在于其他记录中，不能重复添加");
                    }
                }
            }

            if (string.IsNullOrEmpty(request.Id))
            {
                // 新增记录
                var newId = Guid.NewGuid().ToString();
                var flowRequireAudit = new FlowRequireAudit
                {
                    Id = newId,
                    RequireAuditUser = requireAuditUserIds,
                    AuditUserId = designatedAuditUserIds,
                    CreateBy = userId,
                    CreateTime = now,
                    UpdateBy = userId,
                    UpdateTime = now
                };

                await flowRequireAuditRepository.InsertAsync(flowRequireAudit, cancellationToken);
                return newId;
            }
            else
            {
                // 更新记录
                var existingRecord = await flowRequireAuditRepository.Where(it => it.Id == request.Id)
                    .FirstAsync(cancellationToken);

                if (existingRecord == null)
                {
                    throw new ApplicationException($"未找到ID为 {request.Id} 的记录");
                }

                existingRecord.RequireAuditUser = requireAuditUserIds;
                existingRecord.AuditUserId = designatedAuditUserIds;
                existingRecord.UpdateBy = userId;
                existingRecord.UpdateTime = now;

                await flowRequireAuditRepository.UpdateAsync(existingRecord, cancellationToken);

                return request.Id;
            }
        }
    }
}
