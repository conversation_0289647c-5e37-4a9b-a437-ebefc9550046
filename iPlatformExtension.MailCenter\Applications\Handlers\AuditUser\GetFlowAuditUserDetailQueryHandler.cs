﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.MailCenter.Applications.Models.AuditUser;
using iPlatformExtension.MailCenter.Applications.Queries.AuditUser;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AuditUser
{
    /// <summary>
    /// 获取发件必审人详情查询处理程序
    /// </summary>
    internal sealed class GetFlowAuditUserDetailQueryHandler(
        IFlowRequireAuditRepository flowRequireAuditRepository,
        IUserInfoRepository userInfoRepository) : IRequestHandler<GetFlowAuditUserDetailQuery, GetFlowAuditUserDto?>
    {
        public async Task<GetFlowAuditUserDto?> Handle(GetFlowAuditUserDetailQuery request, CancellationToken cancellationToken)
        {
            // 查询发件必审人详情
            var auditUser = await flowRequireAuditRepository.Where(it => it.Id == request.Id)
                .FirstAsync(cancellationToken);

            if (auditUser == null)
            {
                return null;
            }

            // 使用缓存获取用户信息
            ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;

            // 必审人可能有多个，使用分号分隔
            var requireAuditUserIds = !string.IsNullOrEmpty(auditUser.RequireAuditUser) ?
                auditUser.RequireAuditUser.Split(';', StringSplitOptions.RemoveEmptyEntries) : Array.Empty<string>();

            // 指定审核人可能有多个，使用分号分隔
            var auditUserIds = !string.IsNullOrEmpty(auditUser.AuditUserId) ?
                auditUser.AuditUserId.Split(';', StringSplitOptions.RemoveEmptyEntries) : Array.Empty<string>();

            // 获取创建人信息
            var createUser = await userBaseInfoRepository.GetCacheValueAsync(auditUser.CreateBy, cancellationToken: cancellationToken);
            var createByUser = new { userId = auditUser.CreateBy, name = createUser?.CnName ?? string.Empty };

            // 获取更新人信息
            var updateUser = await userBaseInfoRepository.GetCacheValueAsync(auditUser.UpdateBy, cancellationToken: cancellationToken);
            var updateByUser = new { userId = auditUser.UpdateBy, name = updateUser?.CnName ?? string.Empty };

            // 将必审人转换为匿名数组形式
            var requireAuditUserArray = new List<object>();
            foreach (var userId in requireAuditUserIds)
            {
                var userInfo = await userBaseInfoRepository.GetCacheValueAsync(userId, cancellationToken: cancellationToken);
                requireAuditUserArray.Add(new { userId, name = userInfo?.CnName ?? string.Empty });
            }

            // 将指定审核人转换为匿名数组形式
            var designatedAuditUserArray = new List<object>();
            foreach (var userId in auditUserIds)
            {
                var userInfo = await userBaseInfoRepository.GetCacheValueAsync(userId, cancellationToken: cancellationToken);
                designatedAuditUserArray.Add(new { userId, name = userInfo?.CnName ?? string.Empty });
            }



            // 构建结果
            return new GetFlowAuditUserDto
            {
                Id = auditUser.Id,
                RequireAuditUsers = requireAuditUserArray,
                DesignatedAuditUsers = designatedAuditUserArray,
                CreateByUser = createByUser,
                CreateTime = auditUser.CreateTime,
                UpdateByUser = updateByUser,
                UpdateTime = auditUser.UpdateTime
            };
        }
    }
}
