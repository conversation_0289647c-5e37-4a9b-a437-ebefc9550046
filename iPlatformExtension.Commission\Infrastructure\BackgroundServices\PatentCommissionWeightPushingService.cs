﻿using System.Threading.Channels;
using iPlatformExtension.Commission.Application.Commands.Patent;
using iPlatformExtension.Commission.Application.Models.Patent;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Commission.Infrastructure.BackgroundServices;

internal sealed class PatentCommissionWeightPushingService
    : BackgroundConsumeService<PushPatentCommissionWeightCommand>
{
    public PatentCommissionWeightPushingService( 
        IOptionsMonitor<PatentPushingOptions> optionsMonitor,
        Channel<PushPatentCommissionWeightCommand> channel,
        ILogger<PatentCommissionWeightPushingService> logger,
        IServiceScopeFactory serviceScopeFactory) : base(channel, logger, serviceScopeFactory)
    {
        optionsMonitor.OnChange(options => options.RefreshRulesEngines());
    }
}