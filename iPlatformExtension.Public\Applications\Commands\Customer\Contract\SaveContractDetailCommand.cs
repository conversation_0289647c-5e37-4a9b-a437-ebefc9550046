﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Public.Applications.Models.Customer.Contract;
using MediatR;

namespace iPlatformExtension.Public.Applications.Commands.Customer.Contract;

internal sealed record SaveContractDetailCommand(string ContractId, CreateContractDetailDto Dto) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;