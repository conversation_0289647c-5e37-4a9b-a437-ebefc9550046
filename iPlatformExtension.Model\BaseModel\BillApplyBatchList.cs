using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_apply_batch_list", DisableSyncStructure = true)]
	public partial class BillApplyBatchList {

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "bill_id", StringLength = 50)]
		public string BillId { get; set; }

	}

}
