﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;
using System.Linq;
using iPlatformExtension.Repository.Extensions;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 团队关联员工查询
    /// </summary>
    public class RelateTeamMemberQueryHandler : IRequestHandler<RelateTeamMemberQuery, RelateTeamMemberDto>
    {
        private readonly IFreeSql _freeSql;
        private readonly ISystemRoleInfoRepository _systemRoleInfoRepository;

        public RelateTeamMemberQueryHandler(IFreeSql freeSql, ISystemRoleInfoRepository systemRoleInfoRepository)
        {
            _freeSql = freeSql;
            _systemRoleInfoRepository = systemRoleInfoRepository;
        }

        public async Task<RelateTeamMemberDto> Handle(RelateTeamMemberQuery request, CancellationToken cancellationToken)
        {

            //获取所有员工列表
            var customerList = await _freeSql.Select<SysDeptInfo, SysUserInfo>().RightJoin((dept, user) => dept.DeptId == user.DeptId)
                .Where((dept, user) => dept.IsEnabled == true && user.IsEnabled && (dept.FullName.Contains("商标版权事业群") && !dept.FullName.Contains("出口商标部")) ||
                                       dept.FullName.Contains("华进联合, 华进杭州, 商标版权部"))
                .ToListAsync((dept, user) => new TeamMemberInfoDto(user.UserId, user.CnName, user.DeptId, dept.DeptName), cancellationToken);
            if (request.TeamId == null)
            {
                return new RelateTeamMemberDto(customerList, new List<RelationTeamMemberInfoDto>());
            }
            //获取已选择员工列表
            var selectTeamMember = _freeSql.Select<SysTeamAssociateMember, SysUserInfo, SysDeptInfo, SysRoleInfo>()
                .LeftJoin((x, u, d, r) => x.UserId == u.UserId)
                .LeftJoin((x, u, d, r) => u.DeptId == d.DeptId)
                .LeftJoin((x, u, d, r) => x.RoleId == r.RoleId)
                .WhereIf(request.TeamId != null, (x, u, d, r) => x.TeamId == request.TeamId
                         && r.RoleType == RoleType.TrademarkOperation.GetHashCode())
                .Where((x, u, d, r) => x.TeamId == request.TeamId)
                .ToList((x, u, d, r) => new { x.UserId, x.RoleId, d.DeptId, d.DeptName });
            var relationTeamMemberInfoDtos = selectTeamMember.Select(it => new RelationTeamMemberInfoDto(it.UserId,
                customerList.FirstOrDefault(x => x.UserId == it.UserId)?.UserName ?? "", it.RoleId,
                _systemRoleInfoRepository.GetChineseKeyValueAsync(it.RoleId).GetAwaiter().GetResult().Value,
                it.DeptId, it.DeptName));

            return new RelateTeamMemberDto(customerList.Where(it => !selectTeamMember.Select(x => x.UserId).Contains(it.UserId)),
                relationTeamMemberInfoDtos);
        }
    }
}

