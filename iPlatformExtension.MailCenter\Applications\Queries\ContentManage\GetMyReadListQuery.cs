﻿using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.ContentManage
{
    /// <summary>
    /// 
    /// </summary>
    public class GetMyReadListQuery() : QueryBase, IRequest<PageResult<ReadListDto>>
    {
        /// <summary>
        /// 模糊查询值
        /// </summary>
        public string? Search { get; set; }


    }
}
