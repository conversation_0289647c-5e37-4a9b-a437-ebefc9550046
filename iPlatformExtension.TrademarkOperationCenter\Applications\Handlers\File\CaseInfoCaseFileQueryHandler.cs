﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File;

internal sealed class CaseInfoCaseFileQueryHandler(IFreeSql freeSql, IMediator mediator, IMapper mapper)
    : IRequestHandler<CaseInfoCaseFileQuery, IEnumerable<FileListDto>>
{
    public async Task<IEnumerable<FileListDto>> Handle(CaseInfoCaseFileQuery request, CancellationToken cancellationToken)
    {
        var (caseId, keyword, pageIndex, pageSize, returnUrl) = request;
        
        var procInfos = await freeSql.Select<CaseProcInfo>().WithLock().Where(info => info.CaseId == caseId)
            .ToListAsync(info => new CaseProcInfo()
            {
                ProcId = info.ProcId,
                CtrlProcId = info.CtrlProcId,
                BasCtrlProc = new BasCtrlProc
                {
                    CtrlProcId = info.BasCtrlProc.CtrlProcId,
                    CtrlProcZhCn = info.BasCtrlProc.CtrlProcZhCn
                }
            }, cancellationToken);

        var fileList = mapper.Map<PageResult<ProcFileListDto>>(
            await mediator.Send(new CaseFileQuery(procInfos.Select(info => info.ProcId), keyword, pageIndex, pageSize, returnUrl), cancellationToken));
        
        return new PageResult<ProcFileListDto>()
        {
            Data = fileList.Join(procInfos, dto => dto.ObjectId, info => info.ProcId, (dto, info) =>
            {
                dto.CtrlProcId = info.CtrlProcId;
                dto.ProcName = info.BasCtrlProc.CtrlProcZhCn;
                return dto;
            }),
            PageSize = pageSize,
            Page = pageIndex,
            Total = fileList.Total
        };
    }
}