using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_visit_type", DisableSyncStructure = true)]
	public partial class BasVisitType {

		/// <summary>
		/// 来访类型ID
		/// </summary>
		[ Column(Name = "visit_type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string VisitTypeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// 来访类型英文
		/// </summary>
		[ Column(Name = "visit_type_en_us", StringLength = 100)]
		public string VisitTypeEnUs { get; set; }

		/// <summary>
		/// 来访类型日文
		/// </summary>
		[ Column(Name = "visit_type_ja_jp", StringLength = 100)]
		public string VisitTypeJaJp { get; set; }

		/// <summary>
		/// 来访类型中文
		/// </summary>
		[ Column(Name = "visit_type_zh_cn", StringLength = 50)]
		public string VisitTypeZhCn { get; set; }

	}

}
