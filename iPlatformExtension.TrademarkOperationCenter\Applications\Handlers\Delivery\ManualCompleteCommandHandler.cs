﻿using System.Security.Claims;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.Models;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class ManualCompleteCommandHandler(IMediator mediator, ConsumeContextAccessor contextAccessor) : IRequestHandler<ManualCompleteCommand>
{
    public async Task Handle(ManualCompleteCommand request, CancellationToken cancellationToken)
    {
        var user = contextAccessor.ConsumeContext?.User;
        var procId = request.ObjectId!;

        if (!IsAuto(user))
        {
            var sendInternalCommand =
                await mediator.Send(new SendDeliveryCommand(procId, DeliveryButton.CancelDelivery, user.GetUserId()),
                    cancellationToken);
            if (sendInternalCommand is not null)
            {
                await mediator.Send(sendInternalCommand, cancellationToken);
            }
        }
    }

    private static bool IsAuto(ClaimsPrincipal? user)
    {
        var username = user?.GetGivenName();
        return !string.IsNullOrWhiteSpace(username) && username.Contains("权大师");
    }
}