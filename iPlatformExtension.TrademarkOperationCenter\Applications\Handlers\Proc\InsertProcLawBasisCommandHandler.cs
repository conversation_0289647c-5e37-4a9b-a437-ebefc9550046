﻿using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class InsertProcLawBasisCommandHandler(
    ITrademarkLawBasisRepository trademarkLawBasisRepository, 
    IFileListARepository fileListARepository,
    IHttpContextAccessor httpContextAccessor,
    HuaweiObsClient huaweiObsClient) 
    : IRequestHandler<InsertProcLawBasisCommand>
{
    public async Task Handle(InsertProcLawBasisCommand request, CancellationToken cancellationToken)
    {
        var dto = request.Dto;
        
        var userId = (httpContextAccessor.HttpContext?.User).GetUserId();
        var fileInfo = await fileListARepository.GetAsync(dto.FileId ?? -1, cancellationToken) ?? new FileListA()
        {
            Id = -1,
            RealName = string.Empty,
            Bucket = string.Empty
        };

        var lawBasis = new TrademarkLawBasis
        {
            Id = default,
            CreationTime = DateTime.Now,
            Creator = userId,
            FileId = fileInfo.Id,
            FileName = fileInfo.RealName,
            LawId = dto.LawBasisId,
            ProcId = dto.ProcId,
            Reason = dto.FactualReason,
            UpdateTime = DateTime.Now,
            Updater = userId,
            Url = dto.FileId is null ? string.Empty : huaweiObsClient.GenerateTemporaryUrl(fileInfo.GetObjectName(),
                fileInfo.Bucket ?? huaweiObsClient.Bucket, TimeSpan.FromDays(365 * 7)).SignUrl
        };

        await trademarkLawBasisRepository.InsertAsync(lawBasis, cancellationToken);
    }
}