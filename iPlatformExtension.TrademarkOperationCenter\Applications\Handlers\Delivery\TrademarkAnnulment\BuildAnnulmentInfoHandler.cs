﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkAnnulment;

internal sealed class BuildAnnulmentInfoHandler(
    IMapper mapper,
    IHttpContextAccessor httpContextAccessor,
    IDeliveryOtherInfoRepository otherInfoRepository,
    IDeliveryCitedTrademarkRepository citedTrademarkRepository) 
    : IMatchTrademarkProcCommandHandler<BuildOtherInfoCommand>
{
    public async Task HandleAsync(BuildOtherInfoCommand notification, CancellationToken cancellationToken)
    {
        var procInfo = notification.ProcInfo;
        var operatorId = (httpContextAccessor.HttpContext?.User).GetUserId();

        var otherInfo = await otherInfoRepository.GetAsync(procInfo.ProcId, cancellationToken) ?? new DeliOtherInfo()
        {
            ProcId = procInfo.ProcId,
            CreationTime = DateTime.Now,
            Creator = operatorId
        };

        otherInfo.ReservationOfSupplementaryMaterial = procInfo.ReservationOfSupplementaryMaterial;
        otherInfo.TrademarkNiceClasses = procInfo.TrademarkNiceClasses;
        otherInfo.HasAbsoluteReason = procInfo.HasAbsoluteReason;
        otherInfo.ExtendToSameAddress = procInfo.ExtendToSameAddress;
        otherInfo.LawProvisions = procInfo.LawProvisions;
        otherInfo.CtrlProcMark = procInfo.CtrlProcMark;
        otherInfo.Updater = operatorId;
        otherInfo.UpdateTime = DateTime.Now;

        await otherInfoRepository.InsertOrUpdateAsync(otherInfo, cancellationToken);

        var citedObjections = procInfo.CitedTrademarks ?? Array.Empty<ProcCitedTrademark>();
        
        if (citedObjections.Count <= 0)
            return;
        
        var citedTrademarkAnnulments = mapper.Map<List<DeliveryCitedTrademark>>(citedObjections);
        foreach (var citedTrademarkObjection in citedTrademarkAnnulments)
        {
            citedTrademarkObjection.CreationTime = DateTime.Now;
            citedTrademarkObjection.UpdateTime = DateTime.Now;
            citedTrademarkObjection.Creator = operatorId;
            citedTrademarkObjection.Updater = operatorId;
        }

        await citedTrademarkRepository.InsertAsync(citedTrademarkAnnulments, cancellationToken);
    }

    public string CtrlProcId => CtrlProcIds.TrademarkAnnulment;

    public IEnumerable<string> CaseDirections => [CaseDirection.II];
}