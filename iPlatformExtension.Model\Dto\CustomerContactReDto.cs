﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto
{
    /// <summary>
    /// 客户联系人返回model
    /// </summary>
    public class CustomerContactReDto
    {
        public string AddressCn { get; set; }


        public string CallName { get; set; }


        public string ContactId { get; set; }


        public string ContactName { get; set; }


        public string ContactType { get; set; }


        public string Email { get; set; }


        public string Fax { get; set; }


        public string IsEnabled { get; set; }


        public string Mobile { get; set; }


        public string Position { get; set; }


        public string Postcode { get; set; }

        public string Qq { get; set; }

        public string Remark { get; set; }

        public string Seq { get; set; }


        public string Tel { get; set; }

        public string Text { get; set; }


        public string Value { get; set; }
    }
}
