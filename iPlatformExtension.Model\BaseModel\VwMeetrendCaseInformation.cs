using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_meetrend_CaseInformation", DisableSyncStructure = true)]
	public partial class VwMeetrendCaseInformation {

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(StringLength = 50)]
		public string FAPPLYTYPE { get; set; }

		[ Column(StringLength = 100)]
		public string FCASEFLOW { get; set; }

		[ Column(StringLength = 100)]
		public string FCASETYPE { get; set; }

		[ Column(StringLength = 50)]
		public string FCOMPANYID { get; set; }

		[ Column(StringLength = 300)]
		public string FCUSTOMERID { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string FID { get; set; }

		[ Column(StringLength = 50)]
		public string FMANGERCOMPANYID { get; set; }

		[ Column(StringLength = 50)]
		public string FNAME { get; set; }

		[ Column(StringLength = 500)]
		public string FNUMBER { get; set; }

		[ Column(StringLength = 50)]
		public string FUSERID { get; set; }

	}

}
