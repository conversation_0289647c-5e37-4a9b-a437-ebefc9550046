using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_tm_update_volume2", DisableSyncStructure = true)]
	public partial class ATmUpdateVolume2 {

		
		public string 案件任务ID { get; set; }

		
		public string 案件ID { get; set; }

		
		public string 任务编号 { get; set; }

		
		public string 任务编号修改后 { get; set; }

		
		public string 我方文号 { get; set; }

		
		public string 我方文号修改后 { get; set; }

	}

}
