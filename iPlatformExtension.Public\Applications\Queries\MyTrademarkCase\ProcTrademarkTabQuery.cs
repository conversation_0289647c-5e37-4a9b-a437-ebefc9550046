﻿using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using MediatR;

namespace iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;

/// <summary>
/// 根据任务id流向查找页签
/// </summary>
/// <param name="CtrlProcId">任务id</param>
/// <param name="CaseDirection">任务流向</param>
public record ProcTrademarkTabQuery(string CtrlProcId,string CaseDirection) : IRequest<ProcTrademarkTabDto>;

