using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery.TrademarkRegistration;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Exceptions;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery.TrademarkRegistration;

internal sealed class TrademarkRegistrationOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IDeliveryInfoRepository deliveryInfoRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    UnitOfWorkManager unitOfWorkManager,
    ILoggerFactory loggerFactory,
    IBaseCountryRepository countryRepository,
    IApplicantTypeRepository applicantTypeRepository,
    PhoenixClientFactory phoenixClientFactory)
    :
        PhoenixDeliveryHandleBase<TrademarkRegistrationOrderCommand, TrademarkRegistrationOrder,
            PhoenixResponseParameters<OrderData>>(stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository,
            unitOfWorkManager, loggerFactory, phoenixClientFactory),
        IDeliveryHandler<TrademarkRegistrationOrderCommand, TrademarkRegistrationOrder,
            PhoenixResponseParameters<OrderData>>
{
    private static readonly IEqualityComparer<NiceClass> niceCategoriesEqualityComparer =
        new NiceClassEqualityComparer();

    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.StartupDelivery;

    public override async Task InitializeAsync(TrademarkRegistrationOrderCommand command, CancellationToken cancellationToken)
    {
        var procId = command.ProcId;
        _deliveryInfo = await DeliveryInfoRepository.Where(info => info.ProcId == procId)
            .Include(info => info.OtherInfo)
            .IncludeMany(deliInfo => deliInfo.Applicants, 
                select => select.Where(applicant => applicant.DeliveryBusinessType == null && applicant.IsRepresent == true))
            .IncludeMany(deliInfo => deliInfo.Priorities)
            .IncludeMany(deliInfo => deliInfo.NiceCategories)
            .IncludeMany(deliInfo => deliInfo.Files)
            .ToOneAsync(cancellationToken);
        if (_deliveryInfo is null)
        {
            throw new NotFoundException(procId, "递交任务");
        }

        if (command.Version != _deliveryInfo.Version)
        {
            throw new VersionException(command.Version, _deliveryInfo.Version);
        }

        _phoenixClient = _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
    }


    public override async Task<TrademarkRegistrationOrder> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        ArgumentNullException.ThrowIfNull(_deliveryInfo.OtherInfo);

        var procId = _deliveryInfo.ProcId;

        var applicant = _deliveryInfo.Applicants!.FirstOrDefault(applicant => applicant.IsRepresent ?? false);
        if (applicant is null)
        {
            throw new PropertyMissingException(procId, applicant, "递交任务", "申请人代表");
        }

        var countryId = applicant.CountryId ??
                        throw new PropertyMissingException(applicant.ApplicantId, applicant.ApplicantId, "申请人");
        var applicantBookType = ApplicantBookType.GetApplicantBookType(countryId);
        
        string country;
        if (applicantBookType == ApplicantBookType.Overseas)
        {
            var countryInfo = await countryRepository.GetCacheValueAsync(countryId);
            if (countryInfo is null)
            {
                throw new NotFoundException(countryId, "国家");
            }

            if (string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCode) || string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCountryName))
            {
                throw new ApplicationException("商标局没有对应的国家编码和名称");
            }

            country = $"{countryInfo.TrademarkDeliveryCode}  {countryInfo.TrademarkDeliveryCountryName}";
        }
        else
        {
            country = applicantBookType.Description;
        }

        var applicantType = await applicantTypeRepository.GetCacheValueAsync(applicant.TypeId.GetOrDefaultEmpty());
        if (applicantType is null)
        {
            throw new ApplicantTypeMissingException(applicant);
        }

        
        var trademarkLogo =
            _deliveryInfo.Files?.SingleOrDefault(file => file is {BaseFileType: "递交官方", FileDesc: "商标图样"})?.Url;
        if (string.IsNullOrWhiteSpace(trademarkLogo))
        {
            throw new PropertyMissingException(procId, trademarkLogo, "递交任务", "商标图样");
        }

        var agencyFile = _deliveryInfo.Files
            ?.SingleOrDefault(file => file is {BaseFileType: "递交官方", FileDesc: "商标代理委托书"})?.Url;
        if (string.IsNullOrWhiteSpace(agencyFile))
        {
            throw new PropertyMissingException(procId, agencyFile, "递交任务", "代理委托书");
        }

        var trademarkBlackLogo = _deliveryInfo.Files
            ?.FirstOrDefault(file => file is {BaseFileType: "递交官方", FileDesc: "商标图样黑白稿"})?.Url;

        var attachments = _deliveryInfo.Files?.Where(file => file.BaseFileType == "申请人文件" || file.FileDesc == "有关说明文件")
            .Select(file => new ApplicantAttachment(
                file.FileName,
                file.Url ?? throw new PropertyMissingException(file.FileId, file.Url, "递交文件", "下载链接"),
                ApplicantAttachmentType.OfficialAttachment,
                ApplicantAttachmentSubType.GetDescriptionAttachmentSubType(file.FileDesc ??
                                                                           throw new PropertyMissingException(
                                                                               file.FileId,
                                                                               file.FileDesc, "文件描述")))).ToArray();
        if (!(attachments?.Any() ?? false))
        {
            throw new PropertyMissingException(procId, attachments, "递交任务", "附件信息");
        }

        var standardNiceCategories = _deliveryInfo.NiceCategories?.Where(category => category.IsStandard).ToArray();
        if (!(standardNiceCategories?.Any() ?? false))
        {
            throw new PropertyMissingException(procId, standardNiceCategories, "递交任务", "标准尼斯分类");
        }
        
        var niceCategories = standardNiceCategories.GroupBy(category => new NiceClass()
        {
            Id = int.Parse(category.GrandId),
            Number = category.GrandNumber,
            Name = category.GrandName
        }, category => new NiceClass()
        {
            Id = int.Parse(category.ParentId),
            Number = category.ParentNumber,
            Name = category.ParentName,
            Children = standardNiceCategories.Where(niceCategory => niceCategory.ParentId == category.ParentId)
                           .Select(niceCategory => new NiceCategory()
                           {
                               Id = int.Parse(niceCategory.CategoryId),
                               Number = niceCategory.CategoryNumber,
                               Name = niceCategory.CategoryName,
                           }).ToArray()
                       ?? throw new NotFoundException(category.Id, "尼斯分类子类")
        }, (parent, children) =>
        {
            parent.Children = children.GroupBy(category => category, niceCategoriesEqualityComparer)
                .Select(group => group.Key);
            return parent;
        }, niceCategoriesEqualityComparer).ToArray();
        if (!(niceCategories?.Any() ?? false))
        {
            throw new PropertyMissingException(procId, niceCategories, "递交任务", "尼斯分类信息");
        }


        var orderParameters = new TrademarkRegistrationOrder()
        {
            Code = applicant.Postcode,
            OwnerType = applicantType.ApplicantTypeCode == "5" ? "0" : "1",
            Country = country,
            IdCard = applicant.CardNo,
            PrincipalTel = _deliveryInfo.ContactTel ?? throw new PropertyMissingException(procId, _deliveryInfo.ContactTel, "递交任务"),
            AgentOrganConName = _deliveryInfo.AgentUser ?? throw new PropertyMissingException(procId, _deliveryInfo.AgentUser, "递交任务"),
            ContactTel = _deliveryInfo.ContactTel,
            AgentOrganTel = _deliveryInfo.ContactTel,
            SubjectType = applicant.IsChineseIdentity ? "1" : "0",
            DomesticReceiverName = _deliveryInfo.AgencyName ?? throw new PropertyMissingException(procId, _deliveryInfo.AgencyName, "递交任务"),
            DomesticReceiverAddress = _deliveryInfo.ContactAddress ?? throw new PropertyMissingException(procId, _deliveryInfo.ContactAddress, "递交任务"),
            DomesticReceiverEmail = _deliveryInfo.ContactMailBox ?? throw new PropertyMissingException(procId, _deliveryInfo.ContactMailBox, "递交任务"),
            DomesticReceiverCode = _deliveryInfo.ContactPostCode ?? throw new PropertyMissingException(procId, _deliveryInfo.ContactPostCode, "递交任务"),
            ContactEmail = _deliveryInfo.ContactMailBox ?? throw new PropertyMissingException(procId, _deliveryInfo.ContactMailBox, "递交任务"),
            ApplicantAddress = applicant.AddressCn ?? throw new PropertyMissingException(applicant.ApplicantId, applicant.AddressCn, "申请人"),
            ApplicantEnglishAddress = applicantBookType != ApplicantBookType.ChinaMainland ? applicant.AddressEn ?? throw new PropertyMissingException(applicant.Id, applicant.AddressEn, "申请人", "申请人英文地址"): string.Empty,
            ContactName = _deliveryInfo.ContactPerson ?? throw new PropertyMissingException(procId, _deliveryInfo.ContactPerson, "递交任务"),
            PrincipalName = _deliveryInfo.ContactPerson,
            ApplicantName = applicant.ApplicantNameCn,
            EnglishApplicantName = applicantBookType != ApplicantBookType.ChinaMainland ? applicant.ApplicantNameEn : string.Empty,
            BookType = applicantBookType.Code.ToString(),
            CertificatesType = applicantType.ApplicantTypeCode == "5" ? ApplicantCertificationType.GetApplicantCertificationType(applicant.CardType).Code
                .ToString() : string.Empty,
            BrandFile = trademarkLogo,
            BrandBlackFile = trademarkBlackLogo,
            BrandExplain = _deliveryInfo.Description ?? string.Empty,
            PowerOfAttorneyFile = agencyFile,
            Attachments = attachments,
            BrandName = _deliveryInfo.OtherInfo.OfficialName ?? _deliveryInfo.CaseName,
            BrandType = TrademarkType.Hybrid,
            OrderToken = procId,
            NiceCategories = niceCategories,
            Number = niceCategories.Length
        };

        return orderParameters;
    }

    public override Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(TrademarkRegistrationOrder request,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_phoenixClient);
        return _phoenixClient.CreateOrderAsync(PhoenixUri.CreateOrder, request);
    }

    public override async Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        var orderNo = response.Data?.OrderNo!;
        const int newStatus = (int) DeliveryStatus.Ordered;
        
        await Handler.UpdateDeliveryInfoInternalAsync(deliveryInfo =>
        {
            deliveryInfo.Status = newStatus;
            deliveryInfo.OrderNo = orderNo;
            deliveryInfo.OperationResult = true;
            return ValueTask.CompletedTask;
        }, cancellationToken);
    }

    public async Task<bool> HandleExceptionAsync(Exception ex, CancellationToken cancellationToken = default)
    {
        await Handler.HandleExceptionInternalAsync(ex, true, false, cancellationToken);
        return false;
    }

    private class NiceClassEqualityComparer : IEqualityComparer<NiceClass>
    {
        public bool Equals(NiceClass? x, NiceClass? y)
        {
            if (x is null || y is null)
                return false;
            return x.Number == y.Number && x.Name == y.Name;
        }

        public int GetHashCode(NiceClass obj)
        {
            return HashCode.Combine(obj.Number, obj.Name);
        }
    }
}