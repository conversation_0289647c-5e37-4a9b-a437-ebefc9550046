﻿using iPlatformExtension.Common.Cache;

namespace iPlatformExtension.Repository.Extensions;

public static class CacheableRepositoryExtension
{
    /// <summary>
    /// 获取缓存仓储的中文名称键值对
    /// </summary>
    /// <param name="cacheableRepository">缓存仓储</param>
    /// <param name="key">基础任务类型id</param>
    /// <returns>基础任务类型id中文键值对</returns>
    public static async ValueTask<KeyValuePair<string, string>> GetChineseKeyValueAsync<TValue>(this IStringKeyCacheableRepository<TValue> cacheableRepository, string? key)
    {
        ArgumentNullException.ThrowIfNull(cacheableRepository);
        if (string.IsNullOrWhiteSpace(key))
        {
            return new KeyValuePair<string, string>();
        }

        var value = await cacheableRepository.GetTextValueAsync(key) ?? string.Empty;
        return new KeyValuePair<string, string>(key, value);
    }
    
    /// <summary>
    /// 获取缓存仓储键所对应的中文名称
    /// </summary>
    /// <param name="cacheableRepository">基础任务类型仓储</param>
    /// <param name="key">基础任务类型id</param>
    /// <returns>基础任务类型中文名称</returns>
    public static ValueTask<string?> GetChineseValueAsync<TValue>(this IStringKeyCacheableRepository<TValue> cacheableRepository, string? key)
    {
        return string.IsNullOrWhiteSpace(key) ? new ValueTask<string?>() : cacheableRepository.GetTextValueAsync(key);
    }
}