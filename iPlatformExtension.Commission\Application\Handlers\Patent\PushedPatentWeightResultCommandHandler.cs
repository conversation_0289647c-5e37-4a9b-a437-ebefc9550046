﻿using iPlatformExtension.Commission.Application.Commands.Patent;
using iPlatformExtension.Commission.Infrastructure.Logging;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Patent;

internal sealed class PushedPatentWeightResultCommandHandler(
    IBillBonusCaseListRepository bonusCaseListRepository,
    ILogger<PushedPatentWeightResultCommandHandler> logger) 
    : IRequestHandler<PushedPatentWeightResultCommand>
{
    public async Task Handle(PushedPatentWeightResultCommand request, CancellationToken cancellationToken)
    {
        var messageResult = request.MessageResult;
        logger.LogMessageResult(messageResult);
        
        var id = messageResult.Data.UnpackToString();
        
        var bonus = await bonusCaseListRepository.GetAsync(id, cancellationToken);
        if (bonus is null)
        {
            return;
        }

        if (!messageResult.Success)
        {
            logger.LogPatentCommissionPushedFail(id, messageResult.Message);
        }
        else
        {
            bonus.Pushed = true;
            bonus.UpdateTime = DateTime.Now;
            bonus.UpdateUserId = UserIds.Administrator;
            
            await bonusCaseListRepository.UpdateAsync(bonus, cancellationToken);
        }
    }
}