﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;
using MediatR;
using Microsoft.AspNetCore.Http.HttpResults;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 团队移交处理者
    /// </summary>
    /// <remarks>
    /// 团队移交构造函数
    /// </remarks>
    internal sealed class HandoverAllotCommandHandler(IFreeSql freeSql,
        ICaseAllotRepository caseAllotRepository,
        IFlowActivityRepository iFlowActivityRepository,
        IHttpContextAccessor httpContextAccessor,
        IFlowHistoryRepository iFlowHistoryRepository,
        ICaseProcInfoRepository iCaseProcInfoRepository,
        ISysFlowNodeRepository iSysFlowNodeRepository) : IRequestHandler<HandoverAllotCommand>
    {
        /// <summary>
        /// 团队移交方法
        /// </summary>
        public async Task Handle(HandoverAllotCommand request, CancellationToken cancellationToken)
        {
            //获取流程节点
            var sysFlowNode = await iSysFlowNodeRepository.GetCacheValueAsync(new(FlowTypeEnum.AT, CASE_PROC_STATUS.PA));
            ArgumentNullException.ThrowIfNull(sysFlowNode);
            var sysUserInfo = await freeSql.Select<SysUserInfo>().Where(it => it.UserId == request.UserId).WithLock().ToOneAsync(cancellationToken);
            var sysFlowConfig = await freeSql.Select<SysFlowConfig>().Where(it => it.DeptId == "base" && it.IsEnabled == true && it.FlowType == FlowTypeEnum.AT && it.FlowSubType == "TII").WithLock().ToOneAsync(cancellationToken);

            //获取操作用户信息
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(string.IsNullOrWhiteSpace(userId));
            var userName = httpContextAccessor.HttpContext?.User.GetUserName() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(string.IsNullOrWhiteSpace(userName));


            //获取流程活动节点，更新节点
            var flowActivityList = await iFlowActivityRepository.Where(it => request.ProcId.Contains(it.ObjId) && it.FlowType == FlowTypeEnum.AT).ToListAsync(cancellationToken);
            if (flowActivityList.Count > 0)
            {
                foreach (var flowActivity in flowActivityList)
                {
                    flowActivity.Status = FLOW_STATUS.S1000;
                    flowActivity.FlowId = sysFlowConfig.FlowId;
                    flowActivity.CurUserId = request.UserId;
                    flowActivity.CurNodeId = sysFlowNode.NodeId;
                    flowActivity.UpdateUserId = userId;
                    flowActivity.UpdateTime = DateTime.Now;
                    flowActivity.PrevNodeId = sysFlowNode.NodeId;
                    await iFlowActivityRepository.UpdateAsync(flowActivity, cancellationToken);
                    //添加流程历史
                    var sysFlowHistory = new SysFlowHistory
                    {
                        HistoryId = Guid.NewGuid().ToString(),
                        AuditCnName = userName,
                        AuditTime = DateTime.Now,
                        AuditTypeId = FLOW_AUDIT_TYPE.HANDOVER,
                        AuditUserId = userId,
                        FlowSubType = flowActivity.FlowSubType,
                        FlowType = FlowTypeEnum.AT,
                        NodeId = sysFlowNode.NodeId,
                        ObjId = flowActivity.ObjId,
                        Remark = request.Remark,
                    };
                    await iFlowHistoryRepository.InsertAsync(sysFlowHistory, cancellationToken);
                }
                //更新部门信息
                await caseAllotRepository.UpdateDiy.Set(a => a.UndertakeDeptId, sysUserInfo.DeptId)
                    .Set(a => a.FlowUpdateTime, DateTime.Now)
                    .Set(a => a.UpdateUserId, userId)
                    .Set(a => a.FlowCurUserId, request.UserId)
                    .Where(it => request.ProcId.Contains(it.ProcId)).ExecuteAffrowsAsync(cancellationToken);
                //更新proc任务下的团队id
                await iCaseProcInfoRepository.UpdateDiy.Set(a => a.ProcDeptId, sysUserInfo.DeptId)
                    .Set(it => it.UpdateTime, DateTime.Now)
                    .Set(a => a.UpdateUserId, userId)
                    .Set(a => a.TeamId, request.TeamId)
                    .Where(it => request.ProcId.Contains(it.ProcId)).ExecuteAffrowsAsync(cancellationToken);
            }
            else
            {
                throw new ApplicationException("没有对应流程");
            }

        }
    }
}

