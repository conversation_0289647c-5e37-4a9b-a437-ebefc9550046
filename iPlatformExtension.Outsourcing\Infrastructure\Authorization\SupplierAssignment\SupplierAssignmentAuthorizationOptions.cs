﻿using System.Text.RegularExpressions;

namespace iPlatformExtension.Outsourcing.Infrastructure.Authorization.SupplierAssignment;

internal sealed class SupplierAssignmentAuthorizationOptions
{
    /// <summary>
    /// 
    /// </summary>
    public string AuthorizationType { get; set; } = null!;

    /// <summary>
    /// 
    /// </summary>
    public string AuthorizationObjectType { get; set; } = null!;

    /// <summary>
    /// 
    /// </summary>
    public KeyValuePair<string, Regex> PersonalQueryFilterEntry { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public KeyValuePair<string, Regex> WholeQueryFilterEntry { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public IEnumerable<string> CaseTypes
    {
        get;
        set
        {
            if (value.Any() )
            {
                field = value;
            }
            else
            {
                throw new ArgumentException("At least one case type must be specified.");
            }
        }
    } = null!;

    /// <summary>
    /// 
    /// </summary>
    public IEnumerable<string> CaseDirections
    {
        get;
        set
        {
            if (value.Any())
            {
                field = value;
            }
            else
            {
                throw new ArgumentException("At least one case direction must be specified.");
            }
        }
    } = null!;

    public void Deconstruct(out string authorizationObjectType,
        out IEnumerable<string> caseTypes, out IEnumerable<string> caseDirections, 
        out KeyValuePair<string, Regex> personalQueryFilterEntry, out KeyValuePair<string, Regex> wholeQueryFilterEntry)
    {
        authorizationObjectType = AuthorizationObjectType;
        caseTypes = CaseTypes;
        caseDirections = CaseDirections;
        personalQueryFilterEntry = PersonalQueryFilterEntry;
        wholeQueryFilterEntry = WholeQueryFilterEntry;
    }
}