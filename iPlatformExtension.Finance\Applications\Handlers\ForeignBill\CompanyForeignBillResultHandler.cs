﻿using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal sealed class CompanyForeignBillResultHandler(ICompanyRepository companyRepository) : ForeignFeeResultHandlerBase
{
    protected override async ValueTask GetResultAsync(ForeignBillFeeExportDto dto)
    {
        dto.RemittanceCompany = await companyRepository.GetTextValueAsync(dto.RemittanceCompany) ?? string.Empty;
    }
}