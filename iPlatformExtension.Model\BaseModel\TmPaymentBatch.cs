using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "tm_payment_batch", DisableSyncStructure = true)]
	public partial class TmPaymentBatch {

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "batch_no", StringLength = 50, IsNullable = false)]
		public string BatchNo { get; set; }

		[ Column(Name = "batch_status")]
		public int BatchStatus { get; set; } = 0;

		[ Column(Name = "batch_title", StringLength = 500, IsNullable = false)]
		public string BatchTitle { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_del")]
		public bool IsDel { get; set; } = true;

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
