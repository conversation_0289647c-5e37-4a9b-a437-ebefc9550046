﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 商标信息
/// </summary>
public sealed class BrandInfo
{
    /// <summary>
    /// 申请人名称
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ApplicantName { get; set; }
    
    /// <summary>
    /// 商标名称
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("brandName")]
    public string? BrandName { get; set; }

    /// <summary>
    /// 需要撤回的业务对应的官方申请号
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? PreBrandRegisterNo { get; set; }

    /// <summary>
    /// 撤回业务的标志
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? DeleteType { get; set; }

    /// <summary>
    /// 注册号
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("brandRegisterNo")]
    public string? BrandRegisterNo { get; set; }

    /// <summary>
    /// 商标状态，商标注册申请 | 申请收文
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("categoryFlowStateName")]
    public string? CategoryFlowStateName { get; set; }

    /// <summary>
    /// 大类编码名称
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("firstCgName")]
    public string? FirstCgName { get; set; }

    /// <summary>
    /// 大类编码
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("firstCgNo")]
    public string? FirstCgNo { get; set; }
    
    /// <summary>
    /// 商标logo地址
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? LogoUrl { get; set; }

    /// <summary>
    /// 商标logo地址
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ImageUrl { get; set; }

    /// <summary>
    /// 小项
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("info")]
    public GoodsInfo? Info { get; set; }

    /// <summary>
    /// 商标小项信息
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("brandTeam")]
    public IEnumerable<GoodsItem>? GoodsItems { get; set; }

    /// <summary>
    /// 小项个数
    /// </summary>
    [JsonPropertyName("number")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? Number { get; set; }

    /// <summary>
    /// 大类号码
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("intCls")]
    public string? CategoryNumber { get; set; }
}