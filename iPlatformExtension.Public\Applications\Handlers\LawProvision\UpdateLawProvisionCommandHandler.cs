﻿using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Public.Applications.Commands.LawProvision;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.LawProvision;

internal sealed class UpdateLawProvisionCommandHandler(IFreeSql freeSql, PhoenixClientFactory phoenixClientFactory) : IRequestHandler<UpdateLawProvisionCommand>
{
    public async Task Handle(UpdateLawProvisionCommand request, CancellationToken cancellationToken)
    {
        var client = phoenixClientFactory.CreateClient();
        var lawProvisionsData = (await client.GetLawProvisionsAsync(new LawProvisionsRequest
        {
            ProductBaseId = request.ProductId
        }))?.Data ?? Array.Empty<LawProvisionsResponse>();
        if (!lawProvisionsData.Any())
        {
            return;
        }
        
        await freeSql.Delete<SysDictionary>().Where(dictionary => dictionary.DictionaryName == request.DictionaryName)
            .ExecuteAffrowsAsync(cancellationToken);

        var lawProvisionDictionary = lawProvisionsData.Select(data => new SysDictionary
        {
            DictionaryName = request.DictionaryName,
            DictionaryDesc = request.DictionaryDescription,
            Value = data.LawId.ToString(),
            IsEnabled = true,
            TextZhCn = data.LawName
        }).ToList();

        await freeSql.Insert(lawProvisionDictionary)
            .ExecuteSqlBulkCopyAsync(cancellationToken: cancellationToken);
    }
}