using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "tm_apply_update_info", DisableSyncStructure = true)]
	public partial class TmApplyUpdateInfo {

		[ Column(Name = "amount", StringLength = 50)]
		public string Amount { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "daili_wenhao", StringLength = 50)]
		public string <PERSON><PERSON><PERSON><PERSON>hao { get; set; }

		[ Column(Name = "flow_status", StringLength = 50)]
		public string FlowStatus { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "shenqing_hao", StringLength = 50)]
		public string <PERSON><PERSON>ao { get; set; }

		[ Column(Name = "shenqing_ren", StringLength = 2000)]
		public string ShenqingRen { get; set; }

		[ Column(Name = "shenqing_riqi")]
		public DateTime? ShenqingRiqi { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "yewu_leixing", StringLength = 200)]
		public string YewuLeixing { get; set; }

		[ Column(Name = "yewu_zhuangtai", StringLength = 200)]
		public string YewuZhuangtai { get; set; }

	}

}
