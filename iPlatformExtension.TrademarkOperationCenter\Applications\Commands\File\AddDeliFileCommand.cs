﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using MediatR;
namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;

internal sealed record AddDeliFileCommand(IEnumerable<AddDeliFileDto> Files) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;