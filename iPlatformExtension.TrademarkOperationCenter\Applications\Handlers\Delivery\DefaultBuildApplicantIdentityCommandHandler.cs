﻿using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DefaultBuildApplicantIdentityCommandHandler(
    IDeliveryFilesRepository deliveryFilesRepository,
    HuaweiObsClient huaweiObsClient,
    IFileTypeRepository fileTypeRepository,
    IFileDescriptionRepository fileDescriptionRepository,
    IUserInfoRepository userInfoRepository)
    : IMatchNotificationHandler<BuildApplicantIdentityCommand>
{
    private static readonly IEnumerable<string> expectedCtrProcIds =
        [CtrlProcIds.TrademarkTransfer, CtrlProcIds.RefuseReexamination, CtrlProcIds.TrademarkAnnulment];

    ValueTask<bool> IMatchNotificationHandler<BuildApplicantIdentityCommand>.MatchAsync(BuildApplicantIdentityCommand notification, CancellationToken cancellationToken)
    {
        var (_, _, caseDirection, ctrlProcId) = notification;
        return new ValueTask<bool>(StringComparer.OrdinalIgnoreCase.Equals(caseDirection, CaseDirection.II) &&
                                   !expectedCtrProcIds.Contains(ctrlProcId));
    }

    async Task IMatchNotificationHandler<BuildApplicantIdentityCommand>.HandleAsync(BuildApplicantIdentityCommand notification, CancellationToken cancellationToken)
    {

        var (applicant, deliFiles, _, _) = notification;
        var identityFiles = await deliveryFilesRepository.Orm.Select<CaseFile>().WithLock()
            .Where(file => file.ObjId == applicant.ApplicantId && file.FileDescription!.FileType!.FileTypeZhCn == "申请人文件")
            .ToListAsync(cancellationToken);

        var fileIds = identityFiles.Select(file => int.Parse(file.FileNo[4..])).ToArray();
        var files = await deliveryFilesRepository.Orm.Select<FileListA>().WithLock()
            .Where(a => fileIds.Contains(a.Id)).ToListAsync(a =>
            new FileListA()
            {
                Id = a.Id,
                Bucket = a.Bucket,
                ServerPath = a.ServerPath,
                FileName = a.FileName,
                InputTime = a.InputTime
            }, cancellationToken);

        var deliveryFiles = await identityFiles.ToAsyncEnumerable().JoinAwait(files.ToAsyncEnumerable(),
            file => new ValueTask<int>(Convert.ToInt32(file.FileNo[4..])), a => new ValueTask<int>(a.Id),
            async (file, a) =>
            {
                var fileDescription = await fileDescriptionRepository.GetCacheValueAsync(file.DescId);
                if (fileDescription is null)
                {
                    throw new NotFoundException(file.DescId, "文件描述");
                }
                var fileType =
                    (await fileTypeRepository.GetCacheValueAsync(fileDescription.FileTypeId ?? string.Empty))
                    ?.FileTypeZhCn;
                var bucket = a.Bucket;
                var objectName = a.GetObjectName();
                return new DeliFiles()
                {
                    Id = a.Id,
                    ProcId = applicant.ProcId,
                    FileCode = fileDescription.TextCode ?? throw new PropertyMissingException(fileDescription.FileDescId, fileDescription.TextCode, "文件描述", "文件描述编码"),
                    FileNo = file.FileNo,
                    FileName = file.FileName,
                    FileEx = file.FileEx,
                    FileDesc = fileDescription.FileDescZhCn,
                    BaseFileType = fileType,
                    Url = huaweiObsClient.GenerateTemporaryUrl(objectName, bucket, TimeSpan.FromDays(7 * 365)).SignUrl,
                    IsIdentity = false,
                    CaseFileId = file.FileId,
                    UploadTime = a.InputTime,
                    Uploader = await userInfoRepository.GetChineseValueAsync(file.CreateUserId) ?? string.Empty
                };
            }).ToListAsync(cancellationToken);
        
        for (var i = 0; i < deliveryFiles.Count; i++)
        {
            deliFiles.Add(deliveryFiles[i]);
        }
    }
}