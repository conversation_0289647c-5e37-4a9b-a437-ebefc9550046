using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "product_fee", DisableSyncStructure = true)]
	public partial class ProductFee {

		/// <summary>
		/// 业务费项ID
		/// </summary>
		[ Column(Name = "fee_id", StringLength = 50)]
		public string FeeId { get; set; }

		/// <summary>
		/// 订单费项ID
		/// </summary>
		[ Column(Name = "order_fee_id", StringLength = 50)]
		public string OrderFeeId { get; set; }

		/// <summary>
		/// 产品实例ID
		/// </summary>
		[ Column(Name = "product_info_id", StringLength = 50, IsNullable = false)]
		public string ProductInfoId { get; set; }

	}

}
