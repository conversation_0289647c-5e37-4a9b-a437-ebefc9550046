﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

internal sealed record BuildNiceInfoCommand(DeliInfo DeliveryInfo, CaseProcInfo ProcInfo) : IMatchTrademarkProcCommand
{
    public string CtrlProcId => DeliveryInfo.CtrlProcId;

    public string CaseDirection => ProcInfo.CaseInfo.CaseDirection;
}