﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 到款处理人匹配结果
/// </summary>
public record struct ReceivedBillHandlerMatchResult
{
    /// <summary>
    /// 匹配结果成与否
    /// </summary>
    public bool MatchSuccess { get; init; }
    
    /// <summary>
    /// 请款对象
    /// </summary>
    public ReceiveBillHandlerInfo.RequestObjectInfo RequestObject { get; set; }

    /// <summary>
    /// 客户
    /// </summary>
    public ReceiveBillHandlerInfo.MatchedCustomerInfo Customer { get; set; }

    /// <summary>
    /// 商务人员
    /// </summary>
    public UserInfoDto BusinessPersonnel { get; set; }

    /// <summary>
    /// 销售
    /// </summary>
    public UserInfoDto? Salesman { get; set; }

    /// <summary>
    /// 匹配信息
    /// </summary>
    public string? Message { get; set; }
    
}