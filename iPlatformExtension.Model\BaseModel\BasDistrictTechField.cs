using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_district_tech_field", DisableSyncStructure = true)]
	public partial class BasDistrictTechField {

		[ Column(Name = "agent_id", StringLength = 50, IsNullable = false)]
		public string AgentId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "tech_field_id", StringLength = 50)]
		public string TechFieldId { get; set; }

	}

}
