﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
namespace iPlatformExtension.Common.Mediator.PipelineBehaviors;

public sealed class MssqlUnitOfWorkPipelineBehavior<TRequest, TResult>(
    IMediator mediator,
    ObjectPool<StringBuilder> stringBuilderPool,
    UnitOfWorkManage<PlatformFreeSql> manage,
    ILoggerFactory loggerFactory)
    : IMssqlIUnitOfWorkPipelineBehavior<TRequest, TResult>
    where TRequest : IUnitOfWorkCommandMssql
{
    public UnitOfWorkManager UnitOfWorkManager { get; } = manage;

    public ILogger Logger { get; } = loggerFactory.CreateLogger<MssqlUnitOfWorkPipelineBehavior<TRequest, TResult>>();

    public ObjectPool<StringBuilder> StringBuilderPool { get; } = stringBuilderPool;

    public IMediator Mediator { get; } = mediator;
}
