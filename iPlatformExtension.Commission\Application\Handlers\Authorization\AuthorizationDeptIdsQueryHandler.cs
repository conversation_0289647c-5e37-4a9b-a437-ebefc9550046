﻿using iPlatformExtension.Commission.Application.Queries.Authrorization;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Authorization;

internal sealed class AuthorizationDeptIdsQueryHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor) 
    : IRequestHandler<AuthorizationDeptIdsQuery, IEnumerable<string>>
{
    public async Task<IEnumerable<string>> <PERSON>le(AuthorizationDeptIdsQuery request, CancellationToken cancellationToken)
    {
        var user = httpContextAccessor.HttpContext?.User;
        if (user is null)
        {
            throw new ApplicationException("You are not logged in.");
        }
        
        var userId = user.GetUserId();
        var deptIds = await freeSql.Select<SysDeptUser>().WithLock()
            .Where(deptUser => deptUser.UserId == userId).Where(deptUser => deptUser.IsDefault == false)
            .Where(deptUser => deptUser.RoleId == "4857670F-1F1E-46B5-A985-508FB630DD12")
            .GroupBy(deptUser => deptUser.DeptId)
            .ToListAsync(aggregate => aggregate.Key, cancellationToken: cancellationToken);
        
        
        return deptIds;
    }
}