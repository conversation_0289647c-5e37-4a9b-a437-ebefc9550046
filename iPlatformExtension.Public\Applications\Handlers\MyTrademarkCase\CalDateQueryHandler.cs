﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.DateCalculation;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.DateCalculation;
using iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.MyTrademarkCase
{
    /// <summary>
    /// 日期计算
    /// </summary>
    internal sealed class CalDateQueryHandler(IFreeSql freeSql, IMediator mediator, IMapper mapper) : IRequestHandler<CalDateQuery, CalDateDto>
    {
        public async Task<CalDateDto> Handle(CalDateQuery request, CancellationToken cancellationToken)
        {
            var dto = request.DateCal;
            //根据任务获取
            if (request.ProcId is not null)
            {
                var dateCalDto = await freeSql.Select<CaseProcInfo>().WithLock().Where(it => it.ProcId == request.ProcId)
                    .FirstAsync(it => new DateCalDto(it.CtrlProcId, it.CaseInfo.CountryId, it.EntrustDate, it.LegalDueDate,
                        it.CaseInfo.ExtendInfo != null ? it.CaseInfo.ExtendInfo.ValidDate : null, it.ExtensionPeriod, it.ReceiveDate), cancellationToken);
                dto = dto is null ? dateCalDto : mapper.Map(dto, dateCalDto);
            }
            if (request.CaseId is not null)
            {
                var data = await freeSql.Select<CaseInfo>().WithLock().Where(it => it.Id == request.CaseId)
                    .FirstAsync(it => new { it.CountryId, it.ExtendInfo.ValidDate }, cancellationToken);
                dto.CountryId ??= data.CountryId;
                dto.ValidDate ??= data.ValidDate;
            }
            if (dto is null)
            {
                throw new ArgumentNullException(nameof(dto));
            }
            //根据开案信息获取
            if (dto.EntrustDate is null && request.ApplyId is not null)
            {
                dto.EntrustDate = await freeSql.Select<AppApplyInfo>().WithLock().Where(it => it.ApplyId == request.ApplyId)
                    .FirstAsync(it => it.EntrustDate, cancellationToken);
            }

            if (dto.CountryId is null && request.CaseId is not null)
            {
                dto.CountryId = await freeSql.Select<CaseInfo>().WithLock().Where(it => it.Id == request.CaseId)
                    .FirstAsync(it => it.CountryId, cancellationToken);
            }
            return request.Field switch
            {
                nameof(CaseProcInfo.DurationOutpost) => await mediator.Send(new DurationOutpostCalculationQuery(dto), cancellationToken),
                nameof(CaseProcInfo.DurationCustomer) => await mediator.Send(new DurationCustomerCalculationQuery(dto), cancellationToken),
                _ => throw new Exception("没有对应计算规则"),
            };
        }
    }
}

