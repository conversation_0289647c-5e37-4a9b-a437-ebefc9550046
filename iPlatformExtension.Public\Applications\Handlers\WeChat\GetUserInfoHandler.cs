﻿using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models.WeChat;
using iPlatformExtension.Public.Applications.Queries.WeChat;
using MediatR;
using System.Reflection.Metadata.Ecma335;

namespace iPlatformExtension.Public.Applications.Handlers.WeChat
{
    public class GetUserInfoHandler(IFreeSql freeSql, IWxWorkClient wxWorkClient) : IRequestHandler<GetUserInfoQuery, WechatUserInfoDto>
    {
        public async Task<WechatUserInfoDto> Handle(GetUserInfoQuery request, CancellationToken cancellationToken)
        {
            var wxRes = await wxWorkClient.GetUserInfoAsync(request.code);
            if (wxRes != null && wxRes.errcode == 0)
            {
                var userId = wxRes.userid;
                if (freeSql.Select<SysUserInfo>().Where(o => o.UserName == userId).Any())
                {
                    return new WechatUserInfoDto { UserName = userId };
                }
                throw new Exception("没有找到对应用户");
            }
            return null;
        }
    }
}
