﻿using iPlatformExtension.Finance.Applications.Commands.Commission;
using iPlatformExtension.Finance.Applications.Models.Commission;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Service.Interface;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Finance.Controllers;

/// <summary>
/// 付款控制器
/// </summary>
[ApiController]
[Route("[controller]")]
public sealed class IncomeController : ControllerBase
{
    private readonly IMediator _mediator;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="mediator">中介者</param>
    public IncomeController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// 添加划拨费项收入。
    /// 需要验证blade-auth  token
    /// </summary>
    /// <remarks>数据包括已划拨和撤销划拨的。撤销划拨的数据对应传数据金额</remarks>
    /// <param name="dto">划拨后的费项收入数据</param>
    [HttpPost]
    [Authorize]
    public Task CreateAsync(FeesIncomeDtoCollection dto)
    {
        return _mediator.Send(new SaveIncomesCommand(dto.IncomeInfos));
    }
}