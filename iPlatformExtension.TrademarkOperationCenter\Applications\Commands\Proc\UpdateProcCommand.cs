﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;

internal sealed record UpdateProcCommand(string ProcId, JsonPatchDocument<ProcTrademarkDeliveryDto> ProcPatchDocument, int Version) 
    : IFreeSqlUnitOfWorkCommand<PlatformFreeSql>, IRequest<CaseProcInfo>;