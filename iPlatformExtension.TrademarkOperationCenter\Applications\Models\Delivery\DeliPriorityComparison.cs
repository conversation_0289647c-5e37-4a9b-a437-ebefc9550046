﻿using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery {

	public partial class DeliPriorityComparison
    {

		/// <summary>
		/// 主键
		/// </summary>
		[Column(Name = "id", IsIdentity = true, IsPrimary = true)]
		public int Id { get; set; }
		
		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "das_code", StringLength = 50)]
		public string? DasCode { get; set; }

		[ Column(Name = "priority_date")]
		public DateTime? PriorityDate { get; set; }

		[ Column(Name = "priority_id", StringLength = 50)]
		public string PriorityId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "priority_no", StringLength = 50)]
		public string PriorityNo { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		/// <summary>
		/// 我方文号
		/// </summary>
		[Column(Name = "volume")]
		public string? Volume { get; set; }

	}

}
