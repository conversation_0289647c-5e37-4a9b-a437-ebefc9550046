﻿using FreeSql.DataAnnotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto.Flow
{
    [Serializable]
    public class FlowAnalyseDto
    {
        /// <summary>
        /// 统计类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 显示标题
        /// </summary>
        public string? Title { get; set; }
    }
}
