using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_proc_filter", DisableSyncStructure = true)]
	public partial class SysProcFilter {

		[ Column(Name = "filter_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FilterId { get; set; }

		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "case_property", StringLength = 500)]
		public string CaseProperty { get; set; }

		[ Column(Name = "ctrl_proc_property", StringLength = 50)]
		public string CtrlProcProperty { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "filter_name", StringLength = 200)]
		public string FilterName { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "list_id", StringLength = 50)]
		public string ListId { get; set; }

		[ Column(Name = "notice", StringLength = 500)]
		public string Notice { get; set; }

		[ Column(Name = "remark", StringLength = 400)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "stage", StringLength = 50)]
		public string Stage { get; set; }

	}

}
