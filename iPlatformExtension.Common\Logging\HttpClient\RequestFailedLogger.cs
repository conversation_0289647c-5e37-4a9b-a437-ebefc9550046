using iPlatformExtension.Common.Extensions;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.Logging.HttpClient;

public class RequestFailedLogger(ILogger<RequestFailedLogger> logger) : IHttpClientOnlyAsyncLogger
{
    public ValueTask<object?> LogRequestStartAsync(HttpRequestMessage request,
        CancellationToken cancellationToken = new CancellationToken())
    {
        return new ValueTask<object?>(null!);
    }

    public ValueTask LogRequestStopAsync(object? context, HttpRequestMessage request, HttpResponseMessage response,
        TimeSpan elapsed, CancellationToken cancellationToken = new CancellationToken())
    {
        return ValueTask.CompletedTask;
    }

    public ValueTask LogRequestFailedAsync(object? context, HttpRequestMessage request, HttpResponseMessage? response,
        Exception exception, TimeSpan elapsed, CancellationToken cancellationToken = new CancellationToken())
    {
        var uri = request.RequestUri;
        if (exception is HttpRequestException httpRequestException)
        {
            logger.LogHttpRequestException(uri, httpRequestException, httpRequestException.StatusCode);
        }
        else
        {
            logger.LogHttpRequestFailed(uri, exception);
        }
        
        return ValueTask.CompletedTask;
    }
}