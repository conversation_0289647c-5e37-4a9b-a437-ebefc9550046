﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Enumerable;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Repository.Interface;

public interface ICustomerBankRepository :
    IBaseRepository<CusBank, string>,
    IScopeDependency,
    IRedisCacheableRepository<string, IGrouping<string, CustomerBankAccountInfo>>
{
    async Task<IGrouping<string, CustomerBankAccountInfo>?> ICacheableRepository<string, IGrouping<string, CustomerBankAccountInfo>>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        var infos = await Where(bank => bank.AccountNumber == key).ToListAsync(bank => new CustomerBankAccountInfo()
        {
            BankId = bank.BankId,
            AccountNumber = bank.AccountNumber,
            BankName = bank.BankNameReal,
            BeneficiaryName = bank.BeneficiaryName
        }, cancellationToken);
        
        var groups = infos.GroupBy(info => info.AccountNumber);
        return groups.Any() ? groups.Single() : new EmptyGroup<string, CustomerBankAccountInfo>();
    }

    Task<IEnumerable<IGrouping<string, CustomerBankAccountInfo>>> ICacheableRepository<string, IGrouping<string, CustomerBankAccountInfo>>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return Task.FromResult(Enumerable.Empty<IGrouping<string, CustomerBankAccountInfo>>());
    }

    string ICacheableRepository<string, IGrouping<string, CustomerBankAccountInfo>>.GenerateKey(IGrouping<string, CustomerBankAccountInfo> value)
    {
        return value.Key;
    }
}