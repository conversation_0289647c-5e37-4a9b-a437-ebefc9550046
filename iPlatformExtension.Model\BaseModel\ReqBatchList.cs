using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "req_batch_list", DisableSyncStructure = true)]
	public partial class ReqBatchList {

		[ Column(Name = "batch_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string BatchId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "batch_status", StringLength = 50)]
		public string BatchStatus { get; set; }

		[ Column(Name = "batch_title", StringLength = 500, IsNullable = false)]
		public string BatchTitle { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "finish_task")]
		public bool FinishTask { get; set; } = true;

		[ Column(Name = "finish_time")]
		public DateTime? FinishTime { get; set; }

		[ Column(Name = "is_default")]
		public bool IsDefault { get; set; } = false;

		[ Column(Name = "is_dele")]
		public bool IsDele { get; set; } = true;

		[ Column(Name = "is_edit")]
		public bool IsEdit { get; set; } = true;

		[ Column(Name = "item_type", StringLength = 50)]
		public string ItemType { get; set; }

		[ Column(Name = "next_rule", StringLength = 50)]
		public string NextRule { get; set; }

		[ Column(Name = "next_status", StringLength = 50)]
		public string NextStatus { get; set; }

		[ Column(Name = "pay_officer_date")]
		public DateTime? PayOfficerDate { get; set; }

		[ Column(Name = "pct_enter")]
		public bool PctEnter { get; set; } = false;

		[ Column(Name = "remark", StringLength = 3000)]
		public string Remark { get; set; }

		[ Column(Name = "sumamount", DbType = "money")]
		public decimal? Sumamount { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50, IsNullable = false)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "xlsx_type", StringLength = 50)]
		public string XlsxType { get; set; }

	}

}
