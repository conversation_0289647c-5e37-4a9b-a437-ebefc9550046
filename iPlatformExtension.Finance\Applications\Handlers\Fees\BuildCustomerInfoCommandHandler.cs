﻿using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class BuildCustomerInfoCommandHandler : IRequestHandler<BuildCustomerInfoCommand>, IFeesQueryStatementBuilder
{
    private const string CusJoinListAlias = nameof(CusJoinList);
    
    public Task Handle(BuildCustomerInfoCommand request, CancellationToken cancellationToken)
    {
       BuildFeesQueryStatement(request.Dto, request.FeesQuery);
       return Task.CompletedTask;
    }

    public static void BuildFeesQueryStatement(FeeQueryDto dto, ISelect<CaseFeeList> feesQuery, params object?[] args)
    {
        if (dto.CustomerControlIdentifiers.Any())
        {
            feesQuery.InnerJoin<CusJoinList>((list, joinList) =>
                    list.CaseProcInfo.CaseInfo.CustomerId == joinList.FormObjId)
                .WhereDynamicFilter(
                    dto.CustomerControlIdentifiers.BuildContainsDynamicFilter(nameof(CusJoinList.JoinObjId),
                        CusJoinListAlias,
                        SystemDictionaryName.CustomerControlIdentifier.BuildEqualsDynamicFilter(
                            nameof(CusJoinList.JoinType), CusJoinListAlias)));
        }
    }
}