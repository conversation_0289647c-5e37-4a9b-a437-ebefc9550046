﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Repository.Interface;

public interface ISystemDictionaryRepository : 
    IBaseRepository<SysDictionary, string>, 
    IRedisCacheableRepository<SystemDictionaryValueKey, SysDictionary>,
    IRedisCacheableRepository<SystemDictionaryTextKey, SysDictionary>, 
    ICacheableRepository<string, IGrouping<string, SysDictionary>>,
    IScopeDependency
{
    private static readonly SystemDictionaryValueKeyEqualityComparer systemDictionaryValueKeyEqualityComparer = new();
    
    private static readonly SystemDictionaryTextKeyEqualityComparer systemDictionaryTextKeyEqualityComparer = new ();
    
    internal ILogger Logger { get; }
    
    async Task<SysDictionary?> ICacheableRepository<SystemDictionaryValueKey, SysDictionary>.GetValueFromDbAsync(SystemDictionaryValueKey key, CancellationToken cancellationToken)
    {
        var matchValues = await Select.WithLock()
            .Where(dictionary => dictionary.DictionaryName == key.Name && dictionary.Value == key.Value)
            .WhereIf(!string.IsNullOrWhiteSpace(key.Description), dictionary => dictionary.DictionaryDesc == key.Description)
            .ToListAsync(cancellationToken);
        
        Logger.LogDebug("字典匹配个数：{Count}", matchValues.Count);

        if (matchValues.Count == 0)
        {
            return null;
        }

        if (matchValues.Count != 1)
        {
            throw new InvalidDataException($"字典值重复。name: {key.Name}, value: {key.Value}");
        }

        return matchValues.First();
    }

    async Task<IEnumerable<SysDictionary>> ICacheableRepository<SystemDictionaryValueKey, SysDictionary>.
        GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().ToListAsync(cancellationToken);
    }

    SystemDictionaryValueKey ICacheableRepository<SystemDictionaryValueKey, SysDictionary>.GenerateKey(SysDictionary value)
    {
        ArgumentNullException.ThrowIfNull(value);
        return new SystemDictionaryValueKey(value.DictionaryName, value.Value);
    }
    
    IEqualityComparer<SystemDictionaryValueKey> ICacheableRepository<SystemDictionaryValueKey, SysDictionary>.
        KeyEqualityComparer => systemDictionaryValueKeyEqualityComparer;

    IEqualityComparer<SystemDictionaryTextKey>? ICacheableRepository<SystemDictionaryTextKey, SysDictionary>.KeyEqualityComparer => systemDictionaryTextKeyEqualityComparer;

    async Task<SysDictionary?> ICacheableRepository<SystemDictionaryTextKey, SysDictionary>.GetValueFromDbAsync(SystemDictionaryTextKey key, CancellationToken cancellationToken)
    {
        var matchValues = await Select.WithLock()
            .Where(dictionary => dictionary.DictionaryName == key.Name && dictionary.TextZhCn == key.Text)
            .WhereIf(!string.IsNullOrWhiteSpace(key.Description), dictionary => dictionary.DictionaryDesc == key.Description)
            .ToListAsync(cancellationToken);
        
        Logger.LogDebug("字典匹配个数：{Count}", matchValues.Count);

        if (matchValues.Count == 0)
        {
            return null;
        }

        if (matchValues.Count != 1)
        {
            throw new InvalidDataException($"字典值重复。name: {key.Name}, text: {key.Text}");
        }

        return matchValues.First();
    }

    async Task<IEnumerable<SysDictionary>> ICacheableRepository<SystemDictionaryTextKey, SysDictionary>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().ToListAsync(cancellationToken);
    }

    SystemDictionaryTextKey ICacheableRepository<SystemDictionaryTextKey, SysDictionary>.GenerateKey(SysDictionary value)
    {
        return new SystemDictionaryTextKey(value.DictionaryName, value.TextZhCn);
    }

    async Task<IGrouping<string, SysDictionary>?> ICacheableRepository<string, IGrouping<string, SysDictionary>>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        var values = await Select.WithLock().Where(dictionary => dictionary.DictionaryName == key).ToListAsync(cancellationToken);
        return values.Count != 0 ? values.GroupBy(dictionary => dictionary.DictionaryName).First() : null;
    }

    Task<IEnumerable<IGrouping<string, SysDictionary>>> ICacheableRepository<string, IGrouping<string, SysDictionary>>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return Task.FromResult(Enumerable.Empty<IGrouping<string, SysDictionary>>());
    }

    string ICacheableRepository<string, IGrouping<string, SysDictionary>>.GenerateKey(IGrouping<string, SysDictionary> value)
    {
        return value.Key;
    }
}