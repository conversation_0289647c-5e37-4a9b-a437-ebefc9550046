using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_not_request", DisableSyncStructure = true)]
	public partial class RpNotRequest {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "case_type", StringLength = 50)]
		public string CaseType { get; set; }

		[ Column(Name = "date")]
		public DateTime? Date { get; set; }

		[ Column(Name = "fee_name", StringLength = 50)]
		public string FeeName { get; set; }

		[ Column(Name = "month", StringLength = 5)]
		public string Month { get; set; }

		[ Column(Name = "quarter", StringLength = 5)]
		public string Quarter { get; set; }

		[ Column(Name = "year", StringLength = 5)]
		public string Year { get; set; }

	}

}
