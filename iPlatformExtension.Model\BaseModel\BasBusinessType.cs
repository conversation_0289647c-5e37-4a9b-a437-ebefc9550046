using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_business_type", DisableSyncStructure = true)]
	public partial class BasBusinessType {

		/// <summary>
		/// 业务类型ID
		/// </summary>
		[ Column(Name = "business_type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string BusinessTypeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 开案类别
		/// </summary>
		[ Column(Name = "aspx_code", StringLength = 50)]
		public string AspxCode { get; set; }

		[ Column(Name = "business_code", StringLength = 50)]
		public string BusinessCode { get; set; }

		[ Column(Name = "business_code_tmo", StringLength = 50)]
		public string BusinessCodeTmo { get; set; }

		/// <summary>
		/// 业务类型简称
		/// </summary>
		[ Column(Name = "business_type_code", StringLength = 50)]
		public string BusinessTypeCode { get; set; }

		/// <summary>
		/// 业务类型英文
		/// </summary>
		[ Column(Name = "business_type_en_us", StringLength = 50)]
		public string BusinessTypeEnUs { get; set; }

		/// <summary>
		/// 业务类型日语
		/// </summary>
		[ Column(Name = "business_type_ja_jp", StringLength = 50)]
		public string BusinessTypeJaJp { get; set; }

		/// <summary>
		/// 业务类型中文名称
		/// </summary>
		[ Column(Name = "business_type_zh_cn", StringLength = 50)]
		public string BusinessTypeZhCn { get; set; }

		/// <summary>
		/// 案件类型
		/// </summary>
		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户ID
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 是否默认
		/// </summary>
		[ Column(Name = "is_default")]
		public bool? IsDefault { get; set; } = false;

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户ID 
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
