﻿using System.Text;
using FreeSql;
using FreeSql.Internal;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Common.Mediator.Notifications;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Common.Mediator.PipelineBehaviors
{
    public interface
        IMysqlIUnitOfWorkPipelineBehavior<in TRequest, TResult> : IPipelineBehavior<TRequest, TResult?>
        where TRequest : IUnitOfWorkCommandMysql
    {
        internal UnitOfWorkManager UnitOfWorkManager { get; }

        internal ILogger Logger { get; }

        internal ObjectPool<StringBuilder> StringBuilderPool { get; }

        internal IMediator Mediator { get; }

        async Task<TResult?> IPipelineBehavior<TRequest, TResult?>.Handle(TRequest request,
            RequestHandlerDelegate<TResult?> next, CancellationToken cancellationToken)
        {
            TResult? result = default;
            var unitOfWork = UnitOfWorkManager.Begin(request.TransactionPropagation, request.IsolationLevel);
            var commited = false;

            try
            {
                var unitOfWorkType = unitOfWork.GetType().Name;
                List<DbContext.EntityChangeReport.ChangeInfo> entityChangeReports = [];

                result = await next();

                if (unitOfWorkType == "UnitOfWorkOrginal")
                {
                    entityChangeReports.AddRange(unitOfWork.EntityChangeReport.Report);
                }

                unitOfWork.Commit();

                if (entityChangeReports.Count > 0)
                {
                    commited = true;
                    await Mediator.Publish(new EntityChangeNotification(entityChangeReports), cancellationToken);
                }
            }
            catch (Exception exception)
            {
                if (commited)
                {
                    Logger.LogPublishEntitiesChangeEventFailed(exception);
                }
                else
                {
                    unitOfWork.Rollback();
                    Logger.LogTransactionRollback(exception);

                    if (exception is DbUpdateVersionException dbUpdateVersionException)
                    {
                        var stringBuilder = StringBuilderPool.Get();
                        stringBuilder.AppendLine(dbUpdateVersionException.Sql);
                        dbUpdateVersionException.DbParams?.Aggregate(stringBuilder,
                            (builder, parameter) => builder.Append(parameter.ParameterName).Append('=')
                                .Append(parameter.Value)
                                .AppendLine());

                        var sql = stringBuilder.ToString();
                        StringBuilderPool.Return(stringBuilder);

                        Logger.LogDbUpdateInvalidVersion(sql, dbUpdateVersionException);

                        throw new ApplicationException("数据版本号不一致！请刷新页面后再操作", dbUpdateVersionException);
                    }
                    else
                    {
                        throw;
                    }
                }
            }
            finally
            {
                unitOfWork.Dispose();
            }

            return result;
        }
    }
}