using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mon_receive_bill", DisableSyncStructure = true)]
	public partial class MonReceiveBill {

		[ Column(Name = "rb_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string RbId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "allot_id", StringLength = 50)]
		public string AllotId { get; set; }

		[ Column(Name = "bill_id", StringLength = 50)]
		public string BillId { get; set; }

		[ Column(Name = "bill_no", StringLength = 50)]
		public string BillNo { get; set; }

		[ Column(Name = "case_type", StringLength = 10)]
		public string CaseType { get; set; }

		[ Column(Name = "input_time")]
		public DateTime? InputTime { get; set; }

		[ Column(Name = "remittance_charges", DbType = "money")]
		public decimal? RemittanceCharges { get; set; }

	}

}
