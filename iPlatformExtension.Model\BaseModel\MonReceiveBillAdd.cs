using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mon_receive_bill_add", DisableSyncStructure = true)]
	public partial class MonReceiveBillAdd {

		[ Column(Name = "rba_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string RbaId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "a_fee", DbType = "money")]
		public decimal? AFee { get; set; }

		[ Column(Name = "allot_id", StringLength = 50)]
		public string AllotId { get; set; }

		[ Column(Name = "bill_no", StringLength = 50)]
		public string BillNo { get; set; }

		[ Column(Name = "case_no", StringLength = 50)]
		public string CaseNo { get; set; }

		[ Column(Name = "fee_name", StringLength = 50)]
		public string FeeName { get; set; }

		[ Column(Name = "fee_type", StringLength = 200)]
		public string FeeType { get; set; }

		[ Column(Name = "o_fee", DbType = "money")]
		public decimal? OFee { get; set; }

	}

}
