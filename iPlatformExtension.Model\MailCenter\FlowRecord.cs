﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "flow_record", DisableSyncStructure = true)]
	public partial class FlowRecord {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 审核备注
		/// </summary>
		[Column(Name = "audit_remark")]
		public string AuditRemark { get; set; }

		/// <summary>
		/// 审核时间
		/// </summary>
		[Column(Name = "audit_time", DbType = "datetime")]
		public DateTime? AuditTime { get; set; }

		/// <summary>
		/// 提交类型:submit,reject,Transfer
		/// </summary>
		[Column(Name = "audit_type", StringLength = 50)]
		public string AuditType { get; set; }

		/// <summary>
		/// 审核用户id
		/// </summary>
		[Column(Name = "audit_user", StringLength = 50)]
		public string AuditUser { get; set; }

		/// <summary>
		/// 当前节点id
		/// </summary>
		[Column(Name = "cur_node_id", StringLength = 50)]
		public string CurNodeId { get; set; }

		/// <summary>
		/// 流程配置id
		/// </summary>
		[Column(Name = "flow_id", StringLength = 50)]
		public string FlowId { get; set; }

		/// <summary>
		/// 是否当前流程
		/// </summary>
		[Column(Name = "is_current", DbType = "int")]
		public int? IsCurrent { get; set; }

		/// <summary>
		/// 邮件id
		/// </summary>
		[Column(Name = "mail_id", StringLength = 50)]
		public string MailId { get; set; }

		/// <summary>
		/// 上一处理记录
		/// </summary>
		[Column(Name = "pre_record_id", StringLength = 50)]
		public string PreRecordId { get; set; }

		/// <summary>
		/// 状态
		/// </summary>
		[Column(Name = "status", StringLength = 50)]
		public string Status { get; set; }

		/// <summary>
		/// 版本号
		/// </summary>
		[Column(Name = "version", StringLength = 50)]
		public string Version { get; set; }

	}

}
