using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_file_desc_column", DisableSyncStructure = true)]
	public partial class SysFileDescColumn {

		[ Column(Name = "allow_null")]
		public bool? AllowNull { get; set; } = true;

		[ Column(Name = "config_id", StringLength = 50)]
		public string ConfigId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "unit_id", StringLength = 50)]
		public string UnitId { get; set; }

		[ Column(Name = "value", StringLength = 50)]
		public string Value { get; set; }

	}

}
