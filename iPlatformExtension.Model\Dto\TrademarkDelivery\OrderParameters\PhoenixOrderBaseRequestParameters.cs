﻿using System.Text.Json.Serialization;
using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters
{
    /// <summary>
    /// 递交作业请求基本信息
    /// </summary>
    public class PhoenixOrderBaseRequestParameters : PhoenixOrderRequestParameters
    {
        /// <summary>
        /// 机构联系人
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("agentOrganConName")]
        public string? AgentOrganConName { get; set; }

        /// <summary>
        /// 代理机构电话
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("agentOrganTel")]
        public string? AgentOrganTel { get; set; }

        /// <summary>
        /// 交官材料
        /// 不做参数序列化
        /// </summary>
        public IEnumerable<ApplicantAttachment> AttachmentsList { get; set; } = Array.Empty<ApplicantAttachment>();

        /// <summary>
        /// 交官材料
        /// </summary>
        [JsonSerializationSource(nameof(AttachmentsList))]
        //[JsonPropertyName("attachments")]
        //[JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? Attachments { get; private set; }

        /// <summary>
        /// 商标信息
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public IEnumerable<BrandTransferParam> BrandTransferParamList { get; set; } = Array.Empty<BrandTransferParam>();

        /// <summary>
        /// 商标信息
        /// 不做参数序列化
        /// </summary>
        [JsonSerializationSource(nameof(BrandTransferParamList))]
        [JsonPropertyName("brandTransferParam")]
        public string? BrandTransferParam { get; private set; }
    }
}
