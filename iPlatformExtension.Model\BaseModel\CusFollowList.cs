using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_follow_list", DisableSyncStructure = true)]
	public partial class CusFollowList {

		/// <summary>
		/// 客户跟随主键ID
		/// </summary>
		[ Column(Name = "cur_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CurId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 案件流向
		/// </summary>
		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		/// <summary>
		/// 案件类型
		/// </summary>
		[ Column(Name = "case_type", StringLength = 50)]
		public string CaseType { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 客户ID(外键)
		/// </summary>
		[Column(Name = "customer_id", StringLength = 50, IsNullable = false)]
		public string CustomerId { get; set; } = null!;

		/// <summary>
		/// 客户信息
		/// </summary>
		[Navigate(nameof(CustomerId))]
		public virtual CusCustomer? Customer { get; set; }

		/// <summary>
		/// 用户类型
		/// </summary>
		[ Column(Name = "customer_user_type", StringLength = 50, IsNullable = false)]
		public string CustomerUserType { get; set; } = "";

		/// <summary>
		/// 待确认(无数据)
		/// </summary>
		[Column(Name = "head_user_type", StringLength = 50)]
		public string? HeadUserType { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 跟案人
		/// </summary>
		[ Column(Name = "track_user", StringLength = 50)]
		public string? TrackUser { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
