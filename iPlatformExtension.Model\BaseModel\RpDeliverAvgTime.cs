using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_deliver_avg_time", DisableSyncStructure = true)]
	public partial class RpDeliverAvgTime {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "customer_name", StringLength = 150)]
		public string CustomerName { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "fist_draft_days")]
		public int? FistDraftDays { get; set; }

		[ Column(Name = "month", StringLength = 50)]
		public string Month { get; set; }

		[ Column(Name = "number")]
		public int? Number { get; set; }

		[ Column(Name = "over_days")]
		public int? OverDays { get; set; }

		[ Column(Name = "quarter", StringLength = 50)]
		public string Quarter { get; set; }

		[ Column(Name = "sure_draft_days")]
		public int? SureDraftDays { get; set; }

		[ Column(Name = "tech_classify", StringLength = 50)]
		public string TechClassify { get; set; }

		[ Column(Name = "undertake_user", StringLength = 150)]
		public string UndertakeUser { get; set; }

		[ Column(Name = "year", StringLength = 50)]
		public string Year { get; set; }

	}

}
