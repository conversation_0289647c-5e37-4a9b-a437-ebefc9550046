﻿using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow
{
    public class FlowHandOverByBatchCommand : IRequest<List<FlowSubmitByBatchDto>>
    {
        /// <summary>
        /// 流程任务ID
        /// </summary>
        public string[] ObjId { get; set; }

        /// <summary>
        ///  备注
        /// </summary>
        public string FRemark { get; set; }

        /// <summary>
        /// 审核人ID
        /// </summary>
        public string FAuditUserID { get; set; }


        /// <summary>
        /// 流程类型,
        /// 递交:DE
        /// 配案:AT
        /// 核稿:EX
        /// </summary>
        [Required]
        public string FFlowType { get; set; }


    }

}
