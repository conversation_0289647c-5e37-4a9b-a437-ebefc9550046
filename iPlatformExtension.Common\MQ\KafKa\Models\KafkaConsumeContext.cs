﻿using System.Security.Claims;
using System.Text;
using Confluent.Kafka;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.Models;
using iPlatformExtension.Model.BaseModel;
using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.MQ.KafKa.Models;

internal sealed class KafkaConsumeContext<TKey, TValue> : ConsumeContext
{
    private readonly ConsumeResult<TKey?, TValue> _consumeResult;

    public KafkaConsumeContext(ConsumeResult<TKey?, TValue> consumeResult, IServiceProvider serviceProvider) : base(serviceProvider)
    {
        _consumeResult = consumeResult;
        
        _items.Add("topic", consumeResult.Topic);
        _items.Add("messageKey", consumeResult.Message.Key);
        _items.Add("messageValue", consumeResult.Message.Value);
        _items.Add("partition", consumeResult.Partition);
        _items.Add("offset", consumeResult.Offset);
        _items.Add("headers", consumeResult.Message.Headers);
    }

    public override string Topic => _consumeResult.Topic;

    public override T? GetMessageValue<T>() where T : default
    {
        var messageValue = _consumeResult.Message.Value;
        return messageValue is T value ? value : default;
    }

    public TKey? MessageKey => _consumeResult.Message.Key;

    public Headers Headers => _consumeResult.Message.Headers;

    internal async Task SetUserAsync()
    {
        var headers = Headers;
        var userId = headers.TryGetLastBytes("userId", out var bytes) ? bytes.GetString(Encoding.UTF8) : default;

        if (!string.IsNullOrWhiteSpace(userId))
        {
            var userInfoRepository = Services.GetRequiredService<ICacheableRepository<string, SysUserInfo>>();
            var userInfo = await userInfoRepository.GetCacheValueAsync(userId);

            if (userInfo is not null)
            {
                var identity = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, userInfo.UserId),
                    new Claim(ClaimTypes.Name, userInfo.UserName),
                    new Claim(ClaimTypes.GivenName, userInfo.CnName),
                    new Claim(ClaimTypes.GroupSid, userInfo.DeptId)
                });
                identity.AddClaims(userInfo.Roles!.Select(roleInfo => new Claim(ClaimTypes.Role, roleInfo.RoleName)));
                
                User.AddIdentity(identity);
            }
        }
    }
}