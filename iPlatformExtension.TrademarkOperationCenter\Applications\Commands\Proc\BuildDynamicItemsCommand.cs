﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;

internal sealed record BuildDynamicItemsCommand(
    object Entity, 
    IEnumerable<ProcPropertiesDynamicConfig> Configuration, 
    bool UserReadonly, 
    bool IsRoot = false) : 
    IRequest<IReadOnlyDictionary<ProcExtensionKey, ProcExtensionItem>>;