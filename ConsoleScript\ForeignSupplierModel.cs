﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace ConsoleScript
{
    public class Supplier
    {
        [Required]
        public Customer Customer { get; set; } = new Customer();

        [Required]
        public ContactList[] ContactList { get; set; } = [];

        [Required]
        public BankList[] BankList { get; set; } = [];

        [Required]
        public RequestList[] RequestList { get; set; } = [];
    }

    public class Customer
    {
        [Required]
        public string CustomerId { get; set; } = string.Empty;

        [Required]
        public string CustomerCode { get; set; } = string.Empty;

        [Required]
        public string CustomerNameEn { get; set; } = string.Empty;

        /// <summary>
        /// 业务系统显示的外所名称
        /// </summary>
        [Required]
        public string CustomerName { get; set; } = string.Empty;

        [Required]
        public string CountryId { get; set; } = string.Empty;

        [Required]
        public string CountryName { get; set; } = string.Empty;

        public bool IsEnabled { get; set; }

        [Required]
        public string UpdateUserCode { get; set; } = string.Empty;

        [Required]
        public string UpdateUserName { get; set; } = string.Empty;

        [Required]
        public DateTime UpdateTime { get; set; }

        [Required]
        public DateTime CreateTime { get; set; }

        [Required]
        public string CreateUserCode { get; set; } = string.Empty;

        [Required]
        public string CreateUserName { get; set; } = string.Empty;

        [Url]
        public string Website { get; set; } = string.Empty;

        /// <summary>
        /// 企业邮箱
        /// </summary>
        [EmailAddress]
        public string EnterpriseEmail { get; set; } = string.Empty;
    }

    public class ContactList
    {
        [Required]
        public string ContactId { get; set; } = string.Empty;

        [Required]
        public string ContactName { get; set; } = string.Empty;

        [Required]
        public string CallName { get; set; } = string.Empty;

        [Required]
        public List<SupplierContactType> ContactTypeList { get; set; } = [];

        // [Required]
        // public string[] ContactAreaList { get; set; } = [];

        [Required]
        public string Position { get; set; } = string.Empty;

        [Required]
        public List<SupplierContactMail> ContactMailList { get; set; } = [];

        [Required]
        public SupplierContactPhone[] ContactPhoneList { get; set; } = [];

        [Required]
        public SupplierContactAddress[] ContactAddressList { get; set; } = [];

        [Required]
        public string UpdateUserCode { get; set; } = string.Empty;

        [Required]
        public string UpdateUserName { get; set; } = string.Empty;

        [Required]
        public DateTime UpdateTime { get; set; }

        [Required]
        public DateTime CreateTime { get; set; }

        [Required]
        public string CreateUserCode { get; set; } = string.Empty;

        [Required]
        public string CreateUserName { get; set; } = string.Empty;

        [Required]
        public string Remark { get; set; } = string.Empty;

        public bool IsEnabled { get; set; }

        // public bool IsDefault { get; set; }

        [Required]
        public List<string> UseTypeList { get; set; } = [];
    }

    public class BankList
    {
        [Required]
        public string BankId { get; set; } = string.Empty;

        [Required]
        public string CountryId { get; set; } = string.Empty;

        [Required]
        public string CountryName { get; set; } = string.Empty;

        [Required]
        public string AccountNumber { get; set; } = string.Empty;

        [Required]
        public string ReceiverAccountName { get; set; } = string.Empty;

        [Required]
        public string IbanCode { get; set; } = string.Empty;

        [Required]
        public string BeneficiaryAddressReal { get; set; } = string.Empty;

        [Required]
        public string TransferBankName { get; set; } = string.Empty;

        [Required]
        public string BankSwiftCode { get; set; } = string.Empty;

        /// <summary>
        /// 收款银行地址
        /// </summary>
        [Required]
        public string ReceiveBankAddress { get; set; } = string.Empty;

        /// <summary>
        /// 收款银行名称
        /// </summary>
        public string DepositBankName { get; set; } = string.Empty;

        [Required]
        public string TransferBankAddress { get; set; } = string.Empty;

        [Required]
        public DateTime CreateTime { get; set; }

        [Required]
        public string CreateUserCode { get; set; } = string.Empty;

        [Required]
        public string CreateUserName { get; set; } = string.Empty;

        [Required]
        public string UpdateUserCode { get; set; } = string.Empty;

        [Required]
        public string UpdateUserName { get; set; } = string.Empty;

        [Required]
        public DateTime UpdateTime { get; set; }

        [Required]
        public string Remark { get; set; } = string.Empty;

        public bool IsEnabled { get; set; }
    }

    public class RequestList
    {
        [Required]
        public string RequestId { get; set; } = string.Empty;

        [Required]
        public string Content { get; set; } = string.Empty;

        [Required]
        public string Title { get; set; } = string.Empty;

        [Required]
        public DateTime CreateTime { get; set; }

        [Required]
        public string CreateUserCode { get; set; } = string.Empty;

        [Required]
        public string CreateUserName { get; set; } = string.Empty;

        [Required]
        public string UpdateUserCode { get; set; } = string.Empty;

        [Required]
        public string UpdateUserName { get; set; } = string.Empty;

        [Required]
        public DateTime UpdateTime { get; set; }

        public bool IsEnabled { get; set; }

        [Required]
        public List<string> CaseTypeList { get; set; } = [];

        [Required]
        public RequestFileList[] RequestFileList { get; set; } = [];
    }

    public class RequestFileList
    {
        [Required]
        public string RequestFileId { get; set; } = string.Empty;

        [Required]
        public string FileName { get; set; } = string.Empty;

        [Required]
        public string FileDescribe { get; set; } = string.Empty;

        [Required]
        public DateTime UploadTime { get; set; }

        [Required]
        public string UploadUserCode { get; set; } = string.Empty;

        [Required]
        public string UploadUserName { get; set; } = string.Empty;

        [Required]
        public string FileUrl { get; set; } = string.Empty;
    }

    public class SupplierContactType
    {
        
        /// <summary>
        /// 联系人类型中文
        /// </summary>
        public string SupplierContactTypeCn { get; set; } = string.Empty;

        /// <summary>
        /// 联系人类型编码
        /// </summary>
        public string SupplierContactTypeCode { get; set; } = string.Empty;
    }

    public class SupplierContactMail
    {
        /// <summary>
        /// 邮箱类型中文名
        /// </summary>
        public string SupplierContactMailTypeCn { get; set; } = string.Empty;

        /// <summary>
        /// 
        /// </summary>
        [JsonPropertyName("supplierContactMail")]
        public string ContactMail { get; set; } = string.Empty;
    }

    public class SupplierContactPhone
    {
        /// <summary>
        /// 
        /// </summary>
        public string PhoneAreaCode { get; set; }=string.Empty;

        /// <summary>
        /// 
        /// </summary>
        public string ContactPhone { get; set; }=string.Empty;
    }

    public class SupplierContactAddress
    {
        /// <summary>
        /// 联系人地址
        /// </summary>
        [JsonPropertyName("supplierContactAddress")]
        public string ContactAddress { get; set; } = string.Empty;
    }
}
