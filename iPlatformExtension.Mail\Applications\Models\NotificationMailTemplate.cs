﻿namespace iPlatformExtension.Mail.Applications.Models;

internal sealed class NotificationMailTemplate
{
    /// <summary>
    /// 邮件主题
    /// </summary>
    public string Subject { get; internal set; } = string.Empty;

    /// <summary>
    /// 邮件正文
    /// </summary>
    public string Body { get; internal set; } = string.Empty;

    /// <summary>
    /// 邮件正文是否h5
    /// </summary>
    public bool IsHtmlBody { get; internal set; }

    /// <summary>
    /// 是否需要填充收件人
    /// </summary>
    public bool NeedToFormatReceiver { get; internal set; }
}