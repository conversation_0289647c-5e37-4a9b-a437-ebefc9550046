﻿using System.ComponentModel;

namespace iPlatformExtension.Outsourcing.Application.Models.Proc;
/// <summary>
/// 待分配外所计数DTO
/// </summary>
public class NotAssignedCountDto
{
    /// <summary>
    /// 个人待分配外所任务计数
    /// </summary>
    [Description("个人待分配外所任务计数")]
    public long? PersonalCount { get; set; }

    /// <summary>
    /// 全部待分配外所任务计数
    /// </summary>
    [Description("全部待分配外所任务计数")]
    public long? WholeCount { get; set; }
}