﻿using FreeSql;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Copy;
using MediatR;
using System.Reflection;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Copy
{
    /// <summary>
    /// 1对1复制命令
    /// </summary>
    internal sealed class CopyCommandHandler : IRequestHandler<CopyCommand>
    {
        private readonly IFreeSql _freeSql;
        private readonly UnitOfWorkManager _unitOfWorkManager;
        private readonly EntityTypeInfoProvider _entityTypeInfoProvider;

        public CopyCommandHandler(IFreeSql freeSql, UnitOfWorkManager unitOfWorkManager, EntityTypeInfoProvider entityTypeInfoProvider)
        {
            _freeSql = freeSql;
            _unitOfWorkManager = unitOfWorkManager;
            _entityTypeInfoProvider = entityTypeInfoProvider;
        }

        public async Task Handle(CopyCommand request, CancellationToken cancellationToken)
        {
            var assembly = typeof(CaseInfo).Assembly;
            var type = Type.GetType($"iPlatformExtension.Model.BaseModel.{request.Table},{assembly}");
            if (type is null) throw new ArgumentNullException($"{nameof(type)}获取类型失败");
            var baseRepository = _freeSql.GetRepository<object>();
            baseRepository.AsType(type);
            baseRepository.UnitOfWork = _unitOfWorkManager.Current;
            var source = await baseRepository.Select.WhereDynamic(request.SourceId).ToOneAsync(cancellationToken);
            var targetList = await baseRepository.Select.WhereDynamic(request.TargetId).ToListAsync(cancellationToken);
            var entityTypeInfo = _entityTypeInfoProvider.Get(type);
            if (targetList.Count == 0)
            {
                throw new InvalidDataException("复制主键id有误");
            }

            foreach (var target in targetList)
            {
                foreach (var field in request.CopyFieldList)
                {
                    foreach (var propertyInfo in entityTypeInfo.EntityPropertyInfos)
                    {
                        if (propertyInfo.PropertyName == field.Item1)
                        {
                            var value = propertyInfo?.Get(source);
                            propertyInfo.Set(target, value);
                        }

                    }
                }
            }

            foreach (var target in targetList)
            {
                await baseRepository.UpdateAsync(target, cancellationToken);
            }
   
        }
    }
}

