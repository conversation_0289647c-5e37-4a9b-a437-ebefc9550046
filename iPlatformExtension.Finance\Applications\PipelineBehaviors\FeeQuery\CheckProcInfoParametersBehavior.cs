﻿using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Applications.PipelineBehaviors.FeeQuery;

internal sealed class CheckProcInfoParametersBehavior : CheckFeesQueryParametersBehaviorBase<BuildProcInfoCommand>
{
    public CheckProcInfoParametersBehavior(ILoggerFactory loggerFactory) : base(loggerFactory)
    {
    }

    public override bool Check(FeeQueryDto dto)
    {
        var needToQueryProcInfo = false;
        if (dto.CtrlProcIds.Any())
        {
            needToQueryProcInfo = true;
        }
        else if (dto.UndertakerIds.Any())
        {
            needToQueryProcInfo = true;
        }
        else if (dto.MainUndertakerIds.Any() && (!dto.CaseTypes.Any() || dto.CaseTypes.Any(caseType => !CaseType.IsBelongPatent(caseType))))
        {
            needToQueryProcInfo = true;
        }
        else if (dto.CompletionTimePeriod.NeedToQuery)
        {
            needToQueryProcInfo = true;
        }
        else if (dto.EntrustTimePeriod.NeedToQuery && (!dto.CaseTypes.Any() || dto.CaseTypes.Any(caseType => !CaseType.IsBelongPatent(caseType))))
        {
            needToQueryProcInfo = true;
        }
        else if (dto.SendOfficialDatePeriod.NeedToQuery)
        {
            needToQueryProcInfo = true;
        }
        else if (dto.CaseOnly)
        {
            needToQueryProcInfo = true;
        }
        else if (dto.AllocateDateRange.NeedToQuery)
        {
            needToQueryProcInfo = true;
        }
        else if (dto.ProcNos.Any())
        {
            needToQueryProcInfo = true;
        }
        else if (dto.TrademarkDeliveryAgencyIds.Any())
        {
            needToQueryProcInfo = true;
        }

        return needToQueryProcInfo;
    }
}