﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Copy;

/// <summary>
/// 添加复制项
/// </summary>
/// <param name="Name">名称</param>
/// <param name="FatherId">父级id</param>
/// <param name="Type">类型</param>
/// <param name="Table">表</param>
/// <param name="Property">属性</param>
/// <param name="KeyType">属性</param>
public record AddCopyItemCommand(string Name, string? FatherId, string Type, string Table, string? Property,string? KeyType) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

