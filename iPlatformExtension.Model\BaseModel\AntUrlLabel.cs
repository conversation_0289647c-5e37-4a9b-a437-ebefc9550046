using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ant_url_label", DisableSyncStructure = true)]
	public partial class AntUrlLabel {

		[ Column(Name = "label_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string LabelId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_private")]
		public bool IsPrivate { get; set; } = false;

		[ Column(Name = "label_name", StringLength = 500, IsNullable = false)]
		public string LabelName { get; set; }

		[ Column(Name = "label_user", StringLength = 50)]
		public string LabelUser { get; set; }

	}

}
