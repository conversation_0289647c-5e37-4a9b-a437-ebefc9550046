﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

internal sealed class DefaultPolicy(IOptions<SendPolicyOptions<DefaultDeliveryCommandHandler>> options)
    : SendPolicy<DefaultDeliveryCommandHandler>(options);