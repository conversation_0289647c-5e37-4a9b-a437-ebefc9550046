﻿using iPlatformExtension.Commission.Grpc;
using iPlatformExtension.Messages;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Commission.Infrastructure.Logging;

internal static partial class LoggingExtension
{
    [LoggerMessage(LogLevel.Error, "固化国内商标权值提成错误！")]
    public static partial void LogCreatingDomesticTrademarkWeightError(this ILogger logger, Exception exception);
    
    [LoggerMessage(LogLevel.Error, "推送国内商标权值数据错误！")]
    public static partial void LogPushingDomesticTrademarkWeightError(this ILogger logger, Exception exception);
    
    [LoggerMessage(LogLevel.Error, "推送国内商标权值结果处理错误！")]
    public static partial void LogPushedDomesticTrademarkWeightError(this ILogger logger, Exception exception);
    
    [LoggerMessage(LogLevel.Error, "推送国内商标权值[{ProcId}]失败！-> 失败原因：{Message}")]
    public static partial void LogDomesticTrademarkWeightPushedFail(this ILogger logger, string procId, string message);
    
    [LoggerMessage(LogLevel.Information, "推送国内商标权值[{ProcId}]成功！")]
    public static partial void LogDomesticTrademarkWeightPushedSuccess(this ILogger logger, string procId);
    
    [LoggerMessage(LogLevel.Error, "固化出口商标权值提成错误！")]
    public static partial void LogCreatingForeignTrademarkWeightError(this ILogger logger, Exception exception);
    
    [LoggerMessage(LogLevel.Error, "推送出口商标权值数据错误！")]
    public static partial void LogPushingForeignTrademarkWeightError(this ILogger logger, Exception exception);
    
    [LoggerMessage(LogLevel.Error, "推送出口商标权值[{ProcId}]失败！-> 失败原因：{Message}")]
    public static partial void LogForeignTrademarkWeightPushedFail(this ILogger logger, string procId, string message);
    
    [LoggerMessage(LogLevel.Information, "推送出口商标权值[{ProcId}]成功！")]
    public static partial void LogForeignTrademarkWeightPushedSuccess(this ILogger logger, string procId);
    
    [LoggerMessage(LogLevel.Error, "出口商标权值数据[{ProcId}]插入失败!")]
    public static partial void LogInsertForeignTrademarkWeightDataError(this ILogger logger, string procId, Exception exception);
    
    [LoggerMessage(LogLevel.Error, "国内商标权值数据[{ProcId}]插入失败!")]
    public static partial void LogInsertDomesticTrademarkWeightDataError(this ILogger logger, string procId, Exception exception);
    
    [LoggerMessage(LogLevel.Error, "固化国内商标胜诉奖励提成错误！")]
    public static partial void LogCreatingDomesticRewardsError(this ILogger logger, Exception exception);
    
    [LoggerMessage(LogLevel.Warning, "任务[{ProcId}]匹配国内商标胜诉奖励规则数量为{Count}, 不为1！不作数据固化")]
    public static partial void LogInvalidMatchedDomesticRewardRuleCount(this ILogger logger, string procId, int count);
    
    [LoggerMessage(LogLevel.Warning, "任务[{ProcId}]下承办人（或主承办人）为空！不作数据固化！")]
    public static partial void LogUndertakerIsNull(this ILogger logger, string procId);
    
    [LoggerMessage(LogLevel.Error, "国内商标胜诉奖励数据[{ProcId}]插入失败!")]
    public static partial void LogInsertDomesticRewardDataError(this ILogger logger, string procId, Exception exception);
    
    [LoggerMessage(LogLevel.Warning, "推送国内商标胜诉奖励[{ProcId}]失败！-> 失败原因：{Message}")]
    public static partial void LogDomesticRewardsPushedFail(this ILogger logger, string procId, string message);
    
    [LoggerMessage(LogLevel.Error, "推送国内商标胜诉奖励[{ProcId}]响应异常！")]
    public static partial void LogDomesticRewardResponseError(this ILogger logger, string procId, Exception exception);
    
    [LoggerMessage(LogLevel.Error, "推送国内商标胜诉奖励数据异常！")]
    public static partial void LogPushingDomesticTrademarkRewardsError(this ILogger logger, Exception exception);
    
    [LoggerMessage(LogLevel.Information, "GRPC回调结果{MessageResult}")]
    public static partial void LogMessageResult(this ILogger logger, MessageResult messageResult);
    
    [LoggerMessage(LogLevel.Error, "任务刷新胜诉奖励生效日异常！")]
    public static partial void LogRefreshRewardEffectiveDateError(this ILogger logger, Exception exception);
    
    [LoggerMessage(LogLevel.Warning, "推送专利提成数据[{Id}]失败！-> 失败原因：{Message}")]
    public static partial void LogPatentCommissionPushedFail(this ILogger logger, string id, string message);
    
    [LoggerMessage(LogLevel.Error, "推送专利权值数据异常！")]
    public static partial void LogPushingPatentWeightsDataError(this ILogger logger, Exception exception);
    
    [LoggerMessage(LogLevel.Error, "推送专利提成数据响应异常！")]
    public static partial void LogPatentCommissionPushedError(this ILogger logger,Exception exception);

    [LoggerMessage(LogLevel.Warning, "专利提成推送过滤命中规则：{WorkFlowName}！专利提成数据：{BillBonusCaseList}，推送提成数据：{CommissionWeight}")]
    public static partial void LogPushingPatentCommissionExclude(this ILogger logger, string workflowName,
        BillBonusCaseList billBonusCaseList, CommissionWeight commissionWeight);
}