﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class ForeignSupplierFeeRepository(IRedisCache<RedisCacheOptionsBase> redisCache) : IForeignSupplierFeeRepository
{
    
    public Task<bool> AddAsync(string feesId, IEnumerable<CaseFeeList> fees)
    {
        return redisCache.SetCacheValuesAsync(GenerateKey(feesId), fees.ToDictionary(dto => dto.FeeId, dto => dto), TimeSpan.FromDays(1));
    }

    public Task<IEnumerable<CaseFeeList?>> GetAsync(string feesId)
    {
        return redisCache.GetCacheValuesAsync<CaseFeeList>(GenerateKey(feesId));
    }

    public Task<bool> DeleteAsync(string feesId)
    {
        return redisCache.RemoveCacheValuesAsync(GenerateKey(feesId));
    }

    private static string GenerateKey(string feesId)
    {
        return $"ForeignSupplierFee:{feesId}";
    }
}