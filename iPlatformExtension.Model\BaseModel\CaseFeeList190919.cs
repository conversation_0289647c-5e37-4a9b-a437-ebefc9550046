using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_fee_list_190919", DisableSyncStructure = true)]
	public partial class CaseFeeList190919 {

		[ Column(Name = "fee_id", StringLength = 50, IsNullable = false)]
		public string FeeId { get; set; }

		[ Column(Name = "is_import")]
		public bool IsImport { get; set; } = false;

		[ Column(Name = "name_zh_cn", StringLength = 1000)]
		public string NameZhCn { get; set; }

		[ Column(Name = "pay_officer_legal_date")]
		public DateTime? PayOfficerLegalDate { get; set; }

		[ Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
		public string ProcId { get; set; }

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

	}

}
