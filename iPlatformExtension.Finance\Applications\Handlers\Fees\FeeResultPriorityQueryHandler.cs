﻿using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Service.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultPriorityQueryHandler(IPriorityQueryService priorityQuery)
    : IRequestHandler<FeeResultPriorityQuery>
{
    public async Task Handle(FeeResultPriorityQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return;

        var caseIds = request.CaseIds;
        var feeResults = request.FeeResults;

        var priorities = await priorityQuery.GetPrioritiesByCaseIdsAsync(caseIds);
        foreach (var feeItem in feeResults)
        {
            feeItem.Priorities = priorities.TryGetValue(feeItem.CaseId, out var priorityInfos)
                ? priorityInfos
                : Array.Empty<PriorityInfo>();
        }
    }
}