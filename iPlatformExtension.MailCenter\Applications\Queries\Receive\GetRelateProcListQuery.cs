﻿using iPlatformExtension.MailCenter.Applications.Models;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.Receive;

/// <summary>
/// 获取关联任务列表
/// </summary>
/// <param name="ApplyNo">开案号</param>
/// <param name="Volume">我方文号</param>
/// <param name="CountryId">国家地区</param>
/// <param name="AppNo">申请号</param>
/// <param name="CustomerId">客户名称</param>
/// <param name="ApplicantId">申请人代表</param>
/// <param name="ProcNo">任务编号</param>
/// <param name="CtrlProcId">任务名称</param>
/// <param name="CaseName">案件名称</param>
/// <param name="CaseNo">客户文号</param>
/// <param name="RegisterNo">注册号</param>
public record GetRelateProcListQuery(
    string? MailId,
    string? ApplyNo,
    string? Volume,
    List<string>? CountryId,
    string? AppNo,
    string? CustomerId,
    string? ApplicantId,
    string? ProcNo,
    string? CtrlProcId,
    string? CaseName,
    string? CaseNo,
    string? RegisterNo
) : PageModel, IRequest<IEnumerable<GetRelateProcListDto>>;
