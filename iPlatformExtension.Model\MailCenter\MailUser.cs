﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_user", DisableSyncStructure = true)]
	public partial class MailUser {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 发送类型 cc:抄送,to:收件人,from:发件人,se:密送
		/// </summary>
		[Column(Name = "address_type", StringLength = 50, IsNullable = false)]
		public string AddressType { get; set; }

		/// <summary>
		/// 显示名称
		/// </summary>
		[Column(Name = "display_name", StringLength = -1)]
		public string DisplayName { get; set; }

		[Column(Name = "mail_address", StringLength = 200, IsNullable = false)]
		public string MailAddress { get; set; }

		/// <summary>
		/// 收件id/发件id
		/// </summary>
		[Column(Name = "mail_id", StringLength = 50, IsNullable = false)]
		public string MailId { get; set; }

		[Column(Name = "seq", DbType = "int")]
		public int? Seq { get; set; }

	}

}
