﻿using System.Diagnostics;
using System.Globalization;
using System.IO.Compression;
using System.Text;
using System.Web;
using System.Xml;
using iPlatformExtension.Common.Office.Excel;
using iPlatformExtension.Common.Office.Excel.Models;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.ObjectPool;
using MiniExcelLibs;

namespace iPlatformExtension.Common.Extensions;

public static class ExcelExtension
{
    private static readonly Encoding utf8WithBom = new UTF8Encoding(true);

    private const string RNamespaceUri = "http://schemas.openxmlformats.org/officeDocument/2006/relationships";

    private const string XNamespaceUri = "http://schemas.openxmlformats.org/spreadsheetml/2006/main";

    public static async Task<SheetDataAppendWriter?> CreateSheetDataWriterAsync(this Stream stream, string sheetName = "sheet1")
    {
        using var zipArchive = new ZipArchive(stream, ZipArchiveMode.Update, true, utf8WithBom);
        var entry = zipArchive.GetEntry($"xl/worksheets/{sheetName}.xml");
        if (entry is null) return null;

        var fileName = stream is FileStream fileStream ? fileStream.Name : $"tempFile-{Guid.NewGuid()}";

        var writer = new SheetDataAppendWriter(fileName, sheetName, entry.Open());
        await writer.InitializeAsync();

        return writer;
    }

    public static Task AppendAsync<T>(this SheetDataAppendWriter writer, IEnumerable<T> data, EntityTypeInfo typeInfo,
        StringBuilder? stringBuilder = null)
    {
        var index = writer.Count;
        stringBuilder ??= new StringBuilder();
        var count = 0;
        
        foreach (var x1 in data)
        {
            IEnumerable<EntityPropertyInfo> propertyInfos = typeInfo.EntityPropertyInfos;
            var excelColumnInfos = SortCustomProps(propertyInfos
                .Where(info => !info.ExcelColumnInfo.ExcelIgnore)
                .OrderBy(info => info.ExcelColumnInfo, ExcelColumnInfo.ColumnIndexComparer)
                .Select(info => info.ExcelColumnInfo)
                .ToList());

            var excelPropertyInfos = excelColumnInfos.Select(info => info?.PropertyInfo)
                .ToList();

            index++;
            stringBuilder.AppendLine();
            stringBuilder.Append($"""<x:row r="{index}">""");

            var cellIndex = 1;
            foreach (var entityPropertyInfo in excelPropertyInfos)
            {
                var columnName = ConvertXyToCell(cellIndex, index);
                var s = "2";

                if (entityPropertyInfo is null)
                {
                    cellIndex++;
                    continue;
                }
                
                var propertyValue = x1 is not null && entityPropertyInfo.Get is not null
                    ? entityPropertyInfo.Get(x1)
                    : null;
                var excelColumnInfo = entityPropertyInfo.ExcelColumnInfo;
                        
                if (propertyValue is null)
                {
                    stringBuilder.Append($"""<x:c r="{columnName}" s="{s}"></x:c>""");
                }
                else
                {
                    var cellType = "str";
                    var cellValue = string.Empty;
                    
                    if (propertyValue is string propertyStringValue)
                    {
                        // stringBuilder.Append($"""<x:c t="str" r="{columnName}" s="{s}"><x:v>{HttpUtility.HtmlEncode(propertyStringValue)}</x:v></x:c>""");
                        cellValue = HttpUtility.HtmlEncode(propertyStringValue);
                    }
                    else if (excelColumnInfo.ExcelFormat is not null && propertyValue is IFormattable formattableValue)
                    {
                        var formattedValue = formattableValue.ToString(excelColumnInfo.ExcelFormat,
                            CultureInfo.CurrentCulture);
                        cellValue = HttpUtility.HtmlEncode(formattedValue);
                    }
                    else
                    {
                        var propertyType = excelColumnInfo.ExcludeNullableType;
                        if (propertyValue is Enum @enum)
                        {
                            cellValue = @enum.GetDescription();
                        }
                        else if (propertyType.IsNumericType())
                        {
                            cellType = "n";
                            cellValue =
                                (propertyValue as IConvertible)!.ToString(CultureInfo.CurrentCulture);
                        }
                        else switch (propertyValue)
                        {
                            case bool booleanValue:
                                cellType = "b";
                                cellValue = booleanValue ? "1" : "0";
                                break;
                            case byte[]:
                                s = "4";
                                // todo 文件处理
                                break;
                            case DateTime dateTime:
                                cellType = "str";
                                cellValue = excelColumnInfo.ExcelFormat is not null
                                    ? dateTime.ToString(excelColumnInfo.ExcelFormat, CultureInfo.CurrentCulture)
                                    : dateTime.ToString(CultureInfo.CurrentCulture);
                                break;
                            case DateTimeOffset dateTimeOffset:
                                cellType = "str";
                                cellValue = excelColumnInfo.ExcelFormat is not null
                                    ? dateTimeOffset.ToString(excelColumnInfo.ExcelFormat, CultureInfo.CurrentCulture)
                                    : dateTimeOffset.ToString(CultureInfo.CurrentCulture);
                                break;
                            case DateOnly dateOnly:
                                cellType = "str";
                                cellValue = excelColumnInfo.ExcelFormat is not null
                                    ? dateOnly.ToString(excelColumnInfo.ExcelFormat, CultureInfo.CurrentCulture)
                                    : dateOnly.ToString(CultureInfo.CurrentCulture);
                                break;
                            case TimeOnly timeOnly:
                                cellType = "str";
                                cellValue = excelColumnInfo.ExcelFormat is not null
                                    ? timeOnly.ToString(excelColumnInfo.ExcelFormat, CultureInfo.CurrentCulture)
                                    : timeOnly.ToString(CultureInfo.CurrentCulture);
                                break;
                            default:
                                cellValue = HttpUtility.HtmlEncode(propertyValue.ToString());
                                break;
                        }
                    }
                    
                    stringBuilder.Append($"""<x:c t="{cellType}" r="{columnName}" s="{s}" """);

                    if (cellValue is not null)
                    {
                        if (cellValue.StartsWith(" ") || cellValue.EndsWith(" "))
                        {
                            stringBuilder.Append(""" xml:space="preserve" """);
                        }

                        stringBuilder.Append($"><x:v>{cellValue}</x:v>");
                    }
                    
                    stringBuilder.Append("</x:c>");
                }

                cellIndex++;
            }
            
            stringBuilder.Append("</x:row>");
            stringBuilder.AppendLine();
            count++;
        }

        return writer.WriteDataAsync(stringBuilder, count);
    }
    
    public static void Append<T>(this Stream stream, IEnumerable<T> data, EntityTypeInfo typeInfo, StringBuilder? stringBuilder = null, string sheetName = "sheet1")
    {
        using var zipArchive = new ZipArchive(stream, ZipArchiveMode.Update, true, utf8WithBom);
        var entry = zipArchive.GetEntry($"xl/worksheets/{sheetName}.xml");
        if (entry is null) return;

        using var sheetStream = entry.Open();
        var document = new XmlDocument();
        document.Load(sheetStream);

        var nameManager = new XmlNamespaceManager(document.NameTable);
        nameManager.AddNamespace("r", RNamespaceUri);
        nameManager.AddNamespace("x", XNamespaceUri);
        
        var xmlNode = document.SelectSingleNode("x:worksheet/x:sheetData", nameManager);
        if (xmlNode is null) return;
        
        var childrenNodes = xmlNode.SelectNodes("x:row", nameManager);
        var index = childrenNodes?.Count ?? 0;

        // stringBuilder ??= new StringBuilder();
        // stringBuilder.Append(xmlNode.InnerXml);
                
        foreach (var x1 in data)
        {
            index++;

            // stringBuilder.Append($"""<x:row r="{index}">""");
            var row = document.CreateElement("x", "row", XNamespaceUri);
            row.SetAttribute("r", index.ToString());
                    

            IEnumerable<EntityPropertyInfo> propertyInfos = typeInfo.EntityPropertyInfos;
            var excelColumnInfos = SortCustomProps(propertyInfos
                .Where(info => !info.ExcelColumnInfo.ExcelIgnore)
                .OrderBy(info => info.ExcelColumnInfo, ExcelColumnInfo.ColumnIndexComparer)
                .Select(info => info.ExcelColumnInfo)
                .ToList());

            var excelPropertyInfos = excelColumnInfos.Where(info => info is not null).Select(info => info!.PropertyInfo)
                .ToList();

            var cellIndex = 1;
            foreach (var entityPropertyInfo in excelPropertyInfos)
            {
                var columnName = ConvertXyToCell(cellIndex, index);
                var s = "2";

                var cellNode = document.CreateElement("x", "c", XNamespaceUri);
                cellNode.SetAttribute("r", columnName);

                var propertyValue = x1 is not null && entityPropertyInfo.Get is not null
                    ? entityPropertyInfo.Get(x1)
                    : null;
                var excelColumnInfo = entityPropertyInfo.ExcelColumnInfo;
                        
                if (propertyValue is null)
                {
                    // stringBuilder.Append($"""<x:c r="{columnName}" s="{s}"></x:c>""");
                    cellNode.SetAttribute("s", s);
                }
                else
                {
                    var cellType = "str";
                    var cellValue = string.Empty;
                    var valueNode = document.CreateElement("x", "v", XNamespaceUri);
                    if (propertyValue is string propertyStringValue)
                    {
                        // stringBuilder.Append($"""<x:c t="str" r="{columnName}" s="{s}"><x:v>{HttpUtility.HtmlEncode(propertyStringValue)}</x:v></x:c>""");
                        cellNode.SetAttribute("s", s);
                        cellValue = HttpUtility.HtmlEncode(propertyStringValue);
                    }
                    else if (excelColumnInfo.ExcelFormat is not null && propertyValue is IFormattable formattableValue)
                    {
                        var formattedValue = formattableValue.ToString(excelColumnInfo.ExcelFormat,
                            CultureInfo.CurrentCulture);
                        cellValue = HttpUtility.HtmlEncode(formattedValue);
                    }
                    else
                    {
                        var propertyType = excelColumnInfo.ExcludeNullableType;
                        if (propertyValue is Enum @enum)
                        {
                            cellValue = @enum.GetDescription();
                        }
                        else if (propertyType.IsNumericType())
                        {
                            cellType = "n";
                            cellValue =
                                (propertyValue as IConvertible)!.ToString(CultureInfo.CurrentCulture);
                        }
                        else if (propertyValue is bool booleanValue)
                        {
                            cellType = "b";
                            cellValue = booleanValue ? "1" : "0";
                        }
                        else if (propertyValue is byte[])
                        {
                            s = "4";
                            // todo 文件处理
                        }
                        else if (propertyValue is DateTime dateTime)
                        {
                            cellType = "str";
                            cellValue = excelColumnInfo.ExcelFormat is not null
                                ? dateTime.ToString(excelColumnInfo.ExcelFormat, CultureInfo.CurrentCulture)
                                : dateTime.ToString(CultureInfo.CurrentCulture);
                        }
                        else if (propertyValue is DateTimeOffset dateTimeOffset)
                        {
                            cellType = "str";
                            cellValue = excelColumnInfo.ExcelFormat is not null
                                ? dateTimeOffset.ToString(excelColumnInfo.ExcelFormat, CultureInfo.CurrentCulture)
                                : dateTimeOffset.ToString(CultureInfo.CurrentCulture);
                        }
                        else
                        {
                            cellValue = HttpUtility.HtmlEncode(propertyValue.ToString());
                        }
                    }

                    cellNode.SetAttribute("t", cellType);
                    
                    // stringBuilder.Append($"""<x:c t="{cellType}" r="{columnName}" s="{s}" """);

                    if (cellValue is not null)
                    {
                        if (cellValue.StartsWith(" ") || cellValue.EndsWith(" "))
                        {
                            cellNode.SetAttribute("xml:space", "preserve");
                            // stringBuilder.Append(""" "xml:space"="preserve" """);
                        }

                        // stringBuilder.Append($"><x:v>{cellValue}</x:v>");
                        valueNode.InnerText = cellValue;
                    }
                    cellNode.AppendChild(valueNode);
                    
                    // stringBuilder.Append("</x:c>");
                }

                cellIndex++;
                row.AppendChild(cellNode);
            }

            // stringBuilder.Append("</x:row>");
            xmlNode.AppendChild(row);
        }


        // xmlNode.InnerXml = stringBuilder.ToString();
        
        Debug.WriteLine(xmlNode.InnerXml);

        sheetStream.Seek(0, SeekOrigin.Begin);
        sheetStream.SetLength(0);
        document.Save(sheetStream);
        
        Debug.WriteLine(document.InnerXml);

        // return Task.CompletedTask;
    }
    
    internal static IEnumerable<ExcelColumnInfo?> SortCustomProps(List<ExcelColumnInfo> props)
    {
        // https://github.com/shps951023/MiniExcel/issues/142
        //TODO: need optimize performance

        var withCustomIndexProps = props.Where(w => w.ExcelColumnIndex is > -1);
        if (withCustomIndexProps.GroupBy(g => g.ExcelColumnIndex).Any(group => group.Count() > 1))
            throw new InvalidOperationException($"Duplicate column name");

        var maxColumnIndex = props.Count - 1;
        if (withCustomIndexProps.Any())
            maxColumnIndex = Math.Max(withCustomIndexProps.Max(w => w.ExcelColumnIndex!.Value), maxColumnIndex);

        var withoutCustomIndexProps = props.Where(w => w.ExcelColumnIndex is null or -1).ToList();

        List<ExcelColumnInfo?> newProps = [];
        var index = 0;
        for (var i = 0; i <= maxColumnIndex; i++)
        {
            var p1 = withCustomIndexProps.SingleOrDefault(s => s.ExcelColumnIndex == i);
            if (p1 != null)
            {
                newProps.Add(p1);
            }
            else
            {
                var p2 = withoutCustomIndexProps.ElementAtOrDefault(index);
                if (p2 == null)
                {
                    newProps.Add(null);
                }
                else
                {
                    p2.ExcelColumnIndex = i;
                    newProps.Add(p2);
                }
                index++;
            }
        }
        return newProps;
    }

    public static string ConvertXyToCell(int x, int y)
    {
        var dividend = x;
        var columnName = string.Empty;
        int modulo;

        while (dividend > 0)
        {
            modulo = (dividend - 1) % 26;
            columnName = Convert.ToChar(65 + modulo) + columnName;
            dividend = (dividend - modulo) / 26;
        }
        return $"{columnName}{y}";
    }

    public static async Task<PhysicalFileResult> PageExportExcelAsync<TRequest, TResult>(this ControllerBase controller,
        TRequest query,
        ExcelExportOptions<TRequest, TResult> exportOptions, CancellationToken cancellationToken) where TRequest : IPageQuery, IRequest<IEnumerable<TResult>>
    {
        var httpContext = controller.HttpContext;
        var services = httpContext.RequestServices;
        
        
        var sender = services.GetRequiredService<ISender>();
        var typeInfoProvider = services.GetRequiredService<EntityTypeInfoProvider>();
        var stringBuilderPool = services.GetService<ObjectPool<StringBuilder>>();
        var stringBuilder = exportOptions.StringBuilder ?? stringBuilderPool?.Get() ?? new StringBuilder();
        
        var onQuerying = exportOptions.OnQuerying;
        var onQueried = exportOptions.OnQueried;

        await onQuerying(controller, query, cancellationToken);
        var result = await sender.Send(query, cancellationToken);
        await onQueried(controller, result, cancellationToken);
        
        var filePath = Path.Combine(TempDirectory.Current, exportOptions.TempDirectory, $"{DateTimeOffset.Now.Ticks}-{Guid.NewGuid()}-{exportOptions.FileName}");
        Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
        await using var fileStream = File.Create(filePath);
        
        await fileStream.SaveAsAsync(result, excelType: ExcelType.XLSX, cancellationToken: cancellationToken);

        SheetDataAppendWriter? writer = null;
        
        while (result is PageResult<TResult> {HasNextPage:true} pageResult)
        {
            pageResult.Dispose();
            query.PageIndex = pageResult.Page + 1;
            query.Total = pageResult.Total;
            
            await onQuerying(controller, query, cancellationToken);
            result = await sender.Send(query, cancellationToken);
            await onQueried(controller, result, cancellationToken);

            writer ??= await fileStream.CreateSheetDataWriterAsync();
            if (writer is null)
            {
                fileStream.Append(result, typeInfoProvider.Get(typeof(TResult)), stringBuilder);
            }
            else
            {
                await writer.AppendAsync(result, typeInfoProvider.Get(typeof(TResult)), stringBuilder);
            }
            
            stringBuilder.Clear();
            (result as List<TResult>)?.Clear();
        }
        fileStream.Close();

        if (writer is not null)
        {
            await writer.CombineAsync();
            await writer.DisposeAsync();
        }
        
        stringBuilderPool?.Return(stringBuilder);
        
        if (result is IDisposable disposable)
        {
            disposable.Dispose();
        }

        return controller.PhysicalFile(Path.GetFullPath(filePath), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            HttpUtility.UrlEncode(exportOptions.FileName));
    }
}