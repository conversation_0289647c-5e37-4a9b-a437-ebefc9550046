﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery.LicenseFiling;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Exceptions;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Extensions;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery.LicenseFiling;

internal sealed class LicenseFilingOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IBaseCountryRepository countryRepository,
    IDeliveryInfoRepository deliveryInfoRepository,
    IApplicantTypeRepository applicantTypeRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    UnitOfWorkManager unitOfWorkManager,
    ILoggerFactory loggerFactory,
    PhoenixClientFactory phoenixClientFactory)
    : PhoenixDeliveryHandleBase<LicenseFilingOrderCommand, TrademarkLicenseFilingOrder,
            PhoenixResponseParameters<OrderData>>(stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository,
            unitOfWorkManager, loggerFactory, phoenixClientFactory),
        IDeliveryHandler<LicenseFilingOrderCommand, TrademarkLicenseFilingOrder,
            PhoenixResponseParameters<OrderData>>
{
    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.StartupDelivery;
    
    public override async Task<TrademarkLicenseFilingOrder> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        ArgumentNullException.ThrowIfNull(_deliveryInfo.OtherInfo);

        var otherInfo = _deliveryInfo.OtherInfo;
        
        var currentApplicant = _deliveryInfo.Applicants!.Single(applicant => applicant.IsRepresent ?? false);

        var countryId = currentApplicant.CountryId ??
                        throw new PropertyMissingException(currentApplicant.ApplicantId, currentApplicant.CountryId, "申请人");
        var applicantBookType = ApplicantBookType.GetApplicantBookType(countryId);
        
        string country;
        if (applicantBookType == ApplicantBookType.Overseas)
        {
            var countryInfo = await countryRepository.GetCacheValueAsync(countryId);
            if (countryInfo is null)
            {
                throw new NotFoundException(countryId, "国家");
            }

            if (string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCode) || string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCountryName))
            {
                throw new ApplicationException("商标局没有对应的国家编码和名称");
            }

            country = $"{countryInfo.TrademarkDeliveryCode}  {countryInfo.TrademarkDeliveryCountryName}";
        }
        else
        {
            country = applicantBookType.Description;
        }

        var applicantType = await applicantTypeRepository.GetCacheValueAsync(currentApplicant.TypeId.GetOrDefaultEmpty());
        if (applicantType is null)
        {
            throw new ApplicantTypeMissingException(currentApplicant, "当前申请人");
        }
        
        var licensee = _deliveryInfo.Applicants!.FirstOrDefault(applicant => applicant.DeliveryBusinessType is not null);
        if (licensee is null)
        {
            throw new PropertyMissingException(_deliveryInfo.ProcId, _deliveryInfo.Applicants, "递交任务", "许可人信息");
        }

        var licenseeApplicantType = await applicantTypeRepository.GetCacheValueAsync(licensee.TypeId.GetOrDefaultEmpty());
        if (licenseeApplicantType is null)
        {
            throw new ApplicantTypeMissingException(licensee, "转让人");
        }

        var formerApplicantCountryId = licensee.CountryId ??
                                       throw new PropertyMissingException(licensee.Id,
                                           licensee.CountryId, "转让人", "国家信息");

        var licenseeApplicantBookType = ApplicantBookType.GetApplicantBookType(formerApplicantCountryId);
        
        var attachments = _deliveryInfo.Files?
                              .Where(file => int.TryParse(file.FileCode, out _) && file.BaseFileType is "申请人文件" or "递交官方")
                              .OrderBy(file => file.FileDesc)
                              .Select(file => 
                                  new ApplicantAttachment(
                                      file.FileName, 
                                      file.Url ?? string.Empty, 
                                      ApplicantAttachmentType.OfficialAttachment, 
                                      new ApplicantAttachmentSubType(int.Parse(file.FileCode!), file.FileDesc ?? string.Empty))) 
                          ?? Array.Empty<ApplicantAttachment>();
        
        var grandNumbers = _deliveryInfo.OtherInfo.TrademarkNiceClasses?.Split(';') ?? Array.Empty<string>();
        
        var niceCategories = grandNumbers.GroupJoin(_deliveryInfo.NiceCategories ?? Array.Empty<DeliveryNiceCategory>(),
            Convert.ToInt32, category => Convert.ToInt32(category.GrandNumber), (grandNumber, categories) =>
                new BrandInfo
                {
                    BrandRegisterNo = _deliveryInfo.AppNo,
                    FirstCgNo = grandNumber,
                    DeleteType = categories.Any() ? 2 : 1,
                    CategoryFlowStateName = "已注册",
                    GoodsItems = categories.Any()
                        ? categories.Select(category => new GoodsItem
                        {
                            Code = "0",
                            Name = category.CategoryName,
                            Status = "0"
                        })
                        : default
                }).ToArray();

        var order = new TrademarkLicenseFilingOrder
        {
            BookType = applicantBookType.Code.ToString(),
            Country = country,
            OwnerType = applicantType.GetOwnerType(),
            ApplicantName = currentApplicant.ApplicantNameCn,
            IdCard = currentApplicant.CardNo,
            ApplicantEnglishName = currentApplicant.ApplicantNameEn,
            ApplicantAddress = currentApplicant.AddressCn,
            ApplicantEnglishAddress = currentApplicant.AddressEn,
            Code = currentApplicant.Postcode,
            PrincipalName = _deliveryInfo.ContactPerson,
            PrincipalTel = _deliveryInfo.ContactTel,
            AgentOrganConName = _deliveryInfo.AgentUser,
            AgentOrganTel = _deliveryInfo.ContactTel ?? throw new PropertyMissingException(_deliveryInfo.ProcId, _deliveryInfo.ContactTel, "递交任务", "联系电话"),
            ContractEffectiveDate = otherInfo.ContractEffectiveDate,
            ContractTerminationDate = otherInfo.ContractTerminationDate,
            ContactTel = _deliveryInfo.ContactTel,
            ContactEmail = _deliveryInfo.ContactMailBox,
            ContactName = _deliveryInfo.ContactPerson,
            LicenseType = int.TryParse(otherInfo.LicenseType, out var licenseType) ? licenseType : null,
            CertificatesType = currentApplicant.CardType.GetApplicantCertificationType(),
            SubjectType = currentApplicant.GetSubjectType(),
            Licensee = new DeliveryApplicantInfo
            {
                BookType = licenseeApplicantBookType.Code.ToString(),
                OwnerType = licenseeApplicantType.GetOwnerType(),
                ApplicantName = licensee.ApplicantNameCn,
                IdCard = licensee.CardNo,
                ApplicantEnglishName = licensee.ApplicantNameEn,
                ApplicantAddress = licensee.AddressCn,
                ApplicantEnglishAddress = licensee.AddressEn,
                Code = licensee.Postcode,
                CertificatesType = licensee.CardType.GetApplicantCertificationType(),
                SubjectType = licensee.GetSubjectType()
            },
            ApplicantAttachments = attachments,
            BrandInfos = niceCategories,
            OrderToken = _deliveryInfo.ProcId
        };

        return order;
    }

    public override Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(TrademarkLicenseFilingOrder request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        _phoenixClient ??= _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
        return _phoenixClient.CreateOrderAsync(PhoenixUri.TrademarkLicenseFiling, request);
    }

    public override Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        var orderNo = response.Data?.OrderNo!;
        const int newStatus = (int) DeliveryStatus.Ordered;
        
        return Handler.UpdateDeliveryInfoInternalAsync(deliveryInfo =>
        {
            deliveryInfo.Status = newStatus;
            deliveryInfo.OrderNo = orderNo;
            deliveryInfo.OperationResult = true;
            return ValueTask.CompletedTask;
        }, cancellationToken);
    }

    public override async Task InitializeAsync(LicenseFilingOrderCommand command, CancellationToken cancellationToken)
    {
        var procId = command.ProcId;
        _deliveryInfo = await DeliveryInfoRepository.Where(info => info.ProcId == procId)
            .Include(deliInfo => deliInfo.OtherInfo)
            .IncludeMany(deliInfo => deliInfo.Applicants)
            .IncludeMany(deliInfo => deliInfo.NiceCategories)
            .IncludeMany(deliInfo => deliInfo.Files)
            .ToOneAsync(cancellationToken);
        if (_deliveryInfo is null)
        {
            throw new NotFoundException(procId, "递交任务");
        }

        if (command.Version != _deliveryInfo.Version)
        {
            throw new VersionException(command.Version, _deliveryInfo.Version);
        }

        _phoenixClient = _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
    }

    public async Task<bool> HandleExceptionAsync(Exception ex, CancellationToken cancellationToken = default)
    {
        await Handler.HandleExceptionInternalAsync(ex, true, false, cancellationToken);
        return false;
    }
}