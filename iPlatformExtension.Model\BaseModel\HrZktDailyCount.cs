using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "hr_zkt_daily_count", DisableSyncStructure = true)]
	public partial class HrZktDailyCount {

		[ Column(Name = "am_on", StringLength = 50)]
		public string AmOn { get; set; }

		[ Column(Name = "come_late", StringLength = 50)]
		public string ComeLate { get; set; }

		[ Column(Name = "cur_id", StringLength = 50, IsNullable = false)]
		public string CurId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "is_come_late")]
		public bool IsComeLate { get; set; } = false;

		[ Column(Name = "left_early", StringLength = 50)]
		public string LeftEarly { get; set; }

		[ Column(Name = "over_time", StringLength = 50)]
		public string OverTime { get; set; }

		[ Column(Name = "pm_on", StringLength = 50)]
		public string PmOn { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

		[ Column(Name = "work_day")]
		public DateTime? WorkDay { get; set; }

		[ Column(Name = "work_off")]
		public DateTime? WorkOff { get; set; }

		[ Column(Name = "work_on")]
		public DateTime? WorkOn { get; set; }

	}

}
