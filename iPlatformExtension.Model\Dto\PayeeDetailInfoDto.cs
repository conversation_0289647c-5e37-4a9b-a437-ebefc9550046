﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 收款主体详情
/// </summary>
public record PayeeDetailInfoDto
{
    /// <summary>
    /// 公司id 
    /// </summary>
    public string Id { get; init; } = default!;

    /// <summary>
    /// 中文名
    /// </summary>
    public string CnName { get; init; } = default!;

    /// <summary>
    /// 简称
    /// </summary>
    public string? AbbreviatedName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string? EnName { get; init; }

    /// <summary>
    /// 税率
    /// </summary>
    public decimal TaxRate { get; init; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; init; }

    /// <summary>
    /// 传真
    /// </summary>
    public string? Fax { get; init; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? Tel { get; init; }

    /// <summary>
    /// 中文地址
    /// </summary>
    public string CnAddress { get; init; } = default!;

    /// <summary>
    /// 英文地址
    /// </summary>
    public string? EnAddress { get; set; }
    
    /// <summary>
    /// 机构代码
    /// </summary>
    public string? CompanyCode { get; init; }

    /// <summary>
    /// 收款人
    /// </summary>
    public string? Payee { get; init; }

    /// <summary>
    /// 开票人
    /// </summary>
    public string? Invoicer { get; init; }

    /// <summary>
    /// 邮编
    /// </summary>
    public string? PostCode { get; init; }

    /// <summary>
    /// 复核人
    /// </summary>
    public string? Reviewer { get; init; }

    /// <summary>
    /// 地区
    /// </summary>
    public string? DistrictId { get; init; }

    /// <summary>
    /// 银行账户信息
    /// </summary>
    public IEnumerable<PayeeBankAccountInfo> BankAccountInfos { get; set; } = Array.Empty<PayeeBankAccountInfo>();
}