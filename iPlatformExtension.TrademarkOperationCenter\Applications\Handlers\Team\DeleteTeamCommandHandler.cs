﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;
using MediatR;
using Microsoft.AspNetCore.Http.HttpResults;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 删除团队命令处理者
    /// </summary>
    public class DeleteTeamCommandHandler(IFreeSql freeSql, ISysTeamRepository sysTeamRepository, IMediator mediator, IHttpContextAccessor content)
        : IRequestHandler<DeleteTeamCommand>
    {
        public async Task Handle(DeleteTeamCommand request, CancellationToken cancellationToken)
        {
            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userId);

            //移除团队成员
            var teamMemberIdList = freeSql.Select<SysTeamAssociateMember>().Where(it => request.TeamId.Contains(it.TeamId))
                .ToList(dto => dto.TeamMemberId);
            await mediator.Send(new DeleteTeamMemberCommand(teamMemberIdList.ToArray()));
            //移除客户列表
            var sysTeamCustomerIdList = freeSql.Select<SysTeamCustomer>().Where(it => request.TeamId.Contains(it.TeamId))
                .ToList(dto => dto.SysTeamCustomerId);
            await mediator.Send(new DeleteCustomerCommand(sysTeamCustomerIdList.ToArray()));
            //删除团队
            var deleteList = await sysTeamRepository.Where(it => request.TeamId.Contains(it.TeamId)).ToListAsync(cancellationToken);
            foreach (var item in deleteList)
            {
                item.Enable = SysEnum.SystemTeamStatus.Delete.GetHashCode();
                item.UpdateTime = DateTime.Now;
                item.UpdateUserId = userId;
            }

            await sysTeamRepository.UpdateAsync(deleteList, cancellationToken);
        }
    }
}

