﻿using System.Text;
using Confluent.Kafka;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.KafKa.Models;

namespace iPlatformExtension.Common.MQ.KafKa.Converters;

public class StringMessageKeyConverter : IDeserializer<MessageKey>, ISerializer<MessageKey>
{
    public MessageKey Deserialize(ReadOnlySpan<byte> data, bool isNull, SerializationContext context)
    {
        return isNull ? new MessageKey() : Encoding.UTF8.GetString(data);
    }

    public byte[] Serialize(MessageKey data, SerializationContext context)
    {
        return data.ToString().GetBytes(Encoding.UTF8);
    }
}