﻿namespace iPlatformExtension.Public.Applications.Models.Flow;

/// <summary>
/// 流程人员信息
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="CurUserId">当前流程人员</param>
/// <param name="CurUserName">当前流程人员名称</param>
/// <param name="FlowType">流程类型</param>
/// <param name="FlowName">流程类型名称</param>
/// <param name="UserCode">工号</param>
public record ProcessInformationDto(string ProcId, string CurUserId, string CurUserName, string FlowType, string FlowName,string UserCode);

