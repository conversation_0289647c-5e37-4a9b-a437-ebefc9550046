using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_legal_provisions", DisableSyncStructure = true)]
	public partial class BasLegalProvisions {

		/// <summary>
		/// 案件类型
		/// </summary>
		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 裁定法条主键ID
		/// </summary>
		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		/// <summary>
		/// 英文名称
		/// </summary>
		[ Column(Name = "text_en_us", StringLength = 50)]
		public string TextEnUs { get; set; }

		/// <summary>
		/// 日文名称
		/// </summary>
		[ Column(Name = "text_ja_jp", StringLength = 50)]
		public string TextJaJp { get; set; }

		/// <summary>
		/// 中文名称
		/// </summary>
		[ Column(Name = "text_zh_cn", StringLength = 50)]
		public string TextZhCn { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
