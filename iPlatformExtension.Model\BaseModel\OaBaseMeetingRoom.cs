using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "oa_base_meeting_room", DisableSyncStructure = true)]
	public partial class OaBaseMeetingRoom {

		/// <summary>
		/// 关联行政邮箱
		/// </summary>
		[ Column(Name = "administrative_email", StringLength = 500)]
		public string AdministrativeEmail { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 地区
		/// </summary>
		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		/// <summary>
		/// 内置笔记本
		/// </summary>
		[ Column(Name = "has_notebookcomputer")]
		public bool? HasNotebookcomputer { get; set; }

		/// <summary>
		/// 内置投影仪
		/// </summary>
		[ Column(Name = "has_projector")]
		public bool? HasProjector { get; set; }

		/// <summary>
		/// 支持电话会议
		/// </summary>
		[ Column(Name = "has_teleconferencing")]
		public bool? HasTeleconferencing { get; set; }

		/// <summary>
		/// 支持视频会议
		/// </summary>
		[ Column(Name = "has_videoconferencing")]
		public bool? HasVideoconferencing { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "resources", StringLength = 500)]
		public string Resources { get; set; }

		[ Column(Name = "room_id", StringLength = 50, IsNullable = false)]
		public string RoomId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 会议室名称
		/// </summary>
		[ Column(Name = "room_name", StringLength = 50)]
		public string RoomName { get; set; }

		/// <summary>
		/// 容纳人数
		/// </summary>
		[ Column(Name = "seating", StringLength = 50)]
		public string Seating { get; set; }

		[ Column(Name = "seq", StringLength = 50)]
		public string Seq { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
