﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Nice;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Nice;

internal sealed class CaseNiceCategoryQueryHandler(IFreeSql freeSql)
    : IRequestHandler<CaseNiceCategoryQuery, IEnumerable<NiceCategoryDto>>
{
    private static readonly IEqualityComparer<CaseNiceCategoryKey>
        caseNiceCategoryNumberJoinComparer = new CaseNoticeCategoryNumberKeyEqualityComparer();

    public async Task<IEnumerable<NiceCategoryDto>> Handle(CaseNiceCategoryQuery request, CancellationToken cancellationToken)
    {
        IEnumerable<NiceCategoryDto> dto = await freeSql.Select<BasTrademarkItemsLevel>().WithLock()
            .From<BasTrademarkItems>()
            .InnerJoin((level, items) => level.CurId == items.CurId)
            .WhereIf(request.ParentCategoryNumbers.Any(),
                table => request.ParentCategoryNumbers.Contains(table.t1.PId))
            .WhereIf(!request.ParentCategoryNumbers.Any(), table => table.t1.Level == 0)
            .Where(table => table.t1.VersionId == request.VersionId)
            .ToListAsync((level, items) => new NiceCategoryDto(level.CurId, level.Level, level.Lid, items.TextZhCn, level.PId),
                cancellationToken);

        if (string.IsNullOrWhiteSpace(request.CaseId)) return dto;

        var trademarkClassTask = freeSql.Select<CaseInfo>(request.CaseId).WithLock()
            .ToOneAsync(info => info.TrademarkClass, cancellationToken);
        
        var levelValue = dto.First().Level ?? -1;
        var categoryNumbers = dto.Select(categoryDto => categoryDto.CategoryNumber).ToList();
        var caseNiceInfoList = await freeSql.Select<CaseTrademarkNiceCategory>().WithLock()
            .Where(category => category.CaseId == request.CaseId)
            .WhereIf(levelValue == 0, category => categoryNumbers.Contains(category.GrandNumber))
            .WhereIf(levelValue == 1, category => categoryNumbers.Contains(category.ParentNumber))
            .WhereIf(levelValue == 2, category => categoryNumbers.Contains(category.CategoryNumber))
            .ToListAsync(cancellationToken);

        var trademarkClasses =
            (await trademarkClassTask)
            .Split(';', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries)
            .Select(classValue => classValue.PadLeft(2, '0'));
        

        dto = dto.GroupJoin(caseNiceInfoList,
            categoryDto =>
                levelValue == 2 
                    ? new CaseNiceCategoryKey(levelValue, categoryDto.CategoryNumber, categoryDto.CategoryName)
                    : new CaseNiceCategoryKey(levelValue, categoryDto.CategoryNumber), 
            category =>
            {
                return levelValue switch
                {
                    0 => new CaseNiceCategoryKey(levelValue, category.GrandNumber),
                    1 => new CaseNiceCategoryKey(levelValue, category.ParentNumber),
                    2 => new CaseNiceCategoryKey(levelValue, category.CategoryNumber, category.CategoryName),
                    _ => throw new ArgumentOutOfRangeException(nameof(levelValue))
                };
            }, (categoryDto, caseNiceCategories) =>
            {
                if (!caseNiceCategories.Any())
                {
                    if (levelValue == 0 && trademarkClasses.Contains(categoryDto.CategoryNumber))
                    {
                        categoryDto.Selected = true;
                    }
                    return categoryDto;
                }
                if (levelValue != 2)
                {
                    categoryDto.Selected = true;
                }
                else
                {
                    var standardCategory = caseNiceCategories.FirstOrDefault(category => category.IsStandard);
                    categoryDto.Selected = categoryDto.ParentNumber == standardCategory?.ParentNumber;
                }
                categoryDto.CustomCategories = caseNiceCategories.Where(category => !category.IsStandard).Select(category => category.CategoryName);
                return categoryDto;
            }, caseNiceCategoryNumberJoinComparer);

        return dto;
    }
    
    private readonly struct CaseNiceCategoryKey
    {
        internal int Level { get; }

        internal string CategoryNumber { get; } = string.Empty;
        
        internal string CategoryName { get; }

        public CaseNiceCategoryKey(int level, string categoryNumber, string categoryName)
        {
            Level = level;
            CategoryNumber = categoryNumber;
            CategoryName = categoryName;
        }
        
        public CaseNiceCategoryKey(int level, string categoryNumber) : this(level, categoryNumber, string.Empty)
        {
            
        }

        public override int GetHashCode()
        {
            return Level.GetHashCode() ^ CategoryName.GetHashCode() ^
                   (string.IsNullOrWhiteSpace(CategoryName) ? 0 : CategoryName.GetHashCode());
        }
    }

    private sealed class CaseNoticeCategoryNumberKeyEqualityComparer : IEqualityComparer<CaseNiceCategoryKey>
    {
        public bool Equals(CaseNiceCategoryKey x, CaseNiceCategoryKey y)
        {
            return x.Level == y.Level && x.CategoryNumber == y.CategoryNumber && x.CategoryName == y.CategoryName;
        }

        public int GetHashCode(CaseNiceCategoryKey obj)
        {
            return obj.GetHashCode();
        }
    }
}