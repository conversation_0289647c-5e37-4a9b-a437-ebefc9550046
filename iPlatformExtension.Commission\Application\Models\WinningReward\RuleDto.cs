﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.Commission.Application.Models.WinningReward;

/// <summary>
/// 胜诉奖励规则DTO
/// </summary>
public sealed class RuleDto
{
    /// <summary>
    /// 任务名称id
    /// </summary>
    [Required]
    public string CtrlProcId { get; set; } = string.Empty;

    /// <summary>
    /// 案件流向
    /// </summary>
    [Required]
    [Display(Name = "案件流向")]
    public string CaseDirection { get; set; } = string.Empty;

    /// <summary>
    /// 裁定结果
    /// </summary>
    [Required]
    [Display(Name = "裁定结果")]
    public string RulingResult { get; set; } = string.Empty;

    
    /// <summary>
    /// 是否情势变更
    /// </summary>
    [Required]
    public bool SituationChanged { get; set; }

    /// <summary>
    /// 计提日期类型
    /// </summary>
    [Required]
    [Display(Name = "计提日期类型")]
    public string DateType { get; set; } = string.Empty;

    /// <summary>
    /// 是否有效
    /// </summary>
    [Required]
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 撰写人奖励
    /// </summary>
    [Required]
    [Range(0.00, double.MaxValue)]
    public decimal WriterReward { get; set; }

    /// <summary>
    /// 导师奖励
    /// </summary>
    [Required]
    [Range(0.00, double.MaxValue)]
    public decimal MentorReward { get; set; }
}