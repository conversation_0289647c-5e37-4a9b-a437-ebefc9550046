﻿namespace iPlatformExtension.Common.Cache;

/// <summary>
/// 针对键为字符串的可缓存仓储的特殊仓储
/// </summary>
/// <typeparam name="TValue">缓存值得类型</typeparam>
public interface IStringKeyCacheableRepository<TValue> : 
    ICacheableRepository<string, TValue>, 
    IStringKeyCacheableRepository,
    IStringKeyValueRepository
{
    /// <summary>
    /// 一般用于返回缓存值中的中文名称或描述值
    /// </summary>
    /// <param name="value">缓存的值</param>
    /// <returns>值对应的中文名称</returns>
    public string? GetCacheTextValue(TValue value);

    string? IStringKeyCacheableRepository.GetCacheTextValue(object value)
    {
        if (value is TValue cacheValue)
        {
            return GetCacheTextValue(cacheValue);
        }

        return null;
    }

    async ValueTask<string?> IStringKeyValueRepository.GetTextValueAsync(string key)
    {
        var cacheValue = await GetCacheValueAsync(key);
        return cacheValue is null ? null : GetCacheTextValue(cacheValue);
    }
}

public interface IStringKeyCacheableRepository
{
    /// <summary>
    /// 根据缓存对象获取其中中文名称的属性值
    /// </summary>
    /// <param name="value">缓存的对象</param>
    /// <returns>中文名称的属性值</returns>
    public string? GetCacheTextValue(object value);
}