namespace iPlatformExtension.MailCenter.Applications.Models.Customer
{
    /// <summary>
    /// 客户信息DTO
    /// </summary>
    public class CustomerDto
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public string CustomerId { get; set; } = string.Empty;

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; } = string.Empty;

        /// <summary>
        /// 客户英文名称
        /// </summary>
        public string? CustomerNameEn { get; set; }

        /// <summary>
        /// 客户编码
        /// </summary>
        public string? CustomerCode { get; set; }

        /// <summary>
        /// 客户全称
        /// </summary>
        public string? CustomerFullName { get; set; }

        /// <summary>
        /// 是否合作
        /// </summary>
        public bool? IsCooperation { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool? IsEnabled { get; set; }

        /// <summary>
        /// 所属大区
        /// </summary>
        public string? BigArea { get; set; }

        /// <summary>
        /// 客户分类
        /// </summary>
        public string? CustomerType { get; set; }

        /// <summary>
        /// 商务人员ID
        /// </summary>
        public string? BusiUserId { get; set; }

        /// <summary>
        /// 商务人员名称
        /// </summary>
        public string? BusiUserName { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// 传真
        /// </summary>
        public string? Fax { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }
    }
}
