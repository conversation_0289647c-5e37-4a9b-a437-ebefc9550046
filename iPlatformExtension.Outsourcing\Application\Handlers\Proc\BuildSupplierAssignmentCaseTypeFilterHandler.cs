﻿using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using iPlatformExtension.Outsourcing.Infrastructure.Authorization.SupplierAssignment;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

// internal sealed class BuildSupplierAssignmentCaseTypeFilterHandler : IBuildSupplierAssignmentFilterHandler
// {
//     public Task HandleAsync(SupplierAssignmentAuthorizationContext context, CancellationToken cancellationToken)
//     {
//         var (caseTypes, _, _, filters, query) = context;
//         if (filters.TryGetValue("case_type_id", out var filterEntry) && filterEntry.Values.Length > 0)
//         {
//             caseTypes.IntersectWith(filterEntry.Values.ToArray());
//         }
//         
//         query.Where(info => caseTypes.Contains(info.CaseInfo.CaseTypeId));
//         return Task.CompletedTask;
//     }
// }

internal sealed class BuildSupplierAssignmentCaseTypeFilterHandler : IMatchNotificationHandler<NotAssignedProcQueryContext>
{
    private string[] _caseTypes = [];
    
    public ValueTask<bool> MatchAsync(NotAssignedProcQueryContext notification, CancellationToken cancellationToken)
    {
        var filters = notification.Filters;
        if (filters.TryGetValue("case_type_id", out var filter) 
            && "custom".Equals(filter.FilterType, StringComparison.OrdinalIgnoreCase))
        {
            _caseTypes = filter.FilterValue.Split(';');
            return ValueTask.FromResult(true);
        }
        
        return ValueTask.FromResult(false);
    }

    public Task HandleAsync(NotAssignedProcQueryContext notification, CancellationToken cancellationToken)
    {
        IEnumerable<string> caseTypes = _caseTypes;
        var query = notification.Query;
        query.WhereIf(_caseTypes.Length > 0, info => caseTypes.Contains(info.CaseInfo.CaseTypeId));
        notification.CountQueryBuilder.WhereIf(_caseTypes.Length > 0,
            info => caseTypes.Contains(info.CaseInfo.CaseTypeId));
        return Task.CompletedTask;
    }
}