﻿using FreeSql.Internal.CommonProvider;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Finance.Infrastructure.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeePagingQueryHandler(IMediator mediator, IFreeSql freeSql)
    : IRequestHandler<FeePagingQuery, PageResult<FeeListItemDto>>
{
    public async Task<PageResult<FeeListItemDto>> Handle(FeePagingQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return await Task.FromCanceled<PageResult<FeeListItemDto>>(cancellationToken);

        var feeQuery = request.FeesQuery;
        var page = request.PageIndex;
        var pageSize = request.PageSize;

        var feePagingQuery = freeSql.Select<CaseFeeList>();
        Select0Provider.CopyData(feeQuery as Select0Provider, feePagingQuery as Select0Provider, null);

        var selectExpression = await mediator.Send(new FeesSelectExpressionCommand(request.SortCondition),
                cancellationToken);
        var feeIds = await feeQuery
            .OrderByPropertyName(request.SortCondition, request.SortOrder.IsAscending())
            .GroupBy(selectExpression)
            .Page(page, pageSize)
            .ToListAsync(aggregate => aggregate.Key.FeeId, cancellationToken);
        
        var countTask = request.Total is null
            ? feePagingQuery.GroupBy(list => list.FeeId).CountAsync(cancellationToken)
            : Task.FromResult(request.Total.Value);

        var queryData = await mediator.Send(new FeeItemsQuery(feeIds, request.SortCondition, request.SortOrder), cancellationToken);
        
        return new PageResult<FeeListItemDto>()
        {
            Data = queryData,
            Page = page,
            PageSize = pageSize,
            Total = await countTask
        };
    }
}