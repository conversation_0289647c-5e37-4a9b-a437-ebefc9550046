using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_related_list", DisableSyncStructure = true)]
	public partial class CaseRelatedList {

		[ Column(Name = "related_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string RelatedId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "app_no", StringLength = 50)]
		public string AppNo { get; set; }

		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "family_id", StringLength = 50)]
		public string FamilyId { get; set; }

		[ Column(Name = "related_case_id", StringLength = 50)]
		public string RelatedCaseId { get; set; }

		[ Column(Name = "related_type", StringLength = 50, IsNullable = false)]
		public string RelatedType { get; set; }

		[ Column(Name = "same_day_id", StringLength = 50)]
		public string SameDayId { get; set; }

		[ Column(Name = "same_submit_id", StringLength = 50)]
		public string SameSubmitId { get; set; }

	}

}
