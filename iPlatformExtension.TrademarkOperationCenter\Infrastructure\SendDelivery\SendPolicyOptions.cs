﻿namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;

/// <summary>
/// 发送策略选项
/// </summary>
/// <typeparam name="T">发送策略对应的处理者类型</typeparam>
public class SendPolicyOptions<T> where T : ISendDeliveryCommandHandler
{
    /// <summary>
    /// 发起递交命令的key
    /// </summary>
    public string StartupDeliveryMessageKey { get; set; } = string.Empty;

    /// <summary>
    /// 中止递交命令的key
    /// </summary>
    public string StopDeliveryMessageKey { get; set; } = string.Empty;

    /// <summary>
    /// 撤回递交命令的key
    /// </summary>
    public string WithdrawDeliveryMessageKey { get; set; } = string.Empty;

    /// <summary>
    /// 取消自动递交命令的key
    /// </summary>
    public string CancelDeliveryMessageKey { get; set; } = string.Empty;

    /// <summary>
    /// 策略名称
    /// </summary>
    public string PolicyName { get; set; } = default!;

    /// <summary>
    /// 处理器类型
    /// </summary>
    public Type HandlerType => typeof(T);

}