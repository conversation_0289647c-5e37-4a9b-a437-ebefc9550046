﻿using System.Threading.Channels;
using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;

namespace iPlatformExtension.Commission.Infrastructure.BackgroundServices;

internal sealed class RewardRuleChangedNotificationService(
    Channel<RefreshProcRewardDateCommand> channel, 
    ILogger<RewardRuleChangedNotificationService> logger, 
    IServiceScopeFactory serviceScopeFactory) 
    :BackgroundConsumeService<RefreshProcRewardDateCommand>(channel, logger, serviceScopeFactory);