﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface IBaseCountryRepository : 
    IBaseRepository<BasCountry, string>, 
    IRedisCacheableRepository<string, BasCountry>, 
    IScopeDependency,
    IStringKeyCacheableRepository<BasCountry>
{
    Task<BasCountry?> ICacheableRepository<string, BasCountry>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Where(country => country.CountryId == key).WithLock().FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasCountry>> ICacheableRepository<string, BasCountry>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasCountry>.GenerateKey(BasCountry value)
    {
        return value.CountryId;
    }

    string? IStringKeyCacheableRepository<BasCountry>.GetCacheTextValue(BasCountry value)
    {
        return value.CountryZhCn;
    }
}