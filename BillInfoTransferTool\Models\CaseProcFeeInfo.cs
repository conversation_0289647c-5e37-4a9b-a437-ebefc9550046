﻿using iPlatformExtension.Model.Dto;

namespace BillInfoTransferTool.Models;

public record CaseProcFeeInfo
{
    public string CaseId { get; init; } = default!;

    public string ProcId { get; init; } = default!;

    public string FeeId { get; init; } = default!;

    public string CaseTypeId { get; init; } = default!;

    public string CaseDirection { get; init; } = default!;

    public string Volume { get; init; } = default!;

    public string BillId { get; init; } = default!;

    public string ApplyNo { get; set; } = default!;

    public string? ProcNo { get; set; }

    public string CaseName { get; set; } = default!;

    public string? CaseNameEn { get; set; }

    public string CustomerCaseNo { get; set; } = default!;

    public string? TrademarkClass { get; set; }

    public string? TrademarkRegisterNo { get; set; }

    public string? PctApplyNo { get; set; }

    public DateTime? PctApplyDate { get; set; }

    public DateTime ApplicationDate { get; set; }

    public string ApplicationNo { get; set; } = default!;

    public string ProcName { get; set; } = default!;

    public string? BusinessType { get; set; }

    public string? Undertaker { get; set; }

    public string ApplyType { get; set; } = default!;

    public string? CaseSourceType { get; set; }
}