using System;
using System.Collections.Generic;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_applicant_list", DisableSyncStructure = true)]
	public partial class CaseApplicantList {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "address_cn", StringLength = 500)]
		public string AddressCn { get; set; }

		[ Column(Name = "address_id", StringLength = 50)]
		public string AddressId { get; set; }

		[ Column(Name = "applicant_id", StringLength = 400)]
        public string ApplicantId { get; set; }

        [ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		/// <summary>
		/// 案件表导航属性
		/// </summary>
		[Navigate(nameof(CaseId))]
		public CaseInfo CaseInfo { get; set; } = default!;

		[ Column(Name = "classify", StringLength = 50, IsNullable = false)]
		public string Classify { get; set; } = "A";

		[ Column(Name = "is_represent")]
		public bool IsRepresent { get; set; } = false;

		[ Column(Name = "seq")]
		public short? Seq { get; set; }

        /// <summary>
        /// 联系人详细信息
        /// </summary>
        [Navigate(nameof(ApplicantId))]
        public CusApplicant CusApplicant { get; set; } = default!;
    }

}
