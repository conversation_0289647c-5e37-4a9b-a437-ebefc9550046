﻿using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.ContentManage
{
    /// <summary>
    /// 发件阅读人消息
    /// </summary>
    /// <param name="ReaderList"></param>
    /// <param name="OperationType"></param>
    /// <param name="MailId"></param>
    public record SentReaderMessageQuery(List<MailReaderList> ReaderList, OperationTypeEnum OperationType, string MailId = null) : IRequest;

}
