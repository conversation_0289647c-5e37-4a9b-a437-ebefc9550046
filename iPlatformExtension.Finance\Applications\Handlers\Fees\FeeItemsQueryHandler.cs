﻿using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Finance.Infrastructure.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeItemsQueryHandler(IFreeSql freeSql, IMediator mediator)
    : IRequestHandler<FeeItemsQuery, IEnumerable<FeeListItemDto>>
{
    public async Task<IEnumerable<FeeListItemDto>> Handle(FeeItemsQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return await Task.FromCanceled<IEnumerable<FeeListItemDto>>(cancellationToken);

        var feeIds = request.FeeIds;
        if (!feeIds.Any())
        {
            return Array.Empty<FeeListItemDto>();
        } 
        
        var fees = await freeSql.Select<CaseFeeList>().WithLock()
                .From<CaseProcInfo, CaseInfo, CusCustomer, CaseExtendInfo, CusFollowList, CusFollowList>(
                    (feeList, procInfo, caseInfo, customer, extendInfo, customerSalesInfo, customerTrackerInfo) =>
                        feeList.LeftJoin(fee => fee.ProcId == procInfo.ProcId)
                            .LeftJoin(fee => procInfo.CaseId == caseInfo.Id)
                            .LeftJoin(fee => caseInfo.CustomerId == customer.CustomerId)
                            .LeftJoin(fee => caseInfo.Id == extendInfo.CaseId)
                            .LeftJoin(fee => caseInfo.CustomerId == customerSalesInfo.CustomerId 
                                             && caseInfo.CaseDirection == customerSalesInfo.CaseDirection
                                             && caseInfo.CaseTypeId == customerSalesInfo.CaseType
                                             && customerSalesInfo.CustomerUserType == "ay"
                                             && customerSalesInfo.IsEnabled == true)
                            .LeftJoin(fee => caseInfo.CustomerId == customerTrackerInfo.CustomerId 
                                             && caseInfo.CaseDirection == customerTrackerInfo.CaseDirection
                                             && caseInfo.CaseTypeId == customerTrackerInfo.CaseType
                                             && customerTrackerInfo.CustomerUserType == "ga"
                                             && customerTrackerInfo.IsEnabled == true))
                .Where(tables => feeIds.Contains(tables.t1.FeeId))
                .OrderByPropertyName(request.SortCondition, request.SortOrder.IsAscending())
                .ToListAsync((feeList, procInfo, caseInfo, customer, extendInfo, customerSalesInfo, customerTrackerInfo) => new FeeListItemDto()
                {
                    FeeId = feeList.FeeId,
                    Volume = caseInfo.Volume,
                    CaseStatusId = caseInfo.CaseStatusId,
                    CaseId = caseInfo.Id,
                    ProcId = procInfo.ProcId,
                    CtrlProcId = procInfo.CtrlProcId,
                    CountryId = caseInfo.CountryId,
                    CustomerInfo = new CustomerInfo(customer.CustomerId, customer.CustomerFullName, customer.CustomerNameEn),
                    CustomerName = new KeyValuePair<string, string>(customer.CustomerId, customer.CustomerFullName),
                    FeeNameId = feeList.FeeTypeNameId,
                    FeeClass = new KeyValuePair<string, string>(SystemDictionaryName.FeeType.ToSqlStringConstant(), feeList.FeeClass),
                    Currency = feeList.CurrencyId,
                    Amount = feeList.Amount!.Value,
                    AppNo = caseInfo.AppNo,
                    EntryDate = extendInfo.EntryDate,
                    ChargeRule = new KeyValuePair<string, string>(SystemDictionaryName.ChargeRule.ToSqlStringConstant(), feeList.ReceiveRule ?? string.Empty),
                    PayWay = new KeyValuePair<string, string>(SystemDictionaryName.PayWay.ToSqlStringConstant(), feeList.PayWay ?? string.Empty),
                    PayStatus = new KeyValuePair<string, string>(SystemDictionaryName.PayStatus.ToSqlStringConstant(), feeList.PayStatus),
                    ReceiverStatus = new KeyValuePair<string, string>(SystemDictionaryName.ReceiveStatus.ToSqlStringConstant(), feeList.ReceiveStatus),
                    OfficerStatus = new KeyValuePair<string, string>(SystemDictionaryName.OfficerStatus.ToSqlStringConstant(), feeList.OfficerStatus),
                    PayOfficerLegalDate = feeList.PayOfficerLegalDate,
                    CreationTime = feeList.CreateTime!.Value,
                    ApplyChannel = new KeyValuePair<string, string>(SystemDictionaryName.TrademarkApplyChannel.ToSqlStringConstant(), caseInfo.ApplyChannel ?? string.Empty),
                    CaseType = new KeyValuePair<string, string>(SystemDictionaryName.CaseType.ToSqlStringConstant(), caseInfo.CaseTypeId),
                    PaymentName = new KeyValuePair<string, string>(SystemDictionaryName.PaymentName.ToSqlStringConstant(), feeList.PaymentName ?? string.Empty),
                    CaseDirection = new KeyValuePair<string, string>(SystemDictionaryName.CaseDirection.ToSqlStringConstant(), caseInfo.CaseDirection),
                    Undertaker = (caseInfo.CaseTypeId == CaseType.Trade ? procInfo.ProcUndertakeMainUserId : caseInfo.UndertakeMainUserId) ?? string.Empty,
                    ProcUndertaker = procInfo.UndertakeUserId ?? string.Empty,
                    ProcNo = procInfo.ProcNo,
                    CaseName = caseInfo.CaseName,
                    CaseNameEn = caseInfo.CaseNameEn,
                    CustomerCaseNo = caseInfo.CustomerCaseNo,
                    AllocateSales = feeList.SalesUserId,
                    CurrentSales = customerSalesInfo.TrackUser ?? string.Empty,
                    AllocateTracker = feeList.TrackUserId,
                    CurrentTracker = customerTrackerInfo.TrackUser ?? string.Empty,
                    CaseSourceType = new KeyValuePair<string, string>(SystemDictionaryName.HeadUserType.ToSqlStringConstant(), customerSalesInfo.HeadUserType ?? string.Empty),
                    FeesCaseSourceType = new KeyValuePair<string, string>(SystemDictionaryName.HeadUserType.ToSqlStringConstant(), feeList.HeadUserType ?? string.Empty),
                    ReceiveDate = feeList.ReceiveDate,
                    ApplyType = new KeyValuePair<string, string>(caseInfo.ApplyTypeId, string.Empty),
                    CaseBusinessTypeId = caseInfo.BusinessTypeId,
                    ProcBusinessTypeId = procInfo.BusTypeId,
                    // BusinessType = new KeyValuePair<string, string>(CaseType.IsBelongPatent(caseInfo.CaseTypeId) ? caseInfo.BusinessTypeId : procInfo.BusTypeId, string.Empty),
                    IsSameDayCase = caseInfo.IsSameDay ?? false,
                    AllocateBusinessUser = customer.BusiUserId,
                    CurrentBusinessUser = customer.BusiUserId ?? string.Empty,
                    AllocateClueUser = customer.ClueUserId,
                    CurrentClueUser = customer.ClueUserId ?? string.Empty,
                    TrademarkClass = caseInfo.TrademarkClass,
                    PctApplyDate = extendInfo.PctAppDate,
                    PctApplyNo = extendInfo.PctAppNo,
                    TrademarkRegisterNo = caseInfo.RegisterNo,
                    ApplicationDate = caseInfo.AppDate,
                    Remark = feeList.Remark,
                    PreRequestDate = feeList.PreRequestDate,
                    ReceiveDueDate = feeList.ReceiveDueDate,
                    PayOfficerDate = feeList.PayOfficerDate,
                    BillNo = feeList.BillNo,
                    ProcStatusId = procInfo.ProcStatusId,
                    CaseRemark = caseInfo.Remark,
                    TitularWriterId = procInfo.TitularWriteUser,
                    DeliverDate = caseInfo.DeliverDate,
                    SubProcStatusCode = procInfo.SubProcStatusId,
                    ManageCompanyId = caseInfo.ManageCompany,
                    BelongCompanyId = caseInfo.BelongCompany,
                    CustomerCountryId = customer.CountryId,
                    CreatorId = feeList.CreateUserId,
                    EntrustDate = caseInfo.CaseTypeId == CaseType.Trade ? procInfo.EntrustDate : caseInfo.EntrustDate,
                    FinishDate = procInfo.FinishDate,
                    UndertakeDepartmentId = caseInfo.UndertakeDeptId,
                    IsFirstPayAnnual = feeList.IsFirstPayAnnual,
                    RequestDate = feeList.RequestDate,
                    InvoiceNo = feeList.InvoiceNo,
                    CaseSalesId = caseInfo.SalesUserId,
                    DeliveryType = SqlExt.Case().When(procInfo.FilingType == "0", "纸交").When(procInfo.FilingType == "1", "网交").End(),
                    OfficialFeeMarkDescription = feeList.OfficialFeeMarkDescription,
                    OfficialPaymentPublicationDate = feeList.OfficialPaymentPublicationDate,
                    DeliveryKey = new KeyValuePair<string, string>(SystemDictionaryName.DeliveryKey.ToSqlStringConstant(), procInfo.DeliveryKey),
                    SendOfficeDate = procInfo.SendOfficialDate,
                    TrademarkApplicationType = new KeyValuePair<string, string>(SystemDictionaryName.ApplicationType.ToSqlStringConstant(), caseInfo.MultiType),
                    OfficialFeeMark = feeList.OfficialFeeMark == OfficialFeeMark.ToBeConfirmed ? "待确认" : null,
                    NotPaymentInstructions = feeList.NotPaymentInstructions,
                    PaymentAgency = feeList.PaymentAgency
                }, cancellationToken);

        fees = fees.DistinctBy(dto => dto.FeeId).ToList();

        await mediator.Send(new FeesResultQuery(fees), cancellationToken);

        return fees;
    }
}