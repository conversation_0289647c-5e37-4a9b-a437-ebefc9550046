﻿using System.Linq.Expressions;
using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Reflection;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions
{
    public static class SelectExtension
    {
        private readonly static Dictionary<string, string> Sorts = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            {"TrademarkClassDE","CaseProcInfo.DeliInfo.OtherInfo.TrademarkNiceClasses" },
            {"TrademarkClass","CaseProcInfo.CaseInfo.TrademarkClass" },
            {"CaseNameDE","CaseProcInfo.CaseInfo.CaseName" },
            {"CaseName","CaseProcInfo.CaseInfo.CaseName" },
            {"CaseNameEX","CaseProcFlow.CaseProcInfo.CaseInfo.CaseName" },
            {"CtrlProcName","CaseProcInfo.BasCtrlProc.CtrlProcZhCn" },
            {"CtrlProcNameDE","CaseProcInfo.BasCtrlProc.CtrlProcZhCn" },
            {"CtrlProcNameEX","CaseProcFlow.CaseProcInfo.BasCtrlProc.CtrlProcZhCn" },
            {"Volume","CaseProcFlow.CaseProcInfo.CaseInfo.Volume" },
            {"TrademarkClassEX","CaseProcFlow.CaseProcInfo.CaseInfo.TrademarkClass" },
            {"AppNo","CaseProcInfo.CaseInfo.AppNo" },
            {"AppNoEX","CaseProcFlow.CaseProcInfo.CaseInfo.AppNo" },
            {"UndertakeUserName","CaseProcInfo.UndertakeUserInfo.CnName" },
            {"UndertakeUserNameEX","CaseProcFlow.CaseProcInfo.UndertakeUserInfo.CnName" },
            {"LegalDueDate","CaseProcInfo.LegalDueDate" },
            {"LegalDueDateEX","CaseProcFlow.CaseProcInfo.LegalDueDate" },
            {"SubProcStatus","CaseProcInfo.SubProcStatus.TextZhCn" },
            {"SubProcStatusEX","CaseProcFlow.CaseProcInfo.SubProcStatus.TextZhCn" },
            {"RemainingDays","CaseProcInfo.ReturnDocDate" },
            {"RemainingDaysEX","CaseProcFlow.CaseProcInfo.ReturnDocDate" },
            {"ApplicantName","CaseProcInfo.CaseInfo.ApplicantId" },
            {"ReturnDocDate","CaseProcInfo.ReturnDocDate" },
            {"ReturnDocDateEX","CaseProcFlow.CaseProcInfo.ReturnDocDate" },

    };

        /// <summary>
        /// 数据库排序
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="select"></param>
        /// <param name="PropertyName"></param>
        /// <param name="IsAscending"></param>
        /// <param name="FlowType"></param>
        /// <returns></returns>
        public static ISelect<T> OrderByCusPropertyName<T>(this ISelect<T> select, string PropertyName, bool IsAscending = false, string FlowType = "")
        {
            var key = $"{PropertyName}{FlowType}";
            if (Sorts.ContainsKey(key))
            {
                return select.OrderByPropertyName(Sorts[key], IsAscending);
            }
            else if (Sorts.ContainsKey(PropertyName))
            {
                return select.OrderByPropertyName(Sorts[PropertyName], IsAscending);
            }
            return select.OrderByPropertyName(PropertyName, IsAscending);
        }

        /// <summary>
        /// 内存排序(数据量过大不要使用)
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <param name="PropertyName"></param>
        /// <returns></returns>
        public static IEnumerable<T> CustomSort<T>(this IEnumerable<T> list, string PropertyName, bool IsAscending = false)
        {
            if (string.IsNullOrEmpty(PropertyName))
            {
                return list;
            }
            // var property = typeof(T).GetProperty(PropertyName);
            PropertyInfo property = typeof(T).GetProperties()
    .FirstOrDefault(p => p.Name.Equals(PropertyName, StringComparison.OrdinalIgnoreCase));
            if (property != null)
            {
                var parameter = Expression.Parameter(typeof(T), "x");
                var propertyExpression = Expression.Property(parameter, PropertyName);
                var lambda = Expression.Lambda(propertyExpression, parameter);

                var orderMethod = IsAscending
                    ? typeof(Enumerable).GetMethods()
                        .First(m => m.Name == "OrderBy" && m.GetParameters().Length == 2)
                        .MakeGenericMethod(typeof(T), property.PropertyType)
                    : typeof(Enumerable).GetMethods()
                        .First(m => m.Name == "OrderByDescending" && m.GetParameters().Length == 2)
                        .MakeGenericMethod(typeof(T), property.PropertyType);

                list = (IEnumerable<T>)orderMethod.Invoke(null, [list, lambda.Compile()]);
            }
            return list;
        }
    }
}
