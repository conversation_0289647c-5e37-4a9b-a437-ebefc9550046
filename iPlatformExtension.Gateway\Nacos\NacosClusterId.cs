﻿
using System.Runtime.CompilerServices;
using Nacos.V2.Common;

namespace iPlatformExtension.Gateway.Nacos;

public readonly struct NacosClusterId(string serviceName, string groupName = "")
{
    private const string Separator = "@@";
    
    public string ServiceName => serviceName;

    public string GroupName => string.IsNullOrWhiteSpace(groupName) ? Constants.DEFAULT_GROUP : groupName;

    public static implicit operator string(NacosClusterId clusterId) => $"{clusterId.GroupName}{Separator}{clusterId.ServiceName}";

    public static implicit operator NacosClusterId(string clusterId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(clusterId);
        var entries = clusterId.Split(Separator, StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries);

        var serviceName = string.Empty;
        var groupName = Constants.DEFAULT_GROUP;

        switch (entries.Length)
        {
            case 1:
                serviceName = entries[0];
                    break;
            case > 1:
                serviceName = entries[1];
                groupName = entries[0];
                break;
            default:
                ArgumentOutOfRangeException.ThrowIfLessThan(entries.Length, 1);
                break;
        }

        return new NacosClusterId(serviceName, groupName);
    }

    public void Deconstruct(out string service, out string group)
    {
        service = ServiceName;
        group = GroupName;
    }

    public override string ToString()
    {
        return $"{groupName}{Separator}{serviceName}";
    }
}