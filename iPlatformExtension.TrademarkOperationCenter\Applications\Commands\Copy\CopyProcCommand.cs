﻿using MediatR;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Copy;

/// <summary>
/// 复制任务命令
/// </summary>
/// <param name="SourceId">源任务id</param>
/// <param name="TargetId">目标任务id</param>
/// <param name="CopyFieldList">复制项</param>
public sealed record CopyProcCommand(
    [Required(ErrorMessage = "请添加源任务id")] string SourceId, 
    [Required(ErrorMessage = "请先添加【目标任务】")] string[] TargetId,
    [Required(ErrorMessage = "请先勾选【复制内容】")] string[] CopyFieldList,
    bool OnlyChild = false) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

