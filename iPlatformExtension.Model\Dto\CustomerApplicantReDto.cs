﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto
{
    /// <summary>
    /// 申请人列表返回model
    /// </summary>
    public class CustomerApplicantReDto
    {
        public string ApplicantId { get; set; }


        public string TypeId { get; set; }


        public string ApplicantNameCn { get; set; }


        public string ApplicantNameEn { get; set; }


        public string RegisterCode { get; set; }


        public string Tel { get; set; }


        public string Mobile { get; set; }


        public string Fax { get; set; }


        public string CountryId { get; set; }


        public string AddressCn { get; set; }


        public string AddressEn { get; set; }

        public string CardType { get; set; }

        public string CardNo { get; set; }

        public string Remark { get; set; }


        public bool IsEnabled { get; set; }

        public string FeeReduceYear { get; set; }


        public string CustomerId { get; set; }


        public string CustomerName { get; set; }


        public string ApplicantShowName { get; set; }

    }
}
