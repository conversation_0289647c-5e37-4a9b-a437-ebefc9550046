using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "express_my_contact", DisableSyncStructure = true)]
	public partial class ExpressMyContact {

		[ Column(Name = "contact_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ContactId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 寄件人详细地址
		/// </summary>
		[ Column(Name = "address_detail", StringLength = 400)]
		public string AddressDetail { get; set; }

		/// <summary>
		/// 寄件人县/区
		/// </summary>
		[ Column(Name = "area", StringLength = 50)]
		public string Area { get; set; }

		/// <summary>
		/// 寄件人市
		/// </summary>
		[ Column(Name = "city", StringLength = 50)]
		public string City { get; set; }

		[ Column(Name = "company", StringLength = 100)]
		public string Company { get; set; }

		/// <summary>
		/// 联系人类型(寄件 收件联系人)
		/// </summary>
		[ Column(Name = "contact_class", StringLength = 50)]
		public string ContactClass { get; set; }

		/// <summary>
		/// 寄件人国家
		/// </summary>
		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户ID
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "email", StringLength = 50)]
		public string Email { get; set; }

		/// <summary>
		/// 寄件地址
		/// </summary>
		[ Column(Name = "full_address", StringLength = 500)]
		public string FullAddress { get; set; }

		[ Column(Name = "is_default")]
		public bool? IsDefault { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		/// <summary>
		/// 是否共用
		/// </summary>
		[ Column(Name = "is_public")]
		public bool? IsPublic { get; set; }

		/// <summary>
		/// 寄件人电话
		/// </summary>
		[ Column(Name = "mobile", StringLength = 50)]
		public string Mobile { get; set; }

		[ Column(Name = "name", StringLength = 50)]
		public string Name { get; set; }

		[ Column(Name = "postcode", StringLength = 50)]
		public string Postcode { get; set; }

		/// <summary>
		/// 寄件人省份
		/// </summary>
		[ Column(Name = "province", StringLength = 50)]
		public string Province { get; set; }

		/// <summary>
		/// 寄件人电话
		/// </summary>
		[ Column(Name = "tel", StringLength = 50)]
		public string Tel { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户ID
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
