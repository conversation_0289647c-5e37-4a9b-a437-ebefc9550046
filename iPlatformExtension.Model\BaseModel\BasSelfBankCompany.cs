using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_self_bank_company", DisableSyncStructure = true)]
	public partial class BasSelfBankCompany {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "bank_id", StringLength = 50)]
		public string BankId { get; set; }

		[ Column(Name = "company_id", StringLength = 50)]
		public string CompanyId { get; set; }

	}

}
