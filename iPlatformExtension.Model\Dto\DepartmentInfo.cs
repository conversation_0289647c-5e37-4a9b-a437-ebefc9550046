﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 部门信息
/// </summary>
public record DepartmentInfo : INameInfo
{
    /// <summary>
    /// 部门id
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 部门名称
    /// </summary>
    public string CnName { get; set; } = default!;
    
    /// <summary>
    /// 没有英文名
    /// </summary>
    public string? EnName { get; set; }
    
    /// <summary>
    /// 部门编码
    /// </summary>
    public string DeptCode { get; set; } = default!;

    /// <summary>
    /// 部门全称
    /// </summary>
    public string FullName { get; set; } = default!;

    /// <summary>
    /// 分区编码
    /// </summary>
    public string? DistrictCode { get; set; }
}