using iPlatformExtension.Commission.Application.Notifications.Trademark.Domestic;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class DomesticCommissionUserInfoResultHandler(
    IUserInfoRepository userInfoRepository,
    ICompanyRepository companyRepository,
    IDepartmentInfoRepository departmentInfoRepository,
    IDistrictRepository districtRepository) 
    : INotificationHandler<DomesticCommissionResultNotification>, IRequestHandler<DomesticCommissionResultNotification>
{
    public async Task Handle(DomesticCommissionResultNotification notification, CancellationToken cancellationToken)
    {
        var commission = notification.Commission;
        ICacheableRepository<string, SysUserInfo> userCacheableRepository = userInfoRepository;
        var userInfo =
            await userCacheableRepository.GetCacheValueAsync(commission.ProcMainUndertakerId, cancellationToken:cancellationToken);

        if (userInfo is null)
        {
            return;
        }
        
        commission.CnName = userInfo.CnName;
        commission.UserName = userInfo.UserName;
        commission.DeptId = userInfo.DeptId;

        var companyId = userInfo.ManageCompany;
        var company = await companyRepository.GetCacheValueAsync(companyId ?? string.Empty, cancellationToken:cancellationToken);
        if (company is not null)
        {
            commission.DistrictCode = company.DistrictId ?? string.Empty;
            commission.DistrictName =
                await districtRepository.GetChineseValueAsync(commission.DistrictCode) ?? string.Empty;
        }
        
        
        var departmentInfo = await departmentInfoRepository.GetCacheValueAsync(commission.DeptId, cancellationToken:cancellationToken);
        if (departmentInfo is null)
        {
            return;
        }

        commission.DeptName = departmentInfo.FullName;
    }
}