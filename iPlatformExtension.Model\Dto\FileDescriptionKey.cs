﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 文件描述key 
/// </summary>
/// <param name="FileType">文件类型</param>
/// <param name="FileDescription">文件描述</param>
public record struct FileDescriptionKey(string FileType, string FileDescription);

/// <summary>
/// 文件描述key比较器
/// </summary>
public sealed class FileDescriptionKeyEqualityComparer : IEqualityComparer<FileDescriptionKey>
{
    /// <summary>
    /// 默认的比较器
    /// </summary>
    public static readonly FileDescriptionKeyEqualityComparer
        DefaultComparer = new ();
    
    /// <summary>
    /// 判断两个key是否相等
    /// </summary>
    /// <param name="x">key1</param>
    /// <param name="y">key2</param>
    /// <returns></returns>
    public bool Equals(FileDescriptionKey x, FileDescriptionKey y)
    {
        return x.FileType == y.FileType && x.FileDescription == y.FileDescription;
    }

    /// <summary>
    /// 获取一个<see cref="FileDescriptionKey"/>的哈希值
    /// </summary>
    /// <param name="obj"><see cref="FileDescriptionKey"/></param>
    /// <returns>哈希值</returns>
    public int GetHashCode(FileDescriptionKey obj)
    {
        return HashCode.Combine(obj.FileDescription, obj.FileType);
    }
}

