using System.ComponentModel;
using System.Reflection;
using iPlatformExtension.Common.Helper;
using iPlatformExtension.Common.TypeInfo;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Options;
using Microsoft.Net.Http.Headers;

namespace iPlatformExtension.Common.ModelBinders;

public sealed class LargeFileFormDataModelBinder(
    IOptionsMonitor<FormOptions> options,
    EntityTypeInfoProvider typeInfoProvider)
    : IModelBinder
{
    public async Task BindModelAsync(ModelBindingContext bindingContext)
    {
        var httpContext = bindingContext.HttpContext;
        var request = httpContext.Request;
        var cancellationToken = httpContext.RequestAborted;
        if (!MultipartRequestHelper.IsMultipartContentType(request.ContentType))
        {
            throw new UnsupportedContentTypeException(
                $"{request.ContentType} is unsupported, but only multipart/form-data");
        }

        var formAccumulator = new KeyValueAccumulator();
        var files = new FormFileCollection();
        
        var formOptions = options.CurrentValue;
        var boundary = MultipartRequestHelper.GetBoundary(MediaTypeHeaderValue.Parse(request.ContentType),
            formOptions.MultipartBoundaryLengthLimit);
        var reader = new MultipartReader(boundary, request.Body, formOptions.MemoryBufferThreshold);
        
        var modelName = bindingContext.IsTopLevelObject
            ? bindingContext.BinderModelName ?? bindingContext.FieldName
            : bindingContext.ModelName;

        var sectionCount = 1;
        for (var section = await reader.ReadNextSectionAsync(cancellationToken); 
             section is not null; 
             section = await reader.ReadNextSectionAsync(cancellationToken), sectionCount++)
        {
            if (sectionCount > formOptions.ValueCountLimit)
            {
                bindingContext.ModelState.AddModelError(modelName, "the field's count is out of the limit");
                return;
            }

            if (!ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition))
            {
                throw new ArgumentException("Form section has invalid Content-Disposition value: ",
                    section.ContentDisposition);
            }
            
            if (contentDisposition.IsFormDisposition())
            {
                var formDataSection = new FormMultipartSection(section, contentDisposition);
 
                // Content-Disposition: form-data; name="key"
                //
                // value
 
                // Do not limit the key name length here because the multipart headers length limit is already in effect.
                var key = formDataSection.Name;
                var value = await formDataSection.GetValueAsync(cancellationToken);
 
                formAccumulator.Append(key, value);
            }
            else if (contentDisposition.IsFileDisposition())
            {
                var fileSection = new FileMultipartSection(section, contentDisposition);

                await section.Body.DrainAsync(cancellationToken);

                FormFile formFile;
                if (section.BaseStreamOffset.HasValue)
                {
                    formFile = new FormFile(request.Body, section.BaseStreamOffset.Value, section.Body.Length,
                        fileSection.Name, fileSection.FileName);
                }
                else
                {
                    formFile = new FormFile(section.Body, 0, section.Body.Length, fileSection.Name,
                        fileSection.FileName);
                }

                formFile.Headers = new HeaderDictionary(section.Headers);
                files.Add(formFile);
            }
        }
        
        if (request.Body.CanSeek)
        {
            request.Body.Seek(0, SeekOrigin.Begin);
        }

        var formFields = new FormCollection(formAccumulator.GetResults(), files);
        var modelTypeInfo = typeInfoProvider.Get(bindingContext.ModelType);
        object? model;

        if (modelTypeInfo.HasNoArgumentsConstructor)
        {
            model = modelTypeInfo.CreateInstance();
            if (model is null)
            {
                throw new InvalidOperationException($"can not create instance of type: {bindingContext.ModelType.FullName}");
            }
            
            foreach (var entityPropertyInfo in modelTypeInfo.EntityPropertyInfos)
            {
                if (entityPropertyInfo.Set is null)
                {
                    continue;
                }
                
                var modelBindPropertyName = entityPropertyInfo.ModelBindPropertyName ?? entityPropertyInfo.PropertyName;
                if (entityPropertyInfo.PropertyType.IsAssignableFrom(typeof(IFormFile)))
                {
                    var file = files[modelBindPropertyName];
                    if (file is not null)
                    {
                        entityPropertyInfo.Set(model, file);
                    }
                }
                else if (entityPropertyInfo.PropertyType.IsAssignableFrom(typeof(IEnumerable<IFormFile>)))
                {
                    entityPropertyInfo.Set(model, files);
                }
                else
                {
                    var value = formFields[modelBindPropertyName];
                    var converter = TypeDescriptor.GetConverter(entityPropertyInfo.PropertyType);
                    entityPropertyInfo.Set(model, converter.ConvertFrom(value.ToString()));
                }
            }
        }
        else
        {
            var constructors = modelTypeInfo.TypeInfo.DeclaredConstructors;
            if (constructors.Count() > 1)
            {
                throw new InvalidOperationException("the count of constructor is out of the limit");
            }

            var constructor = constructors.First();
            var parameterInfos = constructor.GetParameters();
            var parameters = new object?[parameterInfos.Length];

            for (var i = 0; i < parameterInfos.Length; i++)
            {
                var parameterInfo = parameterInfos[i];
                var bindAttribute = parameterInfo.GetCustomAttribute<BindAttribute>();
                var modelBindParameterName = bindAttribute?.Include.FirstOrDefault() ?? parameterInfo.Name ??
                    throw new InvalidOperationException("the parameter name is null of the constructor");
                if (parameterInfo.ParameterType.IsAssignableFrom(typeof(IFormFile)))
                {
                    parameters[i] = files[modelBindParameterName];
                }
                else if (parameterInfo.ParameterType.IsAssignableFrom(typeof(IEnumerable<IFormFile>)))
                {
                    parameters[i] = files;
                }
                else
                {
                    var typeConverter = TypeDescriptor.GetConverter(parameterInfo.ParameterType);
                    parameters[i] = typeConverter.ConvertFrom(formFields[modelBindParameterName].ToString());
                }
            }

            model = modelTypeInfo.CreateInstance(args: parameters);
        }

        bindingContext.Model = model;
        bindingContext.Result = ModelBindingResult.Success(model);
    }
}