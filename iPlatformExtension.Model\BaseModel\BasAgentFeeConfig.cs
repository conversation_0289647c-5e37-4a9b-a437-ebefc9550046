using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_agent_fee_config", DisableSyncStructure = true)]
	public partial class BasAgentFeeConfig {

		[ Column(Name = "config_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ConfigId { get; set; }

		[ Column(Name = "agent_fee_id", StringLength = 50)]
		public string AgentFeeId { get; set; }

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "base_type", StringLength = 50)]
		public string BaseType { get; set; }

		[ Column(Name = "case_sub_property", StringLength = 50)]
		public string CaseSubProperty { get; set; }

		[ Column(Name = "config_type", StringLength = 50)]
		public string ConfigType { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "date_day")]
		public int? DateDay { get; set; }

		[ Column(Name = "date_month")]
		public int? DateMonth { get; set; }

		[ Column(Name = "date_type", StringLength = 50)]
		public string DateType { get; set; }

		[ Column(Name = "date_year")]
		public int DateYear { get; set; } = 0;

		[ Column(Name = "dest_ctrl_proc_id", StringLength = 50)]
		public string DestCtrlProcId { get; set; }

		[ Column(Name = "fee_name_id", StringLength = 50)]
		public string FeeNameId { get; set; }

		[ Column(Name = "is_auto")]
		public bool IsAuto { get; set; } = true;

		[ Column(Name = "is_item_calc")]
		public bool IsItemCalc { get; set; } = true;

		[ Column(Name = "is_repeat")]
		public bool IsRepeat { get; set; } = true;

		[ Column(Name = "item_end", StringLength = 50)]
		public string ItemEnd { get; set; }

		[ Column(Name = "item_start", StringLength = 50)]
		public string ItemStart { get; set; }

		[ Column(Name = "item_type", StringLength = 50)]
		public string ItemType { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
