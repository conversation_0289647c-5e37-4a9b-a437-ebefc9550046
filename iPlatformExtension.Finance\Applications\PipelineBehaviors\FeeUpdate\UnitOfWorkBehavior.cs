﻿using FreeSql;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.PipelineBehaviors.FeeUpdate;

internal sealed class UnitOfWorkBehavior<TRequest>(UnitOfWorkManager unitOfWorkManager)
    : IPipelineBehavior<TRequest, IEnumerable<FeesUpdateResult>>
    where TRequest : IFeesUpdateCommand
{
    public async Task<IEnumerable<FeesUpdateResult>> Handle(TRequest request, RequestHandlerDelegate<IEnumerable<FeesUpdateResult>> next, CancellationToken cancellationToken)
    {
        IEnumerable<FeesUpdateResult> response = Array.Empty<FeesUpdateResult>();
        if (request is not IFeesUpdateCommand command) return response;
        
        var unitOfWork = unitOfWorkManager.Begin();
        try
        {
            response = await next();
            unitOfWork.Commit();
        }
        catch (Exception)
        {
            unitOfWork.Rollback();
            throw;
        }
        finally
        {
            unitOfWork.Dispose();
        }

        return response;
    }
}