﻿using System.ComponentModel;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// Represents a sorting condition, including the sorting field and order.
/// </summary>
/// <param name="Field">The name of the field to sort by.</param>
/// <param name="Order">The sort order, which can be ascending or descending.</param>
public record SortCondition(
    [property:Description("排序字段")] string Field, 
    [property:Description("排序方式")] SortOrder Order);
