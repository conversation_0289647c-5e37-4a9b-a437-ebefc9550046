﻿using Microsoft.Extensions.Primitives;

namespace iPlatformExtension.Gateway.Nacos;

internal sealed class NacosNamingProxyReloadToken : IChangeToken
{
    private CancellationTokenSource _tokenSource = new();
    
    public IDisposable RegisterChangeCallback(Action<object?> callback, object? state)
    {
        return _tokenSource.Token.Register(callback, state);
    }

    public bool ActiveChangeCallbacks => true;

    public bool HasChanged => _tokenSource.IsCancellationRequested;

    public void OnReload() => _tokenSource.Cancel();
    
    internal IChangeToken Refresh()
    {
        if (!_tokenSource.IsCancellationRequested) return this;
        Interlocked.Exchange(ref _tokenSource, new CancellationTokenSource());
        return this;
    }
}