﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 团队成员查询
    /// </summary>
    public class TeamMemberQueryHandler : IRequestHandler<TeamMemberQuery, IEnumerable<TeamMemberDto>>
    {
        private readonly IFreeSql _freeSql;

        public TeamMemberQueryHandler(IFreeSql freeSql)
        {
            _freeSql = freeSql;
        }

        public async Task<IEnumerable<TeamMemberDto>> Handle(TeamMemberQuery request, CancellationToken cancellationToken)
        {
            var dbSelect = _freeSql.Select<SysTeamAssociateMember, SysUserInfo, SysDeptInfo, SysRoleInfo>()
                .LeftJoin((x, u, d, r) => x.UserId == u.UserId)
                .LeftJoin((x, u, d, r) => u.DeptId == d.DeptId)
                .LeftJoin((x, u, d, r) => x.RoleId == r.RoleId)
                .WhereIf(request.Name != null, (x, y, d, r) =>
                    r.RoleType == RoleType.TrademarkOperation.GetHashCode() && (x.UserName.Contains(request.Name) || x.UserName.Contains(request.Name)))
                .Where((x, u, d, r) => x.TeamId == request.TeamId);
            if (request.PageIndex is not null && request.PageSize is not null)
            {
                dbSelect = dbSelect.Page(request.PageIndex.Value, request.PageSize.Value).Count(out var totalCount).OrderBy(it => it.t1.TeamMemberId);
                var result = await dbSelect.WithLock().ToListAsync(it => new TeamMemberDto(it.t1.TeamMemberId, it.t1.UserId, it.t1.UserName, it.t1.RoleId, it.t4.RoleName, it.t3.DeptName),cancellationToken);
                return new PageResult<TeamMemberDto>()
                {
                    Data = result,
                    Page = request.PageIndex.Value,
                    PageSize = request.PageSize.Value,
                    Total = totalCount
                };
            }
            return await dbSelect.WithLock().ToListAsync(it => new TeamMemberDto(it.t1.TeamMemberId, it.t1.UserId, it.t1.UserName, it.t1.RoleId, it.t4.RoleName, it.t3.DeptName), cancellationToken);
        }
    }
}

