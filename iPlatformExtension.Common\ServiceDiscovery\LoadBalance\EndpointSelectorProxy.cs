using System.Reflection;
using Microsoft.Extensions.ServiceDiscovery;

namespace iPlatformExtension.Common.ServiceDiscovery.LoadBalance;

internal class EndpointSelectorProxy<TSelector> : DispatchProxy where TSelector : IServiceEndpointSelector
{
    private const string AssemblyFileName = "Microsoft.Extensions.ServiceDiscovery.dll";

    private const string InterfaceTypeName =
        "Microsoft.Extensions.ServiceDiscovery.LoadBalancing.IServiceEndpointSelector";

    internal static readonly Type EndpointInterfaceType = Assembly.LoadFrom(AssemblyFileName).GetType(InterfaceTypeName) 
                                                        ?? throw new TypeLoadException($"无法加载类型【{InterfaceTypeName}】");
    
    internal TSelector? Instance { get; set; }
    
    /// <inheritdoc />
    protected override object? Invoke(MethodInfo? targetMethod, object?[]? args)
    {
        if (Instance is null)
        {
            throw new NullReferenceException("代理没有设置对应对象实例");
        }

        var methodName = targetMethod?.Name;

        switch (methodName)
        {
            case nameof(IServiceEndpointSelector.SetEndpoints):
            {
                var serviceEndpointSource = args![0] as ServiceEndpointSource;
                Instance.SetEndpoints(serviceEndpointSource!);
                return null;
            }
            case nameof(IServiceEndpointSelector.GetEndpoint):
            {
                var context = args![0];
                return Instance.GetEndpoint(context);
            }
            
            default: return null;
        }
    }
}