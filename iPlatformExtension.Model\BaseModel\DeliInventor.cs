using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_inventor", DisableSyncStructure = true)]
	public partial class DeliInventor {

		[ Column(Name = "address_cn", StringLength = 200)]
		public string AddressCn { get; set; }

		[ Column(Name = "address_en", StringLength = 500)]
		public string AddressEn { get; set; }

		[ Column(Name = "card_no", StringLength = 50)]
		public string CardNo { get; set; }

		[ Column(Name = "card_type", StringLength = 50)]
		public string CardType { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "inventor_id", StringLength = 50)]
		public string InventorId { get; set; }

		[ Column(Name = "inventor_name_cn", StringLength = 200)]
		public string InventorNameCn { get; set; }

		[ Column(Name = "inventor_name_en", StringLength = 200)]
		public string InventorNameEn { get; set; }

		[ Column(Name = "is_represent")]
		public bool? IsRepresent { get; set; } = false;

		[ Column(Name = "is_unpub")]
		public bool? IsUnpub { get; set; }

		[ Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
		public string ProcId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
