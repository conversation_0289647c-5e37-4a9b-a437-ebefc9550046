﻿namespace iPlatformExtension.Outsourcing.Application.Models.Proc;

/// <summary>
/// 任务更新DTO
/// </summary>
public class ProcPatchDto
{
    /// <summary>
    /// 供应商id
    /// </summary>
    public string? SupplierId { get; set; }

    /// <summary>
    /// 外所文号
    /// </summary>
    public string ForeignNumber { get; set; } = string.Empty;

    /// <summary>
    /// 选所备注
    /// </summary>
    public string ForeignSupplierRemark { get; set; } = string.Empty;

    /// <summary>
    /// 联系人id集合
    /// </summary>
    public ISet<string>? ContactorIds { get; set; }

    /// <summary>
    /// 费项集合id
    /// </summary>
    public string? FeesId { get; set; }
}