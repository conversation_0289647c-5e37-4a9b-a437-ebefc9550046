﻿using System.Collections.Frozen;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.File;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File;

internal sealed class TrademarkLicenseFilingProcApplicantCaseFileDescriptionQueryHandler(
    IFileDescriptionRepository fileDescriptionRepository) : 
    IMatchTrademarkDeliveryBusinessTypeNotificationHandler<ProcApplicantCaseFileDescriptionQuery>
{
    private static readonly IEnumerable<string> matchBusinessTypes = ["0"];
    
    private static readonly FrozenDictionary<string, FileDescriptionKey> descriptionKeys = new Dictionary<string, FileDescriptionKey>
    {
        {"5", new FileDescriptionKey("递交官方", "被许可人营业执照(中文)")},
        {"8", new FileDescriptionKey("递交官方", "被许可人营业执照(外文)")},
        {"6", new FileDescriptionKey("递交官方", "被许可人身份证明(中文)")},
        {"9", new FileDescriptionKey("递交官方", "被许可人身份证明(外文)")},
    }.ToFrozenDictionary();

    IEnumerable<string> IMatchTrademarkDeliveryBusinessTypeNotificationHandler<ProcApplicantCaseFileDescriptionQuery>.MatchBusinessTypes => matchBusinessTypes;

    IReadOnlyDictionary<string, FileDescriptionKey> IMatchTrademarkDeliveryBusinessTypeNotificationHandler<ProcApplicantCaseFileDescriptionQuery>.DescriptionKeys => descriptionKeys;

    IFileDescriptionRepository IMatchTrademarkDeliveryBusinessTypeNotificationHandler<ProcApplicantCaseFileDescriptionQuery>.FileDescriptionRepository => fileDescriptionRepository;
}