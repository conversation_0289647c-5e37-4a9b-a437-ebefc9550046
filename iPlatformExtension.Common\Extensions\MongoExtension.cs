﻿using iPlatformExtension.Common.Db.Mongo;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Driver;

namespace iPlatformExtension.Common.Extensions;

public static class MongoExtension
{

    public static IServiceCollection AddMongoDbContext<T>(this IServiceCollection services, string connectionString, Action<MongoClientSettings>? configure = default) where T : MongoDbContext<T>
    {
        ArgumentNullException.ThrowIfNull(connectionString);
        var mongoOptionsBuilder = services.AddOptions<MongoDbContextOptions<T>>()
            .Configure(options =>
            {
                BsonSerializer.RegisterSerializer(typeof(DateTime),
                    new DateTimeSerializer(DateTimeKind.Local, BsonType.DateTime));
                options.Url = MongoUrl.Create(connectionString);
                options.DatabaseName = options.Url.DatabaseName;
                options.Settings = MongoClientSettings.FromUrl(options.Url);
            });
        if (configure is not null)
        {
            services.AddOptions<MongoClientSettings>().PostConfigure(configure);
            mongoOptionsBuilder.PostConfigure<IOptions<MongoClientSettings>>((options, settingOptions) =>
            {
                options.Settings = settingOptions.Value.Clone();
                options.MongoClient = new MongoClient(options.Settings);
            });
        }
        else
        {
            mongoOptionsBuilder.PostConfigure(options => options.MongoClient = new MongoClient(options.Settings));
        }

        services.AddSingleton<T>();
        return services;
    }
}