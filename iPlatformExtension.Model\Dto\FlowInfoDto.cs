﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto
{
    /// <summary>
    /// 流程信息
    /// </summary>
    public class FlowInfoDto
    {
        /// <summary>
        /// 开案ID
        /// </summary>
        [JsonPropertyName("obj_id")]
        public string ObjectId { get; set; } = null!;
        
        /// <summary>
        /// 案件类型
        /// </summary>
        [JsonPropertyName("flow_type")]
        public string FlowType { get; set; } = null!;
        
        /// <summary>
        /// 业务类型
        /// </summary>
        [JsonPropertyName("flow_sub_type")]
        public string? FlowSubType { get; set; }
        
        /// <summary>
        /// 部门ID
        /// </summary>
        [JsonPropertyName("dept_id")]
        public string? DeptId { get; set; }
        
        /// <summary>
        /// 流程状态
        /// </summary>
        [JsonPropertyName("status")]
        public int Status { get; set; }
        
        /// <summary>
        /// 流程id
        /// </summary>
        [JsonPropertyName("flow_id")]
        public string FlowId { get; set; } = null!;
        
        /// <summary>
        /// 部门名称
        /// </summary>
        [JsonPropertyName("full_name")]
        public string? FullName { get; set; }

        /// <summary>
        /// 是否可用
        /// </summary>
        [JsonPropertyName("is_enabled")]
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 是否在用
        /// </summary>
        [JsonPropertyName("is_used")]
        public bool IsUsed { get; set; }
        
    }
}
