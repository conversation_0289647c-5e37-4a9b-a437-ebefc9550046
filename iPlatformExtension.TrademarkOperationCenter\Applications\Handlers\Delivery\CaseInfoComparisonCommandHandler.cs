﻿using iPlatformExtension.Common.Helper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery
{
    /// <summary>
    /// 比较数据是否一致
    /// </summary>
    internal class CaseInfoComparisonCommandHandler(IFreeSql freeSql, IMediator mediator)
        : IRequestHandler<CaseInfoComparisonQuery, bool>
    {
        public async Task<bool> Handle(CaseInfoComparisonQuery request, CancellationToken cancellationToken)
        {
            //快照数据
            var deliveryInfo = request.DeliveryInfo;

            //组装实时数据
            var procInfo = await freeSql.Select<CaseProcInfo>().Include(procInfo => procInfo.CaseInfo)
                .Where(info => info.ProcId == deliveryInfo.ProcId).FirstAsync(cancellationToken);

            var deliveryStatus = (DeliveryStatus) (deliveryInfo.Status ?? 0);
            if (deliveryStatus >= DeliveryStatus.Delivering)
            {
                return false;
            }

            var deliveryInfoC = new DeliInfo()
            {
                ProcId = deliveryInfo.ProcId,
                CaseId = procInfo.CaseId,
                Status = deliveryInfo.Status,
                FlowType = FlowType.Delivery,
                FlowSubType = "TII",
                DeliveryKey = procInfo.DeliveryKey ?? string.Empty,
            };

            //获取最新申请人列表
            var applicants = await freeSql.Select<CaseApplicantList>().From<CusApplicant, CusAddressList>().WithLock()
                .LeftJoin(
                    (caseApplicant, cusApplicant, cusAddress) => caseApplicant.ApplicantId == cusApplicant.ApplicantId)
                .LeftJoin((caseApplicant, cusApplicant, cusAddress) => caseApplicant.AddressId == cusAddress.AddressId)
                .Where((caseApplicant, cusApplicant, cusAddress) => caseApplicant.CaseId == procInfo.CaseId)
                .ToListAsync((caseApplicant, cusApplicant, cusAddress) => new DeliApplicant()
                {
                    AddressId = cusAddress.AddressId,
                    AddressCn = cusAddress.AddressCn,
                    AddressEn = cusAddress.AddressEn,
                    AddressType = cusAddress.AddressType,
                    ApplicantId = cusApplicant.ApplicantId,
                    AddressDetail = cusAddress.AddressDetail,
                    ApplicantNameCn = cusApplicant.ApplicantNameCn,
                    ApplicantNameEn = cusApplicant.ApplicantNameEn,
                    CardNo = cusApplicant.CardNo,
                    CardType = cusApplicant.CardType,
                    CountryId = cusApplicant.CountryId,
                    Postcode = cusAddress.Postcode,
                    ProvinceId = cusAddress.ProvinceId,
                    CityId = cusAddress.CityId,
                    ProcId = procInfo.ProcId,
                    TypeId = cusApplicant.TypeId,
                    IsRepresent = caseApplicant.IsRepresent
                }, cancellationToken);

            //获取优先权信息
            var caseId = procInfo.CaseId;
            var priorityInfos = await freeSql.Select<CasePriorityInfo>().NoTracking().WithLock()
                .Where(info => info.CaseId == caseId).ToListAsync(info => new DeliPriority()
                {
                    Id = 0,
                    DasCode = info.DasCode,
                    CountryId = info.PriorityCountryId,
                    PriorityDate = info.PriorityDate,
                    PriorityNo = info.PriorityNo,
                    PriorityId = info.PriorityId,
                    Volume = info.Volume
                }, cancellationToken);

            //获取尼斯分类
            var categories = await freeSql.Select<CaseTrademarkNiceCategory>().WithLock()
                .Where(category => category.CaseId == caseId).ToListAsync(category => new DeliveryNiceCategory()
                {
                    Id = 0,
                    CaseId = caseId,
                    CategoryName = category.CategoryName,
                    CategoryNumber = category.CategoryNumber,
                    CategoryId = category.CategoryId,
                    CreationTime = DateTime.Now,
                    GrandNumber = category.GrandNumber,
                    GrandName = category.GrandName,
                    GrandId = category.GrandId,
                    ParentNumber = category.ParentNumber,
                    ParentName = category.ParentName,
                    ParentId = category.ParentId,
                    ProcId = deliveryInfo.ProcId,
                    UpdateTime = DateTime.Now,
                    IsStandard = category.IsStandard
                }, cancellationToken);


            deliveryInfoC.Applicants = applicants;
            deliveryInfoC.Priorities = priorityInfos;
            deliveryInfoC.NiceCategories = categories;

            await mediator.Send(new BuildTrademarkInfoCommand(deliveryInfoC, procInfo), cancellationToken);
            await mediator.Send(new BuildContactCommand(deliveryInfoC, procInfo), cancellationToken);

            var dc = deliveryInfoC.Clone<DeliInfoComparison>();
            var toCompareInfo = deliveryInfo.Clone<DeliInfoComparison>();
            return dc.Comparison(toCompareInfo);
        }
    }
}
