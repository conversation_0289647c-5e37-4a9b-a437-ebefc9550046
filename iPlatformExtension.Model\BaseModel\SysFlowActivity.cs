using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{

    [Table(Name = "sys_flow_activity", DisableSyncStructure = true)]
    public partial class SysFlowActivity : IVersionEntity
    {

        /// <summary>
        /// 流程活动主键
        /// </summary>
        [Column(Name = "activity_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string ActivityId { get; set; } = Guid.NewGuid().ToString().ToUpper();

        [Column(Name = "allow_edit")]
        public bool AllowEdit { get; set; } = true;

        [Column(Name = "create_time")]
        public DateTime? CreateTime { get; set; }

        [Column(Name = "create_user_id", StringLength = 50)]
        public string CreateUserId { get; set; }

        [Column(Name = "cur_node_id", StringLength = 50)]
        public string CurNodeId { get; set; }

        [Column(Name = "cur_user_id", StringLength = 50)]
        public string CurUserId { get; set; }

        [Column(Name = "flow_id", StringLength = 50)]
        public string FlowId { get; set; }

        [Column(Name = "flow_sub_type", StringLength = 50)]
        public string FlowSubType { get; set; }

        [Column(Name = "flow_type", StringLength = 50)]
        public string FlowType { get; set; }

        [Column(Name = "int_due_date")]
        public DateTime? IntDueDate { get; set; }

        [Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
        public string ObjId { get; set; } = Guid.NewGuid().ToString().ToUpper();

        [Column(Name = "prev_audit_type_id", StringLength = 50)]
        public string PrevAuditTypeId { get; set; }

        [Column(Name = "prev_node_id", StringLength = 50)]
        public string PrevNodeId { get; set; }

        [Column(Name = "private_id", StringLength = 50)]
        public string PrivateId { get; set; }

        [Column(Name = "status")]
        public int? Status { get; set; }

        [Column(Name = "update_time")]
        public DateTime? UpdateTime { get; set; }

        [Column(Name = "update_user_id", StringLength = 50)]
        public string UpdateUserId { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [Column(Name = "version", IsVersion = true)]
        public int Version { get; set; }

        /// <summary>
        ///  案件信息
        /// </summary>
        [Navigate(nameof(ObjId))]
        public virtual CaseInfo CaseInfo { get; set; } = default!;

        /// <summary>
        ///  任务信息
        /// </summary>
        [Navigate(nameof(ObjId))]
        public virtual CaseProcInfo CaseProcInfo { get; set; } = default!;

        /// <summary>
        ///  流程当前节点信息
        /// </summary>
        [Navigate(nameof(CurNodeId))]
        public virtual SysFlowNode CurFlowNode { get; set; } = default!;

        /// <summary>
        ///  流程上一节点信息
        /// </summary>
        [Navigate(nameof(PrevNodeId))]
        public virtual SysFlowNode PreFlowNode { get; set; } = default!;

        /// <summary>
        ///  流程当前处理人信息
        /// </summary>
        [Navigate(nameof(CurUserId))]
        public virtual SysUserInfo CurNodeUserInfo { get; set; } = default!;

        /// <summary>
        ///  流程标签
        /// </summary>
        [Navigate(nameof(PrivateId))]
        public virtual SysFlowPrivate SysFlowPrivate { get; set; } = default!;


        /// <summary>
        ///  流程对应历史
        /// </summary>
        [Navigate(nameof(ObjId), TempPrimary = nameof(ObjId))]
        public virtual SysFlowHistory FlowHistory { get; set; } = default!;

        /// <summary>
        ///  核稿流程
        /// </summary>
        [Navigate(nameof(ObjId))]
        public virtual CaseProcFlow CaseProcFlow { get; set; } = default!;

    }

}
