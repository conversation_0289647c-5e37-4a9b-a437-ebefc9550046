﻿using System.Runtime.CompilerServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;

namespace iPlatformExtension.Common.Extensions;

public static class StringExtension
{
    public static string? ToNull(this string? str)
    {
        return string.IsNullOrWhiteSpace(str) ? null : str;
    }

    public static byte[] GetBytes(this string str)
    {
        return GetBytes(str, Encoding.Default);
    }

    public static byte[] GetBytes(this string str, Encoding encoding)
    {
        ArgumentNullException.ThrowIfNull(str);
        ArgumentNullException.ThrowIfNull(encoding);

        return encoding.GetBytes(str);
    }

    public static byte[] GetBytes(this string str, string encoding)
    {
        ArgumentNullException.ThrowIfNull(str);
        ArgumentException.ThrowIfNullOrEmpty(encoding);

        return GetBytes(str, Encoding.GetEncoding(encoding));
    }

    public static string GetString(this byte[] bytes, Encoding encoding)
    {
        return encoding.GetString(bytes);
    }

    public static string GetString(this byte[] bytes) => GetString(bytes, Encoding.Default);

    public static string GetString(this byte[] bytes, string encoding)
    {
        ArgumentNullException.ThrowIfNull(bytes);
        ArgumentException.ThrowIfNullOrWhiteSpace(encoding);

        return GetString(bytes, Encoding.GetEncoding(encoding));
    }

    public static string GetRequireValue(this string? originValue, [CallerArgumentExpression("originValue")]string? parameterName = null)
    {
        if (string.IsNullOrWhiteSpace(originValue))
        {
            throw new ArgumentNullException(parameterName);
        }
        return originValue;
    }

    public static string ToUnderLine(string camelClassName)
    {
        return Regex.Replace(camelClassName ,"([A-Z])", "_$1").ToLower().TrimStart('_');
    }

    public static string GetBooleanChineseDescription(this bool booleanValue) => booleanValue ? "是" : "否";

    public static string GetOrDefaultEmpty(this string? stringValue) => stringValue ?? string.Empty;

    public static string UrlEncode(this string stringValue) => HttpUtility.UrlEncode(stringValue);
}