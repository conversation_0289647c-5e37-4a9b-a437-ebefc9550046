using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mon_deli_config", DisableSyncStructure = true)]
	public partial class MonDeliConfig {

		[ Column(Name = "config_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ConfigId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "month1")]
		public int? Month1 { get; set; }

		[ Column(Name = "month10")]
		public int? Month10 { get; set; }

		[ Column(Name = "month11")]
		public int? Month11 { get; set; }

		[ Column(Name = "month12")]
		public int? Month12 { get; set; }

		[ Column(Name = "month2")]
		public int? Month2 { get; set; }

		[ Column(Name = "month3")]
		public int? Month3 { get; set; }

		[ Column(Name = "month4")]
		public int? Month4 { get; set; }

		[ Column(Name = "month5")]
		public int? Month5 { get; set; }

		[ Column(Name = "month6")]
		public int? Month6 { get; set; }

		[ Column(Name = "month7")]
		public int? Month7 { get; set; }

		[ Column(Name = "month8")]
		public int? Month8 { get; set; }

		[ Column(Name = "month9")]
		public int? Month9 { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
