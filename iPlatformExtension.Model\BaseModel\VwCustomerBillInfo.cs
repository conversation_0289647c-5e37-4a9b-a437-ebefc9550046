using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_customer_bill_info", DisableSyncStructure = true)]
	public partial class VwCustomerBillInfo {

		[ Column(StringLength = 50)]
		public string 币别 { get; set; }

		[ Column(DbType = "money")]
		public decimal? 第三方费用金额 { get; set; }

		[ Column(DbType = "money")]
		public decimal? 服务费金额 { get; set; }

		[ Column(StringLength = 10)]
		public string 付款期限 { get; set; }

		[ Column(DbType = "money")]
		public decimal? 含税金额 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 客户标识 { get; set; }

		[ Column(StringLength = 2000)]
		public string 客户名称 { get; set; }

		[ Column(DbType = "money")]
		public decimal? 年费金额 { get; set; }

		[ Column(StringLength = 10)]
		public string 请款日期 { get; set; }

		[ Column(DbType = "money")]
		public decimal? 税额 { get; set; }

		[ Column(DbType = "money")]
		public decimal? 税率 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 业务员标识 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 帐单标识 { get; set; }

		[ Column(StringLength = 50)]
		public string 账单编号 { get; set; }

		[ Column(StringLength = 50)]
		public string 账单语言 { get; set; }

		[ Column(StringLength = 500)]
		public string 账单主题 { get; set; }

		[ Column(StringLength = 100)]
		public string 账单状态 { get; set; }

		[ Column(DbType = "money")]
		public decimal? 账单总金额 { get; set; }

	}

}
