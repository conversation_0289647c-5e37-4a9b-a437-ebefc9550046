﻿using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Models.Proc;
using iPlatformExtension.Finance.Applications.Queries.Proc;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Proc;

internal sealed class UnpaidOfficialProcResultQueryHandler(IMediator mediator, IFreeSql freeSql) 
    : IRequestHandler<UnpaidOfficialProcResultQuery, IEnumerable<UnpaidOfficialProcDto>>
{
    public async Task<IEnumerable<UnpaidOfficialProcDto>> Handle(UnpaidOfficialProcResultQuery request, CancellationToken cancellationToken)
    {
        var procAmounts = request.Results;
        var procIds = procAmounts.Select(dto => dto.ProcId).ToList();
        
        var tempResults = await freeSql.Select<CaseProcInfo, CaseInfo, CusCustomer>().WithLock()
            .LeftJoin((info, caseInfo, customer) => info.CaseId == caseInfo.Id)
            .LeftJoin((info, caseInfo, customer) => caseInfo.CustomerId == customer.CustomerId)
            .Where((info, caseInfo, customer) => procIds.Contains(info.ProcId))
            .OrderByDescending((info, caseInfo, customer) => info.SendOfficialDate)
            .ToListAsync((info, caseInfo, customer) => new UnpaidOfficialProcDto
            {
                ProcId = info.ProcId,
                ProcNo = info.ProcNo,
                Volume = caseInfo.Volume,
                CaseId = caseInfo.Id,
                Customer = new CustomerInfo(customer.CustomerId, customer.CustomerName, customer.CustomerNameEn),
                CaseName = caseInfo.CaseName,
                TrademarkClasses = caseInfo.TrademarkClass ?? string.Empty,
                SendOfficialDate = info.SendOfficialDate,
                CtrlProcId = info.CtrlProcId,
                DeliveryKey = new KeyValuePair<string, string>(SystemDictionaryName.DeliveryKey.ToSqlStringConstant(),
                    info.DeliveryKey),
                CaseDirection =
                    new KeyValuePair<string, string>(SystemDictionaryName.CaseDirection.ToSqlStringConstant(),
                        caseInfo.CaseDirection),
                ApplicantNo = caseInfo.AppNo,
                RegistrationNo = caseInfo.RegisterNo,
                DeliveryType = SqlExt.Case().When(info.FilingType == "0", "纸交").When(info.FilingType == "1", "网交").End(),
                OfficialPublicationDates = string.Empty,
            }, cancellationToken);

        var results = tempResults.Join(procAmounts, dto => dto.ProcId, dto => dto.ProcId, (dto, procDto) =>
        {
            dto.TotalAmount = procDto.TotalAmount;
            dto.OfficialPublicationDates = procDto.OfficialPublicationDates;

            return dto;
        }).ToList();

        await mediator.Publish(new UnpaidOfficialProcResultNotification(results), cancellationToken);

        return results;
    }
}