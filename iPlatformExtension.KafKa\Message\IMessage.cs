﻿using System.Runtime.CompilerServices;
using Confluent.Kafka;

namespace iPlatformExtension.KafKa.Message;

public interface IMessage<out TMessageKey>
{
    TMessageKey Key { get; }
}

public interface IMessage : IMessage<Null>
{
    [UnsafeAccessor(UnsafeAccessorKind.Constructor)]
    private static extern Null CreateNullKey();
    
    private static readonly Null nullKey = CreateNullKey();
    
    Null IMessage<Null>.Key => nullKey;
}