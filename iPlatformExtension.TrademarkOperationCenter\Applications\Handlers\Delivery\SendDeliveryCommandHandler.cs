﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class SendDeliveryCommandHandler(
    IDeliveryInfoRepository deliveryInfoRepository,
    DefaultSendPolicyProvider policyProvider,
    IServiceProvider serviceProvider)
    : IRequestHandler<SendDeliveryCommand, SendDeliveryInternalCommand?>
{
    public async Task<SendDeliveryInternalCommand?> Handle(SendDeliveryCommand request, CancellationToken cancellationToken)
    {
        var deliveryInfo = await deliveryInfoRepository.GetAsync(request.ProcId, cancellationToken);

        if (deliveryInfo is null)
        {
            throw new NotFoundException(request.ProcId, "递交任务");
        }

        var deliveryButton = request.DeliveryButton;
        var currentUserId = request.OperatorId;
        var policy = await policyProvider.GetPolicyAsync(deliveryInfo);

        if (serviceProvider.GetRequiredService(policy.HandlerType) is not ISendDeliveryCommandHandler handler)
        {
            throw new ApplicationException("不存在对应的递交任务处理程序");
        }

        return deliveryButton switch
        {
            DeliveryButton.StartUpDelivery => await handler.HandleSendStartupCommandAsync(new SendContext(
                TrademarkDeliveryOperation.StartupDelivery.GetDescription(),
                policy.StartupDeliveryMessageKey, deliveryInfo, currentUserId), cancellationToken),
            
            DeliveryButton.StopDelivery => await handler.HandleSendStopCommandAsync(new SendContext(
                TrademarkDeliveryOperation.StopDelivery.GetDescription(),
                policy.StopDeliveryMessageKey, deliveryInfo, currentUserId), cancellationToken),
            
            DeliveryButton.WithdrawDelivery => await handler.HandleSendWithdrawCommandAsync(new SendContext(
                TrademarkDeliveryOperation.WithdrawDelivery.GetDescription(),
                policy.WithdrawDeliveryMessageKey, deliveryInfo, currentUserId), cancellationToken),
            
            DeliveryButton.CancelDelivery => await handler.HandleSendCancelCommandAsync(new SendContext(
                TrademarkDeliveryOperation.CancelDelivery.GetDescription(),
                policy.CancelDeliveryMessageKey, deliveryInfo, currentUserId), cancellationToken),
            
            _ => throw new ArgumentOutOfRangeException(nameof(deliveryButton))
        };
    }
}