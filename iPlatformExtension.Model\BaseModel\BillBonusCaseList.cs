using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_bonus_case_list", DisableSyncStructure = true)]
	public partial class BillBonusCaseList {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "base_date")]
		public DateTime? BaseDate { get; set; }

		[ Column(Name = "base_date_type", StringLength = 50)]
		public string BaseDateType { get; set; }

		[ Column(Name = "bonus_amount", DbType = "money")]
		public decimal? BonusAmount { get; set; } = 0M;

		[ Column(Name = "bonus_grade", StringLength = 50)]
		public string BonusGrade { get; set; }

		[ Column(Name = "bonus_point", DbType = "money")]
		public decimal? BonusPoint { get; set; } = 0M;

		[ Column(Name = "bonus_type", StringLength = 50, IsNullable = false)]
		public string BonusType { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "cn_grade", StringLength = 50)]
		public string CnGrade { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "ctrl_proc_property", StringLength = 50)]
		public string CtrlProcProperty { get; set; }

		[ Column(Name = "en_grade", StringLength = 50)]
		public string EnGrade { get; set; }

		[ Column(Name = "en_oa_grade", StringLength = 50)]
		public string EnOaGrade { get; set; }

		[ Column(Name = "en_translate_grade", StringLength = 50)]
		public string EnTranslateGrade { get; set; }

		[ Column(Name = "extra_point", DbType = "money")]
		public decimal? ExtraPoint { get; set; } = 0M;

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		[ Column(Name = "grade_type", StringLength = 50)]
		public string GradeType { get; set; }

		[ Column(Name = "has_priority")]
		public bool? HasPriority { get; set; }

		[ Column(Name = "initial_amount", DbType = "money")]
		public decimal? InitialAmount { get; set; } = 0M;

		[ Column(Name = "initial_point", DbType = "money")]
		public decimal? InitialPoint { get; set; } = 0M;

		[ Column(Name = "is_add")]
		public bool? IsAdd { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "is_foreign_case")]
		public bool? IsForeignCase { get; set; }

		[ Column(Name = "is_lock")]
		public bool? IsLock { get; set; } = false;

		[ Column(Name = "is_same_day")]
		public bool? IsSameDay { get; set; }

		[ Column(Name = "is_substance", StringLength = 50)]
		public string IsSubstance { get; set; }

		[ Column(Name = "isOA")]
		public bool? IsOA { get; set; }

		[ Column(Name = "month", StringLength = 5)]
		public string Month { get; set; }

		[ Column(Name = "pct_enter")]
		public bool? PctEnter { get; set; }

		[ Column(Name = "pct_pub_language", StringLength = 50)]
		public string PctPubLanguage { get; set; }

		[ Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
		public string ProcId { get; set; }

		[ Column(Name = "real_point", DbType = "money")]
		public decimal? RealPoint { get; set; } = 0M;

		[ Column(Name = "real_point_pure", DbType = "money")]
		public decimal? RealPointPure { get; set; }

		[ Column(Name = "relevancy_ay", StringLength = 50)]
		public string RelevancyAy { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "rule_id", StringLength = 50)]
		public string RuleId { get; set; }

		[ Column(Name = "translate_price", DbType = "money")]
		public decimal? TranslatePrice { get; set; }

		[ Column(Name = "translate_rank_type", StringLength = 50)]
		public string TranslateRankType { get; set; }

		[ Column(Name = "translate_type", StringLength = 50)]
		public string TranslateType { get; set; }

		[ Column(Name = "translate_words_num", DbType = "money")]
		public decimal? TranslateWordsNum { get; set; }

		[ Column(Name = "translate_work_type", StringLength = 50)]
		public string TranslateWorkType { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

		[ Column(Name = "user_percentage", DbType = "money")]
		public decimal? UserPercentage { get; set; }

		[ Column(Name = "year", StringLength = 5)]
		public string Year { get; set; }

		/// <summary>
		/// 推送状态
		/// </summary>
		[Column(Name = "pushed")]
		public bool Pushed { get; set; }
	}

}
