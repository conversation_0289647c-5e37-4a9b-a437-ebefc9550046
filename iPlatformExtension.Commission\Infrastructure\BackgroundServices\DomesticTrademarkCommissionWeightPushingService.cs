﻿using System.Threading.Channels;
using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;

namespace iPlatformExtension.Commission.Infrastructure.BackgroundServices;

internal sealed class DomesticTrademarkCommissionWeightPushingService(
    IServiceScopeFactory serviceScopeFactory,
    Channel<PushDomesticCommissionWeightCommand> channel,
    ILogger<DomesticTrademarkCommissionWeightPushingService> logger)
    : BackgroundConsumeService<PushDomesticCommissionWeightCommand>(channel, logger, serviceScopeFactory);