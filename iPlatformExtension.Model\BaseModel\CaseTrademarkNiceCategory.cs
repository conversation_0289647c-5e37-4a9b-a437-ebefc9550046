﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 案件商标尼斯分类
	/// </summary>
	[Table(Name = "case_trademark_nice_category", DisableSyncStructure = true)]
	public class CaseTrademarkNiceCategory
	{

		/// <summary>
		/// 案件id
		/// </summary>
		[Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; } = default!;

		/// <summary>
		/// 尼斯分类商品名称
		/// </summary>
		[Column(Name = "category_name", StringLength = 100, IsNullable = false)]
		public string CategoryName { get; set; } = "";

		/// <summary>
		/// 商品分类编码
		/// </summary>
		[Column(Name = "category_number", StringLength = 50, IsNullable = false)]
		public string CategoryNumber { get; set; } = "";

		/// <summary>
		/// 商品分类id
		/// </summary>
		[Column(Name = "category_id", StringLength = 50, IsNullable = false)]
		public string CategoryId { get; set; } = "";

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "creation_time", InsertValueSql = "getdate()")]
		public DateTime CreationTime { get; set; }

		/// <summary>
		/// 商品分类大类编码
		/// </summary>
		[Column(Name = "grand_number", StringLength = 50, IsNullable = false)]
		public string GrandNumber { get; set; } = "";

		/// <summary>
		/// 分类名称
		/// </summary>
		[Column(Name = "grand_name", StringLength = 50, IsNullable = false)]
		public string GrandName { get; set; } = "";

		/// <summary>
		/// 分类id
		/// </summary>
		[Column(Name = "grand_id", StringLength = 50, IsNullable = false)]
		public string GrandId { get; set; } = "";

		/// <summary>
		/// 主键
		/// </summary>
		[Column(Name = "id", IsIdentity = true)]
		public long Id { get; init; }

		/// <summary>
		/// 父项商品分类编码
		/// </summary>
		[Column(Name = "parent_number", StringLength = 50, IsNullable = false)]
		public string ParentNumber { get; set; } = "";

		/// <summary>
		/// 群编码
		/// </summary>
		[Column(Name = "parent_name", StringLength = 50, IsNullable = false)]
		public string ParentName { get; set; } = "";

		/// <summary>
		/// 群id
		/// </summary>
		[Column(Name = "parent_id", StringLength = 50, IsNullable = false)]
		public string ParentId { get; set; } = "";

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 是否标准项目
		/// </summary>
		[Column(Name = "is_standard", IsNullable = false)]
		public bool IsStandard { get; set; } = true;

		/// <summary>
		/// 排序
		/// </summary>
		[Column(Name = "order")]
		public int Order { get; set; }

	}

}
