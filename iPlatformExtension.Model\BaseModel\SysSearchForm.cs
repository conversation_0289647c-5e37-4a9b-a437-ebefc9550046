using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_search_form", DisableSyncStructure = true)]
	public partial class SysSearchForm {

		[ Column(Name = "search_form_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string SearchFormId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_id", StringLength = 50)]
		public string ColumnId { get; set; }

		[ Column(Name = "container_id", StringLength = 50)]
		public string ContainerId { get; set; }

		/// <summary>
		/// 是否显示在列表中，目前仅用于查询结果是否显示
		/// </summary>
		[ Column(Name = "is_show")]
		public bool? IsShow { get; set; } = true;

		[ Column(Name = "list_code", StringLength = 50)]
		public string ListCode { get; set; }

		[ Column(Name = "re_column_name", StringLength = 50)]
		public string ReColumnName { get; set; }

		[ Column(Name = "re_form_data_code", StringLength = 50)]
		public string ReFormDataCode { get; set; }

		[ Column(Name = "search_form_code", StringLength = 50)]
		public string SearchFormCode { get; set; }

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

		[ Column(Name = "sub_code", StringLength = 50)]
		public string SubCode { get; set; }

		/// <summary>
		/// 栏位类型  表单构造，列表显示
		/// </summary>
		[ Column(Name = "type", StringLength = 50)]
		public string Type { get; set; }

	}

}
