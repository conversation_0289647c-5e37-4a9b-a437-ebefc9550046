﻿using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Service.Interface;

public interface IPayeeQueryService : IFreeSqlQueryService, ITransientDependency
{
    async Task<PayeeDetailInfoDto?> GetPayeeDetailInfoAsync(string companyId)
    {
        var payeeInfo = await DbQuery.Select<BasCompany>().WithLock()
            .Where(company => company.CompanyId == companyId && company.IsEnabled == true)
            .FirstAsync(company => new PayeeDetailInfoDto()
            {
                CompanyCode = company.CompanyCode,
                Id = company.CompanyId,
                CnName = company.CompanyNameCn,
                EnName = company.CompanyNameEn,
                Payee = company.Payee,
                Invoicer = company.Invoicer,
                Reviewer = company.Reviewer,
                DistrictId = company.DistrictId,
                TaxRate = company.TaxRate ?? decimal.Zero,
                AbbreviatedName = company.ShortNameCn,
                Fax = company.Fax,
                Email = company.Email,
                Tel = company.Tel,
                CnAddress = company.AddressCn,
                EnAddress = company.AddressEn,
                PostCode = company.PostCode
            });
        if (payeeInfo is not null)
        {
            payeeInfo.BankAccountInfos = await DbQuery.Select<BasSelfBank>().WithLock().From<BasSelfBankCompany>()
                .InnerJoin((bank, company) => bank.BankId == company.BankId)
                .Where((bank, company) => company.CompanyId == companyId && bank.IsEnabled == true)
                .OrderBy((bank, company) => bank.Seq)
                .ToListAsync((bank, company) =>
                    new PayeeBankAccountInfo()
                    {
                        AddressCn = bank.BankAddressZhCn,
                        AddressEn = bank.BankAddressEnUs,
                        BankAccount = bank.Account,
                        BankId = bank.BankId,
                        CnName = bank.BankNameZhCn,
                        EnName = bank.BankNameEnUs,
                        Currency = bank.Currency,
                        SwiftCode = bank.BankCode,
                        AccountName = bank.UserNameZhCn,
                        AccountNameEn = bank.UserNameEnUs,
                    });
        }

        return payeeInfo;
    }
}