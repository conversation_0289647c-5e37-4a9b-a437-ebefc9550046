using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_file_setting", DisableSyncStructure = true)]
	public partial class DeliFileSetting {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "file_ex", StringLength = 100)]
		public string FileEx { get; set; }

		[ Column(Name = "file_name", StringLength = 50)]
		public string FileName { get; set; }

		[ Column(Name = "file_regex", StringLength = 200)]
		public string FileRegex { get; set; }

		[ Column(Name = "file_setting_code", StringLength = 50)]
		public string FileSettingCode { get; set; }

		[ Column(Name = "file_type", StringLength = 50)]
		public string FileType { get; set; }

		[ Column(Name = "is_apply")]
		public bool? IsApply { get; set; } = true;

		[ Column(Name = "is_file")]
		public bool? IsFile { get; set; } = true;

		[ Column(Name = "is_unique")]
		public bool? IsUnique { get; set; } = true;

		[ Column(Name = "office_id", StringLength = 50)]
		public string OfficeId { get; set; }

		[ Column(Name = "reply_file_name", StringLength = 50)]
		public string ReplyFileName { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
