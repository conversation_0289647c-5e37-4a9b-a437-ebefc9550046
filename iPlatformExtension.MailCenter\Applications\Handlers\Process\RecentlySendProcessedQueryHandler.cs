using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.Process;
using iPlatformExtension.MailCenter.Applications.Queries.Process;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Process;

/// <summary>
/// 最近7天办理发件待审清单
/// </summary>
internal sealed class RecentlySendProcessedQueryHandler(IFreeSql<MailCenterFreeSql> freeSql, IHttpContextAccessor content, IUserInfoRepository userInfoRepository) : IRequestHandler<RecentlySendProcessedQuery, IEnumerable<SendProcessListDto>>
{
    public async Task<IEnumerable<SendProcessListDto>> Handle(RecentlySendProcessedQuery request, CancellationToken cancellationToken)
    {
        var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
        
        // 查询最近7天内审核过的邮件ID
        var mailIds = await freeSql.Select<FlowRecord, MailSend>()
            .LeftJoin(it => it.t1.MailId == it.t2.MailId)
            .WhereIf(!string.IsNullOrEmpty(request.Search), it => 
                it.t2.MailNo == request.Search || 
                it.t2.MailSubject.Contains(request.Search) || 
                it.t2.MailFrom.Contains(request.Search))
            .Where(it => it.t1.AuditTime.Value.Date > DateTime.Now.Date.AddDays(-7))
            .Where(it => it.t1.AuditUser == userId && (it.t1.IsCurrent == SysEnum.MailFlowActionStatus.DisEnable.GetHashCode() || it.t1.CurNodeId == SysEnum.MailFlowAction.End.ToString()))
            .Distinct()
            .ToListAsync(it => it.t1.MailId, cancellationToken);

        // 查询发件待审清单
        var mailSends = await freeSql.Select<FlowRecord, MailSend, MailSendFlow, FlowRecord, MailUser>()
            .LeftJoin(it => it.t1.MailId == it.t2.MailId)
            .LeftJoin(it => it.t2.MailId == it.t3.MailId)
            .LeftJoin(it => it.t1.PreRecordId == it.t4.Id)
            .LeftJoin(it => it.t2.MailId == it.t5.MailId && it.t5.AddressType == "from")
            .WithLock()
            .Where(it => mailIds.Contains(it.t1.MailId))
            .Where(it => it.t1.IsCurrent == SysEnum.MailFlowActionStatus.Enable.GetHashCode() || it.t1.CurNodeId == SysEnum.MailFlowAction.End.ToString())
            .WhereIf(request.Type != null && request.Type == 1, it => it.t1.AuditUser == userId) // 我审核的
            .WhereIf(request.Type != null && request.Type == 2, it => it.t3.UndertakeUserId == userId) // 我承办的
            .WhereIf(request.Type != null && request.Type == 3, it => it.t1.AuditUser != userId && it.t3.UndertakeUserId != userId) // 其他
            .OrderByDescending(it => it.t4.AuditTime)
            .Page(request.PageIndex!.Value, request.PageSize!.Value)
            .Count(out var count)
            .ToListAsync(it => new SendProcessListDto(
                it.t2.MailId,
                it.t2.MailNo ?? string.Empty,
                it.t2.MailSubject ?? string.Empty,
                it.t2.MailTo ?? string.Empty,
                it.t2.CreateBy,
                it.t2.IsRead ?? false,
                it.t2.MailDate,
                it.t2.Status,
                it.t4.AuditRemark ?? string.Empty,
                it.t1.CurNodeId ?? string.Empty,
                it.t4.AuditRemark ?? string.Empty,
                it.t4.AuditTime,
                it.t4.AuditType ?? string.Empty,
                it.t3.UndertakeUserId ?? string.Empty,
                it.t1.AuditUser ?? string.Empty), 
                cancellationToken);

        // 返回分页结果
        return new PageResult<SendProcessListDto>()
        {
            Data = await mailSends.ToAsyncEnumerable().SelectAwait(async sendList =>
            {
                // 处理发件人信息
                if (sendList.MailFromTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    sendList.MailFrom = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(sendList.MailFromTemp))?.CnName ?? "", UserId = sendList.MailFromTemp };
                }
                
                // 处理承办人信息
                if (sendList.UndertakeUserTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    sendList.UndertakeUser = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(sendList.UndertakeUserTemp))?.CnName ?? "", UserId = sendList.UndertakeUserTemp };
                }
                
                // 处理审核人信息
                if (sendList.AuditUserTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    sendList.AuditUser = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(sendList.AuditUserTemp))?.CnName ?? "", UserId = sendList.AuditUserTemp };
                }
                
                return sendList;
            }).ToListAsync(cancellationToken: cancellationToken),
            Page = request.PageIndex.Value,
            PageSize = request.PageSize.Value,
            Total = count
        };
    }
}
