using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_applicant_type", DisableSyncStructure = true)]
	public partial class BasApplicantType {

		/// <summary>
		/// 申请人类型ID
		/// </summary>
		[ Column(Name = "applicant_type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ApplicantTypeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 编号
		/// </summary>
		[ Column(Name = "applicant_type_code", StringLength = 50)]
		public string ApplicantTypeCode { get; set; }

		/// <summary>
		/// 英文名称
		/// </summary>
		[ Column(Name = "applicant_type_en_us", StringLength = 200)]
		public string ApplicantTypeEnUs { get; set; }

		/// <summary>
		/// 日文名称
		/// </summary>
		[ Column(Name = "applicant_type_ja_jp", StringLength = 200)]
		public string ApplicantTypeJaJp { get; set; }

		/// <summary>
		/// 中文名称
		/// </summary>
		[ Column(Name = "applicant_type_zh_cn", StringLength = 100)]
		public string ApplicantTypeZhCn { get; set; }

		/// <summary>
		/// 国家ID（？）
		/// </summary>
		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
