using iPlatformExtension.Commission.Application.Notifications.Trademark.Domestic;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class DomesticCommissionCustomerResultHandler(ICustomerRepository customerRepository) 
    : INotificationHandler<DomesticCommissionResultNotification>
{
    public async Task Handle(DomesticCommissionResultNotification notification, CancellationToken cancellationToken)
    {
        var commission = notification.Commission;
        var customerId = commission.CustomerId;
        
        var customerInfo = await customerRepository.GetCacheValueAsync(customerId, cancellationToken:cancellationToken);
        commission.CustomerName = customerInfo?.CustomerName ?? string.Empty;
    }
}