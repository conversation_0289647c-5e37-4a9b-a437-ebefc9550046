using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_join_list_separate", DisableSyncStructure = true)]
	public partial class CusJoinListSeparate {

		[ Column(Name = "join_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string JoinId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "form_obj_id", StringLength = 50)]
		public string FormObjId { get; set; }

		[ Column(Name = "form_type", StringLength = 50)]
		public string FormType { get; set; }

		[ Column(Name = "join_obj_id", StringLength = 50)]
		public string JoinObjId { get; set; }

		[ Column(Name = "join_type", StringLength = 50)]
		public string JoinType { get; set; }

	}

}
