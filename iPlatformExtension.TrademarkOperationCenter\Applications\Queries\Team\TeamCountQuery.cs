﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;


/// <summary>
/// 团队任务数统计
/// </summary>
/// <param name="FlowType">流程类型</param>
/// <param name="CaseType">案件类型</param>
/// <param name="FlowStatus">流程状态</param>
/// <param name="UserId">团队列表</param>
public record TeamCountQuery(string? FlowType, string? CaseType, int? FlowStatus, string[]? UserId = null,string? FlowSubType = "TII") : IRequest<IEnumerable<TeamCountDto>>;

