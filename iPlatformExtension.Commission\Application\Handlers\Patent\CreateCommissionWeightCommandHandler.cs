﻿using System.Runtime.CompilerServices;
using iPlatformExtension.Commission.Application.Commands.Patent;
using iPlatformExtension.Commission.Grpc;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Patent;

internal sealed class CreateCommissionWeightCommandHandler(
    ISender sender,
    IFreeSql<PlatformFreeSql> freeSql,
    ICustomerRepository customerRepository,
    IDistrictRepository districtRepository,
    IBaseCtrlProcRepository ctrlProcRepository,
    IDepartmentInfoRepository departmentInfoRepository,
    ICacheableRepository<string, SysUserInfo> userInfoCache,
    ILogger<CreateCommissionWeightCommandHandler> logger) : IStreamRequestHandler<CreateCommissionWeightsCommand, CommissionWeight>
{
    public async IAsyncEnumerable<CommissionWeight> Handle(CreateCommissionWeightsCommand request, [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        
        var patentBonus = request.BonusInfos;
        var (year, month, procId, ctrlProcId, _, caseDirection, caseType, undertakerId, volume, customerId) = patentBonus.Key;

        var undertaker = await userInfoCache.GetCacheValueAsync(undertakerId, cancellationToken: cancellationToken);
        
        var commissionWeight = new CommissionWeight
        {
            Year = int.Parse(year),
            Month = int.Parse(month),
            CaseDirection = caseDirection,
            ProcId = procId,
            CaseType = caseType,
            ProcName = await ctrlProcRepository.GetTextValueAsync(ctrlProcId),
            CustomerId = customerId,
            CustomerName = (await customerRepository.GetCacheValueAsync(customerId, cancellationToken: cancellationToken))?.CustomerName,
            Volume = volume,
            ProcNo = string.Empty,
            UndertakerName = undertaker?.CnName ?? string.Empty,
            UndertakerUsername = undertaker?.UserName ?? string.Empty,
        };
        
        foreach (var billBonusCaseList in patentBonus)
        {
            var userInfo = await userInfoCache.GetCacheValueAsync(billBonusCaseList.UserId, cancellationToken: cancellationToken);
            if (userInfo is null)
            {
                continue;
            }

            var realPoint = billBonusCaseList.RealPoint ?? 0M;
            var otherFeeAmount = billBonusCaseList.BonusAmount ?? 0M;
            
            // if (realPoint == 0 && otherFeeAmount == 0)
            //     continue;

            var userDepartmentRecord = await freeSql.Select<SysUserMonthDept>().Where(x => x.UserId == billBonusCaseList.UserId)
                .Where(x => x.Year == year && x.Month == month).ToOneAsync(cancellationToken);
            var departmentId = userDepartmentRecord?.DeptId ?? userInfo.DeptId;
            var departmentInfo = await departmentInfoRepository.GetCacheValueAsync(departmentId, cancellationToken: cancellationToken);
            var districtCode = userDepartmentRecord?.DistrictId ?? departmentInfo?.DistrictCode ?? string.Empty;

            commissionWeight.Id = billBonusCaseList.Id;
            commissionWeight.Weight = new UserWeight
            {
                Username = userInfo.UserName,
                CnName = userInfo.CnName,
                Weight = realPoint.ToString("F4"),
                DeptName = departmentInfo?.FullName ?? string.Empty,
                DeptId = departmentId,
                DistrictCode = districtCode,
                DistrictName = await districtRepository.GetTextValueAsync(districtCode) ?? string.Empty,
                Fees =
                {
                    {
                        (int)FeeTypes.OtherFees, new Fee()
                        {
                            Type = FeeTypes.OtherFees,
                            Value = otherFeeAmount.ToString("F2"),
                            Currency = "CNY"
                        }
                    }
                }
            };
            
            logger.LogInformation("专利提成数据：{CommissionWeight}", commissionWeight);

            if (!await sender.Send(new PushPatentExcludeCommand(billBonusCaseList, commissionWeight), cancellationToken))
            {
                yield return commissionWeight;
            }
        }
    }
}