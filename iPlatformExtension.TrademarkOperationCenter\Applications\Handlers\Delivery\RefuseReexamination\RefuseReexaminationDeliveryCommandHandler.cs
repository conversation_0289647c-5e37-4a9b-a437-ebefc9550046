﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.RefuseReexamination;

internal sealed class RefuseReexaminationDeliveryCommandHandler(
    IMediator mediator,
    IRedisCache<RedisCacheOptionsBase> redisCache,
    IDeliveryInfoRepository deliveryInfoRepository)
    : DeliveryCommandHandlerBase(mediator, redisCache, deliveryInfoRepository)
{
    
    public override async Task<SendDeliveryInternalCommand?> HandleSendStartupCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        var freeSql = _deliveryInfoRepository.Orm;
        var deliveryInfo = context.DeliveryInfo;
        var otherInfo = deliveryInfo.OtherInfo ?? await freeSql.Select<DeliOtherInfo>()
            .WithLock().Where(info => info.ProcId == deliveryInfo.ProcId).ToOneAsync(cancellationToken);

        if (!(otherInfo.IsSplit ?? false)) 
            return await base.HandleSendStartupCommandAsync(context, cancellationToken);
        
        var files = deliveryInfo.Files ?? await freeSql.Select<DeliFiles>()
            .Where(deliFiles => deliFiles.ProcId == deliveryInfo.ProcId).ToListAsync(cancellationToken);
        if (files.All(deliFiles => deliFiles.FileCode != "39"))
        {
            throw new ApplicationException("递交文件缺少分割申请书");
        }

        return await base.HandleSendStartupCommandAsync(context, cancellationToken);
    }
}