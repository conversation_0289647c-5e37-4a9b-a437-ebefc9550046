﻿namespace iPlatformExtension.MailCenter.Applications.Models.ContentManage
{
    public class ReadListDto
    { 
        /// <summary>
        /// 阅读实例id
        /// </summary>
        public string ReadId { get; set; }

        /// <summary>
        /// 邮件Id
        /// </summary>
        public string? MailId { get; set; }

        /// <summary>
        /// 收件编号
        /// </summary>
        public string MailNo { get; set; }

        /// <summary>
        /// 主题
        /// </summary>
        public string MailSubject { get; set; }

        /// <summary>
        /// 发件人
        /// </summary>
        public string MailFrom { get; set; }

        /// <summary>
        /// 收件日期
        /// </summary>
        public DateTime? MailDate { get; set; }

        /// <summary>
        /// 收件人
        /// </summary>
        public string MailTo { get; set; }

        /// <summary>
        /// 原邮件id
        /// </summary>
        public string Uid { get; set; }

        /// <summary>
        /// -1:取消,0:待解析,1:解析中,2:待分拣;3:办理中;4:已忽略;5:已办理;500:出错
        /// </summary>
        public int? Status { get; set; } = 0;

        /// <summary>
        /// 忽略人
        /// </summary>
        public string IgnoreBy { get; set; }

        /// <summary>
        /// 忽略人名称
        /// </summary>
        public string IgnoreByUserName { get; set; }

        /// <summary>
        /// 忽略时间
        /// </summary>
        public DateTime? IgnoreTime { get; set; }

        /// <summary>
        /// 分拣人
        /// </summary>
        public string SortBy { get; set; }

        /// <summary>
        /// 分拣人名称
        /// </summary>
        public string SortByUserName { get; set; }

        /// <summary>
        /// 分拣日期
        /// </summary>
        public DateTime? SortTime { get; set; }

        /// <summary>
        /// 承办人
        /// </summary>
        public string UndertakeUserId { get; set; }

        /// <summary>
        /// 承办人名称
        /// </summary>
        public string UndertakeUserName { get; set; }

        /// <summary>
        /// 阅读时间
        /// </summary>
        public DateTime? ReadTime { get; set; }

    }
}
