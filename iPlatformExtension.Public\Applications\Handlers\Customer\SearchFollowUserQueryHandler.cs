﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Customer;
using iPlatformExtension.Public.Applications.Queries.Customer;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Customer
{
    /// <summary>
    /// 销售查询处理者
    /// </summary>
    internal sealed class SearchFollowUserQueryHandler : IRequestHandler<SearchFollowUserQuery, IEnumerable<SearchFollowUserDto>>
    {
        private readonly IFreeSql _freeSql;
        private readonly ISystemDictionaryRepository _systemDictionaryRepository;

        public SearchFollowUserQueryHandler(IFreeSql freeSql, ISystemDictionaryRepository systemDictionaryRepository)
        {
            _freeSql = freeSql;
            _systemDictionaryRepository = systemDictionaryRepository;
        }

        public async Task<IEnumerable<SearchFollowUserDto>> Handle(SearchFollowUserQuery request, CancellationToken cancellationToken)
        {
            var searchFollowUserDtos = await _freeSql.Select<CusCustomer, CusFollowList, SysUserInfo>()
                .LeftJoin((cc, cf, u) => cc.ParentId == cf.CustomerId || cc.CustomerId == cf.CustomerId)
                .LeftJoin((cc, cf, u) => cf.TrackUser == u.UserId)
                .Where((cc, cf, u) => cc.CrmCustomerId == request.CrmCustomerId)
                .WhereIf(request.Enable, (cc, cf, u) => cf.IsEnabled == request.Enable)
                .ToListAsync((cc, cf, u) =>
                    new SearchFollowUserDto(cf.TrackUser, u.CnName, cf.CaseType, cf.CaseDirection, cf.IsEnabled), cancellationToken);
            foreach (var followUserDto in searchFollowUserDtos)
            {
                followUserDto.caseTypeName = _systemDictionaryRepository.GetChineseKeyValueAsync(
                    new KeyValuePair<string, string>(SystemDictionaryName.CaseType, followUserDto.caseType)).GetAwaiter().GetResult().Value;
                followUserDto.caseDirectionName = _systemDictionaryRepository.GetChineseKeyValueAsync(
                    new KeyValuePair<string, string>(SystemDictionaryName.CaseDirection, followUserDto.caseDirection)).GetAwaiter().GetResult().Value;
            }

            return searchFollowUserDtos;
        }
    }
}

