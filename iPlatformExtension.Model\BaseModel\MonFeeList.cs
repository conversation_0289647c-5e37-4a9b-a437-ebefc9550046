using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mon_fee_list", DisableSyncStructure = true)]
	public partial class MonFeeList {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "a_fee", DbType = "money")]
		public decimal? AFee { get; set; }

		[ Column(Name = "allot_id", StringLength = 50)]
		public string AllotId { get; set; }

		[ Column(Name = "apply_type", StringLength = 50)]
		public string ApplyType { get; set; }

		[ Column(Name = "bill_no", StringLength = 50)]
		public string BillNo { get; set; }

		[ Column(Name = "business_type", StringLength = 50)]
		public string BusinessType { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "case_type", StringLength = 50)]
		public string CaseType { get; set; }

		[ Column(Name = "crm_fee_id", StringLength = 50)]
		public string CrmFeeId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "dept_name", StringLength = 50)]
		public string DeptName { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "fee_id", StringLength = 50)]
		public string FeeId { get; set; }

		[ Column(Name = "fee_name", StringLength = 500)]
		public string FeeName { get; set; }

		[ Column(Name = "has_invoice", StringLength = 50)]
		public string HasInvoice { get; set; }

		[ Column(Name = "head_user_type", StringLength = 50)]
		public string HeadUserType { get; set; }

		[ Column(Name = "input_time")]
		public DateTime? InputTime { get; set; }

		[ Column(Name = "is_same_day", StringLength = 50)]
		public string IsSameDay { get; set; }

		[ Column(Name = "manage_company", StringLength = 50)]
		public string ManageCompany { get; set; }

		[ Column(Name = "o_fee", DbType = "money")]
		public decimal? OFee { get; set; }

		[ Column(Name = "proc_attribute", StringLength = 50)]
		public string ProcAttribute { get; set; }

		[ Column(Name = "processor_id", StringLength = 50)]
		public string ProcessorId { get; set; }

		[ Column(Name = "receive_date")]
		public DateTime? ReceiveDate { get; set; }

		[ Column(Name = "receive_id", StringLength = 50)]
		public string ReceiveId { get; set; }

		[ Column(Name = "receive_type", StringLength = 50)]
		public string ReceiveType { get; set; }

		[ Column(Name = "sales_user_id", StringLength = 50)]
		public string SalesUserId { get; set; }

		[ Column(Name = "t_fee", DbType = "money")]
		public decimal? TFee { get; set; }

		[ Column(Name = "volume_o", StringLength = 50)]
		public string VolumeO { get; set; }

	}

}
