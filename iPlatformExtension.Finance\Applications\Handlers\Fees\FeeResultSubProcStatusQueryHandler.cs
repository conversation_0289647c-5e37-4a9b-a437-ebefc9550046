﻿using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultSubProcStatusQueryHandler(IBaseSubProcStatusRepository subProcStatusRepository)
    : IRequestHandler<FeeResultSubProcStatusQuery>
{
    public async Task Handle(FeeResultSubProcStatusQuery request, CancellationToken cancellationToken)
    {
        foreach (var feeListItemDto in request.FeeResults)
        {
            var subProcStatus = await subProcStatusRepository.GetCacheValueAsync(feeListItemDto.SubProcStatusCode ?? string.Empty);
            feeListItemDto.SubProcStatusName = subProcStatus?.TextZhCn ?? string.Empty;
        }
    }
}