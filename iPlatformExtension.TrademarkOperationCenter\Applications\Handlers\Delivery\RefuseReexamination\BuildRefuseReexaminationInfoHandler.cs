﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.RefuseReexamination;

/// <summary>
/// 驳回复审
/// </summary>
internal sealed class BuildRefuseReexaminationInfoHandler(
    IDeliveryOtherInfoRepository otherInfoRepository, 
    IHttpContextAccessor httpContextAccessor) 
    : IMatchTrademarkProcCommandHandler<BuildOtherInfoCommand>
{
    string IMatchTrademarkProcCommandHandler<BuildOtherInfoCommand>.CtrlProcId => CtrlProcIds.RefuseReexamination;

    IEnumerable<string> IMatchTrademarkProcCommandHandler<BuildOtherInfoCommand>.CaseDirections => [CaseDirection.II];

    async Task IMatchNotificationHandler<BuildOtherInfoCommand>.HandleAsync(BuildOtherInfoCommand command, CancellationToken cancellationToken)
    {
        var procInfo = command.ProcInfo;
        var operatorId = (httpContextAccessor.HttpContext?.User).GetUserId();

        var otherInfo = await otherInfoRepository.GetAsync(procInfo.ProcId, cancellationToken) ?? new DeliOtherInfo()
        {
            ProcId = procInfo.ProcId,
            CreationTime = DateTime.Now,
            Creator = operatorId
        };

        otherInfo.IsSplit = procInfo.IsSplit;
        otherInfo.NominalChangesType = procInfo.NominalChangesType;
        otherInfo.TrademarkNiceClasses = procInfo.TrademarkNiceClasses;
        otherInfo.CtrlProcMark = procInfo.CtrlProcMark;
        otherInfo.ReservationOfSupplementaryMaterial = procInfo.ReservationOfSupplementaryMaterial;
        otherInfo.Updater = operatorId;
        otherInfo.UpdateTime = DateTime.Now;

        await otherInfoRepository.InsertOrUpdateAsync(otherInfo, cancellationToken);
    }
}