﻿using iPlatformExtension.Finance.Applications.Queries.Public;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Service.Interface;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Finance.Controllers;

/// <summary>
/// 系统设置控制器
/// </summary>
[ApiController]
[Route("/api/[controller]")]
public sealed class SettingsController : ControllerBase
{
    private readonly IMediator _mediator;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="mediator">中介者</param>
    public SettingsController(IMediator mediator)
    {
        _mediator = mediator;
    }


    /// <summary>
    /// 获取账务推送数据设置
    /// </summary>
    /// <returns></returns>
    [HttpGet("monDeliConfig")]
    [ResponseCache(Duration = 3600)]
    public Task<MonDeliConfig> GetMonDeliConfigAsync()
    {
        return _mediator.Send(new MonDeliConfigQuery());
    }
}