﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.Public.Applications.Commands.MyTrademarkCase;

/// <summary>
/// 批量更新代理人状态
/// </summary>
/// <param name="Status">t_apply_ready：申请准备中，t_directed_office：已指示外所，t_wait_sure：客户确认中</param>
/// <param name="ProcList">任务id</param>
public record BatchUpdateAgentStatusCommand([Required(ErrorMessage = "代理人状态是必须")] string Status, List<string> ProcList) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

