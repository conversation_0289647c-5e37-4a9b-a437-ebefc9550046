﻿FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
ENV ASPNETCORE_URLS=http://+:8089
ENV ASPNETCORE_ENVIRONMENT=Production
ENV TZ=Asia/Shanghai
ENV DOTNET_GCHeapHardLimit=80000000
ENV DOTNET_DbgEnableMiniDump=1
ENV DOTNET_DbgMiniDumpTyp=4
ENV DOTNET_DbgMiniDumpName=/tmp/iplatform-public-%p-%t.dmp
ENV DOTNET_EnableCrashReport=1
ENV DOTNET_CreateDumpDiagnostics=1
ENV ASPNETCORE_HOSTINGSTARTUPASSEMBLIES=SkyAPM.Agent.AspNetCore
ENV SKYWALKING__SERVICENAME=prod::iplatform-public
WORKDIR /app
EXPOSE 8089

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["iPlatformExtension.Public/iPlatformExtension.Public.csproj", "iPlatformExtension.Public/"]
COPY ["iPlatformExtension.Repository/iPlatformExtension.Repository.csproj", "iPlatformExtension.Repository/"]
COPY ["iPlatformExtension.Common/iPlatformExtension.Common.csproj", "iPlatformExtension.Common/"]
COPY ["iPlatformExtension.Model/iPlatformExtension.Model.csproj", "iPlatformExtension.Model/"]
COPY ["obs_net/esdk_obs_.net_core.csproj", "obs_net/"]
RUN dotnet restore "iPlatformExtension.Public/iPlatformExtension.Public.csproj"
COPY . .
WORKDIR "/src/iPlatformExtension.Public"
RUN dotnet build "iPlatformExtension.Public.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "iPlatformExtension.Public.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
RUN mkdir "logs"
ENTRYPOINT ["dotnet", "iPlatformExtension.Public.dll"]
