﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class ProcPropertiesDynamicConfigRepository(IFreeSql<PlatformFreeSql> freeSql, UnitOfWorkManage<PlatformFreeSql> uowManger)
    : DefaultRepository<ProcPropertiesDynamicConfig, int>(freeSql, uowManger), IProcPropertiesDynamicConfigRepository;