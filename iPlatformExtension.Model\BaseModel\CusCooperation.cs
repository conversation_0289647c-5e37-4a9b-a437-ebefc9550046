using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_cooperation", DisableSyncStructure = true)]
	public partial class CusCooperation {

		[ Column(Name = "cooperation_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CooperationId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "cooperation_code", StringLength = 100)]
		public string CooperationCode { get; set; }

		[ Column(Name = "cooperation_name", StringLength = 200)]
		public string CooperationName { get; set; }

		[ Column(Name = "cooperation_name_en", StringLength = 300)]
		public string CooperationNameEn { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50, IsNullable = false)]
		public string CreateUserId { get; set; }

		[ Column(Name = "email", StringLength = 200)]
		public string Email { get; set; }

		[ Column(Name = "fax", StringLength = 50)]
		public string Fax { get; set; }

		[ Column(Name = "founding_date")]
		public DateTime? FoundingDate { get; set; }

		[ Column(Name = "head_user_id", StringLength = 50)]
		public string HeadUserId { get; set; }

		[ Column(Name = "is_customer")]
		public bool? IsCustomer { get; set; } = true;

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "is_supplier")]
		public bool? IsSupplier { get; set; } = true;

		[ Column(Name = "mobile", StringLength = 50)]
		public string Mobile { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "tel", StringLength = 50)]
		public string Tel { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
