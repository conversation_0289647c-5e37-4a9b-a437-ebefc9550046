﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailService.Applications.Queries;
using iPlatformExtension.MailService.HostedService;
using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.SignalR.Client;

namespace iPlatformExtension.MailService.Applications.Handlers
{
    public class FlowPrivateMessageHandler(ILogger<MailReceiveHostedService> logger, IFreeSql<MailCenterFreeSql> freeSql,
        IConfiguration configuration, IMediator mediator,
        HubConnection hubConnection) : IRequestHandler<FlowPrivateMessageQuery>
    {
        private const int POLLING_INTERVAL_SECONDS = 60;
        
        public async Task Handle(FlowPrivateMessageQuery request, CancellationToken cancellationToken)
        {
            _ = Task.Run(async () =>
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        var lst = freeSql.Select<MailFlowPrivate, FlowPrivateList>().WithLock().InnerJoin(o => o.t1.Id == o.t2.PrivateId)
                            .GroupBy(o => new { o.t1.Id, o.t1.UserId }).Select(o =>
                                new FlowAnalyseDto
                                {
                                    Count = o.Count(),
                                    UserId = o.Value.Item1.UserId,
                                    Type = $"FlowPrivate_{o.Value.Item1.MailType}".ToString(),
                                    Title = o.Value.Item1.Id,
                                });

                        if (hubConnection.State == HubConnectionState.Connected)
                        {
                            await hubConnection.InvokeAsync("NotificationFlowMessageAsync", "MailCenterFlowPrivateMessage", lst, cancellationToken: cancellationToken);
                        }
                        else
                        {
                            Console.WriteLine($"SignalR连接失败:{hubConnection}");
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("邮件中心个人分类计算异常", ex);
                    }
                    finally
                    {
                        await Task.Delay(TimeSpan.FromSeconds(POLLING_INTERVAL_SECONDS), cancellationToken);
                    }
                }
            }, cancellationToken);
        }
    }
}
