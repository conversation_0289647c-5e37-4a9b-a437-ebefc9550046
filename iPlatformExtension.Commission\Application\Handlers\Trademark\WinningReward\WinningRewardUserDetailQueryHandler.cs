﻿using FreeSql;
using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Commission.Application.Notifications.WinningReward;
using iPlatformExtension.Commission.Application.Queries.WinningReward;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class WinningRewardUserDetailQueryHandler(IFreeSql freeSql, IPublisher publisher) 
    : IRequestHandler<WinningRewardUserDetailQuery, IEnumerable<RewardUserDetail>>
{
    public async Task<IEnumerable<RewardUserDetail>> Handle(WinningRewardUserDetailQuery request, CancellationToken cancellationToken)
    {
        var (userId, year, month, procNo, volume) = request;
        var result = await freeSql.Select<WinningRewardProc, WinningRewardUser>().WithLock()
            .LeftJoin((proc, user) => proc.ProcId == user.ProcId)
            .Where((proc, user) => user.UserId == userId && proc.Year == year && proc.Month == month)
            .WhereIf(!string.IsNullOrWhiteSpace(procNo), (proc, user) => proc.ProcNo.Contains(procNo!))
            .WhereIf(!string.IsNullOrWhiteSpace(volume), (proc, user) => proc.Volume.Contains(volume!))
            .ToListAsync((proc, user) => new RewardUserDetail
            {
                ProcId = proc.ProcId,
                Volume = proc.Volume,
                AppNo = proc.AppNo,
                ProcNo = proc.ProcNo,
                CaseName = proc.CaseName,
                CtrlProcName = proc.CtrlProcId,
                CustomerName = proc.CustomerName,
                RulingResult = proc.RulingResult,
                SituationChanged = SqlExt.Case()
                    .When(proc.SituationChanged == true, "是")
                    .When(proc.SituationChanged == false, "否")
                    .Else(string.Empty).End(),
                BeneficiaryName = user.CnName,
                BeneficiaryReward = user.EditedReward ?? user.Reward,
                RewardBeneficiaryType = user.BeneficiaryType,
                CommissionDate = new DateOnly(proc.CommissionDate.Year, proc.CommissionDate.Month,
                    proc.CommissionDate.Day),
                PushedStatus = SqlExt.Case()
                    .When(proc.Pushed == true, "已推送")
                    .When(proc.Pushed == false, "未推送")
                    .Else(string.Empty).End()
            }, cancellationToken);

        await publisher.Publish(new RewardUserDisplayNotification(result), cancellationToken);
        
        return result;
    }
}