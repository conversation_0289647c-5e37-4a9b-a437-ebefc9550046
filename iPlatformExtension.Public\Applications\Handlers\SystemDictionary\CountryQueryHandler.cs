﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.SystemDictionary;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.SystemDictionary;

internal sealed class CountryQueryHandler(IFreeSql freeSql) : IRequestHandler<CountryQuery, IEnumerable<CountryInfoDto>>
{
    public async Task<IEnumerable<CountryInfoDto>> Handle(CountryQuery request, CancellationToken cancellationToken)
    {
        var (keyword, countryId) = request;
        return await freeSql.Select<BasCountry>().WithLock()
            .WhereIf(!string.IsNullOrWhiteSpace(keyword),
                country => country.CountryZhCn.Contains(keyword!) || country.CountryEnUs.Contains(keyword!))
            .WhereIf(!string.IsNullOrWhiteSpace(countryId), country => country.CountryId == countryId)
            .ToListAsync(country => new CountryInfoDto(country.CountryId, country.CountryZhCn, country.CountryEnUs)
                {
                    CountryCallingCode = country.CountryCallingCode,
                    ContinentCn = country.ContinentCn
                },
                cancellationToken);
    }
}