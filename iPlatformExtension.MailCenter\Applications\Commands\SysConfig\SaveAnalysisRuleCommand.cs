﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.SysConfig;

/// <summary>
/// 保存分析规则
/// </summary>
/// <param name="ConfigId">主键id</param>
/// <param name="ConfigName">规则名称</param>
/// <param name="ConfigRemark">办理意见</param>
/// <param name="IsEnable">1有效2无效</param>
/// <param name="ConfigType">分拣类型,Finish:分拣到承办人,Allot:移入待分拣,Ignore:忽略</param>
/// <param name="HandUser">分拣人</param>
/// <param name="HostId">邮箱id,多个用;分割</param>
/// <param name="ReadUser">阅读人</param>
/// <param name="IgnoreUser">忽略人</param>
/// <param name="Remark">备注</param>
/// <param name="UndertakeUser">承办人</param>
/// <param name="AnalysisRuleDetailList">规则详情</param>
/// <param name="RuleNumber">规则编号</param>
public record SaveAnalysisRuleCommand(string? ConfigId, [Required] string ConfigName, [Required] int IsEnable, string? ConfigRemark, string? ConfigType, string? HandUser,
     string? HostId, string? ReadUser, string? Remark, string? UndertakeUser,string? IgnoreUser ,string? RuleNumber, List<AnalysisRuleDetail>? AnalysisRuleDetailList) : IRequest, IUnitOfWorkCommandMysql;

