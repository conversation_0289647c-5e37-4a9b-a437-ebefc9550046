﻿using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using iPlatformExtension.Outsourcing.Infrastructure.Constants;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class NotAssignedCountCommandHandler(ISender sender) 
    : IRequestHandler<NotAssignedCountCommand, NotAssignedCountDto>
{
    public async Task<NotAssignedCountDto> Handle(NotAssignedCountCommand request, CancellationToken cancellationToken)
    {
        var authorizationType = request.AuthorizationType;

        if (authorizationType.StartsWith(AuthorizationType.TrademarkOutsourcing))
        {
            var counts = await Task.WhenAll(
                sender.Send(new NotAssignedCountQuery(AuthorizationType.TrademarkOutsourcing), cancellationToken), 
                sender.Send(new NotAssignedCountQuery(AuthorizationType.TrademarkOutsourcingAll), cancellationToken));
            return new NotAssignedCountDto()
            {
                PersonalCount =counts[0],
                WholeCount = counts[1]
            };
        }
        if (authorizationType.StartsWith(AuthorizationType.PatentOutsourcing))
        {
            var counts = await Task.WhenAll(
                sender.Send(new NotAssignedCountQuery(AuthorizationType.PatentOutsourcing), cancellationToken), 
                sender.Send(new NotAssignedCountQuery(AuthorizationType.PatentOutsourcingAll), cancellationToken));
            return new NotAssignedCountDto()
            {
                PersonalCount =counts[0],
                WholeCount = counts[1]
            };
        }

        return new NotAssignedCountDto();
    }
}