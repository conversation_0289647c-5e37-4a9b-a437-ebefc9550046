﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Validation.Attributes;
using iPlatformExtension.Public.Applications.Commands.Proc;
using iPlatformExtension.Public.Applications.Models.Proc;
using iPlatformExtension.Public.Applications.Queries.Proc;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 费用名称控制器
/// </summary>
[ApiController]
[Route("fee-name")]
public class FeeNameController(ISender sender) : ControllerBase
{
    /// <summary>
    /// 获取费用名称关联的任务信息
    /// </summary>
    /// <param name="feeNameId">费用名称ID</param>
    /// <param name="keyword">关键字</param>
    /// <returns>任务信息列表</returns>
    [HttpGet("{feeNameId}/ctrl-proc-info")]
    public Task<IEnumerable<FeeNameCtrlProcInfo>> GetFeeNameCtrlProcInfoAsync(
        [FromRoute, Required] string feeNameId,
        [FromQuery] string keyword = "")
    {
        return sender.Send(new FeeNameCtrlProcQuery(feeNameId, keyword), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 添加费用名称关联的任务信息
    /// </summary>
    /// <param name="feeNameId">费用名称ID</param>
    /// <param name="ctrlProcIds">任务名称id列表</param>
    /// <returns>同一响应接口</returns>
    [HttpPost("{feeNameId}/ctrl-proc-info")]
    public Task AddFeeNameCtrlProcInfoAsync(
        [FromRoute, Required] string feeNameId, 
        [FromBody, Required, MinLength(1, ErrorMessage = "至少选择一个任务名称"), NoEmptyItems(ErrorMessage = "任务名称不能为空")] 
        IEnumerable<string> ctrlProcIds)
    {
        return sender.Send(new CreateFeeNameCtrlProcCommand(feeNameId, ctrlProcIds), HttpContext.RequestAborted);
    }
    
    /// <summary>
    /// 删除费用名称关联的任务信息
    /// </summary>
    /// <param name="feeNameId">费用名称ID</param>
    /// <param name="ctrlProcId">任务名称id</param>
    /// <returns>同一响应接口</returns>
    [HttpDelete("{feeNameId}/ctrl-proc-info/{ctrlProcId}")]
    public Task DeleteFeeNameCtrlProcInfoAsync(
        [FromRoute, Required] string feeNameId,
        [FromRoute, Required] string ctrlProcId)
    {
        return sender.Send(new DeleteFeeNameCtrlProcCommand(feeNameId, ctrlProcId), HttpContext.RequestAborted);
    }
}