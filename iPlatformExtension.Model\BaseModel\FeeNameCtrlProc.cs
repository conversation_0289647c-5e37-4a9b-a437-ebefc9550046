﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 费项名称任务名称关联
/// </summary>
[Table(Name = "fee_name_ctrl_proc")]
public class FeeNameCtrlProc
{
    /// <summary>
    /// 费项名称id
    /// </summary>
    [Column(IsPrimary = true, Name = "fee_name_id", StringLength = 50, IsNullable = false)]
    public string FeeNameId { get; set; } = null!;

    /// <summary>
    /// 任务名称id
    /// </summary>
    [Column(IsPrimary = true, Name = "ctrl_proc_id", StringLength = 50, IsNullable = false)]
    public string CtrlProcId { get; set; } = null!;
}