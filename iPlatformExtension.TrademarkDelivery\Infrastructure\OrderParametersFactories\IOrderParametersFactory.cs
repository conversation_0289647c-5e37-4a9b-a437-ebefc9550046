﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;

namespace iPlatformExtension.TrademarkDelivery.Infrastructure.OrderParametersFactories;

public interface IOrderParametersFactory
{
    Task<PhoenixOrderRequestParameters> CreateAsync(CaseProcInfo procInfo, CancellationToken cancellationToken);
}