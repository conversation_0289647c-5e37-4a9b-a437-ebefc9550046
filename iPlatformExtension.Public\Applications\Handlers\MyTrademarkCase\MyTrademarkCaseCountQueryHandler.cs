﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;
using Microsoft.AspNetCore.Http;
using System.Linq;

namespace iPlatformExtension.Public.Applications.Handlers.MyTrademarkCase
{
    /// <summary>
    /// 
    /// </summary>
    internal sealed class MyTrademarkCaseCountQueryHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor, ISystemDictionaryRepository systemDictionaryRepository)
        : IRequestHandler<MyTrademarkCaseCountQuery, MyTrademarkCaseCountDto>
    {
        private static readonly IReadOnlyList<string> foreignCaseDirections = [CaseDirection.IO, CaseDirection.OO];
        
        public async Task<MyTrademarkCaseCountDto> Handle(MyTrademarkCaseCountQuery request, CancellationToken cancellationToken)
        {
            var ctrlProcList = await freeSql.Select<SysDictionary, SysPaperTab>().LeftJoin(it => it.t1.DictionaryId == it.t2.DictionaryId)
                .WhereIf(request.PrivateValue != "BasicOther", it => it.t1.Value == request.PrivateValue)
                .WhereIf(request.PrivateValue == "BasicOther", it => it.t1.Value != request.PrivateValue && it.t1.DictionaryName == "trademark_tab_out")
                .ToListAsync(it => it.t2.CtrlProcId, cancellationToken);

            //获取操作用户信息
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var procPrivateCount = freeSql.Select<CaseProcInfo, SysFlowPrivate, CaseInfo>()
                .LeftJoin(it => it.t1.PrivateId == it.t2.PrivateId && it.t2.UserId == userId)
                .LeftJoin(it => it.t1.CaseId == it.t3.Id)
                .WhereIf(request.PrivateValue != "BasicOther", it => ctrlProcList.Contains(it.t1.CtrlProcId) && (it.t3.CaseDirection == "IO" || it.t3.CaseDirection == "OO"))
                .WhereIf(request.PrivateValue == "BasicOther", it => !ctrlProcList.Contains(it.t1.CtrlProcId) && (it.t3.CaseDirection == "IO" || it.t3.CaseDirection == "OO"))
.Where(it => it.t3.CaseTypeId == "T" && it.t1.FinishDate == null
         && !new List<string>() { CASE_PROC_STATUS.WC, CASE_PROC_STATUS.BCL, CASE_PROC_STATUS.YCL }.Contains(it.t1.ProcStatusId)
         && (it.t1.OutUser == null || it.t1.OutUser == ""))
                .Where(it => it.t1.UndertakeUserId == userId).WithLock()
                .Where(it => !freeSql.Select<CaseProcInfo>()
                    .Where(info => foreignCaseDirections.Contains(info.CaseInfo.CaseDirection))
                    .Where(info => info.BasCtrlProc.IsOutsourcing == true)
                    .Where(info => string.IsNullOrWhiteSpace(info.ForeginAgencyId))
                    .Where(info => info.ProcId == it.t1.ProcId)
                    .Any())
                .GroupBy(it => new { it.t1.UndertakeUserId, it.t2.PrivateId, it.t2.PrivateName }).Select(o =>
                    new TrademarkCaseCountDto(o.Value.Item2.PrivateId ?? "PENDING", o.Count()));

            //出口标签统计
            var group = freeSql.Select<CaseProcInfo, CaseInfo, SysPaperTab, SysDictionary, SysFlowPrivate>()
                .LeftJoin(it => it.t1.CaseId == it.t2.Id)
                .LeftJoin(it => it.t1.CtrlProcId == it.t3.CtrlProcId && (it.t2.CaseDirection == it.t3.CaseDirection))
                .LeftJoin(it => it.t3.DictionaryId == it.t4.DictionaryId)
                .LeftJoin(it => it.t1.PrivateId == it.t5.PrivateId && it.t5.UserId == userId)
                .Where(it => it.t2.CaseTypeId == "T" && it.t1.FinishDate == null
                                                     && !new List<string>()
                                                     {CASE_PROC_STATUS.WC, CASE_PROC_STATUS.BCL, CASE_PROC_STATUS.YCL}.Contains(it.t1.ProcStatusId)
                                                     && (it.t1.OutUser == null || it.t1.OutUser == ""))
                .Where(it => it.t1.UndertakeUserId == userId && (it.t4.DictionaryId == null || it.t4.DictionaryName == "trademark_tab_out"))
                .Where(it => it.t2.CaseDirection == "IO" || it.t2.CaseDirection == "OO")
                .Where(it => !freeSql.Select<CaseProcInfo>()
                    .Where(info => foreignCaseDirections.Contains(info.CaseInfo.CaseDirection))
                    .Where(info => info.BasCtrlProc.IsOutsourcing == true)
                    .Where(info => string.IsNullOrWhiteSpace(info.ForeginAgencyId))
                    .Where(info => info.ProcId == it.t1.ProcId)
                    .Any())
                .WithLock()
                .GroupBy(it => new { it.t3.DictionaryId, it.t4.Value, it.t1.ProcStatusId });

            //状态分组
            var trademarkTypeCountDtos = await group.Select(it =>
                    new TrademarkTypeCountDto(it.Value.Item4.Value, it.Value.Item1.ProcStatusId, it.Count(), null))
                .ToAsyncEnumerable().SelectAwait(async trademarkTypeCountDto =>
            {
                if (!string.IsNullOrWhiteSpace(trademarkTypeCountDto.ProcStatusId))
                {
                    var chineseKeyValueAsync = await systemDictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.ProcStatus, trademarkTypeCountDto.ProcStatusId);
                    trademarkTypeCountDto.ProcStatusName = chineseKeyValueAsync.Value;
                }
                return trademarkTypeCountDto;
            }).ToListAsync(cancellationToken: cancellationToken);
            //统一分组
            var procCount = trademarkTypeCountDtos.GroupBy(it => new { it.Title })
                .Select(it => new TrademarkCaseOutCountDto(it.Key.Title, it.Sum(x => x.Count))).ToList();
            procCount.ForEach(it => it.StatusCountList = trademarkTypeCountDtos.Where(o => o.Title == it.Title).ToList());

            if (procCount.Any(it => it.Title == "BasicOther"))
            {
                foreach (var pCount in procCount)
                {
                    if (pCount.Title != "BasicOther") continue;
                    var trademarkCaseCountDto = procCount.Where(it => string.IsNullOrWhiteSpace(it.Title)).ToList();
                    pCount.StatusCountList?.AddRange(trademarkTypeCountDtos
                        .Where(it => string.IsNullOrWhiteSpace(it.Title))
                        .Select(it => new TrademarkTypeCountDto("BasicOther", it.ProcStatusId, it.Count, it.ProcStatusName)).ToList());
                    pCount.StatusCountList = pCount.StatusCountList?.GroupBy(it => new { it.Title, it.ProcStatusId, it.ProcStatusName })
                        .Select(it => new TrademarkTypeCountDto(it.Key.Title, it.Key.ProcStatusId, it.Sum(x => x.Count), it.Key.ProcStatusName)).ToList();
                    if (trademarkCaseCountDto.Count == 0) continue;
                    foreach (var caseCountDto in trademarkCaseCountDto)
                    {
                        pCount.Count += caseCountDto.Count;
                    }
                }
            }
            else
            {
                procCount.ForEach(it =>
                {
                    if (!string.IsNullOrWhiteSpace(it.Title)) return;
                    it.Title = "BasicOther";
                    it.StatusCountList ??= trademarkTypeCountDtos.Where(it => string.IsNullOrWhiteSpace(it.Title))
                        .Select(it => new TrademarkTypeCountDto("BasicOther", it.ProcStatusId, it.Count)).ToList();
                });

            }

            return new MyTrademarkCaseCountDto(procCount.Where(it => it.Title != null).ToList(), procPrivateCount);
        }
    }
}

