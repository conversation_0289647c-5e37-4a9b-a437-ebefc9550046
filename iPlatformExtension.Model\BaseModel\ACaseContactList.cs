using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_case_contact_list", DisableSyncStructure = true)]
	public partial class ACaseContactList {

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "contact_id", StringLength = 50)]
		public string ContactId { get; set; }

		[ Column(Name = "id", StringLength = 50)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

	}

}
