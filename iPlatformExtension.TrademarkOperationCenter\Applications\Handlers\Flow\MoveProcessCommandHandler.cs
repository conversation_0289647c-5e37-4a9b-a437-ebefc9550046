﻿using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    /// <summary>
    /// 流程移入标签处理者
    /// </summary>
    internal sealed class MoveProcessCommandHandler : IRequestHandler<MoveProcessCommand>
    {
        private readonly IFreeSql _freeSql;
        private readonly IFlowActivityRepository _sysFlowActivityRepository;

        public MoveProcessCommandHandler(IFreeSql freeSql, IFlowActivityRepository sysFlowActivityRepository)
        {
            _freeSql = freeSql;
            _sysFlowActivityRepository = sysFlowActivityRepository;
        }

        public async Task Handle(MoveProcessCommand request, CancellationToken cancellationToken)
        {
            var flowActivityList = await _sysFlowActivityRepository.Where(it => request.Ids.Contains(it.ActivityId)).ToListAsync(cancellationToken);
            flowActivityList.ForEach(it => it.PrivateId = request.PrivateId);
            await _sysFlowActivityRepository.UpdateAsync(flowActivityList, cancellationToken);
        }
    }
}

