﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.File;

internal sealed record ProcApplicantCaseFileDescriptionQuery(
    string TrademarkDeliveryBusinessType,
    IEnumerable<FileListDto> Files) :
    IMatchTrademarkDeliveryBusinessTypeNotification
{
    public IEnumerable<FileListDto> Files { get; set; } = Files;
}