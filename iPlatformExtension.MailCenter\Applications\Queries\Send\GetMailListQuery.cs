using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.MailCenter.Applications.Queries.Send;

/// <summary>
/// 获取邮件列表查询
/// </summary>
public class GetMailListQuery : IRequest<PageResult<GetMailListDto>>
{
    /// <summary>
    /// 页码
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "页码必须大于0")]
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// 每页大小
    /// </summary>
    [Range(1, 100, ErrorMessage = "每页大小必须在1-100之间")]
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// 发件编号
    /// </summary>
    public string? MailNo { get; set; }

    /// <summary>
    /// 邮件主题
    /// </summary>
    public string? MailSubject { get; set; }

    /// <summary>
    /// 发件人
    /// </summary>
    public string? MailFrom { get; set; }

    /// <summary>
    /// 收件人
    /// </summary>
    public string? MailTo { get; set; }

    /// <summary>
    /// 抄送人
    /// </summary>
    public string? MailCc { get; set; }

    /// <summary>
    /// 密送人
    /// </summary>
    public string? MailBcc { get; set; }

    /// <summary>
    /// 发件开始日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 发件结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 创建开始日期
    /// </summary>
    public DateTime? CreateTimeStartDate { get; set; }

    /// <summary>
    /// 创建结束日期
    /// </summary>
    public DateTime? CreateTimeEndDate { get; set; }

    /// <summary>
    /// 作废开始日期
    /// </summary>
    public DateTime? DiscardStartDate { get; set; }

    /// <summary>
    /// 作废结束日期
    /// </summary>
    public DateTime? DiscardEndDate { get; set; }

    /// <summary>
    /// 阅读人
    /// </summary>
    public List<string>? Reader { get; set; }

    /// <summary>
    /// 承办人
    /// </summary>
    public List<string>? UndertakeUser { get; set; }

    /// <summary>
    /// 审核人
    /// </summary>
    public List<string>? AuditUser { get; set; }
    
    /// <summary>
    /// 邮件状态，默认为草稿状态,-1:取消,0:草稿,1:审核中,2:定时发送,3:待发送,4:发送失败,5:已经发送,6:已作废;500:出错
    /// </summary>
    public int Status { get; set; } = SysEnum.SendStatusType.Draft.GetHashCode();

}

/// <summary>
/// 邮件列表项DTO
/// </summary>
public class GetMailListDto
{
    /// <summary>
    /// 邮件ID
    /// </summary>
    public string MailId { get; set; } = string.Empty;

    /// <summary>
    /// 文件编号
    /// </summary>
    public string FileNumber { get; set; } = string.Empty;

    /// <summary>
    /// 邮件主题
    /// </summary>
    public string MailSubject { get; set; } = string.Empty;

    /// <summary>
    /// 发件人
    /// </summary>
    public string MailFrom { get; set; } = string.Empty;

    /// <summary>
    /// 发件人邮箱
    /// </summary>
    public string MailFromEmail { get; set; } = string.Empty;

    /// <summary>
    /// 要求发送时间,先取值IsRequiredProcessTime，为true是为定时发送取该时间，为false时为立即发送
    /// </summary>
    public DateTime? RequiredProcessTime { get; set; }

    /// <summary>
    /// 是否定时发送
    /// </summary>
    public bool? IsRequiredProcessTime { get; set; }

    /// <summary>
    /// 接收时间
    /// </summary>
    public DateTime? MailDate { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 状态名称
    /// </summary>
    public string StatusName { get; set; } = string.Empty;

    /// <summary>
    /// 承办人
    /// </summary>
    public string? UndertakeUserId { get; set; } = string.Empty;

    /// <summary>
    /// 承办人
    /// </summary>
    public object? UndertakeUser { get; set; } = string.Empty;

    /// <summary>
    /// 审核时间
    /// </summary>
    public DateTime? AuditTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 审核人id
    /// </summary>
    /// <value></value>
    public string? AuditUserId { get; set; }

    /// <summary>
    /// 审核人
    /// </summary>
    /// <value></value>
    public object? AuditUser { get; set; }
}
