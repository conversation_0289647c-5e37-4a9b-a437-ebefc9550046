﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Apply;

namespace iPlatformExtension.Outsourcing.Infrastructure.Mappers;

internal sealed class ApplyMapper : Profile
{
    public ApplyMapper()
    {
        CreateMap<ApplyCasePatchDto, AppCaseList>()
            .ForMember(list => list.ForeginAgencyId, opt => opt.MapFrom(src => src.SupplierId))
            .ForMember(list => list.ForeginAgencyRemark, opt => opt.MapFrom(src => src.ForeignSupplierRemark));

        CreateMap<AppCaseList, ApplyCasePatchDto>()
            .ForMember(dto => dto.SupplierId, expression => expression.MapFrom(list => list.ForeginAgencyId))
            .ForMember(dto => dto.ForeignSupplierRemark,
                expression => expression.MapFrom(list => list.ForeginAgencyRemark));
    }
}