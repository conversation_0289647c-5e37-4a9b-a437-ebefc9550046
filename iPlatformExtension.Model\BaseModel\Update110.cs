using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "update_110", DisableSyncStructure = true)]
	public partial class Update110 {

		[ Column(Name = "crm_customer_code", StringLength = 50)]
		public string CrmCustomerCode { get; set; }

		[ Column(Name = "crm_customer_id", StringLength = 50)]
		public string CrmCustomerId { get; set; }

		[ Column(Name = "customer_code", StringLength = 50)]
		public string CustomerCode { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

	}

}
