﻿namespace iPlatformExtension.Public.Applications.Models.Customer;

/// <summary>
/// 申请人搜索
/// </summary>
/// <param name="ApplicantId">申请人id</param>
/// <param name="ApplicantNameCn">申请人中文名称</param>
/// <param name="ApplicantNameEn">申请人英文名称</param>
/// <param name="TypeId">申请人类型id</param>
/// <param name="ApplicantTypeZhCn">申请人类型</param>
/// <param name="CountryZhCn">国籍</param>
/// <param name="CountryEnUs">国籍英文</param>
/// <param name="CardType">证件类型</param>
/// <param name="TextZhCn">证件类型中文名称</param>
/// <param name="CardNo">证件号码</param>
/// <param name="AddressCn">地址中文</param>
/// <param name="AddressEn">地址英文</param>
public record SearchApplicantDto(string CustomerId, string ApplicantId, string ApplicantNameCn, string ApplicantNameEn,string TypeId,
    string ApplicantTypeZhCn, string CountryZhCn,string CountryEnUs, string CardType, string TextZhCn, string CardNo)
{
   public List<Address> Addresses { get; set; }
};