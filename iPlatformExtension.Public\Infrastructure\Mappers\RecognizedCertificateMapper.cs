﻿using System.Text.Json;
using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;

namespace iPlatformExtension.Public.Infrastructure.Mappers
{
    /// <summary>
    /// 公认证mapper
    /// </summary>
    public sealed class RecognizedCertificateMapper : Profile
    {
        /// <summary>
        /// 构造
        /// </summary>
        public RecognizedCertificateMapper()
        {
            CreateMap<AuthenticationRulesDto, RecognizedCertificateConfig>()
                .ForMember((target) => target.CountryIds,
                    config => config.ConvertUsing(new CountryFormatter(), nameof(AuthenticationRulesDto.CountryIds)));

            CreateMap<Holidays, SysHoliday>();
        }
    }
    /// <summary>
    /// 国家转换
    /// </summary>
    public class CountryFormatter : IValueConverter<List<string>, string>
    {
        /// <inheritdoc />
        public string Convert(List<string> sourceMember, ResolutionContext context) =>
            JsonSerializer.Serialize(sourceMember);

    }

}
