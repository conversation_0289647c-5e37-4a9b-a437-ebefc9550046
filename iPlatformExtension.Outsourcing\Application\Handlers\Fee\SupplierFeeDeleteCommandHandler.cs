﻿using iPlatformExtension.Outsourcing.Application.Commands.Fee;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Fee;

internal sealed class SupplierFeeDeleteCommandHandler(IForeignSupplierFeeRepository foreignSupplierFeeRepository) 
    : IRequestHandler<SupplierFeeDeleteCommand>
{
    public Task Handle(SupplierFeeDeleteCommand request, CancellationToken cancellationToken)
    {
        var feesId = request.FeesId;
        return foreignSupplierFeeRepository.DeleteAsync(feesId);
    }
}