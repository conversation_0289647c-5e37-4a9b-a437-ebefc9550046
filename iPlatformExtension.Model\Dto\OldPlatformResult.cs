﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 旧系统返回的统一封装
/// </summary>
/// <typeparam name="T"></typeparam>
public class OldPlatformResult<T>
{
    /// <summary>
    /// 追踪号
    /// </summary>
    [JsonPropertyName("traceId")]
    public string TraceIdentifier { get; set; } = null!;
        
    /// <summary>
    /// 成功标识
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// 响应码
    /// </summary>
    [JsonPropertyName("code")]
    public ResponseCode Code { get; set; }

    /// <summary>
    /// 业务数据
    /// </summary>
    [JsonPropertyName("data")]
    public T Data { get; set; }

    /// <summary>
    /// 信息
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = "操作成功";
}

public enum ResponseCode
{
    Success = 0,
    AuthenticationFailed = -2,
    BusinessFailed = -1,
    SystemError = -500
}