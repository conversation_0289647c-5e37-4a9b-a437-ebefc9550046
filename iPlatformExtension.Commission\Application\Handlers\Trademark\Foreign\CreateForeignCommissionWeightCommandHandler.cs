using Google.Protobuf.WellKnownTypes;
using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;
using iPlatformExtension.Commission.Clients;
using iPlatformExtension.Commission.Infrastructure.Constant;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class CreateForeignCommissionWeightCommandHandler(
    ISender sender,
    IFreeSql<PlatformFreeSql> freeSql
#if !DEBUG
    ,PeriodSettingService.PeriodSettingServiceClient client
#endif
    ) 
    : IRequestHandler<CreateForeignCommissionWeightCommand>
{
    
    public async Task Handle(CreateForeignCommissionWeightCommand request, CancellationToken cancellationToken)
    {
        var endDate = DateTime.Today;
#if !DEBUG
        var settings = (await client.getPeriodSettingListAsync(new Empty(), cancellationToken:cancellationToken)).PeriodSettings;
        
        var todaySetting = settings.FirstOrDefault(setting => setting.Month == endDate.Month);
        if (todaySetting == null)
        {
            return;
        }
        
        var startDateMonth = endDate.Day <= todaySetting.DeadlinePushingPermissionData
            ? endDate.AddMonths(-1)
            : endDate;
        
        var startDate = new DateTime(startDateMonth.Year, startDateMonth.Month, 1);
#endif

#if DEBUG
        var startMonth = endDate.AddMonths(-1);
        var startDate = new DateTime(startMonth.Year, startMonth.Month, 1);
#endif
        
        var procIds = await freeSql.Select<CaseProcInfo>().WithLock()
            .InnerJoin<SysDeptUser>((info, deptUser) => info.UndertakeUserId == deptUser.UserId && deptUser.IsDefault == true)
            .Where(info => startDate <= info.CommissionEffectiveDate && endDate > info.CommissionEffectiveDate)
            .Where(info => CaseDirections.ForeignCaseDirections.Contains(info.CaseInfo.CaseDirection))
            .Where(info => info.CaseInfo.CaseTypeId == CaseType.Trade)
            .Where(info => info.IsEnabled == true)
            .Where(info => !freeSql.Select<RpForeignTrademarkBonus>().Any(commission => commission.ProcId == info.ProcId))
            .ToListAsync(info => info.ProcId, cancellationToken);
        
        foreach (var procId in procIds)
        {
            await sender.Send(new CreateForeignCommissionCommand(procId), cancellationToken);
        }
    }
}