﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;

namespace iPlatformExtension.Outsourcing.Application.Commands.Proc;

internal sealed record UpdateProcCommand(string ProcId, int Version, JsonPatchDocument<ProcPatchDto> Document, bool IsBatch = false) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;