﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Apply;
using iPlatformExtension.Outsourcing.Application.Queries.Apply;
using iPlatformExtension.Outsourcing.Infrastructure.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Apply;

internal sealed class AppCaseOutsourcingQueryHandler(
    IFreeSql<PlatformFreeSql> freeSql,
    IHttpContextAccessor httpContextAccessor,
    IForeignSupplierRepository foreignSupplierRepository) 
    : IRequestHandler<AppCaseOutsourcingQuery, AppCaseOutsourcingDto>
{
    public async Task<AppCaseOutsourcingDto> Handle(AppCaseOutsourcingQuery request, CancellationToken cancellationToken)
    {
        var appCaseId = request.AppCaseId;
        var appCaseList = await freeSql.Select<AppCaseList>(appCaseId).WithLock().ToOneAsync(cancellationToken);
        var currentUserId = httpContextAccessor.HttpContext?.User.GetUserId();

        if (appCaseList is null)
        {
            throw new NotFoundException(appCaseId, "开案案件信息");
        }

        var dto = new AppCaseOutsourcingDto
        {
            AppCaseId = appCaseId,
            ForeignAgencyRemark = appCaseList.ForeginAgencyRemark,
            Editable = currentUserId is not null && appCaseList.CreateUserId == currentUserId
        };

        var foreignAgencyId = appCaseList.ForeginAgencyId;
        if (string.IsNullOrEmpty(foreignAgencyId))
            return dto;
        
        var foreignSupplier = await foreignSupplierRepository.GetCacheValueAsync(foreignAgencyId, cancellationToken: cancellationToken);
        if (foreignSupplier is not null)
        {
            dto.AgencyInfo = foreignSupplier.CreateForeignAgencyInfo();
        }
        
        return dto;
    }
}