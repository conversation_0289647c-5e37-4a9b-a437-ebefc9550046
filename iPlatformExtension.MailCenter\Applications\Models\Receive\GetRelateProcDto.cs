﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 获取关联任务
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="CaseId">案件id</param>
/// <param name="CtrlProcId">任务名称id</param>
/// <param name="CtrlProc">任务名称</param>
/// <param name="ProcNo">任务编号</param>
/// <param name="UndertakeUserId">承办人id</param>
/// <param name="UndertakeUser">承办人</param>
/// <param name="Volume">案卷号</param>
/// <param name="CaseTypeId">案件类型</param>
/// <param name="CaseDirection">案件流向</param>
/// <param name="CaseName">案件名称</param>
/// <param name="CreateBy">关联人</param>
/// <param name="CreateTime">关联时间</param>
/// <param name="RelateId">关联id</param>
/// <param name="ProcStatus">任务状态</param>
/// <param name="CtrlProcMark">任务标识</param>
/// <param name="CtrlProcProperty">任务属性</param>
public record GetRelateProcDto(
    string ProcId,
    string CaseId,
    string CtrlProcId,
    string ProcNo,
    string? UndertakeUserId,
    string Volume,
    string CaseTypeId,
    string CaseDirection,
    string CaseName,
    string ProcStatusId,
    string? CtrlProcMarkId,
    string? CtrlProcPropertyId
)
{
    public object UndertakeUser { get; set; }
    public DateTime? CreateTime { get; set; }
    public object CreateBy { get; set; }
    public object CtrlProc { get; set; }
    public object ProcStatus { get; set; }
    public string RelateId { get; set; }

    public object CtrlProcMark { get; set; }
    public object CtrlProcProperty { get; set; }
};
