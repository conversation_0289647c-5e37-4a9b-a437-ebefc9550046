using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_das_info", DisableSyncStructure = true)]
	public partial class CaseDasInfo {

		[ Column(Name = "agency", StringLength = 50, IsNullable = false)]
		public string Agency { get; set; }

		[ Column(Name = "app_no", StringLength = 50)]
		public string AppNo { get; set; }

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "das_code", StringLength = 50)]
		public string DasCode { get; set; }

		[ Column(Name = "detail", StringLength = 300)]
		public string Detail { get; set; }

		[ Column(Name = "requst_date", StringLength = 50)]
		public string RequstDate { get; set; }

		[ Column(Name = "result", StringLength = 200)]
		public string Result { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

	}

}
