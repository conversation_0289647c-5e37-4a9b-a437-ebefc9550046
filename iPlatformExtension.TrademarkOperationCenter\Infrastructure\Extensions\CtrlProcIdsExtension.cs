﻿using iPlatformExtension.Model.Constants;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;

/// <summary>
/// 任务名称id扩展
/// </summary>
public static class CtrlProcIdsExtension
{
    private static readonly IEnumerable<string> automaticDeliveryCtrlProcIds =
    [
        CtrlProcIds.LicenseFiling,
        CtrlProcIds.NominalChange,
        CtrlProcIds.RefuseReexamination,
        CtrlProcIds.TrademarkAnnulment,
        CtrlProcIds.TrademarkRegistration,
        CtrlProcIds.TrademarkObjections,
        CtrlProcIds.TrademarkRenewal,
        CtrlProcIds.TrademarkTransfer,
        CtrlProcIds.TrademarkWithdraw3Years
    ];

    /// <summary>
    /// 该任务是否是自动递交的任务
    /// </summary>
    /// <param name="ctrlProcId">任务名称id</param>
    /// <returns>该任务支持自动的递交返回<c>true</c>否则返回<c>false</c></returns>
    public static bool IsAutomaticDeliveryCtrlProcId(this string ctrlProcId)
    {
        return automaticDeliveryCtrlProcIds.Contains(ctrlProcId);
    }
}