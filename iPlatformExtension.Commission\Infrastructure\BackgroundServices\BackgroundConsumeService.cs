﻿using System.Threading.Channels;
using iPlatformExtension.Commission.Infrastructure.Models;
using MediatR;

namespace iPlatformExtension.Commission.Infrastructure.BackgroundServices;

internal abstract class BackgroundConsumeService<TCommand>(
    Channel<TCommand> channel, 
    ILogger logger, 
    IServiceScopeFactory serviceScopeFactory) 
    : BackgroundService where TCommand : class, IBackgroundTracingCommand, IRequest
{

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var reader = channel.Reader;

        await foreach (var command in reader.ReadAllAsync(stoppingToken))
        {
            var operationName = command.OperationName;
            
            using var loggingScope = logger.BeginScope(operationName);
            logger.LogInformation("开始执行{OperationName}", operationName);
            
            using var serviceScope = serviceScopeFactory.CreateScope();
            var serviceProvider = serviceScope.ServiceProvider;
            var sender = serviceProvider.GetRequiredService<ISender>();
            await sender.Send(command, stoppingToken);
            
            logger.LogInformation("执行{OperationName}结束", operationName);
        }
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("后台服务退出");
        return base.StopAsync(cancellationToken);
    }
}