﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Commands.Case;

public sealed record CreateCaseSupplierContactCommand(
    [property: Description("案件id"), Required] string CaseId,
    [property: Description("联系人id"), Required] HashSet<string> ContactIds) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;