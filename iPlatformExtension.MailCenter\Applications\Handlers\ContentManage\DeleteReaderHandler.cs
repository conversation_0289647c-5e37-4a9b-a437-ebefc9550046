﻿using Confluent.Kafka;
using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using iPlatformExtension.Common.Extensions;
using Microsoft.AspNetCore.Http;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Common.Helper;
using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.Model.Dto.MailCenter;
using Microsoft.AspNetCore.Server.HttpSys;
using iPlatformExtension.Model.Enum;
using System.Collections.Generic;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using Microsoft.AspNetCore.Components.Forms;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class DeleteReaderHandler(IMailReaderListRepository mailReaderListRepository,
        IHttpContextAccessor httpContextAccessor,
        IMailReceiveRepository mailReceive, IMediator mediator
        ) : IRequestHandler<DeleteReaderCommand>
    {
        public async Task Handle(DeleteReaderCommand request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var readerList = await mailReaderListRepository.Where(o => request.UserIds.Any(i => i == o.UserId) && o.MailId == request.MailId && o.Status == (int)ReaderStatusEnum.ToRead).ToListAsync(cancellationToken);
            if (readerList.Any())
            {
                await mailReaderListRepository.DeleteAsync(o => readerList.Any(r => r.Id == o.Id), cancellationToken);

                await mediator.Send(new MailCenterMessageQuery(readerList,OperationTypeEnum.Delete));
            }
            else
            {
                throw new Exception("删除失败,没有找到对应Id");
            }
        }
    }
}
