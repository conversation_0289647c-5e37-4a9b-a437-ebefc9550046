﻿using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Cache;

public interface IRedisCache<out TOptions> where TOptions : RedisCacheOptionsBase
{
    IOptions<TOptions> Options { get; }

    Task<bool> SetCacheValueAsync<TKey, TValue>(string cacheKey, T<PERSON><PERSON> key, TValue value, bool updateIfExists = false, CancellationToken cancellationToken = default);

    Task<bool> RemoveCacheValueAsync<TKey>(string cacheKey, TK<PERSON> key, CancellationToken cancellationToken = default);

    Task<bool> RemoveCacheValuesAsync(string cacheKey, CancellationToken cancellationToken = default);

    Task<TValue?> GetCacheValueAsync<TKey, TValue>(string cacheKey, TK<PERSON> key, CancellationToken cancellationToken = default);

    Task<bool> SetCacheValuesAsync<TKey, TValue>(string cacheKey, IDictionary<TKey, TValue> values,
        TimeSpan lifeTime, CancellationToken cancellationToken = default);

    Task<IEnumerable<TValue?>> GetCacheValuesAsync<TValue>(string cacheKey, CancellationToken cancellationToken = default);

    Task<TResult?> ExecuteScriptAsync<TResult>(string script, string[]? keys = default, object[]? values = default, CancellationToken cancellationToken = default);

    Task<Dictionary<TKey, TValue?>> GetCacheKeyValuesAsync<TKey, TValue>(string cacheKey, CancellationToken cancellationToken = default);

    Task<bool> RemoveCacheKeyAsync(string cacheKey, CancellationToken cancellationToken = default);
}