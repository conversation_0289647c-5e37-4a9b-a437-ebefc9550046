﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// bladeX统一返回结果
/// </summary>
public class BladeXResult
{
    /// <summary>
    /// 结果编码
    /// </summary>
    public virtual int Code { get; set; }

    /// <summary>
    /// 提示信息
    /// </summary>
    [JsonPropertyName("msg")]
    public virtual string? Message { get; set; }

    /// <summary>
    /// 结果成功与否
    /// </summary>
    public virtual bool Success { get; set; }
}

/// <summary>
/// 带数据BladeX结果
/// </summary>
/// <typeparam name="T"></typeparam>
public class BladeXResult<T> : BladeXResult
{
    /// <summary>
    /// 返回的数据
    /// </summary>
    public virtual T? Data { get; set; }
}