﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Proc;
using iPlatformExtension.Finance.Applications.Models.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Proc;

internal sealed class UpdateCaseProcCommandHandler(
    ICaseProcInfoRepository caseProcInfoRepository, 
    IMapper mapper, IHttpContextAccessor httpContextAccessor) 
    : IRequestHandler<UpdateCaseProcCommand>
{
    public async Task Handle(UpdateCaseProcCommand request, CancellationToken cancellationToken)
    {
        var (document, procInfo) = request;
        var currentUserId = (httpContextAccessor.HttpContext?.User).GetUserId();

        procInfo ??= await caseProcInfoRepository.GetAsync(document.ProcId, cancellationToken);
        
        var dto = mapper.Map<CaseProcPatchDto>(procInfo);
        document.ApplyTo(dto);
        mapper.Map(dto, procInfo);
        procInfo.UpdateUserId = currentUserId;
        procInfo.UpdateTime = DateTime.Now;

        await caseProcInfoRepository.UpdateAsync(procInfo, cancellationToken);
    }
}