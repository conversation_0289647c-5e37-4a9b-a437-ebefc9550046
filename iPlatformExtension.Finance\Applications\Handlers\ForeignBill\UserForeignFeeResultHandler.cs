﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal sealed class UserForeignFeeResultHandler(IUserInfoRepository userInfoRepository) 
    : ForeignFeeResultHandlerBase
{
    protected override async ValueTask GetResultAsync(ForeignBillFeeExportDto dto)
    {
        ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
        dto.CaseSales = (await userBaseInfoRepository.GetCacheValueAsync(dto.CaseSales ?? string.Empty))?.CnName ?? string.Empty;
    }
}