﻿using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;

internal sealed record UpdateSuggestionProcWeightMatchRuleCommand(
    int RuleId, 
    JsonPatchDocument<SuggestionProcWeightMatchRulePatchDto> Document) : 
    IRequest,
    IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;