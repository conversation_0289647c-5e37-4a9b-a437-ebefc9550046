﻿using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Implement;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class CancelOrderCommandHandler(
    DeliveryInfoRepository deliveryInfoRepository,
    PhoenixClientFactory phoenixClientFactory,
    IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<CancelOrderCommand>
{
    public async Task Handle(CancelOrderCommand request, CancellationToken cancellationToken)
    {
        var deliveryInfo = request.DeliveryInfo ?? await deliveryInfoRepository.GetAsync(request.ProcId, deliInfo =>
            new DeliInfo()
            {
                ProcId = deliInfo.ProcId,
                OrderNo = deliInfo.OrderNo,
                DeliveryKey = deliInfo.DeliveryKey,
                Version = deliInfo.Version,
                UpdateTime = deliInfo.UpdateTime,
                UpdateUserId = deliInfo.UpdateUserId
            }, cancellationToken);
        
        if (deliveryInfo != null)
        {
            var orderNo = deliveryInfo.OrderNo;
            var deliveryKey = deliveryInfo.DeliveryKey;

            if (string.IsNullOrWhiteSpace(orderNo))
                return;

            var client = phoenixClientFactory.CreateClient(deliveryKey);
            var parameters = new CancelOrderParameters()
            {
                OrderNo = orderNo
            };
            await client.CancelOrderAsync(parameters);

            deliveryInfo.OrderNo = string.Empty;
            deliveryInfo.UpdateTime = DateTime.Now;
            deliveryInfo.UpdateUserId = (httpContextAccessor.HttpContext?.User).GetUserId();

            await deliveryInfoRepository.UpdateAsync(deliveryInfo, cancellationToken);
        }
    }
}