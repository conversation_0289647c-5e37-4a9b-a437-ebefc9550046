using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_log_table_config", DisableSyncStructure = true)]
	public partial class SysLogTableConfig {

		[ Column(Name = "bas_form_name", StringLength = 50)]
		public string BasFormName { get; set; }

		[ Column(Name = "form_name", StringLength = 50)]
		public string FormName { get; set; }

		[ Column(Name = "key_value")]
		public bool? KeyValue { get; set; }

		[ Column(Name = "sql", StringLength = 4000)]
		public string Sql { get; set; }

		[ Column(Name = "table_name", StringLength = 50)]
		public string TableName { get; set; }

	}

}
