﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

/// <summary>
/// 递交快照删除命令
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="Refresh">是否刷新</param>
/// <param name="Version">版本号</param>
internal record DeleteDeliveryCommand(string ProcId, bool Refresh = true, int? Version = null) 
    : IRequest<DeliInfo?>, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;