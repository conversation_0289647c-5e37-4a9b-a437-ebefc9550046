using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_notice_convey_files_setting", DisableSyncStructure = true)]
	public partial class BasNoticeConveyFilesSetting {

		[ Column(Name = "convey_file_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ConveyFileId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "file_ex", StringLength = 50)]
		public string FileEx { get; set; }

		[ Column(Name = "file_name", StringLength = 50)]
		public string FileName { get; set; }

		[ Column(Name = "file_select_type", StringLength = 50)]
		public string FileSelectType { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
