﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement
{
    internal sealed class SysFlowPrivateRepository : DefaultRepository<SysFlowPrivate, string>, ISysFlowPrivateRepository
    {
        public SysFlowPrivateRepository(IFreeSql fsql, UnitOfWorkManager uowManger) : base(fsql, uowManger)
        {
        }
    }
}
