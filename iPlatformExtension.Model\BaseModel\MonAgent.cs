using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mon_agent", DisableSyncStructure = true)]
	public partial class MonAgent {

		[ Column(Name = "client_id", StringLength = 50)]
		public string ClientId { get; set; }

		[ Column(Name = "client_id_text", StringLength = 200)]
		public string ClientIdText { get; set; }

		[ Column(Name = "processor", StringLength = 50)]
		public string Processor { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
