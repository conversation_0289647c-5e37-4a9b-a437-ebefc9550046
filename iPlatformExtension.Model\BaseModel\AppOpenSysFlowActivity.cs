using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_open_sys_flow_activity", DisableSyncStructure = true)]
	public partial class AppOpenSysFlowActivity {

		[ Column(Name = "allow_edit")]
		public bool AllowEdit { get; set; }

		[ Column(Name = "app_obj_id", StringLength = 50)]
		public string AppObjId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "cur_node_id", StringLength = 50)]
		public string CurNodeId { get; set; }

		[ Column(Name = "cur_user_id", StringLength = 50)]
		public string CurUserId { get; set; }

		[ Column(Name = "flow_id", StringLength = 50)]
		public string FlowId { get; set; }

		[ Column(Name = "flow_sub_type", StringLength = 50)]
		public string FlowSubType { get; set; }

		[ Column(Name = "flow_type", StringLength = 50)]
		public string FlowType { get; set; }

		[ Column(Name = "int_due_date")]
		public DateTime? IntDueDate { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; }

		[ Column(Name = "prev_audit_type_id", StringLength = 50)]
		public string PrevAuditTypeId { get; set; }

		[ Column(Name = "prev_node_id", StringLength = 50)]
		public string PrevNodeId { get; set; }

		[ Column(Name = "private_id", StringLength = 50)]
		public string PrivateId { get; set; }

		[ Column(Name = "status")]
		public int? Status { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
