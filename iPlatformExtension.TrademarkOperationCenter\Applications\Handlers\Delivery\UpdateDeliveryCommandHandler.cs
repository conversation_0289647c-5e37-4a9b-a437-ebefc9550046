using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class UpdateDeliveryCommandHandler(IDeliveryInfoRepository deliveryInfoRepository)
    : IRequestHandler<UpdateDeliveryCommand, DeliInfo>
{
    public async Task<DeliInfo> Handle(UpdateDeliveryCommand request, CancellationToken cancellationToken)
    {
        var deliveryInfo = await deliveryInfoRepository.GetAsync(request.ProcId, cancellationToken);
        if (deliveryInfo is null)
        {
            throw new NotFoundException(request.ProcId, "递交任务");
        }

        request.DeliveryPatch.ApplyTo(deliveryInfo);
        deliveryInfo.UpdateTime = DateTime.Now;
        deliveryInfo.UpdateUserId = request.OperatorId;

        var updated = await deliveryInfoRepository.UpdateAsync(deliveryInfo, cancellationToken);
        if (updated <= 0)
        {
            throw new ApplicationException("更新失败");
        }

        return deliveryInfo;
    }
}