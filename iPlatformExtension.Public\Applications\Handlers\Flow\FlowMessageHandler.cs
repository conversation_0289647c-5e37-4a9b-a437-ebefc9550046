﻿using Hangfire.MemoryStorage.Entities;
using iPlatformExtension.Common.Clients.BladeCommon;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Flow;
using MediatR;
using Newtonsoft.Json.Linq;
using System.Linq.Expressions;
using System.Text;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.BaseModel;
using FlowProcInfoDto = iPlatformExtension.Public.Applications.Models.Flow.FlowInfoDto;
using Newtonsoft.Json;
using Polly;
using iPlatformExtension.Public.MQ.FlowNotificationConsumer;
using iPlatformExtension.Public.Applications.Commands.Flow;
using iPlatformExtension.Model.Enum;

namespace iPlatformExtension.Public.Applications.Handlers.Flow
{

    /// <summary>
    /// 仅用于作业平台流程变动企微消息通知
    /// </summary>
    /// <param name="bladeCommonClient"></param>
    /// <param name="freeSql"></param>
    /// <param name="wxWorkClientExtension"></param>
    public class FlowMessageHandler(BladeCommonClient bladeCommonClient, IFreeSql freeSql, IWxWorkClient wxWorkClientExtension, ILogger<FlowNotificationHostedService> logger) : IRequestHandler<FlowMessageCommand>
    {


        public async Task Handle(FlowMessageCommand request, CancellationToken cancellationToken)
        {
            if (!string.IsNullOrEmpty(request.message))
            {
                try
                {
                    WxMessageContent content = new WxMessageContent();
                    //转成实体后处理
                    JObject json = (JObject)JsonConvert.DeserializeObject(request.message);
                    if (json != null && json.ContainsKey("payload"))
                    {
                        var flowActivity = JsonConvert.DeserializeObject<MQMessageData<MQFlowActivity>>(json["payload"].ToString());
                        if (flowActivity != null)
                        {

                            //审核人变化/节点变化/新增时
                            if (flowActivity.op.ToUpper() == "C" || (flowActivity.op.ToUpper() == "U" && (flowActivity.before.CurNodeId != flowActivity.after.CurNodeId || flowActivity.before.CurUserId != flowActivity.after.CurUserId)))
                            {
                                var lonetime = flowActivity.after != null ? flowActivity.after.UpdateTime : flowActivity.before.UpdateTime;
                                DateTime updateTime = ConvertTimestampToDateTime(Convert.ToInt64(lonetime.Substring(0, 10)));
                                content.SubmitDate = updateTime.ToString("yyyy-MM-dd HH:mm:ss");
                                content.Auditor = flowActivity.after.CurUserId;
                                content.RecipientUserName = await GetUserName(flowActivity.after.CurUserId);
                                content.OperationPath = "商标代理人平台-作业中心-我的商标";
                                var isTMII = freeSql.Select<SysFlowConfig, SysDeptInfo>().LeftJoin((config, dept) => config.DeptId == dept.DeptId).Any((config, dept) => config.FlowId == flowActivity.after.FlowId && dept.DeptCode == "TMII");
                                if (FilterFlow(flowActivity, isTMII))
                                {
                                    //throw new Exception("cuowuo");
                                    //return Task.FromException(new Exception("errrr"));
                                    var id = flowActivity.op.ToUpper() == "C" ? flowActivity.after.ActivityId : flowActivity.before.ActivityId;
                                    Console.WriteLine($"{id}属于TMII已被过滤");
                                    return;
                                }
                                var flowType = await freeSql.Select<SysDictionary>().Where(o => o.DictionaryName == "flow_type" && o.Value == flowActivity.after.FlowType).ToOneAsync();
                                var auditType = await freeSql.Select<SysDictionary>().Where(o => o.DictionaryName == "audit_type" && o.Value == flowActivity.after.PrevAuditTypeId).ToOneAsync();

                                var objId = flowActivity.after.ObjId;
                                if (flowActivity.after.FlowType == FlowTypeEnum.EX)
                                {
                                    objId = await freeSql.Select<CaseProcFlow>().Where(o => o.FlowType == ProcFlowEnum.EX && (o.ProcId == flowActivity.after.ObjId || o.ProcFlowId == flowActivity.after.ObjId)).ToOneAsync(o => o.ProcId);
                                }

                                var flowProcInfo = await freeSql.Select<CaseProcInfo, CaseInfo>().WithLock().LeftJoin((p, c) => p.CaseId == c.Id).Where((p, c) => p.ProcId == objId).ToOneAsync((p, c) => new FlowProcInfoDto { CaseName = c.CaseName, ProcNo = p.ProcNo, UndertakeUserId = p.UndertakeUserId, ApplyId = c.ApplyId });

                                var flowTypeName = flowType.TextZhCn.Replace("流程", "");
                                if (flowProcInfo != null)
                                {
                                    if ("C" == flowActivity.op.ToUpper())
                                    {
                                        //新配案待办
                                        //新的待办任务
                                        //核稿待办
                                        content.Title = $"[{flowProcInfo.CaseName}-{flowProcInfo.ProcNo}]待{flowTypeName}任务";
                                        content.OperationUser = flowActivity.after.CreateUserId;
                                        content.OperationPath = "商标代理人平台-流程中心-我的待办";

                                        if (flowActivity.after.FlowType == FlowTypeEnum.AT)
                                        {
                                            var auditUserId = await freeSql.Select<SysFlowHistory>().Where(o => o.FlowType == FlowTypeEnum.AP && o.ObjId == flowProcInfo.ApplyId).OrderByDescending(o => o.AuditTime).ToOneAsync(o => o.AuditUserId);
                                            content.OperationUser = auditUserId;
                                        }
                                    }
                                    else
                                    {
                                        if (flowActivity.before?.CurNodeId != flowActivity.after.CurNodeId)
                                        {
                                            content.OperationUser = !string.IsNullOrEmpty(flowActivity.before.CurUserId) ? flowActivity.before.CurUserId : flowActivity.after.UpdateUserId;
                                            if (flowActivity.after.Status < FLOW_STATUS.S5000)
                                            {
                                                if (flowActivity.after.PrevAuditTypeId.ToLower() == "submit")
                                                {
                                                    //流程更新待办
                                                    content.Title = $"[{flowProcInfo.CaseName}-{flowProcInfo.ProcNo}]待{flowTypeName}任务";
                                                }
                                                else if (flowActivity.after.PrevAuditTypeId.ToLower() == "reject")
                                                {
                                                    //自动递交流程被退回时
                                                    //提交到流程部被退回时
                                                    //核稿被退回时
                                                    content.Title = $"[{flowProcInfo.CaseName}-{flowProcInfo.ProcNo}]{flowTypeName}已被退回，请核查后重新发起";
                                                    if (isTMII)
                                                    {
                                                        content.Title = $"[{flowProcInfo.CaseName}-{flowProcInfo.ProcNo}]提交到流程部已被退回，请核查后重新发起";
                                                    }
                                                    if (flowActivity.after.FlowType == FlowTypeEnum.EX)
                                                    {
                                                        content.OperationPath = "商标代理人平台-流程中心-我的待办";
                                                    }
                                                }
                                                else
                                                {
                                                    content.Title = $"[{flowProcInfo.CaseName}-{flowProcInfo.ProcNo}]{flowTypeName}-{auditType.TextZhCn}";
                                                    content.OperationPath = "商标代理人平台-流程中心-我的待办";
                                                }
                                            }
                                            else
                                            {
                                                //提交到流程部结束时
                                                //自动递交流程结束时
                                                content.OperationPath = "商标代理人平台-案件中心-商标案件";
                                                if (flowActivity.after.FlowType == FlowTypeEnum.DE && flowActivity.after.Status == FLOW_STATUS.S5000)
                                                {
                                                    content.Title = $"[{flowProcInfo.CaseName}-{flowProcInfo.ProcNo}]{flowTypeName}已完成，请知悉";
                                                    if (isTMII)
                                                    {
                                                        content.Title = $"[{flowProcInfo.CaseName}-{flowProcInfo.ProcNo}]提交到流程部已完成，请知悉";
                                                    }
                                                    content.Auditor = flowActivity.after.CreateUserId;
                                                    content.RecipientUserName = await GetUserName(flowActivity.after.CreateUserId);

                                                }
                                                else if (flowActivity.after.FlowType == FlowTypeEnum.AT && flowActivity.after.Status == FLOW_STATUS.S5000)
                                                {
                                                    content.Title = $"[{flowProcInfo.CaseName}-{flowProcInfo.ProcNo}]待处理任务";
                                                    content.OperationUser = flowActivity.before.CurUserId;
                                                    content.Auditor = flowProcInfo.UndertakeUserId;
                                                    content.RecipientUserName = await GetUserName(flowProcInfo.UndertakeUserId);
                                                    content.OperationPath = "商标代理人平台-作业中心-我的商标";
                                                }
                                                else if (flowActivity.after.FlowType == FlowTypeEnum.EX && flowActivity.after.Status == FLOW_STATUS.S5000)
                                                {
                                                    content.Title = $"[{flowProcInfo.CaseName}-{flowProcInfo.ProcNo}]{flowTypeName}已完成，请知悉";
                                                    content.OperationUser = flowActivity.before.CurUserId;
                                                    content.Auditor = flowProcInfo.UndertakeUserId;
                                                    content.RecipientUserName = await GetUserName(flowProcInfo.UndertakeUserId);
                                                    content.OperationPath = "商标代理人平台-作业中心-我的商标";
                                                }
                                                else
                                                {
                                                    //不在通知清单内的消息
                                                    var id = flowActivity.op.ToUpper() == "C" ? flowActivity.after.ActivityId : flowActivity.before.ActivityId;
                                                    Console.WriteLine($"{id}不在通知清单内的消息");
                                                    return;
                                                }
                                            }
                                        }
                                        else
                                        {
                                            //移交时
                                            content.OperationUser = flowActivity.before.CurUserId;
                                            content.Title = $"[{flowProcInfo.CaseName}-{flowProcInfo.ProcNo}]{flowTypeName}移交";
                                            content.OperationPath = "商标代理人平台-流程中心-我的待办";
                                        }
                                    }
                                }
                                content.OperationUser = await GetCnName(content.OperationUser);
                                content.Auditor = await GetCnName(content.Auditor);

                                //content.RecipientUserName = "H04133";
                                //根据数据适配消息模版发送企业微信
                                var res = await wxWorkClientExtension.SendTextCardMessage(content);
                                Console.WriteLine($"SendTextCardMessage.Result:{JsonConvert.SerializeObject(res)}");
                                return;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "消费出错!!" + request?.message);
                    //保存日志
                    Console.WriteLine($"SendTextCardMessage.Err:{ex.Message},{request?.message}");
                    // return;
                    throw;
                }

            }
        }

        /// <summary>
        /// 流程过滤
        /// </summary>
        /// <param name="flowActivity"></param>
        /// <returns></returns>
        private bool FilterFlow(MQMessageData<MQFlowActivity> flowActivity, bool isTMII)
        {
            if (flowActivity.after != null)
            {
                //过滤递交中间过程
                if (flowActivity.after.FlowType == FlowTypeEnum.DE && flowActivity.after.Status < FLOW_STATUS.S5000 && flowActivity.after.PrevAuditTypeId.ToLower() == "submit")
                {
                    return true;
                }
                //仅处理流程类型为TII或者流程部门编码为TMII的流程
                if (flowActivity.after.FlowSubType != "TII" && !isTMII)
                {
                    return true;
                }
            }
            return false;
        }



        private async Task<string> GetCnName(string userId)
        {
            var userinfo = await freeSql.Select<SysUserInfo>().Where(o => o.UserId == userId).ToOneAsync();
            if (userinfo != null)
            {
                return userinfo.CnName;
            }
            return userId;
        }


        private async Task<string> GetUserName(string userId)
        {
            var userinfo = await freeSql.Select<SysUserInfo>().Where(o => o.UserId == userId).ToOneAsync();
            if (userinfo != null)
            {
                return userinfo.UserName;
            }
            return userId;
        }


        public DateTime ConvertTimestampToDateTime(long timestamp)
        {
            // Unix时间戳起始时间
            DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Local);
            // 将时间戳转换为TimeSpan
            TimeSpan time = TimeSpan.FromSeconds(timestamp);
            // 将TimeSpan添加到起始时间，得到结果DateTime
            return epoch.Add(time).ToLocalTime(); // 如果需要转换为本地时间，使用ToLocalTime()
                                                  // 如果需要保持UTC时间，使用以下代码
                                                  // return epoch.Add(time);
        }


    }
}
