﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class PushDomesticCommissionWeightExceptionHandler(
    ILogger<PushDomesticCommissionWeightCommand> logger)
    : IRequestExceptionHandler<PushDomesticCommissionWeightCommand, Unit, Exception>
{
    public Task Handle(PushDomesticCommissionWeightCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        logger.LogPushingDomesticTrademarkWeightError(exception);
        state.SetHandled(Unit.Value);
        
        return Task.CompletedTask;
    }
}