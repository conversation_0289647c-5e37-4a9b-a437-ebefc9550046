﻿using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeIdsQueryHandler(IMediator mediator) : IRequestHandler<FeeIdsQuery, IEnumerable<FeeListItemDto>>
{
    public async Task<IEnumerable<FeeListItemDto>> Handle(FeeIdsQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return await Task.FromCanceled<IEnumerable<FeeListItemDto>>(cancellationToken);

        var feeIds = await request.FeesQuery.Distinct()
            .ToListAsync(caseFeeList => caseFeeList.FeeId, cancellationToken);
        return await mediator.Send(new FeeItemsQuery(feeIds, request.SortCondition, SortOrder.Ascending), cancellationToken);
    }
}