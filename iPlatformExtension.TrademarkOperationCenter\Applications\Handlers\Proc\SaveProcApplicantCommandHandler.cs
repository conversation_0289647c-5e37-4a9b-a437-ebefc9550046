﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Implement;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc
{
    internal sealed class SaveProcApplicantCommandHandler(IFreeSql freeSql, IMediator mediator, IHttpContextAccessor content, IMapper mapper, ICaseProcApplicantRepository caseProcApplicantRepository, ICaseFileRepository caseFileRepository) : IRequestHandler<SaveProcApplicantCommand>
    {
        public async Task Handle(SaveProcApplicantCommand request, CancellationToken cancellationToken)
        {
            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userId);


            IList<ApplicantFileDto> applicantFileDtos = new List<ApplicantFileDto>();

            CaseProcApplicant procApplicant = new CaseProcApplicant();
            procApplicant = caseProcApplicantRepository.Where(o => o.Id == request.Id).First();

            procApplicant.ApplicantTypeId = string.IsNullOrEmpty(request.TypeId) ? "" : request.TypeId;
            procApplicant.ApplicantId = string.IsNullOrEmpty(request.ApplicantId) ? "" : request.ApplicantId;
            procApplicant.ApplicantNameCn = string.IsNullOrEmpty(request.ApplicantNameCn) ? "" : request.ApplicantNameCn;
            procApplicant.ApplicantNameEn = string.IsNullOrEmpty(request.ApplicantNameEn) ? "" : request.ApplicantNameEn;

            procApplicant.AddrCn = string.IsNullOrEmpty(request.AddressCn) ? "" : request.AddressCn;
            procApplicant.AddrEn =  string.IsNullOrEmpty(request.AddressEn) ? "" : request.AddressEn;
            procApplicant.CardType = string.IsNullOrEmpty(request.CertificationType) ? "" : request.CertificationType;
            procApplicant.CardNo =  string.IsNullOrEmpty(request.CertificationNumber) ? "" : request.CertificationNumber;
            procApplicant.ChangeType = request.ChangeType;
            procApplicant.ChangeTypeName = string.IsNullOrEmpty(request.ChangeTypeName) ? "" : request.ChangeTypeName;
            procApplicant.IsChineseIdentity = request.IsChineseIdentity;
            procApplicant.CountryId = string.IsNullOrEmpty(request.CountryId) ? "" : request.CountryId;
            procApplicant.PostCode = string.IsNullOrEmpty(request.PostCode) ? "" : request.PostCode;

            procApplicant.UpdateUserId = userId;
            procApplicant.UpdateTime = DateTime.Now;

            await caseProcApplicantRepository.UpdateAsync(procApplicant, cancellationToken);

            applicantFileDtos = request.FileList;

            IList<CaseFile> caseFiles = new List<CaseFile>();

            foreach (var file in applicantFileDtos)
            {
                //int file_No = int.Parse(Regex.Replace(file.FileNo, "a002", ""));
                var fileListA = freeSql.Select<FileListA>().Where(a => a.Id == file.FileId).First();
                CaseFile caseFile = new CaseFile();
                caseFile.FileId = Guid.NewGuid().ToString();
                caseFile.FileNo = string.Concat("a002", file.FileId);
                caseFile.ObjId = procApplicant.Id;
                caseFile.DescId = file.DescId;
                caseFile.CreateUserId = procApplicant.CreateUserId;
                caseFile.CreateTime = procApplicant.CreateTime;

                if (fileListA != null)
                {
                    caseFile.FileName = fileListA.RealName;
                    caseFile.FileSize = fileListA.FileSize;
                    caseFile.FileEx = fileListA.RealName.Substring(fileListA.RealName.LastIndexOf("."));
                }

                caseFiles.Add(caseFile);
            }

            caseFileRepository.Delete(o=>o.ObjId == procApplicant.Id);

            await caseFileRepository.InsertAsync(caseFiles, cancellationToken);
        }
    }
}
