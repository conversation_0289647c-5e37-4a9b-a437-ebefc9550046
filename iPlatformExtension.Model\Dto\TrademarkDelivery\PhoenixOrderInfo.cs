﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

public class PhoenixOrderInfo
{
    public object[] Receipts { get; set; }
    public Diplomats Diplomats { get; set; }
    public object[] Other { get; set; }
    public Attachments[] Attachments { get; set; }
    public Order Order { get; set; }
}

public class Diplomats
{
    public int DiplomatsId { get; set; }
    public string OrderNo { get; set; }
    public int FlowState { get; set; }
    public string FlowStateName { get; set; }
    public string CreateTime { get; set; }
    public string UpdateTime { get; set; }
    public object FinishTime { get; set; }
    public int OwnerType { get; set; }
    public int SubjectType { get; set; }
    public string ApplicantName { get; set; }
    public string ApplicantEnglishName { get; set; }
    public int BookType { get; set; }
    public string Country { get; set; }
    public string Telephone { get; set; }
    public string ApplicantAddress { get; set; }
    public string ApplicantEnglishAddress { get; set; }
    public string IdCard { get; set; }
    public int CertificatesType { get; set; }
    public int BrandType { get; set; }
    public int Prov { get; set; }
    public int City { get; set; }
    public int Area { get; set; }
    public string Street { get; set; }
    public string Code { get; set; }
    public string BrandExplain { get; set; }
    public string AgentOrganName { get; set; }
    public string AgentOrganTel { get; set; }
    public string AgentOrganConName { get; set; }
    public int AgentOrganId { get; set; }
    public string PrincipalName { get; set; }
    public string PrincipalTel { get; set; }
    public int Version { get; set; }
    public int Number { get; set; }
    public string DomesticReceiverName { get; set; }
    public string DomesticReceiverAddress { get; set; }
    public string DomesticReceiverCode { get; set; }
    public string Message { get; set; }
    public string DomesticReceiverEmail { get; set; }
    public string SubmitTypeNo { get; set; }
    public int IsReplenish { get; set; }
    public string FactReason { get; set; }
    public string LegalPerson { get; set; }
    public string Post { get; set; }
    public int Flow2State { get; set; }
    public string Flow2StateName { get; set; }
    public object PcAssignTime { get; set; }
    public object PcFinishTime { get; set; }
    public Categories[] Categories { get; set; }
    public string BizName { get; set; }
}

public class Categories
{
    public int DiplomatCategoryId { get; set; }
    public string OrderNo { get; set; }
    public int FirstCgId { get; set; }
    public string FirstCgNo { get; set; }
    public string FirstCgName { get; set; }
    public string CreateTime { get; set; }
    public string UpdateTime { get; set; }
    public string ReceiptNo { get; set; }
    public string ReceiptOfficeUrl { get; set; }
    public string ProxyNo { get; set; }
    public int CategoryFlowState { get; set; }
    public string CategoryFlowStateName { get; set; }
    public int Version { get; set; }
    public string BrandTeam { get; set; }
    public int Number { get; set; }
    public object ApplyTime { get; set; }
    public string BrandName { get; set; }
    public string BrandRegisterNo { get; set; }
    public object BrandStartTime { get; set; }
    public string Message { get; set; }
    public string KeyCode { get; set; }
    public object Items { get; set; }
    public object Receipts { get; set; }
}

/// <summary>
/// 权大师返回附件信息
/// </summary>
public class Attachments
{
    /// <summary>
    /// 附件id
    /// </summary>
    public int AttachmentId { get; set; }
    
    /// <summary>
    /// 订单号
    /// </summary>
    public string? OrderNo { get; set; }
    
    /// <summary>
    /// 附件类型
    /// </summary>
    public int AttachmentType { get; set; }
    
    /// <summary>
    /// 附件名称
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? AttachmentName { get; set; }
    
    /// <summary>
    /// 子类型
    /// </summary>
    public int SubType { get; set; }
    
    /// <summary>
    /// 子名称
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? SubName { get; set; }
    
    /// <summary>
    /// 附件链接
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? AttachmentPic { get; set; }
    
    /// <summary>
    /// 商标注册号
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ReceiptNo { get; set; }
    
    /// <summary>
    /// 代理好
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ProxyNo { get; set; }
    
    /// <summary>
    /// 收文时间
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public DateTime? ReceiveTime { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Name { get; set; }
    
    /// <summary>
    /// 绝限时间
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public DateTime? LimitTime { get; set; }
}

public class Order
{
    public int OrderId { get; set; }
    public string OrderNo { get; set; }
    public string ProductName { get; set; }
    public int Number { get; set; }
    public string CreateTime { get; set; }
    public string UpdateTime { get; set; }
    public int UserId { get; set; }
    public string UserName { get; set; }
    public string ContactName { get; set; }
    public string ContactTel { get; set; }
    public string ContactEmail { get; set; }
    public string ContactFixedTel { get; set; }
    public int CancelState { get; set; }
    public int OrderUserId { get; set; }
    public string OrderToken { get; set; }
    public int SubmitType { get; set; }
    public int Total { get; set; }
    public int ActualCharge { get; set; }
    public int OfficialCharge { get; set; }
    public int ServiceCharge { get; set; }
    public int InvoiceCharge { get; set; }
    public int InternalCost { get; set; }
    public int ThirdCost { get; set; }
    public int ApplyInvoiceStatus { get; set; }
    public object PayTime { get; set; }
    public int PayType { get; set; }
    public string PayTypeName { get; set; }
    public int ReceiveId { get; set; }
    public int PayState { get; set; }
    public int ProxyType { get; set; }
    public string ProductNo { get; set; }
    public int ProductModelId { get; set; }
    public int ProductId { get; set; }
}

