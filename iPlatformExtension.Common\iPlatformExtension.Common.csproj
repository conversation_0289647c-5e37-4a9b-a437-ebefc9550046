﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>iPlatformExtension.Common</RootNamespace>
    <LangVersion>preview</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Confluent.Kafka" Version="2.6.0" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="FreeSql.Provider.MySqlConnector" Version="3.5.105" />
    <PackageReference Include="FreeSql.DbContext" Version="3.5.105" />
    <PackageReference Include="FreeSql.Extensions.LazyLoading" Version="3.5.105" />
    <PackageReference Include="FreeSql.Provider.SqlServer" Version="3.5.105" />
    <PackageReference Include="Grpc.Net.ClientFactory" Version="2.66.0" />
    <PackageReference Include="Hangfire.Core" Version="1.8.6" />
    <PackageReference Include="HuaweiCloud.SDK.OBS.Core" Version="********" />
    <PackageReference Include="log4net" Version="2.0.15" />
    <PackageReference Include="MailKit" Version="4.8.0" />
    <PackageReference Include="MediatR" Version="12.4.1" />
    <PackageReference Include="MessagePack" Version="2.5.192" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.0" />
    
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.0" />
<!--    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />-->
<!--    <PackageReference Include="Microsoft.Extensions.FileProviders.Physical" Version="9.0.0" />-->
<!--    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />-->
<!--    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.3" />-->
<!--    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />-->
<!--    <PackageReference Include="Microsoft.Extensions.ObjectPool" Version="8.0.2" />-->
<!--    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />-->
<!--    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.0" />-->
    <PackageReference Include="Microsoft.Extensions.ServiceDiscovery" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.0" />
    <PackageReference Include="MiniExcel" Version="1.35.0" />
    <PackageReference Include="MongoDB.Driver" Version="3.0.0" />
    <PackageReference Include="nacos-sdk-csharp" Version="1.3.10" />
    <PackageReference Include="RulesEngine" Version="5.0.3" />
    <PackageReference Include="SkyAPM.Diagnostics.FreeSql" Version="2.2.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.0" />
    <PackageReference Include="System.Linq.Async" Version="6.0.1" />
  </ItemGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\iPlatformExtension.Model\iPlatformExtension.Model.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="bin\**" />
    <Compile Remove="obj\**" />
    <Compile Remove="MQ\KafkaExtension.cs" />
    <Compile Remove="Db\FreeSQL\DbContextOptions.cs" />
    <Compile Remove="Db\Log\LogDbContext.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Remove="bin\**" />
    <EmbeddedResource Remove="obj\**" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="bin\**" />
    <None Remove="obj\**" />
  </ItemGroup>




<!--  <ItemGroup>-->
<!--    <Folder Include="Clients\OrderCaseEntrust\Options\" />-->
<!--  </ItemGroup>-->

</Project>