using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Commands.Commission;

/// <summary>
/// 插入收入明细命令
/// </summary>
/// <param name="IncomeDetails">收入明细</param>
internal sealed record InsertIncomesCommand(IEnumerable<MPaymentDetail> IncomeDetails) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;