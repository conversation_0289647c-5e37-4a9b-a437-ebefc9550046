﻿namespace iPlatformExtension.Model.Dto;
/// <summary>
/// 导师信息
/// </summary>
public class MentorInfo
{
    /// <summary>
    /// 
    /// </summary>
    public string MentorId { get; set; } = null!;

    /// <summary>
    /// 
    /// </summary>
    public string MentorUserName { get; set; } = null!;
    
    /// <summary>
    /// 用户id
    /// </summary>
    public string UserId  { get; set; } = null!;
    
    /// <summary>
    /// 工号
    /// </summary>
    public string UserName  { get; set; } = null!;
    
    /// <summary>
    /// 部门id
    /// </summary>
    public string DeptId  { get; set; } = null!;
    
    /// <summary>
    /// 中文名
    /// </summary>
    public string MentorCnName  { get; set; } = null!;
    
    /// <summary>
    /// 邮件
    /// </summary>
    public string? Email  { get; set; }
    
    /// <summary>
    /// 是否可用
    /// </summary>
    public bool? IsEnabled  { get; set; }
}