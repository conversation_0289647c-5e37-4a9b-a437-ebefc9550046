﻿using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.CDC;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using KafkaFlow;
using MediatR;

namespace iPlatformExtension.Outsourcing.Consumers;

public sealed class CdcCaseProcInfoHandler(IPublisher publisher, EntityTypeInfoProvider entityTypeInfoProvider) : IMessageHandler<CdcMessageValue<CdcProcInfo>>
{
    public Task Handle(IMessageContext context, CdcMessageValue<CdcProcInfo> message)
    {
        var entityTypeInfo = entityTypeInfoProvider.Get(typeof(CaseProcInfo));
        return message.Payload.Source.Table == entityTypeInfo.TableName 
            ? publisher.Publish(new CdcProcNotification(message), context.ConsumerContext.WorkerStopped) 
            : Task.CompletedTask;
    }
}