﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Http.Json;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Clients.Finance;

public class  FinanceHttpClient
{
    private readonly HttpClient _httpClient;

    private readonly IOptionsMonitor<JsonOptions> _optionsMonitor;

    public FinanceHttpClient(HttpClient httpClient, IOptionsMonitor<JsonOptions> optionsMonitor)
    {
        _httpClient = httpClient;
        _optionsMonitor = optionsMonitor;
    }

    public Task<BladeXResult?> PostBillInfoAsync(IEnumerable<BillInfoConvertDto> billInfo)
    {
        var options = _optionsMonitor.CurrentValue;
        return _httpClient.PostJsonAsync<IEnumerable<BillInfoConvertDto>, BladeXResult>(
            "/api/finance-service/billinfo/billListFromBussinessToFinance", billInfo, options.SerializerOptions);
    }
}