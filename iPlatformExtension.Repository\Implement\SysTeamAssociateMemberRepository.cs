﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Repository.Implement
{
    public class SysTeamAssociateMemberRepository : DefaultRepository<SysTeamAssociateMember, string>, ISysTeamAssociateMemberRepository
    {
        public SysTeamAssociateMemberRepository(IFreeSql fsql, UnitOfWorkManager uowManger) : base(fsql, uowManger)
        {
        }
    }
}
