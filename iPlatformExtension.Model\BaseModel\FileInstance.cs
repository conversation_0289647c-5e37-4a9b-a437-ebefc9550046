using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "file_instance", DisableSyncStructure = true)]
	public partial class FileInstance {

		[ Column(Name = "instance_id", IsPrimary = true)]
		public int InstanceId { get; set; }

		[ Column(Name = "home_dir", StringLength = 50)]
		public string HomeDir { get; set; }

		[ Column(Name = "instance_name", StringLength = 50)]
		public string InstanceName { get; set; }

		[ Column(Name = "remark", StringLength = 200)]
		public string Remark { get; set; }

	}

}
