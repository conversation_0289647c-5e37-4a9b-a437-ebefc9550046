﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel 
{

	/// <summary>
	/// 商标递交信息
	/// </summary>
	[Table(Name = "trademark_delivery_info", DisableSyncStructure = true)]
	public class TrademarkDeliveryInfo 
	{

		/// <summary>
		/// 递交信息主键
		/// </summary>
		[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
		public int Id { get; set; }

		/// <summary>
		/// 申请人地址英文
		/// </summary>
		[Column(Name = "applicant_address_en", StringLength = 500, IsNullable = false)]
		public string ApplicantAddressEn { get; set; } = "";

		/// <summary>
		/// 申请人地址
		/// </summary>
		[Column(Name = "applicant_address_zh", StringLength = 500, IsNullable = false)]
		public string ApplicantAddressZh { get; set; } = "";

		/// <summary>
		/// 申请人英文名
		/// </summary>
		[Column(Name = "applicant_name_en", StringLength = 100, IsNullable = false)]
		public string ApplicantNameEn { get; set; } = "";

		/// <summary>
		/// 申请人中文名
		/// </summary>
		[Column(Name = "applicant_name_zh", StringLength = 50, IsNullable = false)]
		public string ApplicantNameZh { get; set; } = "";

		/// <summary>
		/// 申请人邮政编码
		/// </summary>
		[Column(Name = "applicant_post_code", StringLength = 10, IsNullable = false)]
		public string ApplicantPostCode { get; set; } = "";

		/// <summary>
		/// 申请人类型
		/// </summary>
		[Column(Name = "applicant_type", DbType = "nchar(2)", IsNullable = false)]
		public string ApplicantType { get; set; } = "";

		/// <summary>
		/// 书式类型
		/// </summary>
		[Column(Name = "book_type")]
		public int BookType { get; set; }

		/// <summary>
		/// 国家
		/// </summary>
		[Column(Name = "country", DbType = "nchar(3)", IsNullable = false)]
		public string Country { get; set; } = "";

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "creation_time", InsertValueSql = "getdate()")]
		public DateTime CreationTime { get; set; }

		/// <summary>
		/// 创建用户id
		/// </summary>
		[Column(Name = "creator_id", StringLength = 50, IsNullable = false)]
		public string CreatorId { get; set; } = "";

		/// <summary>
		/// 任务id
		/// </summary>
		[Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
		public string ProcId { get; set; } = default!;

		/// <summary>
		/// 任务编号
		/// </summary>
		[Column(Name = "proc_no", StringLength = 10, IsNullable = false)]
		public string ProcNo { get; set; } = "";

		/// <summary>
		/// 接收人、联系人地址
		/// </summary>
		[Column(Name = "receiver_address", StringLength = 500, IsNullable = false)]
		public string ReceiverAddress { get; set; } = "";

		/// <summary>
		/// 联系人、接收人邮箱
		/// </summary>
		[Column(Name = "receiver_email", StringLength = 50, IsNullable = false)]
		public string ReceiverEmail { get; set; } = "";

		/// <summary>
		/// 接收人
		/// </summary>
		[Column(Name = "receiver_name", StringLength = 50, IsNullable = false)]
		public string ReceiverName { get; set; } = "";

		/// <summary>
		/// 联系人名称
		/// </summary>
		[Column(Name = "contact_person", StringLength = 50, IsNullable = false)]
		public string ContactPerson { get; set; } = string.Empty;

		/// <summary>
		/// 联系人、接收人邮编
		/// </summary>
		[Column(Name = "receiver_post_code", StringLength = 20, IsNullable = false)]
		public string ReceiverPostCode { get; set; } = "";

		/// <summary>
		/// 接收人、联系人电话
		/// </summary>
		[Column(Name = "receiver_telephone", StringLength = 50, IsNullable = false)]
		public string ReceiverTelephone { get; set; } = "";

		/// <summary>
		/// 商标说明
		/// </summary>
		[Column(Name = "trademark_description", StringLength = 500, IsNullable = false)]
		public string TrademarkDescription { get; set; } = "";

		/// <summary>
		/// 商标名称
		/// </summary>
		[Column(Name = "trademark_name", StringLength = 100, IsNullable = false)]
		public string TrademarkName { get; set; } = "";

		/// <summary>
		/// 商标类型
		/// </summary>
		[Column(Name = "trademark_type", StringLength = 50, IsNullable = false)]
		public string TrademarkType { get; set; } = "";

		/// <summary>
		/// 承办人Id
		/// </summary>
		[Column(Name = "undertaker_id", StringLength = 50, IsNullable = false)]
		public string UndertakerId { get; set; } = "";

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 更新用户id
		/// </summary>
		[Column(Name = "updater_id", StringLength = 50, IsNullable = false)]
		public string UpdaterId { get; set; } = "";

		/// <summary>
		/// 我方文号
		/// </summary>
		[Column(Name = "volume", StringLength = 50, IsNullable = false)]
		public string Volume { get; set; } = "";

		/// <summary>
		/// 身份证号
		/// 统一社会信用代码
		/// </summary>
		[Column(Name = "identity_number", StringLength = 100, IsNullable = false)]
		public string IdentityNumber { get; set; } = String.Empty;

		/// <summary>
		/// 证件类型
		/// </summary>
		[Column(Name = "certificate_type", IsNullable = false)]
		public int CertificateType { get; set; } = 0;

		/// <summary>
		/// 证件号吗
		/// </summary>
		[Column(Name = "certificate_number", StringLength = 100, IsNullable = false)]
		public string CertificateNumber { get; set; } = string.Empty;

		/// <summary>
		/// 是否颜色组合
		/// </summary>
		[Column(Name = "is_multipart_color", IsNullable = false)]
		public bool IsMultipartColor { get; set; }

		/// <summary>
		/// 是否三维
		/// </summary>
		[Column(Name = "is_three_d", IsNullable = false)]
		public bool IsThreeD { get; set; }

		/// <summary>
		/// 是否声音
		/// </summary>
		[Column(Name = "is_voice", IsNullable = false)]
		public bool IsVoice { get; set; }

		/// <summary>
		/// 共同申请人集合
		/// </summary>
		[Navigate(nameof(TrademarkOtherApplicant.DeliverInfoId))]
		public virtual ICollection<TrademarkOtherApplicant>? OtherApplicants { get; set; }

		/// <summary>
		/// 优先权信息
		/// </summary>
		[Navigate(nameof(TrademarkPriorityInfo.DeliveryId))]
		public virtual ICollection<TrademarkPriorityInfo>? PriorityInfos { get; set; }
	}

}
