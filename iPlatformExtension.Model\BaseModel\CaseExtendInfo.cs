using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_extend_info", DisableSyncStructure = true)]
	public partial class CaseExtendInfo {

		[ Column(Name = "case_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "app_pages")]
		public int? AppPages { get; set; }

		/// <summary>
		/// 商标说明
		/// </summary>
		[ Column(Name = "case_descriptions", StringLength = 4000)]
		public string CaseDescriptions { get; set; }

		/// <summary>
		/// 初审公告日
		/// </summary>
		[ Column(Name = "chushen_pub_date")]
		public DateTime? ChushenPubDate { get; set; }

		/// <summary>
		/// 初审公告号
		/// </summary>
		[ Column(Name = "chushen_pub_no", StringLength = 50)]
		public string ChushenPubNo { get; set; }

		/// <summary>
		/// 初审公告期
		/// </summary>
		[ Column(Name = "chushen_pub_serialno", StringLength = 50)]
		public string ChushenPubSerialno { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// DAS码
		/// </summary>
		[ Column(Name = "das_code", StringLength = 50)]
		public string DasCode { get; set; }

		/// <summary>
		/// 从属权项数
		/// </summary>
		[ Column(Name = "dependent_claims")]
		public int? DependentClaims { get; set; }

		/// <summary>
		/// 进入国家阶段期限
		/// </summary>
		[ Column(Name = "entry_date")]
		public DateTime? EntryDate { get; set; }

		/// <summary>
		/// 独立权项数
		/// </summary>
		[ Column(Name = "independent_claims")]
		public int? IndependentClaims { get; set; }

		/// <summary>
		/// 原案申请日,母案申请日
		/// </summary>
		[ Column(Name = "initial_app_date")]
		public DateTime? InitialAppDate { get; set; }

		/// <summary>
		/// 原案申请号,母案申请号
		/// </summary>
		[ Column(Name = "initial_app_no", StringLength = 50)]
		public string InitialAppNo { get; set; }

		/// <summary>
		/// 有效终止日
		/// </summary>
		[ Column(Name = "invalid_date")]
		public DateTime? InvalidDate { get; set; }

		/// <summary>
		/// PCT申请日
		/// </summary>
		[ Column(Name = "pct_app_date")]
		public DateTime? PctAppDate { get; set; }

		/// <summary>
		/// PCT申请号
		/// </summary>
		[ Column(Name = "pct_app_no", StringLength = 50)]
		public string? PctAppNo { get; set; }

		/// <summary>
		/// PCT公布语言
		/// </summary>
		[ Column(Name = "pct_language", StringLength = 50)]
		public string PctLanguage { get; set; }

		/// <summary>
		/// PCT公布日
		/// </summary>
		[ Column(Name = "pct_pub_date")]
		public DateTime? PctPubDate { get; set; }

		[ Column(Name = "pct_pub_language", StringLength = 50)]
		public string PctPubLanguage { get; set; }

		/// <summary>
		/// PCT公布号
		/// </summary>
		[ Column(Name = "pct_pub_no", StringLength = 50)]
		public string PctPubNo { get; set; }

		/// <summary>
		/// 国际检索单位
		/// 关联表  bas_international_search
		/// </summary>
		[ Column(Name = "pct_search_unit", StringLength = 200)]
		public string PctSearchUnit { get; set; }

		[ Column(Name = "pic_file_no", StringLength = 50)]
		public string PicFileNo { get; set; }

		/// <summary>
		/// 绘图幅数
		/// </summary>
		[ Column(Name = "picture_count")]
		public int? PictureCount { get; set; }

		[ Column(Name = "pub_begin_date")]
		public DateTime? PubBeginDate { get; set; }

		[ Column(Name = "pub_end_date")]
		public DateTime? PubEndDate { get; set; }

		[ Column(Name = "register_date")]
		public DateTime? RegisterDate { get; set; }

		[ Column(Name = "service_items_num")]
		public int? ServiceItemsNum { get; set; }

		/// <summary>
		/// 说明书页数
		/// </summary>
		[ Column(Name = "specification_pages")]
		public int? SpecificationPages { get; set; }

		/// <summary>
		/// 说明书字数
		/// </summary>
		[ Column(Name = "specification_words")]
		public int? SpecificationWords { get; set; }

		/// <summary>
		/// 临时申请日
		/// </summary>
		[ Column(Name = "temp_app_date")]
		public DateTime? TempAppDate { get; set; }

		/// <summary>
		/// 临时申请号
		/// </summary>
		[ Column(Name = "temp_app_no", StringLength = 50)]
		public string TempAppNo { get; set; }

		[ Column(Name = "total_claims")]
		public int? TotalClaims { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "use_declare", StringLength = 50)]
		public string UseDeclare { get; set; }

		[ Column(Name = "use_deli_date")]
		public DateTime? UseDeliDate { get; set; }

		[ Column(Name = "use_deli_end_date")]
		public DateTime? UseDeliEndDate { get; set; }

		/// <summary>
		/// 有效起始日
		/// </summary>
		[ Column(Name = "valid_date")]
		public DateTime? ValidDate { get; set; }

		[ Column(Name = "valid_no", StringLength = 50)]
		public string ValidNo { get; set; }

		/// <summary>
		/// 注册申请日
		/// </summary>
		[ Column(Name = "zhuce_app_date")]
		public DateTime? ZhuceAppDate { get; set; }

		/// <summary>
		/// 注册公告日
		/// </summary>
		[ Column(Name = "zhuce_pub_date")]
		public DateTime? ZhucePubDate { get; set; }

		/// <summary>
		/// 注册公告号
		/// </summary>
		[ Column(Name = "zhuce_pub_no", StringLength = 50)]
		public string ZhucePubNo { get; set; }

		/// <summary>
		/// 注册公告期
		/// </summary>
		[ Column(Name = "zhuce_pub_serialno", StringLength = 50)]
		public string ZhucePubSerialno { get; set; }

	}

}
