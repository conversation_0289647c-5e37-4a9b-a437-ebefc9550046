using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_conflict", DisableSyncStructure = true)]
	public partial class CusConflict {

		[ Column(Name = "conflict_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ConflictId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "conflict_company", StringLength = 200)]
		public string ConflictCompany { get; set; }

		[ Column(Name = "conflict_type", StringLength = 50)]
		public string ConflictType { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
