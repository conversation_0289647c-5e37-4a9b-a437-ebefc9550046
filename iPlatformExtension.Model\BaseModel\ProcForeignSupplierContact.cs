﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel 
{

	/// <summary>
	/// 任务境外代理联系人
	/// </summary>
	[Table(Name = "proc_foreign_supplier_contact", DisableSyncStructure = true)]
	public class ProcForeignSupplierContact
	{

		/// <summary>
		/// 联系人id
		/// </summary>
		[Column(Name = "contact_id", IsPrimary = true, IsNullable = false)]
		public string ContactId { get; set; } = null!;

		/// <summary>
		/// 任务id
		/// </summary>
		[Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ProcId { get; set; } = null!;

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "creation_time", InsertValueSql = "getdate()")]
		public DateTime CreationTime { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 更新者
		/// </summary>
		[Column(Name = "updater", StringLength = 50, IsNullable = false)]
		public string Updater { get; set; } = null!;

		/// <summary>
		/// 是否代表
		/// </summary>
		[Column(Name = "representative")]
		public bool Representative { get; set; }

	}

}
