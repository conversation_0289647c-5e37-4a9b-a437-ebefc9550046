﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Copy;

/// <summary>
/// 复制案件命令
/// </summary>
/// <param name="SourceId">源案件</param>
/// <param name="TargetId">目标案件</param>
/// <param name="CopyFieldList">复制内容勾选项</param>
public record CopyCaseCommand([Required(ErrorMessage = "请添加源案件id")] string SourceId, [Required(ErrorMessage = "请先添加【目标案件】")] string[] TargetId,
    [Required(ErrorMessage = "请先勾选【复制内容】")] string[] CopyFieldList) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

