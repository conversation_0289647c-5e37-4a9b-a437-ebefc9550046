﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    internal sealed class UpdateCaseInfoByATHandler : IRequestHandler<UpdateCaseInfoByATCommand>
    {

        private readonly IMediator _mediator;
        private readonly IFreeSql _freeSql;

        public UpdateCaseInfoByATHandler(IFreeSql freeSql, IMediator mediator)
        {
            _freeSql = freeSql;
            _mediator = mediator;

        }

        public async Task Handle(UpdateCaseInfoByATCommand request, CancellationToken cancellationToken)
        {
            if (request.info.FFlowType == FlowTypeEnum.AT)
            {
                /* g.trademark_tab = '4'
                0:查询分析
                1:创新项目
                2:建议类任务
                3:注册申请
                4:撰写类
                5:撰写类(基础) ((g.trademark_tab='4' and p.ctrl_proc_mark='Simple') or g.trademark_tab='5') and c.case_direction='II' 
                6:撰写类(补充/一次性) ((g.trademark_tab='4' and (p.ctrl_proc_mark='OneOff' or p.ctrl_proc_mark='Sup')) or g.trademark_tab='6' or (g.trademark_tab='4' and isnull(p.ctrl_proc_mark,'')=''))  and c.case_direction='II' 
                7:其他 (isnull(g.trademark_tab,'') not in ('0','1','2','3','4','5','6')) and c.case_direction='II' ";
                 */
                var fa = request.fa;
                if (request.info.FAuditTypeID == "submit")
                {
                    await _mediator.Send(new UpdateCaseProcInfoCommand(new UpdateCaseProcInfoDto { ProcId = fa.ObjId, ProcStatus = "TCLZ", SubProcStatus = "t_submiting" }), cancellationToken);
                }


                var nodeinfo = await _freeSql.Select<SysFlowNode>().Where(o => o.NodeId == fa.CurNodeId).FirstAsync(cancellationToken);
                if (nodeinfo.NodeCode == "BEGIN")
                {
                    // await _mediator.Send(new UpdateCaseProcInfoCommand(fa.ObjId, "TWCL", "t_untreated"), cancellationToken);
                }

                if (nodeinfo.NodeCode == "END")
                {
                }
                await _mediator.Send(new UpdateCaseProcInfoCommand(new UpdateCaseProcInfoDto { ProcId = request.info.ProcID, ProcStatus = "TCLZ", SubProcStatus = "t_submiting" }), cancellationToken);
            }
        }
    }
}
