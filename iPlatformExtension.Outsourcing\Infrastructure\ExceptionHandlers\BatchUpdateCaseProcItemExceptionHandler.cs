﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Infrastructure.Extensions;
using MediatR.Pipeline;

namespace iPlatformExtension.Outsourcing.Infrastructure.ExceptionHandlers;

internal sealed class BatchUpdateCaseProcItemExceptionHandler(IFreeSql<PlatformFreeSql> freeSql, ILogger<BatchUpdateCaseProcItemExceptionHandler> logger) 
    : IRequestExceptionHandler<ProcJsonPatchDocument, BatchUpdateProcItemResult, Exception>
{
    public async Task Handle(ProcJsonPatchDocument request, Exception exception, RequestExceptionHandlerState<BatchUpdateProcItemResult> state,
        CancellationToken cancellationToken)
    {
        var procId = request.ProcId;
        
        logger.LogUpdateProcError(procId, exception);
        
        state.SetHandled(new BatchUpdateProcItemResult()
        {
            ItemId = await freeSql.Select<CaseProcInfo>(procId).WithLock().ToOneAsync(info => info.ProcNo, cancellationToken).ConfigureAwait(false),
            Message = exception.Message,
            Success = false
        });
    }
}