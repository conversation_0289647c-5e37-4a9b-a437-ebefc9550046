using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 大客户案件流向表
		/// 用于商标提成规则计算，存在此表中则认为是大客户。
	/// </summary>
	[ Table(Name = "sys_customer_big_client", DisableSyncStructure = true)]
	public partial class SysCustomerBigClient {

		/// <summary>
		/// id
		/// </summary>
		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 案件流向类型
		/// </summary>
		[ Column(Name = "case_flow_type", StringLength = 50, IsNullable = false)]
		public string CaseFlowType { get; set; } = "NT";

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 案件流向
		/// </summary>
		[Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; } = null!;

		/// <summary>
		/// 客户ID
		/// </summary>
		[ Column(Name = "customer_id", StringLength = 50, IsNullable = false)]
		public string CustomerId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 权值
		/// </summary>
		[ Column(Name = "real_point", DbType = "money")]
		public decimal RealPoint { get; set; } = 0M;

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
