using iPlatformExtension.MailCenter.Applications.Commands.AuditUser;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AuditUser
{
    /// <summary>
    /// 删除发件审核人命令处理程序
    /// </summary>
    internal sealed class DeleteAuditUserCommandHandler(
        IFlowAuditUserRepository flowAuditUserRepository) : IRequestHandler<DeleteAuditUserCommand, bool>
    {
        public async Task<bool> Handle(DeleteAuditUserCommand request, CancellationToken cancellationToken)
        {
            // 检查记录是否存在
            var existingRecord = await flowAuditUserRepository.Where(it => it.Id == request.Id)
                .FirstAsync(cancellationToken);

            if (existingRecord == null)
            {
                throw new ApplicationException($"未找到ID为 {request.Id} 的记录");
            }

            // 删除记录
            var result = await flowAuditUserRepository.DeleteAsync(it => it.Id == request.Id, cancellationToken);

            return result > 0;
        }
    }
}
