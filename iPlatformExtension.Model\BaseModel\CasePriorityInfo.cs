using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_priority_info", DisableSyncStructure = true)]
	public partial class CasePriorityInfo {

		/// <summary>
		/// 优先权主键ID
		/// </summary>
		[ Column(Name = "priority_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string PriorityId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 案件ID
		/// </summary>
		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 接入码
		/// </summary>
		[ Column(Name = "das_code", StringLength = 50)]
		public string? DasCode { get; set; }

		/// <summary>
		/// 优先权地区
		/// </summary>
		[ Column(Name = "priority_country_id", StringLength = 50)]
		public string PriorityCountryId { get; set; }

		/// <summary>
		/// 优先权日
		/// </summary>
		[ Column(Name = "priority_date")]
		public DateTime? PriorityDate { get; set; }

		/// <summary>
		/// 优先权号
		/// </summary>
		[ Column(Name = "priority_no", StringLength = 50)]
		public string PriorityNo { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		/// <summary>
		/// 我方文号
		/// </summary>
		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

	}

}
