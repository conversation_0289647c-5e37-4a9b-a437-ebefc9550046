using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.MailCenter.Applications.Commands.Template;
using iPlatformExtension.MailCenter.Applications.Models.Template;
using iPlatformExtension.MailCenter.Applications.Queries.Template;
using iPlatformExtension.Model.Dto;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.MailCenter.Controllers
{
    /// <summary>
    /// 邮件模板控制器
    /// </summary>
    /// <param name="mediator">中介者</param>
    [Route("[controller]")]
    [ApiController]
    public class TemplateController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// 获取邮件模板列表
        /// </summary>
        /// <remarks>
        /// 获取邮件模板列表，支持按名称模糊查询和启用状态筛选。
        /// </remarks>
        /// <param name="query">查询参数</param>
        /// <returns>邮件模板列表分页结果</returns>
        [HttpGet("GetMailTemplateList")]
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        public async Task<PageResult<GetMailTemplateListDto>> GetMailTemplateList(
            [FromQuery] GetMailTemplateListQuery query)
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 获取邮件模板详情
        /// </summary>
        /// <remarks>
        /// 根据模板ID获取邮件模板详细信息。
        /// </remarks>
        /// <param name="templateId">模板ID</param>
        /// <returns>邮件模板详情</returns>
        [HttpGet("GetMailTemplateDetail")]
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        public async Task<GetMailTemplateDetailDto> GetMailTemplateDetail(
            [FromQuery] string templateId)
        {
            return await mediator.Send(new GetMailTemplateDetailQuery(templateId), HttpContext.RequestAborted);
        }

        /// <summary>
        /// 保存邮件模板
        /// </summary>
        /// <remarks>
        /// 保存邮件模板信息。当TemplateId为空时新增模板，不为空时修改现有模板。
        /// </remarks>
        /// <param name="command">保存邮件模板命令</param>
        /// <returns>模板ID</returns>
        [HttpPost("SaveMailTemplate")]
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        public async Task<string> SaveMailTemplate(
            [FromBody] SaveMailTemplateCommand command)
        {
            return await mediator.Send(command, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 删除邮件模板
        /// </summary>
        /// <remarks>
        /// 根据模板ID删除邮件模板。
        /// </remarks>
        /// <param name="templateId">模板ID</param>
        /// <returns></returns>
        [HttpDelete("DeleteMailTemplate")]
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        public async Task DeleteMailTemplate(
            [FromQuery] string templateId)
        {
            await mediator.Send(new DeleteMailTemplateCommand(templateId), HttpContext.RequestAborted);
        }
    }
}
