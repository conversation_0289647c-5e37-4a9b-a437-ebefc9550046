using MongoDB.Driver;

namespace iPlatformExtension.Common.Db.Mongo;

public class MongoDbContextOptions
{
    public string DatabaseName { get; set; } = default!;

    public IMongoClient MongoClient { get; set; } = default!;

    public MongoUrl Url { get; set; } = default!;

    public MongoClientSettings Settings { get; set; } = default!;
}

public class MongoDbContextOptions<T> : MongoDbContextOptions
{
}