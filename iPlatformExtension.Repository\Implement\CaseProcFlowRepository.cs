﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Repository.Implement
{
    internal sealed class CaseProcFlowRepository(IFreeSql freeSql, UnitOfWorkManager uowManger)
: DefaultRepository<CaseProcFlow, string>(freeSql, uowManger),ICaseProcFlowRepository;

}
