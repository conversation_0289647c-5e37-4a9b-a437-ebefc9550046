﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface IApplyTypeRepository : 
    IBaseRepository<BasApplyType, string>, 
    IStringKeyCacheableRepository<BasApplyType>,
    IScopeDependency,
    IRedisCacheableRepository<string, BasApplyType>
{
    Task<BasApplyType?> ICacheableRepository<string, BasApplyType>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Orm.Select<BasApplyType>().WithLock().Where(applyType => applyType.ApplyTypeId == key).FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasApplyType>> ICacheableRepository<string, BasApplyType>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Orm.Select<BasApplyType>().WithLock().ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasApplyType>.GenerateKey(BasApplyType value)
    {
        return value.ApplyTypeId;
    }

    string IStringKeyCacheableRepository<BasApplyType>.GetCacheTextValue(BasApplyType value)
    {
        return value.ApplyTypeZhCn;
    }
}