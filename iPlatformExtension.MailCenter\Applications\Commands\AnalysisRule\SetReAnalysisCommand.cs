﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.AnalysisRule;

/// <summary>
/// 操作命令
/// </summary>
/// <param name="MailId">邮件id</param>
/// <param name="Status">状态0:待解析,4:已忽略</param>
public record SetReAnalysisCommand([Required] List<string> MailId, int Status) : IRequest, IUnitOfWorkCommandMysql;

