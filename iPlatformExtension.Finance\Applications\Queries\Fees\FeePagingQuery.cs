﻿using FreeSql;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Queries.Fees;

internal sealed record FeePagingQuery
    (ISelect<CaseFeeList> FeesQuery, string SortCondition, SortOrder SortOrder, int PageIndex, int PageSize, long? Total) 
    : IRequest<PageResult<FeeListItemDto>>;
