﻿using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkRegistration;

internal sealed class TrademarkRegistrationInitializeFilesCommandHandler(
    IFreeSql freeSql,
    HuaweiObsClient huaweiObsClient,
    IHttpContextAccessor httpContextAccessor,
    IFileDescriptionRepository fileDescriptionRepository)
    : BuildDeliveryInitializeFilesCommandHandler
{
    private const string FileDescription = "商标代理委托书";

    private const string FileType = "递交官方";

    protected override string CtrlProcId => CtrlProcIds.TrademarkRegistration;

    protected override async Task HandleAsync(BuildDeliveryInitializeFilesCommand request, CancellationToken cancellationToken)
    {
        var applicant = request.Applicants.FirstOrDefault(deliApplicant => deliApplicant.IsRepresent == true);
        if (applicant is null)
            return;
        
        var fileDescription =
            await fileDescriptionRepository.GetCacheValueAsync(new FileDescriptionKey(FileType, FileDescription));
        if (fileDescription is null)
        {
            throw new NotFoundException(FileDescription, "文件描述");
        }

        var proxyDocument = await freeSql.Select<CaseFile>().WithLock().Where(file => file.ObjId == applicant.ApplicantId)
            .Where(file => file.DescId == fileDescription.FileDescId)
            .Where(file => file.FileEx == ".jpg" || file.FileEx == ".jpeg")
            .ToOneAsync(cancellationToken);

        if (proxyDocument is null)
        {
            return;
        }
        
        var fileId = Convert.ToInt32(proxyDocument.FileNo[4..]);
        var fileInfo = await freeSql.Select<FileListA>(fileId).WithLock().ToOneAsync(cancellationToken);
        if (fileInfo is null)
        {
            throw new NotFoundException(fileId, "文件数据");
        }

        var currentUser = httpContextAccessor.HttpContext?.User;
        request.Files.Add(new DeliFiles
        {
            FileDesc = FileDescription,
            FileEx = proxyDocument.FileEx,
            FileName = proxyDocument.FileName,
            FileNo = proxyDocument.FileNo,
            IsShow = true,
            ProcId = request.ProcInfo.ProcId,
            BaseFileType = FileType,
            IsIdentity = false,
            Url = huaweiObsClient.GenerateTemporaryUrl(fileInfo.GetObjectName(), fileInfo.Bucket, TimeSpan.FromDays(7 * 365)).SignUrl,
            UploadTime = fileInfo.InputTime,
            Uploader = currentUser?.GetGivenName() ?? throw new NotFoundException(currentUser.GetUserId(), "用户信息"),
            CaseFileId = proxyDocument.FileId,
            Id = fileInfo.Id
        });
    }
    
}