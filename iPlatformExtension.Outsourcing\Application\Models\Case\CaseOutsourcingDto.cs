﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;

namespace iPlatformExtension.Outsourcing.Application.Models.Case;

public class CaseOutsourcingDto
{
    /// <summary>
    /// 供应商id
    /// </summary>
    [JsonIgnore]
    public string? SupplierId { get; set; }
    
    /// <summary>
    /// 外所文号
    /// </summary>
    [Description("外所文号")]
    public string ForeignNumber { get; set; } = string.Empty;

    /// <summary>
    /// 境外代理信息
    /// </summary>
    [Description("境外代理信息")]
    public ForeignAgencyInfo? AgencyInfo { get; set; }

    /// <summary>
    /// 联系人id集合
    /// </summary>
    [Description("联系人id集合")]
    public IEnumerable<string>? ContactIds { get; set; } = [];
    
    /// <summary>
    /// 是否可以编辑
    /// </summary>
    [Description("是否可以编辑")]
    [Required]
    public bool Editable { get; set; }
}