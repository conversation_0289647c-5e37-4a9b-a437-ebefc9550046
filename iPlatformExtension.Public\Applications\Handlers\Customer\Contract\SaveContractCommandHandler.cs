﻿using iPlatformExtension.Public.Applications.Commands.Customer.Contract;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Customer.Contract;

internal sealed class SaveContractCommandHandler(
    ICusContractRepository contractRepository,
    ICaseFileRepository caseFileRepository,
    IContractDetailRepository contractDetailRepository) : IRequestHandler<SaveContractCommand>
{
    public async Task Handle(SaveContractCommand request, CancellationToken cancellationToken)
    {
        var (files, contract, details) = request;
        await contractRepository.InsertAsync(contract, cancellationToken);
        await caseFileRepository.InsertAsync(files, cancellationToken);
        await contractDetailRepository.InsertAsync(details, cancellationToken);
    }
}