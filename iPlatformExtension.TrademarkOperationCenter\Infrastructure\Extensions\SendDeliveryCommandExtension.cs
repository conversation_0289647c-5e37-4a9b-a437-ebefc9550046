﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.LicenseFiling;
using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.NominalChange;
using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.RefuseReexamination;
using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkAnnulment;
using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkObjections;
using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkRegistration;
using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkRenewal;
using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkTransfer;
using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.Withdraw3Years;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery.SendPolicies;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;

/// <summary>
/// 添加递交扩展函数
/// </summary>
public static class SendDeliveryCommandExtension
{
    /// <summary>
    /// 添加发送递交策略
    /// </summary>
    /// <param name="services">服务服务集合</param>
    /// <param name="configurePolicy">策略配置</param>
    /// <typeparam name="TPolicy">发送策略类型</typeparam>
    /// <typeparam name="THandler">策略处理器类型</typeparam>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSendDeliveryPolicy<TPolicy, THandler>(this IServiceCollection services, Action<SendPolicyOptions<THandler>> configurePolicy)
        where TPolicy : class, ISendPolicy
        where THandler : class, ISendDeliveryCommandHandler
    {
        services.TryAddScoped<DefaultSendPolicyProvider>();
        services.AddOptions<SendPolicyOptions<THandler>>().Configure(configurePolicy);
        services.TryAddEnumerable(ServiceDescriptor.Transient<ISendPolicy, TPolicy>());
        services.AddTransient<THandler>();

        return services;
    }

    /// <summary>
    /// 添加递交策略
    /// </summary>
    /// <param name="services">服务服务集合</param>
    /// <returns>服务服务集合</returns>
    public static IServiceCollection AddSendDeliveryPolicies(this IServiceCollection services)
    {
        return services.AddSendDeliveryPolicy<DefaultPolicy, DefaultDeliveryCommandHandler>(_ =>
        {

        }).AddSendDeliveryPolicy<TrademarkRegistrationPolicy, TrademarkRegistrationDeliveryCommandHandler>(options =>
        {
            options.StartupDeliveryMessageKey = "TrademarkRegistration:order";
            options.StopDeliveryMessageKey = "TrademarkRegistration:stop";
            options.WithdrawDeliveryMessageKey = "TrademarkRegistration:withdraw";
            options.CancelDeliveryMessageKey = "TrademarkRegistration:cancel";
        }).AddSendDeliveryPolicy<RefuseReexaminationPolicy, RefuseReexaminationDeliveryCommandHandler>(options =>
        {
            options.StartupDeliveryMessageKey = "RefuseReexamination:order";
            options.StopDeliveryMessageKey = "RefuseReexamination:stop";
            options.WithdrawDeliveryMessageKey = "RefuseReexamination:withdraw";
            options.CancelDeliveryMessageKey = "RefuseReexamination:cancel";
        }).AddSendDeliveryPolicy<NominalChangePolicy, NominalChangeDeliveryCommandHandler>(options =>
        {
            options.StartupDeliveryMessageKey = "NominalChange:order";
            options.StopDeliveryMessageKey = "NominalChange:stop";
            options.WithdrawDeliveryMessageKey = "NominalChange:withdraw";
            options.CancelDeliveryMessageKey = "NominalChange:cancel";
        }).AddSendDeliveryPolicy<TrademarkTransferPolicy, TrademarkTransferDeliveryCommandHandler>(options =>
        {
            options.StartupDeliveryMessageKey = "TrademarkTransfer:order";
            options.StopDeliveryMessageKey = "TrademarkTransfer:stop";
            options.WithdrawDeliveryMessageKey = "TrademarkTransfer:withdraw";
            options.CancelDeliveryMessageKey = "TrademarkTransfer:cancel";
        }).AddSendDeliveryPolicy<TrademarkRenewalPolicy, TrademarkRenewalDeliveryCommandHandler>(options =>
        {
            options.StartupDeliveryMessageKey = "TrademarkRenewal:order";
            options.StopDeliveryMessageKey = "TrademarkRenewal:stop";
            options.WithdrawDeliveryMessageKey = "TrademarkRenewal:withdraw";
            options.CancelDeliveryMessageKey = "TrademarkRenewal:cancel";
        }).AddSendDeliveryPolicy<TrademarkObjectionsPolicy, TrademarkObjectionsDeliveryCommandHandler>(options =>
        {
            options.StartupDeliveryMessageKey = "TrademarkObjections:order";
            options.StopDeliveryMessageKey = "TrademarkObjections:stop";
            options.WithdrawDeliveryMessageKey = "TrademarkObjections:withdraw";
            options.CancelDeliveryMessageKey = "TrademarkObjections:cancel";
        }).AddSendDeliveryPolicy<TrademarkAnnulmentPolicy, TrademarkAnnulmentDeliveryCommandHandler>(options =>
        {
            options.StartupDeliveryMessageKey = "TrademarkAnnulment:order";
            options.StopDeliveryMessageKey = "TrademarkAnnulment:stop";
            options.WithdrawDeliveryMessageKey = "TrademarkAnnulment:withdraw";
            options.CancelDeliveryMessageKey = "TrademarkAnnulment:cancel";
        }).AddSendDeliveryPolicy<Withdraw3YearsPolicy, Withdraw3YearsDeliveryCommandHandler>(options =>
        {
            options.StartupDeliveryMessageKey = "Withdraw3Years:order";
            options.StopDeliveryMessageKey = "Withdraw3Years:stop";
            options.WithdrawDeliveryMessageKey = "Withdraw3Years:withdraw";
            options.CancelDeliveryMessageKey = "Withdraw3Years:cancel";
        }).AddSendDeliveryPolicy<LicenseFilingPolicy, LicenseFilingDeliveryCommandHandler>(options =>
        {
            options.StartupDeliveryMessageKey = "LicenseFiling:order";
            options.StopDeliveryMessageKey = "LicenseFiling:stop";
            options.WithdrawDeliveryMessageKey = "LicenseFiling:withdraw";
            options.CancelDeliveryMessageKey = "LicenseFiling:cancel";
        });
    }

}