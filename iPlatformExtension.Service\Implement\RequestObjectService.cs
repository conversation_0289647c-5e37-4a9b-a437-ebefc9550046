﻿using iPlatformExtension.Repository.Interface;
using iPlatformExtension.Service.Interface;

namespace iPlatformExtension.Service.Implement;

internal sealed class RequestObjectService : IRequestObjectQueryService
{

    public RequestObjectService(ISystemDictionaryRepository systemDictionaryRepository, IFreeSql dbQuery)
    {
        SystemDictionaryRepository = systemDictionaryRepository;
        DbQuery = dbQuery;
    }

    public IFreeSql DbQuery { get; }


    public ISystemDictionaryRepository SystemDictionaryRepository { get; }
}