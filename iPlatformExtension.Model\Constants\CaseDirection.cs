﻿// ReSharper disable All
namespace iPlatformExtension.Model.Constants;

/// <summary>
/// 案件流向
/// </summary>
public static class CaseDirection
{
    /// <summary>
    /// 外-外
    /// </summary>
    public const string OO = nameof(OO);

    /// <summary>
    /// 外-内
    /// </summary>
    public const string OI = nameof(OI);

    /// <summary>
    /// 内-外
    /// </summary>
    public const string IO = nameof(IO);

    /// <summary>
    /// 内-内
    /// </summary>
    public const string II = nameof(II);
}