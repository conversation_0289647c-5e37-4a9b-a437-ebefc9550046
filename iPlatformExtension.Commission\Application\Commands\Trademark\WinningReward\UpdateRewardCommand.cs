﻿using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;

internal sealed record UpdateRewardCommand(string ProcId, JsonPatchDocument<RewardPatchDto> Document) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;