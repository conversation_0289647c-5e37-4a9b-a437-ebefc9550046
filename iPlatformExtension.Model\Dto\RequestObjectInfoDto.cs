﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 请款对象详情信息
/// </summary>
public record RequestObjectInfoDto
{
    /// <summary>
    /// 请款对象id
    /// </summary>
    public string RequestObjectId { get; init; } = default!;

    /// <summary>
    /// 请款对象名称
    /// </summary>
    public string Name { get; init; } = default!;

    /// <summary>
    /// 纳税人资格（类型）
    /// </summary>
    public KeyValuePair<string, string> TaxpayerQualificationType { get; set; }

    /// <summary>
    /// 纳税识别号
    /// 身份证号
    /// </summary>
    public string IdentifierNumber { get; set; } = default!;

    /// <summary>
    /// 发票抬头
    /// </summary>
    public string InvoiceTitle { get; set; } = default!;

    /// <summary>
    /// 银行名称
    /// 开户行
    /// </summary>
    public string? BankName { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string? Telephone { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 账户号码
    /// </summary>
    public string? AccountNo { get; set; }

    /// <summary>
    /// 是否境外
    /// </summary>
    public bool IsOutbound { get; set; }
}