﻿using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Public.Applications.Commands.LawProvision;

internal record UpdateLawProvisionCommand(string DictionaryName, string DictionaryDescription, int ProductId) : IRequest
{
    internal static readonly UpdateLawProvisionCommand AnnulmentLawProvisionCommand =
        new(SystemDictionaryName.AnnulmentLawProvision, "商标无效法律条款", 5);

    internal static readonly UpdateLawProvisionCommand ObjectionsLawProvisionCommand =
        new(SystemDictionaryName.ObjectionsLawProvision, "商标异议法律条款", 4);
}

