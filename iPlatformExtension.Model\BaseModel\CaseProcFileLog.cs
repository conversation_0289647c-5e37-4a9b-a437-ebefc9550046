using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_proc_file_log", DisableSyncStructure = true)]
	public partial class CaseProcFileLog {

		[ Column(DbType = "varchar(36)", IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 操作时间
		/// </summary>
		[ Column(Name = "createTime", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 文件名
		/// </summary>
		[ Column(Name = "file_name", DbType = "varchar(MAX)")]
		public string FileName { get; set; }

		/// <summary>
		/// 文件类型：1任务文件，2核稿文件，3草稿文件
		/// </summary>
		[ Column(Name = "file_type", DbType = "char(1)")]
		public string FileType { get; set; }

		/// <summary>
		/// 0删除，1添加
		/// </summary>
		[ Column(Name = "flg", DbType = "char(1)")]
		public string Flg { get; set; }

		/// <summary>
		/// 任务Id
		/// </summary>
		[ Column(Name = "proc_id", DbType = "varchar(36)")]
		public string ProcId { get; set; }

		/// <summary>
		/// 操作人Id
		/// </summary>
		[ Column(Name = "user_id", DbType = "varchar(36)")]
		public string UserId { get; set; }

		/// <summary>
		/// 操作人
		/// </summary>
		[ Column(Name = "user_name", DbType = "varchar(36)")]
		public string UserName { get; set; }

		/// <summary>
		/// 我方文号
		/// </summary>
		[ Column(Name = "volume", DbType = "varchar(50)")]
		public string Volume { get; set; }

	}

}
