﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Proc;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Net.Mime;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers
{
    /// <summary>
    /// 任务申请人控制器
    /// </summary>
    /// <param name="mediator"></param>
    [Route("[controller]")]
    [ApiController]
    [Produces(MediaTypeNames.Application.Json)]
    public sealed class ProcApplicantController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// 任务申请人详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet]
        public Task<ProcApplicantDto> GetProcApplicantAsync([FromQuery] ProcApplicantQuery query)
        {
            return mediator.Send(query);
        }

        /// <summary>
        /// 任务申请人新增
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost]
        public Task InsertApplicantAsync(InsertProcApplicantCommand command)
        {
            return mediator.Send(command);
        }

        /// <summary>
        /// 任务申请人编辑
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut]
        public Task SaveApplicantAsync(SaveProcApplicantCommand command)
        {
            return mediator.Send(command);
        }

        /// <summary>
        /// 任务申请人删除
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpDelete]
        public Task DeleteApplicantAsync(DeleteProcApplicantCommand command)
        {
            return mediator.Send(command);
        }
    }
}
