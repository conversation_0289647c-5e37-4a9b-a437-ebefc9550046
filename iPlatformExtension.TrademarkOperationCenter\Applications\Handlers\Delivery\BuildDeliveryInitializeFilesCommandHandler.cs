﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal abstract class BuildDeliveryInitializeFilesCommandHandler : INotificationHandler<BuildDeliveryInitializeFilesCommand>
{

    public virtual async Task Handle(BuildDeliveryInitializeFilesCommand request, CancellationToken cancellationToken)
    {
        if (await MatchAsync(request, cancellationToken))
        {
            await HandleAsync(request, cancellationToken);
        }
    }

    protected virtual ValueTask<bool> MatchAsync(BuildDeliveryInitializeFilesCommand request,
        CancellationToken cancellationToken)
    {
        var (procInfo, _, isFirstTime, _) = request;
        return new ValueTask<bool>(StringComparer.OrdinalIgnoreCase.Equals(procInfo.CtrlProcId, CtrlProcId) && isFirstTime);
    }

    protected abstract Task HandleAsync(BuildDeliveryInitializeFilesCommand request,
        CancellationToken cancellationToken);
    
    protected abstract string CtrlProcId { get; }
}