﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Common.Exceptions;

public sealed class FeesUpdateException : ApplicationException
{
    private string? _errorMessage;
    
    public string FeeId { get; }

    public override string Message => $"{base.Message}{_errorMessage}";

    public FeesUpdateException(string feeId) : this(feeId, GetBaseErrorMessage(feeId))
    {
        
    }

    public FeesUpdateException(string feeId, string message) : base(message)
    {
        ArgumentNullException.ThrowIfNull(feeId);
        FeeId = feeId;
    }

    public FeesUpdateException(FeesUpdateResult updateResult) 
        : this(updateResult.FeeId, updateResult.Message ?? GetBaseErrorMessage(updateResult.FeeId))
    {
        
    }

    private static string GetBaseErrorMessage(string feeId) => $"费项[{feeId}]更新失败.";

    /// <summary>
    /// 费项更新失败
    /// </summary>
    /// <param name="feeId">费项id</param>
    /// <param name="message">失败消息</param>
    public static FeesUpdateException UpdateFail(string feeId, string? errorMessage)
        => new FeesUpdateException(feeId)
        {
            _errorMessage = errorMessage
        };

    /// <summary>
    /// 费项不存在
    /// </summary>
    /// <param name="feeId">费项id</param>
    public static FeesUpdateException NotExist(string feeId) => UpdateFail(feeId, "[费项不存在]");


    /// <summary>
    /// 费项锁定中
    /// </summary>
    /// <param name="feeId">费项id</param>
    public static FeesUpdateException UpdatingError(string feeId) => UpdateFail(feeId, "[正在更新中]");

}