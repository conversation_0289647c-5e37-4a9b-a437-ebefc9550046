﻿using Confluent.Kafka;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using iPlatformExtension.Common.MQ.MailMQ;
using iPlatformExtension.Model.Dto.MailCenter;
using Microsoft.Extensions.DependencyInjection;


namespace iPlatformExtension.MailService.Infrastructure.MQ
{
    public static class KafkaConfig
    {
        public static void InitKafaService(this IServiceCollection services, IConfiguration configuration)
        {
            // 添加Kafka配置
            services.Configure<KafkaProducerSettings>("MailServerProducer", configuration.GetSection("KafKa:MailServerProducer"));
            services.Configure<KafkaConsumerSettings>("MailServerConsumer", configuration.GetSection("KafKa:MailServerConsumer"));
            services.AddSingleton<KafkaProducerFactory<Null, MailSendMessage>>();
            services.AddSingleton<KafkaConsumerFactory<Null, MailSendMessage>>();
            services.AddSingleton<KafkaConsumerService<Null, MailSendMessage>>();
            services.AddSingleton<KafkaProducerService<Null, MailSendMessage>>();

            services.Configure<KafkaProducerSettings>("MailCenterProducer", configuration.GetSection("KafKa:MailCenterProducer"));
            services.Configure<KafkaConsumerSettings>("MailCenterConsumer", configuration.GetSection("KafKa:MailCenterConsumer"));
            services.AddSingleton<KafkaProducerFactory<Null, MailCenterMessageContent>>();
            services.AddSingleton<KafkaConsumerFactory<Null, MailCenterMessageContent>>();
            services.AddSingleton<KafkaConsumerService<Null, MailCenterMessageContent>>();
            services.AddSingleton<KafkaProducerService<Null, MailCenterMessageContent>>();
        }
    }
}
