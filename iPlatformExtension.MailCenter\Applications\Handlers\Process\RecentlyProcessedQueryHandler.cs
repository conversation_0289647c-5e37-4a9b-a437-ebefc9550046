﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.Process;
using iPlatformExtension.MailCenter.Applications.Queries.Process;
using MediatR;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Process;

/// <summary>
/// 最近办理
/// </summary>
internal sealed class RecentlyProcessedQueryHandler(IFreeSql<MailCenterFreeSql> freeSql, IHttpContextAccessor content, IUserInfoRepository userInfoRepository) : IRequestHandler<RecentlyProcessedQuery, IEnumerable<RecentlyProcessedDto>>
{
    public async Task<IEnumerable<RecentlyProcessedDto>> Handle(RecentlyProcessedQuery request, CancellationToken cancellationToken)
    {
        var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
        var Action = new[] { "收件办理", "收件分拣", "结束" };

        var mailIds = await freeSql.Select<FlowRecord, MailReceive>()
            .LeftJoin(it => it.t1.MailId == it.t2.MailId)
            .WhereIf(request.Search is not null && !Action.Contains(request.Search), it => it.t2.MailNo == request.Search || it.t2.MailSubject.Contains(request.Search) || it.t2.MailFrom.Contains(request.Search))
            .Where(it => it.t1.AuditTime.Value.Date > DateTime.Now.Date.AddDays(-7))
            .Where(it => it.t1.AuditUser == userId && (it.t1.IsCurrent == SysEnum.MailFlowActionStatus.DisEnable.GetHashCode() || it.t1.CurNodeId == SysEnum.MailFlowAction.End.ToString()))
            .Distinct().ToListAsync(it => it.t1.MailId, cancellationToken);

        var mailReceives = await freeSql.Select<FlowRecord, MailReceive, MailReceiveFlow, FlowRecord>().WithLock()
            .LeftJoin(it => it.t1.MailId == it.t2.MailId)
            .LeftJoin(it => it.t2.MailId == it.t3.MailId)
            .LeftJoin(it => it.t1.PreRecordId == it.t4.Id)
            .Where(it => mailIds.Contains(it.t1.MailId))
            .Where(it => it.t1.IsCurrent == SysEnum.MailFlowActionStatus.Enable.GetHashCode() || it.t1.CurNodeId == SysEnum.MailFlowAction.End.ToString())
            .WhereIf(request.Type != null && request.Type == SysEnum.HandleType.Allot.GetHashCode(), it => it.t3.SortBy == userId)
            .WhereIf(request.Type != null && request.Type == SysEnum.HandleType.Submit.GetHashCode(), it => it.t3.UndertakeUserId == userId)
            .WhereIf(request.Type != null && request.Type == SysEnum.HandleType.Other.GetHashCode(), it => it.t3.UndertakeUserId != userId && it.t3.SortBy != userId)
            .WhereIf(Action.Contains(request.Search), it => it.t1.CurNodeId == request.Search.Replace("收件办理", SysEnum.MailFlowAction.Handle.ToString()).Replace("收件分拣", SysEnum.MailFlowAction.Allot.ToString()).Replace("结束", SysEnum.MailFlowAction.End.ToString()))
            .OrderByDescending(it => it.t4.AuditTime)
            .Page(request.PageIndex!.Value, request.PageSize!.Value).Count(out var count)
            .ToListAsync(it => new RecentlyProcessedDto(it.t1.MailId, it.t2.MailFrom, it.t2.MailNo, it.t2.MailPriority, it.t2.MailSubject,
                it.t2.MailTo, it.t2.Status, it.t2.MailDate, it.t1.AuditType, it.t1.AuditRemark, it.t4.AuditTime, it.t3.SortBy, it.t1.CurNodeId, it.t1.AuditUser, it.t3.UndertakeUserId), cancellationToken);


        return new PageResult<RecentlyProcessedDto>()
        {
            Data = await mailReceives.ToAsyncEnumerable().SelectAwait(async receiveList =>
            {
                if (receiveList.SortByTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    receiveList.SortBy = new
                    {
                        CnName =
                            (await userBaseInfoRepository.GetCacheValueAsync(receiveList.SortByTemp))?.CnName ?? "",
                        UserId = receiveList.SortByTemp
                    };
                }
                if (receiveList.AuditUserTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    receiveList.AuditUser = new
                    {
                        CnName =
                            (await userBaseInfoRepository.GetCacheValueAsync(receiveList.AuditUserTemp))?.CnName ?? "",
                        UserId = receiveList.AuditUserTemp
                    };
                }
                if (receiveList.UndertakeUserTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    receiveList.UndertakeUser = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(receiveList.UndertakeUserTemp))?.CnName ?? "", UserId = receiveList.UndertakeUserTemp };
                }

                return receiveList;
            }).ToListAsync(cancellationToken: cancellationToken),
            Page = request.PageIndex.Value,
            PageSize = request.PageSize.Value,
            Total = count
        };
    }
}


