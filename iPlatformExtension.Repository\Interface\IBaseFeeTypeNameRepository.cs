﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Repository.Interface;

public interface IBaseFeeTypeNameRepository : 
    IStringKeyCacheableRepository<FeeNameInfo>,
    IBaseRepository<BasFeeTypeName, string>,
    IScopeDependency,
    IRedisCacheableRepository<string, FeeNameInfo>
{
    Task<FeeNameInfo> ICacheableRepository<string, FeeNameInfo>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Select.WithLock().Where(name => name.NameId == key).ToOneAsync(name => 
            new FeeNameInfo(name.NameId, name.NameCode, name.NameZhCn, name.NameEnUs), cancellationToken);
    }

    async Task<IEnumerable<FeeNameInfo>> ICacheableRepository<string, FeeNameInfo>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().ToListAsync(name => 
            new FeeNameInfo(name.NameId, name.NameCode, name.NameZhCn, name.NameEnUs), cancellationToken);
    }

    string ICacheableRepository<string, FeeNameInfo>.GenerateKey(FeeNameInfo value)
    {
        return value.Id;
    }

    string? IStringKeyCacheableRepository<FeeNameInfo>.GetCacheTextValue(FeeNameInfo value)
    {
        return value.CnName;
    }
}