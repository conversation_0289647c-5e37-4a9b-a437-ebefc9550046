using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "tm_case_forward_notice_batch", DisableSyncStructure = true)]
	public partial class TmCaseForwardNoticeBatch {

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; }

		[ Column(Name = "courier_description", StringLength = 500)]
		public string CourierDescription { get; set; }

		[ Column(Name = "courier_number", StringLength = 50)]
		public string CourierNumber { get; set; }

		[ Column(Name = "courier_services_company", StringLength = 100)]
		public string CourierServicesCompany { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "flow_cur_user_id", StringLength = 50)]
		public string FlowCurUserId { get; set; }

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		[ Column(Name = "send_time")]
		public DateTime? SendTime { get; set; }

		[ Column(Name = "sender", StringLength = 50)]
		public string Sender { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
