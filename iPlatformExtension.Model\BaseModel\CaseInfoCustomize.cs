using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_info_customize", DisableSyncStructure = true)]
	public partial class CaseInfoCustomize {

		[ Column(Name = "customize_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CustomizeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "a1", StringLength = 500)]
		public string A1 { get; set; }

		[ Column(Name = "a10", StringLength = 500)]
		public string A10 { get; set; }

		[ Column(Name = "a11", StringLength = 500)]
		public string A11 { get; set; }

		[ Column(Name = "a12", StringLength = 500)]
		public string A12 { get; set; }

		[ Column(Name = "a13", StringLength = 500)]
		public string A13 { get; set; }

		[ Column(Name = "a14", StringLength = 500)]
		public string A14 { get; set; }

		[ Column(Name = "a15", StringLength = 500)]
		public string A15 { get; set; }

		[ Column(Name = "a16", StringLength = 500)]
		public string A16 { get; set; }

		[ Column(Name = "a17", StringLength = 500)]
		public string A17 { get; set; }

		[ Column(Name = "a18", StringLength = 500)]
		public string A18 { get; set; }

		[ Column(Name = "a19", StringLength = 500)]
		public string A19 { get; set; }

		[ Column(Name = "a2", StringLength = 500)]
		public string A2 { get; set; }

		[ Column(Name = "a20", StringLength = 500)]
		public string A20 { get; set; }

		[ Column(Name = "a21", StringLength = 500)]
		public string A21 { get; set; }

		[ Column(Name = "a22", StringLength = 500)]
		public string A22 { get; set; }

		[ Column(Name = "a23", StringLength = 500)]
		public string A23 { get; set; }

		[ Column(Name = "a24", StringLength = 500)]
		public string A24 { get; set; }

		[ Column(Name = "a25", StringLength = 500)]
		public string A25 { get; set; }

		[ Column(Name = "a3", StringLength = 500)]
		public string A3 { get; set; }

		[ Column(Name = "a4", StringLength = 500)]
		public string A4 { get; set; }

		[ Column(Name = "a5", StringLength = 500)]
		public string A5 { get; set; }

		[ Column(Name = "a6", StringLength = 500)]
		public string A6 { get; set; }

		[ Column(Name = "a7", StringLength = 500)]
		public string A7 { get; set; }

		[ Column(Name = "a8", StringLength = 500)]
		public string A8 { get; set; }

		[ Column(Name = "a9", StringLength = 500)]
		public string A9 { get; set; }

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

	}

}
