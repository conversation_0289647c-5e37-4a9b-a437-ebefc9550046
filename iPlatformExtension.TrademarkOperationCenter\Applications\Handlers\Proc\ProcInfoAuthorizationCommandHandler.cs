﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class ProcInfoAuthorizationCommandHandler(IHttpContextAccessor httpContextAccessor) : 
    IRequestHandler<ProcInfoAuthorizationCommand, bool>
{
    public Task<bool> Handle(ProcInfoAuthorizationCommand request, CancellationToken cancellationToken)
    {
        var currentUser = httpContextAccessor.HttpContext?.User;
        var currentUserId = currentUser.GetUserId();
        var isReadOnly = !(currentUserId == request.ProcInfo.ProcUndertakeMainUserId ||
                           (currentUser?.IsInRole("商标流程人员") ?? false));

        return Task.FromResult(isReadOnly);
    }
}