﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery;

internal sealed class StopOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IDeliveryInfoRepository deliveryInfoRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    UnitOfWorkManager unitOfWorkManager,
    ILoggerFactory loggerFactory,
    PhoenixClientFactory phoenixClientFactory)
    :
        PhoenixDeliveryHandleBase<StopOrderCommand, PhoenixOrderOperationParameters,
            PhoenixResponseParameters<OrderData>>(stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository,
            unitOfWorkManager, loggerFactory, phoenixClientFactory),
        IDeliveryHandler<StopOrderCommand, PhoenixOrderOperationParameters, PhoenixResponseParameters<OrderData>>
{
    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.StopDelivery;

    public override Task<PhoenixOrderOperationParameters> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        return Task.FromResult(new PhoenixOrderOperationParameters()
        {
            OrderNo = _deliveryInfo.OrderNo,
            NiceClassNumbers = string.Join(',',  _deliveryInfo.OtherInfo?.TrademarkNiceClasses?.Split(';')
                .Select(category => category.PadLeft(2, '0')) ?? ["01"]),
        });
    }

    public override Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(PhoenixOrderOperationParameters request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_phoenixClient);
        return _phoenixClient.OperateOrderAsync(PhoenixUri.StopOrder, request);
    }

    public override async Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        const int deliveryStatus = (int) DeliveryStatus.Ready;

        await Handler.UpdateDeliveryInfoInternalAsync(async deliveryInfo =>
        {
            var history = new DeliHistory
            {
                FinishTime = DateTime.Now,
                ProcId = deliveryInfo.ProcId,
                StartTime = DateTime.Now,
                UserId = Handler.DefaultUserId,
                Operation = DeliveryOperation.GetDescription(),
                DisplayJsonId = null,
                Display = true
            };
            await DeliveryHistoryRepository.InsertAsync(history, cancellationToken);

            deliveryInfo.Status = deliveryStatus;
            deliveryInfo.OperationResult = true;
        }, cancellationToken);
    }

    public Task<bool> HandleExceptionAsync(Exception ex, CancellationToken cancellationToken = default)
    {
        return Handler.HandleExceptionInternalAsync(ex, false, true, cancellationToken);
    }
}