﻿namespace iPlatformExtension.Model.Attributes;

/// <summary>
/// 用于指定特殊属性的序列化源
/// </summary>
[AttributeUsage(AttributeTargets.Property)]
public sealed class JsonSerializationSourceAttribute : Attribute
{
    /// <summary>
    /// 属性源名称
    /// </summary>
    public string SourceName { get; }
    
    /// <summary>
    /// 是否忽略源属性的序列化
    /// </summary>
    /// <remarks>默认是true</remarks>
    public bool IgnoreSource { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="sourceName">源属性名称</param>
    /// <param name="ignoreSource">是否忽略源属性的序列化</param>
    public JsonSerializationSourceAttribute(string sourceName, bool ignoreSource = true)
    {
        SourceName = sourceName;
        IgnoreSource = ignoreSource;
    }
}