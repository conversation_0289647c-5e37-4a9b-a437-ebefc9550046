﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 分页查询参数
/// </summary>
public interface IPageQuery
{
    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; }

    /// <summary>
    /// 页面大小
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// 总数
    /// </summary>
    public long? Total { get; set; }

    /// <summary>
    /// 解构函数
    /// </summary>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页面大小</param>
    /// <param name="total">总数</param>
    void Deconstruct(out int pageIndex, out int pageSize, out long? total)
    {
        pageIndex = PageIndex;
        pageSize = PageSize;
        total = Total;
    }
}