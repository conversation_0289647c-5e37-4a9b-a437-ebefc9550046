﻿using System.IO.Compression;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File;

internal sealed class DeliveryFilesCompressCommandHandler(
    IHttpClientFactory httpClientFactory,
    IFreeSql freeSql,
    IBaseCtrlProcRepository baseCtrlProcRepository)
    : IRequestHandler<DeliveryFilesCompressCommand, string>
{
    public async Task<string> Handle(DeliveryFilesCompressCommand request, CancellationToken cancellationToken)
    {
        var procId = request.ProcId;
        var procInfo = await freeSql.Select<CaseProcInfo>(procId).ToOneAsync(info => new CaseProcInfo()
        {
            ProcId = info.ProcId,
            ProcNo = info.ProcNo,
            CtrlProcId = info.CtrlProcId
        }, cancellationToken);
        var procName = await baseCtrlProcRepository.GetChineseValueAsync(procInfo.CtrlProcId);

        if (procName?.Contains('/') ?? false)
        {
            procName = procName.Replace("/", "或");
        }

        var directoryInfo = CreateDirectory(procId);
        var downloadClient = httpClientFactory.CreateClient();
        foreach (var deliveryFile in request.DeliveryFiles)
        {
            var fileName = $"{directoryInfo.FullName}/{Path.GetFileNameWithoutExtension(deliveryFile.FileName)}{deliveryFile.FileEx}";
            await using var fileStream = System.IO.File.Open(fileName, FileMode.Create);
            var downloadStream = await downloadClient.GetStreamAsync(deliveryFile.Url, cancellationToken);
            await downloadStream.CopyToAsync(fileStream, cancellationToken);
        }

        var zipFileName = $"./temp/{procInfo.ProcNo}_{procName}_递交文件.zip";
        ZipFile.CreateFromDirectory(directoryInfo.FullName, zipFileName, CompressionLevel.SmallestSize, false);
        directoryInfo.Delete(true);

        return zipFileName;
    }
    
    private static string GenerateName(string procId) => $"{procId}-{DateTime.Now.Ticks}";

    private static DirectoryInfo CreateDirectory(string procId)
    {
        var directoryName = $"./temp/{GenerateName(procId)}";
        if (!Directory.Exists(directoryName))
        {
            return Directory.CreateDirectory(directoryName);
        }

        return new DirectoryInfo(directoryName);
    }
}