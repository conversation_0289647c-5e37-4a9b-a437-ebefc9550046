﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.AnalysisRule;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AnalysisRule
{
    /// <summary>
    /// 设置承办人
    /// </summary>
    internal sealed class SetUndertakeUserCommandHandler(
        IMailReceiveFlowRepository mailReceiveFlowRepository,
        IFreeSql<MailCenterFreeSql> freeSql,
        IHttpContextAccessor context
    ) : IRequestHandler<SetUndertakeUserCommand>
    {
        public async Task Handle(
            SetUndertakeUserCommand request,
            CancellationToken cancellationToken
        )
        {
            var userId = context.HttpContext?.User.GetUserID() ?? string.Empty;
            var mailReceives = await freeSql
                .Select<MailReceive>()
                .WithLock()
                .Where(it => request.MailId.Contains(it.MailId))
                .Where(it =>
                    it.Status != SysEnum.ReceiveFileType.Handle.GetHashCode()
                    && it.Status != SysEnum.ReceiveFileType.Sort.GetHashCode()
                )
                .ToListAsync(it => it.MailId, cancellationToken);

            if (mailReceives.Any())
            {
                throw new ApplicationException("邮件状态必须为待分拣或办理中状态");
            }
            var receiveFlows = await mailReceiveFlowRepository
                .Where(it => request.MailId.Contains(it.MailId))
                .WithLock()
                .ToListAsync(cancellationToken);
            receiveFlows.ForEach(it =>
            {
                it.UndertakeUserId = request.UserId;
                it.UpdateTime = DateTime.Now;
                it.UpdateBy = userId;
            });
            await mailReceiveFlowRepository.UpdateAsync(receiveFlows, cancellationToken);
            if (receiveFlows.Count() != 0 && (receiveFlows.Count() < request.MailId.Count))
            {
                var mailIdList = request
                    .MailId.Where(it => !receiveFlows.Select(x => x.MailId).Contains(it))
                    .Select(it => new MailReceiveFlow
                    {
                        Id = Guid.NewGuid().ToString(),
                        CreateBy = userId,
                        CreateTime = DateTime.Now,
                        MailId = it,
                        UndertakeUserId = request.UserId,
                    });

                await mailReceiveFlowRepository.InsertAsync(mailIdList, cancellationToken);
            }

            if (receiveFlows.Count == 0)
            {
                var mailReceiveFlows = request.MailId.Select(it => new MailReceiveFlow
                {
                    Id = Guid.NewGuid().ToString(),
                    CreateBy = userId,
                    CreateTime = DateTime.Now,
                    MailId = it,
                    UndertakeUserId = request.UserId,
                });
                await mailReceiveFlowRepository.InsertAsync(mailReceiveFlows, cancellationToken);
            }
        }
    }
}
