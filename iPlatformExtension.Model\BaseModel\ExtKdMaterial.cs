using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 金蝶物料编号
	/// </summary>
	[ Table(Name = "ext_kd_material", DisableSyncStructure = true)]
	public partial class ExtKdMaterial {

		/// <summary>
		/// 物料编码
		/// </summary>
		[ Column(Name = "code", DbType = "varchar(255)", IsPrimary = true, IsNullable = false)]
		public string Code { get; set; }

		/// <summary>
		/// 物料名称
		/// </summary>
		[ Column(Name = "name", DbType = "varchar(255)", IsNullable = false)]
		public string Name { get; set; }

	}

}
