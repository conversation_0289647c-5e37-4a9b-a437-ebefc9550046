using iPlatformExtension.MailCenter.Applications.Models;
using iPlatformExtension.MailCenter.Applications.Models.Process;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.Process;

/// <summary>
/// 获取最近7天办理发件待审清单
/// </summary>
/// <param name="Search">搜索</param>
/// <param name="Type">全部：0 /我审核的：1 /我承办的：2 /其他：3</param>
public record RecentlySendProcessedQuery(string? Search, int? Type) : PageModel, IRequest<IEnumerable<SendProcessListDto>>;
