﻿using System.Linq.Expressions;
using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 接口定义了系统日志功能的要求，用于在更新实体信息时记录日志。
/// </summary>
/// <typeparam name="TEntity">实体类型，必须是类类型。</typeparam>
public interface ISystemLoggable<TEntity> where TEntity : class
{
    /// <summary>
    /// 获取父级表单名称，用于标识日志来源。
    /// </summary>
    string BaseFormName => "update_case_info";
    
    /// <summary>
    /// 获取基础键的表达式，用于确定关联父级对象的唯一键。
    /// </summary>
    [JsonIgnore]
    Expression<Func<TEntity, string>> BaseKey { get; }
    
}
