﻿using iPlatformExtension.Common.Clients.Mail;
using MailKit;
using MailKit.Security;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.ObjectPools.SMTP;

public sealed class SmtpClientPooledObjectPolicy(
    string serviceKey,
    IOptionsMonitor<MailAccount> accountOptions, 
    IHostEnvironment hostEnvironment,
    ILogger<SmtpClientPooledObjectPolicy> logger) : 
    PooledObjectPolicy<ScopedPooledSmtpClient>
{
    public string ServiceKey => serviceKey;

    public MailAccount MailAccountOptions => accountOptions.Get(serviceKey);
    
    public override ScopedPooledSmtpClient Create()
    {
        var options = accountOptions.Get(serviceKey);
        var client = hostEnvironment.IsProduction()
            ? new ScopedPooledSmtpClient()
            : new ScopedPooledSmtpClient(new ProtocolLogger(Console.OpenStandardOutput()));

        client.Timeout = Timeout.Infinite;
        client.Connect(options.Host, options.Port, SecureSocketOptions.None);
        client.Authenticate(options.Username, options.Password);

        return client;
    }

    public override bool Return(ScopedPooledSmtpClient obj)
    {
        logger.LogInformation("return to the pool");
        return true;
    }
}