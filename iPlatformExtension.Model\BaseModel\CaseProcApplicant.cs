﻿using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;
using iPlatformExtension.Model.Attributes;
using iPlatformExtension.Model.Constants;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 任务申请人
/// </summary>
[Table(Name = "case_proc_applicant", DisableSyncStructure = true)]
public sealed class CaseProcApplicant 
{

	/// <summary>
	/// 变更id
	/// </summary>
	[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
	[Display(Name = "任务申请人id", Order = 0)]
	[Hide]
	public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

	/// <summary>
	/// 地址中文
	/// </summary>
	[Column(Name = "addr_cn", StringLength = 500, IsNullable = false)]
	[Display(Name = "地址（中文）", Order = 4)]
	public string AddrCn { get; set; } = "";

	/// <summary>
	/// 地址英文
	/// </summary>
	[Column(Name = "addr_en", StringLength = 500, IsNullable = false)]
	[Display(Name = "地址（英文）", Order = 5)]
	public string AddrEn { get; set; } = "";

	/// <summary>
	/// 申请人id
	/// </summary>
	[Column(Name = "applicant_id", StringLength = 50, IsNullable = false)]
	public string ApplicantId { get; set; } = "";

	/// <summary>
	/// 申请人英文
	/// </summary>
	[Column(Name = "applicant_name_cn", StringLength = 500, IsNullable = false)]
	[Display(Name = "名称（中文）", Order = 2)]
	public string ApplicantNameCn { get; set; } = "";

	/// <summary>
	/// 申请人中文
	/// </summary>
	[Column(Name = "applicant_name_en", StringLength = 500, IsNullable = false)]
	[Display(Name = "名称（英文）", Order = 3)]
	public string ApplicantNameEn { get; set; } = "";

	/// <summary>
	/// 申请人类型
	/// </summary>
	[Column(Name = "applicant_type_id", StringLength = 50, IsNullable = false)]
	public string ApplicantTypeId { get; set; } = "";

	/// <summary>
	/// 证件号码
	/// </summary>
	[Column(Name = "card_no", StringLength = 50, IsNullable = false)]
	public string CardNo { get; set; } = "";

	/// <summary>
	/// 证件类型
	/// </summary>
	[Column(Name = "card_type", StringLength = 50, IsNullable = false)]
	public string CardType { get; set; } = "";

	/// <summary>
	/// 变更类型
	/// </summary>
	[Column(Name = "change_type", StringLength = 50, IsNullable = false)]
	[KeyValueDisplay(IsSystemDictionary = true, DictionaryName = SystemDictionaryName.TrademarkDeliveryBusinessType)]
	[Display(Name = "办理业务", Order = 1)]
	public string ChangeType { get; set; } = "";

	/// <summary>
	/// 国家
	/// </summary>
	[Column(Name = "country_id", StringLength = 50, IsNullable = false)]
	public string CountryId { get; set; } = "";

	/// <summary>
	/// 创建时间
	/// </summary>
	[Column(Name = "create_time", InsertValueSql = "getdate()")]
	public DateTime CreateTime { get; set; }

	/// <summary>
	/// 创建人
	/// </summary>
	[Column(Name = "create_user_id", StringLength = 50, IsNullable = false)]
	public string CreateUserId { get; set; } = "";

	/// <summary>
	/// 证明材料是否中文
	/// </summary>
	[Column(Name = "is_chinese_identity")]
	public bool IsChineseIdentity { get; set; } = true;

	/// <summary>
	/// 任务id
	/// </summary>
	[Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
	public string ProcId { get; set; } = "";

	/// <summary>
	/// 更新时间
	/// </summary>
	[Column(Name = "update_time", InsertValueSql = "getdate()")]
	public DateTime UpdateTime { get; set; }

	/// <summary>
	/// 更新人
	/// </summary>
	[Column(Name = "update_user_id", StringLength = 50, IsNullable = false)]
	public string UpdateUserId { get; set; } = "";

    /// <summary>
    /// 邮编
    /// </summary>
    [Column(Name = "post_code", StringLength = 50, IsNullable = false)]
    public string PostCode { get; set; } = "";

    /// <summary>
    /// 变更中文
    /// </summary>
    [Column(Name = "change_type_name", StringLength = 50, IsNullable = false)]
    public string ChangeTypeName { get; set; } = "";
    

}