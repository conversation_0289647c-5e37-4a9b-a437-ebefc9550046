﻿using System.Collections;

namespace iPlatformExtension.Common.Enumerable;

public sealed class EmptyGroup<TKey, TValue> : IGrouping<TKey, TValue>
{
    private static readonly IEnumerable<TValue> emptyValues = [];
    
    public IEnumerator<TValue> GetEnumerator()
    {
        return emptyValues.GetEnumerator();
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }

    public TKey Key => default!;
}