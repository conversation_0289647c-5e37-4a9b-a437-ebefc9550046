﻿using iPlatformExtension.Model.BaseModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace CommonTest
{
    public class CopyTest
    {
        [Fact]
        public void main()
        {
            var s = new DeliApplicant() { ApplicantNameCn = "1234556" };

            var json = JsonSerializer.Serialize(s);
            var lst = new List<DeliApplicant> { new DeliApplicant() { ApplicantNameCn = "1111111" } //快照
        , new DeliApplicant() { ApplicantNameCn = "222222" }
        };


            var lst2 = new List<DeliApplicantcopy2> { new DeliApplicantcopy2() { ApplicantNameCn = "222222" } //源数据
        , new DeliApplicantcopy2() { ApplicantNameCn = "1111111" }
        };


            //DeliApplicantCopy 需要做对比的字段

            var lst111 = JsonSerializer.Deserialize<List<DeliApplicantCopy>>(JsonSerializer.Serialize(lst.OrderBy(o => o.ApplicantNameCn)));//
            var lst222= JsonSerializer.Deserialize<List<DeliApplicantCopy>>(JsonSerializer.Serialize(lst2.OrderBy(o => o.ApplicantNameCn)));//

            var jsonlst = JsonSerializer.Serialize(lst111.OrderBy(o => o.ApplicantNameCn));
            var json2st = JsonSerializer.Serialize(lst222.OrderBy(o => o.ApplicantNameCn));

            if (jsonlst == json2st)
            {

            }




            var s3 = JsonSerializer.Deserialize<DeliApplicantCopy>(json);
            var json3 = JsonSerializer.Serialize(s3);


            var s2 = new DeliApplicantCopy() { ApplicantNameCn = "1234556" };
            var json2 = JsonSerializer.Serialize(s2);

            if (json2 == json3)
            {

            }

        }
    }
}
