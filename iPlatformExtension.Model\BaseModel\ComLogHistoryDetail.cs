using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "com_log_history_detail", DisableSyncStructure = true)]
	public partial class ComLogHistoryDetail {

		[ Column(Name = "detail_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DetailId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "error_code", StringLength = 50)]
		public string ErrorCode { get; set; }

		[ Column(Name = "error_message", StringLength = 1000)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "finish_time")]
		public DateTime? FinishTime { get; set; }

		[ Column(Name = "form_code", StringLength = 50)]
		public string FormCode { get; set; }

		[ Column(Name = "history_id", StringLength = 50)]
		public string HistoryId { get; set; }

		[ Column(Name = "start_time")]
		public DateTime? StartTime { get; set; }

		[ Column(Name = "status", StringLength = 50)]
		public string Status { get; set; }

	}

}
