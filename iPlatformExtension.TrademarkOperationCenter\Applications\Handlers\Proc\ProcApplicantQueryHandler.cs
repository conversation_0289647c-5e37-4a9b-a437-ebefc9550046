﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc
{
    internal sealed class ProcApplicantQueryHandler(IFreeSql freeSql) : IRequestHandler<ProcApplicantQuery, ProcApplicantDto>
    {
        public async Task<ProcApplicantDto> Handle(ProcApplicantQuery request, CancellationToken cancellationToken)
        {
            return await freeSql.Select<CaseProcApplicant>().WithLock().Where(applicant => applicant.Id == request.Id).FirstAsync(applicant =>
            new ProcApplicantDto() {
                Id = applicant.Id,
                ProcId = applicant.ProcId,
                ChangeType = applicant.ChangeType,
                ApplicantId = applicant.Id,
                ApplicantNameCn = applicant.ApplicantNameCn,
                ApplicantNameEn = applicant.ApplicantNameEn,
                AddressCn = applicant.AddrCn,
                AddressEn = applicant.AddrEn,
                CountryId = applicant.CountryId,
                TypeId = applicant.ApplicantTypeId,
                IsChineseIdentity = applicant.IsChineseIdentity,
                CertificationType = applicant.CardType,
                CertificationNumber = applicant.CardNo,
                PostCode = applicant.PostCode,
                ChangeTypeName= applicant.ChangeTypeName
            });
        
        }
    }
}
