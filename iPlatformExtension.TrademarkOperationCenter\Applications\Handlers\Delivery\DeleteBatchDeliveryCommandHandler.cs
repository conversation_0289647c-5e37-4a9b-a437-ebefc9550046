﻿using System.Text;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DeleteBatchDeliveryCommandHandler(IMediator mediator, ObjectPool<StringBuilder> stringBuilderPool)
    : IRequestHandler<DeleteBatchDeliveryCommand>
{
    public async Task Handle(DeleteBatchDeliveryCommand request, CancellationToken cancellationToken)
    {
        var batchOperationResult = new DeliveryBatchOperationResult();
        foreach (var (procId, version) in request.DeliveryItems)
        {
            batchOperationResult.Results.Add(await mediator.Send(new DeleteDeliveryBatchItemCommand(procId, version),
                cancellationToken));
        }
        
        if (!batchOperationResult.Success)
        {
            var stringBuilder = stringBuilderPool.Get();
            batchOperationResult.Message = batchOperationResult.GetMessage(stringBuilder);
            stringBuilderPool.Return(stringBuilder);

            throw new ApplicationException(batchOperationResult.Message);
        }
    }
}