using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_pay_batch", DisableSyncStructure = true)]
	public partial class BillPayBatch {

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "apply_user_id", StringLength = 50)]
		public string ApplyUserId { get; set; }

		[ Column(Name = "apply_user_tel", StringLength = 50)]
		public string ApplyUserTel { get; set; }

		[ Column(Name = "bank_id", StringLength = 50)]
		public string? BankId { get; set; }

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "batch_name", StringLength = 100)]
		public string BatchName { get; set; }

		[ Column(Name = "batch_status", StringLength = 50)]
		public string BatchStatus { get; set; }

		[ Column(Name = "beneficiary_bank_id", StringLength = 50)]
		public string BeneficiaryBankId { get; set; }

		[ Column(Name = "business_no", StringLength = 100)]
		public string BusinessNo { get; set; }

		[ Column(Name = "charge_bank_id", StringLength = 50)]
		public string ChargeBankId { get; set; }

		[ Column(Name = "company_id", StringLength = 50)]
		public string? CompanyId { get; set; }

		[ Column(Name = "contract_no", StringLength = 50)]
		public string ContractNo { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "currency_rate", DbType = "money")]
		public decimal? CurrencyRate { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "district_id", StringLength = 50)]
		public string DistrictId { get; set; }

		[ Column(Name = "domestic_amount", DbType = "money")]
		public decimal? DomesticAmount { get; set; }

		[ Column(Name = "domestic_currency", StringLength = 50)]
		public string DomesticCurrency { get; set; }

		[ Column(Name = "exchange_rate_bill_to_domestic", DbType = "money")]
		public decimal? ExchangeRateBillToDomestic { get; set; }

		[ Column(Name = "exchange_rate_bill_to_pay", DbType = "numeric(18,10)")]
		public decimal? ExchangeRateBillToPay { get; set; }

		[ Column(Name = "exchange_rate_pay_to_domestic", DbType = "money")]
		public decimal? ExchangeRatePayToDomestic { get; set; }

		[ Column(Name = "fx_amount", DbType = "money")]
		public decimal? FxAmount { get; set; }

		[ Column(Name = "iban_code", StringLength = 50)]
		public string IbanCode { get; set; }

		[ Column(Name = "invoice_no", StringLength = 50)]
		public string InvoiceNo { get; set; }

		[ Column(Name = "paid_amount", DbType = "money")]
		public decimal? PaidAmount { get; set; }

		[ Column(Name = "paid_currency", StringLength = 50)]
		public string PaidCurrency { get; set; }

		[ Column(Name = "pay_date")]
		public DateTime? PayDate { get; set; }

		[ Column(Name = "print_time")]
		public DateTime? PrintTime { get; set; }

		[ Column(Name = "priority_type", StringLength = 50)]
		public string PriorityType { get; set; }

		[ Column(Name = "purchase_amount", DbType = "money")]
		public decimal? PurchaseAmount { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string? Remark { get; set; }

		[ Column(Name = "remittance_charges", DbType = "money")]
		public decimal? RemittanceCharges { get; set; }

		[ Column(Name = "remittance_charges_borne", StringLength = 50)]
		public string RemittanceChargesBorne { get; set; }

		[ Column(Name = "remittance_charges_currency", StringLength = 50)]
		public string RemittanceChargesCurrency { get; set; }

		[Column(Name = "remittance_charges_todetail", StringLength = 50)]
		public string? RemittanceChargesTodetail { get; set; } = string.Empty;

		[ Column(Name = "remittance_information", StringLength = 140)]
		public string RemittanceInformation { get; set; }

		[ Column(Name = "remittance_method", StringLength = 50)]
		public string RemittanceMethod { get; set; }

		[ Column(Name = "remittance_type", StringLength = 50)]
		public string RemittanceType { get; set; }

		[ Column(Name = "transac_code", StringLength = 50)]
		public string TransacCode { get; set; }

		[ Column(Name = "transac_remark", StringLength = 100)]
		public string TransacRemark { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
