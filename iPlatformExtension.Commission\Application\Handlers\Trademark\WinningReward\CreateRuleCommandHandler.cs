﻿using AutoMapper;
using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class CreateRuleCommandHandler(
    IMapper mapper,
    IHttpContextAccessor httpContextAccessor,
    IWinningRewardRuleRepository winningRewardRuleRepository) : IRequestHandler<CreateRuleCommand>
{
    public async Task Handle(CreateRuleCommand request, CancellationToken cancellationToken)
    {
        var rule = mapper.Map<WinningRewardRule>(request.Dto);

        if (await winningRewardRuleRepository
                .Where(rewardRule => rewardRule.CtrlProcId == rule.CtrlProcId)
                .Where(rewardRule => rewardRule.CaseDirection == rule.CaseDirection)
                .Where(rewardRule => rewardRule.SituationChanged == rule.SituationChanged)
                .Where(rewardRule => rewardRule.RulingResult == rule.RulingResult)
                .Where(rewardRule => rewardRule.IsEnabled == true)
                .AnyAsync(cancellationToken))
        {
            throw new ApplicationException("已存在相同的有效胜诉奖励规则");
        }
        
        var userId = (httpContextAccessor.HttpContext?.User).GetUserId();
        rule.Creator = rule.Updater = userId;
        rule.CreationTime = rule.UpdateTime = DateTime.Now;
        
        await winningRewardRuleRepository.InsertAsync(rule, cancellationToken);
    }
}