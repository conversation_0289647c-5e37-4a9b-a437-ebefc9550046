﻿using System.Security.Claims;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Unicode;
using Autofac.Extensions.DependencyInjection;
using Confluent.Kafka;
using Hangfire;
using Hangfire.Dashboard;
using Hangfire.MemoryStorage;
using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Common.Authentication.Providers;
using iPlatformExtension.Common.Authorization;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Db;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Filters;
using iPlatformExtension.Common.Formatters.Input;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using iPlatformExtension.Common.MQ.KafKa.Handlers.Consumer;
using iPlatformExtension.Common.MQ.KafKa.Handlers.Producer;
using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.SchedulerJob.Jobs;
using iPlatformExtension.TrademarkOperationCenter.Consumers;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Authorization.Proc;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Authorization.SendDelivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Nacos.AspNetCore.V2;
using NLog.Web;

[assembly:ApplicationPart("iPlatformExtension.Common")]

var builder = WebApplication.CreateBuilder(args);

var configuration = builder.Configuration;
var environment = builder.Environment;
var logging = builder.Logging;

logging.ClearProviders();
logging.AddSimpleConsole(options => options.IncludeScopes = true);
logging.AddNLogWeb(new NLogAspNetCoreOptions
{
    RemoveLoggerFactoryFilter = false,
    LoggingConfigurationSectionName = "NLog"
});

if (!environment.IsLocal())
{
    configuration.AddNacosV2Configuration(configuration.GetSection("nacos"));
}
else
{
    configuration.AddJsonFile($"clients.{environment.EnvironmentName}.json", true, true);
    configuration.AddJsonFile($"mqs.{environment.EnvironmentName}.json", true, true);
}

// Add services to the container.
if (!environment.IsLocal())
{   
    builder.Services.AddNacosAspNet(configuration);
    builder.Services.AddNacosServiceDiscovery(options => options.AllowedSchemes = [Uri.UriSchemeHttp]);
    builder.Services.AddNacosServiceClient("iPlatformExtension.Public");
}

if (environment.IsLocal())
{
    builder.Services.AddLocalServiceClient("iPlatformExtension.Public");
}

builder.Services.AddControllers(options =>
{
    options.InputFormatters.Insert(0, JsonPatchInputFormatterExtension.GetJsonPatchInputFormatter());
    options.Filters.Add<ActionExceptionFilter<ResultData>>();
    options.Filters.Add<ActionResultFilter<ResultData>>();
}).AddJsonOptions(options =>
{
    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.DictionaryKeyPolicy = JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
    options.JsonSerializerOptions.Converters.Add(new DateTimeJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new NullableDateTimeJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter(JsonNamingPolicy.CamelCase, false));
    options.JsonSerializerOptions.Converters.Add(new ProcExtensionKeyConverter());
    options.JsonSerializerOptions.Converters.Add(new DateOnlyJsonConverter());
    if (environment.IsLocal())
    {
        options.JsonSerializerOptions.WriteIndented = true;
    }
});

builder.Services.AddPhoenixClient("AcipKey1");
builder.Services.AddPhoenixClient("AcipKey2");
builder.Services.AddPhoenixClient("BeijingKey");
builder.Services.AddPhoenixClient();

builder.Services.ConfigureHttpJsonOptions(options =>
{
    options.SerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.SerializerOptions.DictionaryKeyPolicy = JsonNamingPolicy.CamelCase;
    options.SerializerOptions.Converters.Add(new DateTimeJsonConverter());
    options.SerializerOptions.Converters.Add(new DateTimeOffsetJsonConverter());
    options.SerializerOptions.Converters.Add(new NullableDateTimeJsonConverter());
    options.SerializerOptions.Converters.Add(new NullableDateTimeOffsetConverter());
    options.SerializerOptions.Converters.Add(new ProcExtensionKeyConverter());
    options.SerializerOptions.Converters.Add(new JsonStringEnumConverter(JsonNamingPolicy.CamelCase, false));
    options.SerializerOptions.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddHuaweiObsClient();

builder.Services.AddSendDeliveryPolicies();

builder.Services.AddKafKaConsumers<FlowEventMessage>().ConfigureServerOptions(serverOptions =>
    {
        serverOptions.GroupId = $"{environment.ApplicationName}-{environment.EnvironmentName}-{nameof(FlowEventMessage)}";
        serverOptions.AutoOffsetReset = AutoOffsetReset.Latest;
        serverOptions.EnableAutoCommit = false;
    }, "KafKa:FlowEvent:ServerOptions").ConfigureConsumerOptions((consumerOptions, _) =>
    {
        var consumerBuilder = consumerOptions.ConsumerBuilder!;
        
        var jsonSerializerOptions = new JsonSerializerOptions(JsonSerializerDefaults.Web);
        jsonSerializerOptions.Converters.Add(new DateTimeJsonConverter());
        jsonSerializerOptions.Converters.Add(new DateTimeOffsetJsonConverter());
        
        consumerBuilder.SetKeyDeserializer(new JsonMessageKeyConverter(jsonSerializerOptions));
        consumerBuilder.SetValueDeserializer(
            new JsonMessageValueConverter<FlowEventMessage>(jsonSerializerOptions));

    }, "KafKa:FlowEvent")
    .AddConsumer<TrademarkDeliveryConsumer>()
    .AddConsumeFailedHandler<IgnoreConsumeFailedHandler>()
    .Build();

builder.Services.AddKafKaProducer<DeliveryMessage>().ConfigureServerOptions(options =>
{
    options.Acks = Acks.All;
}, "KafKa:Producer").ConfigureProducer((options, _) =>
{
    options.ProducerBuilder!.SetKeySerializer(
        new JsonMessageKeyConverter(new JsonSerializerOptions(JsonSerializerDefaults.Web)));
    options.ProducerBuilder!.SetValueSerializer(
        new JsonMessageValueConverter<DeliveryMessage>(new JsonSerializerOptions(JsonSerializerDefaults.Web)));
}).AddProducerInterceptor<W3CTracingHeaderProducerHandler<MessageKey, DeliveryMessage>>().Build();

builder.Services.AddKafKaProducer<FlowEventMessage>().ConfigureServerOptions("KafKa:Producer")
    .ConfigureProducer(options =>
    {
        options.ProducerBuilder ??= new ProducerBuilder<MessageKey, FlowEventMessage>(options.ServerOptions);

        var jsonSerializerOptions = new JsonSerializerOptions(JsonSerializerDefaults.Web);
        options.ProducerBuilder.SetKeySerializer(new JsonMessageKeyConverter(jsonSerializerOptions));
        options.ProducerBuilder.SetValueSerializer(
            new JsonMessageValueConverter<FlowEventMessage>(jsonSerializerOptions));
    })
    .AddProducerInterceptor<UserIdProducerHandler<MessageKey, FlowEventMessage>>()
    .AddProducerInterceptor<W3CTracingHeaderProducerHandler<MessageKey, FlowEventMessage>>()
    .Build();

builder.Services.AddAutoMapper(typeof(Program));
builder.Services.AddObjectPools();
builder.Services.AddPlatformFreeSql(configuration.GetConnectionString("Default") ?? string.Empty)
    .ConfigureGlobalCurdLogging("Logging:FreeSql:Curd");
if (environment.IsProduction())
{
    builder.Services.AddMongoDbContext<PlatformMongoDbContext>(configuration.GetConnectionString("Log")
                                                     ?? throw new ArgumentNullException("ConnectionStrings:Log", "缺少数据库连接字符串"));
    builder.Services.AddEntityChangeLogs();
}
builder.Services.AddDataService();
builder.Services.AddCache().ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
{
    options.ConnectionString = configuration.GetConnectionString("Redis");
    options.Converters.Add(new ClaimsIdentityConverter());
});
builder.Services.AddValidation();
builder.Services.AddMediatRServices().Add(DependencyInjectionExtension.AddApplicationServices).Build();
builder.Services.AddAuthentication(options =>
{
    options.DefaultForbidScheme = BladeAuthOptions.SchemeName;
    options.DefaultScheme = PlatformAuthOptions.SchemeName;
    options.DefaultChallengeScheme = BladeAuthOptions.SchemeName;
}).AddJwtBearer(PlatformAuthOptions.SchemeName,
    options =>
    {
        var platformAuthenticationOptions =
            configuration.Get<PlatformAuthOptions>(sectionKey: "IPlatformAuth");
        options.TokenValidationParameters = new TokenValidationParameters
        {
            IssuerSigningKey = new SymmetricSecurityKey(
                platformAuthenticationOptions?.SecurityKey.GetBytes(Encoding.UTF8) ??
                throw new ArgumentNullException(nameof(configuration))),
            ValidateIssuerSigningKey = true,
            ValidAudiences = platformAuthenticationOptions.Audiences,
            ValidIssuers = platformAuthenticationOptions.Issuers
        };
        options.Events = new JwtBearerEvents
        {
            OnTokenValidated = AuthenticationExtension.ValidateUserAsync
        };
        options.SaveToken = true;
        options.ForwardDefaultSelector = context =>
            context.Request.Headers.Authorization.Count > 0 ? PlatformAuthOptions.SchemeName : null;
    }).AddScheme<BladeAuthOptions, BladeAuthenticationHandler>(BladeAuthOptions.SchemeName, "bladeX token验证", options =>
    {
        options.Events.OnTokenValidated = AuthenticationExtension.ValidateUserAsync;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            ValidateIssuer = false,
            ValidateAudience = false,
            IssuerSigningKey = new SymmetricSecurityKey(
                configuration["BladeAuth:SecurityKey"]?.GetBytes(Encoding.UTF8) ??
                throw new ArgumentNullException("BladeAuth:SecurityKey", "缺少blade-auth秘钥"))
        };
        options.ForwardDefaultSelector = context =>
            context.Request.Headers.ContainsKey(options.TokenHeaderName) ? BladeAuthOptions.SchemeName : null;
    }).ReplaceAuthenticationSchemeProvider<ForwardAuthenticationSchemeProvider>();

builder.Services.AddHttpLogging(options =>
{
    options.LoggingFields |= HttpLoggingFields.RequestHeaders;
    options.LoggingFields |= HttpLoggingFields.RequestPath;
    options.LoggingFields |= HttpLoggingFields.RequestQuery;
    options.LoggingFields |= HttpLoggingFields.RequestBody;
    options.LoggingFields |= HttpLoggingFields.ResponsePropertiesAndHeaders;
    options.LoggingFields |= HttpLoggingFields.ResponseBody;
    options.RequestBodyLogLimit = 327680;
});
builder.Services.AddHttpContextAccessor();

if (!environment.IsLocal())
{
    builder.Services.AddSingleton((p) =>
    {
        var connection = new HubConnectionBuilder()
            .WithUrl(new Uri(configuration["ConnectionStrings:SignalR"] ?? throw new ArgumentNullException("ConnectionStrings:SignalR", "缺少SignalR连接")))
            .WithAutomaticReconnect(new RetryPolicy())
            .Build();
        connection.StartAsync();
        return connection;
    });
}

//builder.Services.AddScoped<IScopeDependency, FlowAnalyseJob>();
builder.Services.AddScoped<IAuthorizationHandler, DeliveryCommandAuthorizationHandler>();
builder.Services.AddScoped<IAuthorizationHandler, ProcAuthorizationHandler>();
builder.Services.AddTransient<IAuthorizationHandler, BatchDeliveriesAuthorizationHandler>();
builder.Services.AddTransient<IAuthorizationMiddlewareResultHandler, DefaultAuthorizationResultHandler>();
builder.Services.AddAuthorizationBuilder()
    .AddPolicy("SendDelivery", policyBuilder =>
    {
        policyBuilder.AuthenticationSchemes.Add(PlatformAuthOptions.SchemeName);
        policyBuilder.RequireClaim(ClaimTypes.NameIdentifier);
        policyBuilder.Requirements.Add(new SendDeliveryAuthorizationRequirement(FlowType.Delivery, "TII")
        {
            RequireToAuthorizeButtons =
                {DeliveryButton.StartUpDelivery, DeliveryButton.StopDelivery, DeliveryButton.WithdrawDelivery}
        });
    })
    .AddPolicy("Proc", policyBuilder =>
    {
        policyBuilder.RequireClaim(ClaimTypes.NameIdentifier);
        policyBuilder.AuthenticationSchemes.Add(PlatformAuthOptions.SchemeName);
        policyBuilder.RequireRole("商标流程人员");
    })
    .AddPolicy("DeliveriesValidation", policyBuilder =>
    {
        policyBuilder.AuthenticationSchemes.Add(PlatformAuthOptions.SchemeName);
        policyBuilder.RequireAuthenticatedUser();
    });

if (!environment.IsProduction())
{
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = builder.Environment.ApplicationName,
            Version = "2.8.2.RELEASE-v2.7.11",
            Description = "商标作业中心描述"
        });
        var documentPath = Path.Combine(AppContext.BaseDirectory, $"{builder.Environment.ApplicationName}.xml");
        c.IncludeXmlComments(documentPath, true);

        var modelPath = Path.Combine(AppContext.BaseDirectory, "iPlatformExtension.Model.xml");
        c.IncludeXmlComments(modelPath, true);
        c.OrderActionsBy(o => o.RelativePath);
    });

}

builder.Services.TryAddSingleton<EntityTypeInfoProvider>();


if (!environment.IsLocal())
{
    builder.Services.AddHangfire(hangfireConfiguration => hangfireConfiguration.SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
        .UseMemoryStorage());
    builder.Services.AddHangfireServer(serverOptions =>
    {
        serverOptions.SchedulePollingInterval = TimeSpan.FromSeconds(10);
        serverOptions.Queues = ["flow"];
    });
}

// builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory());

var app = builder.Build();

// app.Logger.LogEnvironmentVariables();

app.UseExceptionHandler("/Error");

app.UseW3CTraceResponse();

// Configure the HTTP request pipeline.
if (!app.Environment.IsProduction())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.HandleUnsuccessfulResponse();

app.UseRequestEnableBuffering();

app.UseHttpLogging();

app.UseAuthentication();
app.UseAuthorization();

#region hangfire


if (!environment.IsLocal())
{
    app.MapHangfireDashboard(new DashboardOptions()
    {
        Authorization = Array.Empty<IDashboardAuthorizationFilter>(),
        AsyncAuthorization = Array.Empty<IDashboardAsyncAuthorizationFilter>()
    });
    
    var jobManager = app.Services.GetRequiredService<IRecurringJobManager>();
    jobManager.RemoveIfExists("定时流程统计");
    jobManager.AddOrUpdate<FlowAnalyseJob>("定时流程统计", (job) => job.ExecuteAsync(), "*/10 * * * * *", new RecurringJobOptions()
    {
        TimeZone = TimeZoneInfo.Local,
    });
}
#endregion

app.MapControllers();

app.Run();

/// <summary>
/// 商标作业中心程序启动入口
/// </summary>
public partial class Program;