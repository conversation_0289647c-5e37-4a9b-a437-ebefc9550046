﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery
{

    public partial class DeliApplicantComparison
    {

        [Column(Name = "address_cn", StringLength = 500)]
        public string? AddressCn { get; set; }

        [Column(Name = "address_detail", StringLength = 200)]
        public string? AddressDetail { get; set; }

        [Column(Name = "address_en", StringLength = 500)]
        public string? AddressEn { get; set; }


        [Column(Name = "address_id", StringLength = 50)]
        public string AddressId { get; set; } = Guid.NewGuid().ToString().ToUpper();

        [Column(Name = "address_type", StringLength = 50)]
        public string AddressType { get; set; }

        [Column(Name = "applicant_id", StringLength = 50)]
        public string ApplicantId { get; set; }

        [Column(Name = "applicant_name_cn", StringLength = 100)]
        public string ApplicantNameCn { get; set; }

        [Column(Name = "applicant_name_en", StringLength = 200)]
        public string ApplicantNameEn { get; set; }

        [Column(Name = "card_no", StringLength = 50)]
        public string CardNo { get; set; }

        /// <summary>
        /// 证件类型
        /// </summary>
        [Column(Name = "card_type")]
        public string CardType { get; set; } = default!;

        [Column(Name = "city_id", StringLength = 50)]
        public string CityId { get; set; }

        [Column(Name = "country_id", StringLength = 50)]
        public string CountryId { get; set; }

        [Column(Name = "country_id_local", StringLength = 50)]
        public string CountryIdLocal { get; set; }

        [Column(Name = "email", StringLength = 200)]
        public string Email { get; set; }

        [Column(Name = "entity_id", StringLength = 50)]
        public string EntityId { get; set; }

        [Column(Name = "fee_reduce", StringLength = 50)]
        public string FeeReduce { get; set; }

        [Column(Name = "fee_reduce_date")]
        public DateTime? FeeReduceDate { get; set; }

        [Column(Name = "fee_reduce_year", StringLength = 500)]
        public string FeeReduceYear { get; set; }

        [Column(Name = "is_en_addr")]
        public bool? IsEnAddr { get; set; } = false;

        [Column(Name = "is_en_name")]
        public bool? IsEnName { get; set; } = false;

        [Column(Name = "is_represent")]
        public bool? IsRepresent { get; set; }

        [Column(Name = "postcode", StringLength = 50)]
        public string? Postcode { get; set; }

        [Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
        public string ProcId { get; set; }

        [Column(Name = "province_id", StringLength = 50)]
        public string ProvinceId { get; set; }

        [Column(Name = "proxy_no", StringLength = 50)]
        public string ProxyNo { get; set; }

        [Column(Name = "register_code", StringLength = 50)]
        public string RegisterCode { get; set; }

        [Column(Name = "seq")]
        public int? Seq { get; set; }

        [Column(Name = "tel", StringLength = 50)]
        public string Tel { get; set; }

        [Column(Name = "type_id", StringLength = 50)]
        public string TypeId { get; set; }

        /// <summary>
        /// 身份证明原件是否中文
        /// </summary>
        [Column(Name = "is_chinese_identity")]
        public bool IsChineseIdentity { get; set; } = false;

    }

}
