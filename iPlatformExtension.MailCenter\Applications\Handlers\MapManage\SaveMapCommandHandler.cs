﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.MapManage;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.MapManage
{
    /// <summary>
    /// 保存映射配置
    /// </summary>
    internal sealed class SaveMapCommandHandler(IMapConfigRepository mapConfigRepository, IHttpContextAccessor content, 
    IFreeSql<MailCenterFreeSql> freeSql,IBaseCtrlProcRepository baseCtrlProcRepository) : IRequestHandler<SaveMapCommand>
    {
        public async Task Handle(SaveMapCommand request, CancellationToken cancellationToken)
        {
            var now = DateTime.Now;
            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            if (string.IsNullOrWhiteSpace(request.Scope))
            {
                throw new ApplicationException("解析范围不能为空");
            }
            
            if  (request.MapId is null){
                // 检查是否已经存在相同的配置
                var existingScope = await freeSql.Select<MapConfig>().WithLock().AnyAsync(x => x.MapValue == request.MapValue && x.CtrlProcId == request.CtrlProcId, cancellationToken);
                if (existingScope)
                {
                    throw new ApplicationException($"[{request.MapValue}]一[{await baseCtrlProcRepository.GetTextValueAsync(request.CtrlProcId)}]映射关系已存在，请勿重复保存。");
                }
            }
            // 查找是否存在
            var existingMap = await mapConfigRepository.Where(x => x.MapId == request.MapId)
                .FirstAsync(cancellationToken);

            if (existingMap == null)
            {
                // 获取当年最大序号
                var year = DateTime.Now.ToString("yy");
                var prefix = $"SJYS{year}";
                var maxNo = await freeSql.Select<MapConfig>().WithLock()
                    .Where(x => x.MapNo.StartsWith(prefix))
                    .MaxAsync(x => x.MapNo.Substring(6), cancellationToken) ?? "0000";

                // 生成新序号（需要在事务中执行以确保并发安全）
                var sequence = int.Parse(maxNo) + 1;
                var newMapNo = $"{prefix}{sequence:D4}";  // D4 确保是4位数，不足补0

                var newMap = new MapConfig
                {
                    MapId = request.MapId ?? Guid.NewGuid().ToString(),
                    MapNo = newMapNo,  // 设置新生成的编号
                    Scope = request.Scope,
                    CtrlProcId = request.CtrlProcId,
                    IsEnabled = request.IsEnabled,
                    MapValue = request.MapValue,
                    UpdateTime = now,
                    UpdateUser = userId,
                    CreateTime = now,
                    CreateUser = userId,
                };

                await mapConfigRepository.InsertAsync(newMap, cancellationToken);
            }
            else
            {
                // 更新现有记录
                existingMap.Scope = request.Scope;
                existingMap.CtrlProcId = request.CtrlProcId;
                existingMap.IsEnabled = request.IsEnabled;
                existingMap.MapValue = request.MapValue;
                existingMap.UpdateTime = now;
                existingMap.UpdateUser = userId;
                await mapConfigRepository.UpdateAsync(existingMap, cancellationToken);
            }
        }
    }
}

