using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_open_case_info", DisableSyncStructure = true)]
	public partial class AppOpenCaseInfo {

		[ Column(Name = "active_case")]
		public bool ActiveCase { get; set; }

		[ Column(Name = "active_type_id", StringLength = 50)]
		public string ActiveTypeId { get; set; }

		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		[ Column(Name = "app_date")]
		public DateTime? AppDate { get; set; }

		[ Column(Name = "app_no", StringLength = 50)]
		public string AppNo { get; set; }

		[ Column(Name = "app_obj_id", StringLength = 50)]
		public string AppObjId { get; set; }

		[ Column(Name = "applicant_id", StringLength = 50)]
		public string ApplicantId { get; set; }

		[ Column(Name = "apply_channel", StringLength = 50)]
		public string ApplyChannel { get; set; }

		[ Column(Name = "apply_id", StringLength = 50)]
		public string ApplyId { get; set; }

		[ Column(Name = "apply_name", StringLength = 500)]
		public string ApplyName { get; set; }

		[ Column(Name = "apply_no", StringLength = 50)]
		public string ApplyNo { get; set; }

		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "banzheng_riqi")]
		public DateTime? BanzhengRiqi { get; set; }

		[ Column(Name = "belong_company", StringLength = 50)]
		public string BelongCompany { get; set; }

		[ Column(Name = "belong_district", StringLength = 50)]
		public string BelongDistrict { get; set; }

		[ Column(Name = "business_type_id", StringLength = 50)]
		public string BusinessTypeId { get; set; }

		[ Column(Name = "case_descriptions", StringLength = 4000)]
		public string CaseDescriptions { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_emergent_id", StringLength = 50)]
		public string CaseEmergentId { get; set; }

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "case_level_id", StringLength = 50)]
		public string CaseLevelId { get; set; }

		[ Column(Name = "case_name", StringLength = 500)]
		public string CaseName { get; set; }

		[ Column(Name = "case_name_en", StringLength = 500)]
		public string CaseNameEn { get; set; }

		[ Column(Name = "case_status_id", StringLength = 50)]
		public string CaseStatusId { get; set; }

		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "certificate_no", StringLength = 50)]
		public string CertificateNo { get; set; }

		[ Column(Name = "charge_rule", StringLength = 50)]
		public string ChargeRule { get; set; }

		[ Column(Name = "contract_no", StringLength = 200)]
		public string ContractNo { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "crm_case_id", StringLength = 50)]
		public string CrmCaseId { get; set; }

		[ Column(Name = "customer_case_no", StringLength = 100)]
		public string CustomerCaseNo { get; set; }

		[ Column(Name = "customer_id", StringLength = 300)]
		public string CustomerId { get; set; }

		[ Column(Name = "deliver_agency", StringLength = 50)]
		public string DeliverAgency { get; set; }

		[ Column(Name = "deliver_date")]
		public DateTime? DeliverDate { get; set; }

		[ Column(Name = "division_property", StringLength = 50)]
		public string DivisionProperty { get; set; }

		[ Column(Name = "entrust_date")]
		public DateTime? EntrustDate { get; set; }

		[ Column(Name = "examime_date")]
		public DateTime? ExamimeDate { get; set; }

		[ Column(Name = "expire_date")]
		public DateTime? ExpireDate { get; set; }

		[ Column(Name = "fee_reduce")]
		public int? FeeReduce { get; set; }

		[ Column(Name = "filing_type", StringLength = 50)]
		public string FilingType { get; set; }

		[ Column(Name = "first_agency_user", StringLength = 50)]
		public string FirstAgencyUser { get; set; }

		[ Column(Name = "first_pay_annual", StringLength = 50)]
		public string FirstPayAnnual { get; set; }

		[ Column(Name = "first_priority_date")]
		public DateTime? FirstPriorityDate { get; set; }

		[ Column(Name = "foregin_agency_id", StringLength = 50)]
		public string ForeginAgencyId { get; set; }

		[ Column(Name = "foregin_case_no", StringLength = 50)]
		public string ForeginCaseNo { get; set; }

		[ Column(Name = "invalid_code", StringLength = 50)]
		public string InvalidCode { get; set; }

		[ Column(Name = "invalid_holder_name", StringLength = 4000)]
		public string InvalidHolderName { get; set; }

		[ Column(Name = "invalid_request_user", StringLength = 1000)]
		public string InvalidRequestUser { get; set; }

		[ Column(Name = "is_active")]
		public bool? IsActive { get; set; }

		[ Column(Name = "is_ahead_pub")]
		public bool? IsAheadPub { get; set; }

		[ Column(Name = "is_ca")]
		public bool? IsCa { get; set; }

		[ Column(Name = "is_cip")]
		public bool? IsCip { get; set; }

		[ Column(Name = "is_color")]
		public bool IsColor { get; set; }

		[ Column(Name = "is_division")]
		public bool? IsDivision { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "is_essence_exam")]
		public bool? IsEssenceExam { get; set; }

		[ Column(Name = "is_fee_reduce")]
		public bool? IsFeeReduce { get; set; }

		[ Column(Name = "is_grace")]
		public bool IsGrace { get; set; }

		[ Column(Name = "is_invalid_program")]
		public bool? IsInvalidProgram { get; set; }

		[ Column(Name = "is_pph")]
		public bool? IsPph { get; set; }

		[ Column(Name = "is_preservation")]
		public bool? IsPreservation { get; set; }

		[ Column(Name = "is_priority_review")]
		public bool? IsPriorityReview { get; set; }

		[ Column(Name = "is_same_day")]
		public bool? IsSameDay { get; set; }

		[ Column(Name = "is_secrecy_request")]
		public bool? IsSecrecyRequest { get; set; }

		[ Column(Name = "issue_date")]
		public DateTime? IssueDate { get; set; }

		[ Column(Name = "issue_no", StringLength = 50)]
		public string IssueNo { get; set; }

		[ Column(Name = "manage_company", StringLength = 50)]
		public string ManageCompany { get; set; }

		[ Column(Name = "manage_district", StringLength = 50)]
		public string ManageDistrict { get; set; }

		[ Column(Name = "member_country", StringLength = 500)]
		public string MemberCountry { get; set; }

		[ Column(Name = "multi_type", StringLength = 50)]
		public string MultiType { get; set; }

		[ Column(Name = "official_status_id", StringLength = 50)]
		public string OfficialStatusId { get; set; }

		[ Column(Name = "pct_enter")]
		public bool PctEnter { get; set; }

		[ Column(Name = "pub_date")]
		public DateTime? PubDate { get; set; }

		[ Column(Name = "pub_no", StringLength = 50)]
		public string PubNo { get; set; }

		[ Column(Name = "register_no", StringLength = 50)]
		public string RegisterNo { get; set; }

		[ Column(Name = "rel_volume", StringLength = 50)]
		public string RelVolume { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "renewal_date")]
		public DateTime? RenewalDate { get; set; }

		[ Column(Name = "request_remark", StringLength = 4000)]
		public string RequestRemark { get; set; }

		[ Column(Name = "sales_user_id", StringLength = 50)]
		public string SalesUserId { get; set; }

		[ Column(Name = "second_agency_user", StringLength = 50)]
		public string SecondAgencyUser { get; set; }

		[ Column(Name = "show_mode", StringLength = 50)]
		public string ShowMode { get; set; }

		[ Column(Name = "tech_field_id", StringLength = 50)]
		public string TechFieldId { get; set; }

		[ Column(Name = "track_user", StringLength = 50)]
		public string TrackUser { get; set; }

		[ Column(Name = "trademark_class", StringLength = 500)]
		public string TrademarkClass { get; set; }

		[ Column(Name = "trademark_contact_id", StringLength = 50)]
		public string TrademarkContactId { get; set; }

		[ Column(Name = "undertake_dept_id", StringLength = 50)]
		public string UndertakeDeptId { get; set; }

		[ Column(Name = "undertake_main_user_id", StringLength = 200)]
		public string UndertakeMainUserId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "version_id", StringLength = 50)]
		public string VersionId { get; set; }

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

		[ Column(Name = "volume_old", StringLength = 50)]
		public string VolumeOld { get; set; }

	}

}
