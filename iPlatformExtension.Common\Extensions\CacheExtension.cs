﻿using iPlatformExtension.Common.Cache;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace iPlatformExtension.Common.Extensions;

public static class CacheExtension
{
    /// <summary>
    /// 添加缓存组件。
    /// 可缓存仓储。<see cref="ICacheableRepository{TKey,TValue}"/>
    /// 目前缓存通过内存做缓存。
    /// </summary>
    /// <remarks>该方法必须在服务集合添加<see cref="ICacheableRepository{TKey,TValue}"/>后调用</remarks>
    /// <param name="services">注册的服务集合</param>
    /// <returns></returns>
    public static CacheBuilder AddCache(this IServiceCollection services)
    {
        var builder = new CacheBuilder(services);
        services.AddMemoryCache();

        var cacheServiceDescriptors = services.SelectMany(descriptor =>
        {
            var cacheServiceTypes = descriptor.ServiceType.GetInterfaces()
                .Where(serviceType => typeof(ICacheableRepository<,>) == (serviceType.IsGenericType
                    ? serviceType.GetGenericTypeDefinition()
                    : serviceType));
            return cacheServiceTypes.Select(serviceType => new KeyValuePair<Type, ServiceDescriptor>(serviceType, descriptor));
        }).ToArray();

        services.AddSingleton<CacheExpirationTokenProvider>();
        services.AddScoped<CacheProvider>();
        // services.TryAddEnumerable(ServiceDescriptor.Singleton(typeof(CacheExpirationToken), typeof(CacheExpirationToken<>)));
        foreach (var (cacheableServiceType, serviceDescriptor) in cacheServiceDescriptors)
        {
            var cacheValueType = cacheableServiceType.GetGenericArguments()[1];
            
            var cacheExpirationTokenType =
                typeof(CacheExpirationToken<>).MakeGenericType(cacheValueType);
            services.TryAddSingleton(cacheExpirationTokenType);
            services.TryAddEnumerable(ServiceDescriptor.Singleton(typeof(CacheExpirationToken), cacheExpirationTokenType));

            var stringKeyCacheType = typeof(IStringKeyCacheableRepository<>).MakeGenericType(cacheValueType);
            if (serviceDescriptor.ServiceType.GetInterfaces().Any(type => type == stringKeyCacheType))
            {
                services.TryAdd(ServiceDescriptor.Describe(stringKeyCacheType,
                    provider => provider.GetRequiredService(serviceDescriptor.ServiceType), serviceDescriptor.Lifetime));
            }
            
            services.TryAdd(ServiceDescriptor.Describe(cacheableServiceType,
                provider => provider.GetRequiredService(serviceDescriptor.ServiceType), serviceDescriptor.Lifetime));
        }

        return builder;
    }

    public static CacheBuilder ConfigureMemory(this CacheBuilder cacheBuilder, Action<MemoryCacheOptions> setupAction)
    {
        ArgumentNullException.ThrowIfNull(setupAction);
        cacheBuilder.Services.Configure(setupAction);
        return cacheBuilder;
    }
    
    /// <summary>
    /// 配置redis缓存
    /// </summary>
    /// <param name="cacheBuilder">缓存构建者</param>
    /// <param name="setupAction">redis缓存的选项配置</param>
    /// <typeparam name="TOptions">缓存选项的类型参数</typeparam>
    /// <typeparam name="TCache">redis缓存的类型参数</typeparam>
    /// <returns>缓存构建者</returns>
    public static CacheBuilder ConfigureRedis<TOptions, TCache>(this CacheBuilder cacheBuilder, Action<TOptions>? setupAction = default) 
        where TOptions : RedisCacheOptionsBase 
        where TCache : class, IRedisCache<TOptions>
    {
        cacheBuilder.Services.AddOptions<TOptions>();
        if (setupAction is not null)
        {
            cacheBuilder.Services.Configure(setupAction);
        }

        cacheBuilder.Services.AddSingleton<TCache>();
        cacheBuilder.Services.AddSingleton<IRedisCache<TOptions>>(
            provider => provider.GetRequiredService<TCache>());
        cacheBuilder.Services.AddSingleton<IRedisCache<RedisCacheOptionsBase>>(provider =>
            provider.GetRequiredService<TCache>());

        return cacheBuilder;
    }

    /// <summary>
    /// 尝试获取<see cref="IStringKeyCacheableRepository{TValue}"/>的缓存仓储
    /// </summary>
    /// <param name="provider">缓存提供者</param>
    /// <param name="repository">缓存仓储</param>
    /// <typeparam name="TValue">缓存仓储的值类型参数</typeparam>
    /// <returns>获取的结果</returns>
    public static bool TryGetStringKeyCacheableRepository<TValue>(
        this CacheProvider provider, out IStringKeyCacheableRepository<TValue>? repository)
    {
        var cacheRepository = provider.GetCacheableRepository<string, TValue>();
        repository = cacheRepository as IStringKeyCacheableRepository<TValue>;
        return repository != null;
    }

    public static async Task<bool> TryLockAsync(this IRedisCache<RedisCacheOptionsBase> redisCache, string lockKey, string entityKey,
        TimeSpan lockTime)
    {
        var expirationTimestamp = DateTimeOffset.Now.Add(lockTime).ToUnixTimeMilliseconds();
        const string script = """
                              local absoluteExpiredTicks = redis.call('HGET', KEYS[1], ARGV[1])
                              if (not(absoluteExpiredTicks)) then 
                                  redis.call('HSET', KEYS[1], ARGV[1], ARGV[3])
                                  return 1
                              else
                                  absoluteExpiredTicks = tonumber(absoluteExpiredTicks)
                                  if (absoluteExpiredTicks < tonumber(ARGV[2])) then
                                      redis.call('HSET', KEYS[1], ARGV[1], ARGV[3])
                                      return 1
                                  else
                                      return 0
                                  end
                              end
                              """;

        var result = await redisCache.ExecuteScriptAsync<int>(script, keys: [lockKey],
            values: [entityKey, DateTimeOffset.Now.ToUnixTimeMilliseconds(), expirationTimestamp]);

        return result == 1;
    }
}