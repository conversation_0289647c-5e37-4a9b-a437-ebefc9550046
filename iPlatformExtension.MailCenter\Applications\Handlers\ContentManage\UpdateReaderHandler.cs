﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class UpdateReaderHandler(IFreeSql<MailCenterFreeSql> mySql, IHttpContextAccessor httpContextAccessor, IMailReaderListRepository mailReaderListRepository) : IRequestHandler<UpdateReaderCommand>
    {
        public async Task Handle(UpdateReaderCommand request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var readerLists = await mailReaderListRepository.Where(o => request.MailIds.Contains(o.MailId) && o.UserId == userId && o.Status == (int)ReaderStatusEnum.ToRead).ToListAsync(cancellationToken);
            if (readerLists.Any())
            {
                readerLists.ForEach(o =>
                {
                    o.UpdateTime=DateTime.Now;
                    o.UpdateBy = userId;
                    o.Status = (int) ReaderStatusEnum.AlreadyRead;
                });
                await mailReaderListRepository.UpdateAsync(readerLists, cancellationToken);
            }
            else
            {
                throw new ApplicationException("设置失败,找不到邮件阅读记录.");
            }

        }
    }
}
