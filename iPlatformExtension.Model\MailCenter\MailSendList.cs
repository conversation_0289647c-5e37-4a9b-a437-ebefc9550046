﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_send_list", DisableSyncStructure = true)]
	public partial class MailSendList {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		[Column(Name = "mail_id", StringLength = 50)]
		public string MailId { get; set; }

		/// <summary>
		/// 发送时间，立即发送：当前时间，定时发送：定时时间
		/// </summary>
		[Column(Name = "send_time", DbType = "datetime")]
		public DateTime? SendTime { get; set; }

		/// <summary>
		/// 发送状态0未发送:1已发送
		/// </summary>
		[Column(Name = "status", DbType = "int")]
		public int? Status { get; set; }

		[Column(Name = "update_by", StringLength = 50)]
		public string UpdateBy { get; set; }

		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

	}

}
