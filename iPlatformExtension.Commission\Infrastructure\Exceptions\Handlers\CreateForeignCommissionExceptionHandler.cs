﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class CreateForeignCommissionExceptionHandler(ILogger<CreateForeignCommissionCommand> logger) : IRequestExceptionHandler<CreateForeignCommissionCommand, Unit, Exception>
{
    public Task Handle(CreateForeignCommissionCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        logger.LogInsertForeignTrademarkWeightDataError(request.ProcId, exception);
        if (request.WeightDto is null)
        {
            state.SetHandled(Unit.Value);
        }
        return Task.CompletedTask;
    }
}