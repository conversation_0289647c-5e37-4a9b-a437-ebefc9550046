using OBS;

namespace iPlatformExtension.Common.Clients.HuaweiObs;

internal sealed class DefaultHuaweiObsReactor<TResponse> 
{
    private readonly Func<ObsClient, IAsyncResult, TResponse> _getResponse;

    public DefaultHuaweiObsReactor(Func<ObsClient, IAsyncResult,TResponse> getResponse)
    {
        _getResponse = getResponse;
    }

    public void ReactResponse(IAsyncResult asyncResult)
    {
        if (asyncResult.AsyncState is not TaskCompletionSource<TResponse>
            {
                Task.AsyncState: ObsClient client
            } source) return;
        try
        {
            var response = _getResponse(client, asyncResult);
            source.SetResult(response);
        }
        catch (Exception e)
        {
            source.SetException(e);
        }
    }
    
    
}