﻿using System.Diagnostics;
using System.Security.Claims;
using System.Text;
using FreeSql;
using iPlatformExtension.Common.Mediator.Notifications;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Common.Db.Log;

internal sealed class EntityChangeLoggingHandler(
    PlatformMongoDbContext dbContext,
    EntityTypeInfoProvider entityTypeInfoProvider,
    ObjectPool<StringBuilder> stringBuilderPool,
    ILogger<EntityChangeLoggingHandler> logger,
    IHttpContextAccessor contextAccessor) : INotificationHandler<EntityChangeNotification>
{
    private Task LogEntityChangeInfoAsync(List<DbContext.EntityChangeReport.ChangeInfo> changeInfos)
    {
        if (changeInfos.Count == 0)
        {
            return Task.FromResult(0);
        }

        var changeReport = new EntityChangeLogEntry()
        {
            ExecuteTime = DateTime.Now,
            Reports = [..changeInfos],
            TraceId = Activity.Current?.TraceId.ToString() ?? string.Empty
        };

        var httpContext = contextAccessor.HttpContext;
        if (httpContext is not null)
        {
            changeReport.Operator = httpContext.User.FindFirstValue(ClaimTypes.NameIdentifier) ?? string.Empty;
            if (string.IsNullOrEmpty(changeReport.TraceId))
            {
                changeReport.TraceId = httpContext.TraceIdentifier;
            }
        }
        
        Debug.Assert(changeReport.TraceId is not null, "traceId为空");
        
        return Task.Run(async () =>
        {
            var stringBuilder = stringBuilderPool.Get();

            try
            {
                var entityChangeLogs = changeReport.Reports.Select(report => GenerateEntityChangeLog(report,
                    entityTypeInfoProvider.Get(report.EntityType), changeReport.ExecuteTime, changeReport.Operator,
                    changeReport.TraceId, stringBuilder)).ToArray();
                await dbContext.EntityChangeLogs.InsertManyAsync(entityChangeLogs);
                logger.LogDebug("记录数据变更历史");
            }
            catch (Exception e)
            {
                logger.LogError(e, "请求[{TraceId}]：记录数据变更历史错误", changeReport.TraceId);
            }
            finally
            {
                stringBuilderPool.Return(stringBuilder);
            }
            
        });
    }

    private static EntityChangeLog GenerateEntityChangeLog(DbContext.EntityChangeReport.ChangeInfo changeInfo,
        EntityTypeInfo entityTypeInfo,  DateTime logTime, string @operator, string traceId, StringBuilder stringBuilder)
    {
        stringBuilder.Clear();
        var entityChangeLog = new EntityChangeLog()
        {
            TableName = entityTypeInfo.TableName ?? changeInfo.EntityType.Name,
            LogTime = logTime,
            ChangeType = (int) changeInfo.Type,
            Operator = @operator,
            TraceId = traceId
        };

        IEnumerable<EntityPropertyInfo> propertyInfos = entityTypeInfo.EntityPropertyInfos;
        
        Debug.Assert(propertyInfos is not null, $"{propertyInfos}转换后为空");
        
        var keyProperty = propertyInfos.FirstOrDefault(propertyInfo => propertyInfo.IsKey);
        switch (changeInfo.Type)
        {
            case DbContext.EntityChangeType.Insert:
            {
                entityChangeLog.EntityId = keyProperty?.Get != null
                    ? keyProperty.Get(changeInfo.Object)?.ToString() ?? string.Empty
                    : string.Empty;
                stringBuilder.AppendLine("新增数据");
                foreach (var entityPropertyInfo in propertyInfos)
                {
                    if (entityPropertyInfo is {ColumnIgnore: false, Get: not null})
                    {
                        stringBuilder.Append(
                            $"@{entityPropertyInfo.ColumnName}=[{entityPropertyInfo.Get(changeInfo.Object)}]\n");
                    }
                }

                entityChangeLog.Message = stringBuilder.ToString();
                break;
            }
            case DbContext.EntityChangeType.Update:
            {
                entityChangeLog.EntityId = keyProperty?.Get != null
                    ? keyProperty.Get(changeInfo.Object)?.ToString() ?? string.Empty
                    : string.Empty;
                stringBuilder.AppendLine("更新数据");
                foreach (var entityPropertyInfo in propertyInfos)
                {
                    
                    if (entityPropertyInfo is {ColumnIgnore: false, Get: not null})
                    {
                        var formerValue = entityPropertyInfo.Get(changeInfo.BeforeObject)?.ToString();
                        var currentValue = entityPropertyInfo.Get(changeInfo.Object)?.ToString();

                        if (formerValue != currentValue)
                        {
                            stringBuilder.Append(
                                $"@{entityPropertyInfo.ColumnName}：[{formerValue}] => [{currentValue}]\n");
                        }
                    }
                }

                entityChangeLog.Message = stringBuilder.ToString();
                break;
            }
            case DbContext.EntityChangeType.Delete:
            {
                entityChangeLog.EntityId = keyProperty?.Get != null
                    ? keyProperty.Get(changeInfo.Object)?.ToString() ?? string.Empty
                    : string.Empty;
                stringBuilder.AppendLine("删除数据");
                foreach (var entityPropertyInfo in propertyInfos)
                {
                    if (entityPropertyInfo is {ColumnIgnore: false, Get: not null})
                    {
                        stringBuilder.Append(
                            $"@{entityPropertyInfo.ColumnName}=[{entityPropertyInfo.Get(changeInfo.Object)}]\n");
                    }
                }

                entityChangeLog.Message = stringBuilder.ToString();
                break;
            }
            case DbContext.EntityChangeType.SqlRaw:
            {
                entityChangeLog.EntityId = string.Empty;
                stringBuilder.AppendLine("原生SQL");
                stringBuilder.AppendLine(changeInfo.Object.ToString());

                entityChangeLog.Message = stringBuilder.ToString();
                break;
            }
            default:
                throw new ArgumentOutOfRangeException(nameof(changeInfo), $"{changeInfo.Type}不在枚举范围");
        }
        
        return entityChangeLog;
    }

    public Task Handle(EntityChangeNotification notification, CancellationToken cancellationToken)
    {
        return LogEntityChangeInfoAsync(notification.Reports);
    }
}