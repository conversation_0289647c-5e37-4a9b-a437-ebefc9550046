﻿using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using System.Reflection;
using FreeSql.DataAnnotations;
using iPlatformExtension.Common.Office.Excel.Models;
using iPlatformExtension.Model.Attributes;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Common.TypeInfo;

internal readonly struct EntityTypeInfoBuilder
{
    private readonly System.Reflection.TypeInfo _typeInfo;

    private readonly EntityTypeInfo _entityTypeInfo;

    internal EntityTypeInfoBuilder(Type entityType)
    {
        _typeInfo = entityType.GetTypeInfo();
        _entityTypeInfo = new EntityTypeInfo()
        {
            TypeInfo = _typeInfo
        };
    }
    
    

    internal EntityPropertyInfo BuildPropertyInfo(PropertyInfo propertyInfo)
    {
        return new EntityPropertyInfoBuilder(propertyInfo).BuildDbColumnInfo().BuildDisplayInfo().BuildGetter(_typeInfo)
            .BuildSetter(_typeInfo).BuildModelBindInfo().BuildExcelInfo().Build();
    }

    internal EntityTypeInfoBuilder BuildProperties()
    {
        var properties = _typeInfo.GetProperties(BindingFlags.Instance | BindingFlags.Public);
        var entityPropertyInfos = new EntityPropertyInfoCollection(properties.Length);
        for (var i = 0; i < properties.Length; i++)
        {
            var propertyInfo = properties[i];
            entityPropertyInfos.TryAdd(propertyInfo.Name, BuildPropertyInfo(propertyInfo));
        }

        _entityTypeInfo.EntityPropertyInfos = entityPropertyInfos;
        return this;
    }

    internal EntityTypeInfoBuilder BuildEntityTypeInfo()
    {
        var tableInfo = _typeInfo.GetCustomAttribute<TableAttribute>();
        if (tableInfo is not null)
        {
            _entityTypeInfo.TableName = tableInfo.Name;
        }

        if (_typeInfo.GetConstructor(BindingFlags.Instance|BindingFlags.Public|BindingFlags.NonPublic, Array.Empty<Type>()) is not null)
        {
            _entityTypeInfo.HasNoArgumentsConstructor = true;
        }
        
        return this;
    }

    internal EntityTypeInfo Build() => _entityTypeInfo;
}