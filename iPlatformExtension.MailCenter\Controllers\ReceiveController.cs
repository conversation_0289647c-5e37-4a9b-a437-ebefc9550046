﻿using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.MailCenter.Applications.Commands.Receive;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.MailCenter.Controllers
{
    /// <summary>
    /// 接收邮件控制器
    /// </summary>
    /// <param name="mediator"></param>
    [Route("[controller]")]
    [ApiController]
    public class ReceiveController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// 获取收件详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetReceiveDetail")]
        public async Task<GetReceiveDetailDto> GetReceiveDetail(
            [FromQuery] GetReceiveDetailQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取收件附件列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetMailAttachment")]
        public async Task<IEnumerable<GetMailAttachmentDto>> GetMailAttachment(
            [FromQuery] GetMailAttachmentQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 收件查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetReceiveList")]
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        public async Task<IEnumerable<GetReceiveListDto>> GetReceiveList(
            [FromBody] GetReceiveListQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 收件办理
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetReceiveExtension")]
        public async Task<GetReceiveExtensionDto> GetReceiveExtension(
            [FromQuery] GetReceiveExtensionQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 流程历史
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetFlowHistory")]
        public async Task<IEnumerable<GetFlowHistoryDto>> GetFlowHistoryQuery(
            [FromQuery] GetFlowHistoryQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取已关联规则列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRelatedRuleList")]
        public async Task<IEnumerable<GetRelatedRuleListDto>> GetRelatedRuleList(
            [FromQuery] GetRelatedRuleListQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取已关联邮件列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRelateMail")]
        public async Task<IEnumerable<GetRelateMailDto>> GetRelateMail(
            [FromQuery] GetRelateMailQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取可关联邮件列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRelateMailList")]
        public async Task<IEnumerable<GetRelateMailListDto>> GetRelateMailList(
            [FromQuery] GetRelateMailListQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取退回提交移交用户
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetActionUserList")]
        public async Task<GetActionUserListDto> GetActionUserList(
            [FromQuery] GetActionUserListQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取已关联任务列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRelateProc")]
        public async Task<IEnumerable<GetRelateProcDto>> GetRelate(
            [FromQuery] GetRelateProcQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 查询任务列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRelateProcList")]
        public async Task<IEnumerable<GetRelateProcListDto>> GetRelateList(
            [FromQuery] GetRelateProcListQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取已关联案件列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRelateCase")]
        public async Task<IEnumerable<GetRelateCaseDto>> GetRelateCase(
            [FromQuery] GetRelateCaseQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 查询案件列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRelateCaseList")]
        public async Task<IEnumerable<GetRelateCaseListDto>> GetRelateList(
            [FromQuery] GetRelateCaseListQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取已关联客户列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRelateCustomer")]
        public async Task<IEnumerable<GetRelateCustomerDto>> GetRelateCustomer(
            [FromQuery] GetRelateCustomerQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 设置关联
        /// </summary>
        /// <returns></returns>
        [HttpPost("SetRelate")]
        public async Task SetRelate([FromBody] SetRelateCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 删除关联
        /// </summary>
        /// /// <returns></returns>
        [HttpPost("DeleteRelate")]
        public async Task DeleteRelate([FromBody] DeleteRelateCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 获取邮箱列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        [HttpGet("GetMyHostList")]
        public async Task<IEnumerable<GetMailHostListDto>> GetMyHostList(
            [FromQuery] GetMyHostListQuery query
        )
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 下载所有附件
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("download-attachments")]
        public async Task<IActionResult> DownloadAttachments(
            [FromQuery] DownAllAttachmentQuery query
        )
        {
            var zipBytes = await mediator.Send(query);
            return File(zipBytes.Bytes, "application/zip", zipBytes.FileName);
        }

        /// <summary>
        /// 设置发件名称
        /// </summary>
        /// <returns></returns>
        [HttpPost("SetSendName")]
        public async Task SetSendName([FromBody] SetSendNameCommand command)
        {
            await mediator.Send(command);
        }

    }
}
