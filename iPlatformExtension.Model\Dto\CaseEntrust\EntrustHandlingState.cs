﻿using System.ComponentModel;

namespace iPlatformExtension.Model.Dto.CaseEntrust;

/// <summary>
/// 委案处理状态
/// </summary>
public enum EntrustHandlingState
{
    /// <summary>
    /// 成功
    /// </summary>
    [Description("已完成")]
    Completed = 1,
    
    /// <summary>
    /// 处理中
    /// </summary>
    [Description("处理中")]
    Handling = 0,
    
    /// <summary>
    /// 失败
    /// </summary>
    [Description("可推送")]
    Pushable = -1
}