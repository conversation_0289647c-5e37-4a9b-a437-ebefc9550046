﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 日期范围
/// </summary>
public struct DateRange(DateOnly? start, DateOnly? end)
{
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateOnly? StartDate { get; set; } = start;

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateOnly? EndDate { get; set; } = end;

    /// <summary>
    /// 解构函数
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    public void Deconstruct(out DateOnly? startDate, out DateOnly? endDate)
    {
        startDate = StartDate;
        endDate = EndDate;
    }
}