using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.MailCenter.Applications.Commands.Send
{
    /// <summary>
    /// 写邮件命令
    /// </summary>
    /// <param name="MailId">邮件ID，为空表示新增</param>
    /// <param name="MailFrom">发件人</param>
    /// <param name="MailTo">收件人，多个邮箱用分号分隔</param>
    /// <param name="MailCc">抄送，多个邮箱用分号分隔</param>
    /// <param name="MailBcc">密送，多个邮箱用分号分隔</param>
    /// <param name="MailSubject">邮件主题</param>
    /// <param name="MailHtmlBody">邮件正文</param>
    /// <param name="MailRelayBody">转发邮件正文</param>
    /// <param name="HostId">邮箱配置ID</param>
    /// <param name="IsImportant">是否紧急</param>
    /// <param name="IsRead">是否要求已读回执</param>
    /// <param name="IsRequiredProcessTime">是否定时发送</param>
    /// <param name="SendTime">发送时间，为空表示立即发送</param>
    /// <param name="Attachments">附件列表</param>
    /// <param name="SignatureBody">签名正文</param>
    public record WriteMailCommand(
        string? MailId,
        [Required(ErrorMessage = "发件人不能为空")] List<MailAddressList> MailFrom,
        [Required(ErrorMessage = "收件人不能为空")]  List<MailAddressList> MailTo,
        List<MailAddressList>? MailCc,
        List<MailAddressList>? MailBcc,
        [Required(ErrorMessage = "邮件主题不能为空")] string MailSubject,
        [Required(ErrorMessage = "邮件正文不能为空")] string MailHtmlBody,
        [Required(ErrorMessage = "邮箱配置ID不能为空")] string HostId,
        string? MailRelayBody = null,
        bool? IsImportant = false,
        bool? IsRead = false,
        bool? IsRequiredProcessTime = false,
        DateTime? SendTime = null,
        List<AttachmentInfo>? Attachments = null,
        string? SignatureBody = null) : IRequest<string>, IUnitOfWorkCommandMysql;

    /// <summary>
    /// 附件信息
    /// </summary>
    public class AttachmentInfo
    {
        /// <summary>
        /// 附件ID
        /// </summary>
        public string AttachmentId { get; set; }
    }

    /// <summary>
    /// 邮件地址
    /// </summary>
    public class MailAddressList
    {
        /// <summary>
        /// 邮件地址
        /// </summary>
        public string MailAddress { get; set; }

        /// <summary>
        /// 显示名称
        /// </summary>
        public string? DisplayName { get; set; }
    }
}
