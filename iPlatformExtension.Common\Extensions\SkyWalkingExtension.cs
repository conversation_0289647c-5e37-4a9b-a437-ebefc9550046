﻿using System.Diagnostics;
using iPlatformExtension.Common.Db.FreeSQL;
using Microsoft.Extensions.DependencyInjection;
using SkyApm;
using SkyApm.Diagnostics.FreeSql;

namespace iPlatformExtension.Common.Extensions;

public static class SkyWalkingExtension
{
    public static readonly DiagnosticListener freeSqlDiagnosticListener = new ("FreeSqlDiagnosticListener");
    
    public static PlatformFreeSqlBuilder<T> AddSkyWalkingDiagnostic<T>(this PlatformFreeSqlBuilder<T> builder)
    {
        builder.Services.AddSingleton<ITracingDiagnosticProcessor, FreeSqlTracingDiagnosticProcessor>();
        return builder.ConfigureGlobal(freeSql =>
        {
            freeSql.Aop.CurdBefore += (s, e) =>
            {
                freeSqlDiagnosticListener.Write(FreeSqlTracingDiagnosticProcessor.FreeSql_CurdBefore, (object?) e);
            };
            freeSql.Aop.CurdAfter += (s, e) =>
            {
                freeSqlDiagnosticListener.Write(FreeSqlTracingDiagnosticProcessor.FreeSql_CurdAfter, (object?) e);
            };

            freeSql.Aop.SyncStructureBefore += (s, e) =>
            {

                freeSqlDiagnosticListener.Write(FreeSqlTracingDiagnosticProcessor.FreeSql_SyncStructureBefore, (object?) e);
            };
            freeSql.Aop.SyncStructureAfter += (s, e) =>
            {
                freeSqlDiagnosticListener.Write(FreeSqlTracingDiagnosticProcessor.FreeSql_SyncStructureAfter, (object?) e);
            };

            freeSql.Aop.CommandBefore += (s, e) =>
            {

                freeSqlDiagnosticListener.Write(FreeSqlTracingDiagnosticProcessor.FreeSql_CommandBefore, (object?) e);
            };
            freeSql.Aop.CommandAfter += (s, e) =>
            {

                freeSqlDiagnosticListener.Write(FreeSqlTracingDiagnosticProcessor.FreeSql_CommandAfter, (object?) e);
            };

            freeSql.Aop.TraceBefore += (s, e) =>
            {

                freeSqlDiagnosticListener.Write(FreeSqlTracingDiagnosticProcessor.FreeSql_TraceBefore, (object?) e);
            };
            freeSql.Aop.TraceAfter += (s, e) =>
            {

                freeSqlDiagnosticListener.Write(FreeSqlTracingDiagnosticProcessor.FreeSql_TraceAfter, (object?) e);
            };
        });
    }
}