﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.User;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.User;

internal sealed class UserKeywordQueryHandler(IFreeSql<PlatformFreeSql> freeSql) : IRequestHandler<UserKeywordQuery, IEnumerable<UserInfoDto>>
{
    private const int LimitSize = 30;
    
    public async Task<IEnumerable<UserInfoDto>> Handle(UserKeywordQuery request, CancellationToken cancellationToken)
    {
        var keyword = request.Keyword;
        
        return await freeSql.Select<SysUserInfo>().WithLock()
            .WhereIf(!string.IsNullOrWhiteSpace(keyword), info => info.CnName.StartsWith(keyword) || info.UserName.StartsWith(keyword))
            .Take(LimitSize).ToListAsync(info => new UserInfoDto
            {
                UserId = info.UserId,
                CnName = info.CnName,
                UserName = info.UserName,
                DeptId = info.DeptId,
                IsEnabled = info.IsEnabled,
                EmailAddress = info.Email
            }, cancellationToken);
    }
}