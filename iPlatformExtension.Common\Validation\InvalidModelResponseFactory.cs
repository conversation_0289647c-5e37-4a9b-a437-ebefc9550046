﻿using System.Text;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Common.Validation;

/// <summary>
/// 参数验证失败的处理程序
/// </summary>
public sealed class InvalidModelResponseFactory
{
    private readonly ObjectPool<StringBuilder> _stringBuilderPool;

    private readonly ILogger _logger;

    /// <summary>
    /// 构造函数注入
    /// </summary>
    /// <param name="stringBuilderPool">字符构建者池</param>
    /// <param name="loggerFactory">日志组件工厂</param>
    public InvalidModelResponseFactory(ObjectPool<StringBuilder> stringBuilderPool, ILoggerFactory loggerFactory)
    {
        _stringBuilderPool = stringBuilderPool;
        _logger = loggerFactory.CreateLogger(GetType());
    }

    /// <summary>
    /// 统一格式化参数验证失败的错误结果
    /// </summary>
    /// <remarks>apiResult对象池不作对象的回收。
    /// 到resultFilter的<c>OnResultExecutionAsync</c>一起回收</remarks>
    /// <param name="actionContext">当前请求上下文</param>
    /// <returns>请求响应结果</returns>
    public IActionResult CreateInvalidModelResponse<T>(ActionContext actionContext) where T : class, new()
    {
        var modelState = actionContext.ModelState;
        ArgumentNullException.ThrowIfNull(modelState);
        var stringBuilder = _stringBuilderPool.Get();
        stringBuilder.Append("参数验证错误：");

        var errorMessage = modelState.Where(pair => pair.Value?.ValidationState == ModelValidationState.Invalid)
            .Aggregate(stringBuilder, (current, pair) =>
            {
                var (key, value) = pair;
                current.Append('@').Append(key).Append(": ");
                return value?.Errors.Aggregate(current,
                    (errorBuilder, error) => errorBuilder.Append(error.ErrorMessage).Append(' ')) ?? current;
            }).ToString();
        
        var ex = new ArgumentException(errorMessage);
        _logger.LogInvalidModel(ex, actionContext.ActionDescriptor.DisplayName);

        _stringBuilderPool.Return(stringBuilder);


        var apiResult = new T();
        if (apiResult is ApiResult<object> result)
        {
            result.Fail(ex);
        }

        actionContext.HttpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
        return new ObjectResult(apiResult);
    }
}