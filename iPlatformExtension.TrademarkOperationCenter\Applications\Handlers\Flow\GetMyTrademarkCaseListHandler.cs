﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    /// <summary>
    /// 获取标签列表处理者
    /// </summary>
    public class GetMyTrademarkCaseListHandler : IRequestHandler<GetMyTrademarkCaseListQuery, IEnumerable<GetMyTrademarkCaseListDto>>
    {
        private readonly IFreeSql _freeSql;
        private readonly IHttpContextAccessor _httpContextAccessor;

        /// <summary>
        /// 获取标签列表构造函数
        /// </summary>
        /// <param name="freeSql"></param>
        /// <param name="httpContextAccessor"></param>
        public GetMyTrademarkCaseListHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor)
        {
            _freeSql = freeSql;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<IEnumerable<GetMyTrademarkCaseListDto>> Handle(GetMyTrademarkCaseListQuery request, CancellationToken cancellationToken)
        {
            var userid = _httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userid);

            var list = await _freeSql.Select<SysFlowPrivate>()
                .Where(o => o.FlowType == request.flowType && o.UserId == userid)
                .WhereIf(request.flowSubType is not null, it => it.FlowSubType == request.flowSubType).WithLock()
                .ToListAsync(flow => new GetMyTrademarkCaseListDto(flow.PrivateId, flow.UserId, flow.FlowType, flow.FlowSubType, flow.PrivateName, flow.IsShow, flow.Seq), cancellationToken);
            return list.OrderBy(o => o.flowType).ThenBy(o => o.seq);
        }
    }
}
