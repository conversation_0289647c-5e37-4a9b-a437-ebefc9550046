﻿using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Commission.Application.Queries.Trademark.Foreign;

internal sealed record ForeignWeightDetailQuery(
    DateRange DateRange,
    IEnumerable<string> DeptIds,
    string? DistrictCode,
    string? Keyword,
    int PageIndex,
    int PageSize) : IRequest<IEnumerable<ProcWeightDetailExport>>, IScopeLoggerCommand, IPageQuery
{
    public int PageIndex { get; set; } = PageIndex;

    public int PageSize { get; set; } = PageSize;
    
    public long? Total { get; set; }
}