﻿
using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Encryption;
using iPlatformExtension.MailService.Applications.Commands;
using iPlatformExtension.MailService.Extensions;
using iPlatformExtension.MailService.Infrastructure;
using iPlatformExtension.Model.MailCenter;
using MailKit;
using MediatR;
using Microsoft.Extensions.Hosting;
using System.Reflection.Metadata.Ecma335;

namespace iPlatformExtension.MailService.HostedService
{
    public class MailReceiveHostedService(ILogger<MailReceiveHostedService> logger, IFreeSql<MailCenterFreeSql> freeSql, IConfiguration configuration, IMediator mediator, MailTool mailTool) : BackgroundService
    {
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            logger.LogInformation("邮件接收器启动");
            try
            {
                var receiveMaillst = freeSql.Select<MailHost>().Where(o => !o.IsPrivate && o.IsEnabled ).ToList();
                while (receiveMaillst.Count > 0)
                {
                    foreach (var mail in receiveMaillst)
                    {
                        if (mailTool.TryGetRecord().ContainsKey(mail.Account.ToString()))
                        {
                            continue;
                        }
                        try
                        {
                            _ = Task.Factory.StartNew(async () =>
                                      {
                                          await mediator.Send(new ReceiveMailCommand(new Applications.Models.MailConfigDto
                                          {
                                              HostId = mail.HostId,
                                              ImapHost = mail.ImapHost,
                                              ImapPort = mail.ImapPort,
                                              SenderAccount = mail.Account,
                                              SenderPassword = DESHelper.DESDecrypt(mail.Password)
                                          }));
                                      });
                            Thread.Sleep(200);
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, $"{mail.Account}邮箱异常");
                            continue;
                        }
                    }
                    //Thread.Sleep(1000 * 60 * 2);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "邮件接收异常");
                throw;
            }
        }
    }
}
