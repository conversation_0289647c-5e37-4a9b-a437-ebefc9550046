﻿using FreeSql;
using FreeSql.Internal.CommonProvider;
using iPlatformExtension.Finance.Applications.Models.Proc;
using iPlatformExtension.Finance.Applications.Queries.Proc;
using iPlatformExtension.Finance.Infrastructure.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Proc;

internal sealed class UnpaidProcPagingQueryHandler(IMediator mediator, IFreeSql freeSql) : IRequestHandler<UnpaidProcPagingQuery, IEnumerable<UnpaidOfficialProcDto>>
{
    public async Task<IEnumerable<UnpaidOfficialProcDto>> Handle(UnpaidProcPagingQuery request, CancellationToken cancellationToken)
    {
        var feesQuery = request.FeesQuery;
        var page = request.PageIndex;
        var pageSize = request.PageSize;
        
        var feePagingQuery = freeSql.Select<CaseFeeList>();
        Select0Provider.CopyData(feesQuery as Select0Provider, feePagingQuery as Select0Provider, null);

        var unpaidOfficialProcList = await feesQuery
            .OrderByDescending(feeList => feeList.CaseProcInfo.SendOfficialDate)
            .OrderBy(feeList => feeList.CaseProcInfo.ProcId)
            .GroupBy(feeList => new CaseProcInfo()
            {
                ProcId = feeList.CaseProcInfo.ProcId,
                SendOfficialDate = feeList.CaseProcInfo.SendOfficialDate
            })
            .Page(page, pageSize)
            .ToListAsync(aggregate => new UnpaidOfficialProcDto()
            {
                ProcId = aggregate.Key.ProcId,
                TotalAmount = aggregate.Sum(aggregate.Value.Amount),
                OfficialPublicationDates = string.Join(';', 
                    freeSql.Select<CaseFeeList>()
                        .Where(list => list.ProcId == aggregate.Key.ProcId)
                        .ToList(list => list.OfficialPaymentPublicationDate!.Value.ToString("yyyy-MM-dd")))
            }, cancellationToken);
        
        var countTask = request.Total is null
            ? feePagingQuery.GroupBy(list => list.ProcId).CountAsync(cancellationToken)
            : Task.FromResult(request.Total.Value);

        return new PageResult<UnpaidOfficialProcDto>()
        {
            Data = await mediator.Send(new UnpaidOfficialProcResultQuery(unpaidOfficialProcList), cancellationToken),
            Page = page,
            PageSize = pageSize,
            Total = await countTask
        };
    }
}