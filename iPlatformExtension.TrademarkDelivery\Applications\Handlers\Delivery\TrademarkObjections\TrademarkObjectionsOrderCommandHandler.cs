﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery.TrademarkObjections;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Exceptions;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Extensions;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery.TrademarkObjections;

internal sealed class TrademarkObjectionsOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IBaseCountryRepository countryRepository,
    IApplicantTypeRepository applicantTypeRepository,
    IDeliveryInfoRepository deliveryInfoRepository,
    ISystemDictionaryRepository dictionaryRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    UnitOfWorkManager unitOfWorkManager,
    ILoggerFactory loggerFactory,
    PhoenixClientFactory phoenixClientFactory)
    : PhoenixDeliveryHandleBase<TrademarkObjectionsOrderCommand, TrademarkObjectionsOrder,
        PhoenixResponseParameters<OrderData>>(stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository,
        unitOfWorkManager, loggerFactory, phoenixClientFactory), 
        IDeliveryHandler<TrademarkObjectionsOrderCommand, TrademarkObjectionsOrder, PhoenixResponseParameters<OrderData>>
{
    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.StartupDelivery;
    
    public override async Task<TrademarkObjectionsOrder> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        ArgumentNullException.ThrowIfNull(_deliveryInfo.OtherInfo);

        var otherInfo = _deliveryInfo.OtherInfo;
        
        var grandNumbers = _deliveryInfo.OtherInfo.TrademarkNiceClasses?.Split(';') ?? Array.Empty<string>();

        var lawBasisList = await (_deliveryInfo.LawBasis ?? Array.Empty<DeliveryLawBasis>()).ToAsyncEnumerable()
            .SelectAwait(async basis => new LawBasis
            {
                Reason = basis.Reason,
                LawId = Convert.ToInt32(basis.LawId),
                LawName = (await dictionaryRepository.GetChineseKeyValueAsync(
                    SystemDictionaryName.ObjectionsLawProvision, basis.LawId)).Value,
                FileName = basis.FileName,
                FileUrl = basis.Url
            }).ToArrayAsync(cancellationToken);
        
        var currentApplicant = _deliveryInfo.Applicants!.Single(applicant => applicant.IsRepresent ?? false);

        var countryId = currentApplicant.CountryId ??
                        throw new PropertyMissingException(currentApplicant.ApplicantId, currentApplicant.CountryId, "申请人");
        var applicantBookType = ApplicantBookType.GetApplicantBookType(countryId);
        
        string country;
        if (applicantBookType == ApplicantBookType.Overseas)
        {
            var countryInfo = await countryRepository.GetCacheValueAsync(countryId);
            if (countryInfo is null)
            {
                throw new NotFoundException(countryId, "国家");
            }

            if (string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCode) || string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCountryName))
            {
                throw new ApplicationException("商标局没有对应的国家编码和名称");
            }

            country = $"{countryInfo.TrademarkDeliveryCode}  {countryInfo.TrademarkDeliveryCountryName}";
        }
        else
        {
            country = applicantBookType.Description;
        }

        var applicantType = await applicantTypeRepository.GetCacheValueAsync(currentApplicant.TypeId.GetOrDefaultEmpty());
        if (applicantType is null)
        {
            throw new ApplicantTypeMissingException(currentApplicant, "当前申请人");
        }
        
        var attachments = _deliveryInfo.Files?
                              .Where(file => int.TryParse(file.FileCode, out _) && file.BaseFileType is "申请人文件" or "递交官方")
                              .Select(file => 
                                  new ApplicantAttachment(
                                      file.FileName, 
                                      file.Url ?? string.Empty, 
                                      ApplicantAttachmentType.OfficialAttachment, 
                                      new ApplicantAttachmentSubType(int.Parse(file.FileCode!), file.FileDesc ?? string.Empty))) 
                          ?? Array.Empty<ApplicantAttachment>();

        var order = new TrademarkObjectionsOrder
        {
            BookType = applicantBookType.Code.ToString(),
            Country = country,
            OwnerType = applicantType.GetOwnerType(),
            ApplicantName = currentApplicant.ApplicantNameCn,
            IdCard = currentApplicant.CardNo,
            ApplicantAddress = currentApplicant.AddressCn,
            Code = currentApplicant.Postcode,
            PrincipalName = _deliveryInfo.ContactPerson,
            PrincipalTel = _deliveryInfo.ContactTel,
            AgentOrganConName = _deliveryInfo.AgentUser,
            SubjectType = currentApplicant.GetSubjectType(),
            CertificatesType = currentApplicant.CardType.GetApplicantCertificationType(),
            IsReplenish =  otherInfo.ReservationOfSupplementaryMaterial switch
            {
                false => 0,
                true => 1,
                _ => throw new ApplicationException("[是否保留补充材料权利]缺少对应的值")
            },
            LawBasisList = lawBasisList,
            QuoteRegistrationNumbers = otherInfo.CitedRegisterNumbers?.Replace(';', ','),
            ApplicantAttachments = attachments,
            BrandInfos = grandNumbers.Select(grandNumber => new BrandInfo
            {
                BrandRegisterNo = _deliveryInfo.AppNo,
                FirstCgNo = grandNumber
            }),
            OrderToken = _deliveryInfo.ProcId,
            ContactName = _deliveryInfo.ContactPerson,
            ContactTel = _deliveryInfo.ContactTel,
            ContactEmail = _deliveryInfo.ContactMailBox,
            AgentOrganTel = _deliveryInfo.ContactTel,
        };

        return order;
    }

    public override Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(TrademarkObjectionsOrder request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        _phoenixClient ??= _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
        return _phoenixClient.CreateOrderAsync(PhoenixUri.TrademarkObjections, request);
    }

    public override Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        var orderNo = response.Data?.OrderNo!;
        const int newStatus = (int) DeliveryStatus.Ordered;
        
        return Handler.UpdateDeliveryInfoInternalAsync(deliveryInfo =>
        {
            deliveryInfo.Status = newStatus;
            deliveryInfo.OrderNo = orderNo;
            deliveryInfo.OperationResult = true;
            return ValueTask.CompletedTask;
        }, cancellationToken);
    }

    public override async Task InitializeAsync(TrademarkObjectionsOrderCommand command, CancellationToken cancellationToken)
    {
        var procId = command.ProcId;
        _deliveryInfo = await DeliveryInfoRepository.Where(info => info.ProcId == procId)
            .Include(info => info.OtherInfo)
            .IncludeMany(info => info.Applicants)
            .IncludeMany(info => info.Files)
            .IncludeMany(info => info.LawBasis)
            .ToOneAsync(cancellationToken);

        if (_deliveryInfo is null)
        {
            throw new NotFoundException(procId, "递交任务");
        }
        
        _deliveryInfo.ValidateVersion(command.Version);

        _phoenixClient = _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
    }
    
    public async Task<bool> HandleExceptionAsync(Exception ex, CancellationToken cancellationToken = default)
    {
        await Handler.HandleExceptionInternalAsync(ex, true, false, cancellationToken);
        return false;
    }
}