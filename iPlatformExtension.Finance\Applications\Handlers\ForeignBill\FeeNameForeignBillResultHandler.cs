﻿using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal sealed class FeeNameForeignBillResultHandler(IBaseFeeTypeNameRepository feeTypeNameRepository) 
    : ForeignFeeResultHandlerBase
{
    protected override async ValueTask GetResultAsync(ForeignBillFeeExportDto dto)
    {
        dto.FeeName = await feeTypeNameRepository.GetTextValueAsync(dto.FeeName ?? string.Empty) ?? string.Empty;
    }
}