﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace iPlatformExtension.Common.Converters;

public sealed class DateTimeToDateOnlyJsonConverter : JsonConverter<DateTime?>
{
    public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        return reader.TryGetDateTime(out var date) ? date : default;
    }

    public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value?.ToString("yyyy-MM-dd"));
    }
}