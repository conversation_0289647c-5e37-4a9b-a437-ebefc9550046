﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_receive_flow", DisableSyncStructure = true)]
	public partial class MailReceiveFlow {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 办理结果
		/// </summary>
		[Column(Name = "action", StringLength = 30)]
		public string Action { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 是否当前流程
		/// </summary>
		[Column(Name = "current", DbType = "int")]
		public int? Current { get; set; }

		/// <summary>
		/// 办理意见
		/// 
		/// </summary>
		[Column(Name = "display_name", StringLength = 50)]
		public string DisplayName { get; set; }

		/// <summary>
		/// 完成日期
		/// </summary>
		[Column(Name = "finish_date", DbType = "datetime")]
		public DateTime? FinishDate { get; set; }

		/// <summary>
		/// 忽略人
		/// </summary>
		[Column(Name = "ignore_by", StringLength = 50)]
		public string IgnoreBy { get; set; }

		/// <summary>
		/// 忽略时间
		/// </summary>
		[Column(Name = "ignore_time", DbType = "datetime")]
		public DateTime? IgnoreTime { get; set; }

		/// <summary>
		/// 邮件id
		/// </summary>
		[Column(Name = "mail_id", StringLength = 50, IsNullable = false)]
		public string MailId { get; set; }

		/// <summary>
		/// 发件名称
		/// </summary>
		[Column(Name = "send_name", StringLength = 200)]
		public string SendName { get; set; }

		/// <summary>
		/// 流程序号
		/// </summary>
		[Column(Name = "seq", DbType = "int")]
		public int? Seq { get; set; }

		/// <summary>
		/// 分拣人
		/// </summary>
		[Column(Name = "sort_by", StringLength = 50)]
		public string SortBy { get; set; }

		/// <summary>
		/// 分拣日期
		/// </summary>
		[Column(Name = "sort_time", DbType = "datetime")]
		public DateTime? SortTime { get; set; }

		/// <summary>
		/// 承办人
		/// </summary>
		[Column(Name = "undertake_user_id", StringLength = 50)]
		public string UndertakeUserId { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[Column(Name = "update_by", StringLength = 50)]
		public string UpdateBy { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

	}

}
