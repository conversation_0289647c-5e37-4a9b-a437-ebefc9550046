﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 关联列表
/// </summary>
/// <param name="Id">案件id</param>
/// <param name="Volume">案卷号</param>
/// <param name="CaseTypeId">案件类型</param>
/// <param name="CaseDirection">案件流向</param>
/// <param name="CaseName">案件名称</param>
/// <param name="CustomerName">客户名称</param>
public record GetRelateCaseListDto(string Id, string Volume, string? CaseTypeId, string? CaseDirection, string? CaseName, string? CustomerName);

