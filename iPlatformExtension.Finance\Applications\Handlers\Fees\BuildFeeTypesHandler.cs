﻿using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class BuildFeeTypesHandler : IRequestHandler<BuildFeeTypesCommand>, IFeesQueryStatementBuilder
{
    private const string NameCode = nameof(BasFeeTypeName.NameCode);

    private const string IsFirstPayAnnual = nameof(CaseFeeList.IsFirstPayAnnual);
    
    private const string BasFeeTypeNameAlias = nameof(BasFeeTypeName);
    
    private const string FeeAlias = nameof(CaseFeeList);
    
    private static readonly IEnumerable<string> annualFeeCodes = new[] {"AN", "ANZNJ"};

    private static readonly IEnumerable<string> licenseFeeCodes = new[] {"SQ1", "SQ2", "SQ3", "SQ4"};
    

    public Task Handle(BuildFeeTypesCommand request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested) return Task.FromCanceled(cancellationToken);
        BuildFeesQueryStatement(request.Dto, request.FeesQuery);
        return Task.CompletedTask;

    }

    public static void BuildFeesQueryStatement(FeeQueryDto dto, ISelect<CaseFeeList> feesQuery, params object?[] args)
    {
        feesQuery.LeftJoin(fee => fee.FeeTypeNameId == fee.FeeTypeName.NameId);
        switch (dto.AnnualFeeType)
        {
            case AnnualFeeType.AnnualFee:
                feesQuery.WhereDynamicFilter(annualFeeCodes.BuildContainsDynamicFilter(
                    NameCode, BasFeeTypeNameAlias,
                    false.BuildEqualsDynamicFilter(IsFirstPayAnnual, FeeAlias)));
                break;
            case AnnualFeeType.NotAnnualButLicense:
                feesQuery.Where(fee => SqlExt.IsNull(fee.FeeTypeName.NameCode, string.Empty) != "ABCDE");
                feesQuery.Where(fee =>
                    (annualFeeCodes.Contains(fee.FeeTypeName.NameCode) && fee.IsFirstPayAnnual == true) ||
                    !annualFeeCodes.Contains(SqlExt.IsNull(fee.FeeTypeName.NameCode, string.Empty)));
                break;
            case AnnualFeeType.LicenseFee:
                feesQuery.Where(fee =>
                    (annualFeeCodes.Contains(fee.FeeTypeName.NameCode) && fee.IsFirstPayAnnual == true) ||
                    (licenseFeeCodes.Contains(fee.FeeTypeName.NameCode) && fee.IsFirstPayAnnual == false));
                break;
            case AnnualFeeType.NotAnnualNotLicense:
                feesQuery.Where(fee =>
                        !annualFeeCodes.Contains(SqlExt.IsNull(fee.FeeTypeName.NameCode, string.Empty)))
                    .Where(fee => !licenseFeeCodes.Contains(SqlExt.IsNull(fee.FeeTypeName.NameCode, string.Empty)));
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(dto.AnnualFeeType), "年费类型传参不正确");
        }
    }
}