using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_visit", DisableSyncStructure = true)]
	public partial class CusVisit {

		/// <summary>
		/// 访问ID
		/// </summary>
		[ Column(Name = "visit_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string VisitId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 内容
		/// </summary>
		[ Column(Name = "content", StringLength = -2)]
		public string Content { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 客户负责人
		/// </summary>
		[ Column(Name = "cus_leader", StringLength = 50)]
		public string CusLeader { get; set; }

		/// <summary>
		/// 客户参与人
		/// </summary>
		[ Column(Name = "cus_participant", StringLength = 500)]
		public string CusParticipant { get; set; }

		[ Column(Name = "form_type", StringLength = 50)]
		public string FormType { get; set; }

		/// <summary>
		/// 是否启用
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		/// <summary>
		/// 我方负责人
		/// </summary>
		[ Column(Name = "leader", StringLength = 50)]
		public string Leader { get; set; }

		/// <summary>
		/// 客户ID
		/// </summary>
		[ Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

		/// <summary>
		/// 我方参与人
		/// </summary>
		[ Column(Name = "participant", StringLength = 500)]
		public string Participant { get; set; }

		/// <summary>
		/// 地点
		/// </summary>
		[ Column(Name = "place", StringLength = 500)]
		public string Place { get; set; }

		/// <summary>
		/// 电话
		/// </summary>
		[ Column(Name = "tel", StringLength = 50)]
		public string Tel { get; set; }

		/// <summary>
		/// 访问主题
		/// </summary>
		[ Column(Name = "title", StringLength = 500)]
		public string Title { get; set; }

		/// <summary>
		/// 访问类型
		/// </summary>
		[ Column(Name = "type", StringLength = 50)]
		public string Type { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// 访问时间
		/// </summary>
		[ Column(Name = "vist_time")]
		public DateTime? VistTime { get; set; }

	}

}
