﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.NominalChange;

internal sealed class NominalChangeDeliveryCommandHandler(
    IMediator mediator,
    IRedisCache<RedisCacheOptionsBase> redisCache,
    IDeliveryInfoRepository deliveryInfoRepository,
    IApplyTypeRepository applyTypeRepository) :
    DeliveryCommandHandlerBase(mediator, redisCache, deliveryInfoRepository)
{
    public override async Task<SendDeliveryInternalCommand?> HandleSendStartupCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        var deliveryInfo = context.DeliveryInfo;
        
        if (await applyTypeRepository.GetChineseValueAsync(deliveryInfo.ApplyTypeId) != "普通")
        {
            throw new ApplicationException("仅支持当前任务在“普通”类型的商标场景下自动递交");
        }
        
        if (deliveryInfo.Applicants?.Any(applicant => applicant.IsOtherApplicant) ?? false)
        {
            throw new ApplicationException("暂不支持当前任务在“共同申请/共有商标”的场景下自动递交");
        }
        
        return await base.HandleSendStartupCommandAsync(context, cancellationToken);
    }
}