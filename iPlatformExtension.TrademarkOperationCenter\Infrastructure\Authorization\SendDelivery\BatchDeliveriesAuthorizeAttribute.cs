﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using Microsoft.AspNetCore.Authorization;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Authorization.SendDelivery;

/// <summary>
/// 批量配送授权属性构造函数
/// </summary>
/// <param name="validateTypes">需要验证的批量配送类型集合</param>
public class BatchDeliveriesAuthorizeAttribute(params BatchDeliveriesValidateType[] validateTypes) : AuthorizeAttribute("DeliveriesValidation"), IAuthorizationRequirementData
{
    /// <summary>
    /// 获取授权要求集合
    /// </summary>
    /// <returns>基于验证类型生成的授权要求集合</returns>
    public IEnumerable<IAuthorizationRequirement> GetRequirements()
    {
        // 根据传入的验证类型集合，生成对应的授权要求
        return validateTypes.Select(validateType => new DeliveriesValidationRequirement(validateType));
    }
}
