using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 商标提成规则设置
	/// </summary>
	[ Table(Name = "trademark_bonus_config", DisableSyncStructure = true)]
	public partial class TrademarkBonusConfig {

		/// <summary>
		/// id
		/// </summary>
		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 案件流向
		/// </summary>
		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		/// <summary>
		/// 国家ID
		/// </summary>
		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 案件任务主键ID
		/// </summary>
		[ Column(Name = "ctrl_proc_id", StringLength = 50, IsNullable = false)]
		public string CtrlProcId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 任务标识
		/// </summary>
		[ Column(Name = "ctrl_proc_mark", DbType = "varchar(50)")]
		public string CtrlProcMark { get; set; }

		/// <summary>
		/// 案件状态ID
		/// </summary>
		[ Column(Name = "ctrl_proc_status_id", DbType = "varchar(50)")]
		public string CtrlProcStatusId { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 权值
		/// </summary>
		[ Column(Name = "real_point", DbType = "money")]
		public decimal RealPoint { get; set; } = 0M;

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "writing_type", StringLength = 50)]
		public string WritingType { get; set; }

		/// <summary>
		/// 日期类型
		/// </summary>
		[Column(Name = "date_type", StringLength = 50)]
		public string DateType { get; set; } = null!;

		/// <summary>
		/// 多类权值
		/// </summary>
		[Column(Name = "multipart_classes_point")]
		public decimal MultipartClassesPoint { get; set; }

	}

}
