﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <LangVersion>13</LangVersion>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DocumentationFile>bin\Debug\iPlatformExtension.Public.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DocumentationFile>bin\Release\iPlatformExtension.Public.xml</DocumentationFile>
    </PropertyGroup>

<!--    <PropertyGroup>-->
<!--        <ContainerBaseImage>mcr.microsoft.com/dotnet/aspnet:8.0</ContainerBaseImage>-->
<!--        <ContainerRuntimeIdentifier>linux-x64</ContainerRuntimeIdentifier>-->
<!--        <ContainerRegistry>harbor.aciplaw.com</ContainerRegistry>-->
<!--        <ContainerRepository>dev/iplatform.public</ContainerRepository>-->
<!--        <ContainerImageTag>dev</ContainerImageTag>-->
<!--        <ContainerUser>root</ContainerUser>-->
<!--        <PublishProfile>DefaultContainer</PublishProfile>-->
<!--    </PropertyGroup>-->

<!--    <ItemGroup>-->
<!--        <ContainerPort Include="8089" Type="tcp" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_HTTP_PORTS" Value="8089" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_URLS" Value="http://+:8089" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_ENVIRONMENT" Value="Development" />-->
<!--        <ContainerEnvironmentVariable Include="TZ" Value="Asia/Shanghai" />-->
<!--    </ItemGroup>-->

    <ItemGroup>
        <Protobuf Include="..\Protos\mails.proto" GrpcServices="Client" Link="Protos\mails.proto" />
        <Protobuf Include="..\Protos\mail_messages.proto" GrpcServices="Client" Link="Protos\mail_messages.proto" />
        <Protobuf Include="..\Protos\common_messages.proto" GrpcServices="Client" Link="Protos\common_messages.proto" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Grpc.Tools" Version="2.62.0">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Hangfire.MemoryStorage" Version="1.8.0" />
        <PackageReference Include="Hangfire.NetCore" Version="1.8.6" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
        <PackageReference Include="nacos-sdk-csharp.Extensions.Configuration" Version="1.3.9" />
        <PackageReference Include="nacos-sdk-csharp.AspNetCore" Version="1.3.9" />
        <PackageReference Include="NLog.DiagnosticSource" Version="5.2.0" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
        <PackageReference Include="Polly" Version="8.3.1" />
        <PackageReference Include="Scalar.AspNetCore" Version="2.2.1" />
        <PackageReference Include="SkyAPM.Agent.AspNetCore" Version="2.2.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\iPlatformExtension.Repository\iPlatformExtension.Repository.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Update="Applications\GenerateCode\GenerateCommand.tt">
        <Generator>TextTemplatingFileGenerator</Generator>
        <LastGenOutput>GenerateCommand.cs</LastGenOutput>
      </None>
      <None Update="Applications\GenerateCode\GenerateQuery.tt">
        <Generator>TextTemplatingFileGenerator</Generator>
        <LastGenOutput>GenerateQuery.cs</LastGenOutput>
      </None>
    </ItemGroup>

    <ItemGroup>
      <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
    </ItemGroup>

    <ItemGroup>
      <Compile Update="Applications\GenerateCode\GenerateCommand.cs">
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
        <DependentUpon>GenerateCommand.tt</DependentUpon>
      </Compile>
      <Compile Update="Applications\GenerateCode\GenerateQuery.cs">
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
        <DependentUpon>GenerateQuery.tt</DependentUpon>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Applications\Models\User\" />
    </ItemGroup>

    <ItemGroup>
        <InternalsVisibleTo Include="iPlatform.Extension.Public.Test" />
    </ItemGroup>

</Project>
