﻿using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.ContentManage
{
    public class GetTagListQuery :  IRequest<List<MailTagDto>>
    {
        /// <summary>
        /// 邮件类型:Receive:收件标签,Send:发件标签.
        /// </summary>
        public string MailType { get; set; } = SysEnum.MailType.Receive.ToString();
    }
}
