﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace iPlatformExtension.Common.Db.Mongo;

internal class MongoDbContextBuilder<T>(IServiceCollection services)
{
    public IServiceCollection Services { get; } = services;

    public MongoDbContextOptions<T> DbContextOptions { get; } = new();

    public MongoDbContextBuilder<T> ConfigureDbContextOptions(Action<MongoDbContextOptions<T>> configure)
    {
        configure(DbContextOptions);
        Services.TryAddEnumerable(ServiceDescriptor.Singleton<MongoDbContextOptions, MongoDbContextOptions<T>>());
        return this;
    }
}