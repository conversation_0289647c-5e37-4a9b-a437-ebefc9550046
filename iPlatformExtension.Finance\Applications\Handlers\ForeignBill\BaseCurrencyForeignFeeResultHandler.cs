﻿using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal sealed class BaseCurrencyForeignFeeResultHandler(ICurrencyRepository currencyRepository) : ForeignFeeResultHandlerBase
{
    protected override async ValueTask GetResultAsync(ForeignBillFeeExportDto dto)
    {
        dto.Currency = await currencyRepository.GetTextValueAsync(dto.Currency ?? string.Empty) ?? string.Empty;
        dto.BillCurrency = await currencyRepository.GetTextValueAsync(dto.BillCurrency ?? string.Empty) ?? string.Empty;
        dto.PaidCurrency = await currencyRepository.GetTextValueAsync(dto.PaidCurrency ?? string.Empty) ?? string.Empty;
        dto.RemittanceChargesCurrency =
            await currencyRepository.GetTextValueAsync(dto.RemittanceChargesCurrency ?? string.Empty) ?? string.Empty;
    }
}