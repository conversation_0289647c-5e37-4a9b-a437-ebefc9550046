﻿using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal abstract class OtherInfoResultNotificationHandlerBase(ISystemDictionaryRepository dictionaryRepository) : 
    INotificationHandler<OtherInfoResultQuery>
{
    protected readonly ISystemDictionaryRepository _dictionaryRepository = dictionaryRepository;
    
    protected abstract ValueTask<bool> MatchAsync(OtherInfoResultQuery query, CancellationToken cancellationToken);

    protected virtual async Task HandleAsync(OtherInfoResultQuery query, CancellationToken cancellationToken)
    {
        var dto = query.TrademarkDeliveryDto;
        var otherInfoSnapshot = dto.OtherInfoSnapshot;

        otherInfoSnapshot.CtrlProcMark =
            (await _dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.CtrlProcMark,
                otherInfoSnapshot.CtrlProcMark)).Value;
    }

    async Task INotificationHandler<OtherInfoResultQuery>.Handle(OtherInfoResultQuery notification, CancellationToken cancellationToken)
    {
        if (await MatchAsync(notification, cancellationToken))
        {
            await HandleAsync(notification, cancellationToken);
        }
    }
}