﻿using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Attributes
{
    [AttributeUsage(AttributeTargets.Class)]
    public class ServiceAttribute : Attribute
    {
        public ServiceLifetime LifeTime { get; set; }
        public ServiceAttribute(ServiceLifetime serviceLifetime = ServiceLifetime.Scoped)
        {
            LifeTime = serviceLifetime;
        }
    }
}
