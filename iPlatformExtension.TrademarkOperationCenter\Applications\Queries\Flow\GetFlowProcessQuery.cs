﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using MediatR;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow
{
    [Serializable]
    public class GetFlowProcessQuery : QueryBase, IRequest<PageResult<GetFlowProcessDto>>
    {
        /// <summary>
        ///  流程类型
        /// </summary>
        public string FFlowType { get; set; }


        /// <summary>
        ///  案件类型
        /// </summary>
        public string FSubType { get; set; }


        /// <summary>
        /// 标签ID
        /// </summary>
        public string[]? FPrivate { get; set; }

        /// <summary>
        /// 
        /// 待办:0,递交中:1000,3000,待检查:5000,待完成:6000,错误:500
        /// </summary>
        public int[]? Status { get; set; }

        /// <summary>
        /// 模糊查询:案件名称
        /// </summary>
        public string? SearchKey { get; set; }

        /// <summary>
        /// 团队ID
        /// </summary>
        public string? TeamID { get; set; }


        /// <summary>
        /// 任务ID
        /// </summary>
        public string? ProcID { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        public string Sort { get; set; } = "UpdateTime";

        /// <summary>
        /// 生效排序字段正序/倒序
        /// true:正序
        /// false:倒序
        /// </summary>
        public bool IsAscending { get; set; } = false;
        

    }
}
