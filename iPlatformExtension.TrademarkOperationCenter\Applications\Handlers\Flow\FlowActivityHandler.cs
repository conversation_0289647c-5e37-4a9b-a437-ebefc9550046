﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Repository.Implement;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using MediatR;
using System.Xml.Linq;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public class FlowActivityHandler(IFlowActivityRepository sysFlowActivityRepository, IMediator mediator, IFlowHistoryRepository flowHistoryRepository,
       ISysFlowNodeRepository sysFlowNodeRepository, IDeliveryInfoRepository deliveryInfoRepository) : IRequestHandler<FlowActivityCommand, SysFlowActivity>
    {
        public async Task<SysFlowActivity> Handle(FlowActivityCommand request, CancellationToken cancellationToken)
        {
            var userInfo = request.CurrentUser;
            var userid = userInfo.UserId;
            var ProcStatus = "TCLZ";
            var SubProcStatus = "t_in_process";
            SysFlowActivity fa;
            var cnName = userInfo.CnName;
            var curUserID = "";

            if (request.FlowInfo.FFlowType == FlowType.Delivery)
            {
                var hasDeliInfo = await deliveryInfoRepository.Where(o => o.ProcId == request.FlowInfo.ProcID)
                    .AnyAsync(cancellationToken);
                if (!hasDeliInfo)
                {
                    throw new Exception("递交数据不完整，不能提交！");
                }
            }


            fa = await sysFlowActivityRepository
                    .Where(o => o.ObjId == request.FlowInfo.ObjId && o.FlowType == request.FlowInfo.FFlowType)
                    .FirstAsync(cancellationToken);
            if (fa != null)
            {
                //流程检查
                if (fa.Status == FLOW_STATUS.S5000)
                {
                    if (!(request.FlowInfo.FAuditTypeID == FlowAuditType.Reject && (request.FlowInfo.FFlowType == FlowTypeEnum.BL || request.FlowInfo.FFlowType == FlowTypeEnum.AT)))
                    {
                        throw new Exception("流程已完成，不能提交！");
                    }
                }
                else
                {
                    if (fa.CurUserId != userid)
                    {
                        throw new Exception("您不是当前流程办理人员，不能提交！");
                    }
                    else if (fa.CurNodeId != request.FlowInfo.FCurNodeID)
                    {
                        throw new Exception("流程状态已更新，不能提交！");
                    }
                }

                curUserID = fa.CurUserId;
                if (fa.FlowSubType != request.FlowInfo.FFlowSubType)
                {
                    throw new Exception(fa.FlowSubType == "TII" ? "国内商标流程已启动" : "流程已启动");
                }

                fa.FlowId = request.FlowInfo.FFlowID;
                fa.Status = request.FlowInfo.FStatus;
                fa.PrevNodeId = fa.CurNodeId;
                fa.CurNodeId = request.FlowInfo.FNextNodeID;
                fa.CurUserId = request.FlowInfo.FNextUserID;
                fa.UpdateUserId = userid;
                fa.UpdateTime = DateTime.Now;
                fa.PrevAuditTypeId = request.FlowInfo.FAuditTypeID;
                fa.PrivateId = "";


                await mediator.Send(new ProcStatusCommand(request.FlowInfo, fa, ProcStatus, SubProcStatus, curUserID));
                await sysFlowActivityRepository.UpdateAsync(fa, cancellationToken);
            }
            else
            {
                fa = new SysFlowActivity()
                {
                    ObjId = request.FlowInfo.ObjId,
                    FlowId = request.FlowInfo.FFlowID,
                    Status = request.FlowInfo.FStatus,
                    FlowType = request.FlowInfo.FFlowType,
                    CurNodeId = request.FlowInfo.FNextNodeID,
                    CurUserId = request.FlowInfo.FNextUserID,
                    FlowSubType = request.FlowInfo.FFlowSubType,
                    PrevNodeId = request.FlowInfo.FCurNodeID,
                    PrevAuditTypeId = request.FlowInfo.FAuditTypeID,
                    AllowEdit = request.FlowInfo.FAllowEdit,
                    UpdateTime = DateTime.Now,
                    CreateTime = DateTime.Now,
                    CreateUserId = userid,
                    UpdateUserId = userid
                };

                await mediator.Send(new ProcStatusCommand(request.FlowInfo, fa, ProcStatus, SubProcStatus, ""));
                await sysFlowActivityRepository.InsertAsync(fa);
            }

            var history = new SysFlowHistory()
            {
                HistoryId = Guid.NewGuid().ToString(),
                ObjId = request.FlowInfo.ObjId,
                FlowType = request.FlowInfo.FFlowType,
                FlowSubType = request.FlowInfo.FFlowSubType,
                NodeId = request.FlowInfo.FCurNodeID,
                AuditUserId = userid,
                AuditCnName = cnName,
                AuditTime = DateTime.Now,
                Remark = request.FlowInfo.FRemark,
                AuditTypeId = request.FlowInfo.FAuditTypeID
            };

            await flowHistoryRepository.InsertAsync(history);
            return fa;
        }


    }
}
