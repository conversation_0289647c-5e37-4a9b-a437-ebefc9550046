﻿using iPlatformExtension.Common.Mediator.Handlers;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

internal interface IMatchTrademarkProcCommandHandler<in TNotification> : IMatchNotificationHandler<TNotification> 
    where TNotification : IMatchTrademarkProcCommand
{
    protected string CtrlProcId { get; }
    
    protected IEnumerable<string> CaseDirections { get; }

    ValueTask<bool> IMatchNotificationHandler<TNotification>.MatchAsync(TNotification notification, CancellationToken cancellationToken)
    {
        return new ValueTask<bool>(StringComparer.OrdinalIgnoreCase.Equals(CtrlProcId, notification.CtrlProcId) && CaseDirections.Contains(notification.CaseDirection));
    }
}