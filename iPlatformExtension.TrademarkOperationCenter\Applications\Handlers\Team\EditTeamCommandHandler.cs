﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;
using MediatR;
using static iPlatformExtension.Model.BaseModel.SysTeam;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 编辑团队
    /// </summary>
    public class EditTeamCommandHandler(
        IFreeSql freeSql,
        IMediator mediator,
        ISysTeamRepository sysTeamRepository,
        IMapper mapper,
        IHttpContextAccessor content)
        : IRequestHandler<EditTeamCommand>
    {
        public async Task Handle(EditTeamCommand request, CancellationToken cancellationToken)
        {
            if (await freeSql.Select<SysTeam>()
                    .Where(it =>
                        it.TeamId != request.TeamId && it.TeamName == request.TeamName &&
                        it.Enable == SysEnum.SystemTeamStatus.Normal.GetHashCode()).AnyAsync(cancellationToken))
            {
                throw new ApplicationException("团队名字已有重复");
            }

            var sysTeam = sysTeamRepository.Where(it => it.TeamId == request.TeamId).First();
            if (sysTeam == null)
            {
                throw new ApplicationException("没有找到该团队");
            }

            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userId);

            //非专属团队释放客户或失效
            if (!request.IsExclusive || !request.IsEffect)
            {
                var sysTeamCustomerList = freeSql.Select<SysTeamCustomer>().Where(it => it.TeamId == request.TeamId)
                    .ToList(it => it.SysTeamCustomerId);
                if (sysTeamCustomerList.Any())
                    await mediator.Send(new DeleteCustomerCommand(sysTeamCustomerList.ToArray()), cancellationToken);
            }

            var compare = new SysTeamEqualityComparer();
            if (!compare.Equals(sysTeam, mapper.Map<SysTeam>(request)))
            {
                sysTeam.IsEffect = request.IsEffect;
                sysTeam.IsExclusive = request.IsExclusive;
                sysTeam.UpdateTime = DateTime.Now;
                sysTeam.UpdateUserId = userId;
                sysTeam.TeamName = request.TeamName;
                sysTeam.TeamDescription = request.TeamDescription;
                sysTeam.Seq = request.Seq;
                sysTeam.AuthorizeUser = request.AuthorizeUser;
                await sysTeamRepository.UpdateAsync(sysTeam, cancellationToken);
            }

            //添加成员
            await mediator.Send(new EditTeamMemberCommand(sysTeam.TeamId, request.Members));
            //有效添加客户
            if (request.CustomerId is not null && request.CustomerId.Any() && request.IsEffect)
            {
                await mediator.Send(new EditCustomerCommand(request.CustomerId, sysTeam.TeamId));
            }
        }
    }
}