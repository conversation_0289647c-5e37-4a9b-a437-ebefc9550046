using System.Net.Mime;
using iPlatformExtension.Common.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;

namespace iPlatformExtension.Common.Logging.HttpClient;

public class TextRequestBodyLogger(ILogger<TextRequestBodyLogger> logger) : IHttpClientOnlyAsyncLogger
{
    private static readonly IEnumerable<string> textContentTypes =
    [
        MediaTypeNames.Application.Json,
        MediaTypeNames.Application.JsonPatch,
        MediaTypeNames.Application.Xml,
        MediaTypeNames.Application.XmlPatch,
        MediaTypeNames.Text.Xml,
        MediaTypeNames.Text.Plain,
        MediaTypeNames.Text.Html
    ];
    
    public async ValueTask<object?> LogRequestStartAsync(HttpRequestMessage request,
        CancellationToken cancellationToken = new CancellationToken())
    {
        var headers = request.Headers;
        var content = request.Content;
        var logContent = false;

        if (textContentTypes.Any(contentType => content?.Headers.ContentType?.MediaType?.Equals(contentType) ?? false))
        {
            logContent = true;
        }
        
        else if (headers.TryGetValues(HeaderNames.ContentType, out var contentTypes) && contentTypes.Any(textContentTypes.Contains))
        {
            logContent = true;
        }

        if (!logContent) return null;
        
        var requestBody = await content?.ReadAsStringAsync(cancellationToken)!;
        logger.LogHttpTextRequestBody(requestBody);

        return null;
    }

    public ValueTask LogRequestStopAsync(object? context, HttpRequestMessage request, HttpResponseMessage response,
        TimeSpan elapsed, CancellationToken cancellationToken = new CancellationToken())
    {
        return ValueTask.CompletedTask;
    }

    public ValueTask LogRequestFailedAsync(object? context, HttpRequestMessage request, HttpResponseMessage? response,
        Exception exception, TimeSpan elapsed, CancellationToken cancellationToken = new CancellationToken())
    {
        return ValueTask.CompletedTask;
    }
}