using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_country_list", DisableSyncStructure = true)]
	public partial class CaseCountryList {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "approval_date")]
		public DateTime? ApprovalDate { get; set; }

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "expiry_date")]
		public DateTime? ExpiryDate { get; set; }

		[ Column(Name = "is_enabled", StringLength = 50)]
		public string IsEnabled { get; set; }

		[ Column(Name = "official_status_id", StringLength = 50)]
		public string OfficialStatusId { get; set; }

		[ Column(Name = "reamark", StringLength = 200)]
		public string Reamark { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
