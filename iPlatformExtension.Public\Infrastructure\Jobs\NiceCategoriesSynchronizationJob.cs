using Hangfire;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Public.Applications.Commands.NiceCategories;
using MediatR;

namespace iPlatformExtension.Public.Infrastructure.Jobs;

internal sealed class NiceCategoriesSynchronizationJob : IScopeDependency
{
    private readonly PhoenixClient _phoenixClient;

    private readonly IMediator _mediator;

    private readonly IFreeSql _freeSql;

    private readonly ILogger _logger;

    public NiceCategoriesSynchronizationJob(PhoenixClientFactory phoenixClientFactory, IMediator mediator, IFreeSql freeSql, ILoggerFactory loggerFactory)
    {
        _phoenixClient = phoenixClientFactory.CreateClient();
        _mediator = mediator;
        _freeSql = freeSql;
        _logger = loggerFactory.CreateLogger(GetType());
    }
    
    [Queue("nice")]
    public async Task UpdateNiceCategoriesAsync()
    {
        
        var response = await _phoenixClient.GetNiceCategoriesAsync(PhoenixRequestParameters.Default);

        if (response?.Data is null)
        {
            throw new NullReferenceException("请求权大师尼斯分类接口失败");
        }

        var versionId = await _freeSql.Select<BasTrademarkItemsVersion>().WithLock()
            .Where(level => level.VersionName == "权大师尼斯分类").FirstAsync(version => version.VersionId);

        var operatorId = await _freeSql.Select<SysUserInfo>().WithLock().Where(info => info.UserName == "admin")
            .FirstAsync(info => info.UserId);
        if (operatorId is null)
        {
            throw new NotFoundException("admin", "用户");
        }
        
        _logger.LogInformation("定时同步尼斯分类");

        if (versionId is null)
        {
            await _mediator.Send(new CreateNiceCategoriesCommand(response.Data.List, operatorId));
        }
        else
        {
            await _mediator.Send(new UpdateNiceCategoriesCommand(versionId, response.Data.List, operatorId));
        }
    }
}