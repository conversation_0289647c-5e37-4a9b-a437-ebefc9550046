﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Outsourcing.Infrastructure.Authorization.SupplierAssignment;

internal sealed class SupplierAssignmentAuthorizationContextBuilder
{
    private List<SysAuthFilter> _filters = [];

    private List<string> _ctrlProcIds = [];

    private IEnumerable<string> _caseTypes = [];

    private IEnumerable<string> _caseDirections = [];

    private IEnumerable<string> _excludeBelongCompanies = [];

    private IEnumerable<string> _excludeProcStatusIds = [];

    public SupplierAssignmentAuthorizationContextBuilder AddFilters(IEnumerable<SysAuthFilter> filters)
    {
        if (filters is List<SysAuthFilter> filterList)
        {
            _filters = filterList;
        }
        else
        {
            _filters = [..filters];
        }

        return this;
    }
    
    public SupplierAssignmentAuthorizationContextBuilder AddCtrlProcIds(IEnumerable<string> ctrlProcIds)
    {
        if (ctrlProcIds is List<string> ctrlProcIdList)
        {
            _ctrlProcIds = ctrlProcIdList;
        }
        else
        {
            _ctrlProcIds = [..ctrlProcIds];
        }

        return this;
    }
    
    public SupplierAssignmentAuthorizationContextBuilder AddCaseTypes(IEnumerable<string> caseTypes)
    {
        _caseTypes = caseTypes;
        return this;
    }
    
    public SupplierAssignmentAuthorizationContextBuilder AddExcludeBelongCompanies(IEnumerable<string> excludeBelongCompanies)
    {
        _excludeBelongCompanies = excludeBelongCompanies;
        return this;
    }
    
    public SupplierAssignmentAuthorizationContextBuilder AddExcludeProcStatusIds(IEnumerable<string> excludeProcStatusIds)
    {
        _excludeProcStatusIds = excludeProcStatusIds;
        return this;
    }
    
    public SupplierAssignmentAuthorizationContextBuilder AddCaseDirections(IEnumerable<string> caseDirections)
    {
        _caseDirections = caseDirections;
        return this;
    }

    public SupplierAssignmentAuthorizationContext Build(IFreeSql freeSql)
    {
        var context = new SupplierAssignmentAuthorizationContext(_caseTypes, _caseDirections, _filters)
        {
            PersonalQuery = freeSql.Select<CaseProcInfo>().WithLock()
                .WhereIf(_filters.Count <= 0, info => false)
                .Where(info => !_excludeProcStatusIds.Contains(info.ProcStatusId))
                .Where(info => !freeSql.Select<CaseInfo>().Any(c => _excludeBelongCompanies.Contains(c.BelongCompany) && c.Id == info.CaseId))
                .Where(info => info.IsEnabled == true).Where(info => string.IsNullOrWhiteSpace(info.ForeginAgencyId)),
            CtrlProcIds = [.._ctrlProcIds],
            WholeQuery = CreateDefaultQuery(freeSql, _caseTypes, _caseDirections, _ctrlProcIds, _filters)
        };

        return context;
    }
    
    private ISelect<CaseProcInfo> CreateDefaultQuery(IFreeSql freeSql, IEnumerable<string> caseTypes, IEnumerable<string> caseDirections,
        IEnumerable<string> ctrlProcIds, List<SysAuthFilter> filters)
    {
        return freeSql.Select<CaseProcInfo>().WithLock()
            .WhereIf(filters.Count <= 0, info => false)
            .WhereIf(caseTypes.Any(), info => caseTypes.Contains(info.CaseInfo.CaseTypeId))
            .WhereIf(caseDirections.Any(), info => caseDirections.Contains(info.CaseInfo.CaseDirection))
            .WhereIf(ctrlProcIds.Any(), info => ctrlProcIds.Contains(info.CtrlProcId))
            .Where(info => !_excludeProcStatusIds.Contains(info.ProcStatusId))
            .Where(info => !freeSql.Select<CaseInfo>().Any(c => _excludeBelongCompanies.Contains(c.BelongCompany) && c.Id == info.CaseId))
            .Where(info => info.IsEnabled == true)
            .Where(info => string.IsNullOrWhiteSpace(info.ForeginAgencyId));
    }
}