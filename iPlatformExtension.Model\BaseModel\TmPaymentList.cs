using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "tm_payment_list", DisableSyncStructure = true)]
	public partial class TmPaymentList {

		[ Column(Name = "agent_name", StringLength = 200, IsNullable = false)]
		public string AgentName { get; set; }

		[ Column(Name = "app_date", StringLength = 50)]
		public string AppDate { get; set; }

		[ Column(Name = "app_no", StringLength = 50)]
		public string AppNo { get; set; }

		[ Column(Name = "applicant_name", StringLength = 200)]
		public string ApplicantName { get; set; }

		[ Column(Name = "bas_amount", StringLength = 50)]
		public string BasAmount { get; set; }

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; }

		[ Column(Name = "bill_date", StringLength = 50)]
		public string BillDate { get; set; }

		[ Column(Name = "business_name", StringLength = 200, IsNullable = false)]
		public string BusinessName { get; set; }

		[ Column(Name = "case_name", StringLength = 200)]
		public string CaseName { get; set; }

		[ Column(Name = "class_type", StringLength = 50)]
		public string ClassType { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "flow_status")]
		public int FlowStatus { get; set; } = 0;

		[ Column(Name = "foreign_flag", StringLength = 50)]
		public string ForeignFlag { get; set; }

		[ Column(Name = "has_invoice", StringLength = 50)]
		public string HasInvoice { get; set; }

		[ Column(Name = "list_id", StringLength = 50, IsNullable = false)]
		public string ListId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "pay_type", StringLength = 50)]
		public string PayType { get; set; }

		[ Column(Name = "plus_amount", StringLength = 50)]
		public string PlusAmount { get; set; }

		[ Column(Name = "plus_money", StringLength = 50)]
		public string PlusMoney { get; set; }

		[ Column(Name = "register_no", StringLength = 50)]
		public string RegisterNo { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "totals", StringLength = 50)]
		public string Totals { get; set; }

	}

}
