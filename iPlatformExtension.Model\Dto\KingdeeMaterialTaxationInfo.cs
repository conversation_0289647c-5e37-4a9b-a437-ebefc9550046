﻿using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 金蝶物料信息
/// </summary>
public record KingdeeMaterialTaxationInfo
{
    /// <summary>
    /// 费用名称id
    /// </summary>
    public string FeeTypeNameId { get; init; } = default!;

    /// <summary>
    /// 费用类型
    /// </summary>
    public string FeeClass { get; init; } = default!;

    /// <summary>
    /// 案件流向
    /// </summary>
    public string CaseDirection { get; init; } = default!;

    /// <summary>
    /// 案件类型
    /// </summary>
    public string CaseType { get; init; } = default!;

    /// <summary>
    /// 费用名称别名
    /// </summary>
    public string FeeNameAlias { get; init; } = default!;

    /// <summary>
    /// 申请类型
    /// </summary>
    public string? ApplyType { get; init; }

    /// <summary>
    /// 金蝶物料编码
    /// </summary>
    public string KingdeeMaterialCode { get; init; } = default!;

    /// <summary>
    /// 金蝶物料名称
    /// </summary>
    public string KingdeeMaterialName { get; init; } = default!;

    /// <summary>
    /// 发票号
    /// </summary>
    public string TaxationCode { get; init; } = default!;

    /// <summary>
    /// 发票名称
    /// </summary>
    public string TaxationName { get; init; } = default!;

}