using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "oa_base_meeting_resources", DisableSyncStructure = true)]
	public partial class OaBaseMeetingResources {

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		/// <summary>
		/// 数量
		/// </summary>
		[ Column(Name = "quantity")]
		public int? Quantity { get; set; }

		[ Column(Name = "resources_id", StringLength = 50, IsNullable = false)]
		public string ResourcesId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "text_en_us", StringLength = 50)]
		public string TextEnUs { get; set; }

		[ Column(Name = "text_ja_jp", StringLength = 50)]
		public string TextJaJp { get; set; }

		[ Column(Name = "text_zh_cn", StringLength = 50)]
		public string TextZhCn { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
