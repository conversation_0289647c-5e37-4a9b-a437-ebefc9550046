using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_form_dynamic", DisableSyncStructure = true)]
	public partial class SysFormDynamic {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "dynamic_form_code", StringLength = 50)]
		public string DynamicFormCode { get; set; }

		[ Column(Name = "dynamic_form_name", StringLength = 50)]
		public string DynamicFormName { get; set; }

		[ Column(Name = "foreign_id_name", StringLength = 50)]
		public string ForeignIdName { get; set; }

		[ Column(Name = "key_id_name", StringLength = 50)]
		public string KeyIdName { get; set; }

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

		[ Column(Name = "table_name", StringLength = 50)]
		public string TableName { get; set; }

	}

}
