﻿using OBS;

namespace iPlatformExtension.Common.Clients.HuaweiObs;

internal sealed class HuaweiObsBeginRequestContext<TRequest>
{

    private readonly TRequest _request;
    
    private readonly Func<TRequest, AsyncCallback, object?, IAsyncResult> _beginRequest;

    public HuaweiObsBeginRequestContext(Func<TRequest, AsyncCallback, object?, IAsyncResult> beginRequest, TRequest request)
    {
        _beginRequest = beginRequest;
        _request = request;
    }


    public IAsyncResult BeginRequest(AsyncCallback callback, object? state)
    {
        ArgumentNullException.ThrowIfNull(state);

        if (state is not HuaweiObsBeginRequestContext<TRequest> context)
            throw new ArgumentException("the argument is not HuaweiObsBeginRequestContext<TRequest>", nameof(state));
        
        var request = context._request;

        return context._beginRequest(request, callback, null);

    }
}