using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "tm_case_notice_batch", DisableSyncStructure = true)]
	public partial class TmCaseNoticeBatch {

		[ Column(Name = "key_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string KeyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "batch_no", StringLength = 50)]
		public string BatchNo { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_del")]
		public bool IsDel { get; set; } = true;

		[ Column(Name = "is_paper")]
		public bool IsPaper { get; set; } = true;

		[ Column(Name = "post_mark_date")]
		public DateTime? PostMarkDate { get; set; }

		[ Column(Name = "post_mark_no", StringLength = 50)]
		public string PostMarkNo { get; set; }

		[ Column(Name = "receive_time")]
		public DateTime? ReceiveTime { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "title", StringLength = 500)]
		public string Title { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
