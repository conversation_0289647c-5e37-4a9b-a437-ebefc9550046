using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_oa_type", DisableSyncStructure = true)]
	public partial class BasOaType {

		[ Column(Name = "type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string TypeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "class_id", StringLength = 50)]
		public string ClassId { get; set; }

		[ Column(Name = "code", StringLength = 50)]
		public string Code { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "is_process")]
		public bool? IsProcess { get; set; }

		[ Column(Name = "name", StringLength = 100)]
		public string Name { get; set; }

	}

}
