﻿using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Queries.Proc;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Proc;

internal sealed class UnpaidOfficialProcResultCustomerControlIdentifierHandler(IFreeSql freeSql, ISystemDictionaryRepository dictionaryRepository) 
    : INotificationHandler<UnpaidOfficialProcResultNotification>
{
    public async Task Handle(UnpaidOfficialProcResultNotification notification, CancellationToken cancellationToken)
    {
        var results = notification.Results;
        var customerIds = results.Select(dto => dto.Customer.Id).ToList();
        
        var customerControlIdentifierIds = await freeSql.Select<CusJoinList>().WithLock()
            .Where(list => customerIds.Contains(list.FormObjId))
            .Where(list => list.JoinType == SystemDictionaryName.CustomerControlIdentifier)
            .ToListAsync(list => new KeyValuePair<string, string>(list.FormObjId, list.JoinObjId), cancellationToken);
        
        var controlIdentifierInfos =
            await dictionaryRepository.GetCacheValueAsync(SystemDictionaryName.CustomerControlIdentifier);
        
        if (controlIdentifierInfos is not null)
        {
            var identifierDictionary = controlIdentifierInfos.ToDictionary(info => info.DictionaryId,
                info => new CustomerControlIdentifierInfo(info.DictionaryId, info.TextZhCn, info.TextEnUs));

            _ = results.GroupJoin(customerControlIdentifierIds, dto => dto.Customer.Id, pair => pair.Key,
                (dto, pairs) =>
                {
                    var infos = new List<CustomerControlIdentifierInfo>(pairs.Count());
                    foreach (var (_, dictionaryId) in pairs)
                    {
                        if (identifierDictionary.TryGetValue(dictionaryId, out var info))
                        {
                            infos.Add(info);
                        }
                    }

                    dto.CustomerControlIdentifiers = infos;

                    return dto;
                }).ToList();
        }
    }
}