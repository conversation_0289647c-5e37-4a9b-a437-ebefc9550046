using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_info_caiwu", DisableSyncStructure = true)]
	public partial class CaseInfoCaiwu {

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

	}

}
