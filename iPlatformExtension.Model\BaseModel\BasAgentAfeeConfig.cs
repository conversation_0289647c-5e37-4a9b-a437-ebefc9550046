using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_agent_afee_config", DisableSyncStructure = true)]
	public partial class BasAgentAfeeConfig {

		[ Column(Name = "config_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ConfigId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "base_type", StringLength = 50)]
		public string BaseType { get; set; }

		[ Column(Name = "config_type", StringLength = 50)]
		public string ConfigType { get; set; }

		[ Column(Name = "date_day")]
		public int? DateDay { get; set; }

		[ Column(Name = "date_month")]
		public int? DateMonth { get; set; }

		[ Column(Name = "date_type", StringLength = 50)]
		public string DateType { get; set; }

		[ Column(Name = "date_year")]
		public int DateYear { get; set; } = 0;

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "obj_agent_afee_id", StringLength = 50)]
		public string ObjAgentAfeeId { get; set; }

		[ Column(Name = "obj_config_id", StringLength = 50)]
		public string ObjConfigId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
