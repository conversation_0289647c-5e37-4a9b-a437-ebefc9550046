using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_foreign_contact_list", DisableSyncStructure = true)]
	public partial class CaseForeignContactList {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "contact_id", StringLength = 50)]
		public string ContactId { get; set; }

	}

}
