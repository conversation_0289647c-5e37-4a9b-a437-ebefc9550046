﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File;

internal sealed class PackageDeliveryFilesCommandHandler(IFreeSql freeSql, IMapper mapper, IMediator mediator)
    : IRequestHandler<PackageDeliveryFilesCommand, int?>
{

    public async Task<int?> Handle(PackageDeliveryFilesCommand request, CancellationToken cancellationToken)
    {
        var procId = request.ProcId;
        var files = await freeSql.Select<DeliFiles>()
            .Where(deliFiles => deliFiles.ProcId == procId && deliFiles.IsIdentity != true)
            .ToListAsync(cancellationToken);

        var lawBasisFiles = await freeSql.Select<DeliveryLawBasis>().Where(basis => basis.ProcId == procId)
            .ToListAsync(cancellationToken);
        files.AddRange(mapper.Map<List<DeliFiles>>(lawBasisFiles));

        if (files.Count <= 0) return null;
        
        var zipFileName = await mediator.Send(new DeliveryFilesCompressCommand(files, procId), cancellationToken);
        
        var fileId = await mediator.Send(new UploadFileCommand(zipFileName, $"delivery/{procId}/package"), cancellationToken);
        System.IO.File.Delete(zipFileName);

        return fileId;

    }
}