﻿using System.Text;
using AutoMapper;
using iPlatformExtension.Common.Clients.BladeCommon;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Public.Applications.Models.Statistics;
using iPlatformExtension.Public.Applications.Queries.Statistics;
using MediatR;
using Microsoft.Extensions.ObjectPool;
using MongoDB.Bson;
using Polly;

namespace iPlatformExtension.Public.Applications.Handlers.Statistics
{
    /// <summary>
    /// 3.2 1天微信通知
    /// </summary>
    internal sealed class WeChatTeamAllDeadLineWarningQueryHandler(
        IFreeSql freeSql,
        BladeCommonClient bladeCommonClient,
        IMediator mediator,
        IMapper mapper,
        ObjectPool<StringBuilder> stringBuilderPool,
        ILogger<WeChatTeamAllDeadLineWarningQuery> logger
    )
        : IRequestHandler<
            WeChatTeamAllDeadLineWarningQuery,
            IEnumerable<WeChatTeamAllDeadLineWarningDto>
        >
    {
        private static readonly string[] DateLineTeamRoles = ["date_line_team"];

        public async Task<IEnumerable<WeChatTeamAllDeadLineWarningDto>> Handle(
            WeChatTeamAllDeadLineWarningQuery request,
            CancellationToken cancellationToken
        )
        {
            var today = DateTime.Now.Date;
            var intFirstDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.IntFirstDate),
                cancellationToken
            );
            var cusFirstDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.CusFirstDate),
                cancellationToken
            );
            var intFinishDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.IntFinishDate),
                cancellationToken
            );
            var cusFinishDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.CusFinishDate),
                cancellationToken
            );
            var legalDueDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.LegalDueDate),
                cancellationToken
            );
            var intFirstDateWarning = mapper.Map<List<IntFirstDateWarning>>(
                intFirstDateWarningDtos
            );
            var cusFirstDateWarning = mapper.Map<List<CusFirstDateWarning>>(
                cusFirstDateWarningDtos
            );
            var intFinishDateWarning = mapper.Map<List<IntFinishDateWarning>>(
                intFinishDateWarningDtos
            );
            var cusFinishDateWarning = mapper.Map<List<CusFinishDateWarning>>(
                cusFinishDateWarningDtos
            );
            var legalDueDateWarning = mapper.Map<List<LegalDueDateWarning>>(
                legalDueDateWarningDtos
            );

            var deptRoleCode = SysEnum.DeptRoleCode.DR.ToString();

            var sysDeptUsers = await freeSql
                .Select<SysDeptUser, SysDeptRole, SysUserInfo, SysDeptInfo>()
                .WithLock()
                .LeftJoin(it => it.t1.RoleId == it.t2.RoleId)
                .LeftJoin(it => it.t1.UserId == it.t3.UserId)
                .LeftJoin(it => it.t1.DeptId == it.t4.DeptId)
                .Where(it =>
                    (it.t2.RoleCode == deptRoleCode && it.t1.IsDefault == true)
                    || it.t1.IsDefault == false
                )
                .Where(it => it.t3.Roles.Any(x => DateLineTeamRoles.Contains(x.RoleCode)))
                .Where(it => it.t4.IsEnabled == true)
                .WhereIf(request.UserId is not null, it => request.UserId.Contains(it.t1.UserId))
                .ToListAsync(
                    it => new
                    {
                        it.t1.UserId,
                        it.t1.DeptId,
                        it.t3.UserName,
                        it.t3.CnName,
                    },
                    cancellationToken
                );
            foreach (
                var underTakeUser in sysDeptUsers
                    .Select(it => new
                    {
                        it.UserId,
                        it.UserName,
                        it.CnName,
                    })
                    .Distinct()
            )
            {
                var ownDeptList = sysDeptUsers
                    .Where(it => it.UserId == underTakeUser.UserId)
                    .Select(it => it.DeptId);
                var cusFirstDateCount = cusFirstDateWarning.Count(it =>
                    ownDeptList.Contains(it.DeptId) && it.CusFirstDate >= today
                );
                var cusFinishDateCount = cusFinishDateWarning.Count(it =>
                    ownDeptList.Contains(it.DeptId) && it.CusFinishDate >= today
                );
                var legalDueDateCount = legalDueDateWarning.Count(it =>
                    ownDeptList.Contains(it.DeptId) && it.LegalDueDate >= today
                );
                var intFirstDateCount = intFirstDateWarning.Count(it =>
                    ownDeptList.Contains(it.DeptId) && it.IntFirstDate >= today
                );
                var intFinishDateCount = intFinishDateWarning.Count(it =>
                    ownDeptList.Contains(it.DeptId) && it.IntFinishDate >= today
                );
                var stringBuilder = stringBuilderPool.Get();
                stringBuilder.Append(
                    $"尊敬的{underTakeUser.CnName}:您好!您的团队有以下案件任务需在今日完成,请您督促其尽快处理,谢谢!\r\n"
                );
                stringBuilder.Append(
                    $"{SysEnum.ReportType.IntFirstDate.GetDescription()}:{intFirstDateCount} "
                );
                stringBuilder.Append(
                    $"{SysEnum.ReportType.CusFirstDate.GetDescription()}:{cusFirstDateCount} "
                );
                stringBuilder.Append(
                    $"{SysEnum.ReportType.IntFinishDate.GetDescription()}:{intFinishDateCount} "
                );
                stringBuilder.Append(
                    $"{SysEnum.ReportType.CusFinishDate.GetDescription()}:{cusFinishDateCount} "
                );
                stringBuilder.Append(
                    $"{SysEnum.ReportType.LegalDueDate.GetDescription()}:{legalDueDateCount}\r\n"
                );
                var cusFirstDateOverCount = cusFirstDateWarning.Count(it =>
                    ownDeptList.Contains(it.DeptId) && it.CusFirstDate < today
                );
                var cusFinishDateOverCount = cusFinishDateWarning.Count(it =>
                    ownDeptList.Contains(it.DeptId) && it.CusFinishDate < today
                );
                var legalDueDateOverCount = legalDueDateWarning.Count(it =>
                    ownDeptList.Contains(it.DeptId) && it.LegalDueDate < today
                );
                var intFirstDateOverCount = intFirstDateWarning.Count(it =>
                    ownDeptList.Contains(it.DeptId) && it.IntFirstDate < today
                );
                var intFinishDateOverCount = intFinishDateWarning.Count(it =>
                    ownDeptList.Contains(it.DeptId) && it.IntFinishDate < today
                );
                if (
                    cusFirstDateCount
                        + cusFinishDateCount
                        + legalDueDateCount
                        + intFirstDateCount
                        + intFinishDateCount
                        + cusFirstDateOverCount
                        + cusFinishDateOverCount
                        + legalDueDateOverCount
                        + intFirstDateOverCount
                        + intFinishDateOverCount
                    == 0
                )
                    continue;

                stringBuilder.Append(
                    $"【团队成员超期任务提醒】\r\n您的团队有以下案件任务已经超期，请您督促其尽快处理，谢谢！\r\n"
                );
                stringBuilder.Append(
                    $"{SysEnum.ReportType.CusFirstDate.GetDescription()}:{cusFirstDateOverCount} "
                );
                stringBuilder.Append(
                    $"{SysEnum.ReportType.IntFirstDate.GetDescription()}:{intFirstDateOverCount} "
                );
                stringBuilder.Append(
                    $"{SysEnum.ReportType.CusFinishDate.GetDescription()}:{cusFinishDateOverCount} "
                );
                stringBuilder.Append(
                    $"{SysEnum.ReportType.IntFinishDate.GetDescription()}:{intFinishDateOverCount} "
                );
                stringBuilder.Append(
                    $"{SysEnum.ReportType.LegalDueDate.GetDescription()}:{legalDueDateOverCount} "
                );
                var enterpriseWechatNotification = new EnterpriseWechatNotification
                {
                    Title = "【团队成员当天期限任务提醒】",
                    //UserAccount = "H05091",
                    UserAccount = underTakeUser.UserName,
                    Content = stringBuilder.ToString(),
                    Url = "https://ipr.aciplaw.com/openDefaultBrowser.html",
                    ButtonText = "点击跳转",
                };

                var sendSimpleNotificationAsync =
                    await bladeCommonClient.SendTextCardNotificationAsync(
                        enterpriseWechatNotification
                    );

                stringBuilderPool.Return(stringBuilder);
                if (!sendSimpleNotificationAsync!.Success)
                {
                    logger.LogError(
                        sendSimpleNotificationAsync.Data?.MessageId
                            + sendSimpleNotificationAsync.Data?.Message
                    );
                }
            }

            return new List<WeChatTeamAllDeadLineWarningDto>();
        }
    }
}
