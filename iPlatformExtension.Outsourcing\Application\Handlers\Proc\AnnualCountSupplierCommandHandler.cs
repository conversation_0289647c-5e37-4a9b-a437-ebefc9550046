﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class AnnualCountSupplierCommandHandler(IFreeSql<PlatformFreeSql> freeSql) : IRequestHandler<AnnualCountSupplierCommand, IReadOnlyDictionary<int, int>>
{
    public async Task<IReadOnlyDictionary<int, int>> Handle(AnnualCountSupplierCommand request, CancellationToken cancellationToken)
    {
        var supplierId = request.SupplierId;
        return await freeSql.Select<CaseProcInfo>().WithLock()
            .LeftJoin<CaseInfo>((info, caseInfo) => info.CaseId == caseInfo.Id)
            .Where<CaseInfo>(caseInfo => caseInfo.CaseDirection == CaseDirection.IO)
            .Where<CaseInfo>(caseInfo => caseInfo.CaseTypeId == CaseType.Trade)
            .Where(info => info.ForeginAgencyId == supplierId)
            .Where(info => info.EntrustDate != null)
            .GroupBy(info => info.EntrustDate!.Value.Year)
            .ToDictionaryAsync(aggregate => aggregate.Count(), cancellationToken);

    }
}