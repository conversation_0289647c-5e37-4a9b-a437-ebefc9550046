﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Commands.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Proc;

internal sealed class CreateFeeNameCtrlProcCommandHandler(
    IBaseCtrlProcRepository baseCtrlProcRepository,
    IFeeNameCtrlProcRepository feeNameCtrlProcRepository) 
    : IRequestHandler<CreateFeeNameCtrlProcCommand>
{
    public async Task Handle(CreateFeeNameCtrlProcCommand request, CancellationToken cancellationToken)
    {
        var (feeNameId, ctrlProcIds) = request;

        var feeNameCtrlProcList = await ctrlProcIds.ToAsyncEnumerable().WhereAwaitWithCancellation(async (ctrlProcId, token) =>
        {
            var baseCtrlProc = await baseCtrlProcRepository.GetAsync(ctrlProcId, cancellationToken: token);
            if (baseCtrlProc is not null && (baseCtrlProc.IsEnabled ?? false))
            {
                return true;
            }
            
            throw new ApplicationException($"任务名称{baseCtrlProc?.CtrlProcZhCn ?? string.Empty}不存在或已失效");
        }).Select(ctrlProcId => new FeeNameCtrlProc()
        {
            FeeNameId = feeNameId,
            CtrlProcId = ctrlProcId
        }).ToListAsync(cancellationToken);

        await feeNameCtrlProcRepository.InsertAsync(feeNameCtrlProcList, cancellationToken);
    }
}