using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_fee_sort", DisableSyncStructure = true)]
	public partial class BillFeeSort {

		[ Column(Name = "id_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string IdId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "bill_id", StringLength = 50, IsNullable = false)]
		public string BillId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "fee_type_name_id", StringLength = 50)]
		public string FeeTypeNameId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
