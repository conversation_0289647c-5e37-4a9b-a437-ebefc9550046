using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.Send;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Enum;
using MediatR;
using Microsoft.AspNetCore.Http;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send;

/// <summary>
/// 重新发送邮件命令处理程序
/// </summary>
/// <param name="mailSendRepository">邮件发送仓储</param>
/// <param name="mailSendListRepository">邮件发送列表仓储</param>
/// <param name="httpContextAccessor">HTTP上下文访问器</param>
internal sealed class ResendMailCommandHandler(
    IMailSendRepository mailSendRepository,
    IMailSendListRepository mailSendListRepository,
    IHttpContextAccessor httpContextAccessor) : IRequestHandler<ResendMailCommand>
{
    /// <summary>
    /// 处理重新发送邮件命令
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    public async Task Handle(ResendMailCommand request, CancellationToken cancellationToken)
    {
        if (request.MailIds == null || request.MailIds.Count == 0)
        {
            throw new ApplicationException("请提供要重新发送的邮件ID");
        }

        var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
        var now = DateTime.Now;

        // 批量处理每个邮件ID
        foreach (var mailId in request.MailIds)
        {
            // 获取邮件信息
            var mailSend = await mailSendRepository
                .Where(m => m.MailId == mailId)
                .FirstAsync(cancellationToken)
                ?? throw new ApplicationException($"邮件ID {mailId} 不存在");

            // 检查邮件状态是否为发送失败
            if (mailSend.Status != SendStatusType.SendFailed.GetHashCode())
            {
                throw new ApplicationException($"邮件ID {mailId} 不是处于发送失败状态，无法重新发送");
            }

            // 更新邮件状态为待发送
            mailSend.Status = SendStatusType.PendingSend.GetHashCode();
            await mailSendRepository.UpdateAsync(mailSend, cancellationToken);

            // 获取邮件发送列表信息
            var mailSendList = await mailSendListRepository
                .Where(m => m.MailId == mailId)
                .FirstAsync(cancellationToken);

            if (mailSendList != null)
            {
                // 更新邮件发送列表中的状态
                mailSendList.Status = SendStatusType.PendingSend.GetHashCode();
                mailSendList.UpdateBy = userId;
                mailSendList.UpdateTime = now;
                await mailSendListRepository.UpdateAsync(mailSendList, cancellationToken);
            }
            else
            {
                // 如果不存在发送记录，创建一个新的
                mailSendList = new()
                {
                    Id = Guid.NewGuid().ToString(),
                    CreateBy = userId,
                    CreateTime = now,
                    MailId = mailId,
                    Status = SendStatusType.PendingSend.GetHashCode(),
                    SendTime = mailSend.SendTime ?? now,
                    UpdateBy = userId,
                    UpdateTime = now
                };
                await mailSendListRepository.InsertAsync(mailSendList, cancellationToken);
            }
        }
    }
}
