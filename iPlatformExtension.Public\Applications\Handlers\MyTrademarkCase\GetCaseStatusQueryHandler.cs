﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.MyTrademarkCase
{
    /// <summary>
    /// 获取案件状态
    /// </summary>
    internal sealed class GetCaseStatusQueryHandler(IFreeSql freeSql)
        : IRequestHandler<GetCaseStatusQuery, GetCaseStatusDto>
    {
        public async Task<GetCaseStatusDto> Handle(GetCaseStatusQuery request, CancellationToken cancellationToken)
        {
            var start = await freeSql.Select<SysFlowHistory>().Where(it => it.ObjId == request.ProcId && it.FlowType == "DE").WithLock().FirstAsync(cancellationToken);
            return new GetCaseStatusDto(start != null);
        }
    }
}

