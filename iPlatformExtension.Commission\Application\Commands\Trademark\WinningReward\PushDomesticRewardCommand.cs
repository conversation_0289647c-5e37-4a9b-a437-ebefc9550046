﻿using iPlatformExtension.Commission.Infrastructure.Models;
using MediatR;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;

/// <summary>
/// 推送胜诉奖励命令
/// </summary>
/// <param name="UserId">当前操作的用户id</param>
/// <param name="DeptIds">关联的部门id集合</param>
/// <param name="Year">年份</param>
/// <param name="Month">月份</param>
public sealed record PushDomesticRewardCommand(string UserId, IEnumerable<string> DeptIds, int Year, int Month)
    : IBackgroundTracingCommand, IRequest
{
    /// <summary>
    /// 追踪id
    /// </summary>
    public string TraceParentId { get; set; } = null!;
    
    /// <summary>
    /// 操作名称
    /// </summary>
    public string OperationName { get; set; } = "国内商标胜诉奖励推送";
}