using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "M_PaymentDetail", DisableSyncStructure = true)]
	public partial class MPaymentDetail {

		[ Column(Name = "BATCH_ID")]
		public int? BATCHID { get; set; }

		[ Column(Name = "CRM_ID", StringLength = 300)]
		public string CRMID { get; set; }

		[ Column(Name = "E_MESSAGE", StringLength = 4000)]
		public string EMESSAGE { get; set; }

		[ Column]
		public float? FAGENCYAMO { get; set; }

		[ Column(StringLength = 300)]
		public string FAPPLYTYPE { get; set; }

		[ Column(StringLength = 300)]
		public string FARRIVALACCOUNT { get; set; }

		[ Column]
		public float? FARRIVALAMO { get; set; }

		[ Column(StringLength = 300)]
		public string FARRIVALTYPE { get; set; }

		[ Column(StringLength = 300)]
		public string FBUTYPE { get; set; }

		[ Column(StringLength = 300)]
		public string FCASEFLOW { get; set; }

		[ Column(StringLength = 300, IsNullable = false)]
		public string FCASEID { get; set; }

		[ Column(StringLength = 300)]
		public string FCASETYPE { get; set; }

		[ Column(StringLength = 300)]
		public string FCHARGEDETAIL { get; set; }

		[ Column(StringLength = 50)]
		public string? FCLUEUSERID { get; set; }

		[ Column(StringLength = 300, IsNullable = false)]
		public string FCOMPANYID { get; set; }

		[ Column(StringLength = 300, IsNullable = false)]
		public string FCUSTOMERID { get; set; }

		[ Column]
		public DateTime FDATE { get; set; }

		[ Column(StringLength = 300)]
		public string FDOUBLETYPE { get; set; }

		[ Column(StringLength = 300)]
		public string? FFLOWPERSONID { get; set; }

		[Column(Name = nameof(FBUSIUSERID))]
		public string? FBUSIUSERID { get; set; }

		[ Column(StringLength = 300, IsNullable = false)]
		public string FID { get; set; }

		[ Column(StringLength = 300)]
		public string FINVOCIE { get; set; }

		[ Column(StringLength = 3000)]
		public string FREMARK { get; set; }

		[ Column(StringLength = 300)]
		public string FSOURCETYPE { get; set; }

		[ Column(StringLength = 300)]
		public string FTASKTYPE { get; set; }

		[ Column]
		public float? FTHIRDCOST { get; set; }

		[ Column]
		public DateTime? FUPDATETIME { get; set; }

		[ Column(StringLength = 300)]
		public string? FUSERID { get; set; }

		[ Column(Name = "OPERATION_ID", IsIdentity = true, IsPrimary = true)]
		public long OPERATIONID { get; set; }

		[ Column(Name = "OPERATION_TYPE")]
		public int? OPERATIONTYPE { get; set; }

		[ Column(Name = "PROCESS_FLAG", StringLength = 1)]
		public string PROCESSFLAG { get; set; } = "P";

		[ Column(Name = "SYSTEM_TYPE")]
		public int? SYSTEMTYPE { get; set; }

	}

}
