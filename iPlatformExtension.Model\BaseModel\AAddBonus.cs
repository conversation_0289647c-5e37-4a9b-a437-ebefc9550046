using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_add_bonus", DisableSyncStructure = true)]
	public partial class AAddBonus {

		
		public string 案件名称 { get; set; }

		
		public DateTime? 官方来文日 { get; set; }

		
		public string 申请号 { get; set; }

		[ Column(Name = "申请人(中文)")]
		public string 申请人中文 { get; set; }

		
		public string 我方文号 { get; set; }

		
		public string 主承办人 { get; set; }

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
