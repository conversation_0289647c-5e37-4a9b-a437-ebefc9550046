﻿using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class BuildApplicantInfoHandler : IRequestHandler<BuildApplicantInfoCommand>, IFeesQueryStatementBuilder
{
    private const string CaseApplicantListAlias = nameof(CaseApplicantList);
    
    public Task Handle(BuildApplicantInfoCommand request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested) return Task.FromCanceled(cancellationToken);
        BuildFeesQueryStatement(request.Dto, request.FeesQuery, cancellationToken);
        return Task.CompletedTask;
    }

    public static void BuildFeesQueryStatement(FeeQueryDto dto, ISelect<CaseFeeList> feesQuery, params object?[] args)
    {
        feesQuery.InnerJoin<CaseApplicantList>((caseFeeList, applicantList) =>
                caseFeeList.CaseProcInfo.CaseId == applicantList.CaseId)
            .WhereDynamicFilter(dto.Applicants.BuildContainsDynamicFilter(nameof(CaseApplicantList.ApplicantId),
                CaseApplicantListAlias));
    }
}