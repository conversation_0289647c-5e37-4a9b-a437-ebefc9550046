using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_user_menu", DisableSyncStructure = true)]
	public partial class SysUserMenu {

		[ Column(Name = "fa", StringLength = 50)]
		public string Fa { get; set; }

		[ Column(Name = "is_default")]
		public bool? IsDefault { get; set; } = false;

		[ Column(Name = "menu_id", StringLength = 50)]
		public string MenuId { get; set; }

		[ Column(Name = "menu_name", StringLength = 100)]
		public string MenuName { get; set; }

		[ Column(Name = "parent_id", StringLength = 50)]
		public string ParentId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
