﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;
using FlowInfo = iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow.FlowInfo;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow
{
    public record FlowSubmitByBatchProcessCommand(FlowInfo[] infos, UserBaseInfo CurrentUser) : IRequest<List<FlowSubmitByBatchDto>>;
}
