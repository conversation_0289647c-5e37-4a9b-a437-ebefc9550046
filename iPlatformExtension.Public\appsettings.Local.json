﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information",
      "Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware": "Debug",
      "IFreeSql": "Information"
    },
    "Console": {
      "FormatterOptions": {
        "TimestampFormat": "[yyyy-MM-dd HH:mm:ss]"
      }
    },
    "FreeSql": {
      "Curd": {
        "IncludeCurdTypes": [ "Select", "Delete", "Update", "Insert", "InsertOrUpdate" ],
        "Environments": [ "Local", "Development" ]
      }
    }
  },
  "NLog": {
    "autoReload": true,
    "throwConfigExceptions": true,
    "internalLogLevel": "Debug",
    "targets": {
      "infoLog": {
        "type": "File",
        "encoding": "utf-8",
        "maxArchiveFiles": 7,
        "fileName": "${basedir}/logs/${shortdate}.log",
        "layout": "[${longdate}] [${uppercase:${level}}] [${logger}] [${activity:property=TraceId}] [${aspnet-request-url}] [${aspnet-mvc-action}] : ${newline}${message} ${newline}\n                ${exception:format=ToString} "
      },
      "errorLog": {
        "type": "File",
        "encoding": "utf-8",
        "maxArchiveFiles": 7,
        "fileName": "${basedir}/logs/${shortdate}-error.log",
        "layout": "[${longdate}] [${uppercase:${level}}] [${logger}] [${activity:property=TraceId}] [${aspnet-request-url}] [${aspnet-mvc-action}] : ${newline}${message} ${newline}\n                ${exception:format=ToString} "
      }
    },
    "rules": [
      {
        "logger": "*",
        "minLevel": "Info",
        "writeTo": "InfoLog"
      },
      {
        "logger": "*",
        "minLevel": "Error",
        "writeTo": "errorLog"
      }
    ]
  },
  "IPlatformAuth": {
    "Issuers": [
      "ipr.aciplaw.com"
    ],
    "Audiences": [
      "patas.aciplaw.com",
      "patas-test.aciplaw.com",
      "patas-dev.aciplaw.com"
    ],
    "SecurityKey": "MDBkMDVkMmVlZGI1MTQ4NDE0ZTI3ZmNiODI4MDdhMDA="
  },
  "BladeAuth": {
    "Issuer": "",
    "Audience": "iPlatformExtend",
    "SecurityKey": "bladexisapowerfulmicroservicearchitectureupgradedandoptimizedfromacommercialproject"
  },
  "ConnectionStrings": {
    "Default": "data source=***************,7433;initial catalog=acip_iplatform_1009;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true;Min Pool Size=5;Max Pool Size=10;Connection Lifetime=300",
    "Redis": "*************:6379,password=Acip_cc@54,defaultDatabase=10"
  },
  //正式
  //"WxConfig": {
  //  "Coprid": "ww61328062c163c8ec",
  //  "Corpsecret": "g5Bp0gCFe8Z8lyJm6eBl181ACOYpoZ0m0SAiVqZUF_w",
  //  "Appid": "1000062"
  //}
  //测试
  //"WxConfig": {
  //  "Corpid": "ww61328062c163c8ec",
  //  "Corpsecret": "Kcbpj6VXRNI_v4pmg_6tuvPpM2CLRQ6ujCpyrp3QS_Q",
  //  "Agentid": "1000075",
  //  "RedirctTargetName": "应用中心",
  //  "RedirectUri": "http://ipr.aciplaw.com/openDefaultBrowser.html"
  //},
  "WxConfig": {
    "Corpid": "ww61328062c163c8ec",
    "Corpsecret": "Kcbpj6VXRNI_v4pmg_6tuvPpM2CLRQ6ujCpyrp3QS_Q",
    "Agentid": "1000075",
    "RedirctTargetName": "应用中心",
    "RedirectUri": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=ww61328062c163c8ec&redirect_uri=https%3A%2F%2Fpatas.aciplaw.com%2F%2Fauth%2Foauth%2Fcallback2&response_type=code&scope=SCOPE&agentid=1000045&state=STATE#wechat_redirect"
  },
  "SignalRWithOrigins": [
    "https://ipr.aciplaw.com",
    "https://ipr-test.aciplaw.com",
    "http://ipr-test.aciplaw.com",
    "http://localhost:5166",
    "http://************:5079"
  ]


}