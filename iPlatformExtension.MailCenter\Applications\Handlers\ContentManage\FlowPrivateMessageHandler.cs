﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.MailCenter.Infrastructure;
using iPlatformExtension.MailCenter.Infrastructure.Extension;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class FlowPrivateMessageHandler(ILogger<FlowPrivateMessageHandler> logger, IFreeSql<MailCenterFreeSql> freeSql,
        IConfiguration configuration, IMediator mediator, MailTool mailTool
        ) : IRequestHandler<CalculateFlowPrivateCountQuery>
    {
        private const int POLLING_INTERVAL_SECONDS = 60;
        public async Task Handle(CalculateFlowPrivateCountQuery request, CancellationToken cancellationToken)
        {
            _ = Task.Run(() =>
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    var lst = freeSql.Select<MailFlowPrivate, FlowPrivateList, FlowRecord>().WithLock().InnerJoin(o => o.t1.Id == o.t2.PrivateId)
                     .LeftJoin(o => o.t2.MailId == o.t3.MailId)
                     .Where(o => o.t3.IsCurrent == MailFlowActionStatus.Enable.GetHashCode())
                        .GroupBy(o => new { o.t1.Id, o.t1.UserId }).Select(o =>
                            new FlowAnalyseDto
                            {
                                Count = o.Count(),
                                UserId = o.Value.Item1.UserId,
                                Type = "FlowPrivate".ToString(),
                                Title = o.Value.Item1.Id,
                            });

                    mailTool.TryAdd(lst);
                }
            }, cancellationToken);
        }
    }
}
