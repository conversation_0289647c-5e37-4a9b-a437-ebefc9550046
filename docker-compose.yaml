﻿services:
  iplatform.extension.finance:
    image: harbor.aciplaw.com/test/iplatform.finance:latest
    build:
      context: .
      dockerfile: iPlatformExtension.Finance/Dockerfile
    volumes:
      - "/root/iplatform/finance:/root"
      - "/tmp/iplatform.finance/logs:/app/logs"
      - "/tmp/iplatform:/tmp"
    network_mode: host
    pid: host
    
  iplatform.monitor:
    image: mcr.microsoft.com/dotnet/monitor:7
    pid: host
    network_mode: host
    volumes:
      - "/tmp/iplatform:/tmp"
    command:
      - --no-auth
      - true
    depends_on:
      - iplatform.extension.finance