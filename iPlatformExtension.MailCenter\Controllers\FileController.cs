﻿using iPlatformExtension.MailCenter.Applications.Queries.File;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Org.BouncyCastle.Bcpg.OpenPgp;
using System.IO;

namespace iPlatformExtension.MailCenter.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class FileController(IMediator mediator) :  ControllerBase
    {
        /// <summary>
        /// 文件下载
        /// </summary>
        /// <param name="fileUrl">完整文件下载地址</param>
        /// <param name="fileRealName">文件真实名称</param>
        /// <returns></returns>
        [HttpGet("FileDownload")]
        public async Task<IActionResult> FileDownload(string fileUrl,string fileRealName)
        {
            var file = await mediator.Send(new GetFileBytesQuery(fileUrl));
            return File(file, "application/octet-stream", fileRealName);
        }
    }
}
