﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 案件列表查询参数
/// </summary>
public class CaseListQueryDto
{
    /// <summary>
    /// 案件名称
    /// </summary>
    public string? CaseName { get; set; }

    /// <summary>
    /// 案件类型
    /// </summary>
    public string? CaseType { get; set; }

    /// <summary>
    /// 国家编码
    /// </summary>
    public string? CountryCode { get; set; }

    /// <summary>
    /// 案件流向
    /// </summary>
    public string? CaseDirection { get; set; }
}