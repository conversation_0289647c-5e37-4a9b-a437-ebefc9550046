using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_further_information_list", DisableSyncStructure = true)]
	public partial class AppFurtherInformationList {

		[ Column(Name = "apply_case_id", StringLength = 50)]
		public string ApplyCaseId { get; set; }

		[ Column(Name = "apply_id", StringLength = 50)]
		public string ApplyId { get; set; }

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "deadline")]
		public DateTime? Deadline { get; set; }

		[ Column(Name = "flow_end_time")]
		public DateTime? FlowEndTime { get; set; }

		[ Column(Name = "flow_start_time")]
		public DateTime? FlowStartTime { get; set; }

		[ Column(Name = "further_info_subject", StringLength = 200)]
		public string FurtherInfoSubject { get; set; }

		[ Column(Name = "further_information_batch_id", StringLength = 50)]
		public string FurtherInformationBatchId { get; set; }

		[ Column(Name = "further_information_id", StringLength = 50)]
		public string FurtherInformationId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "further_information_type", StringLength = 50)]
		public string FurtherInformationType { get; set; }

		[ Column(Name = "is_finish")]
		public bool? IsFinish { get; set; } = false;

		[ Column(Name = "load_type", StringLength = 50)]
		public string LoadType { get; set; }

		[ Column(Name = "new_int_first_date", StringLength = 50)]
		public string NewIntFirstDate { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

		/// <summary>
		/// ί��id
		/// </summary>
		public string? EntrustId { get; set; }

	}

}
