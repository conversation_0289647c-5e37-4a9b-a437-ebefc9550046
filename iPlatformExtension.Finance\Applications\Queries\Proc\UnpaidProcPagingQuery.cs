﻿using FreeSql;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Models.Proc;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Queries.Proc;

internal sealed record UnpaidProcPagingQuery(
    ISelect<CaseFeeList> FeesQuery, 
    string SortCondition, 
    SortOrder SortOrder, 
    int PageIndex, int PageSize, 
    long? Total) : IRequest<IEnumerable<UnpaidOfficialProcDto>>;