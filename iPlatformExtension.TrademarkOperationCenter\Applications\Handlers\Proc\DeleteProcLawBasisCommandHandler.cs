﻿using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class DeleteProcLawBasisCommandHandler(ITrademarkLawBasisRepository trademarkLawBasisRepository) : IRequestHandler<DeleteProcLawBasisCommand>
{
    public Task Handle(DeleteProcLawBasisCommand request, CancellationToken cancellationToken)
    {
        return trademarkLawBasisRepository.DeleteAsync(request.LawBasisId, cancellationToken);
    }
}