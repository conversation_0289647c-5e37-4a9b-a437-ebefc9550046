using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mon_receive_bill_other", DisableSyncStructure = true)]
	public partial class MonReceiveBillOther {

		[ Column(Name = "rbo_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string RboId { get; set; }

		[ Column(Name = "a_fee", DbType = "money")]
		public decimal? AFee { get; set; }

		[ Column(Name = "allot_id", StringLength = 50)]
		public string AllotId { get; set; }

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "bill_no", StringLength = 50)]
		public string BillNo { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_no", StringLength = 50)]
		public string <PERSON>N<PERSON> { get; set; }

		[ Column(Name = "fee_name", StringLength = 100)]
		public string FeeName { get; set; }

		[ Column(Name = "fee_type", StringLength = 20)]
		public string FeeType { get; set; }

		[ Column(Name = "input_time")]
		public DateTime? InputTime { get; set; }

		[ Column(Name = "o_fee", DbType = "money")]
		public decimal? OFee { get; set; }

		[ Column(Name = "t_fee", DbType = "money")]
		public decimal? TFee { get; set; }

	}

}
