using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "oa_flow_config", DisableSyncStructure = true)]
	public partial class OaFlowConfig {

		/// <summary>
		/// 流程类型
		/// </summary>
		[ Column(Name = "attendance_type", StringLength = 50)]
		public string AttendanceType { get; set; }

		/// <summary>
		/// 适用分公司
		/// </summary>
		[ Column(Name = "company_id", StringLength = 50)]
		public string CompanyId { get; set; }

		/// <summary>
		/// 流程id
		/// </summary>
		[ Column(Name = "flow_id", StringLength = 50)]
		public string FlowId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		/// <summary>
		/// 适用角色
		/// </summary>
		[ Column(Name = "role_type", StringLength = 50)]
		public string RoleType { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
