﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.Service.Interface;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Finance.Controllers;

/// <summary>
/// 申请人控制器
/// </summary>
[ApiController]
[Route("applicant")]
public class ApplicantController : ControllerBase
{
    private readonly IApplicantQueryService _applicantQuery;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="applicantQuery">申请人查询服务</param>
    public ApplicantController(IApplicantQueryService applicantQuery)
    {
        _applicantQuery = applicantQuery;
    }

    /// <summary>
    /// 通过关键字查询申请人信息
    /// 如果选中客户，连客户id一起传回来
    /// </summary>
    /// <param name="keyword">关键字</param>
    /// <param name="customerId">客户id</param>
    /// <returns>申请人信息</returns>
    [HttpGet]
    [ResponseCache(Duration = 600, VaryByQueryKeys = new []{"keyword", "customerId"})]
    public Task<IEnumerable<ApplicantInfo>> GetApplicantInfosAsync(string? keyword, string? customerId)
    {
        return _applicantQuery.GetApplicantsAsync(keyword, customerId);
    }
}