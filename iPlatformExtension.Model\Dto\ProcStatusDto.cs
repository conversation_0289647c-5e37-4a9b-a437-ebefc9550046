﻿namespace iPlatformExtension.Model.Dto
{
    /// <summary>
    /// 委案订单任务状态查询结果Dto
    /// </summary>
    public class ProcStatusDto
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string OrderCode { get; set; }

        /// <summary>
        /// 订单下案件Id
        /// </summary>
        public string CaseId { get; set; }

        /// <summary>
        /// 产品Id
        /// </summary>
        public string OrderProductId { get; set; }

        /// <summary>
        /// 产品对应任务状态  格式：任务名称+‘-’+任务状态（承办人：XXXX）
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
         
    }
}
