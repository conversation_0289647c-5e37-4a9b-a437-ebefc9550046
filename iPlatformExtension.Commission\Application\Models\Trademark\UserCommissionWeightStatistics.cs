﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Commission.Application.Models.Trademark;

/// <summary>
/// 用户提成权重统计
/// </summary>
public class UserCommissionWeightStatistics
{
    /// <summary>
    /// 序号
    /// </summary>
    [ExcelColumn(Name = "序号", IndexName = "A")]
    public int SerialNumber { get; set; }
    
    /// <summary>
    /// 用户id
    /// </summary>
    [ExcelColumn(Ignore = true)]
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 工号
    /// </summary>
    [ExcelColumn(Name = "员工编号", IndexName = "E")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 姓名
    /// </summary>
    [ExcelColumn(Name = "员工姓名", IndexName = "F")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 地区
    /// </summary>
    [ExcelColumn(Name = "地区", IndexName = "D")]
    public string District { get; set; } = string.Empty;

    /// <summary>
    /// 任务数量
    /// </summary>
    [ExcelColumn(Name = "任务量", IndexName = "G")]
    public int ProcCount { get; set; } = 0;

    /// <summary>
    /// 总权值
    /// </summary>
    [ExcelColumn(Name = "权值总额", IndexName = "H")]
    public decimal TotalWeightPoint { get; set; } = 0M;

    /// <summary>
    /// 部门名称
    /// </summary>
    [ExcelColumn(Name = "所属部门", IndexName = "C")]
    public string DeptName { get; set; } = string.Empty;

    /// <summary>
    /// 月份
    /// </summary>
    [ExcelColumn(Name = "所属月份", IndexName = "B")]
    public int Month { get; set; }

    /// <summary>
    /// 年度
    /// </summary>
    [ExcelColumn(Ignore = true)]
    public int Year { get; set; }
}