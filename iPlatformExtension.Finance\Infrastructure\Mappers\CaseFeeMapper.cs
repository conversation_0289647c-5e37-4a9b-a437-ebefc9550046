﻿using AutoMapper;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Finance.Infrastructure.Mappers;

internal sealed class CaseFeeMapper : Profile
{
    public CaseFeeMapper()
    {
        CreateMap<CaseFeeList, CaseFeePatchDto>()
            .ForMember(dto => dto.OfficialPaymentStatus, expression => expression.MapFrom(list => list.OfficerStatus));

        CreateMap<CaseFeePatchDto, CaseFeeList>()
            .ForMember(list => list.OfficialNotificationChecked, expression =>
            {
                expression.Condition(dto => dto.OfficialNotificationChecked.HasValue);
                expression.MapFrom(dto => dto.OfficialNotificationChecked!.Value);
            })
            .ForMember(list => list.PaymentListChecked, expression =>
            {
                expression.Condition(dto => dto.PaymentListChecked.HasValue);
                expression.MapFrom(dto => dto.PaymentListChecked!.Value);
            })
            .ForMember(list => list.OfficerStatus, expression =>
            {
                expression.Condition(dto => !ReferenceEquals(dto.OfficialPaymentStatus, null));
                expression.MapFrom(dto => dto.OfficialPaymentStatus!.Value);
            })
            .ForMember(list => list.OfficialFeeMark, expression =>
            {
                expression.Condition(dto => dto.OfficialFeeMark.HasValue);
                expression.MapFrom(dto => dto.OfficialFeeMark!.Value);
            })
            .ForMember(list => list.PayOfficerLegalDate, options =>
            {
                options.Condition(dto => dto.PayOfficerLegalDate.HasValue);
                options.MapFrom(dto => dto.PayOfficerLegalDate);
            })
            .ForMember(list => list.OfficialPaymentPublicationDate, options =>
            {
                options.Condition((_, list) => list.OfficialPaymentPublicationDate is null);
                options.MapFrom(dto => dto.OfficialPaymentPublicationDate);
            })
            .ForMember(list => list.PayOfficerDate, options =>
            {
                options.Condition(dto => dto.PayOfficerDate.HasValue);
                options.MapFrom(dto => dto.PayOfficerDate);
            });

        CreateMap<FeeListItemDto, OfficialFeesExportDto>()
            .ForMember(dto => dto.ApplicantNames, options =>
            {
                options.MapFrom((dto, _) => string.Join(',', dto.Applicants.Select(info => info.ApplicantCnName)));
            })
            .ForMember(dto => dto.CustomerName, options =>
            {
                options.MapFrom(dto => dto.CustomerInfo.CnName);
            })
            .ForMember(dto => dto.TrademarkNiceClasses, options =>
            {
                options.MapFrom(dto => dto.TrademarkClass);
            })
            .ForMember(dto => dto.DeliveryKey, options =>
            {
                options.Condition(dto => dto.DeliveryKey is not null);
                options.MapFrom(dto => dto.DeliveryKey!.Value.Value);
            })
            .ForMember(dto => dto.CaseDirection, options =>
            {
                options.MapFrom(dto => dto.CaseDirection.Value);
            })
            .ForMember(dto => dto.ApplicationNumber, options =>
            {
                options.MapFrom(dto => string.IsNullOrWhiteSpace(dto.AppNo) ? dto.TrademarkRegisterNo : dto.AppNo);
            })
            .ForMember(dto => dto.CustomerControlIdentifiers, options =>
            {
                options.MapFrom(
                    (dto, _) => string.Join(';', dto.CustomerControlIdentifiers.Select(info => info.CnName)));
            });

        CreateMap<FeeListItemDto, PaymentOfficialFeesExportDto>().IncludeBase<FeeListItemDto, OfficialFeesExportDto>();

        CreateMap<FeeListItemDto, NotPaidOfficialFeesExportDto>().IncludeBase<FeeListItemDto, PaymentOfficialFeesExportDto>();
    }
}