﻿using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class BadDeptCommandHandler(
    ICaseFeeRepository caseFeeRepository,
    IHttpContextAccessor httpContextAccessor)
    : FeesUpdate<PERSON><PERSON><PERSON>Handler(caseFeeRepository, httpContextAccessor)
{
    public ICaseFeeRepository CaseFeeRepository { get; } = caseFeeRepository;

    public IHttpContextAccessor HttpContextAccessor { get; } = httpContextAccessor;

    protected override FinanceOperation UpdateOperation => FinanceOperation.BadDept;
    
    protected override ValueTask<FeesUpdateResult> UpdateFeeAsync(CaseFeeList caseFeeList, FeeUpdateInfoDto feeUpdateInfoDto, string operatorId)
    {
        return new ValueTask<FeesUpdateResult>(caseFeeList.MarkBadDept(operatorId));
    }
}