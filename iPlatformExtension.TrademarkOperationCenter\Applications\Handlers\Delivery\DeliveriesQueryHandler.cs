﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DeliveriesQueryHandler(
    IFreeSql freeSql,
    IUserInfoRepository infoRepository,
    IBaseCtrlProcRepository baseCtrlProcRepository)
    : IRequestHandler<DeliveriesQuery, IEnumerable<ProcItemDto>>
{
    public async Task<IEnumerable<ProcItemDto>> Handle(DeliveriesQuery request, CancellationToken cancellationToken)
    {
        var procIds = request.ProcIds;
        ICacheableRepository<string, UserBaseInfo> userInfoRepository = infoRepository;
        
        var procList = await freeSql.Select<DeliInfo, CaseInfo, CaseApplicantList>().WithLock()
            .LeftJoin<CaseInfo>((procInfo, caseInfo) => procInfo.CaseId == caseInfo.Id)
            .LeftJoin<CaseApplicantList>((procInfo, applicant) => procInfo.CaseId == applicant.CaseId && applicant.IsRepresent == true)
            .Where((procInfo, caseInfo, applicant) => procIds.Contains(procInfo.ProcId))
            .ToListAsync((procInfo, caseInfo, applicant) => new ProcItemDto()
            {
                ProcId = procInfo.ProcId,
                CaseId = procInfo.CaseId,
                ProcNo = procInfo.ProcNo,
                Name = caseInfo.CaseName,
                AgencyId = procInfo.AgencyId,
                UndertakerId = procInfo.UndertakerId,
                CtrlProcId = procInfo.CtrlProcId,
                TrademarkClass = caseInfo.TrademarkClass ?? string.Empty,
                ApplicantId = applicant.ApplicantId,
                AppNo = procInfo.AppNo ?? string.Empty,
                Version = procInfo.Version,
                AgencyName = procInfo.AgencyName
            }, cancellationToken);

        var applicantIds = procList.Select(dto => dto.ApplicantId).ToList();
        var applicantList = await freeSql.Select<CaseApplicantList, CusApplicant>().WithLock()
            .LeftJoin((caseApplicant, applicant) => caseApplicant.ApplicantId == applicant.ApplicantId)
            .Where((caseApplicant, applicant) => applicantIds.Contains(applicant.ApplicantId))
            .ToListAsync((caseApplicant, applicant) =>
                    new ApplicantInfo(applicant.ApplicantId, applicant.ApplicantNameCn, applicant.ApplicantNameEn)
                    {
                        IsRepresent = caseApplicant.IsRepresent
                    }, cancellationToken);

        return await procList.GroupJoin(applicantList, dto => dto.ApplicantId, info => info.ApplicantId, (dto, applicants) =>
        {
            dto.ApplicantInfo = applicants.FirstOrDefault(applicantInfo => applicantInfo.IsRepresent);
            return dto;
        }).ToAsyncEnumerable().SelectAwait(async procInfo =>
        {
            var undertakerInfo = await userInfoRepository.GetCacheValueAsync(procInfo.UndertakerId ?? string.Empty);
            var baseCtrlProcInfo = await baseCtrlProcRepository.GetCacheValueAsync(procInfo.CtrlProcId);

            procInfo.UndertakerInfo = undertakerInfo;
            procInfo.ProcName = baseCtrlProcInfo?.CtrlProcZhCn ?? string.Empty;
            
            return procInfo;
        }).ToListAsync(cancellationToken);
    }
}