using Microsoft.Extensions.Http.Logging;

namespace iPlatformExtension.Common.Logging.HttpClient;

public interface IHttpClientOnlyAsyncLogger : IHttpClientAsyncLogger
{
    object? IHttpClientLogger.LogRequestStart(HttpRequestMessage request)
    {
        return null;
    }

    void IHttpClientLogger.LogRequestStop(object? context, HttpRequestMessage request, HttpResponseMessage response, TimeSpan elapsed)
    {
        
    }

    void IHttpClientLogger.LogRequestFailed(object? context, HttpRequestMessage request, HttpResponseMessage? response, Exception exception,
        TimeSpan elapsed)
    {
        
    }
}