using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_info_customize_column", DisableSyncStructure = true)]
	public partial class CaseInfoCustomizeColumn {

		[ Column(Name = "column_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ColumnId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_name", StringLength = 50)]
		public string ColumnName { get; set; }

		[ Column(Name = "column_type", StringLength = 50)]
		public string ColumnType { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
