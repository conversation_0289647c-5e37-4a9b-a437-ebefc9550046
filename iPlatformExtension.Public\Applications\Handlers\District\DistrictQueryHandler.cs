﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models.District;
using iPlatformExtension.Public.Applications.Queries.District;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.District;

internal sealed class DistrictQueryHandler(IFreeSql freeSql) : IRequestHandler<DistrictQuery, IEnumerable<DistrictInfo>>
{
    public async Task<IEnumerable<DistrictInfo>> <PERSON>le(DistrictQuery request, CancellationToken cancellationToken)
    {
        var (keyword, isEnabled) = request;
        return await freeSql.Select<BasDistrict>().WithLock()
            .WhereIf(isEnabled is not null, district => district.IsEnabled == isEnabled)
            .WhereIf(!string.IsNullOrWhiteSpace(keyword), district => district.TextZhCn.Contains(keyword!))
            .ToListAsync(district => new DistrictInfo(
                district.DistrictId,
                district.DistrictCode,
                district.TextZhCn,
                district.TextEnUs,
                district.CrmValue,
                district.IsManage,
                district.IsEnabled), cancellationToken);
    }
}