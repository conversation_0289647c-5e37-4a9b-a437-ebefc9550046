using iPlatformExtension.Common.Clients.HttpMessageHandlers;
using iPlatformExtension.Common.Clients.IPlatformWeb;
using iPlatformExtension.Common.Clients.IPlatformWeb.HttpMessageHandlers;
using iPlatformExtension.Common.Clients.IPlatformWeb.Options;
using iPlatformExtension.Common.Clients.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Extensions;

/// <summary>
/// 
/// </summary>
public static class PlatformWebClientExtenstion
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <returns></returns>
    public static IServiceCollection AddPlatformHttpClient(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddOptions<PlatformHttpClientOptions>().Bind(configuration);
        services.AddOptions<HttpClientLoggingOptions>()
            .PostConfigure<IOptionsMonitor<PlatformHttpClientOptions>>((options, monitor) =>
            {
                var loggingOptions = monitor.CurrentValue.Logging;
                foreach (var (uri, loggingFieldsOptions) in loggingOptions)
                {
                    options.TryAdd(uri, loggingFieldsOptions);
                }
            });
        services.AddScoped<JwtHeaderHandler>();
        services.AddScoped<ContentLoggingHandler>();
        services.AddHttpClient<PlatformHttpClient>("iPlatform.Web")
            .AddHttpMessageHandler<ContentLoggingHandler>()
            .AddHttpMessageHandler<JwtHeaderHandler>();
        return services;
    }
}