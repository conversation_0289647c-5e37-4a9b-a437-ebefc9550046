﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkDelivery.Infrastructure.Behaviors;

internal sealed class DeliveryVersionValidBehavior<TCommand, TResult> : IPipelineBehavior<TCommand, TResult>
    where TCommand : IDeliveryVersionCommand
{
    private readonly IFreeSql _freeSql;

    public DeliveryVersionValidBehavior(IFreeSql freeSql)
    {
        _freeSql = freeSql;
    }

    public async Task<TResult> Handle(TCommand request, RequestHandlerDelegate<TResult> next, CancellationToken cancellationToken)
    {
        var procId = request.ProcId;
        var currentVersion = request.Version;

        var actualVersion = await _freeSql.Select<DeliInfo>(procId).ToOneAsync(info => info.Version, cancellationToken);
        if (currentVersion == actualVersion)
        {
            return await next();
        }

        var ex = new VersionException(currentVersion, actualVersion);
        var history = new DeliHistory
        {
            ErrorMessage = ex.Message,
            FinishTime = DateTime.Now,
            ProcId = procId,
            StartTime = DateTime.Now,
            UserId = IDeliveryCommand.AdminUserId,
            Operation = "递交版本号验证",
            DisplayJsonId = null,
            Display = false,
            DisplayJson = null
        };
        await _freeSql.Insert(history).ExecuteAffrowsAsync(cancellationToken);

        throw ex;
    }
}

public interface IDeliveryVersionCommand : IDeliveryCommand
{}