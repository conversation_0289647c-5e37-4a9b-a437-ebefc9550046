using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Common.Cache;

public interface IRedisCacheableRepository<TKey, TValue> : ICacheableRepository<TKey, TValue> where TK<PERSON> : notnull
{
    protected IRedisCache<RedisCacheOptionsBase> RedisCache { get; }

    async ValueTask<TValue?> ICacheableRepository<TKey, TValue>.GetCacheValueAsync(TKey key, bool mustExist, string? keyArgumentName,
        CancellationToken cancellationToken)
    {
        if (key is null)
        {
            throw new ArgumentNullException(keyArgumentName);
        }

        if (MemoryCache.TryGetValue<IDictionary<TKey, TValue?>>(CacheKey, out var cacheValues) && (cacheValues?.TryGetValue(key, out var cacheValue) ?? false) && cacheValue is not null)
        {
            return cacheValue;
        }
        
        cacheValue = await RedisCache.GetCacheValueAsync<TKey, TValue>(<PERSON><PERSON><PERSON><PERSON>, key, cancellationToken);
        if (cacheValue is null)
        {
            cacheValue = await GetValueFromDbAsync(key, cancellationToken);

            if (cacheValue is not null)
            {
                await RedisCache.SetCacheValueAsync(CacheKey, key, cacheValue, true, cancellationToken);
            }
            else if (mustExist)
            {
                throw new KeyNotFoundException($"无法找到对应的值。 key: {key}");
            }
        }

        if (cacheValues is null)
        {
            await CreateCacheAsync(cancellationToken);
        }
        
        return cacheValue;
    }

    // protected new Task<IEnumerable<TValue>> GetValuesFromDbAsync(CancellationToken cancellationToken = default);

    async ValueTask<bool> ICacheableRepository<TKey, TValue>.RemoveCacheValueAsync(TKey key, CancellationToken cancellationToken)
    {
        if (MemoryCache.TryGetValue<IDictionary<TKey, TValue?>>(CacheKey, out var cacheValues) 
            && cacheValues is not null)
        {
            cacheValues.Remove(key);
        }
        
        return await RedisCache.RemoveCacheValueAsync(CacheKey, key, cancellationToken);
    }

    // async Task<IEnumerable<TValue>> ICacheableRepository<TKey, TValue>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    // {
    //     IEnumerable<TValue> values;
    //
    //     if (!ExpirationToken.HasChanged)
    //     {
    //          values = (await RedisCache.GetCacheValuesAsync<TValue>(CacheKey, cancellationToken))!;
    //          if (values.Any(value => value is not null)) return values.Where(value => value is not null);
    //     }
    //     
    //     values = await GetValuesFromDbAsync(cancellationToken);
    //     var keyValues = values.Where(value => value is not null)
    //         .Select(value => new KeyValuePair<TKey, TValue>(GenerateKey(value!), value!))
    //         .DistinctBy(pair => pair.Key)
    //         .ToDictionary(pair => pair.Key, pair => pair.Value);
    //     await RedisCache.SetCacheValuesAsync(CacheKey, keyValues, TimeSpan.FromHours(5), cancellationToken);
    //     
    //     return values;
    // }
    //
    // protected new Task<TValue?> GetValueFromDbAsync(TKey key, CancellationToken cancellationToken = default);
    //
    // async Task<TValue?> ICacheableRepository<TKey, TValue>.GetValueFromDbAsync(TKey key, CancellationToken cancellationToken)
    // {
    //     TValue? value;
    //
    //     if (!ExpirationToken.HasChanged)
    //     {
    //         value = await RedisCache.GetCacheValueAsync<TKey, TValue>(CacheKey, key, cancellationToken);
    //         if (value is not null) return value;
    //     }
    //         
    //     value = await GetValueFromDbAsync(key, cancellationToken);
    //     await RedisCache.SetCacheValueAsync(CacheKey, key, value, true, cancellationToken);
    //
    //     return value;
    // }
    
    
}