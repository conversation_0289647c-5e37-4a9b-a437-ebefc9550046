﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.Interceptor
{
    public class RepeatedActionFilter : Attribute, IActionFilter
    {
        private static readonly ConcurrentDictionary<string, string> LockRequest = new ConcurrentDictionary<string, string>();
        public void OnActionExecuted(ActionExecutedContext context)
        {
            var path = context.HttpContext.Request.Path;
            var querystring = context.HttpContext.Request.QueryString;
            LockRequest.Remove($"{path}{querystring}", out string currentThreadId);
            Console.WriteLine($"{currentThreadId}释放!");
        }

        public void OnActionExecuting(ActionExecutingContext context)
        {
            var path = context.HttpContext.Request.Path;
            var querystring = context.HttpContext.Request.QueryString;
            if (!LockRequest.TryAdd($"{path}{querystring}", Thread.CurrentThread.ManagedThreadId.ToString()))
            {
                Console.WriteLine($"{Thread.CurrentThread.ManagedThreadId}拦截!");
                throw new ApplicationException($"{Thread.CurrentThread.ManagedThreadId}请勿重复提交");
            }
            Console.WriteLine($"{Thread.CurrentThread.ManagedThreadId}通过!");
        }
    }
}
