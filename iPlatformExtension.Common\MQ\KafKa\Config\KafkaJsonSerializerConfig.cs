using System.Text.Json;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.MQ.KafKa.Converters;

namespace iPlatformExtension.Common.MQ.KafKa.Config
{
    /// <summary>
    /// Kafka JSON序列化配置策略
    /// </summary>
    public class KafkaJsonSerializerConfig
    {
        private readonly JsonSerializerOptions _options;

        /// <summary>
        /// 获取JSON序列化选项
        /// </summary>
        public JsonSerializerOptions Options => _options;

        /// <summary>
        /// 初始化Kafka JSON序列化配置
        /// </summary>
        /// <param name="options">自定义JSON序列化选项，如果为null则使用默认选项</param>
        public KafkaJsonSerializerConfig(JsonSerializerOptions? options = null)
        {
            _options = options ?? JsonSerializerOptionsFactory.CreateDefault();
        }

        /// <summary>
        /// 创建支持Column特性的Kafka JSON序列化配置
        /// </summary>
        /// <typeparam name="T">要序列化/反序列化的类型</typeparam>
        /// <returns>Kafka JSON序列化配置</returns>
        public static KafkaJsonSerializerConfig CreateWithColumnSupport<T>() where T : class
        {
            return new KafkaJsonSerializerConfig(JsonSerializerOptionsFactory.CreateWithColumnSupport<T>());
        }

        /// <summary>
        /// 创建用于Kafka消息处理的JSON值转换器
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <returns>JSON消息值转换器</returns>
        public JsonMessageValueConverter<T> CreateMessageValueConverter<T>()
        {
            return new JsonMessageValueConverter<T>(_options);
        }
    }
}