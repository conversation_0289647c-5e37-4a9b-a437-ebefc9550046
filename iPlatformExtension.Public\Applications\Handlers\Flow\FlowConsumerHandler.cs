﻿using Confluent.Kafka;
using iPlatformExtension.Public.Applications.Commands.Flow;
using iPlatformExtension.Public.MQ.FlowNotificationConsumer;
using MediatR;
using Microsoft.AspNetCore.Components.Forms;
using System.Diagnostics;

namespace iPlatformExtension.Public.Applications.Handlers.Flow
{
    [Obsolete]
    public class FlowConsumerHandler : INotificationHandler<FlowConsumerCommand>
    {
        private readonly ConsumerBuilder<string, string> _consumerBuilder;
        private readonly IMediator _mediator;
        private readonly ILogger<FlowConsumerHandler> _logger;

        public FlowConsumerHandler(IConfiguration configuration, IMediator mediator, ILogger<FlowConsumerHandler> logger)
        {
            var config = configuration.GetSection("Kafka:FlowConsumer").Get<ConsumerConfig>();
            config.AutoOffsetReset = AutoOffsetReset.Earliest;
            _consumerBuilder = new ConsumerBuilder<string, string>(config)
                  .SetKeyDeserializer(Deserializers.Utf8)
                    .SetValueDeserializer(Deserializers.Utf8);
            _mediator = mediator;
            _logger = logger;
        }


        public async Task Handle(FlowConsumerCommand notification, CancellationToken cancellationToken)
        {
            using (var consumer = _consumerBuilder.Build())
            {
                Console.WriteLine($"构建消费者成功{consumer.Name}");
                consumer.Subscribe(notification.consumer.Topic);

                while (true)
                {
                    try
                    {
                        var consumeResult = consumer.Consume();
                        if (consumeResult.IsPartitionEOF)
                        {
                            Console.WriteLine($"End of partition {consumeResult.TopicPartitionOffset} reached.");
                            continue;
                        }
                        await _mediator.Send(new FlowMessageCommand(consumeResult.Value));
                        // 处理消息
                        Console.WriteLine($"{nameof(FlowConsumerHandler)} Received message at {consumeResult.TopicPartitionOffset}: {consumeResult.Value}");
                    }
                    catch (ConsumeException e)
                    {
                        _logger.LogError(e, "FlowConsumerHandler消费异常");
                        Console.WriteLine($"Error occured: {e.Error.Reason}");
                        continue;
                    }
                }
            }
        }
    }
}
