﻿﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.Signature
{
    /// <summary>
    /// 删除签名命令
    /// </summary>
    /// <param name="Id">签名ID</param>
    public record DeleteSignatureCommand([Required(ErrorMessage = "签名ID不能为空")] string Id) : IRequest,IUnitOfWorkCommandMysql;
}
