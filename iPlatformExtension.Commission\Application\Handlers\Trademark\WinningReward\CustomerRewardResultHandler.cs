﻿using iPlatformExtension.Commission.Application.Notifications.WinningReward;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class CustomerRewardResultHandler(ICustomerRepository customerRepository) : INotificationHandler<RewardResultNotification>
{
    public async Task Handle(RewardResultNotification notification, CancellationToken cancellationToken)
    {
        var proc = notification.RewardProc;
        proc.CustomerName =
            (await customerRepository.GetCacheValueAsync(proc.CustomerId, cancellationToken: cancellationToken))
            ?.CustomerName ?? string.Empty;
    }
}