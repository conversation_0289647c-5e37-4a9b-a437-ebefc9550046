﻿using Hangfire;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Public.Applications.Commands.LawProvision;
using MediatR;

namespace iPlatformExtension.Public.Infrastructure.Jobs;

internal sealed class LawProvisionSynchronizationJob(IMediator mediator) : IScopeDependency
{
    [Queue("law-provision")]
    [DisableConcurrentExecution(300)]
    public async Task UpdateLawProvisionAsync()
    {
        await mediator.Send(UpdateLawProvisionCommand.AnnulmentLawProvisionCommand);
        await mediator.Send(UpdateLawProvisionCommand.ObjectionsLawProvisionCommand);
    }
}