using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_copy_rules", DisableSyncStructure = true)]
	public partial class CaseCopyRules {

		[ Column(Name = "config_id", StringLength = 50)]
		public string ConfigId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "rule_id", StringLength = 50, IsNullable = false)]
		public string RuleId { get; set; }

		[ Column(Name = "rule_user", StringLength = 50)]
		public string RuleUser { get; set; }

	}

}
