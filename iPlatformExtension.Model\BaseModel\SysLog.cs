using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_log", DisableSyncStructure = true)]
	public partial class SysLog {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "bas_form_name", StringLength = 50)]
		public string BasFormName { get; set; }

		[ Column(Name = "bas_key_id", StringLength = 50)]
		public string BasKeyId { get; set; }

		[ Column(Name = "data_xml", StringLength = -2)]
		public string DataXml { get; set; }

		[ Column(Name = "data_xml_result", StringLength = -2)]
		public string DataXmlResult { get; set; }

		[ Column(Name = "edit_type", StringLength = 50)]
		public string EditType { get; set; }

		[ Column(Name = "form_name", StringLength = 50)]
		public string FormName { get; set; }

		[ Column(Name = "input_time")]
		public DateTime? InputTime { get; set; }

		[ Column(Name = "ip_address", StringLength = 50)]
		public string IpAddress { get; set; }

		[ Column(Name = "key_id", StringLength = 50)]
		public string KeyId { get; set; }

		[ Column(Name = "key_value", StringLength = 200)]
		public string KeyValue { get; set; }

		[ Column(Name = "log_type", StringLength = 50)]
		public string LogType { get; set; }

		[ Column(Name = "status")]
		public int? Status { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
