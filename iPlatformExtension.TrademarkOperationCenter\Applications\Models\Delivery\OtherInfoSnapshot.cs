﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 商标递交其他信息快照
/// </summary>
public sealed class OtherInfoSnapshot
{
    /// <summary>
    /// 任务标识
    /// </summary>
    public string? CtrlProcMark { get; set; }
    
    /// <summary>
    /// 是否分割
    /// </summary>
    public bool? IsSplit { get; set; }

    /// <summary>
    /// 名义变更类型
    /// </summary>
    public string? NominalChangesType { get; set; }

    /// <summary>
    /// 引证商标注册号
    /// </summary>
    public string? CitedRegisterNumbers { get; set; }

    /// <summary>
    /// 法律与事实依据
    /// </summary>
    public IEnumerable<LegalFactualBasis> FactualBasis { get; set; } = Enumerable.Empty<LegalFactualBasis>();

    /// <summary>
    /// 是否涉及绝对理由
    /// </summary>
    public bool? HasAbsoluteReason { get; set; }

    /// <summary>
    /// 同意地址延及后续
    /// </summary>
    public bool? ExtendToSameAddress { get; set; }

    /// <summary>
    /// 无效宣告法律条文
    /// </summary>
    public string? AnnulmentLawBasic { get; set; }

    /// <summary>
    /// 无效宣告引证商标信息
    /// </summary>
    public IEnumerable<CitedTrademarkInfo> CitedTrademarkInfos { get; set; } = Enumerable.Empty<CitedTrademarkInfo>();

    /// <summary>
    /// 合同生效日期
    /// </summary>
    public DateOnly? ContractEffectiveDate { get; set; }

    /// <summary>
    /// 合同终止日期
    /// </summary>
    public DateOnly? ContractTerminationDate { get; set; }

    /// <summary>
    /// 许可类型
    /// </summary>
    public string? LicenseType { get; set; }
    
    /// <summary>
    /// 是否保留补充材料权利
    /// </summary>
    public bool? ReservationOfSupplementaryMaterial { get; set; }
    
    /// <summary>
    /// 交官名称
    /// </summary>
    public string? OfficialName { get; set; }
}