﻿using iPlatformExtension.MailCenter.Applications.Models.Receive;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Queries.Receive;

/// <summary>
/// 获取动作用户
/// </summary>
/// <param name="MailId">邮件id</param>
/// <param name="Action">动作Submit:提交,Transfer:移交,Reject:退回</param>
/// <param name="Flow">Send:发件,Receive:收件</param>
public record GetActionUserListQuery(string MailId, string Action, MailType Flow = MailType.Receive) : IRequest<GetActionUserListDto>;

