﻿using iPlatformExtension.Commission.Application.Notifications.Trademark.Domestic;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class DomesticCommissionProcResultHandler(IBaseCtrlProcRepository baseCtrlProcRepository) 
    : INotificationHandler<DomesticCommissionResultNotification>
{
    public async Task Handle(DomesticCommissionResultNotification notification, CancellationToken cancellationToken)
    {
        var commission = notification.Commission;
        var ctrlProcId = commission.CtrlProcId;
        commission.CtrlProcZhCn = await baseCtrlProcRepository.GetTextValueAsync(ctrlProcId) ?? string.Empty;
        
        commission.TrademarkItemsNum = commission.TrademarkClasses.Split(';').Length;
    }
}