using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mon_expand", DisableSyncStructure = true)]
	public partial class MonExpand {

		[ Column(Name = "allot_id", StringLength = 50, IsNullable = false)]
		public string AllotId { get; set; }

		[ Column(Name = "cost", DbType = "money")]
		public decimal? Cost { get; set; }

		[ Column(Name = "cost_case", DbType = "money")]
		public decimal? CostCase { get; set; }

		[ Column(Name = "cost_coop", DbType = "nchar(10)")]
		public string CostCoop { get; set; }

		[ Column(Name = "cost_other", DbType = "money")]
		public decimal? CostOther { get; set; }

		[ Column(Name = "expand_type", StringLength = 50)]
		public string ExpandType { get; set; }

		[ Column(Name = "fa_check", DbType = "money")]
		public decimal? FaCheck { get; set; }

		[ Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

		[ Column(Name = "processor_per1")]
		public double? ProcessorPer1 { get; set; }

		[ Column(Name = "processor_per2", StringLength = 50)]
		public string ProcessorPer2 { get; set; }

		[ Column(Name = "processor1", StringLength = 50)]
		public string Processor1 { get; set; }

		[ Column(Name = "processor2", StringLength = 50)]
		public string Processor2 { get; set; }

		[ Column(Name = "remark", StringLength = 200)]
		public string Remark { get; set; }

		[ Column(Name = "sub_id", StringLength = 50)]
		public string SubId { get; set; }

	}

}
