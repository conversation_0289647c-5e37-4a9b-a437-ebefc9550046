﻿using FreeSql;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeQueryBuildHandler(IMediator mediator, IFreeSql freeSql, ILogger<FeeQueryBuildHandler> logger)
    : IRequestHandler<FeeQueryBuildCommand, ISelect<CaseFeeList>>
{
    private const string FeeAlias = nameof(CaseFeeList);

    public async Task<ISelect<CaseFeeList>> Handle(FeeQueryBuildCommand request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
        {
            logger.LogError("费项查询：操作中止");
            return await Task.FromCanceled<ISelect<CaseFeeList>>(cancellationToken);
        }
        
        var feesQuery = freeSql.Select<CaseFeeList>().As(FeeAlias).WithLock();

        await mediator.Send(new BuildCaseFeeCommand(request.Dto, feesQuery), cancellationToken);
        await mediator.Send(new BuildFeeTypesCommand(request.Dto, feesQuery), cancellationToken);
        await mediator.Send(new BuildProcInfoCommand(request.Dto, feesQuery), cancellationToken);
        await mediator.Send(new BuildApplicantInfoCommand(request.Dto, feesQuery), cancellationToken);
        await mediator.Send(new BuildCaseInfoCommand(request.Dto, feesQuery), cancellationToken);
        await mediator.Send(new BuildCustomerInfoCommand(request.Dto, feesQuery), cancellationToken);
        await mediator.Send(new BuildFeeClassCommand(request.Dto, feesQuery), cancellationToken);

        return feesQuery;
    }
}