﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class BaseCtrlProcRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    UnitOfWorkManage<PlatformFreeSql> uowManger,
    IMemoryCache memoryCache,
    DefaultRedisCache redisCache,
    CacheExpirationToken<BasCtrlProc> expirationToken)
    : DefaultRepository<BasCtrlProc, string>(freeSql, uowManger), IBaseCtrlProcRepository
{
    IMemoryCache ICacheableRepository<string, BasCtrlProc>.MemoryCache => memoryCache;

    CacheExpirationToken<BasCtrlProc> ICacheableRepository<string, BasCtrlProc>.ExpirationToken => expirationToken;

    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}