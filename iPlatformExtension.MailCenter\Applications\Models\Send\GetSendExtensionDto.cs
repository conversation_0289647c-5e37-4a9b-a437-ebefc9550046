namespace iPlatformExtension.MailCenter.Applications.Models.Send;

/// <summary>
/// 发件详情DTO
/// </summary>
/// <param name="MailId">邮件id</param>
/// <param name="MailNo">发件编号</param>
/// <param name="Status">发件状态</param>
/// <param name="MailFrom">发件人</param>
/// <param name="MailDate">发件时间</param>
/// <param name="AuditUserTemp">审核人ID</param>
/// <param name="UpdateTime">更新时间</param>
/// <param name="UndertakeUserTemp">承办人ID</param>
/// <param name="DiscardTime">作废时间</param>
/// <param name="HostId">邮箱id</param>
/// <param name="CreateTime">创建时间</param>
/// <param name="SendTime">定时发送时间</param>
/// <param name="SendName">发件人名称</param>
public record GetSendExtensionDto(
    string MailId,
    string MailNo,
    int? Status,
    string MailFrom,
    DateTime? MailDate,
    string AuditUserTemp,
    DateTime? UpdateTime,
    string UndertakeUserTemp,
    DateTime? DiscardTime,
    string? HostId,
    DateTime? CreateTime,
    DateTime? SendTime,
    string? SendName
)
{
    /// <summary>
    /// 审核人信息
    /// </summary>
    public object AuditUser { get; set; }

    /// <summary>
    /// 承办人信息
    /// </summary>
    public object UndertakeUser { get; set; }
};
