﻿using System.Linq;
using System.Linq.Expressions;
using System.Runtime.InteropServices.Marshalling;
using System.Text;
using System.Text.Json;
using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Public.Applications.Commands.MyTrademarkCase;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;
using Microsoft.Extensions.ObjectPool;
using Microsoft.IdentityModel.Tokens;

namespace iPlatformExtension.Public.Applications.Handlers.MyTrademarkCase
{
    /// <summary>
    /// 保存公认证
    /// </summary>
    internal sealed class SaveAuthenticationRulesCommandHandler(IHttpContextAccessor httpContextAccessor, IMapper mapper
        , IRecognizedCertificateConfigRepository recognizedCertificateConfigRepository, IBaseCtrlProcRepository baseCtrlProcRepository
        , IBaseCountryRepository countryRepository, ISystemDictionaryRepository dictionaryRepository)
        : IRequestHandler<SaveAuthenticationRulesCommand>
    {
        public async Task Handle(SaveAuthenticationRulesCommand request, CancellationToken cancellationToken)
        {
            var model = request.dto;
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            if (string.IsNullOrWhiteSpace(userId))
            {
                throw new ArgumentNullException($"UserId不能为空");
            }

            if (model.Id is null)
            {
                var certificateConfig = mapper.Map<RecognizedCertificateConfig>(model);
                certificateConfig.Id = Guid.NewGuid().ToString();
                certificateConfig.CreateUserId = userId;
                certificateConfig.CreateTime = DateTime.Now;

                Expression<Func<RecognizedCertificateConfig, bool>> func = config => false;
                var recognizedCertificateConfigs = recognizedCertificateConfigRepository
                    .Where(it => it.CtrlProcId == model.CtrlProcId && it.IsEnable)
                    .WithLock();
                if (model.RecognizedCertificateId != "None")
                {
                    func = JsonSerializer.Deserialize<List<string>>(certificateConfig.CountryIds)!
                        .Aggregate(func, (current, country) => current.Or(x => x.CountryIds.Contains(country)));
                    recognizedCertificateConfigs = recognizedCertificateConfigs.Where(func);
                }
                var anyAsync = await recognizedCertificateConfigs.ToListAsync(it => it.RecognizedCertificateId, cancellationToken);
                if (anyAsync.Count > 0)
                {
                    var ctrlProc = await baseCtrlProcRepository.GetCacheValueAsync(model.CtrlProcId);
                    var countryList = await countryRepository.Where(it => model.CountryIds.Contains(it.CountryId)).ToListAsync(it => it.CountryZhCn, cancellationToken);
                    var cacheValueAsync = await dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.CountryRecognized, anyAsync.FirstOrDefault());
                    throw new ArgumentException($"{ctrlProc?.CtrlProcZhCn}已存在{string.Join(";", countryList)}/{cacheValueAsync.Value}的记录，请调整后再保存");
                }
                await recognizedCertificateConfigRepository.InsertAsync(certificateConfig, cancellationToken);
            }
            else
            {
                var recognizedCertificateConfig = await recognizedCertificateConfigRepository.GetAsync(model.Id, cancellationToken);
                if (recognizedCertificateConfig is null)
                {
                    throw new NullReferenceException("recognizedCertificateConfig id not found");
                }

                var certificateConfig = mapper.Map(model, recognizedCertificateConfig);

                Expression<Func<RecognizedCertificateConfig, bool>> func = config => false;
                var recognizedCertificateConfigs = recognizedCertificateConfigRepository
                    .Where(it =>
                        it.RecognizedCertificateId == certificateConfig.RecognizedCertificateId &&
                        it.CtrlProcId == model.CtrlProcId && it.Id != model.Id)
                    .WithLock();
                if (model.RecognizedCertificateId != "None")
                {
                    func = JsonSerializer.Deserialize<List<string>>(certificateConfig.CountryIds)!
                        .Aggregate(func, (current, country) => current.Or(x => x.CountryIds.Contains(country)));
                    recognizedCertificateConfigs = recognizedCertificateConfigs.Where(func);
                }
                var anyAsync = await recognizedCertificateConfigs.ToListAsync(it => it.RecognizedCertificateId, cancellationToken);
                if (anyAsync.Count > 0)
                {
                    var ctrlProc = await baseCtrlProcRepository.GetCacheValueAsync(model.CtrlProcId);
                    var countryList = await countryRepository.Where(it => model.CountryIds.Contains(it.CountryId)).ToListAsync(it => it.CountryZhCn, cancellationToken);
                    var cacheValueAsync = await dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.CountryRecognized, anyAsync.FirstOrDefault());
                    throw new ArgumentException($"{ctrlProc?.CtrlProcZhCn}已存在{string.Join(";", countryList)}/{cacheValueAsync.Value}的记录，请调整后再保存");
                }
                certificateConfig.UpdateUserId = userId;
                certificateConfig.UpdateTime = DateTime.Now;
                await recognizedCertificateConfigRepository.UpdateAsync(certificateConfig, cancellationToken);
            }
        }
    }
}

