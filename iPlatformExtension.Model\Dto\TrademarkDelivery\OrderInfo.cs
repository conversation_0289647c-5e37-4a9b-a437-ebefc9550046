﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 递交的订单信息
/// </summary>
public sealed class OrderInfo
{
    /// <summary>
    /// 机构联系人
    /// </summary>
    [JsonPropertyName("agentOrganConName")]
    public string AgentOrganConName { get; set; } = default!;

    /// <summary>
    /// 代理机构ID
    /// </summary>
    [JsonPropertyName("agentOrganId")]
    public string AgentOrganId { get; set; } = default!;

    /// <summary>
    /// 机构名称
    /// </summary>
    [JsonPropertyName("agentOrganName")]
    public string AgentOrganName { get; set; } = default!;

    /// <summary>
    /// 代理机构电话
    /// </summary>
    [JsonPropertyName("agentOrganTel")]
    public string AgentOrganTel { get; set; } = default!;

    /// <summary>
    /// 电子邮箱
    /// </summary>
    [JsonPropertyName("contactEmail")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ContactEmail { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    [JsonPropertyName("contactName")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ContactName { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    [JsonPropertyName("contactTel")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ContactTel { get; set; }

    /// <summary>
    /// 申请人国内接收人地址
    /// </summary>
    [JsonPropertyName("domesticReceiverAddress")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? DomesticReceiverAddress { get; set; }

    /// <summary>
    /// 申请人国内接收人地址邮编
    /// </summary>
    [JsonPropertyName("domesticReceiverCode")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? DomesticReceiverCode { get; set; }

    /// <summary>
    /// 申请人联系人
    /// </summary>
    [JsonPropertyName("domesticReceiverName")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? DomesticReceiverName { get; set; }

    /// <summary>
    /// 委托人电话
    /// </summary>
    [JsonPropertyName("principalName")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? PrincipalName { get; set; }

    /// <summary>
    /// 委托人姓名
    /// </summary>
    [JsonPropertyName("principalTel")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? PrincipalTel { get; set; }
}