﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DisplayJsonResultQueryHandler(IFreeSql freeSql) : IRequestHandler<DisplayJsonResultQuery, string>
{
    public Task<string> Handle(DisplayJsonResultQuery request, CancellationToken cancellationToken)
    {
        return freeSql.Select<DeliveryDisplayJson>(request.ResultId)
            .ToOneAsync(json => json.DisplayJson, cancellationToken);
    }
}