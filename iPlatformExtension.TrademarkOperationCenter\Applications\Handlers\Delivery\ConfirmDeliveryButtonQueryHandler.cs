﻿using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class ConfirmDeliveryButtonQueryHandler : IRequestHandler<ConfirmDeliveryButtonQuery, bool>
{
    public Task<bool> Handle(ConfirmDeliveryButtonQuery request, CancellationToken cancellationToken)
    {
        var status = (DeliveryStatus) (request.DeliveryInfo.Status ?? 0);
        var result = status != DeliveryStatus.Confirmed;
        return Task.FromResult(result);
    }
}