namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 年费类型
/// 不传给null值
/// 0：年费
/// 1：非年费（含授权费）
/// 2：授权费
/// 3：非年费（不含授权费）
/// </summary>
public enum AnnualFeeType : byte
{

    /// <summary>
    /// 年费
    /// </summary>
    AnnualFee,
    
    /// <summary>
    /// 非年费（含授权费）
    /// </summary>
    NotAnnualButLicense,
    
    /// <summary>
    /// 授权费
    /// </summary>
    LicenseFee,
    
    /// <summary>
    /// 非年费（不含授权费）
    /// </summary>
    NotAnnualNotLicense
}