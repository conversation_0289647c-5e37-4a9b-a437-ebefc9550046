﻿using System.Diagnostics;
using Grpc.Core;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.GrpcServices;
using iPlatformExtension.Mail.Applications.Commands;
using iPlatformExtension.Messages;
using iPlatformExtension.Messages.Mails;
using MediatR;

namespace iPlatformExtension.Mail.GrpcServices;

/// <summary>
/// 通知远程服务
/// </summary>
/// <param name="mediator">中介者</param>
/// <param name="logger">日志</param>
public sealed class NotificationService(IMediator mediator, ILogger<NotificationService> logger) : Notification.NotificationBase
{
    /// <summary>
    /// 邮件通知
    /// </summary>
    /// <param name="requestStream">邮件请求流</param>
    /// <param name="responseStream">结果响应流</param>
    /// <param name="context">当前请求上下文</param>
    public override async Task NotifyAsync(
        IAsyncStreamReader<NotificationMail> requestStream, IServerStreamWriter<MessageResult> responseStream, ServerCallContext context)
    {
        await foreach (var message in requestStream.ReadAllAsync())
        {
            try
            {
                await mediator.Send(new SendNotificationMailCommand(message), context.CancellationToken);
                await responseStream.WriteAsync(new MessageResult()
                {
                    ResultCode = ResultCode.Success,
                    Success = true,
                    TraceId = Activity.Current?.TraceId.ToString(),
                    Message = "操作成功",
                    Data = message.MessageId.Pack()
                });
            }
            catch (Exception e)
            {
                logger.LogError(e, "发送邮件失败");
                await responseStream.WriteAsync(new MessageResult()
                {
                    ResultCode = ResultCode.Error,
                    Success = false,
                    TraceId = Activity.Current?.TraceId.ToString(),
                    Message = e.Message,
                    Data = message.MessageId.Pack()
                });
            }
        }
    }
}