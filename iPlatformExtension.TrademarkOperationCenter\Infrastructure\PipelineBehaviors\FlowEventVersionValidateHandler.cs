﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.PipelineBehaviors;

internal sealed class FlowEventVersionValidateHandler<TRequest, TResult>(IFreeSql freeSql) : IPipelineBehavior<TRequest, TResult> 
    where TRequest : IFlowEventRequest
{
    public async Task<TResult> Handle(TRequest request, RequestHandlerDelegate<TResult> next, CancellationToken cancellationToken)
    {
        if (request.Checked)
        {
            return await next();
        }
        
        var eventMessage = request.EventMessage;
        var activity = await freeSql.Select<SysFlowActivity>(eventMessage.ActivityId).WithLock().ToOneAsync(
            flowActivity => new SysFlowActivity()
            {
                ActivityId = flowActivity.ActivityId,
                CurNodeId = flowActivity.CurNodeId,
                Version = flowActivity.Version,
                PrevAuditTypeId = flowActivity.PrevAuditTypeId
            }, cancellationToken);
        
        activity.ValidateVersion(eventMessage.Version);

        if (!string.Equals(eventMessage.NodeId, activity.CurNodeId, StringComparison.OrdinalIgnoreCase))
        {
            throw new ApplicationException($"流程节点已变更！当前流程节点为[{eventMessage.NodeId}]， 实际流程节点为[{activity.CurNodeId}]");
        }

        if (!string.Equals(eventMessage.SubmitType, activity.PrevAuditTypeId, StringComparison.OrdinalIgnoreCase))
        {
            throw new ApplicationException(
                $"流程提交类型不匹配！当前提交类型为[{eventMessage.SubmitType}]，实际提交类型为[{activity.PrevAuditTypeId}]");
        }
        
        return await next();
    }
}