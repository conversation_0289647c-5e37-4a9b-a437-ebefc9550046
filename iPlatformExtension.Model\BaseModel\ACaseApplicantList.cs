using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_case_applicant_list", DisableSyncStructure = true)]
	public partial class ACaseApplicantList {

		[ Column(Name = "address_cn", StringLength = 500)]
		public string AddressCn { get; set; }

		[ Column(Name = "address_id", StringLength = 50)]
		public string AddressId { get; set; }

		[ Column(Name = "applicant_id", StringLength = 500)]
		public string ApplicantId { get; set; }

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "classify", StringLength = 50, IsNullable = false)]
		public string Classify { get; set; }

		[ Column(Name = "id", StringLength = 50)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "is_represent")]
		public bool? IsRepresent { get; set; } = false;

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
