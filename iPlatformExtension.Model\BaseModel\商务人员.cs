using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(DisableSyncStructure = true)]
	public partial class 商务人员 {

		[ Column(Name = "cust_id", StringLength = 50)]
		public string CustId { get; set; }

		[ Column(Name = "cust_name", StringLength = 100)]
		public string CustName { get; set; }

		[ Column(Name = "cust_user", StringLength = 50)]
		public string CustUser { get; set; }

		[ Column(Name = "cust_user_id", StringLength = 50)]
		public string CustUserId { get; set; }

	}

}
