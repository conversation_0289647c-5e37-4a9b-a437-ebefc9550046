﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File
{
    public record UploadFileToCaseCammand(IEnumerable<UploadFileToCaseDto> Files, DeliveryFilesOperation Operation) 
        : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;
}
