﻿namespace iPlatformExtension.Public.Applications.Models.DateCalculation
{
    /// <summary>
    /// 日期基础信息
    /// </summary>
    /// <param name="CtrlProcId">任务id</param>
    /// <param name="CountryId">国家编码</param>
    /// <param name="EntrustDate">委案日</param>
    /// <param name="LegalDueDate">官方期限</param>
    /// <param name="ValidDate">有效起始日</param>
    /// <param name="ExtensionPeriod">宽展期限</param>
    /// <param name="ReceiveDate">收文日期</param>
    public class DateCalDto()
    {
        public DateCalDto(string ctrlProcId, string countryId,  DateTime? entrustDate,DateTime? legalDueDate , DateTime? validDate , DateTime? extensionPeriod, DateTime? receiveDate) : this()
        {
            this.ReceiveDate = receiveDate;
            this.ExtensionPeriod = extensionPeriod;
            this.ValidDate = validDate;
            this.LegalDueDate = legalDueDate;
            this.EntrustDate = entrustDate;
            this.CountryId = countryId;
            this.CtrlProcId = ctrlProcId;
        }

        public DateTime? ReceiveDate { get; set; }
        public DateTime? ExtensionPeriod { get; set; }

        public DateTime? ValidDate { get; set; }
        public DateTime? LegalDueDate { get; set; }
        public DateTime? EntrustDate { get; set; }
        public string? CountryId { get; set; }
        public string CtrlProcId { get; set; }

        /// <summary>
        /// 通知书收文日
        /// </summary>
        public DateTime? NotificationReceiptDate { get; set; }
    }
}
