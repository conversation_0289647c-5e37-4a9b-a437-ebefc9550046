﻿using FreeSql;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.MailCenter;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.MailCenterRepository.Interface
{
    public interface IMailFlowPrivateRepository : IBaseRepository<MailFlowPrivate, string>, IScopeDependency;

}
