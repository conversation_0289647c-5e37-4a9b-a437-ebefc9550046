using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_proc_examine", DisableSyncStructure = true)]
	public partial class CaseProcExamine {

		[ Column(Name = "proc_examine_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ProcExamineId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "examine_user_id", StringLength = 50)]
		public string ExamineUserId { get; set; }

		[ Column(Name = "formal_error")]
		public int? FormalError { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "is_submit")]
		public bool? IsSubmit { get; set; } = false;

		[ Column(Name = "proc_flow_id", StringLength = 50)]
		public string ProcFlowId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "substantive_desc", StringLength = 2000)]
		public string SubstantiveDesc { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
