﻿using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;

namespace ConsoleScript;

public class SendEmailTask
{
    
    
    public async Task RunAsync()
    {
        using var smtpClient = new SmtpClient();
        await smtpClient.ConnectAsync("smtp.aciplaw.com", 25, SecureSocketOptions.None);
        await smtpClient.AuthenticateAsync("<EMAIL>", "huajin@3188");
        
        var mailMessage = new MimeMessage();
        mailMessage.From.Add(new MailboxAddress("【正式版本】系统邮箱", "<EMAIL>"));
        mailMessage.To.Add(new MailboxAddress("yanjr", "<EMAIL>"));
        mailMessage.Subject = "内部期限超过7天汇总表-2";

        var memoryStream = new MemoryStream();
        await using var stream = File.OpenRead("ImportDeliveryKeyField.xlsx");
        await stream.CopyToAsync(memoryStream);
        
        var bodyBuilder = new BodyBuilder();
        bodyBuilder.Attachments.Add("草拟吗.xlsx", memoryStream.ToArray());
        bodyBuilder.HtmlBody = """
                               各位老师：您好！<br/>
                               初稿期限（内）、定稿期限（内） 截至今日，超期7天以上（含7天）未完成任务汇总详见附件表格，请质检部和各专业部领导督办。<br/>
                               谢谢！<br/>
                               """;
        mailMessage.Body = bodyBuilder.ToMessageBody();

        await smtpClient.SendAsync(mailMessage);
    }
}