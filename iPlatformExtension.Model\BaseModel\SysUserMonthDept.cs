﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 记录用户每月部门数据
/// </summary>
[Table(Name = "sys_user_month_dept", DisableSyncStructure = true)]
public partial class SysUserMonthDept {

	/// <summary>
	/// 创建时间
	/// </summary>
	[Column(Name = "create_time")]
	public DateTime CreateTime { get; set; }

	/// <summary>
	/// 部门ID
	/// </summary>
	[Column(Name = "dept_id", StringLength = 50)]
	public string DeptId { get; set; } = string.Empty;

	/// <summary>
	/// ID
	/// </summary>
	[Column(Name = "id", StringLength = 50, IsNullable = false, IsPrimary = true)]
	public string Id { get; set; } = Guid.CreateVersion7().ToString();

	/// <summary>
	/// 月份
	/// </summary>
	[Column(Name = "month", StringLength = 5)]
	public string Month { get; set; } = string.Empty;

	/// <summary>
	/// 用户ID
	/// </summary>
	[Column(Name = "user_id", StringLength = 50, IsNullable = false)]
	public string UserId { get; set; } = string.Empty;

	/// <summary>
	/// 年份
	/// </summary>
	[Column(Name = "year", StringLength = 5)]
	public string Year { get; set; } = string.Empty;

	/// <summary>
	/// 地区id
	/// </summary>
	[Column(Name = "district_id", StringLength = 50)]
	public string DistrictId { get; set; } = string.Empty;

}