﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class InsertProcCitedTrademarkCommandHandler(
    IMapper mapper,
    IHttpContextAccessor httpContextAccessor,
    ICitedTrademarkRepository citedTrademarkRepository) : IRequestHandler<InsertProcCitedTrademarkCommand>
{
    public Task Handle(InsertProcCitedTrademarkCommand request, CancellationToken cancellationToken)
    {
        var citedTrademark = mapper.Map<ProcCitedTrademark>(request.Dto);
        var userId = (httpContextAccessor.HttpContext?.User).GetUserId();

        citedTrademark.Creator = userId;
        citedTrademark.CreationTime = DateTime.Now;
        citedTrademark.Updater = userId;
        citedTrademark.UpdateTime = DateTime.Now;

        return citedTrademarkRepository.InsertAsync(citedTrademark, cancellationToken);
    }
}