﻿using System.Linq.Expressions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models.DateCalculation;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.DateCalculation;
using iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.Public.Applications.Handlers.DateCalculation
{
    /// <summary>
    /// 指示外所期限计算
    /// </summary>
    internal sealed class DurationOutpostCalculationQueryHandler(IFreeSql freeSql, IMediator mediator)
        : IRequestHandler<DurationOutpostCalculationQuery, CalDateDto>
    {
        public async Task<CalDateDto> Handle(DurationOutpostCalculationQuery request, CancellationToken cancellationToken)
        {
            var dateCal = request.DateCalDto;
            var recognizedCertificateConfig = await freeSql.Select<RecognizedCertificateConfig, SysDictionary>().WithLock()
                .LeftJoin(it => it.t1.RecognizedCertificateId == it.t2.Value && it.t2.DictionaryName == "country_recognized")
                .Where(it => it.t1.IsEnable && it.t1.CtrlProcId == dateCal.CtrlProcId && it.t1.CountryIds.Contains($"\""+request.DateCalDto.CountryId+"\""))
                .ToListAsync(it => new { it.t1, it.t2.Value }, cancellationToken);
            if (recognizedCertificateConfig.Count > 1)
            {
                throw new Exception("查找到有多个任务配置");
            }

            if (recognizedCertificateConfig.Count != 0)
            {
                var recognizedConfig = recognizedCertificateConfig.FirstOrDefault();
                //公认证配置计算
                return _configDictionary.TryGetValue(recognizedConfig.Value, out var days)
                    ? await mediator.Send(new DateCalculationQuery(dateCal.EntrustDate!.Value, Day: days),
                        cancellationToken)
                    : throw new Exception("没有找到配置");
            }

            if (dateCal.LegalDueDate.HasValue)
            {
                var calibrationDate = (dateCal.LegalDueDate.Value - DateTime.Today).Days;
                var dateSettings = _dateSettings.ExpressionCompile(calibrationDate);
                if (dateSettings.Count() > 1 || !dateSettings.Any())
                {
                    throw new Exception($"找到{dateSettings.Count()}条范围规则");
                }

                var dateSetting = dateSettings.FirstOrDefault();
                return await mediator.Send(new DateCalculationQuery(dateCal.LegalDueDate.Value, dateSetting.day), cancellationToken);

            }
            //其中：起始办理时间 = 有效起始日；

            //1、委案日超过起始办理时间的：指示外所期限 = 委案日 + 20个工作日；
            //2、委案日未超过起始办理时间的： 指示外所期限 = 起始办理时间 + 20个工作日；
            //3、委案日在宽展期内的 指示外所期限 = 宽展期限 - 5个工作日；
            //4、上述三点均不命中时，按兜底规则[指示外所期限 = 委案日 + 4个工作日];
            if (dateCal is { EntrustDate: not null, ValidDate: not null } && dateCal.EntrustDate.Value > dateCal.ValidDate)
            {
                return await mediator.Send(new DateCalculationQuery(dateCal.EntrustDate.Value, 20), cancellationToken);
            }
            if (dateCal is { EntrustDate: not null, ValidDate: not null } && dateCal.EntrustDate.Value <= dateCal.ValidDate)
            {
                return await mediator.Send(new DateCalculationQuery(dateCal.ValidDate.Value, 20), cancellationToken);
            }
            if (dateCal is { EntrustDate: not null, ExtensionPeriod: not null } && dateCal.EntrustDate.Value <= dateCal.ExtensionPeriod)
            {
                return await mediator.Send(new DateCalculationQuery(dateCal.ExtensionPeriod.Value, -5), cancellationToken);
            }
            return await mediator.Send(new DateCalculationQuery(dateCal.EntrustDate.Value, 4), cancellationToken);
        }

        private readonly List<DateSetting> _dateSettings =
            [new DateSetting { day = 0, MaxValue = 5 },
                new DateSetting { day = -2, MinValue = 5, MaxValue = 10 },
                new DateSetting { day = -5, MinValue = 10, MaxValue = 20 },
                new DateSetting { day = -10, MinValue = 20, MaxValue = 30 },
                new DateSetting { day = -20, MinValue = 30 }];

        /// <summary>
        /// 公认证日期配置
        /// </summary>
        private readonly Dictionary<string, int> _configDictionary = new()
        {
            {CountryRecognized.None.ToString(),4},
            {CountryRecognized.NotaryCountry.ToString(),10},
            {CountryRecognized.RecognizedCountry.ToString(),20}
        };
    }
}

