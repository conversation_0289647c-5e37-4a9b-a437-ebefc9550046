﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.Commission.Application.Models.WinningReward;

/// <summary>
/// 胜诉奖励规则更新DTO
/// </summary>
public sealed class RulePatchDto
{
    /// <summary>
    /// 裁定结果
    /// </summary>
    [Display(Name = "裁定结果")]
    public string RulingResult { get; set; } = string.Empty;

    /// <summary>
    /// 是否情势变更
    /// </summary>
    public bool SituationChanged { get; set; }

    /// <summary>
    /// 计提日期类型
    /// </summary>
    [Display(Name = "计提日期类型")]
    public string DateType { get; set; } = string.Empty;

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 撰写人奖励
    /// </summary>
    [Range(0, double.MaxValue)]
    public decimal WriterReward { get; set; }

    /// <summary>
    /// 导师奖励
    /// </summary>
    [Range(0, double.MaxValue)]
    public decimal MentorReward { get; set; }
}