﻿namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 申请人书式类型
/// </summary>
public sealed class ApplicantBookType
{
    /// <summary>
    /// 中国大陆
    /// </summary>
    public static readonly ApplicantBookType ChinaMainland = new(1, "中国大陆", "CN");

    /// <summary>
    /// 海外
    /// </summary>
    public static readonly ApplicantBookType Overseas = new(2, "海外", string.Empty);

    /// <summary>
    /// 海外
    /// </summary>
    public static readonly ApplicantBookType Taiwan = new(3, "中国台湾", "TW");

    /// <summary>
    /// 香港
    /// </summary>
    public static readonly ApplicantBookType HongKong = new(4, "中国香港", "HK");

    /// <summary>
    /// 澳门
    /// </summary>
    public static readonly ApplicantBookType Macau = new(5, "中国澳门", "MO");

    private static readonly IReadOnlyDictionary<int, ApplicantBookType> applicantBookTypes;

    private static readonly IReadOnlyDictionary<string, ApplicantBookType> countryApplicantBookTypes;

    static ApplicantBookType()
    {
        applicantBookTypes = new Dictionary<int, ApplicantBookType>()
        {
            {1, ChinaMainland},
            {2, Overseas},
            {3, Taiwan},
            {4, HongKong},
            {5, Macau}
        };

        countryApplicantBookTypes = new Dictionary<string, ApplicantBookType>()
        {
            {"CN", ChinaMainland},
            {string.Empty, Overseas},
            {"HK", HongKong},
            {"TW", Taiwan},
            {"MO", Macau},
        };
    }

    /// <summary>
    /// 尝试通过编码值获取申请人书类型
    /// </summary>
    /// <param name="code">编码值</param>
    /// <param name="applicantBookType">申请人书类型</param>
    /// <returns>存在返回true，不存在返回false</returns>
    public static bool TryGetApplicantBookType(int code, out ApplicantBookType? applicantBookType)
    {
        return applicantBookTypes.TryGetValue(code, out applicantBookType);
    }
    
    /// <summary>
    /// 尝试通过编码值获取申请人书类型
    /// </summary>
    /// <param name="countryCode">国家编码值</param>
    /// <returns>申请人书类型</returns>
    public static ApplicantBookType GetApplicantBookType(string countryCode)
    {
        return countryApplicantBookTypes.TryGetValue(countryCode, out var applicantBookType)
            ? applicantBookType
            : Overseas;
    }

    /// <summary>
    /// 通过编码值获取申请人书类型
    /// </summary>
    /// <param name="code">编码值</param>
    /// <returns>申请人书类型</returns>
    /// <exception cref="ArgumentOutOfRangeException">编码值不在给定的枚举范围内</exception>
    public static ApplicantBookType GetApplicantBookType(int code)
    {
        if (TryGetApplicantBookType(code, out var applicantBookType) && applicantBookType is not null)
            return applicantBookType;

        throw new ArgumentOutOfRangeException(nameof(code), $"给定的编码值{code}超出枚举范围");
    }

    private ApplicantBookType(int code, string description, string countryCode)
    {
        Description = description;
        CountryCode = countryCode;
        Code = code;
    }
    
    /// <summary>
    /// 编码值
    /// </summary>
    public int Code { get; }
    
    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; }
    
    /// <summary>
    /// 业务系统国家编码
    /// </summary>
    public string CountryCode { get; }
}