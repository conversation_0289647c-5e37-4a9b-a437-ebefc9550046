﻿using FreeSql;
using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Commission.Application.Notifications.WinningReward;
using iPlatformExtension.Commission.Application.Queries.WinningReward;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class WinningRewardRulesQueryHandler(IFreeSql freeSql, IPublisher publisher) 
    : IRequestHandler<WinningRewardRulesQuery, IEnumerable<RuleDisplayDto>>
{
    public async Task<IEnumerable<RuleDisplayDto>> Handle(WinningRewardRulesQuery request, CancellationToken cancellationToken)
    {
        var ctrlProcIds = request.CtrlProcIds;
        var result = await freeSql.Select<WinningRewardRule>().WithLock()
            .WhereIf(ctrlProcIds.Any(), rule => ctrlProcIds.Contains(rule.CtrlProcId))
            .ToListAsync(rule => new RuleDisplayDto
            {
                RuleId = rule.Id,
                ProcName = rule.CtrlProcId,
                CaseDirection = rule.CaseDirection,
                RulingResult = rule.RulingResult,
                SituationChanged = SqlExt.Case().When(rule.SituationChanged == true, "是")
                    .When(rule.SituationChanged == false, "否").Else(string.Empty).End(),
                DateType = rule.DateType,
                WriterReward = rule.WriterReward,
                MentorReward = rule.MentorReward,
                IsEnabled = rule.IsEnabled == true ? "是" : "否",
                UpdateTime = rule.UpdateTime
            }, cancellationToken);

        await publisher.Publish(new RewardRuleDisplayResultNotification(result), cancellationToken);
        
        return result;
    }
}