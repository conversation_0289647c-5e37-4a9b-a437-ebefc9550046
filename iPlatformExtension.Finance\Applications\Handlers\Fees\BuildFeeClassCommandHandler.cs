using System.Linq.Expressions;
using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class BuildFeeClassCommandHandler(IMediator mediator, IFreeSql freeSql)
    : IRequestHandler<BuildFeeClassCommand>, IFeesQueryStatementBuilder
{
    private const string FeeAlias = nameof(CaseFeeList);
    
    private readonly IMediator _mediator = mediator;

    private static readonly IEnumerable<string> notAgentFeeClass = new[] {FeeClass.Official, FeeClass.Third};

    public Task Handle(BuildFeeClassCommand request, CancellationToken cancellationToken)
    {
        var feesQuery = request.FeesQuery;
        
        if (request.Dto.AgentFees)
        {
            Expression<Func<CaseFeeList, bool>> officialWithAgentFeesCondition = feeList =>
                notAgentFeeClass.Contains(feeList.FeeClass) || feeList.FeeClass == FeeClass.Agent && freeSql.Select<CaseFeeList>()
                    .Any(list =>
                        list.FeeTypeNameId == feeList.FeeTypeNameId && list.FeeClass == FeeClass.Official &&
                        list.ProcId == feeList.ProcId && list.FeeId != feeList.FeeId);

            feesQuery.Where(officialWithAgentFeesCondition);
        }
        
        BuildFeesQueryStatement(request.Dto, feesQuery);
        
        return Task.CompletedTask;

        // var feesQuery = _freeSql.Select<CaseFeeList>().As(nameof(CaseFeeList)).WithLock();
        //
        // await _mediator.Send(new BuildCaseFeeCommand(request.Dto, feesQuery), cancellationToken);
        // await _mediator.Send(new BuildFeeTypesCommand(request.Dto, feesQuery), cancellationToken);
        // await _mediator.Send(new BuildProcInfoCommand(request.Dto, feesQuery), cancellationToken);
        // await _mediator.Send(new BuildApplicantInfoCommand(request.Dto, feesQuery), cancellationToken);
        // await _mediator.Send(new BuildCaseInfoCommand(request.Dto, feesQuery), cancellationToken);
        //
        // var officialFeeIds = await feesQuery
        //     .Where(list => _freeSql.Select<CaseFeeList>().Any(fees =>
        //         list.FeeClass == FeeClass.Agent && fees.FeeTypeNameId == list.FeeTypeNameId &&
        //         fees.FeeClass == FeeClass.Official)).ToListAsync(list => list.FeeId, cancellationToken);
        //
        // BuildFeesQueryStatement(request.Dto, request.FeesQuery, officialFeeIds);
    }

    public static void BuildFeesQueryStatement(FeeQueryDto dto, ISelect<CaseFeeList> feesQuery, params object?[] args)
    {
        feesQuery.WhereDynamicFilter(
            dto.FeeClasses.BuildContainsDynamicFilter(nameof(CaseFeeList.FeeClass), FeeAlias));
    }
}