﻿using System.Security.Claims;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Infrastructure.Constants;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Outsourcing.Infrastructure.Authorization.SupplierAssignment;

internal sealed class SupplierAssignmentAuthorizationContextFactory(
    IFreeSql<PlatformFreeSql> freeSql, 
    IOptionsMonitor<SupplierAssignmentAuthorizationOptions> optionsMonitor)
{
    private static readonly IEnumerable<string> excludeProcStatusIds = ["TYCL", "TBCL"];
    
    private IEnumerable<string>? _excludeBelongCompanies;

    private readonly Dictionary<SupplierAssignmentAuthorizationContextKey, Lazy<Task<SupplierAssignmentAuthorizationContextBuilder>>>
        _contextBuilderLazies = new(3);
    
    public async Task<SupplierAssignmentAuthorizationContext> CreateContextAsync(
        string authorizationType, ClaimsPrincipal user, CancellationToken cancellationToken = default)
    {
        var userId = user.GetUserId();
        var key = new SupplierAssignmentAuthorizationContextKey(authorizationType, userId);
        
        var options = optionsMonitor.Get(authorizationType);
        var (authorizationObjectType, 
            caseTypes, 
            caseDirections, 
            personalQueryFilterEntry, 
            wholeQueryFilterEntry) = options;

        if (!_contextBuilderLazies.TryGetValue(key, out var contextBuilderLazy))
        {
            contextBuilderLazy = new Lazy<Task<SupplierAssignmentAuthorizationContextBuilder>>(async () =>
            {
        
                _excludeBelongCompanies ??= await freeSql.Select<BasCompany>().WithLock()
                    .Where(company => company.DistrictId == "NJ" || company.DistrictId == "ZJ")
                    .ToListAsync(company => company.CompanyId, cancellationToken);
                
                var filters = await freeSql.Select<SysAuthFilter>().WithLock()
                    .InnerJoin<SysAuth>((filter, auth) => filter.AuthId == auth.AuthId)
                    .Where<SysAuth>(auth => auth.IsEnabled == true)
                    .WhereIf<SysAuthFilter, SysAuth>(authorizationObjectType == AuthorizationObjectType.User, 
                        (filter, auth) => auth.AuthUser == userId)
                    .WhereIf<SysAuthFilter, SysAuth>(authorizationObjectType == AuthorizationObjectType.Role, 
                        (filter, auth) => user.GetRoleIds().Contains(auth.AuthUser))
                    .Where<SysAuth>(auth => auth.AuthType == authorizationObjectType)
                    .Where<SysAuth>(auth => auth.FormId == authorizationType)
                    .OrderBy(filter => filter.Seq)
                    .ToListAsync(filter => new SysAuthFilter
                    {
                        AuthFilterId = filter.AuthFilterId,
                        FilterId = filter.FilterId,
                        FilterType = filter.FilterType,
                        FilterValue = filter.FilterValue,
                    }, cancellationToken);
                
                var ctrlProcIds = await freeSql.Select<BasCtrlProc>().WithLock()
                    .InnerJoin<BasCtrlProcDirection>((proc, direction) =>
                        proc.CtrlProcId == direction.CtrlProcId)
                    .Where<BasCtrlProcDirection>(direction =>
                        caseDirections.Contains(direction.CaseDirection))
                    .Where(proc => caseTypes.Contains(proc.CaseTypeId))
                    .Where(proc => proc.IsEnabled == true)
                    .Where(proc => proc.IsOutsourcing == true)
                    .Distinct()
                    .ToListAsync(proc => proc.CtrlProcId, cancellationToken);
    
                return new SupplierAssignmentAuthorizationContextBuilder().AddCtrlProcIds(ctrlProcIds)
                    .AddFilters(filters).AddCaseDirections(caseDirections).AddCaseTypes(caseTypes)
                    .AddExcludeBelongCompanies(_excludeBelongCompanies).AddExcludeProcStatusIds(excludeProcStatusIds);
                
            });
            
            _contextBuilderLazies[key] = contextBuilderLazy;
        }
        
        var builder = await contextBuilderLazy.Value;
        
        var context = builder.Build(freeSql);
        context.SetPersonalQuery(personalQueryFilterEntry);
        context.SetWholeQuery(wholeQueryFilterEntry);
            
        return context;
    }
}