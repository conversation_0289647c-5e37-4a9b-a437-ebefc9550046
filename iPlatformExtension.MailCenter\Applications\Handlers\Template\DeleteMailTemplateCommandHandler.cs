using iPlatformExtension.MailCenter.Applications.Commands.Template;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Template
{
    /// <summary>
    /// 删除邮件模板命令处理程序
    /// </summary>
    internal sealed class DeleteMailTemplateCommandHandler(
        IMailTemplateRepository mailTemplateRepository
    ) : IRequestHandler<DeleteMailTemplateCommand>
    {
        public async Task Handle(DeleteMailTemplateCommand request, CancellationToken cancellationToken)
        {
            var template = await mailTemplateRepository
                .Where(t => t.TemplateId == request.TemplateId)
                .FirstAsync(cancellationToken)
                ?? throw new ApplicationException($"未找到ID为 {request.TemplateId} 的模板");

            await mailTemplateRepository.DeleteAsync(template, cancellationToken);
        }
    }
}
