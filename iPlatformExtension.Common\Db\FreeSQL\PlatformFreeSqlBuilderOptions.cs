using System.Collections.Concurrent;
using FreeSql;
using FreeSql.Internal.Model.Interface;
using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Db.FreeSQL;

public class PlatformFreeSqlBuilderOptions<T>
{
    public ServiceLifetime Lifetime { get; set; }

    public required string ConnectionString { get; set; }

    public string[] SlaveConnectionStrings { get; set; } = [];

    public DataType DbType { get; set; }

    public bool LazyLoading { get; set; } = true;

    public bool CommandParameterWithLambda { get; set; }

    public TimeSpan CommandTimeout { get; set; }

    public bool UseAdoConnectionPool { get; set; } 

    public ConcurrentDictionary<Type, ITypeHandler> TypeHandlers => FreeSql.Internal.Utils.TypeHandlers;

    public Action<IFreeSql, IServiceProvider> GlobalConfigure { get; set; } = (_, _) => { };
}