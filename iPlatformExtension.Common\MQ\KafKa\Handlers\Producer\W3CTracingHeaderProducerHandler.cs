﻿using System.Diagnostics;
using System.Text;
using Confluent.Kafka;
using iPlatformExtension.Common.Extensions;
using Microsoft.Net.Http.Headers;

namespace iPlatformExtension.Common.MQ.KafKa.Handlers.Producer;

public class W3CTracingHeaderProducerHandler<TKey, TValue> : DelegatingProducer<TKey, TValue>
{
    public override Task<DeliveryResult<TKey, TValue>> ProduceAsync(string topic, Message<TKey, TValue> message, CancellationToken cancellationToken = new CancellationToken())
    {
        AddTracingHeader(message.Headers);
        return base.ProduceAsync(topic, message, cancellationToken);
    }

    public override Task<DeliveryResult<TKey, TValue>> ProduceAsync(TopicPartition topicPartition, Message<TKey, TValue> message,
        CancellationToken cancellationToken = new CancellationToken())
    {
        AddTracingHeader(message.Headers);
        return base.ProduceAsync(topicPartition, message, cancellationToken);
    }

    public override void Produce(TopicPartition topicPartition, Message<TKey, TValue> message, Action<DeliveryReport<TKey, TValue>>? deliveryHandler = null)
    {
        AddTracingHeader(message.Headers);
        base.Produce(topicPartition, message, deliveryHandler);
    }

    public override void Produce(string topic, Message<TKey, TValue> message, Action<DeliveryReport<TKey, TValue>>? deliveryHandler = null)
    {
        AddTracingHeader(message.Headers);
        base.Produce(topic, message, deliveryHandler);
    }

    private static void AddTracingHeader(Headers headers)
    {
        var activity = Activity.Current;
        headers.Add(HeaderNames.TraceParent, activity?.Id?.GetBytes(Encoding.UTF8) ?? []);
    }
}