﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Enum;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement
{
    internal sealed class SystemRoleInfoRepository(
        IFreeSql<PlatformFreeSql> freeSql,
        IMemoryCache memoryCache,
        CacheExpirationToken<SysRoleInfo> expirationToken,
        DefaultRedisCache redisCache)
        : BaseRepository<SysRoleInfo, string>(freeSql),
            ISystemRoleInfoRepository
    {

        public IMemoryCache MemoryCache { get; } = memoryCache;

        public CacheExpirationToken<SysRoleInfo> ExpirationToken { get; } = expirationToken;

        public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;

        public string GetRole(SysEnum.RoleCode roleCode, SysEnum.RoleType roleType)
        {
            //缓存获取员工操作
            MemoryCache.TryGetValue(nameof(roleType) + roleCode.GetDescription(), out string? roleId);
            if (roleId is null)
            {
                roleId = Orm.Select<SysRoleInfo>().Where(it => it.RoleCode == roleCode.GetDescription()
                                                               && it.RoleType == roleType.GetHashCode()).ToOne(it => it.RoleId);
                MemoryCache.Set(nameof(roleType) + roleCode.GetDescription(), roleId);
            }

            return roleId;
        }
    }
}
