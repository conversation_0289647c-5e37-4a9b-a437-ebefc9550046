using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_copy_columns_config", DisableSyncStructure = true)]
	public partial class CaseCopyColumnsConfig {

		[ Column(Name = "config_id", StringLength = 50, IsNullable = false)]
		public string ConfigId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "config_title", StringLength = 500)]
		public string ConfigTitle { get; set; }

		[ Column(Name = "copy_type", StringLength = 50)]
		public string CopyType { get; set; }

		[ Column(Name = "copy_value", StringLength = 50)]
		public string CopyValue { get; set; }

		[ Column(Name = "is_default")]
		public bool IsDefault { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
