using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Send;
using iPlatformExtension.MailCenter.Applications.Queries.Send;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send
{
    /// <summary>
    /// 获取发件办理信息处理者
    /// </summary>
    internal sealed class GetSendExtensionQueryHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IUserInfoRepository userInfoRepository
    ) : IRequestHandler<GetSendExtensionQuery, GetSendExtensionDto>
    {
        public async Task<GetSendExtensionDto> Handle(
            GetSendExtensionQuery request,
            CancellationToken cancellationToken
        )
        {
            var getSendExtensionDto = await freeSql
                .Select<MailSend, MailSendFlow>()
                .WithLock()
                .LeftJoin(it => it.t1.MailId == it.t2.MailId)
                .Where(it => it.t1.MailId == request.MailId)
                .FirstAsync(
                    it => new GetSendExtensionDto(
                        it.t1.MailId,
                        it.t1.MailNo,
                        it.t1.Status,
                        it.t1.MailFrom,
                        it.t1.MailDate,
                        it.t2.AuditUser,
                        it.t2.UpdateTime,
                        it.t2.UndertakeUserId,
                        it.t2.DiscardTime,
                        it.t1.HostId,
                        it.t2.CreateTime,
                        it.t1.SendTime,
                        it.t2.DisplayName
                    ),
                    cancellationToken
                );

            // 获取审核人信息
            if (getSendExtensionDto.AuditUserTemp is not null)
            {
                ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                    userInfoRepository;
                getSendExtensionDto.AuditUser = new
                {
                    CnName = (
                        await userBaseInfoRepository.GetCacheValueAsync(
                            getSendExtensionDto.AuditUserTemp, cancellationToken: cancellationToken)
                    )?.CnName ?? "",
                    UserId = getSendExtensionDto.AuditUserTemp,
                };
            }

            // 获取承办人信息
            if (getSendExtensionDto.UndertakeUserTemp is not null)
            {
                ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                    userInfoRepository;
                getSendExtensionDto.UndertakeUser = new
                {
                    CnName = (
                        await userBaseInfoRepository.GetCacheValueAsync(
                            getSendExtensionDto.UndertakeUserTemp, cancellationToken: cancellationToken)
                    )?.CnName ?? "",
                    UserId = getSendExtensionDto.UndertakeUserTemp,
                };
            }

            return getSendExtensionDto;
        }
    }
}
