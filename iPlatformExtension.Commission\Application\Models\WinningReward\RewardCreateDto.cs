﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.Commission.Application.Models.WinningReward;

/// <summary>
/// 胜诉奖励创建DTO
/// </summary>
public class RewardCreateDto
{
    /// <summary>
    /// 生效日
    /// </summary>
    [Required]
    public DateTime CommissionDate { get; set; }

    /// <summary>
    /// 撰写人奖励
    /// </summary>
    [Required]
    [Range(0, double.MaxValue)]
    public decimal WriterReward { get; set; }

    /// <summary>
    /// 导师奖励
    /// </summary>
    [Required]
    [Range(0, double.MaxValue)]
    public decimal MentorReward { get; set; }
}