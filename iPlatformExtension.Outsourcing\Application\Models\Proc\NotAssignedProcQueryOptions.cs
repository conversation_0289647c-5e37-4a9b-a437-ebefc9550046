﻿using System.Collections.Immutable;
using FreeSql;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Outsourcing.Application.Models.Proc;

public class NotAssignedProcQueryOptions
{
    public Func<ISelect<CaseProcInfo>, ISelect<CaseProcInfo>> BuildDefaultQuery { get; set; } = select => select;
    
    public Func<IFreeSql, CancellationToken, Task<ISet<string>>> GetDefaultCtrlProcIdsAsync { get; set; } = (_, _) => Task.FromResult<ISet<string>>(new HashSet<string>());

    /// <summary>
    /// 
    /// </summary>
    public IEnumerable<string> SpecialCtrlProcIds { get; set; } = [];
}