using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "hr_scheduling", DisableSyncStructure = true)]
	public partial class HrScheduling {

		[ Column(Name = "id", IsPrimary = true, IsIdentity = true)]
		public int Id { get; set; }

		[ Column(Name = "calendar_date", DbType = "date")]
		public DateTime? CalendarDate { get; set; }

		[ Column(Name = "cn_name", StringLength = 50)]
		public string CnName { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 100)]
		public string CreateUserId { get; set; }

		[ Column(Name = "district_name", StringLength = 50)]
		public string DistrictName { get; set; }

		[ Column(Name = "itinerary", StringLength = 50)]
		public string Itinerary { get; set; }

		[ Column(Name = "position_name", StringLength = 50)]
		public string PositionName { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 100)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
