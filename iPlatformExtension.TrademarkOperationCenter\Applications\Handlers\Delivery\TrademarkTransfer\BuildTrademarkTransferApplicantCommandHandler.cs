﻿using AutoMapper;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkTransfer;

internal sealed class BuildTrademarkTransferApplicant<PERSON>ommandHandler(IMapper mapper) : 
    BuildProcApplicantCommandHandler(mapper)
{

    public override string CtrlProcId => CtrlProcIds.TrademarkTransfer;

    public override IEnumerable<string> CaseDirections => [CaseDirection.II];
}