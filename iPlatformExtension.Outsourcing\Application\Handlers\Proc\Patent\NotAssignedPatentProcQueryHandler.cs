﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc.Patent;

internal sealed class NotAssignedPatentProcQueryHandler(
    IPublisher publisher,
    IFreeSql<PlatformFreeSql> freeSql,
    NotAssignedProcQueryContextFactory contextFactory) : IRequestHandler<NotAssignedPatentProcQuery, IEnumerable<NotAssignedPatentProcDto>>
{
    public async Task<IEnumerable<NotAssignedPatentProcDto>> Handle(NotAssignedPatentProcQuery request, CancellationToken cancellationToken)
    {
        var authorizationType = request.AuthorizationType;
        var keyword = request.Keyword;
        var (field, order) = request.SortCondition;

        var context = await contextFactory.CreateAsync(authorizationType, cancellationToken);

        if (context is null)
        {
            return [];
        }
        
        if (!string.IsNullOrWhiteSpace(keyword))
        {
            var caseIdSet = context.CaseIds;
            caseIdSet.UnionWith(await freeSql.Select<CaseInfo>().WithLock()
                .Where(info => info.Volume.StartsWith(keyword))
                .ToListAsync(info => info.Id, cancellationToken));
            caseIdSet.UnionWith(await freeSql.Select<CaseInfo>().WithLock()
                .Where(info => info.CaseName.StartsWith(keyword))
                .ToListAsync(info => info.Id, cancellationToken));
            caseIdSet.UnionWith(await freeSql.Select<CaseExtendInfo>().WithLock()
                .Where(info => info.PctAppNo!.StartsWith(keyword))
                .ToListAsync(info => info.CaseId, cancellationToken));
            caseIdSet.UnionWith(await freeSql.Select<CaseInfo>().WithLock()
                .Where(info => info.Customer.CustomerFullName.Contains(keyword))
                .ToListAsync(info => info.Id, cancellationToken));
            caseIdSet.UnionWith(await freeSql.Select<CaseInfo>().WithLock()
                .Where(info => info.Country.CountryZhCn.Contains(keyword))
                .ToListAsync(info => info.Id, cancellationToken));

            if (caseIdSet.Count == 0)
            {
                var ctrlProcIdSet = context.CtrlProcIds;
                var ctrlProcIds = await freeSql.Select<BasCtrlProc>()
                    .Where(basCtrlProc => basCtrlProc.CtrlProcZhCn.Contains(keyword!))
                    .ToListAsync(basCtrlProc => basCtrlProc.CtrlProcId, cancellationToken);
                ctrlProcIdSet.IntersectWith(ctrlProcIds);
            }
        }
        
        await publisher.Publish(context, cancellationToken);
        
        var totalCountTask = context.CountQueryBuilder.Build().CountAsync(cancellationToken);
        
        var results = await context.Query
            .OrderByPropertyNameIf(!string.IsNullOrWhiteSpace(field), field, order == SortOrder.Ascending)
            .OrderBy(info => info.ProcId)
            .Skip(((request.PageIndex ?? 1) - 1) * (request.PageSize ?? 10)).Take(request.PageSize ?? 10)
            .ToListAsync(info => new NotAssignedPatentProcDto()
            {
                ProcId = info.ProcId,
                Volume = info.CaseInfo.Volume,
                CaseName = info.CaseInfo.CaseName,
                CustomerId = info.CaseInfo.CustomerId,
                CountryId = info.CaseInfo.CountryId ?? string.Empty,
                InternalInitialDraftDeadline = info.IntFirstDate,
                ProcName = info.CtrlProcId,
                ApplyType = info.CaseInfo.ApplyTypeId ?? string.Empty,
                OfficialDeadline = info.LegalDueDate,
                CaseId = info.CaseId,
                Version = info.Version,
                CaseDirection = info.CaseInfo.CaseDirection,
                PctAppNo = info.CaseInfo.ExtendInfo!.PctAppNo ?? string.Empty
            }, cancellationToken);
            
        await publisher.Publish(new NotAssignedPatentProcResultNotification(results), cancellationToken);

        var total = await totalCountTask;
        return new PageResult<NotAssignedPatentProcDto>()
        {
            Data = results,
            Total = total,
            Page = request.PageIndex ?? 1,
            PageSize = request.PageSize ?? 10,
        };
    }
}