﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Queries.Proc;

public sealed record SupplierProcCountQuery(
    [property: Required, Description("客户id")]string CustomerId, 
    [property: Required, Description("任务名称id")]string CtrlProcId, 
    [property: Required, Description("国家编码集合")]IEnumerable<string> Countries) 
    : IRequest<IEnumerable<SupplierTrademarkProcCountDto>>;