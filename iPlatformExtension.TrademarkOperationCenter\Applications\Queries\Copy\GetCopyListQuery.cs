﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Copy;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Copy;

/// <summary>
/// 获取复制列表
/// </summary>
/// <param name="Type">类型(law:法律信息,base:基础信息,ctrl_proc_id:对应任务的任务复制内容列表)</param>
public record GetCopyListQuery([Required(ErrorMessage = "请输入搜索类型")] string[] Type) : IRequest<IEnumerable<GetCopyListDto>>;

