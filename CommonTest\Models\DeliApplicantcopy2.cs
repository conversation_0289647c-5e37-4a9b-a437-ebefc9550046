using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_applicant", DisableSyncStructure = true)]
	public partial class DeliApplicantcopy2
    {

		[ Column(Name = "address_cn", StringLength = 500)]
		public string? AddressCn { get; set; }

		[ Column(Name = "address_detail", StringLength = 200)]
		public string? AddressDetail { get; set; }

		[ Column(Name = "address_en", StringLength = 500)]
		public string? AddressEn { get; set; }


        [Column(Name = "applicant_name_cn", StringLength = 100)]
        public string ApplicantNameCn { get; set; }



    }

}
