﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class CaseNiceCategoryRepository : DefaultRepository<CaseTrademarkNiceCategory, long>, 
    ICaseNiceCategoryRepository
{
    public CaseNiceCategoryRepository(IFreeSql freeSql, UnitOfWorkManager uowManger) : base(freeSql, uowManger)
    {
    }
}