﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Nice;

internal sealed record InsertCaseNiceCommand(
        string VersionId, 
        string CaseId, 
        IEnumerable<StandardCategoryDto> StandardCategories, 
        IEnumerable<CustomCategoryDto> CustomCategories,
        IEnumerable<string> GrandNumbers) 
    : IRequest;