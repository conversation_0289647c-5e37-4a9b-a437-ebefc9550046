﻿using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 删除成员
    /// </summary>
    public class DeleteTeamMemberCommandHandler : IRequestHandler<DeleteTeamMemberCommand>
    {
        private readonly IFreeSql _freeSql;
        private readonly ISysTeamAssociateMemberRepository _sysTeamAssociateMemberRepository;

        public DeleteTeamMemberCommandHandler(IFreeSql freeSql, ISysTeamAssociateMemberRepository sysTeamAssociateMemberRepository)
        {
            _freeSql = freeSql;
            _sysTeamAssociateMemberRepository = sysTeamAssociateMemberRepository;
        }

        public async Task Handle(DeleteTeamMemberCommand request, CancellationToken cancellationToken)
        {
            await _sysTeamAssociateMemberRepository.DeleteAsync(it => request.TeamMemberId.Contains(it.TeamMemberId), cancellationToken);
        }
    }
}

