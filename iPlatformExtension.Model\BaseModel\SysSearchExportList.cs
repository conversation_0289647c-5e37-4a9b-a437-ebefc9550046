using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_search_export_list", DisableSyncStructure = true)]
	public partial class SysSearchExportList {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_id", StringLength = 50)]
		public string ColumnId { get; set; }

		[ Column(Name = "is_default")]
		public bool? IsDefault { get; set; } = false;

		/// <summary>
		/// 是否显示在列表中，目前仅用于查询结果是否显示
		/// </summary>
		[ Column(Name = "is_show")]
		public bool? IsShow { get; set; }

		[ Column(Name = "search_form_code", StringLength = 50)]
		public string SearchFormCode { get; set; }

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

		/// <summary>
		/// 栏位类型  表单构造，列表显示
		/// </summary>
		[ Column(Name = "type", StringLength = 50)]
		public string Type { get; set; }

		[ Column(Name = "user_export_id", StringLength = 50, IsNullable = false)]
		public string UserExportId { get; set; }

	}

}
