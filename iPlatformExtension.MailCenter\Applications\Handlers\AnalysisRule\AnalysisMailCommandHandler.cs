﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Commands.Analysis;
using iPlatformExtension.MailCenter.Applications.Commands.AnalysisRule;
using iPlatformExtension.MailCenter.Applications.Models.Analysis;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AnalysisRule
{
    /// <summary>
    /// 解析邮件
    /// </summary>
    internal sealed class AnalysisMailCommandHandler(
        IMediator mediator,
        IFreeSql<MailCenterFreeSql> freeSql,
        IRedisCache<RedisCacheOptionsBase> redisCache,
        IMemoryCache cache
    ) : IRequestHandler<AnalysisMailCommand>
    {
        private const string MailAnalysisCacheKey = "MailAnalysis";

        public async Task Handle(AnalysisMailCommand request, CancellationToken cancellationToken)
        {
            var mailReceivesSelect = freeSql
                .Select<MailReceive>()
                .WhereIf(
                    request.MailId is null,
                    it => it.Status == SysEnum.ReceiveFileType.WaitAnalysis.GetHashCode()
                )
                .WhereIf(request.MailId is not null, it => request.MailId!.Contains(it.MailId));

            if (request.MailId is null)
            {
                mailReceivesSelect.Page(1, 100);
            }
            var mailReceives = await mailReceivesSelect.ToListAsync(
                it => new MailAnalysis()
                {
                    MailId = it.MailId,
                    Attachments = it.Attachments,
                    CreateBy = it.CreateBy,
                    CreateTime = it.CreateTime,
                    HostId = it.HostId,
                    MailCc = it.MailCc,
                    MailDate = it.MailDate,
                    MailEmlUrl = it.MailEmlUrl,
                    MailFrom = it.MailFrom,
                    MailHeader = it.MailHeader,
                    MailHtmlBody = it.MailHtmlBody,
                    MailNo = it.MailNo,
                    MailPriority = it.MailPriority,
                    MailSize = it.MailSize,
                    MailSubject = it.MailSubject,
                    MailTextBody = it.MailTextBody,
                    MailTo = it.MailTo,
                    Status = it.Status,
                    Uid = it.Uid,
                    Version = it.Version,
                    MailUsers = freeSql
                        .Select<MailUser>()
                        .Where(x => x.MailId == it.MailId && x.AddressType == "cc")
                        .ToList(),
                    MailAttachments = freeSql
                        .Select<MailAttachments>()
                        .Where(x => x.MailId == it.MailId)
                        .ToList(),
                    MailFroms = freeSql
                        .Select<MailUser>()
                        .Where(x => x.MailId == it.MailId && x.AddressType == "from")
                        .ToList(),
                },
                cancellationToken
            );

            var existingMailIds = await redisCache.GetCacheKeyValuesAsync<string, string>(
                MailAnalysisCacheKey,
                cancellationToken
            );
            if (existingMailIds.Any(it => request.MailId.Contains(it.Value)))
            {
                // 缓存中已存在这些MailId，说明已有其他进程在处理，直接返回
                throw new ApplicationException("部分邮件已在处理中");
            }
            // 将待处理的mailId存入Redis缓存
            if (mailReceives.Any())
            {
                var mailIdDict = mailReceives.ToDictionary(m => m.MailId, m => m.MailId);
                await redisCache.SetCacheValuesAsync(
                    MailAnalysisCacheKey,
                    mailIdDict,
                    TimeSpan.FromMinutes(3),
                    cancellationToken
                );
            }

            try
            {
                //配置解析
                foreach (var mailReceive in mailReceives)
                {
                    await mediator.Send(new ConfigAnalysisCommand(mailReceive), cancellationToken);
                    await mediator.Send(new AnalysisRelateCommand(mailReceive), cancellationToken);
                }
            }
            finally
            {
                // 处理完成后删除缓存
                if (mailReceives.Any())
                {
                    foreach (var mailReceive in mailReceives)
                    {
                        await redisCache.RemoveCacheValueAsync(
                            MailAnalysisCacheKey,
                            mailReceive.MailId, cancellationToken);
                    }
                }
            }
        }
    }
}
