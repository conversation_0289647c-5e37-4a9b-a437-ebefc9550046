using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_biology_list", DisableSyncStructure = true)]
	public partial class CaseBiologyList {

		[ Column(Name = "cur_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CurId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "biology_address", StringLength = 2000)]
		public string BiologyAddress { get; set; }

		[ Column(Name = "biology_class", StringLength = 50)]
		public string BiologyClass { get; set; }

		[ Column(Name = "biology_date")]
		public DateTime? BiologyDate { get; set; }

		[ Column(Name = "biology_id", StringLength = 50)]
		public string BiologyId { get; set; }

		[ Column(Name = "biology_islive")]
		public bool BiologyIslive { get; set; } = false;

		[ Column(Name = "biology_serial", StringLength = 50)]
		public string BiologySerial { get; set; }

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50, IsNullable = false)]
		public string UpdateUserId { get; set; }

	}

}
