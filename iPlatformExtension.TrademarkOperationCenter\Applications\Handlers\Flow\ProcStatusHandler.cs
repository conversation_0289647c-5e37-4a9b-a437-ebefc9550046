﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public sealed class ProcStatusHandler(ISysFlowNodeRepository _sysFlowNodeRepository,
        IDeliveryFilesRepository _deliFilesRepository,
        ICaseExtendInfoRepository _caseExtendInfoRepository,
        ICaseProcInfoRepository _caseProcInfoRepository,
        IMediator _mediator,
        ICaseProcFlowRepository _caseProcFlowRepository
        ) : IRequestHandler<ProcStatusCommand>
    {
        public async Task Handle(ProcStatusCommand request, CancellationToken cancellationToken)
        {
            var info = request.info;
            var fa = request.fa;
            var logoNo = "";
            var ProcStatus = "TCLZ";
            var SubProcStatus = "t_in_process";
            var firstExamineUserId = "";
            fa.Status = FLOW_STATUS.S1000;
            DateTime? AllocateDate = null;
            CaseProcInfo caseProcInfo = null;
            caseProcInfo = await _caseProcInfoRepository.Where(o => o.ProcId == request.info.ProcID).ToOneAsync(cancellationToken);
            var nodeInfo = await _sysFlowNodeRepository.GetAsync(fa.CurNodeId, cancellationToken);
            if (info.FAuditTypeID == "submit")
            {
                if (fa.FlowType == FlowTypeEnum.DE)
                {
                    ProcStatus = "TCLZ";
                    SubProcStatus = "t_submiting";
                }
                else if (fa.FlowType == FlowTypeEnum.EX)
                {
                    ProcStatus = "TCLZ";
                    SubProcStatus = "t_internal_audit";
                }
                else if (fa.FlowType == FlowTypeEnum.AT)
                {
                    ProcStatus = "TCLZ";
                    SubProcStatus = "t_allocating";
                    if (fa.PrevNodeId == "9C03ABC4-960C-41D2-A92C-52B39B1CA585")
                    {
                        firstExamineUserId = request.curUserID;
                    }
                }
                else
                {
                    ProcStatus = "TCLZ";
                    SubProcStatus = "t_in_process";
                }
            }
            else if (info.FAuditTypeID == "reject")
            {
                //回退保持原来状态
                ProcStatus = caseProcInfo.ProcStatusId;
                SubProcStatus = caseProcInfo.SubProcStatusId;
            }

            if (nodeInfo.NodeCode == "BEGIN")
            {
                // fa.Status = info.FAuditTypeID == "reject" ? FLOW_STATUS.S1000 : FLOW_STATUS.START;
                //退回保留原本案件状态和代理人状态
                ProcStatus = caseProcInfo.ProcStatusId;
                SubProcStatus = caseProcInfo.SubProcStatusId;
            }
            else if (nodeInfo.NodeCode == "END")
            {
                //if (request.info.FFlowType == FlowType.Delivery && request.info.FFlowSubType == "T")
                //{
                //    procNote += request.info.ProcNote;
                //}

                fa.Status = FLOW_STATUS.S5000;
                fa.CurUserId = "";
                //注册申请任务获取商标图样
                if (fa.FlowType == FlowTypeEnum.DE)
                {
                    ProcStatus = "TYCL";
                    SubProcStatus = "t_finish";
                    fa.Status = FLOW_STATUS.S5000;
                    if (caseProcInfo.BasCtrlProc.CtrlProcCode == "TAP")
                    {
                        var file = await _deliFilesRepository.Where(o => o.ProcId == fa.ObjId && o.FileDesc == "商标图样").FirstAsync(cancellationToken);
                        if (file != null)
                        {
                            logoNo = file.Id.ToString();
                            var caseId = caseProcInfo.CaseId;
                            if (!string.IsNullOrEmpty(caseId))
                            {
                                var ext = await _caseExtendInfoRepository.Where(o => o.CaseId == caseId).FirstAsync(cancellationToken);
                                if (ext != null)
                                {
                                    ext.PicFileNo = $"a002{logoNo}";
                                    // await _freeSql.Update<CaseExtendInfo>().SetSource(ext).ExecuteUpdatedAsync(cancellationToken);
                                    await _caseExtendInfoRepository.UpdateAsync(ext, cancellationToken);
                                }
                            }
                        }
                    }
                }
                else if (fa.FlowType == FlowTypeEnum.EX)
                {
                    ProcStatus = "TCLZ";
                    SubProcStatus = "t_internal_review";
                }
                else if (fa.FlowType == FlowTypeEnum.AT)
                {
                    AllocateDate = DateTime.Now;
                    ProcStatus = "TCLZ";
                    SubProcStatus = "t_untreated";

                    var tmlist = new List<string> { "0", "3" };
                    if (tmlist.Contains(caseProcInfo.BasCtrlProc.TrademarkTab) && caseProcInfo.BasCtrlProc.CaseTypeId == "T" && caseProcInfo.CaseInfo.CaseDirection == "II")
                    {
                        SubProcStatus = "t_in_process";
                    }

                    else if (caseProcInfo.CtrlProcId == "DD886CBE-D8D4-4BE0-97F7-FD521674269A")
                    {
                        int? leftTime =
                        !caseProcInfo.RequirementSubmitDate.HasValue ? null : (caseProcInfo.RequirementSubmitDate.Value - DateTime.Now.Date).Days + 1;
                        if (leftTime.HasValue && leftTime.Value > 0)
                        {
                            SubProcStatus = "t_not_time";
                        }
                    }
                }
                else
                {
                    ProcStatus = "TYCL";
                    SubProcStatus = "t_finish";
                }
            }

            await _mediator.Send(new UpdateCaseProcInfoCommand(new UpdateCaseProcInfoDto
            {
                ProcId = request.info.ProcID,
                ProcStatus = ProcStatus,
                SubProcStatus = SubProcStatus,
                CaseId = caseProcInfo?.CaseId,
                LogoFileID = logoNo,
                FirstExamineUserId = firstExamineUserId,
                AllocateDate = AllocateDate,
                IsAddProc = request.info.IsAddProc,
                //ProcNote = procNote
            }), cancellationToken);
        }
    }
}
