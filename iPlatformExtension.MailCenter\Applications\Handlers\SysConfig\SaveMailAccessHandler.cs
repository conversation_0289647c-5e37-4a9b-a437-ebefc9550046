﻿using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using Microsoft.Extensions.Hosting;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    public class SaveMailAccessHandler(IMailAccessRepository mailAccessRepository) : IRequestHandler<SaveMailAccessCommand>
    {
        public async Task Handle(SaveMailAccessCommand request, CancellationToken cancellationToken)
        {
            await mailAccessRepository.Where(o => o.HostId == request.HostId).ToDelete().ExecuteAffrowsAsync();
            List<MailAccess> mailAccesses = new List<MailAccess>();
            foreach (var access in request.Accesses)
            {
                foreach (var useId in access.UseIdList)
                {
                    mailAccesses.Add(new MailAccess
                    {
                        AccessMode = access.AccessMode,
                        UseType = access.UseType,
                        CreateBy = "admin",
                        CreateTime = DateTime.Now,
                        HostId = request.HostId,
                        Id = Guid.NewGuid().ToString(),
                        UpdateBy = "admin",
                        UpdateTime = DateTime.Now,
                        UseId = useId
                    });
                }
            }
            if (mailAccesses.Any())
            {
                await mailAccessRepository.InsertAsync(mailAccesses);
            }
        }
    }
}
