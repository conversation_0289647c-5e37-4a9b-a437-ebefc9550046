using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_customer_brief", DisableSyncStructure = true)]
	public partial class BasCustomerBrief {

		[ Column(Name = "brief_id", StringLength = 50, IsNullable = false)]
		public string BriefId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "brief_name", StringLength = 200)]
		public string BriefName { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
