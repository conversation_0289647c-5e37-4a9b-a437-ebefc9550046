﻿namespace iPlatformExtension.MailCenter.Applications.Models.AuditUser
{
    /// <summary>
    /// 发件必审人列表DTO
    /// </summary>
    public class GetFlowAuditUserDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 必审人信息
        /// </summary>
        public object RequireAuditUsers { get; set; }

        /// <summary>
        /// 指定审核人信息
        /// </summary>
        public object DesignatedAuditUsers { get; set; }

        /// <summary>
        /// 创建人信息
        /// </summary>
        public object CreateByUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 更新人信息
        /// </summary>
        public object UpdateByUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
    }
}
