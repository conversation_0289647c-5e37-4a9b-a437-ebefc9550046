﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.Withdraw3Years;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery.SendPolicies;

internal sealed class Withdraw3YearsPolicy(IOptions<SendPolicyOptions<Withdraw3YearsDeliveryCommandHandler>> options, IFreeSql freeSql)
    : SendPolicy<Withdraw3YearsDeliveryCommandHandler>(options)
{
    public override async ValueTask<bool> MatchPolicyAsync(DeliInfo deliveryInfo)
    {
        var caseInfo = await freeSql.Select<CaseInfo>(deliveryInfo.CaseId).ToOneAsync();
        return deliveryInfo.CaseTypeId == CaseType.Trade
               && string.Equals(CaseDirection.II, caseInfo.CaseDirection, StringComparison.OrdinalIgnoreCase)
               && deliveryInfo.CtrlProcId == CtrlProcIds.TrademarkWithdraw3Years;
    }
}