﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Case;

namespace iPlatformExtension.Outsourcing.Infrastructure.Mappers;

public class CaseMapper : Profile
{
    public CaseMapper()
    {
        CreateMap<CaseInfo, CasePatchDto>()
            .ForMember(dto => dto.ForeignAgencyId, opt => opt.MapFrom(info => info.ForeginAgencyId))
            .ForMember(dto => dto.ForeignCaseNo, opt => opt.MapFrom(info => info.ForeginCaseNo));

        CreateMap<CasePatchDto, CaseInfo>()
            .ForMember(info => info.ForeginAgencyId, expression => expression.MapFrom(dto => dto.ForeignAgencyId))
            .ForMember(info => info.ForeginCaseNo, expression => expression.MapFrom(dto => dto.ForeignCaseNo));
    }
}