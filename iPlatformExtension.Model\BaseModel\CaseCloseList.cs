using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_close_list", DisableSyncStructure = true)]
	public partial class CaseCloseList {

		/// <summary>
		/// 结案案件id
		/// </summary>
		[ Column(Name = "close_case_list_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CloseCaseListId { get; set; }

		/// <summary>
		/// 结案事务id
		/// </summary>
		[ Column(Name = "case_close_id", StringLength = 50)]
		public string CaseCloseId { get; set; }

		/// <summary>
		/// 案件id
		/// </summary>
		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		/// <summary>
		/// 案件结案前状态
		/// </summary>
		[ Column(Name = "case_status_id_old", StringLength = 50)]
		public string CaseStatusIdOld { get; set; }

		[ Column(Name = "close_reason", StringLength = 1000)]
		public string CloseReason { get; set; }

		/// <summary>
		/// 错误信息
		/// </summary>
		[ Column(Name = "error_message", StringLength = 500)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
