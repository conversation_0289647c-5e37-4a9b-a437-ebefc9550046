﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Customer;
using iPlatformExtension.Public.Applications.Queries.Customer;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Customer
{
    /// <summary>
    /// 查询联系类别
    /// </summary>
    internal sealed class SearchContactTypeQueryHandler : IRequestHandler<SearchContactTypeQuery, IEnumerable<SearchContactTypeDto>>
    {
        private readonly IFreeSql _freeSql;

        public SearchContactTypeQueryHandler(IFreeSql freeSql)
        {
            _freeSql = freeSql;
        }

        public async Task<IEnumerable<SearchContactTypeDto>> Handle(SearchContactTypeQuery request, CancellationToken cancellationToken)
        {
            return await _freeSql.Select<BasContactType>().Where(it => it.IsEnabled == true).ToListAsync(it =>
                new SearchContactTypeDto(it.ContactTypeId, it.ContactTypeZhCn, it.ContactTypeEnUs, it.ContactTypeJaJp), cancellationToken);
        }
    }
}

