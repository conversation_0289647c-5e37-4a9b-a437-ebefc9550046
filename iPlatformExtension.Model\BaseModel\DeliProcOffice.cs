using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_proc_office", DisableSyncStructure = true)]
	public partial class DeliProcOffice {

		[ Column(Name = "office_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string OfficeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "app_file_code", StringLength = 50)]
		public string AppFileCode { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "file_type", StringLength = 50)]
		public string FileType { get; set; }

		[ Column(Name = "office_name", StringLength = 50)]
		public string OfficeName { get; set; }

	}

}
