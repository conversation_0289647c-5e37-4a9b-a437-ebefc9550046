﻿using System.Security.Cryptography;
using System.Text;

namespace iPlatformExtension.Common.Encryption
{
    public class Sha1Helper
    {

        /// <summary>
        /// 对字符串SHA1加密
        /// </summary>
        /// <param name="source">源字符串</param>
        /// <param name="encoding">编码类型</param>
        /// <returns>加密后的十六进制字符串</returns>
        public static string Sha1Encrypt(string source, Encoding encoding = null)
        {
            using (SHA1 sha1Hash = SHA1.Create())
            {
                // 将输入字符串转换为字节数组
                byte[] data = sha1Hash.ComputeHash(Encoding.UTF8.GetBytes(source));

                // 创建一个新的Stringbuilder来收集字节并创建一个字符串
                StringBuilder builder = new StringBuilder();

                // 循环遍历每个字节的集合，并格式化为十六进制的值
                for (int i = 0; i < data.Length; i++)
                {
                    builder.Append(data[i].ToString("x2"));
                }

                return builder.ToString();
            }
        }

    }
}
