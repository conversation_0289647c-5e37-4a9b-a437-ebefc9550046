﻿using iPlatformExtension.Commission.Infrastructure.Models;
using MediatR;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;

/// <summary>
/// 固化出口商标权值命令 
/// </summary>
public sealed record CreateForeignCommissionWeightCommand : IRequest, IBackgroundTracingCommand
{
    /// <inheritdoc />
    public string TraceParentId { get; set; } = null!;

    /// <inheritdoc />
    public string OperationName { get; set; } = "出口商标权值固化";
}