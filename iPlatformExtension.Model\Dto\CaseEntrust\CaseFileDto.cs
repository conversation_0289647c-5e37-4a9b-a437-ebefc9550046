﻿using FreeSql.DataAnnotations;


namespace iPlatformExtension.Model.Dto.CaseEntrust
{
    public class CaseFileDto
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        public string FileId { get; set; }
        public DateTime? CreateTime { get; set; }

        public string? CreateUser { get; set; }
        /// <summary>
        /// 文件后缀
        /// </summary>
        public string? FileEx { get; set; }
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件编号 
        /// </summary>
        public string? FileNo { get; set; }

        /// <summary>
		/// 管制事项,提案,以及其他ID
		/// </summary>
        public string ObjId { get; set; }

        /// <summary>
		/// 文件大小
		/// </summary>
        public long? FileSize { get; set; }
    }
}
