﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class UpdateWithdrawResultCommandHandler(
    IMediator mediator,
    IDeliveryInfoRepository deliveryInfoRepository,
    IRedisCache<RedisCacheOptionsBase> redisCache)
    : IRequestHandler<UpdateWithdrawResultCommand, bool>
{

    public async Task<bool> Handle(UpdateWithdrawResultCommand request, CancellationToken cancellationToken)
    {
        var result = request.Result;
        var deliveryInfo = request.DeliveryInfo;
        var operatorId = request.OperatorId;

        if (request.IsAuto)
        {
            await redisCache.RemoveCacheValueAsync(LockKey.DeliveringLockKey, deliveryInfo.ProcId, cancellationToken);
            await mediator.Send(
                new InsertHistoryCommand(
                    deliveryInfo.ProcId,
                    result.GetOperationDescriptionWithResult(),
                    operatorId,
                    result.Message,
                    null,
                    result.Success), cancellationToken);
        }
        
        if (result.Success)
        {
            deliveryInfo.Status = (int) DeliveryStatus.Ready;
            deliveryInfo.OperationResult = true;
            deliveryInfo.IsAuto = false;
            deliveryInfo.DeliveryDate = null;
            deliveryInfo.DisplayJson = "[]";
        }

        if (result.Success && request.IsAuto)
        {
            await mediator.Send(new CancelOrderCommand(deliveryInfo), cancellationToken);
        }
        
        deliveryInfo.UpdateTime = DateTime.Now;
        deliveryInfo.UpdateUserId = operatorId;
        await deliveryInfoRepository.UpdateAsync(deliveryInfo, cancellationToken);

        return result.Success;
    }
}