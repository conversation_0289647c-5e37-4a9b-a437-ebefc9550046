using OBS;

namespace iPlatformExtension.Common.Clients.HuaweiObs;

internal static class HuaweiObsReact
{
    internal static AsyncCallback CreateObsResponseCallback<TResponse>(Func<ObsClient, IAsyncResult, TResponse> getResponse)
    {
        ArgumentNullException.ThrowIfNull(getResponse);
        return new DefaultHuaweiObsReactor<TResponse>(getResponse).ReactResponse;
    }

    internal static Func<AsyncCallback, object?, IAsyncResult> CreateBeginMethod<TRequest>(
        HuaweiObsBeginRequestContext<TRequest> context)
    {
        ArgumentNullException.ThrowIfNull(context);
        return context.BeginRequest;
    }
}