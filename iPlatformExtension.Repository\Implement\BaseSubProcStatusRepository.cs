﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class BaseSubProcStatusRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    CacheExpirationToken<BasSubProcStatus> expirationToken,
    DefaultRedisCache redisCache)
    : BaseRepository<BasSubProcStatus, string>(freeSql), IBaseSubProcStatusRepository
{
    public IMemoryCache MemoryCache { get; } = memoryCache;

    public CacheExpirationToken<BasSubProcStatus> ExpirationToken { get; } = expirationToken;

    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}