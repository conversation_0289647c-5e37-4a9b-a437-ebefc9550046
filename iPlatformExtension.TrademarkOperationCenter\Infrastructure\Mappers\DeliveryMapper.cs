﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Mappers;

/// <summary>
/// 商标作业平台的对象映射
/// </summary>
public sealed class DeliveryMapper : Profile
{
    /// <summary>
    /// 对象映射配置
    /// </summary>
    public DeliveryMapper()
    {
        // 尼斯分类信息快照
        MapNiceCategories();

        // 递交文件信息
        MapDeliveryFiles();

        // 商标信息快照
        MapTrademarkInfo();
        
        // 申请人信息快照
        MapApplicantInfo();
        
        // 优先权信息快照
        MapPriorityInfo();

        // 联系人信息快照
        MapContactInfo();
        
        // 其他信息
        MapOtherInfo();

        // 递交信息快照
        MapDeliveryInfo();
    }

    private void MapNiceCategories()
    {
        CreateMap<CaseTrademarkNiceCategory, DeliveryNiceCategory>();

        CreateMap<ProcNiceCategory, DeliveryNiceCategory>();
    }

    private void MapDeliveryFiles()
    {
        CreateMap<DeliFiles, FileInfoSnapshot>()
            .ForMember(snapshot => snapshot.FileDescription,
                expression => expression.MapFrom(info => info.FileDesc))
            .ForMember(snapshot => snapshot.FileType,
                expression => expression.MapFrom(info => info.BaseFileType));
    }

    private void MapTrademarkInfo()
    {
        CreateMap<DeliInfo, TrademarkInfoSnapshot>()
            .ForMember(snapshot => snapshot.TrademarkName,
                expression => expression.MapFrom(info => info.CaseName))
            .ForMember(snapshot => snapshot.TrademarkDescription,
                expression => expression.MapFrom(info => info.Description))
            .ForMember(snapshot => snapshot.TrademarkType,
                expression => expression.MapFrom(info => info.ApplyTypeId))
            .ForMember(snapshot => snapshot.MarkingType,
                expression => expression.MapFrom(info => info.MarkingType))
            .ForMember(snapshot => snapshot.AgentUser,
                expression => expression.MapFrom(info => info.AgentUser))
            .ForMember(snapshot => snapshot.OfficialDeadline,
                expression => expression.MapFrom(info => info.OfficialDeadline))
            .ForMember(snapshot => snapshot.CtrlProcName,
                expression => expression.MapFrom(info => info.CtrlProcId))
            .ForMember(snapshot => snapshot.Customer,
                expression => expression.MapFrom(info => info.CustomerId));
    }

    private void MapApplicantInfo()
    {
        CreateMap<DeliApplicant, ApplicantInfoSnapshot>()
            .ForMember(snapshot => snapshot.Country,
                expression => expression.MapFrom(applicant => applicant.CountryId))
            .ForMember(snapshot => snapshot.ApplicantType,
                expression => expression.MapFrom(applicant => applicant.TypeId))
            .ForMember(snapshot => snapshot.IdentityNumber,
                expression => expression.MapFrom(applicant => applicant.CardNo))
            .ForMember(snapshot => snapshot.CertificateNumber,
                expression => expression.MapFrom(applicant => applicant.CardNo))
            .ForMember(snapshot => snapshot.CertificateType,
                expression => expression.MapFrom(applicant => applicant.CardType))
            .ForMember(snapshot => snapshot.ApplicantAddressEn,
                expression => expression.MapFrom(applicant => applicant.AddressEn))
            .ForMember(snapshot => snapshot.ApplicantAddressZh,
                expression => expression.MapFrom(applicant => applicant.AddressCn))
            .ForMember(snapshot => snapshot.ApplicantNameZh,
                expression => expression.MapFrom(applicant => applicant.ApplicantNameCn))
            .ForMember(snapshot => snapshot.ApplicantNameEn,
                expression => expression.MapFrom(applicant => applicant.ApplicantNameEn))
            .ForMember(snapshot => snapshot.ApplicantPostCode,
                expression => expression.MapFrom(applicant => applicant.Postcode));


        CreateMap<DeliApplicant, OtherApplicantInfoSnapshot>()
            .ForMember(snapshot => snapshot.ApplicantNameZh,
                expression => expression.MapFrom(applicant => applicant.ApplicantNameCn))
            .ForMember(snapshot => snapshot.ApplicantNameEn,
                expression => expression.MapFrom(applicant => applicant.ApplicantNameEn))
            .ForMember(snapshot => snapshot.CertificateNumber,
                expression => expression.MapFrom(applicant => applicant.CardNo))
            .ForMember(snapshot => snapshot.CertificateType,
                expression => expression.MapFrom(applicant => applicant.CardType));
        
        CreateMap<CaseProcApplicant, DeliApplicant>()
            .ForMember(applicant => applicant.Id, 
                expression => expression.MapFrom(applicant => default(int)))
            .ForMember(applicant => applicant.ApplicantId,
                expression => expression.MapFrom(applicant => applicant.Id))
            .ForMember(applicant => applicant.AddressCn,
                expression => expression.MapFrom(applicant => applicant.AddrCn))
            .ForMember(applicant => applicant.AddressEn,
                expression => expression.MapFrom(applicant => applicant.AddrEn))
            .ForMember(applicant => applicant.TypeId,
                expression => expression.MapFrom(applicant => applicant.ApplicantTypeId))
            .ForMember(applicant => applicant.DeliveryBusinessType,
                expression => expression.MapFrom(applicant => applicant.ChangeType));
    }

    private void MapPriorityInfo()
    {
        CreateMap<DeliPriority, PriorityInfoSnapshot>()
            .ForMember(snapshot => snapshot.Area,
                expression => expression.MapFrom(priority => priority.CountryId));
    }

    private void MapContactInfo()
    {
        CreateMap<DeliInfo, ContactInfoSnapshot>()
            .ForMember(snapshot => snapshot.ContactTelephone,
                expression => expression.MapFrom(info => info.ContactTel));
    }

    private void MapOtherInfo()
    {
        CreateMap<DeliOtherInfo, OtherInfoSnapshot>()
            .ForMember(snapshot => snapshot.AnnulmentLawBasic,
                expression => expression.MapFrom(info => info.LawProvisions));

        CreateMap<DeliveryLawBasis, LegalFactualBasis>()
            .ForMember(basis => basis.LegalBasis, expression => expression.MapFrom(basis => basis.LawId))
            .ForMember(basis => basis.EvidenceUrl, expression => expression.MapFrom(basis => basis.Url))
            .ForMember(basis => basis.FactualReason, expression => expression.MapFrom(basis => basis.Reason))
            .ForMember(basis => basis.EvidenceFileName, expression => expression.MapFrom(basis => basis.FileName));

        CreateMap<DeliveryCitedTrademark, CitedTrademarkInfo>()
            .ForMember(info => info.ContactName, expression => expression.MapFrom(trademark => trademark.Contactor))
            .ForMember(info => info.FirstCategoryNumber,
                expression => expression.MapFrom(trademark => trademark.TrademarkClasses));

        CreateMap<DeliveryLawBasis, DeliFiles>()
            .ForMember(files => files.FileName, expression => expression.MapFrom(basis => basis.FileName))
            .ForMember(files => files.FileEx,
                expression => expression.MapFrom(basis => Path.GetExtension(basis.FileName)))
            .ForMember(files => files.Url, expression =>
            {
                expression.Condition(basis => !string.IsNullOrWhiteSpace(basis.Url));
                expression.MapFrom(basis => basis.Url);
            })
            .ForMember(files => files.Id, expression => expression.MapFrom(basis => basis.FileId));
    }

    private void MapDeliveryInfo()
    {
        CreateMap<DeliInfo, TrademarkDeliveryDto>()
            .ForMember(dto => dto.FileInfoSnapshots,
                expression => expression.MapFrom(info => info.Files))
            .ForMember(dto => dto.ContactInfoSnapshot,
                expression => expression.MapFrom(info => info))
            .ForMember(dto => dto.TrademarkInfoSnapshot,
                expression => expression.MapFrom(info => info))
            .ForMember(dto => dto.PriorityInfoSnapshots,
                expression =>
                {
                    expression.Condition(info => info.Priorities != null);
                    expression.MapFrom(info => info.Priorities);
                })
            .ForMember(dto => dto.ApplicantInfoSnapshot,
                expression =>
                {
                    expression.Condition(info => info.Applicants != null);
                    expression.MapFrom(info =>
                        info.Applicants!.FirstOrDefault(applicant => applicant.IsRepresent ?? false));
                })
            .ForMember(dto => dto.OtherApplicantInfoSnapshots,
                expression =>
                {
                    expression.Condition(info => info.Applicants != null);
                    expression.MapFrom(info =>
                        info.Applicants!.Where(applicant => applicant.IsOtherApplicant));
                })
            .ForMember(dto => dto.ProcApplicantInfoSnapshot,
                expression =>
                {
                    expression.Condition(info => info.Applicants is not null);
                    expression.MapFrom(info => info.Applicants!.FirstOrDefault(applicant => applicant.IsProcApplicant));
                })
            .ForMember(dto => dto.CreationTime,
                expression => expression.MapFrom(info => info.CreateTime))
            .ForMember(dto => dto.CreatorId,
                expression => expression.MapFrom(info => info.CreateUserId))
            .ForMember(dto => dto.UpdateTime,
                expression => expression.MapFrom(info => info.UpdateTime))
            .ForMember(dto => dto.UpdaterId,
                expression => expression.MapFrom(info => info.UpdateUserId))
            .ForMember(dto => dto.OtherInfoSnapshot,
                expression => expression.MapFrom(info => info.OtherInfo ?? new DeliOtherInfo()
                {
                    ProcId = info.ProcId
                }))
            .ForMember(dto => dto.TrademarkNiceClasses,
                expression =>
                {
                    expression.Condition(info => info.OtherInfo is not null);
                    expression.MapFrom(info => info.OtherInfo!.TrademarkNiceClasses);
                });
    }
    
}