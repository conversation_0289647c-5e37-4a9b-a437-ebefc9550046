using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_user_tech_field", DisableSyncStructure = true)]
	public partial class SysUserTechField {

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "tech_field_id", StringLength = 50)]
		public string TechFieldId { get; set; }

		[ Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

	}

}
