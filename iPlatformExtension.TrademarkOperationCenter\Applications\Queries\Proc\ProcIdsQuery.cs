﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Proc;

/// <summary>
/// 批量任务查询
/// </summary>
/// <param name="ProcIds">任务id集合</param>
/// <param name="Search">搜索</param>
/// <param name="CaseTypeId">案件类型</param>
/// <param name="CaseDirection">案件流向</param>
/// <param name="CustomerId">客户id</param>
/// <param name="CtrlProcId">控制任务id</param>
/// <param name="ExcludeProcId">排除任务id</param>
public sealed record ProcIdsQuery(IEnumerable<string>? ProcIds, string? Search = null, string? CaseTypeId = null, string? CaseDirection = null, string? CustomerId = null
    , string? CtrlProcId = null,string? ExcludeProcId = null)
    : PageModel, IRequest<IEnumerable<ProcItemDto>>;