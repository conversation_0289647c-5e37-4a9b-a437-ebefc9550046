﻿namespace iPlatformExtension.Finance.Applications.Models.CurrencyRate;

/// <summary>
/// 汇率同步信息
/// </summary>
internal sealed class KingdeeCurrencyRate
{
    public string Id { get; set; } = null!;

    public string SourceCurrencyCode { get; set; } = null!;
    
    public string SourceCurrencyName { get; set; } = null!;
    
    public string TargetCurrencyCode { get; set; } = null!;
    
    public string TargetCurrencyName { get; set; } = null!;
    
    public decimal ExchangeRage { get; set; }
    
    public DateTime BeginDate { get; set; }
    
    public DateTime EndDate { get; set; }
    
    public string CreateBy { get; set; } = null!;
    
    public DateTime CreateTime { get; set; }
    
    public string UpdateBy { get; set; } = null!;
    
    public DateTime UpdateTime { get; set; }
    
    public bool IsDeleted { get; set; }
}

