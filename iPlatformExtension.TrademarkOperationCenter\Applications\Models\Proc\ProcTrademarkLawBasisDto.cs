﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;

/// <summary>
/// 商标异议条款数据
/// </summary>
public sealed class ProcTrademarkLawBasisDto
{
    /// <summary>
    /// 法律条款id
    /// </summary>
    [Required]
    public string LawBasisId { get; set; } = string.Empty;

    /// <summary>
    /// 事实理由
    /// </summary>
    [Required]
    public string FactualReason { get; set; } = string.Empty;

    /// <summary>
    /// 证据材料文件id
    /// </summary>
    public int? FileId { get; set; }

    /// <summary>
    /// 任务id
    /// </summary>
    [Required]
    public string ProcId { get; set; } = string.Empty;
}