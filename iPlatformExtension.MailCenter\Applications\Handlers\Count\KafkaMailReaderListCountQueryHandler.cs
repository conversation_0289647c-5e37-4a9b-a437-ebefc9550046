﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.MailCenter.Applications.Models.Count;
using iPlatformExtension.MailCenter.Applications.Queries.Count;
using iPlatformExtension.Model.Enum;
using MediatR;
using Microsoft.AspNetCore.SignalR.Client;
using System.Collections.Generic;
using iPlatformExtension.Model.Dto.Flow;
using static iPlatformExtension.Model.Enum.SysEnum;
using iPlatformExtension.Common.Db.FreeSQL;
using MongoDB.Bson;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Count;

internal sealed class KafkaMailReaderListCountQueryHandler(IRedisCache<RedisCacheOptionsBase> redisCache, HubConnection hubConnection, IMediator mediator, IFreeSql<MailCenterFreeSql> freeSql)
    : RedisCacheHandle(redisCache, mediator, freeSql), IRequestHandler<KafkaMailReaderListCountQuery, IEnumerable<KafkaCountDto>>
{
    public async Task<IEnumerable<KafkaCountDto>> Handle(
        KafkaMailReaderListCountQuery request,
        CancellationToken cancellationToken
    )
    {
        var cdcModelPayload = request.cdcModel.Payload;
        var userId = String.Empty;
        Console.WriteLine(nameof(KafkaMailReaderListCountQueryHandler)+":"+request.cdcModel.Payload.ToJson());
        // 处理新增未读邮件
        if (cdcModelPayload.Op.ToLower() == "c" && cdcModelPayload.After?.Status == ReaderStatusEnum.ToRead.GetHashCode())
        {
            userId = cdcModelPayload.After.UserId;
            await UpdateHandleCache("ReaderList", cdcModelPayload.After.UserId, 1, cdcModelPayload.TsDateTime, cancellationToken);
        }

        // 处理邮件状态变更为已读
        if (cdcModelPayload.Op.ToLower() == "u" && (
            cdcModelPayload.After?.Status == ReaderStatusEnum.AlreadyRead.GetHashCode() ||
            cdcModelPayload.After?.Status == ReaderStatusEnum.Invalid.GetHashCode()))
        {
            userId = cdcModelPayload.After.UserId;
            await UpdateHandleCache("ReaderList", cdcModelPayload.After.UserId, -1, cdcModelPayload.TsDateTime, cancellationToken);
        }

        // 处理删除未读邮件
        if (cdcModelPayload.Op.ToLower() == "d" && cdcModelPayload.Before?.Status == 0)
        {
            userId = cdcModelPayload.Before.UserId;
            await UpdateHandleCache("ReaderList", cdcModelPayload.Before.UserId, -1, cdcModelPayload.TsDateTime, cancellationToken);
        }

        if (!string.IsNullOrWhiteSpace(userId))
        {
            var count = await GetCount(userId, cancellationToken);
            await hubConnection.InvokeAsync("MailCountMessageAsync", "MailCenterCount", userId, count, cancellationToken: cancellationToken);
        }

        return new List<KafkaCountDto>();
    }
}