﻿using CSScriptLib;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Components.Forms;
using MimeKit;
using System.Text;
using iPlatformExtension.MailCenterRepository.Interface;
using System.Collections.Generic;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class AddReaderHandler(IFreeSql<MailCenterFreeSql> sql,
        IMailReaderListRepository mailReaderListRepository,
        IHttpContextAccessor httpContextAccessor,
        IMailReceiveRepository mailReceive,
        IMailSendFlowRepository mailSendFlowRepository,
        IMediator mediator) : IRequestHandler<AddReaderCommand>
    {
        public async Task Handle(AddReaderCommand request, CancellationToken cancellationToken)
        {
            ArgumentNullException.ThrowIfNull(request);

            var userId = httpContextAccessor.HttpContext?.User.GetUserID()
                ?? throw new InvalidOperationException("用户ID不能为空");

            var lst = from mailId in request.MailList
                      from userid in request.UserList
                      select new AddReaderMail { MailId = mailId, UserId = userid };

            // 根据添加类型处理现有读者
            if (request.AddType == ReaderAddType.Replace)
            {
                await mailReaderListRepository
                    .Where(o => lst.Any(m => m.MailId == o.MailId && o.Status == 0))
                    .ToDelete()
                    .ExecuteAffrowsAsync(cancellationToken);
            }
            else if (request.AddType != ReaderAddType.Add)
            {
                throw new ArgumentException($"不支持的添加模式: {request.AddType}");
            }

            // 获取现有的阅读记录
            var existingReaders = await mailReaderListRepository
                .Where(o => lst.Any(m => m.MailId == o.MailId && m.UserId == o.UserId))
                .ToListAsync(cancellationToken);

            // 创建新的阅读记录
            var newReaders = lst
                .Where(o => !existingReaders.Any(m => m.UserId == o.UserId && m.MailId == o.MailId))
                .Select(o => new MailReaderList
                {
                    Id = Guid.NewGuid().ToString(),
                    CreateTime = DateTime.Now,
                    CreateBy = userId,
                    Status = 0,
                    UserId = o.UserId,
                    MailId = o.MailId,
                    MailType = request.MailType
                })
                .ToList();
            if (newReaders.Any())
            {
                await Task.WhenAll(
                    mailReaderListRepository.InsertAsync(newReaders),
                    mediator.Send(new MailCenterMessageQuery(newReaders, request.MailType == SysEnum.MailType.Receive.ToString() ? OperationTypeEnum.Add : OperationTypeEnum.SendAddReader), cancellationToken)
                );
            }
        }
    }
}
