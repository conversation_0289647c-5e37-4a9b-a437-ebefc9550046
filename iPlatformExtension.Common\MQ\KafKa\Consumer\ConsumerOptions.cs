﻿using System.Threading.Channels;
using Confluent.Kafka;
using iPlatformExtension.Common.MQ.KafKa.Models;

namespace iPlatformExtension.Common.MQ.KafKa.Consumer;

public class ConsumerOptions<TMessage>
{
    public ConsumerConfig ServerOptions { get; set; } = new();

    public IEnumerable<string> Topics { get; set; } = Array.Empty<string>();

    public ConsumerBuilder<MessageKey, TMessage>? ConsumerBuilder { get; set; }

    public bool ConcurrentConsume { get; set; } = false;

    public int ConcurrentCount { get; set; } = 5;

    internal Channel<ConsumeResult<MessageKey, TMessage>> BackgroundConsumeChannel { get; set; } = null!;
}