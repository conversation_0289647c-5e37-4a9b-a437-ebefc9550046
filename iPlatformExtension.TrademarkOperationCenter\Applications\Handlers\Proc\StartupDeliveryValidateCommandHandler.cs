﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Proc;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class StartupDeliveryValidateCommandHandler(IMediator mediator, IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<StartupDeliveryValidateCommand, BatchDeliveryValidateResult>
{
    public async Task<BatchDeliveryValidateResult> Handle(StartupDeliveryValidateCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = httpContextAccessor.HttpContext?.User.GetUserId() ?? string.Empty;
        var procList = await mediator.Send(new ProcIdsQuery(request.ProcIds), cancellationToken);

        var validResult = new BatchDeliveryValidateResult(procList.Count())
        {
            Success = procList.GroupBy(dto => dto.CtrlProcId).Count() == 1
        };

        if (!validResult.Success)
        {
            return validResult.MultipartCtrlProc();
        }

        validResult = procList.Where(dto => dto.UndertakerId != currentUserId)
            .Aggregate(validResult, (result, dto) => result.InvalidUndertaker(dto.ProcNo));
        if (!validResult.Success)
        {
            return validResult;
        }

        var procIds = procList.Select(dto => dto.ProcId).ToList();
        var deliveryList = await mediator.Send(new BatchDeliveryValidationQuery(procIds, false), cancellationToken);
        validResult = deliveryList
            .Where(dto => dto.FlowType == FlowType.Delivery || dto.DeliveryFlowSubType != "TII")
            .Aggregate(validResult, (result, info) => result.CannotStartup(info.ProcNo));

        return validResult;
    }
}