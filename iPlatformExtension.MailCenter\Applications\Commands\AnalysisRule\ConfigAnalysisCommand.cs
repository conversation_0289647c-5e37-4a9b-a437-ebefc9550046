﻿using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.MailCenter.Applications.Models.Analysis;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.AnalysisRule;

/// <summary>
/// 配置解析
/// </summary>
/// <param name="MailAnalysis">邮件分析</param>
public record ConfigAnalysisCommand(MailAnalysis MailAnalysis) : IRequest, IUnitOfWorkCommandMysql;
