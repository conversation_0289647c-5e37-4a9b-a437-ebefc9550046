using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_product_field", DisableSyncStructure = true)]
	public partial class CaseProductField {

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "product_field_id", StringLength = 50)]
		public string ProductFieldId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
