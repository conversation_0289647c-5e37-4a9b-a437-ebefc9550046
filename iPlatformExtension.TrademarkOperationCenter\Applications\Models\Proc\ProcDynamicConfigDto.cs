﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;

/// <summary>
/// 任务动态配置DTO
/// </summary>
public class ProcDynamicConfigDto
{
    /// <summary>
    /// 类型名称
    /// </summary>
    [Required]
    public string TypeName { get; set; } = default!;

    /// <summary>
    /// 案件流向
    /// </summary>
    [Required]
    public string CaseDirection { get; set; } = default!;
}