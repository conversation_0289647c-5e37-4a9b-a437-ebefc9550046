﻿using FreeSql;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

/// <summary>
/// 费项查询语句构建者
/// </summary>
public interface IFeesQueryStatementBuilder
{
    /// <summary>
    /// 构建费项查询语句
    /// </summary>
    /// <param name="dto">查询参数</param>
    /// <param name="feesQuery">费项查询</param>
    /// <param name="args">额外参数</param>
    static abstract void BuildFeesQueryStatement(FeeQueryDto dto, ISelect<CaseFeeList> feesQuery, params object?[] args);
}