﻿using Microsoft.Extensions.DependencyInjection.Extensions;
using Yarp.ReverseProxy.Configuration;

namespace iPlatformExtension.Gateway.Nacos;

public static class NacosYarpExtension
{
    public static RouteConfig GenerateRouteConfig(this RouteConfig baseRouteConfig, NacosClusterId clusterId)
    {
        var baseRouteId = baseRouteConfig.RouteId;
        var baseClusterId = baseRouteConfig.ClusterId;
        var baseRoutePattern = baseRouteConfig.Match.Path;
        var baseRouteMatch = baseRouteConfig.Match;
        var (service, group) = clusterId;
        var routeTransforms = new List<Dictionary<string, string>>(baseRouteConfig.Transforms?.Count ?? 0);
        var baseTransforms = baseRouteConfig.Transforms;
        
        if (baseTransforms is not null)
        {
            foreach (var transform in baseTransforms)
            {
                var routeTransform = new Dictionary<string, string>(transform);
                foreach (var (key, value) in transform)
                {
                    routeTransform[key] = value.Replace("[service]", service, StringComparison.CurrentCultureIgnoreCase)
                        .Replace("[group]", group, StringComparison.CurrentCultureIgnoreCase);
                }
                routeTransforms.Add(routeTransform);
            }
        }

        return new RouteConfig
        {
            RouteId = string.IsNullOrWhiteSpace(baseRouteId) ? clusterId : baseRouteId,
            ClusterId = string.IsNullOrWhiteSpace(baseClusterId) ? clusterId : baseClusterId,
            Match = new RouteMatch()
            {
                Hosts = baseRouteMatch.Hosts,
                Path = string.IsNullOrWhiteSpace(baseRoutePattern)
                    ? $"/{service}/{{**catch-all}}"
                    : baseRoutePattern.Replace("[service]", service, StringComparison.CurrentCultureIgnoreCase)
                        .Replace("[group]", group, StringComparison.CurrentCultureIgnoreCase),
                QueryParameters = baseRouteMatch.QueryParameters,
                Headers = baseRouteMatch.Headers,
                Methods = baseRouteMatch.Methods
            },
            AuthorizationPolicy = baseRouteConfig.AuthorizationPolicy,
            Order = baseRouteConfig.Order,
            RateLimiterPolicy = baseRouteConfig.RateLimiterPolicy,
            TimeoutPolicy = baseRouteConfig.TimeoutPolicy,
            Timeout = baseRouteConfig.Timeout,
            CorsPolicy = baseRouteConfig.CorsPolicy,
            MaxRequestBodySize = baseRouteConfig.MaxRequestBodySize,
            Metadata = baseRouteConfig.Metadata,
            Transforms = routeTransforms
        };
    }

    public static IReverseProxyBuilder LoadFromNacos(this IReverseProxyBuilder builder,
        string configSectionName = "NacosYarp")
    {
        var services = builder.Services;
        services.AddSingleton<NacosNamingBackgroundService>();
        services.AddHostedService(provider => provider.GetRequiredService<NacosNamingBackgroundService>());
        services.TryAddSingleton<NacosNamingProxyConfigProvider>();
        services.AddSingleton<IProxyConfigProvider>(provider =>
            provider.GetRequiredService<NacosNamingProxyConfigProvider>());
        services.AddOptions<NacosYarpOptions>().BindConfiguration(configSectionName);

        return builder;
    }
}