using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 权大师请求基类
/// </summary>
public abstract class PhoenixRequestParameters
{
    /// <summary>
    /// appKey
    /// </summary>
    public string? AppKey { get; set; }

    /// <summary>
    /// 签名方法。默认是md5
    /// </summary>
    public string SignMethod { get; set; } = "md5";

    /// <summary>
    /// 当前时间的时间戳
    /// </summary>
    public long Timestamp { get; set; } = DateTimeOffset.Now.ToUnixTimeMilliseconds();

    /// <summary>
    /// 版本号。默认1.0
    /// </summary>
    [JsonPropertyName("v")]
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// 参数格式。默认json
    /// </summary>
    public string Format { get; set; } = "json";

    /// <summary>
    /// 签名
    /// </summary>
    public string? Sign { get; set; }

    /// <summary>
    /// 用户id
    /// </summary>
    public string Executor { get; set; } = "2f784238364f416f593739506c63534861746f7143673d3d";
    
    /// <summary>
    /// 权大师系统的华进用户id
    /// </summary>
    public string UserId { get; set; } = "2f784238364f416f593739506c63534861746f7143673d3d";

    /// <summary>
    /// 权大师请求参数
    /// </summary>
    public static readonly PhoenixRequestParameters Default = new DefaultPhoenixRequestParameters();
    
    internal sealed class DefaultPhoenixRequestParameters : PhoenixRequestParameters
    {
        
    }
}