using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{

    [Table(Name = "cus_customer", DisableSyncStructure = true)]
    public partial class CusCustomer
    {

        /// <summary>
        /// 客户ID
        /// </summary>
        [Column(Name = "customer_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string CustomerId { get; set; } = Guid.NewGuid().ToString().ToUpper();

        [Column(Name = "addr_city", StringLength = 256)]
        public string AddrCity { get; set; }

        [Column(Name = "addr_district", StringLength = 256)]
        public string AddrDistrict { get; set; }

        [Column(Name = "addr_province", StringLength = 256)]
        public string AddrProvince { get; set; }

        [Column(Name = "balance_way", StringLength = 50)]
        public string BalanceWay { get; set; }

        /// <summary>
        /// 商务人员
        /// </summary>
        [Column(Name = "busi_user_id", StringLength = 50)]
        public string? BusiUserId { get; set; }

        [Column(Name = "clue_user_id", StringLength = 50)]
        public string? ClueUserId { get; set; }

        /// <summary>
        /// 客户负责人
        /// </summary>
        [Column(Name = "corporation", StringLength = 50)]
        public string Corporation { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        [Column(Name = "country_id", StringLength = 50)]
        public string? CountryId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column(Name = "create_time")]
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人（关联）
        /// </summary>
        [Column(Name = "create_user_id", StringLength = 50, IsNullable = false)]
        public string CreateUserId { get; set; }

        [Column(Name = "crm_customer_code", StringLength = 50)]
        public string CrmCustomerCode { get; set; }

        [Column(Name = "crm_customer_id", StringLength = 50)]
        public string CrmCustomerId { get; set; }

        /// <summary>
        /// 客户简称
        /// </summary>
        [Column(Name = "customer_code", StringLength = 100)]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 信誉等级
        /// </summary>
        [Column(Name = "customer_credit", StringLength = 50)]
        public string CustomerCredit { get; set; }

        /// <summary>
        /// 客户来源
        /// </summary>
        [Column(Name = "customer_from", StringLength = 50)]
        public string CustomerFrom { get; set; }

        [Column(Name = "customer_full_name", StringLength = 2000)]
        public string CustomerFullName { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Column(Name = "customer_name", StringLength = 1000)]
        public string CustomerName { get; set; }

        /// <summary>
        /// 英文名称
        /// </summary>
        [Column(Name = "customer_name_en", StringLength = 2000)]
        public string CustomerNameEn { get; set; }

        [Column(Name = "customer_name_other", StringLength = 300)]
        public string CustomerNameOther { get; set; }

        [Column(Name = "district", StringLength = 50)]
        public string District { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [Column(Name = "email", StringLength = 200)]
        public string? Email { get; set; }

        /// <summary>
        /// 传真
        /// </summary>
        [Column(Name = "fax", StringLength = 100)]
        public string Fax { get; set; }

        /// <summary>
        /// （未使用？）
        /// </summary>
        [Column(Name = "head_user_id1", StringLength = 50)]
        public string HeadUserId1 { get; set; }

        /// <summary>
        /// 案源类别( personal 表示个案,public表示公案)
        /// </summary>
        [Column(Name = "head_user_type", StringLength = 50)]
        public string HeadUserType { get; set; }

        /// <summary>
        /// 所属行业
        /// </summary>
        [Column(Name = "industry", StringLength = 50)]
        public string Industry { get; set; }

        /// <summary>
        /// （未使用？）
        /// </summary>
        [Column(Name = "introducer", StringLength = 50)]
        public string Introducer { get; set; }

        /// <summary>
        /// （未使用？）
        /// </summary>
        [Column(Name = "introducer_tel", StringLength = 50)]
        public string IntroducerTel { get; set; }

        [Column(Name = "is_applicant")]
        public bool? IsApplicant { get; set; } = false;

        /// <summary>
        /// 利益冲突
        /// </summary>
        [Column(Name = "is_conflict")]
        public bool? IsConflict { get; set; } = false;

        [Column(Name = "is_cooperation")]
        public bool? IsCooperation { get; set; } = false;

        /// <summary>
        /// 是否有效
        /// </summary>
        [Column(Name = "is_enabled")]
        public bool? IsEnabled { get; set; } = true;

        [Column(Name = "is_import")]
        public bool IsImport { get; set; } = false;

        [Column(Name = "manage_company", StringLength = 50)]
        public string ManageCompany { get; set; }

        [Column(Name = "parent_id", StringLength = 50)]
        public string? ParentId { get; set; }

        [Column(Name = "pay_way", StringLength = 50)]
        public string PayWay { get; set; }

        [Column(Name = "payment_name", StringLength = 50)]
        public string PaymentName { get; set; }

        [Column(Name = "receive_rule", StringLength = 50)]
        public string ReceiveRule { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column(Name = "remark", StringLength = 2000)]
        public string Remark { get; set; }

        /// <summary>
        /// 保密等级
        /// </summary>
        [Column(Name = "secret_id", StringLength = 50)]
        public string SecretId { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [Column(Name = "tel", StringLength = 100)]
        public string Tel { get; set; }

        /// <summary>
        /// 申请人类型
        /// </summary>
        [Column(Name = "type_id", StringLength = 50)]
        public string TypeId { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column(Name = "update_time")]
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 更新人（关联）
        /// </summary>
        [Column(Name = "update_user_id", StringLength = 50)]
        public string UpdateUserId { get; set; }

        [Column(Name = "website", StringLength = 1000)]
        public string Website { get; set; }

        /// <summary>
        /// 所属大区
        /// </summary>
        [Column(Name = "Big_Area", StringLength = 20)]
        public string BigArea { get; set; }

        /// <summary>
        /// 客户分类
        /// </summary>
        [Column(Name = "customer_type", StringLength = 50)]
        public string CustomerType { get; set; }

        /// <summary>
        /// 客户案源跟案人信息
        /// </summary>
        [Navigate(nameof(CustomerId))]
        public virtual ICollection<CusFollowList> CustomerFollows { get; set; } = default!;



        /// <summary>
        /// 团队信息导航
        /// </summary>
        [Navigate(nameof(CustomerId), TempPrimary = (nameof(SysTeamCustomer.CustomerId)))]
        public SysTeamCustomer TeamCustomer { get; set; }

    }

}
