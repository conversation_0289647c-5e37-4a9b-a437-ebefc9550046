using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ud_task_info_config", DisableSyncStructure = true)]
	public partial class UdTaskInfoConfig {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "columns_id", StringLength = 50)]
		public string ColumnsId { get; set; }

		[ Column(Name = "columns_name", StringLength = 50)]
		public string ColumnsName { get; set; }

		/// <summary>
		/// 该栏位是否有导入
		/// </summary>
		[ Column(Name = "is_import")]
		public bool? IsImport { get; set; }

		[ Column(Name = "is_key_id")]
		public bool? IsKeyId { get; set; }

		/// <summary>
		/// 是否必填
		/// </summary>
		[ Column(Name = "is_not_null")]
		public bool? IsNotNull { get; set; }

		/// <summary>
		/// 是否更新该栏位
		/// </summary>
		[ Column(Name = "is_update")]
		public bool? IsUpdate { get; set; }

		[ Column(Name = "task_id", StringLength = 50)]
		public string TaskId { get; set; }

	}

}
