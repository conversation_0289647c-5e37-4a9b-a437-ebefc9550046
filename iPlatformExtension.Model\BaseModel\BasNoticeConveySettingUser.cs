using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_notice_convey_setting_user", DisableSyncStructure = true)]
	public partial class BasNoticeConveySettingUser {

		[ Column(Name = "convey_setting_id", StringLength = 50)]
		public string ConveySettingId { get; set; }

		[ Column(Name = "create_date")]
		public DateTime? CreateDate { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; }

		[ Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "user_source", StringLength = 50)]
		public string UserSource { get; set; }

		[ Column(Name = "user_type", StringLength = 50)]
		public string UserType { get; set; }

	}

}
