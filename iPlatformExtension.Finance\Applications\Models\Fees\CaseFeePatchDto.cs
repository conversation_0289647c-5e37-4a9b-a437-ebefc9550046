﻿using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Finance.Applications.Models.Fees;

/// <summary>
/// 费项批量更新DTO
/// </summary>
public class CaseFeePatchDto
{
    /// <summary>
    /// 费项id
    /// </summary>
    public required string FeeId { get; set; }

    /// <summary>
    /// 缴官费通知书核查状态
    /// </summary>
    public bool? OfficialNotificationChecked { get; set; }

    /// <summary>
    /// 待支出清单核查状态
    /// </summary>
    public bool? PaymentListChecked { get; set; }
    
    /// <summary>
    /// 官费标识
    /// </summary>
    public OfficialFeeMark? OfficialFeeMark { get; set; }
    
    /// <summary>
    /// 官费标识说明
    /// </summary>
    public string? OfficialFeeMarkDescription { get; set; }

    /// <summary>
    /// 缴费状态
    /// </summary>
    public OfficialPaymentStatus? OfficialPaymentStatus { get; set; }

    /// <summary>
    /// 不缴费说明
    /// </summary>
    public string? NotPaymentInstructions { get; set; }

    /// <summary>
    /// 缴费期限
    /// </summary>
    public DateTime? PayOfficerLegalDate { get; set; }

    /// <summary>
    /// 缴费发文日期
    /// </summary>
    public DateTime? OfficialPaymentPublicationDate { get; set; }

    /// <summary>
    /// 缴费日期
    /// </summary>
    public DateTime? PayOfficerDate { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}