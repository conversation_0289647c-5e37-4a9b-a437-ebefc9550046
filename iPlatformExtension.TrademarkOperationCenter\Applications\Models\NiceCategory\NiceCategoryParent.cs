﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;

/// <summary>
/// 尼斯分类
/// </summary>
/// <remarks>含有子项</remarks>
/// <param name="CategoryId">分类id</param>
/// <param name="CategoryNumber">分类编号</param>
/// <param name="CategoryName">分类名称</param>
/// <param name="Selected">是否选中</param>
public record NiceCategoryParent(string CategoryId, string CategoryNumber, string CategoryName, bool Selected) 
    : NiceCategoryChild(CategoryId, CategoryNumber, CategoryName, Selected)
{
    /// <summary>
    /// 分类子项
    /// </summary>
    public IEnumerable<NiceCategoryChild> Children { get; set; } = Array.Empty<NiceCategoryParent>();

    /// <summary>
    /// 非标准分类
    /// </summary>
    public IEnumerable<string> CustomCategories { get; set; } = Array.Empty<string>();
}