using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_promptness_cus_ap_rate", DisableSyncStructure = true)]
	public partial class RpPromptnessCusApRate {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "apply_type", StringLength = 50)]
		public string ApplyType { get; set; }

		[ Column(Name = "case_type", StringLength = 50)]
		public string CaseType { get; set; }

		[ Column(Name = "customer_name", StringLength = 500)]
		public string CustomerName { get; set; }

		[ Column(Name = "finish_num")]
		public int? FinishNum { get; set; }

		[ Column(Name = "month", StringLength = 5)]
		public string Month { get; set; }

		[ Column(Name = "over_num")]
		public int? OverNum { get; set; }

		[ Column(Name = "quarter", StringLength = 5)]
		public string Quarter { get; set; }

		[ Column(Name = "year", StringLength = 5)]
		public string Year { get; set; }

	}

}
