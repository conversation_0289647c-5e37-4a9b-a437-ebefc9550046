﻿using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.MailCenter.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class ContentManageController(IMediator mediator) : ControllerBase
    {

        [HttpDelete]
        public async Task<IActionResult> test()
        {
            return Ok();
        }


        /// <summary>
        /// 保存我的个人分类列表
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        [HttpPost("SaveFlowPrivate")]
        public async Task SaveFlowPrivate([FromBody] SaveFlowPrivateCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 删除我的分类
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        /// 
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        [HttpPost("DeleteFlowPrivate")]
        public async Task DeleteFlowPrivate([FromQuery] DeleteFlowPrivateCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 待办设置分类
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        [HttpPost("SetFlowPrivate")]
        public async Task SetFlowPrivate([FromBody] SetFlowPirvateCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 获取我的分类标签
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        [HttpGet("GetMyFlowPrivate")]
        public async Task<List<MailFlowPrivate>> GetMyFlowPrivate([FromQuery] GetMyFlowPrivateQuery query)
        {
            return await mediator.Send(query);
        }


        /// <summary>
        /// 保存我的标签列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        [HttpPost("SaveMailTag")]
        public async Task SaveMailTag([FromBody] SaveMailTagCommand query)
        {
            await mediator.Send(query);
        }


        /// <summary>
        /// 删除我的标签
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("DeleteMailTag")]
        public async Task DeleteMailTag([FromQuery] DeleteMailTagCommand query)
        {
            await mediator.Send(query);
        }

        /// <summary>
        /// 邮件设置标签
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("SetMailTag")]
        public async Task SetMailTag([FromBody] SetMailTagCommand query)
        {
            await mediator.Send(query);
        }

        /// <summary>
        /// 获取邮件的标签
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetTagListByMailId")]
        public async Task<List<MailTagDto>> GetTagList([FromQuery] GetTagListByMailIdQuery query)
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取邮件列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetMailListByTag")]
        public async Task<PageResult<MailListByTagDto>> GetMailListByTag([FromQuery] GetMailListByTagQuery query)
        {
            return await mediator.Send(query);
        }



        /// <summary>
        /// 获取我的标签
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetTagList")]
        public async Task<List<MailTagDto>> GetMailListByTag([FromQuery] GetTagListQuery query)
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 设置阅读人
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("AddReader")]
        public async Task AddReader([FromBody] AddReaderCommand readers)
        {
            await mediator.Send(readers);
        }

        /// <summary>
        /// 获取我的待阅读列表(收件)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetMyReadList")]
        public async Task<PageResult<ReadListDto>> GetMyReadList([FromQuery] GetMyReadListQuery query)
        {
            return await mediator.Send(query);
        }



        /// <summary>
        /// 获取我的待阅读历史(收件)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetMyReadHistoryList")]
        public async Task<PageResult<ReadListDto>> GetMyReadHistoryList([FromQuery] GetMyReadHistoryListQuery query)
        {

            return await mediator.Send(query);
        }


        /// <summary>
        /// 获取我的待阅读列表(发件)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetMyReadBySendList")]
        public async Task<PageResult<ReadListDto>> GetMyReadBySendList([FromQuery] GetMyReadListBySendQuery query)
        {
            return await mediator.Send(query);
        }



        /// <summary>
        /// 获取我的待阅读历史(发件)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetMyReadHistoryBySendList")]
        public async Task<PageResult<ReadListDto>> GetMyReadHistoryList([FromQuery] GetMyReadHistoryListBySendQuery query)
        {
            return await mediator.Send(query);
        }



        /// <summary>
        /// 获取邮件阅读列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetReadListByMail")]
        public async Task<PageResult<GetReadListByMailDto>> GetReadListByMail([FromQuery] GetReadListByMailQuery query)
        {
            return await mediator.Send(query);
        }


        /// <summary>
        /// 删除未读的阅读人
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("DeleteReader")]
        public async Task DeleteReader([FromBody] DeleteReaderCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 设置阅读状态
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("UpdateReader")]
        public async Task UpdateReader([FromBody] UpdateReaderCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 获取我的分类数量
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetFlowPrivateTodo")]
        public async Task<List<FlowAnalyseDto>> GetFlowPrivateTodo()
        {
            return await mediator.Send(new GetFlowPrivateQuery());
        }
    }
}
