using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "con_batch_relation_notice", DisableSyncStructure = true)]
	public partial class ConBatchRelationNotice {

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; }

		[ Column(Name = "notice_id", StringLength = 50, IsNullable = false)]
		public string NoticeId { get; set; }

	}

}
