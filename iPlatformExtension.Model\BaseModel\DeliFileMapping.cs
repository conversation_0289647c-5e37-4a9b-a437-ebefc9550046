using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_file_mapping", DisableSyncStructure = true)]
	public partial class DeliFileMapping {

		[ Column(Name = "file_desc", StringLength = 50)]
		public string FileDesc { get; set; }

		[ Column(Name = "file_id", StringLength = 50)]
		public string FileId { get; set; }

		[ Column(Name = "file_setting_id", StringLength = 50)]
		public string FileSettingId { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
