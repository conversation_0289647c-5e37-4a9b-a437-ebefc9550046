﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Components.Forms;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 获取关联邮件查询
    /// </summary>
    internal sealed class GetRelateMailListQueryHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IMediator mediator,
        IHttpContextAccessor content
    ) : IRequestHandler<GetRelateMailListQuery, IEnumerable<GetRelateMailListDto>>
    {
        public async Task<IEnumerable<GetRelateMailListDto>> Handle(
            GetRelateMailListQuery request,
            CancellationToken cancellationToken
        )
        {
            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            string[]? mailNoList = null;
            if (request.MailNo is not null)
            {
                mailNoList = request.MailNo.Replace(" ", ";").Split(";");
            }
            var hostIds = await mediator.Send(
                new GetUserAccessQuery(userId, null),
                cancellationToken
            );

            List<string>? mailCorrelatives = new List<string>();
            if (!string.IsNullOrWhiteSpace(request.MailId))
            {
                mailCorrelatives = await freeSql
                    .Select<MailCorrelative>()
                    .Where(it =>
                        (it.MailId == request.MailId || it.ObjId == request.MailId)
                        && (
                            it.CorrelateType == SysEnum.CorrelateType.ReceiveMail.ToString()
                            || it.CorrelateType == SysEnum.CorrelateType.SendMail.ToString()
                        )
                    )
                    .ToListAsync(it => it.ObjId, cancellationToken);
            }

            var receiveTemp = freeSql
                .Select<MailReceive, MailReceiveFlow>()
                .WithLock()
                .LeftJoin(it => it.t1.MailId == it.t2.MailId)
                .WhereIf(
                    request.MailFrom is not null,
                    it => it.t1.MailFrom.Contains(request.MailFrom)
                )
                .WhereIf(request.MailNo is not null, it => mailNoList.Contains(it.t1.MailNo))
                .WhereIf(
                    request.MailSubject is not null,
                    it => it.t1.MailSubject.Contains(request.MailSubject)
                )
                .WhereIf(mailCorrelatives.Count > 0, it => !mailCorrelatives.Contains(it.t1.MailId))
                .Where(it =>
                    hostIds.HostId.Contains(it.t1.HostId)
                    || it.t2.UndertakeUserId == userId
                    || it.t2.IgnoreBy == userId
                    || it.t2.SortBy == userId
                )
                .WithTempQuery(it => new GetRelateMailListDto(
                    it.t1.MailId,
                    "'收件'",
                    it.t1.MailNo,
                    it.t1.MailFrom,
                    it.t1.MailSubject,
                    it.t1.CreateTime,
                    it.t1.MailDate
                ));

            var sendTemp = freeSql
                .Select<MailSend>()
                .WithLock()
                .WhereIf(request.MailFrom is not null, it => it.MailFrom.Contains(request.MailFrom))
                .WhereIf(request.MailNo is not null, it => mailNoList.Contains(it.MailNo))
                .WhereIf(
                    request.MailSubject is not null,
                    it => it.MailSubject.Contains(request.MailSubject)
                )
                .WhereIf(mailCorrelatives.Count > 0, it => !mailCorrelatives.Contains(it.MailId))
                .Where(it => hostIds.HostId.Contains(it.HostId))
                .WithTempQuery(it => new GetRelateMailListDto(
                    it.MailId,
                    "'发件'",
                    it.MailNo,
                    it.MailFrom,
                    it.MailSubject,
                    it.CreateTime,
                    it.MailDate
                ));

            var list = await receiveTemp
                .UnionAll(sendTemp)
                .OrderByDescending(it => it.MailNo)
                .Page(request.PageIndex!.Value, request.PageSize!.Value)
                .Count(out var totalCount)
                .ToListAsync(cancellationToken);
            return new PageResult<GetRelateMailListDto>()
            {
                Data = list,
                Page = request.PageIndex.Value,
                PageSize = request.PageSize.Value,
                Total = totalCount,
            };
        }
    }
}
