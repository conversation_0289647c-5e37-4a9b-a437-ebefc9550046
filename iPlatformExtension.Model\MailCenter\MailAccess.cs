﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_access", DisableSyncStructure = true)]
	public partial class MailAccess {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 访问权限,查看:read/发件:write/分拣:sorter/管理:manager
		/// </summary>
		[Column(Name = "access_mode", StringLength = 50, IsNullable = false)]
		public string AccessMode { get; set; }

		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 邮箱id
		/// </summary>
		[Column(Name = "host_id", StringLength = 50, IsNullable = false)]
		public string HostId { get; set; }

		[Column(Name = "update_by", StringLength = 50)]
		public string UpdateBy { get; set; }

		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 对应权限使用类型具体ID:
		/// 部门:sys_dept_info.dept_id/
		/// 个人用户:sys_user_Info.user_id/
		/// 角色:sys_role_info.role_id
		/// </summary>
		[Column(Name = "use_id", StringLength = 50, IsNullable = false)]
		public string UseId { get; set; }

		/// <summary>
		/// 权限使用类型,部门:dept/个人用户:user/角色:role
		/// </summary>
		[Column(Name = "use_type", StringLength = 50, IsNullable = false)]
		public string UseType { get; set; }

	}

}
