﻿using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using MediatR;

namespace iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;

/// <summary>
/// 获取标签名称
/// </summary>
/// <param name="DictionaryName">字典值：trademark_tab_out（国外商标）trademark_tab（国内商标）</param>
public record GetMyTrademarkCasePrivateQuery(string? DictionaryName) : IRequest<IEnumerable<GetMyTrademarkCasePrivateDto>>;

