﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.Proc;

/// <summary>
/// 任务额外数据项
/// </summary>
public sealed class ProcExtensionItem
{
    /// <summary>
    /// 显示名称
    /// </summary>
    public string DisplayName { get; set; } = null!;

    /// <summary>
    /// 具体值
    /// </summary>
    public object? Value { get; set; }

    /// <summary>
    /// 前端显示值
    /// </summary>
    public object? DisplayValue { get; set; }

    /// <summary>
    /// 键名
    /// </summary>
    public string Key { get; set; } = null!;

    /// <summary>
    /// 类型
    /// </summary>
    public string Type { get; set; } = null!;

    /// <summary>
    /// 列表表头。
    /// 针对集合属性需要
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public IEnumerable<ProcExtensionListHeader>? Headers { get; set; }

    /// <summary>
    /// 是否显示
    /// </summary>
    public bool IsShow { get; set; } = true;

    /// <summary>
    /// 是否只读
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public bool? IsReadonly { get; set; }
}