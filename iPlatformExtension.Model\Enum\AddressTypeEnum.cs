﻿

namespace iPlatformExtension.Model.Enum
{
    /// <summary>
    /// 邮件用户类型
    /// </summary>
    public static class AddressTypeEnum
    {
        /// <summary>
        /// 收件人
        /// </summary>
        public const string To = "to";

        /// <summary>
        /// 抄送
        /// </summary>
        public const string Cc = "cc";

        /// <summary>
        /// 发件人
        /// </summary>
        public const string From = "from";

        /// <summary>
        /// 密送
        /// </summary>
        public const string Bcc = "bcc";
    }
}
