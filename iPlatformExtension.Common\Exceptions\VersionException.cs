﻿namespace iPlatformExtension.Common.Exceptions;

public class VersionException : ApplicationException
{
    public VersionException(int currentVersion)
    {
        CurrentVersion = currentVersion;
    }

    public VersionException(int currentVersion, int actualVersion)
    {
        CurrentVersion = currentVersion;
        ActualVersion = actualVersion;
    }

    public int CurrentVersion { get; }

    public int? ActualVersion { get; }

    public override string Message
    {
        get
        {
            var actualVersionTips = string.Empty;
            if (ActualVersion.HasValue)
            {
                actualVersionTips = $"实际版本号：{ActualVersion}。";
            }

            return $"当前获取版本号为{CurrentVersion}，与实际版本号不符!{actualVersionTips}数据可能已更新，请重新刷新页面";
        }
    }
}