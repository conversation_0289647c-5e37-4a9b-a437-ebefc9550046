using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;

namespace iPlatformExtension.MailCenterRepository.Implement
{
    internal class MapConfigRepository(
        IFreeSql<MailCenterFreeSql> fsql,
        UnitOfWorkManage<MailCenterFreeSql> manager)
        : DefaultRepository<MapConfig, string>(fsql, manager), IMapConfigRepository;
} 