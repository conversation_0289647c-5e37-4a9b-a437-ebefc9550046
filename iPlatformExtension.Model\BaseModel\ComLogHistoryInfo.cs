using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "com_log_history_info", DisableSyncStructure = true)]
	public partial class ComLogHistoryInfo {

		[ Column(Name = "info_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string InfoId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "error_code", StringLength = 50)]
		public string ErrorCode { get; set; }

		[ Column(Name = "error_message", StringLength = 1000)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "finish_time")]
		public DateTime? FinishTime { get; set; }

		[ Column(Name = "history_id", StringLength = 50)]
		public string HistoryId { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; }

		[ Column(Name = "rel_id", StringLength = 50, IsNullable = false)]
		public string RelId { get; set; }

		[ Column(Name = "start_time")]
		public DateTime? StartTime { get; set; }

		[ Column(Name = "status", StringLength = 50)]
		public string Status { get; set; }

	}

}
