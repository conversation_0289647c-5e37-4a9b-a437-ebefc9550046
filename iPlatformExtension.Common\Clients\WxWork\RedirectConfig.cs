using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.Clients.WxWork
{
    /// <summary>
    /// 页面重定向跳转配置
    /// </summary>
    public class RedirectConfig
    {
        public string code { get; set; }
        public string id { get; set; }
        public string title { get; set; }
        public string search { get; set; }
    }
}
