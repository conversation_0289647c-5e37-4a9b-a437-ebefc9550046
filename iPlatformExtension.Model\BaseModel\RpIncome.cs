using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_income", DisableSyncStructure = true)]
	public partial class RpIncome {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		[ Column(Name = "a_fee", DbType = "money")]
		public decimal? AFee { get; set; }

		[ Column(Name = "business_dept", StringLength = 50)]
		public string BusinessDept { get; set; }

		[ Column(Name = "business_dept_seq")]
		public int? BusinessDeptSeq { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "district_seq")]
		public int? DistrictSeq { get; set; }

		[ Column(Name = "manage_company", StringLength = 50)]
		public string ManageCompany { get; set; }

		[ Column(Name = "month")]
		public int? Month { get; set; }

		[ Column(Name = "o_fee", DbType = "money")]
		public decimal? OFee { get; set; }

		[ Column(Name = "t_fee", DbType = "money")]
		public decimal? TFee { get; set; }

		[ Column(Name = "total_fee", DbType = "money")]
		public decimal? TotalFee { get; set; }

		[ Column(Name = "year")]
		public int? Year { get; set; }

	}

}
