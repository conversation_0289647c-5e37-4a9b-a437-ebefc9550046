﻿using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultProcStatusQueryHandler(ISystemDictionaryRepository systemDictionaryRepository)
    : IRequestHandler<FeeResultProcStatusQuery>
{
    public async Task Handle(FeeResultProcStatusQuery request, CancellationToken cancellationToken)
    {
        foreach (var feeListItemDto in request.FeeResults)
        {
            feeListItemDto.ProcStatus =
                await systemDictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.ProcStatus,
                    feeListItemDto.ProcStatusId);
        }
    }
}