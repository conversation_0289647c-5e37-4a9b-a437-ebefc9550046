using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_export", DisableSyncStructure = true)]
	public partial class SysExport {

		[ Column(Name = "export_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ExportId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "export_code", StringLength = 50)]
		public string ExportCode { get; set; }

		[ Column(Name = "export_name_en_us", StringLength = 200)]
		public string ExportNameEnUs { get; set; }

		[ Column(Name = "export_name_ja_jp", StringLength = 200)]
		public string ExportNameJaJp { get; set; }

		[ Column(Name = "export_name_zh_cn", StringLength = 50)]
		public string ExportNameZhCn { get; set; }

		[ Column(Name = "is_head")]
		public bool? IsHead { get; set; } = false;

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "sql", StringLength = -2)]
		public string Sql { get; set; }

		[ Column(Name = "template_file", StringLength = 50)]
		public string TemplateFile { get; set; }

	}

}
