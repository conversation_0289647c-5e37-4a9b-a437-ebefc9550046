﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 权大师订单操作参数
/// </summary>
public class PhoenixOrderOperationParameters : PhoenixRequestParameters
{
    /// <summary>
    /// 尼斯分类号字符串
    /// </summary>
    [JsonPropertyName("cls")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? NiceClassNumbers { get; set; }

    /// <summary>
    /// 权大师订单号
    /// </summary>
    [JsonPropertyName("orderNo")]
    public string OrderNo { get; set; } = default!;
}