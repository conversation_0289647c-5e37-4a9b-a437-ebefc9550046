﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.Clients.WxWork
{
    public class WxMessage
    {
        /// <summary>
        /// 成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向关注该企业应用的全部成员发送
        /// </summary>
        public string touser { get; set; }

        /// <summary>
        /// 部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数
        /// </summary>
        public string? toparty { get; set; }

        /// <summary>
        /// 标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数
        /// </summary>
        public string? totag { get; set; }
        /// <summary>
        /// 必填,消息类型，默认：markdown
        /// </summary>
        public string msgtype { get; set; } = "markdown";

        /// <summary>
        /// 企业应用的id。
        /// </summary>
        public string agentid { get; set; }
        /// <summary>
        /// 	markdown内容，最长不超过2048个字节，必须是utf8编码
        /// </summary>

        /// <summary>
        /// 非必填,表示是否开启重复消息检查，0表示否，1表示是，默认0
        /// </summary>
        public int? enable_duplicate_check { get; set; }
        /// <summary>
        /// 非必填,表示是否重复消息检查的时间间隔，默认1800s，最大不超过4小时
        /// </summary>
        public int? duplicate_check_interval { get; set; }
    }
}
