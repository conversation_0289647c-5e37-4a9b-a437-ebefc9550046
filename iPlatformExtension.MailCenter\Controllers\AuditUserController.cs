﻿using System.ComponentModel;
using iPlatformExtension.MailCenter.Applications.Commands.AuditUser;
using iPlatformExtension.MailCenter.Applications.Models.AuditUser;
using iPlatformExtension.MailCenter.Applications.Queries.AuditUser;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using ModelContextProtocol.Server;

namespace iPlatformExtension.MailCenter.Controllers
{
    /// <summary>
    /// 审核用户控制器
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    [McpServerToolType]
    public class AuditUserController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// 获取发件必审人列表
        /// </summary>
        /// <param name="auditingName">必审人名称</param>
        /// <param name="designatedAuditName">指定审核人名称</param>
        /// <returns>发件必审人列表</returns>
        [HttpGet("GetFlowAuditUserList"), McpServerTool, Description("获取发件必审人列表")]
        public async Task<IEnumerable<GetFlowAuditUserDto>> GetFlowAuditUserList(
            [FromQuery, Description("必审人名称")] string? auditingName = null,
            [FromQuery, Description("指定审核人名称")] string? designatedAuditName = null)
        {
            return await mediator.Send(new GetFlowAuditUserQuery(auditingName, designatedAuditName));
        }

        /// <summary>
        /// 获取发件必审人详情
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>发件必审人详情</returns>
        [HttpGet("GetFlowAuditUserDetail/{id}"), McpServerTool, Description("获取发件必审人详情")]
        public async Task<GetFlowAuditUserDto?> GetFlowAuditUserDetail([Description("记录ID")] string id)
        {
            return await mediator.Send(new GetFlowAuditUserDetailQuery(id));
        }

        /// <summary>
        /// 保存发件必审人
        /// </summary>
        /// <param name="command">保存命令</param>
        /// <returns>记录ID</returns>
        /// <remarks>
        /// ID为空表示新增，非空表示编辑
        /// 只需要传入必审人ID列表和指定审核人ID列表，无需传入名称
        /// </remarks>
        [HttpPost("SaveFlowAuditUser"), McpServerTool, Description("保存发件必审人")]
        public async Task<string> SaveFlowAuditUser([FromBody, Description("保存命令")] SaveFlowAuditUserCommand command)
        {
            return await mediator.Send(command);
        }

        /// <summary>
        /// 删除发件必审人
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>是否删除成功</returns>
        [HttpDelete("DeleteFlowAuditUser/{id}"), McpServerTool, Description("删除发件必审人")]
        public async Task<bool> DeleteFlowAuditUser([Description("记录ID")] string id)
        {
            return await mediator.Send(new DeleteFlowAuditUserCommand(id));
        }

        /// <summary>
        /// 获取发件审核人列表
        /// </summary>
        /// <param name="auditingName">审核人名称</param>
        /// <returns>发件审核人列表</returns>
        [HttpGet("GetAuditUserList"), McpServerTool, Description("获取发件审核人列表")]
        public async Task<IEnumerable<GetFlowAuditUserListDto>> GetAuditUserList(
            [FromQuery, Description("审核人名称")] string? auditingName = null)
        {
            return await mediator.Send(new GetFlowAuditUserListQuery(auditingName));
        }

        /// <summary>
        /// 保存发件审核人
        /// </summary>
        /// <param name="command">保存命令</param>
        /// <returns>记录ID列表</returns>
        /// <remarks>
        /// 可以传入多个审核人ID或名称，将会创建多条记录
        /// </remarks>
        [HttpPost("SaveAuditUser"), McpServerTool, Description("保存发件审核人")]
        public async Task<List<string>> SaveAuditUser([FromBody, Description("保存命令")] SaveAuditUserCommand command)
        {
            return await mediator.Send(command);
        }

        /// <summary>
        /// 删除发件审核人
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>是否删除成功</returns>
        [HttpDelete("DeleteAuditUser/{id}"), McpServerTool, Description("删除发件审核人")]
        public async Task<bool> DeleteAuditUser([Description("记录ID")] string id)
        {
            return await mediator.Send(new DeleteAuditUserCommand(id));
        }

        /// <summary>
        /// 获取审核必选人信息
        /// </summary>
        /// <param name="userId">用户ID，如果提供则检查该用户是否是必选人，否则检查是否有必选人配置</param>
        /// <returns>审核必选人信息</returns>
        [HttpGet("GetAuditRequired"),McpServerTool,Description("获取审核必选人信息")]
        public async Task<GetAuditRequiredDto> GetAuditRequired([FromQuery, Description("用户ID，如果提供则检查该用户是否是必选人，否则检查是否有必选人配置")] string? userId = null)
        {
            return await mediator.Send(new GetAuditRequiredQuery(userId));
        }
    }
}
