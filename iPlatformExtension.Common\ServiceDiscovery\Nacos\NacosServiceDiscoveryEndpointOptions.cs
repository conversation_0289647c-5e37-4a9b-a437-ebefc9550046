﻿using Nacos.V2.Common;

namespace iPlatformExtension.Common.ServiceDiscovery.Nacos;

public class NacosServiceDiscoveryEndpointOptions<TOptions>
{
    /// <summary>
    /// 服务名
    /// </summary>
    public string ServiceName { get; set; } = string.Empty;

    /// <summary>
    /// 组名
    /// </summary>
    public string GroupName { get; set; } = Constants.DEFAULT_GROUP;

    /// <summary>
    /// 传输协议
    /// </summary>
    public string UriScheme { get; set; } = Uri.UriSchemeHttp;

    /// <summary>
    /// 
    /// </summary>
    public Action<IServiceProvider, TOptions>? ConfigureOptions { get; set; }

    public void Deconstruct(out string serviceName, out string groupName, out string uriScheme)
    {
        serviceName = ServiceName;
        groupName = GroupName;
        uriScheme = UriScheme;
    }
}