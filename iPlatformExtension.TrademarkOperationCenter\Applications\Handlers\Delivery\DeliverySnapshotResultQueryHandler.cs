﻿using AutoMapper;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DeliverySnapshotResultQueryHandler(
    IMapper mapper,
    IMediator mediator,
    IApplyTypeRepository applyTypeRepository,
    IApplicantTypeRepository applicantTypeRepository,
    ISystemDictionaryRepository dictionaryRepository,
    IBaseCountryRepository countryRepository,
    ICustomerRepository customerRepository,
    IBaseCtrlProcRepository procRepository,
    IUserInfoRepository infoRepository)
    : IRequestHandler<DeliverySnapshotResultQuery, TrademarkDeliveryDto>
{
    public async Task<TrademarkDeliveryDto> Handle(DeliverySnapshotResultQuery request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request.DeliveryInfo);

        ICacheableRepository<string, UserBaseInfo> userInfoRepository = infoRepository;

        var deliveryInfo = request.DeliveryInfo;
        deliveryInfo.ApplyTypeId = (await applyTypeRepository.GetChineseKeyValueAsync(deliveryInfo.ApplyTypeId)).Value;
        deliveryInfo.CustomerId =
            await customerRepository.Select.Where(customer => customer.CustomerId == deliveryInfo.CustomerId)
                .ToOneAsync(customer => customer.CustomerName, cancellationToken) ?? string.Empty;
        
        var ctrlProcId = deliveryInfo.CtrlProcId;

        if (deliveryInfo.Applicants is not null)
        {
            foreach (var applicant in deliveryInfo.Applicants)
            {
                applicant.TypeId =
                    (await applicantTypeRepository.GetCacheValueAsync(applicant.TypeId.GetOrDefaultEmpty()))?.ApplicantTypeZhCn ??
                    string.Empty;
                applicant.CardType =
                    (await dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.CardType,
                        applicant.CardType)).Value;
                applicant.CountryId = (await countryRepository.GetCacheValueAsync(applicant.CountryId ?? string.Empty))
                    ?.CountryZhCn ?? string.Empty;
                applicant.DeliveryBusinessType = (await dictionaryRepository.GetChineseKeyValueAsync(
                        SystemDictionaryName.TrademarkDeliveryBusinessType,
                        applicant.DeliveryBusinessType ?? string.Empty))
                    .Value;
            }
        }

        if (deliveryInfo.Priorities is not null)
        {
            foreach (var priority in deliveryInfo.Priorities)
            {
                priority.CountryId = (await countryRepository.GetCacheValueAsync(priority.CountryId))
                    ?.CountryZhCn ?? string.Empty;
            }
        }
        
        

        deliveryInfo.DeliveryKey =
            (await dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.DeliveryKey,
                deliveryInfo.DeliveryKey)).Value;

        deliveryInfo.ApplicationType =
            (await dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.ApplicationType,
                deliveryInfo.ApplicationType)).Value;

        var dto = mapper.Map<TrademarkDeliveryDto>(deliveryInfo);
        dto.UndertakerName = (await userInfoRepository.GetCacheValueAsync(deliveryInfo.UndertakerId))?.CnName ??
                             string.Empty;
        dto.TrademarkInfoSnapshot.CtrlProcName = await procRepository.GetChineseValueAsync(ctrlProcId) ?? string.Empty;

        await mediator.Publish(new OtherInfoResultQuery(ctrlProcId, dto), cancellationToken);

        return dto;
    }
}