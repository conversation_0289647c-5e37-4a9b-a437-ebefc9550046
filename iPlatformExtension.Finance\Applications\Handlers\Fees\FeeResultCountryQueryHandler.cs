﻿using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultCountryQueryHandler(IBaseCountryRepository countryRepository)
    : IRequestHandler<FeeResultCountryQuery>
{
    public async Task Handle(FeeResultCountryQuery request, CancellationToken cancellationToken)
    {
        foreach (var feeListItemDto in request.FeeResults)
        {
            feeListItemDto.CountryName =
                (await countryRepository.GetCacheValueAsync(feeListItemDto.CountryId))?.CountryZhCn ?? string.Empty;
            feeListItemDto.CustomerCountryName =
                (await countryRepository.GetCacheValueAsync(feeListItemDto.CustomerCountryId))?.CountryZhCn ?? string.Empty;
        }
    }
}