﻿using System.Linq.Expressions;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class ProcExtensionDynamicQueryHandler(IFreeSql freeSql, EntityTypeInfoProvider typeInfoProvider)
    : IRequestHandler<ProcExtensionDynamicQuery, CaseProcInfo?>
{
    public Task<CaseProcInfo?> Handle(ProcExtensionDynamicQuery request, CancellationToken cancellationToken)
    {
        var query = freeSql.Select<CaseProcInfo>(request.ProcId).WithLock();
        var configs = request.Configs;

        if (!configs.Any())
        {
            return Task.FromResult(default(CaseProcInfo));
        }
        
        var entityTypeInfo = typeInfoProvider.Get(typeof(CaseProcInfo));
        var type = entityTypeInfo.TypeInfo.AsType();
        
        var memberBindings = new List<MemberBinding>(configs.Count());
        var parameterExpression = Expression.Parameter(type, "info");

        var procIdExpression = Expression.Property(parameterExpression, nameof(CaseProcInfo.ProcId));
        memberBindings.Add(Expression.Bind(type.GetProperty(nameof(CaseProcInfo.ProcId))!, procIdExpression));
        
        foreach (var dynamicConfig in configs.Where(config => config.BelongClassName == type.Name))
        {
            if (dynamicConfig.Include != NavigationType.None)
            {
                query.IncludeByPropertyName(dynamicConfig.PropertyName);
            }
            else if (dynamicConfig.PropertyName == nameof(CaseProcInfo.ProcId))
            {
            }
            else
            {
                var propertyExpression = Expression.Property(parameterExpression, dynamicConfig.PropertyName);
                memberBindings.Add(Expression.Bind(type.GetProperty(dynamicConfig.PropertyName)!,
                    propertyExpression));
            }
        }
        
        var memberInitExpression = Expression.MemberInit(Expression.New(type), memberBindings);
        var selectExpression = Expression.Lambda<Func<CaseProcInfo, CaseProcInfo>>(memberInitExpression, parameterExpression);

        return query.ToOneAsync(selectExpression, cancellationToken)!;
    }
}