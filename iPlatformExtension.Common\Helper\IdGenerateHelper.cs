﻿using System.Collections;

namespace iPlatformExtension.Common.Helper
{
    public class TimestampAndRandom
    {
        /// <summary>
        /// 随机数缓存
        /// </summary>
        private static Hashtable ht;

        /// <summary>
        /// 时间戳刻度缓存
        /// </summary>
        private long lastTimeStampStyleTicks;

        /// <summary>
        /// 时间戳缓存（上一次计算ID的系统时间按时间戳刻度取值）
        /// </summary>
        private long lastEndDateTimeTicks;

        public TimestampAndRandom()
        {
            if (ht == null)
                ht = new Hashtable();
        }

        /// <summary>
        /// IdentityGenerator的静态实例
        /// </summary>
        private static readonly TimestampAndRandom ig = new TimestampAndRandom();

        private static readonly object lockObj = new object();

        /// <summary>
        /// 静态随机数生成器
        /// </summary>
        private static Random random;

        /// <summary>
        /// 时间刻度
        /// </summary>
        public enum TimestampStyle : long
        {
            /// <summary>
            /// 时间刻度精度取为1毫秒（此项无意义，因为一般PC机系统时钟只能精确到10毫秒）
            /// </summary>
            MillSecondTicks = 10000,

            /// <summary>
            /// 时间刻度精度取为10毫秒，这是一般PC机系统时钟的最小精度单位
            /// </summary>
            TenMillSecondTicks = 100000,

            /// <summary>
            /// 时间刻度精度取为100毫秒
            /// </summary>
            HundredMillSecondTicks = 1000000,

            /// <summary>
            /// 时间刻度精度取为1秒，即1000毫秒
            /// </summary>
            SecondTicks = 10000000,

            /// <summary>
            /// 时间刻度精度取为5秒
            /// </summary>
            FiveSecondTicks = 50000000,

            /// <summary>
            /// 时间刻度精度取为10秒
            /// </summary>
            TenSecondTicks = 100000000,

            /// <summary>
            /// 时间刻度精度取为1分种（60秒）
            /// </summary>
            MinutesTicks = 600000000
        }

        /// <summary>
        /// 起始时间
        /// </summary>
        public static readonly DateTime startDateTime = new DateTime(2017, 1, 1, 0, 0, 0, 0);

        /// <summary>
        /// 获取随机数
        /// </summary>
        /// <param name="length">随机数长度</param>
        /// <returns></returns>
        public long GetRandom(int length)
        {
            if (length <= 0)
                throw new Exception("随机数长度指派错误，长度不能为0或负数");
            if (random == null)
                random = new Random(Guid.NewGuid().GetHashCode());

            int minValue = 0;
            int maxValue = int.Parse(System.Math.Pow(10, length).ToString());
            long result = long.Parse(random.Next(minValue, maxValue).ToString());
            return result;
        }

        /// <summary>
        /// 按照时间戳刻度计算当前时间戳
        /// </summary>
        /// <param name="startDateTime">起始时间</param>
        /// <param name="timestampStyleTicks">时间戳刻度值</param>
        /// <returns>long</returns>
        private long GetTimestamp(DateTime startDateTime, long timestampStyleTicks)
        {
            if (timestampStyleTicks == 0)
                throw new Exception("时间戳刻度样式精度值不符，不能为0或负数");
            DateTime endDateTime = DateTime.Now;
            long ticks = (endDateTime.Ticks - startDateTime.Ticks) / timestampStyleTicks;
            return ticks;
        }

        /// <summary>
        /// 根据指定长度的尾随机数和时间戳返回一个编码
        /// </summary>
        /// <param name="randomLength">随机数长度</param>
        /// <returns>long</returns>
        public long GetIdentity(int randomLength)
        {
            //定义起始时间
            //DateTime startDateTime = new DateTime(2015, 1, 1, 0, 0, 0, 0);
            //时间戳刻度
            //TimestampStyle timestampStyle = TimestampStyle.SecondTicks;
            TimestampStyle timestampStyle = TimestampStyle.HundredMillSecondTicks;
            long timestampStyleTicks = long.Parse(timestampStyle.ToString("D"));

            //return GetIdentity(startDateTime, timestampStyleTicks, randomLength);

            /***开始计算一个id***/
            long result = 0;
            lock (lockObj)
            {
                //新一轮时间戳刻度更新后更新缓存
                //如果该参数不变则不进行此更新
                if (timestampStyleTicks != lastTimeStampStyleTicks)
                    ht.Clear();

                //取得时间戳（当前时间按刻度取值）
                long timestamp = GetTimestamp(startDateTime, timestampStyleTicks);

                //新一轮时间戳更新后更新缓存
                if (timestamp != lastEndDateTimeTicks)
                    ht.Clear();
                //幂
                long power = long.Parse(Math.Pow(10, randomLength).ToString());
                //随机数
                long rand = GetRandom(randomLength);
                //生成结果（Id）
                result = timestamp * power + rand;
                //long result = rand;

                //如果发现重复
                if (ht.ContainsKey(result))
                {
                    bool find = false;
                    //在随机数长度范围内再重复查找一次
                    for (int i = 0; i < power; i++)
                    {
                        rand = GetRandom(randomLength);
                        result = timestamp * power + rand;
                        //发现非重复的Id
                        if (!ht.ContainsKey(result))
                        {
                            //将新的Id加入HashTable缓存
                            ht.Add(result, result);
                            find = true;
                            break;//找到一个同一时间戳内的Id即退出
                        }
                    }
                    //此处运行在当前时间戳内无法再继续生成Id的代码，如：
                    //
                    //throw new Exception("已无法生成更多Id，请增加时间戳刻度TimestampStyle或增加随机数长度randomLength");
                    if (!find)
                        throw new Exception("已无法生成更多Id");
                }
                else
                {
                    //将新的Id加入HashTable缓存
                    ht.Add(result, result);
                }
                //记录当前一轮时间戳（当前时间按刻度取值）
                this.lastEndDateTimeTicks = timestamp;
                //记录当前一轮时间戳刻度
                this.lastTimeStampStyleTicks = timestampStyleTicks;
            }

            return result;
        }

        public static string GenerateId()
        {
            return ig.GetIdentity(5).ToString();
        }
    }
}
