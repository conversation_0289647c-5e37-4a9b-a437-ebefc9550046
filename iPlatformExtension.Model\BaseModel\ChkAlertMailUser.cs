using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "chk_alert_mail_user", DisableSyncStructure = true)]
	public partial class ChkAlertMailUser {

		[ Column(Name = "address_type", StringLength = 50, IsNullable = false)]
		public string AddressType { get; set; }

		[ Column(Name = "display_name", StringLength = 50)]
		public string DisplayName { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "mail_address", StringLength = 200, IsNullable = false)]
		public string MailAddress { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; }

	}

}
