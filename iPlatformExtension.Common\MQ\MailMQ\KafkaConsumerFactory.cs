﻿using Confluent.Kafka;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace iPlatformExtension.Common.MQ.MailMQ
{
    public class KafkaConsumerFactory<TKey, TValue>
    {
        //private readonly KafkaConsumerSettings _config;
        private readonly IOptionsMonitor<KafkaConsumerSettings> _config;
        private readonly ILogger<KafkaConsumerFactory<TKey, TValue>> _logger;

        public KafkaConsumerFactory(IOptionsMonitor<KafkaConsumerSettings> config, ILogger<KafkaConsumerFactory<TKey, TValue>> logger)
        {
            _config = config;
            _logger = logger;
        }

        public IConsumer<TKey, TValue> CreateConsumer(string configName)
        {
            var setting = _config.Get(configName);
            var consumerConfig = new ConsumerConfig
            {
                BootstrapServers = setting.BootstrapServers,
                GroupId = setting.GroupId,
                AutoOffsetReset = AutoOffsetReset.Earliest
            };
            var jsonSerializerOptions = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var consumerBuilder = new ConsumerBuilder<TKey, TValue>(consumerConfig)
                  .SetKeyDeserializer(new CustomStringSerializer<TKey>(jsonSerializerOptions))
                  //  .SetKeyDeserializer((IDeserializer<TKey>)Deserializers.Null)
                .SetValueDeserializer(new ValueByUtf8Converter<TValue>()); ;
            consumerBuilder.SetLogHandler((_, m) =>
            {
                _logger.Log((LogLevel)m.LevelAs(LogLevelType.MicrosoftExtensionsLogging), $"KafkaConsumerMessage: {m?.Message}");
            });
            return consumerBuilder.Build();
        }
    }
}
