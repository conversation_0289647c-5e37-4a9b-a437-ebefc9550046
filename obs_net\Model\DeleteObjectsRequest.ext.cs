﻿/*----------------------------------------------------------------------------------
// Copyright 2019 Huawei Technologies Co.,Ltd.
// Licensed under the Apache License, Version 2.0 (the "License"); you may not use
// this file except in compliance with the License.  You may obtain a copy of the
// License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations under the License.
//----------------------------------------------------------------------------------*/
using System.Collections.Generic;

namespace OBS.Model
{
    public partial class DeleteObjectsRequest : ObsBucketWebServiceRequest
    {

        public void AddKey(string key)
        {
            AddKey(new KeyVersion { Key = key });
        }

        public void AddKey(string key, string versionId)
        {
            KeyVersion kv = new KeyVersion();
            kv.Key = key;
            kv.VersionId = versionId;
            AddKey(kv);
        }

        private void AddKey(KeyVersion keyVersion)
        {
            this.Objects.Add(keyVersion);
        }
    }
}
