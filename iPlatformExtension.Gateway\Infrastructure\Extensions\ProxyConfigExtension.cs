﻿using System.Runtime.CompilerServices;
using iPlatformExtension.Gateway.Nacos;
using Yarp.ReverseProxy.Configuration;

namespace iPlatformExtension.Gateway.Infrastructure.Extensions;

internal static class ProxyConfigExtension
{
    [UnsafeAccessor(UnsafeAccessorKind.Field, Name = "_routeConfigs")]
    private static extern ref Dictionary<string, RouteConfig> GetRouteConfigs(NacosNamingProxyConfigProvider nacosConfigProvider);
    
    [UnsafeAccessor(UnsafeAccessorKind.Field, Name = "_clusterConfigs")]
    private static extern ref Dictionary<string, ClusterConfig> GetClusterConfigs(NacosNamingProxyConfigProvider nacosConfigProvider);
    
    public static Dictionary<string, RouteConfig>? GetRouteConfigs(this IProxyConfigProvider proxyConfigProvider)
    {
        if (proxyConfigProvider is NacosNamingProxyConfigProvider nacosConfigProvider)
        {
            return GetRouteConfigs(nacosConfigProvider);
        }
        return null;
    }

    public static Dictionary<string, ClusterConfig>? GetClusterConfigs(this IProxyConfigProvider proxyConfigProvider)
    {
        if (proxyConfigProvider is NacosNamingProxyConfigProvider nacosConfigProvider)
        {
            return GetClusterConfigs(nacosConfigProvider);
        }
        
        return null;
    }
}