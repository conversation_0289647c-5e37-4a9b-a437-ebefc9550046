using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_empty_send_official_date", DisableSyncStructure = true)]
	public partial class VwEmptySendOfficialDate {

		[ Column(StringLength = 50, IsNullable = false)]
		public string 案件标识 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 案件官方状态 { get; set; }

		[ Column(StringLength = 500, IsNullable = false)]
		public string 案件名称 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 案件内部状态 { get; set; }

		[ Column(StringLength = 50)]
		public string 承办人 { get; set; }

		[ Column(StringLength = 500, IsNullable = false)]
		public string 承办人部门 { get; set; }

		[ Column(StringLength = 10, IsNullable = false)]
		public string 官方期限 { get; set; }

		[ Column(StringLength = 1000, IsNullable = false)]
		public string 客户名称 { get; set; }

		[ Column(StringLength = 4000)]
		public string 任务备注 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 任务标识 { get; set; }

		[ Column(StringLength = 50)]
		public string 任务名称 { get; set; }

		[ Column(StringLength = 1000, IsNullable = false)]
		public string 任务属性 { get; set; }

		[ Column(StringLength = 10)]
		public string 任务完成日期 { get; set; }

		[ Column(StringLength = 50)]
		public string 任务状态 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 申请号 { get; set; }

		[ Column(StringLength = 50)]
		public string 我方文号 { get; set; }

		[ Column(StringLength = 50)]
		public string 业务类型 { get; set; }

	}

}
