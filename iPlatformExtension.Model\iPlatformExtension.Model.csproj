<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>iPlatformExtension.Model</RootNamespace>
    <LangVersion>preview</LangVersion>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DocumentationFile>bin/Debug/net8.0/iPlatformExtension.Model.xml</DocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DocumentationFile>bin/Release/net8.0/iPlatformExtension.Model.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FreeSql" Version="3.5.105" />
    <PackageReference Include="MessagePack.Annotations" Version="2.5.192" />
    <PackageReference Include="MongoDB.Bson" Version="3.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="obj\**" />
    <Compile Remove="Dto\KeyValue.cs" />
    <Compile Remove="Dto\TrademarkDelivery\TrademarkDeliveryDto.cs" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Remove="obj\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="obj\**" />
    <None Remove="iPlatformExtension.Model.xml" />
  </ItemGroup>


</Project>
