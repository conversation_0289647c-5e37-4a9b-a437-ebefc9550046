﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow
{
    [Serializable]
    public class FlowInfoQuery : IRequest<GetFlowInfoDto?>
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string ObjId { get; set; }

        /// <summary>
        /// 流程类型
        /// </summary>
        public string FlowType { get; set; }

        /// <summary>
        /// 案件类型
        /// </summary>
        public string FlowSubType { get; set; }

        ///// <summary>
        ///// 部门ID
        ///// </summary>
        //public string DeptID { get; set; }
    }
}
