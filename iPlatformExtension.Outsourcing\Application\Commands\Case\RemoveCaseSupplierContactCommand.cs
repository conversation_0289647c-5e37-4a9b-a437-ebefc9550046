﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Commands.Case;

public sealed record RemoveCaseSupplierContactCommand(
    [property: Required, Description("案件id")]string CaseId, 
    [property: Required, Description("联系人id")]string ContactId) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;