using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal class InvoiceConfigurationRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    CacheExpirationToken<IGrouping<KingdeeMaterialTaxationKey, KingdeeMaterialTaxationInfo>> expirationToken)
    : BaseRepository<FinancialInvoiceAliasConfig, string>(freeSql), IInvoiceAliasConfigurationRepository
{
    // ((ICacheableRepository<KingdeeMaterialTaxationKey, IGrouping<KingdeeMaterialTaxationKey, KingdeeMaterialTaxationInfo>>)this).SetTokenName(nameof(KingdeeMaterialTaxationInfo));

    public IMemoryCache MemoryCache { get; } = memoryCache;

    public CacheExpirationToken<IGrouping<KingdeeMaterialTaxationKey, KingdeeMaterialTaxationInfo>> ExpirationToken { get; } = expirationToken;
}