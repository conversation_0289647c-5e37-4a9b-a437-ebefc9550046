﻿namespace iPlatformExtension.Public.Applications.Models.Flow
{
    public class MQSource
    {
        public string version { get; set; }
        public string connector { get; set; }
        public string name { get; set; }
        public string ts_ms { get; set; }
        public string snapshot { get; set; }
        public string db { get; set; }
        public string sequence { get; set; }
        public string schema { get; set; }
        public string table { get; set; }
        public string change_lsn { get; set; }
        public string commit_lsn { get; set; }
        public string event_serial_no { get; set; }
    }
}
