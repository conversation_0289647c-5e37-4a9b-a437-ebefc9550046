﻿using System.Collections.Concurrent;
using iPlatformExtension.Common.Clients.Mail;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Common.ObjectPools.SMTP;

public sealed class SmtpClientPool(SmtpClientPooledObjectPolicy policy, PooledSmtpClientBackgroundService backgroundService) 
    : ObjectPool<ScopedPooledSmtpClient>
{
    private readonly Func<ScopedPooledSmtpClient> _createFunc = policy.Create;
    
    private readonly Func<ScopedPooledSmtpClient, bool> _returnFunc = policy.Return;
    
    private readonly int _maxCapacity = policy.MailAccountOptions.PooledCount - 1;
    
    private int _numItems;
    
    private readonly ConcurrentQueue<ScopedPooledSmtpClient> _items = new ();

    private readonly ConcurrentQueue<ScopedPooledSmtpClient> _expiredClients = backgroundService.ExpiredClients;
    
    private ScopedPooledSmtpClient? _fastItem;

    public override ScopedPooledSmtpClient Get()
    {
        ScopedPooledSmtpClient client;
        while (!TryGet(out client))
        {
            _expiredClients.Enqueue(client);
        }

        return client;
    }

    private bool TryGet(out ScopedPooledSmtpClient client)
    {
        client = GetInternal();
        try
        {
            client.NoOp();
        }
        catch (Exception e)
        {
            client.IsExpired = true;
        }
        return !client.IsExpired;
    }

    private ScopedPooledSmtpClient GetInternal()
    {
        var result = _fastItem;
        if (result != null && Interlocked.CompareExchange(ref _fastItem, default, result) == result)
            return result;
        if (!_items.TryDequeue(out result))
            return _createFunc();
        Interlocked.Decrement(ref _numItems);
        return result;
    }
        
    public override void Return(ScopedPooledSmtpClient obj)
    {
        if (!_returnFunc(obj))
            return ;
        if (_fastItem == null && Interlocked.CompareExchange(ref _fastItem, obj, default) == null)
            return ;
        if (Interlocked.Increment(ref _numItems) <= _maxCapacity)
        {
            _items.Enqueue(obj);
        }
        else
        {
            Interlocked.Decrement(ref _numItems);
            obj.Accessor = null;
            obj.Dispose();
        }
    }
}