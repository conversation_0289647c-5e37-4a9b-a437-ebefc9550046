﻿namespace iPlatformExtension.Finance.Infrastructure;

/// <summary>
/// 日志扩展
/// </summary>
public static partial class LoggingExtension
{
    
    /// <summary>
    /// 记录汇率同步消息过期
    /// </summary>
    /// <param name="logger">日志器</param>
    /// <param name="currentTime">当前时间</param>
    /// <param name="beginTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    [LoggerMessage(LogLevel.Warning, "汇率同步消息过期！当前时间为：{CurrentTime}。有效时间为[{BeginTime}]到[{EndTime}]")]
    public static partial void LogCurrencyRateOverdue(this ILogger logger, DateTime currentTime, DateTime beginTime,
        DateTime endTime);

    /// <summary>
    /// 打印无效汇率日志
    /// </summary>
    /// <param name="logger">日志器</param>
    /// <param name="id">消息id</param>
    /// <param name="sourceCurrency">源币种</param>
    /// <param name="targetCurrency">目标币种</param>
    [LoggerMessage(LogLevel.Warning, "id为[{Id}]的汇率信息无效。源币种：{SourceCurrency}，目标币种：{TargetCurrency}")]
    public static partial void LogIgnoreInvalidCurrencyRate(this ILogger logger, string id, string sourceCurrency,
        string targetCurrency);
}