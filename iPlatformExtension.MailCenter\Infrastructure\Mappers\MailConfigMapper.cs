﻿using AutoMapper;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenter.Applications.Models.Analysis;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.Model.MailCenter;

namespace iPlatformExtension.MailCenter.Infrastructure.Mappers
{
    /// <summary>
    /// 邮箱规则
    /// </summary>
    public class MailConfigMapper : Profile
    {
        /// <summary>
        /// 配置
        /// </summary>
        public MailConfigMapper()
        {
            CreateMap<SaveAnalysisRuleCommand, MailConfig>();
            CreateMap<AnalysisRuleDetail, MailConfigFilter>();
        }
    }
}
