﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Public.Applications.Models.Agency;
using iPlatformExtension.Public.Applications.Queries.Agency;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Agency;

internal sealed class AgencyTrademarkDeliveryQueryHandler(IFreeSql freeSql, ISystemDictionaryRepository dictionaryRepository) : 
    IRequestHandler<AgencyTrademarkDeliveryQuery, IEnumerable<AgencyTrademarkDeliveryInfoDto>>
{
    public async Task<IEnumerable<AgencyTrademarkDeliveryInfoDto>> Handle(AgencyTrademarkDeliveryQuery request, CancellationToken cancellationToken)
    {
        var (keyword, agencyId, isEnabled) = request;
        var agencies = await freeSql.Select<BasAgency>().WithLock()
            .WhereIf(!string.IsNullOrWhiteSpace(keyword),
                agency => agency.AgencyNameCn.Contains(keyword!) || agency.AgencyNameEn.Contains(keyword!))
            .WhereIf(!string.IsNullOrWhiteSpace(agencyId), agency => agency.AgencyId == agencyId)
            .WhereIf(isEnabled.HasValue, agency => agency.IsEnabled == isEnabled)
            .ToListAsync(agency => new AgencyTrademarkDeliveryInfoDto
            {
                AgencyId = agency.AgencyId,
                AgencyNameCn = agency.AgencyNameCn,
                AgencyNameEn = agency.AgencyNameEn,
                TrademarkDeliveryContactor = agency.TrademarkContactor,
                TrademarkMailbox = agency.TrademarkMailbox,
                TrademarkTel = agency.TrademarkTel,
                DeliverKey = agency.PhoenixDeliveryKey,
                PostCode = agency.PostCode
            }, cancellationToken);

        for (var i = 0; i < agencies.Count; i++)
        {
            var agency = agencies[i];
            var deliveryKey = agency.DeliverKey;
            
            if (string.IsNullOrWhiteSpace(deliveryKey)) 
                continue;
            
            var deliveryKeys = deliveryKey.Split(';',
                StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries);
            agency.DeliveryKeys = await deliveryKeys.ToAsyncEnumerable()
                .SelectAwait(key =>
                    dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.DeliveryKey, key))
                .ToArrayAsync(cancellationToken);
        }

        return agencies;
    }
}