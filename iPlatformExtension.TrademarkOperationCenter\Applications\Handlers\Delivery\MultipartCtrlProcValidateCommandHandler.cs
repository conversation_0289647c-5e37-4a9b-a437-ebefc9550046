﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class MultipartCtrlProcValidateCommandHandler : IRequestHandler<MultipartCtrlProcValidateCommand, BatchDeliveryValidateResult>
{
    public Task<BatchDeliveryValidateResult> Handle(MultipartCtrlProcValidateCommand request, CancellationToken cancellationToken)
    {
        var validResult = new BatchDeliveryValidateResult(request.ValidationItems.Count())
        {
            Success = request.ValidationItems.GroupBy(dto => dto.CtrlProcId).Count() == 1
        };

        return Task.FromResult(validResult.Success ? validResult : validResult.MultipartCtrlProc());
    }
}