﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{
    /// <summary>
    /// 页签详情配置
    /// </summary>
    [ Table(Name = "sys_paper_tab", DisableSyncStructure = true)]
    public class SysPaperTab
    {
        [ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

        [ Column(Name = "dictionary_id", StringLength = 50)]
        public string DictionaryId { get; set; }

        [ Column(Name = "case_direction", StringLength = 50)]
        public string CaseDirection { get; set; }

        [ Column(Name = "ctrl_proc_id", StringLength = 50)]
        public string CtrlProcId { get; set; }

        [ Column(Name = "update_time")]
        public DateTime? UpdateTime { get; set; }

        [ Column(Name = "update_user_id", StringLength = 50)]
        public string UpdateUserId { get; set; }

        [ Column(Name = "create_time")]
        public DateTime? CreateTime { get; set; }

        [ Column(Name = "is_enable")]
        public bool IsEnable { get; set; }

        [ Column(Name = "create_user_id", StringLength = 50)]
        public string CreateUserId { get; set; }

    }
    
}
