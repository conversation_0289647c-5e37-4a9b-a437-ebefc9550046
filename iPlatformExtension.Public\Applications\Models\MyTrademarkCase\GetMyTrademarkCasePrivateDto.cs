﻿using System.Collections.Generic;

namespace iPlatformExtension.Public.Applications.Models.MyTrademarkCase;

/// <summary>
/// 获取个人标签
/// </summary>
/// <param name="DictionaryId">字典id</param>
/// <param name="DictionaryName">字典类型</param>
/// <param name="DictionaryDesc">字典描述</param>
/// <param name="TextZhCn">页签中文名</param>
/// <param name="PrivateValue">页签值</param>
public record GetMyTrademarkCasePrivateDto(string? DictionaryId,string DictionaryName,string DictionaryDesc,string TextZhCn,string PrivateValue);

