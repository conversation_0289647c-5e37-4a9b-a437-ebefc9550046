﻿using Microsoft.AspNetCore.SignalR;

namespace iPlatformExtension.Public.Infrastructure.SignalR
{
    public class FlowTickerHub : Hub
    {
 

        public async Task SendMessage(string method, string message)
        {
            ///FlowMessage
            await Clients.All.SendAsync(method, message);

            string name = Context.User.Identity.Name;

            foreach (var connectionId in _connections.GetConnections(name))
            {
                // Clients.Client(connectionId).addChatMessage(name + ": " + message);
                await Clients.Client(connectionId).SendAsync(method, message);
            }
        }

        private readonly static ConnectionMapping<string> _connections =
           new ConnectionMapping<string>();


        public override Task OnConnectedAsync()
        {
            string name = Context.User.Identity.Name;

            _connections.Add(name, Context.ConnectionId);

            return base.OnConnectedAsync();
        }


        public override Task OnDisconnectedAsync(Exception? exception)
        {
            string name = Context.User.Identity.Name;

            _connections.Remove(name, Context.ConnectionId);
            return base.OnDisconnectedAsync(exception);
        }

    }
}
