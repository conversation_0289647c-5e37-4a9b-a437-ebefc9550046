using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_proc_config_column", DisableSyncStructure = true)]
	public partial class SysProcConfigColumn {

		[ Column(Name = "column_name", StringLength = 50)]
		public string ColumnName { get; set; }

		[ Column(Name = "column_value1", StringLength = 4000)]
		public string ColumnValue1 { get; set; }

		[ Column(Name = "column_value2", StringLength = 200)]
		public string ColumnValue2 { get; set; }

		[ Column(Name = "column_value3", StringLength = 200)]
		public string ColumnValue3 { get; set; }

		[ Column(Name = "column_value4", StringLength = 200)]
		public string ColumnValue4 { get; set; }

		[ Column(Name = "config_id", StringLength = 50)]
		public string ConfigId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
