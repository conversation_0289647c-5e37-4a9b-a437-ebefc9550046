﻿using AutoMapper;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class UpdateProcCommandHandler(
    IMapper mapper,
    IHttpContextAccessor httpContextAccessor,
    ICaseProcInfoRepository caseProcInfoRepository)
    : IRequestHandler<UpdateProcCommand, CaseProcInfo>
{
    public async Task<CaseProcInfo> Handle(UpdateProcCommand request, CancellationToken cancellationToken)
    {
        var (procId, pathDocument, version) = request;
        var procInfo = await caseProcInfoRepository.GetAsync(procId, cancellationToken);
        if (procInfo is null)
        {
            throw new NotFoundException(procId, "案件任务");
        }
        
        procInfo.ValidateVersion(version);

        var dto = mapper.Map<ProcTrademarkDeliveryDto>(procInfo);
        pathDocument.ApplyTo(dto);
        mapper.Map(dto, procInfo);
        
        var userId = (httpContextAccessor.HttpContext?.User).GetUserId();
        procInfo.UpdateUserId = userId;
        procInfo.UpdateTime = DateTime.Now;

        var updated = await caseProcInfoRepository.UpdateAsync(procInfo, cancellationToken);
        if (updated <= 0)
        {
            throw new ApplicationException("更新失败");
        }

        return procInfo;
    }
}