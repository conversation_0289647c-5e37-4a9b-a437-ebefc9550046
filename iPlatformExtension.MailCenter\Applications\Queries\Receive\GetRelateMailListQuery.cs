﻿using iPlatformExtension.MailCenter.Applications.Models;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.Receive;

/// <summary>
/// 获取关联邮件列表
/// </summary>
/// <param name="MailId">排除邮件id</param>
/// <param name="MailNo">邮箱编号</param>
/// <param name="MailFrom">发件人</param>
/// <param name="MailSubject">邮件主题</param>
/// <param name="MailType">邮件类型,Receive:收件,Send:发件</param>
public record GetRelateMailListQuery(string? MailId,string? MailNo, string? MailFrom, string? MailSubject,string? MailType = "Receive") : PageModel, IRequest<IEnumerable<GetRelateMailListDto>>;

