﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.Proc;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Proc;

internal sealed class CtrlProcQueryHandler(IFreeSql freeSql) : IRequestHandler<CtrlProcQuery, IEnumerable<CtrlProcInfo>>
{
    public async Task<IEnumerable<CtrlProcInfo>> Handle(CtrlProcQuery request, CancellationToken cancellationToken)
    {
        var (keyword, caseType, caseDirection, isEnabled) = request;

        return await freeSql.Select<BasCtrlProc>().WithLock()
            .LeftJoin<BasCtrlProcDirection>((proc, direction) => proc.CtrlProcId == direction.CtrlProcId)
            .WhereIf(!string.IsNullOrWhiteSpace(keyword),
                proc => proc.CtrlProcZhCn.Contains(keyword!)
                        || proc.CtrlProcEnUs.Contains(keyword!))
            .WhereIf<BasCtrlProc, BasCtrlProcDirection>(!string.IsNullOrWhiteSpace(caseDirection), 
                (proc, direction) => direction.CaseDirection == caseDirection)
            .WhereIf(!string.IsNullOrWhiteSpace(caseType), proc => proc.CaseTypeId == caseType)
            .WhereIf(isEnabled.HasValue, proc => proc.IsEnabled == request.IsEnabled)
            .GroupBy(proc => new CtrlProcInfo(proc.CtrlProcId, proc.CtrlProcZhCn, proc.CtrlProcEnUs)
            {
                IsEnabled = proc.IsEnabled
            })
            .ToListAsync(aggregate => aggregate.Key, cancellationToken);
    }
}