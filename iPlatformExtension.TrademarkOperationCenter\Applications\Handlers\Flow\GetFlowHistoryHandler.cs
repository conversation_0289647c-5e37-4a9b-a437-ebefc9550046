﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;
using iPlatformExtension.Common.Extensions;
using Confluent.Kafka;
using System.Xml.Linq;
using iPlatformExtension.Model.Enum;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public class GetFlowHistoryHandler : IRequestHandler<FlowHistoryQuery, GetFlowHistoryDto>
    {
        private readonly IFreeSql _freeSql;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public GetFlowHistoryHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor)
        {
            _freeSql = freeSql;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<GetFlowHistoryDto> Handle(FlowHistoryQuery request, CancellationToken cancellationToken)
        {
            var userid = _httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;

            var objId = request.ObjId;
            //todo:兼容业务系统流程显示,需优化
            if (request.FlowType == "EX")
            {
                objId = await _freeSql.Select<CaseProcFlow>().Where(o => o.ProcId == request.ObjId && o.FlowType == ProcFlowEnum.EX).ToOneAsync(o => o.ProcFlowId);
            }

            ArgumentNullException.ThrowIfNull(userid);
            var flowHistorySql = _freeSql.Select<SysFlowHistory, SysFlowNode, SysDictionary, SysUserInfo>()
                .InnerJoin((fa, node, di, user) => fa.NodeId == node.NodeId)
                .LeftJoin((fa, node, di, user) => fa.AuditTypeId == di.Value && di.DictionaryName == "audit_type")
                .LeftJoin((fa, node, di, user) => fa.AuditUserId == user.UserId)
                .Where((fa, node, di, user) => fa.ObjId == objId && fa.FlowType == request.FlowType && fa.FlowSubType == request.FlowSubType
                ).OrderBy((fa, node, di, user) => fa.AuditTime).WithLock();


            var flowHistoryList = await flowHistorySql.ToListAsync((fa, node, di, user) => new GetFlowHistoryList()
            {
                HistoryId = fa.HistoryId,
                ObjId = fa.ObjId,
                AuditCnName = user.CnName,
                AuditTime = fa.AuditTime,
                AuditTypeId = fa.AuditTypeId,
                AuditTypeZhCn = di.TextZhCn,
                NameZhCn = node.NameZhCn,
                NodeCode = node.NodeCode,
                NodeId = fa.NodeId,
                Remark = fa.Remark,
                AuditCnUserId = fa.AuditUserId,
                ProcFlowId = _freeSql.Select<CaseProcFlow>().Where(o => o.ProcId == fa.ObjId || o.ProcFlowId == fa.ObjId).ToOne(o => o.ProcFlowId),
                ProcId = _freeSql.Select<CaseProcFlow>().Where(o => o.ProcId == fa.ObjId || o.ProcFlowId == fa.ObjId).ToOne(o => o.ProcFlowId),
            }, cancellationToken);

            var flowActivitySql = _freeSql.Select<SysFlowActivity, SysFlowNode, SysUserInfo, SysUserInfo>()
                .InnerJoin((fa, node, user1, user2) => fa.CurNodeId == node.NodeId)
                .LeftJoin((fa, node, user1, user2) => fa.CurUserId == user1.UserId)
                .LeftJoin((fa, node, user1, user2) => fa.UpdateUserId == user2.UserId)
                .Where((fa, node, user1, user2) => fa.ObjId == objId && fa.FlowType == request.FlowType
                ).WithLock();


            var flowActivity = await flowActivitySql.ToOneAsync((fa, node, user1, user2) => new GetFlowActivity()
            {
                AuditCnName = user1.CnName,
                CurNodeId = fa.CurNodeId,
                CurUserId = fa.CurUserId,
                //AllowEdit = "",
                //FAllowEdit = "",
                //FMaxSeq = "",
                //FNext = "",
                FlowType = fa.FlowType,
                NameZhCn = node.NameZhCn,
                NodeCode = node.NodeCode,
                PrevAuditTypeId = fa.PrevAuditTypeId,
                Status = fa.Status,
                UpdateUser = user2.CnName,
                ObjId = fa.ObjId,

            }, cancellationToken);

            if (flowActivity != null && (flowActivity.Status == 0 || flowActivity.Status == 1000))
            {
                flowHistoryList.Add(new GetFlowHistoryList
                {
                    HistoryId = flowActivity.HistoryId,
                    ObjId = flowActivity.ObjId,
                    AuditCnName = flowActivity.AuditCnName,
                    AuditTime = flowActivity.AuditTime,
                    AuditTypeId = flowActivity.AuditTypeId,
                    AuditTypeZhCn = flowActivity.AuditTypeZhCn,
                    NameZhCn = flowActivity.NameZhCn,
                    NodeCode = flowActivity.NodeCode,
                    NodeId = flowActivity.CurNodeId,
                    Remark = flowActivity.Remark,
                    AuditCnUserId = flowActivity.CurUserId
                });
            }
            return new GetFlowHistoryDto()
            {
                FlowHistoryList = flowHistoryList,
                FlowActivity = flowActivity
            };
        }
    }
}
