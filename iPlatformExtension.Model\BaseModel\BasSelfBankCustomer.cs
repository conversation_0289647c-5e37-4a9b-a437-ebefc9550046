using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_self_bank_customer", DisableSyncStructure = true)]
	public partial class BasSelfBankCustomer {

		[ Column(Name = "bank_id", StringLength = 50)]
		public string BankId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50, IsNullable = false)]
		public string CustomerId { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; }

	}

}
