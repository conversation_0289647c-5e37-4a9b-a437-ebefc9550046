using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_delegation_template_data", DisableSyncStructure = true)]
	public partial class BasDelegationTemplateData {

		[ Column(Name = "data_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DataId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "data_name", StringLength = 50)]
		public string DataName { get; set; }

		[ Column(Name = "data_sql", StringLength = -2)]
		public string DataSql { get; set; }

		[ Column(Name = "list_name", StringLength = 250)]
		public string ListName { get; set; }

		[ Column(Name = "list_sql", StringLength = -2)]
		public string ListSql { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
