﻿using Google.Protobuf;
using Grpc.Core;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.GrpcServices;
using iPlatformExtension.Messages.Mails;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Statistics;
using iPlatformExtension.Public.Applications.Queries.Statistics;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;
using MiniExcelLibs;
using MongoDB.Bson;

namespace iPlatformExtension.Public.Applications.Handlers.Statistics
{
    /// <summary>
    /// 5应启动而未启动递交流程或管制完成的代理师任务的排查情况
    /// </summary>
    internal sealed class CheckDeliveryQueryHandler(
        IFreeSql freeSql,
        Notification.NotificationClient notificationClient,
        ILogger<CheckDeliveryQuery> logger,
        IBaseCtrlProcRepository iBaseCtrlProcRepository,
        IUserInfoRepository iUserInfoRepository,
        IBaseCaseStatusRepository iBaseCaseStatusRepository,
        ISystemDictionaryRepository dictionaryRepository,
        ICompanyRepository iCompanyRepository,
        IDepartmentInfoRepository departmentInfoRepository,
        IApplyTypeRepository applyTypeRepository,
        IHostEnvironment iHostEnvironment
    ) : IRequestHandler<CheckDeliveryQuery, IEnumerable<CheckDeliveryDto>>
    {
        public async Task<IEnumerable<CheckDeliveryDto>> Handle(
            CheckDeliveryQuery request,
            CancellationToken cancellationToken
        )
        {
            ICacheableRepository<string, UserBaseInfo> userInfoRepository = iUserInfoRepository;
            var caseProcInfoSelect = await freeSql
                .Select<CaseProcInfo>()
                .Where(it =>
                    (it.CaseInfo.CaseDirection == "II") 
                    || (
                        it.CaseInfo.CaseDirection == "IO"
                        && it.CaseInfo.ApplyTypeId == "8B9326B4-C566-4044-B017-CFAE68236F17"
                    )
                )
                .Where(it=> it.CaseInfo.CaseTypeId == "P")
                .Where(it=> it.FinishDate == null
                           && (it.FinishDocDate != null))
                .Where(it => !string.IsNullOrWhiteSpace(it.UndertakeUserId))
                .Where(it =>
                    it.SubProcStatus.TextZhCn != "递交中" || it.SubProcStatus.TextZhCn == null
                )
                .WithLock()
                .ToListAsync(
                    it => new FinishDateDelivery
                    {
                        Volume = it.CaseInfo.Volume,
                        ApplyTypeId = it.CaseInfo.ApplyTypeId,
                        CtrlProcId = it.CtrlProcId,
                        CtrlProcProperty = it.CtrlProcProperty,
                        ProcStatusId = it.ProcStatusId,
                        ProcNote = it.ProcNote,
                        UndertakeUserId = it.UndertakeUserId,
                        DeptId = it.UndertakeUserInfo.DeptId,
                        SubProcStatusValue = it.SubProcStatus.TextZhCn,
                        TitularWriteUser = it.TitularWriteUser,
                        CustomerName = it.CaseInfo.Customer.CustomerName,
                        ManageCompany = it.CaseInfo.ManageCompany,
                        BelongCompany = it.CaseInfo.BelongCompany,
                        FinishDocDate = it.FinishDocDate,
                    },
                    cancellationToken
                );

            var finishDateDeliveries = await caseProcInfoSelect
                .ToAsyncEnumerable()
                .SelectAwait(async procInfo =>
                {
                    if (!string.IsNullOrWhiteSpace(procInfo.CtrlProcId))
                    {
                        var baseCtrlProcInfo = await iBaseCtrlProcRepository.GetCacheValueAsync(
                            procInfo.CtrlProcId,
                            cancellationToken: cancellationToken
                        );
                        procInfo.CtrlProcZhCn = baseCtrlProcInfo?.CtrlProcZhCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.UndertakeUserId))
                    {
                        var userInfo = await userInfoRepository.GetCacheValueAsync(
                            procInfo.UndertakeUserId,
                            cancellationToken: cancellationToken
                        );
                        procInfo.UndertakeUserName = userInfo?.CnName;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.TitularWriteUser))
                    {
                        var userInfo = await userInfoRepository.GetCacheValueAsync(
                            procInfo.TitularWriteUser,
                            cancellationToken: cancellationToken
                        );
                        procInfo.TitularWriteUserName = userInfo?.CnName;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.CtrlProcProperty))
                    {
                        var baseBasCaseStatus = await iBaseCaseStatusRepository.GetCacheValueAsync(
                            procInfo.CtrlProcProperty,
                            cancellationToken: cancellationToken
                        );
                        procInfo.CtrlProcPropertyValue = baseBasCaseStatus?.CaseStatusZhCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.ProcStatusId))
                    {
                        var chineseKeyValueAsync =
                            await dictionaryRepository.GetChineseKeyValueAsync(
                                SystemDictionaryName.ProcStatus,
                                procInfo.ProcStatusId
                            );
                        procInfo.ProcStatusName = chineseKeyValueAsync.Value;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.BelongCompany))
                    {
                        var chineseKeyValueAsync = await iCompanyRepository.GetCacheValueAsync(
                            procInfo.BelongCompany,
                            cancellationToken: cancellationToken
                        );
                        procInfo.BelongCompanyValue = chineseKeyValueAsync?.CompanyNameCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.ManageCompany))
                    {
                        var chineseKeyValueAsync = await iCompanyRepository.GetCacheValueAsync(
                            procInfo.ManageCompany,
                            cancellationToken: cancellationToken
                        );
                        procInfo.ManageCompanyValue = chineseKeyValueAsync?.CompanyNameCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.DeptId))
                    {
                        var chineseKeyValueAsync =
                            await departmentInfoRepository.GetCacheValueAsync(
                                procInfo.DeptId,
                                cancellationToken: cancellationToken
                            );
                        procInfo.DeptName = chineseKeyValueAsync?.CnName;
                    }
                    if (!string.IsNullOrWhiteSpace(procInfo.ApplyTypeId))
                    {
                        var chineseKeyValueAsync = await applyTypeRepository.GetCacheValueAsync(
                            procInfo.ApplyTypeId,
                            cancellationToken: cancellationToken
                        );
                        procInfo.ApplyTypeValue = chineseKeyValueAsync?.ApplyTypeZhCn;
                    }

                    return procInfo;
                })
                .ToListAsync(cancellationToken);
            var firstDocDelivery = await freeSql
                .Select<CaseProcInfo>()
                .Where(it =>
                    (it.CaseInfo.CaseTypeId == "P"
                     && it.CaseInfo.CaseDirection == "II")
                    || (
                        it.CaseInfo.CaseDirection == "IO"
                        && it.CaseInfo.CaseTypeId == "P"
                        && it.CaseInfo.ApplyTypeId == "8B9326B4-C566-4044-B017-CFAE68236F17"
                    )
                )
                .Where(it => it.FinishDate == null
                             &&  it.FirstDocDate != null)
                .Where(it => !string.IsNullOrWhiteSpace(it.UndertakeUserId))
                .Where(it =>
                    it.SubProcStatus.TextZhCn != "递交中" || it.SubProcStatus.TextZhCn == null
                )
                .WithLock()
                .ToListAsync(
                    it => new FirstDocDelivery
                    {
                        Volume = it.CaseInfo.Volume,
                        ApplyTypeId = it.CaseInfo.ApplyTypeId,
                        CtrlProcId = it.CtrlProcId,
                        CtrlProcProperty = it.CtrlProcProperty,
                        ProcStatusId = it.ProcStatusId,
                        ProcNote = it.ProcNote,
                        UndertakeUserId = it.UndertakeUserId,
                        DeptId = it.UndertakeUserInfo.DeptId,
                        SubProcStatusValue = it.SubProcStatus.TextZhCn,
                        TitularWriteUser = it.TitularWriteUser,
                        CustomerName = it.CaseInfo.Customer.CustomerName,
                        ManageCompany = it.CaseInfo.ManageCompany,
                        BelongCompany = it.CaseInfo.BelongCompany,
                        FirstDocDate = it.FirstDocDate,
                    },
                    cancellationToken
                );

            var firstDocDeliverys = await firstDocDelivery
                .ToAsyncEnumerable()
                .SelectAwait(async procInfo =>
                {
                    if (!string.IsNullOrWhiteSpace(procInfo.CtrlProcId))
                    {
                        var baseCtrlProcInfo = await iBaseCtrlProcRepository.GetCacheValueAsync(
                            procInfo.CtrlProcId,
                            cancellationToken: cancellationToken
                        );
                        procInfo.CtrlProcZhCn = baseCtrlProcInfo?.CtrlProcZhCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.UndertakeUserId))
                    {
                        var userInfo = await userInfoRepository.GetCacheValueAsync(
                            procInfo.UndertakeUserId,
                            cancellationToken: cancellationToken
                        );
                        procInfo.UndertakeUserName = userInfo?.CnName;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.TitularWriteUser))
                    {
                        var userInfo = await userInfoRepository.GetCacheValueAsync(
                            procInfo.TitularWriteUser,
                            cancellationToken: cancellationToken
                        );
                        procInfo.TitularWriteUserName = userInfo?.CnName;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.CtrlProcProperty))
                    {
                        var baseBasCaseStatus = await iBaseCaseStatusRepository.GetCacheValueAsync(
                            procInfo.CtrlProcProperty,
                            cancellationToken: cancellationToken
                        );
                        procInfo.CtrlProcPropertyValue = baseBasCaseStatus?.CaseStatusZhCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.ProcStatusId))
                    {
                        var chineseKeyValueAsync =
                            await dictionaryRepository.GetChineseKeyValueAsync(
                                SystemDictionaryName.ProcStatus,
                                procInfo.ProcStatusId
                            );
                        procInfo.ProcStatusName = chineseKeyValueAsync.Value;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.BelongCompany))
                    {
                        var chineseKeyValueAsync = await iCompanyRepository.GetCacheValueAsync(
                            procInfo.BelongCompany,
                            cancellationToken: cancellationToken
                        );
                        procInfo.BelongCompanyValue = chineseKeyValueAsync?.CompanyNameCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.ManageCompany))
                    {
                        var chineseKeyValueAsync = await iCompanyRepository.GetCacheValueAsync(
                            procInfo.ManageCompany,
                            cancellationToken: cancellationToken
                        );
                        procInfo.ManageCompanyValue = chineseKeyValueAsync?.CompanyNameCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.DeptId))
                    {
                        var chineseKeyValueAsync =
                            await departmentInfoRepository.GetCacheValueAsync(
                                procInfo.DeptId,
                                cancellationToken: cancellationToken
                            );
                        procInfo.DeptName = chineseKeyValueAsync?.CnName;
                    }
                    if (!string.IsNullOrWhiteSpace(procInfo.ApplyTypeId))
                    {
                        var chineseKeyValueAsync = await applyTypeRepository.GetCacheValueAsync(
                            procInfo.ApplyTypeId,
                            cancellationToken: cancellationToken
                        );
                        procInfo.ApplyTypeValue = chineseKeyValueAsync?.ApplyTypeZhCn;
                    }
                    return procInfo;
                })
                .ToListAsync(cancellationToken);

            var underTakeUserList = finishDateDeliveries
                .Select(it => new { it.UndertakeUserId, it.UndertakeUserName })
                .ToList();
            underTakeUserList.AddRange(
                firstDocDeliverys.Select(it => new { it.UndertakeUserId, it.UndertakeUserName })
            );
            if (request.UserId is not null)
            {
                underTakeUserList = underTakeUserList
                    .Where(it => request.UserId.Contains(it.UndertakeUserId))
                    .ToList();
            }
            var asyncDuplexStreamingCall = notificationClient.NotifyAsync();
            var readTask = Task.Factory.StartNew(
                async () =>
                {
                    await foreach (
                        var response in asyncDuplexStreamingCall.ResponseStream.ReadAllAsync(
                            cancellationToken: cancellationToken
                        )
                    )
                    {
                        if (response is null)
                            continue;

                        var messageId = response.Data.UnpackToString();
                        if (response.Success)
                        {
                            logger.LogInformation(
                                "追踪Id：{TraceId}。邮件Id：{MessageId}。信息：{Message}",
                                response.TraceId,
                                messageId,
                                response.Message
                            );
                        }
                        else
                        {
                            logger.LogError(
                                "追踪Id：{TraceId}。邮件Id：{MessageId}。信息：{Message}",
                                response.TraceId,
                                messageId,
                                response.Message
                            );
                        }
                    }
                },
                cancellationToken
            );

            underTakeUserList = iHostEnvironment.IsProduction()
                ? underTakeUserList.Distinct().ToList()
                : underTakeUserList.Distinct().Where(it => new[]
                {
                    "ea937da8-85fe-498d-9843-17e7d616c692",
                    "c1597c51-781b-4682-81c7-e01dc23431ce",
                }.Contains(it.UndertakeUserId)).ToList();

            foreach (
                var underTakeUser in underTakeUserList.Select(it => it.UndertakeUserId).Distinct()
            )
            {
                if (
                    !finishDateDeliveries.Any(it => underTakeUser == it.UndertakeUserId)
                    && !firstDocDeliverys.Any(it => underTakeUser == it.UndertakeUserId)
                )
                    continue;

                var dictionary = new Dictionary<string, object>()
                {
                    ["完成初稿"] = firstDocDeliverys.Where(it =>
                        underTakeUser == it.UndertakeUserId
                    ),
                    ["完成定稿"] = finishDateDeliveries.Where(it =>
                        underTakeUser == it.UndertakeUserId
                    ),
                };

                var notificationMail = new NotificationMail
                {
                    Sender = "【正式版本】系统邮箱",
                    MessageId = Guid.NewGuid().ToString(),
                    BodyTemplate = BodyTemplate.CheckDelivery,
                };
                using (var stream = new MemoryStream())
                {
                    await stream.SaveAsAsync(
                        dictionary,
                        excelType: ExcelType.XLSX,
                        cancellationToken: cancellationToken
                    );
                    stream.Seek(0, SeekOrigin.Begin);

                    notificationMail.Attachments.Add(
                        new MailAttachment
                        {
                            FileName =
                                "应启动而未启动递交流程或管制完成的代理师任务的排查情况.xlsx",
                            Data = await ByteString.FromStreamAsync(stream, cancellationToken),
                        }
                    );
                    var receivers = iHostEnvironment.IsProduction()
                        ? [underTakeUser]
                        : new List<string>
                        {
                            "ea937da8-85fe-498d-9843-17e7d616c692",
                            "c1597c51-781b-4682-81c7-e01dc23431ce",
                        };
                    notificationMail.Receivers.AddRange(receivers);
                    logger.LogInformation(
                        notificationMail.ToJson() + notificationMail.Receivers.ToJson()
                    );
                    await asyncDuplexStreamingCall.RequestStream.WriteAsync(
                        notificationMail,
                        cancellationToken
                    );
                }
            }

            #region 汇总邮件


            var notificationMailTotal = new NotificationMail
            {
                Sender = "【正式版本】系统邮箱",
                MessageId = Guid.NewGuid().ToString(),
                BodyTemplate = BodyTemplate.CheckDelivery,
            };
            using (var stream = new MemoryStream())
            {
                await stream.SaveAsAsync(
                    new Dictionary<string, object>()
                    {
                        ["完成初稿"] = firstDocDeliverys,
                        ["完成定稿"] = finishDateDeliveries,
                    },
                    excelType: ExcelType.XLSX,
                    cancellationToken: cancellationToken
                );
                stream.Seek(0, SeekOrigin.Begin);

                notificationMailTotal.Attachments.Add(
                    new MailAttachment
                    {
                        FileName = "应启动而未启动递交流程或管制完成的代理师任务的排查情况.xlsx",
                        Data = await ByteString.FromStreamAsync(stream, cancellationToken),
                    }
                );
                var receivers = iHostEnvironment.IsProduction()
                    ? ["72743ed8-d6a4-4925-b6e4-e3e897ab6a30"]
                    : new List<string>
                    {
                        "ea937da8-85fe-498d-9843-17e7d616c692",
                        "c1597c51-781b-4682-81c7-e01dc23431ce",
                    };

                notificationMailTotal.Receivers.AddRange(receivers);

                if (iHostEnvironment.IsProduction())
                {
                    notificationMailTotal.ReceiverAccounts.AddRange(
                        new List<MailReceiverAccount>
                        {
                            new()
                            {
                                DisplayName = "流程部事业邮箱",
                                MailBoxAddress = "<EMAIL>",
                            },
                            new()
                            {
                                DisplayName = "华进国内专利流程",
                                MailBoxAddress = "<EMAIL>",
                            },
                        }
                    );
                }

                logger.LogInformation(
                    notificationMailTotal.ToJson() + notificationMailTotal.Receivers.ToJson()
                );
                await asyncDuplexStreamingCall.RequestStream.WriteAsync(
                    notificationMailTotal,
                    cancellationToken
                );
            }

            #endregion
            await asyncDuplexStreamingCall.RequestStream.CompleteAsync();

            await readTask;
            return new List<CheckDeliveryDto>();
        }
    }
}
