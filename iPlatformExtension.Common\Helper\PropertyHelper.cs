﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.Helper
{
    public static class PropertyHelper
    {
        /// <summary>
        /// 获取属性字段名称
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static IEnumerable<string> GetProperty<T>()
        {
            var propertyInfos = typeof(T).GetProperties();
            foreach (var propertyInfo in propertyInfos)
            {
                yield return propertyInfo.Name;
            }
        }
    }
}
