﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class BaseTrademarkItemsVersionRepository : DefaultRepository<BasTrademarkItemsVersion, string>,
    IBaseTrademarkItemsVersionRepository
{
    public BaseTrademarkItemsVersionRepository(IFreeSql freeSql, UnitOfWorkManager uowManger) : base(freeSql, uowManger)
    {
    }
}