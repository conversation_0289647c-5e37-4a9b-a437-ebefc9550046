﻿using System.Diagnostics;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.TrademarkOperationCenter.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Filters;

internal sealed class ReleaseUpdateLockFilter(IRedisCache<RedisCacheOptionsBase> redisCache, IMemoryCache memoryCache) 
    : IAsyncExceptionFilter
{
    public async Task OnExceptionAsync(ExceptionContext context)
    {
        var traceId = Activity.Current?.SpanId.ToString() ?? context.HttpContext.TraceIdentifier;
        var lockTracingKey = $"{DeliveryInfoController.RequestLockTracingKey}:{traceId}";
        var procId = memoryCache.Get<string>(lockTracingKey);

        if (procId is not null)
        {
            await redisCache.RemoveCacheValueAsync(DeliveryInfoController.UpdateDeliveryResult<PERSON>ey, procId);
            memoryCache.Remove(lockTracing<PERSON>ey);
        }
    }
}