﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Nice;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Nice;

internal sealed class CaseNiceQueryHandler(IFreeSql freeSql)
    : IRequestHandler<CaseNiceQuery, IEnumerable<StandardCategoryDto>>
{
    public Task<IEnumerable<StandardCategoryDto>> Handle(CaseNiceQuery request, CancellationToken cancellationToken)
    {
        var query = freeSql.Select<CaseTrademarkNiceCategory>().WithLock()
            .Where(category => category.CaseId == request.CaseId);

        return query.ToPageableResultAsync(request, category =>
                new StandardCategoryDto(category.Id,
                    category.GrandId, category.GrandNumber, category.GrandName,
                    category.ParentId, category.ParentNumber, category.ParentName,
                    category.CategoryId, category.CategoryNumber, category.CategoryName, category.IsStandard, category.Order),
            cancellationToken);
    }
}