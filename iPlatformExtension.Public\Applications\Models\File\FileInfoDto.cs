﻿using System.ComponentModel;

namespace iPlatformExtension.Public.Applications.Models.File;

/// <summary>
/// 文件信息
/// </summary>
public class FileInfoDto
{
    /// <summary>
    /// 文件名称
    /// </summary>
    [Description("文件名称")]
    public required string FileName { get; set; }
    
    /// <summary>
    /// 文件类型
    /// </summary>
    [Description("文件类型")]
    public string? FileType { get; set; }
    
    /// <summary>
    /// 文件描述
    /// </summary>
    [Description("文件描述")]
    public string? FileDescription { get; set; }
    
    /// <summary>
    /// 文件临时地址
    /// </summary>
    [Description("文件临时地址")]
    public string? FileUrl { get; set; }
}