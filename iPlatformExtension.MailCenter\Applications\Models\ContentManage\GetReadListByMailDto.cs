﻿namespace iPlatformExtension.MailCenter.Applications.Models.ContentManage
{
    public class GetReadListByMailDto
    { 
        
        /// <summary>
        /// 阅读人Id
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 阅读人
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 阅读状态 0:未读,1:已读,2:作废
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 阅读时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 添加人
        /// </summary>
        public string CreateBy { get; set; }
        public string CreateByUserId { get; set; }
    }
}
