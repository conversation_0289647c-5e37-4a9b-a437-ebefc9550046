#!/bin/bash
 
# 获取镜像的历史并构建层与父层的映射
history=$(docker history ${1} --no-trunc | awk '/^[0-9a-f]{12}/ { print $1 " " $2 }')
 
# 创建层与父层的映射
declare -A layers
while IFS= read -r line; do
    layer_id=$(echo $line | awk '{ print $1 }')
    parent_id=$(echo $line | awk '{ print $2 }')
    layers[$layer_id]=$parent_id
done <<< "$history"
 
# 打印依赖树
function print_tree() {
    local layer=$1
    local prefix=$2
    echo "$prefix$layer"
    if [[ -n ${layers[$layer]} ]]; then
        print_tree ${layers[$layer]} " $prefix"
    fi
}
 
# 从最顶层的空白层开始打印
print_tree '<missing>' ''