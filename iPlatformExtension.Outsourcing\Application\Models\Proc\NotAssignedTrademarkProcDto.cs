﻿using System.ComponentModel;
using System.Text.Json.Serialization;
using iPlatformExtension.Common.Converters;

namespace iPlatformExtension.Outsourcing.Application.Models.Proc;

/// <summary>
/// 未分配外所的任务
/// </summary>
public class NotAssignedTrademarkProcDto
{
    /// <summary>
    /// 任务id
    /// </summary>
    [Description("任务id")]
    public string ProcId { get; set; } = string.Empty;

    /// <summary>
    /// 我方文号
    /// </summary>
    [Description("我方文号")]
    public string Volume { get; set; }=string.Empty;

    /// <summary>
    /// 任务编号
    /// </summary>
    [Description("任务编号")]
    public string ProcNo { get; set; } = string.Empty;

    /// <summary>
    /// 案件名称
    /// </summary>
    [Description("商标名称")]
    public string CaseName { get; set; } = string.Empty;

    /// <summary>
    /// 客户id
    /// </summary>
    [Description("客户id")]
    public string CustomerId { get; set; } = string.Empty;

    /// <summary>
    /// 客户名称
    /// </summary>
    [Description("客户名称")]
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// 国家
    /// </summary>
    [Description("国家")]
    public string Country { get; set; } = string.Empty;

    /// <summary>
    /// 国际分类
    /// </summary>
    [Description("国际分类")]
    public string NiceClasses { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    [Description("任务名称")]
    public string ProcName { get; set; } = string.Empty;

    /// <summary>
    /// 任务标识
    /// </summary>
    [Description("任务标识")]
    public string ProcMark { get; set; } = string.Empty;

    /// <summary>
    /// 委案日期
    /// </summary>
    [JsonConverter(typeof(DateTimeToDateOnlyJsonConverter))]
    [Description("委案日期")]
    public DateTime? EntrustDate { get; set; }

    /// <summary>
    /// 官方期限
    /// </summary>
    [JsonConverter(typeof(DateTimeToDateOnlyJsonConverter))]
    [Description("官方期限")]
    public DateTime? OfficialDeadline { get; set; }

    /// <summary>
    /// 任务状态
    /// </summary>
    [Description("任务状态")]
    public string ProcStatus { get; set; }=string.Empty;

    /// <summary>
    /// 案件id
    /// </summary>
    [Description("案件id")]
    public string CaseId { get; set; } = string.Empty;

    /// <summary>
    /// 版本号
    /// </summary>
    [Description("版本号")]
    public int Version { get; set; }
}