﻿using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc.Trademark;

internal sealed class NotAssignedTrademarkProcExportResultHandler(
    ISystemDictionaryRepository dictionaryRepository,
    IBaseCtrlProcRepository baseCtrlProcRepository,
    IBaseCountryRepository countryRepository,
    ICustomerRepository customerRepository,
    IUserInfoRepository userInfoRepository
    ) : INotificationHandler<NotAssignedProcExportResultNotification>
{
    public async Task Handle(NotAssignedProcExportResultNotification notification, CancellationToken cancellationToken)
    {
        foreach (var dto in notification.Dto)
        {
            dto.Country = await countryRepository.GetTextValueAsync(dto.Country) ?? string.Empty;
            dto.ProcName = await baseCtrlProcRepository.GetTextValueAsync(dto.ProcName) ?? string.Empty;
            dto.CustomerName = await customerRepository.GetTextValueAsync(dto.CustomerId) ?? string.Empty;
            dto.Undertaker = await userInfoRepository.GetTextValueAsync(dto.Undertaker) ?? string.Empty;
            dto.CaseDirection = await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.CaseDirection, dto.CaseDirection);
            dto.ApplicationChannel = await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.TrademarkApplyChannel, dto.ApplicationChannel);
        }
    }
}