using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "express_logistics_code", DisableSyncStructure = true)]
	public partial class ExpressLogisticsCode {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "code", StringLength = 50, IsNullable = false)]
		public string Code { get; set; }

		[ Column(Name = "remark", StringLength = 100, IsNullable = false)]
		public string Remark { get; set; }

		[ Column(Name = "type", StringLength = 50, IsNullable = false)]
		public string Type { get; set; }

	}

}
