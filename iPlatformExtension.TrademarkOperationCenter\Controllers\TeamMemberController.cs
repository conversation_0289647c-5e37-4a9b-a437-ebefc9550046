﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers
{
    /// <summary>
    /// 团队成员管理控制器
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    [Authorize]
    public class TeamMemberController : Controller
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// 构造函数注入中间件组件
        /// </summary>
        /// <param name="mediator"></param>
        public TeamMemberController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 编辑团队成员
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        public async Task EditTeamMemberAsync(EditTeamMemberInput model)
        {
            await _mediator.Send(new EditTeamMemberCommand(model.TeamId, model.Members));
        }

        /// <summary>
        /// 删除团队成员
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task DeleteTeamMemberAsync(DeleteTeamMemberCommand model)
        {
            await _mediator.Send(model);
        }

        /// <summary>
        /// 获取团队成员列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetTeamMemberList")]
        public async Task<IEnumerable<TeamMemberDto>> GetTeamMemberListAsync([FromQuery] TeamMemberQuery model)
        {
            return await _mediator.Send(model);
        }

        /// <summary>
        /// 获取关联团队成员列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRelateMemberList")]
        public async Task<RelateTeamMemberDto> GetRelateMemberListAsync([FromQuery] RelateTeamMemberQuery model)
        {
            return await _mediator.Send(model);
        }


        /// <summary>
        /// 获取团队成员角色
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetTeamRole")]
        public async Task<IEnumerable<TeamRoleDto>> GetTeamRoleAsync([FromQuery] TeamRoleQuery model)
        {
            return await _mediator.Send(model);
        }
    }
}
