using System;
using System.Text.Json;
using Confluent.Kafka;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.MQ.KafKa.Consumer
{
    /// <summary>
    /// Kafka消息处理器，负责消息的反序列化和处理
    /// </summary>
    /// <typeparam name="TMessage">消息类型</typeparam>
    public class KafkaMessageProcessor<TMessage> where TMessage : class
    {
        private readonly ILogger<KafkaMessageProcessor<TMessage>>? _logger;

        public KafkaMessageProcessor(ILogger<KafkaMessageProcessor<TMessage>>? logger = null)
        {
            _logger = logger;
        }

        /// <summary>
        /// 处理消息
        /// </summary>
        /// <param name="consumeResult">消费结果</param>
        /// <param name="messageFunc">消息处理函数</param>
        /// <returns>处理结果，成功返回true，失败返回false</returns>
        public bool ProcessMessage(ConsumeResult<Ignore, string> consumeResult, Action<TMessage> messageFunc)
        {
            if (consumeResult == null) throw new ArgumentNullException(nameof(consumeResult));
            if (messageFunc == null) throw new ArgumentNullException(nameof(messageFunc));

            // 记录消费信息
            LogConsumeInfo(consumeResult);

            // 检查是否到达分区末尾
            if (consumeResult.IsPartitionEOF)
            {
                LogPartitionEOF(consumeResult);
                return false;
            }

            // 反序列化消息
            TMessage? messageResult = DeserializeMessage(consumeResult.Message.Value);
            if (messageResult == null)
            {
                return false;
            }

            // 执行消息处理函数
            try
            {
                messageFunc(messageResult);
                return true;
            }
            catch (Exception ex)
            {
                LogMessageProcessError(ex);
                return false;
            }
        }

        /// <summary>
        /// 反序列化消息
        /// </summary>
        /// <param name="messageValue">消息值</param>
        /// <returns>反序列化后的消息对象</returns>
        private TMessage? DeserializeMessage(string messageValue)
        {
            try
            {
                return JsonSerializer.Deserialize<TMessage>(messageValue, JsonSerializerOptionsFactory.CreateDefault(true));
            }
            catch (Exception ex)
            {
                var errorMessage = $" - {DateTime.Now:yyyy-MM-dd HH:mm:ss}【Exception 消息反序列化失败，Value：{messageValue}】 ：{ex.StackTrace?.ToString()}";
                _logger?.LogError(ex, errorMessage);
                Console.WriteLine(errorMessage);
                return null;
            }
        }

        /// <summary>
        /// 记录消费信息
        /// </summary>
        /// <param name="consumeResult">消费结果</param>
        private void LogConsumeInfo(ConsumeResult<Ignore, string> consumeResult)
        {
            var message = $"Consumed message '{consumeResult.Message?.Value}' at: '{consumeResult?.TopicPartitionOffset}'.";
            _logger?.LogInformation(message);
            Console.WriteLine(message);
        }

        /// <summary>
        /// 记录分区末尾信息
        /// </summary>
        /// <param name="consumeResult">消费结果</param>
        private void LogPartitionEOF(ConsumeResult<Ignore, string> consumeResult)
        {
            var message = $" - {DateTime.Now:yyyy-MM-dd HH:mm:ss} 已经到底了：{consumeResult.Topic}, partition {consumeResult.Partition}, offset {consumeResult.Offset}.";
            _logger?.LogInformation(message);
            //Console.WriteLine(message);
        }

        /// <summary>
        /// 记录消息处理错误
        /// </summary>
        /// <param name="ex">异常</param>
        private void LogMessageProcessError(Exception ex)
        {
            var errorMessage = $" - {DateTime.Now:yyyy-MM-dd HH:mm:ss} 消息处理失败：{ex.Message}";
            _logger?.LogError(ex, errorMessage);
            Console.WriteLine(errorMessage);
        }
    }
}