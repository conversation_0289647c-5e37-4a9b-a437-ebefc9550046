﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Helper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;
using System.Linq.Expressions;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public class GetFlowProcessHandler : IRequestHandler<GetFlowProcessQuery, PageResult<GetFlowProcessDto>>
    {
        private readonly IFreeSql _freeSql;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMediator _mediator;

        public GetFlowProcessHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor, IMediator mediator)
        {
            _freeSql = freeSql;
            _httpContextAccessor = httpContextAccessor;
            _mediator = mediator;
        }

        public async Task<PageResult<GetFlowProcessDto>> Handle(GetFlowProcessQuery request, CancellationToken cancellationToken)
        {

            var userid = _httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;

            ArgumentException.ThrowIfNullOrEmpty(userid);

            Expression<Func<SysFlowActivity, bool>> whereCommon = (fa) => fa.FlowType == request.FFlowType && fa.CurUserId == userid;
            Expression<Func<SysFlowActivity, bool>> whereTodo = null;
            if (request.FFlowType == FlowTypeEnum.DE)
            {
                whereCommon = whereCommon.And((fa) => fa.FlowSubType == request.FSubType);
                if (request.Status != null && request.Status.Any())
                {
                    if (request.Status.Contains((int)DeliveryStatus.Ready))
                    {
                        whereTodo = whereTodo.Or(o => (o.CurFlowNode.NodeCode == null || o.CurFlowNode.NodeCode != "TII_DE_End") && o.CaseProcInfo.DeliInfo.Status == (int)DeliveryStatus.Ready && o.CaseProcInfo.DeliInfo.OperationResult == true);
                    }
                    if (request.Status.Contains((int)DeliveryStatus.Ordered))
                    {
                        whereTodo = whereTodo.Or(o => (o.CaseProcInfo.DeliInfo.Status == (int)DeliveryStatus.Ordered || o.CaseProcInfo.DeliInfo.Status == (int)DeliveryStatus.Delivering) && o.CaseProcInfo.DeliInfo.OperationResult == true);
                    }

                    var err = request.Status.Contains((int)DeliveryStatus.Error);
                    if (request.Status.Contains((int)DeliveryStatus.Error))
                    {
                        whereTodo = whereTodo.Or(fa => fa.CaseProcInfo.DeliInfo.OperationResult == false);
                    }

                    if (request.Status.Contains((int)DeliveryStatus.Complete))
                    {
                        whereTodo = whereTodo.Or(fa => fa.CurFlowNode.NodeCode == "TII_DE_End" && fa.CaseProcInfo.DeliInfo.OperationResult == true && fa.CaseProcInfo.DeliInfo.Status < (int)DeliveryStatus.Confirmed);
                    }
                    if (request.Status.Contains((int)DeliveryStatus.Confirmed))
                    {
                        whereTodo = whereTodo.Or(fa => fa.CurFlowNode.NodeCode == "TII_DE_End" && fa.CaseProcInfo.DeliInfo.OperationResult == true && fa.CaseProcInfo.DeliInfo.Status == (int)DeliveryStatus.Confirmed);
                    }
                }
                else
                {
                    whereTodo = (fa) => fa.CaseProcInfo.DeliInfo.Status.HasValue;
                }
            }
            else
            {
                whereCommon = whereCommon.And((fa) => fa.FlowSubType == request.FSubType && fa.Status == FLOW_STATUS.S1000);
            }

            if (request.FPrivate is not null)
            {
                Expression<Func<SysFlowActivity, bool>> wherePrivate = activity => request.FPrivate.Contains(activity.PrivateId);
                if (request.FPrivate.Contains("PENDING"))
                {
                    wherePrivate = wherePrivate.Or(x => x.PrivateId == null || x.PrivateId == "");
                }
                whereTodo = whereTodo.And(wherePrivate);
            }

            if (!string.IsNullOrWhiteSpace(request.TeamID))
            {
                whereTodo = whereTodo.And((fa) => fa.CaseProcInfo.TeamId == request.TeamID);
            }

            if (!string.IsNullOrWhiteSpace(request.SearchKey) && request.FFlowType != FlowTypeEnum.EX)
            {
                whereTodo = whereTodo.And((fa) => fa.CaseProcInfo.CaseInfo.CaseName.Contains(request.SearchKey) ||
                    fa.CaseProcInfo.CaseInfo.Volume.Contains(request.SearchKey) || fa.CaseProcInfo.CaseInfo.Customer.CustomerName.Contains(request.SearchKey) || fa.CurFlowNode.NameZhCn.Contains(request.SearchKey)
                   || fa.CaseProcInfo.CaseInfo.Applicants.Any(app => app.CusApplicant.ApplicantNameCn.Contains(request.SearchKey)) || fa.CaseProcInfo.CaseInfo.AppNo.Contains(request.SearchKey) || fa.CaseProcInfo.CaseInfo.RegisterNo.Contains(request.SearchKey)
                   || fa.CaseProcInfo.BasCtrlProc.CtrlProcZhCn.Contains(request.SearchKey)
                   );
            }

            if (request.FFlowType == FlowTypeEnum.DE)
            {
                var deQuery = request.Clone<FlowProcessByDEQuery>();
                deQuery.whereCommon = whereCommon;
                deQuery.whereTodo = whereTodo;
                return await _mediator.Send(deQuery, cancellationToken);
            }
            else if (request.FFlowType == FlowTypeEnum.EX)
            {
                var deQuery = request.Clone<FlowProcessByEXQuery>();
                deQuery.whereCommon = whereCommon;
                deQuery.whereTodo = whereTodo;
                return await _mediator.Send(deQuery, cancellationToken);
            }
            else
            {
                var deQuery = request.Clone<FlowProcessQuery>();
                deQuery.whereCommon = whereCommon;
                deQuery.whereTodo = whereTodo;
                return await _mediator.Send(deQuery, cancellationToken);
            }
        }
    }
}
