using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_issue_type", DisableSyncStructure = true)]
	public partial class BasIssueType {

		/// <summary>
		/// 发文类型主键ID
		/// </summary>
		[ Column(Name = "issue_type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string IssueTypeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		/// <summary>
		/// 发文类型编码
		/// </summary>
		[ Column(Name = "issue_type_code", StringLength = 50)]
		public string IssueTypeCode { get; set; }

		/// <summary>
		/// 发文类型英文名称
		/// </summary>
		[ Column(Name = "issue_type_name_en_us", StringLength = 100)]
		public string IssueTypeNameEnUs { get; set; }

		/// <summary>
		/// 发文类型日文名称
		/// </summary>
		[ Column(Name = "issue_type_name_ja_jp", StringLength = 100)]
		public string IssueTypeNameJaJp { get; set; }

		/// <summary>
		/// 发文类型中文名称
		/// </summary>
		[ Column(Name = "issue_type_name_zh_cn", StringLength = 50)]
		public string IssueTypeNameZhCn { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
