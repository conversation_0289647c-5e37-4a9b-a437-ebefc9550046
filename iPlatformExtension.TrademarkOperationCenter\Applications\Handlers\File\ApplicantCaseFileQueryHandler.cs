﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File;

internal sealed class ApplicantCaseFile<PERSON><PERSON>y<PERSON>andler(IMediator mediator)
    : IRequestHandler<ApplicantCaseFileQuery, IEnumerable<FileListDto>>
{
    public Task<IEnumerable<FileListDto>> Handle(ApplicantCaseFileQuery request, CancellationToken cancellationToken)
    {
        var (applicantId, keyword, pageIndex, pageSize, returnUrl) = request;
        return mediator.Send(new CaseFileQuery(new[] {applicantId}, keyword, pageIndex, pageSize, returnUrl), cancellationToken);
    }
}