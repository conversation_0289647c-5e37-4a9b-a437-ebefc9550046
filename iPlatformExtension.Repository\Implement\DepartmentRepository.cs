﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal class DepartmentRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    DefaultRedisCache redisCache,
    CacheExpirationToken<DepartmentInfo> expirationToken)
    : BaseRepository<SysDeptInfo, string>(freeSql), IDepartmentInfoRepository
{
    public IMemoryCache MemoryCache { get; } = memoryCache;

    public CacheExpirationToken<DepartmentInfo> ExpirationToken { get; } = expirationToken;

    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}