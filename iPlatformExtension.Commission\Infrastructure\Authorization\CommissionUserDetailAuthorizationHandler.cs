﻿using iPlatformExtension.Commission.Application.Queries.Authrorization;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace iPlatformExtension.Commission.Infrastructure.Authorization;

internal sealed class CommissionUserDetailAuthorizationHandler(
    ISender sender, ICacheableRepository<string, SysUserInfo> userInfoCache) 
    : AuthorizationHandler<CommissionUserDetailRequirement, HttpContext>
{
    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, CommissionUserDetailRequirement requirement,
        HttpContext resource)
    {
        var user = context.User;
        var routeValues = resource.Request.RouteValues;

        if (routeValues.TryGetValue("userId", out var id) && id is string userId)
        {
            if (user.GetUserId() == userId)
            {
                context.Succeed(requirement);
                return;
            }

            var userDeptId = (await userInfoCache.GetCacheValueAsync(userId))?.DeptId ?? string.Empty;
            var deptIds = await sender.Send(new AuthorizationDeptIdsQuery(), resource.RequestAborted);

            if (deptIds.Contains(userDeptId))
            {
                context.Succeed(requirement);
                return;
            }
            
            context.Fail(new AuthorizationFailureReason(this, GetFailureReasonMessage(requirement, userId)));
            
        }
    }
    
    private static string GetFailureReasonMessage(CommissionUserDetailRequirement requirement, string userId) 
        => $"你没有权限查看当前用户[{userId}]的{requirement.CommissionType}数据";
}