Version 3.22.3

New Features:
1.Support Content-Disposition standard metadata interface;

-----------------------------------------------------------------------------------

Version 3.20.7

New Features:
1. Added the following APIs for checking whether an object exists: ObsClient.HeadObject, ObsClient.BeginHeadObject, and ObsClient.EndHeadObject.

Documentation & Demo:
1. Added the section about the APIs for checking whether an object exists.

Resolved Issues:
1. Fix ObsClient cannot release TCP connection in time under .Net Framework.
2. Fix the issue that resumable download in poor network environment.
3. Fixed the issue that message and code fields in the error messages are occasionally empty.

-----------------------------------------------------------------------------------

Version 3.19.7.1

Resolved Issues:
1. Fixed the issue that an error message is reported indicating unmatched signature when an object with special characters is operated through the URL generated by calling ObsClient.CreateTemporarySignature.

-----------------------------------------------------------------------------------

Version 3.19.7

Resolved Issues:
1. Fixed the null pointer issue of the API for downloading an object in asynchronous mode (ObsClient.EndGetObject).

-----------------------------------------------------------------------------------

Version 3.1.3

Resolved Issues:
1. Modified ObsClient.PutObject to prevent upload issues when the Chunk mode is not supported by the server.

-----------------------------------------------------------------------------------

Version 3.1.2

New Features:
1. FunctionGraph configuration and query are supported in the bucket event notification APIs: ObsClient.SetBucketNotification and ObsClient.GetBucketNotification.

Documentation & Demo:
1. Added the description about FunctionGraph configuration to the event notification section in the Developer Guide.

Resolved Issues:
1. Fixed the issue that the bucket creation API ObsClient.CreateBucket returns incorrect error information due to protocol negotiation.
2. Rectified the error in the sample code BucketOperationsSample.cs.

