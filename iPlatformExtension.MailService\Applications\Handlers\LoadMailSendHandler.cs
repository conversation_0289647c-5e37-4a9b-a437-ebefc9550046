﻿using Confluent.Kafka;
using FreeSql;
using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Encryption;
using iPlatformExtension.Common.MQ.MailMQ;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.MailService.Applications.Commands;
using iPlatformExtension.MailService.Applications.Models;
using iPlatformExtension.MailService.Applications.Queries;
using iPlatformExtension.MailService.Infrastructure.MQ;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.MailCenter;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Hosting;
using MimeKit;
using System.Collections.Generic;
using System.Dynamic;
using System.Net.Mail;
using System.Text.RegularExpressions;
using System.Threading;
using static Confluent.Kafka.ConfigPropertyNames;
using static FreeSql.Internal.GlobalFilter;
using static iPlatformExtension.Model.Enum.SysEnum;
using AttachmentCollection = MimeKit.AttachmentCollection;
using MailHost = iPlatformExtension.Model.MailCenter.MailHost;

namespace iPlatformExtension.MailService.Applications.Handlers
{
    public class LoadMailSendHandler(IFreeSql<MailCenterFreeSql> freeSql,
         //KafkaProducerService<Null, MailSendMessage> producer,
         IMediator mediator,
         HuaweiObsClient huaweiObsClient,
         IConfiguration configuration,
          ILogger<LoadMailSendHandler> logger,
          KafkaProducerService<Null, MailCenterMessageContent> producer,
          IMailSendFlowRepository mailSendFlowRepository
        ) : IRequestHandler<MailSendQuery>
    {

        private SemaphoreSlim _semaphore = new SemaphoreSlim(2);
        public async Task Handle(MailSendQuery request, CancellationToken cancellationToken)
        {
            Console.WriteLine("进入发送逻辑1");
            var lst = await freeSql.Select<MailSendList>()
                .WithLock()
                .Where(o => o.Status == SendStatusType.PendingSend.GetHashCode() && (SqlExt.IsNull(o.SendTime, DateTime.Now) >= DateTime.Now))
                .Take(10)
                .OrderBy(o => o.SendTime)
                .ToListAsync(cancellationToken);
            await SendEmailAsync(lst, cancellationToken);
            return;
        }

        /// <summary>
        /// 发送单封邮件
        /// </summary>
        /// <param name="message"></param>
        /// <returns></returns>
        public async Task SendEmailAsync(MailSend mailSendInfo, CancellationToken cancellationToken)
        {
            await _semaphore.WaitAsync();
            try
            {
                Console.WriteLine("进入发送逻辑2");
                await SendWithRetryAsync(mailSendInfo);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 批量发送邮件
        /// </summary>
        /// <param name="mailSends"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task SendEmailAsync(List<MailSendList> mailSendList, CancellationToken cancellationToken)
        {
            if (mailSendList == null || mailSendList.Count == 0)
            {
                return;
            }

            var mails = await freeSql.Select<MailSend>().Where(o => mailSendList.Any(m => m.MailId == o.MailId)).ToListAsync();
            if (mails == null || mails.Count == 0)
            {
                return;
            }

            var tasks = mails.Select(mailSendInfo => SendEmailAsync(mailSendInfo, cancellationToken));
            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// 发送邮件,可重试
        /// </summary>
        /// <param name="mailSendInfo"></param>
        /// <param name="maxRetries"></param>
        /// <param name="ct"></param>
        /// <returns></returns>
        private async Task SendWithRetryAsync(MailSend mailSendInfo, CancellationToken ct = default)
        {
            try
            {
                Console.WriteLine($"{mailSendInfo.MailSubject}进入发送");
                //var mailInfo = await freeSql.Select<MailSend>().Where(o => o.MailId == mailSendInfo.MailId && o.Status == SendStatusType.PendingSend.GetHashCode()).ToOneAsync(ct);
                if (mailSendInfo != null)
                {
                    ////进入队列
                    //var res = await producer.SendMessageAsync(new MailSendMessage()
                    //{
                    //    MailId = mailInfo.MailId,
                    //    MailTitle = mailInfo.MailSubject,
                    //    MailNo = mailInfo.MailNo,
                    //    OperatorUser = "测试",
                    //    DateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    //    RecipientBy = ""
                    //}, cancellationToken);
                    //发送

                    var host = await freeSql.Select<MailHost>().Where(o => o.HostId == mailSendInfo.HostId && o.IsEnabled).FirstAsync(ct);
                    if (host == null)
                    {
                        await freeSql.Update<MailSend>().Set(o => o.Status, SendStatusType.Error.GetHashCode()).Where(o => o.MailId == mailSendInfo.MailId).ExecuteAffrowsAsync(ct);
                        await freeSql.Update<MailSendList>().Set(o => o.Status, SendStatusType.Error.GetHashCode()).Where(o => o.MailId == mailSendInfo.MailId).ExecuteAffrowsAsync(ct);
                        Console.WriteLine($"{mailSendInfo.MailNo}发件失败:{mailSendInfo.HostId}邮箱状态异常。");
                        logger.LogError($"{mailSendInfo.MailNo} 发件失败:{mailSendInfo.HostId}邮箱状态异常。");
                        return;
                    }

                    var mailUser = await freeSql.Select<MailUser>().Where(o => o.MailId == mailSendInfo.MailId).ToListAsync(ct);
                    var mailAttactments = await freeSql.Select<MailAttachments>().Where(o => o.MailId == mailSendInfo.MailId).ToListAsync(ct);

                    var attachmentCollection = new AttachmentCollection();
                    foreach (var attachment in mailAttactments)
                    {
                        var objectName = $"{attachment.ServerPath}/{attachment.FileName}";
                        var fileRes = await huaweiObsClient.GetObjectToByteAsync(objectName);
                        attachmentCollection.Add(attachment.RealName, fileRes);
                    }

                    var hostConfig = new Applications.Models.MailConfigDto
                    {
                        HostId = host.HostId,
                        SmtpHost = host.SmtpHost,
                        SmtpPort = host.SmtpPort,
                        SenderAccount = host.Account,
                        SenderPassword = DESHelper.DESDecrypt(host.Password),
                        ShowName = host.ShowName,
                        IsPrivate = host.IsPrivate
                    };
                    var sendCmd = new SendMailBySmtpCommand(
                        mailUser.Where(o => o.AddressType == AddressTypeEnum.To).Select(o => o.MailAddress).ToList(),
                        mailUser.Where(o => o.AddressType == AddressTypeEnum.Cc).Select(o => o.MailAddress).ToList(),
                        mailUser.Where(o => o.AddressType == AddressTypeEnum.Bcc).Select(o => o.MailAddress).ToList(),
                        mailSendInfo.MailSubject,
                        mailSendInfo.MailHtmlBody,
                        true,
                        attachmentCollection,
                        hostConfig
                    );

                    var hasRecord = await freeSql.Select<MailSendRecord>().Where(o => o.MailId == mailSendInfo.MailId).AnyAsync();
                    if (hasRecord)
                    {
                        Console.WriteLine($"{mailSendInfo.MailNo}疑似重复发送");
                        logger.LogError($"{mailSendInfo.MailNo}疑似重复发送");

                        return;
                    }
                    var sendRes = await mediator.Send(sendCmd, ct);
                    if (sendRes)
                    {
                        //发送记录
                        await freeSql.Insert(new MailSendRecord()
                        {
                            MailId = mailSendInfo.MailId,
                            CreateTime = DateTime.Now,
                            CreateBy = "admin",
                            Id = Guid.NewGuid().ToString()
                        }).ExecuteAffrowsAsync(ct);

                        using (var uow = freeSql.CreateUnitOfWork())
                        {
                            try
                            {
                                // 在事务中执行的操作  
                                await freeSql.Update<MailSend>().
                                    Set(o => o.Status, SendStatusType.Sent.GetHashCode())
                                    .Where(o => o.MailId == mailSendInfo.MailId).ExecuteAffrowsAsync(ct);
                                await freeSql.Update<MailSendList>()
                                    .Set(o => o.Status, SendStatusType.Sent.GetHashCode())
                                     .Set(o => o.UpdateTime, DateTime.Now)
                                    .Where(o => o.MailId == mailSendInfo.MailId).ExecuteAffrowsAsync(ct);
                                uow.Commit();
                                Console.WriteLine($"{mailSendInfo.MailSubject}已提交");
                            }
                            catch (Exception ex)
                            {
                                uow.Rollback();
                                throw;
                            }
                        };
                        var mailSendFlow = await mailSendFlowRepository.Where(o => o.MailId == mailSendInfo.MailId).FirstAsync();
                        await producer.SendMessageAsync(new MailCenterMessageContent()
                        {
                            MailId = mailSendInfo.MailId,
                            MailTitle = mailSendInfo.MailSubject,
                            MailNo = mailSendInfo.MailNo,
                            DateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                            RecipientBy = mailSendInfo.CreateBy,
                            OperatorUser = mailSendInfo.CreateBy,
                            SentMail = host.Account,
                            OperationType = OperationTypeEnum.Send,
                            Undertaker = mailSendFlow?.UndertakeUserId
                        }, "MailCenterProducer", ct);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"{mailSendInfo.MailNo}邮件发送失败");
            }
        }
    }
}
