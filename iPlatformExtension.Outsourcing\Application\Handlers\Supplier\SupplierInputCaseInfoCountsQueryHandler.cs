﻿using System.Runtime.CompilerServices;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;
using iPlatformExtension.Outsourcing.Application.Queries.Supplier;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Supplier;

internal sealed class SupplierInputCaseInfoCountsQueryHandler(IFreeSql<PlatformFreeSql> freeSql, ISender sender)
    : IStreamRequestHandler<SupplierInputCaseInfoCountsQuery, SupplierCaseCountDto>
{
    public async IAsyncEnumerable<SupplierCaseCountDto> Handle(SupplierInputCaseInfoCountsQuery request, [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        request.Deconstruct(out var manageCompanyId, out var caseDirections, out var ctrlProcIds, out var applicationTypeIds, out var dateRange, out var supplierIds);
       
        foreach (var supplierId in supplierIds)
        {
            var query = new SupplierInputCaseInfoCountQuery(manageCompanyId, caseDirections, ctrlProcIds,
                applicationTypeIds, dateRange, supplierId);
            
            var dto = new SupplierCaseCountDto()
            {
                SupplierId = supplierId,
                Count = await sender.Send(query, cancellationToken)
            };

            yield return dto;
        }
    }
}