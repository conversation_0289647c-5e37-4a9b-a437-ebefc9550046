using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "chk_mail_subscribe", DisableSyncStructure = true)]
	public partial class ChkMailSubscribe {

		[ Column(Name = "alert_id", StringLength = 50, IsNullable = false)]
		public string AlertId { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "exec_hour")]
		public int ExecHour { get; set; } = 0;

		[ Column(Name = "exec_type", StringLength = 50)]
		public string ExecType { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "next_run_time")]
		public DateTime NextRunTime { get; set; }

		[ Column(Name = "type_value", StringLength = 50)]
		public string TypeValue { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

		[ Column(Name = "yd_id", StringLength = 50, IsNullable = false)]
		public string YdId { get; set; } = Guid.NewGuid().ToString().ToUpper();

	}

}
