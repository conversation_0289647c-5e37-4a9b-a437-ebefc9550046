﻿using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class BuildCaseFeeHandler(ILogger<BuildCaseFeeHandler> logger, IFreeSql freeSql)
    : IRequestHandler<BuildCaseFeeCommand>, IFeesQueryStatementBuilder
{
    private const string FeeAlias = nameof(CaseFeeList);

    public Task Handle(BuildCaseFeeCommand request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
        {
            logger.LogError("构建费项查询操作中止");
            return Task.FromCanceled(cancellationToken);
        }
        BuildFeesQueryStatement(request.Dto, request.FeesQuery, freeSql);
        return Task.CompletedTask;
    }

    public static void BuildFeesQueryStatement(FeeQueryDto dto, ISelect<CaseFeeList> feesQuery, params object?[] args)
    {
        var freeSql = args[0] as IFreeSql;

        feesQuery.WhereDynamicFilter(dto.BillNos.BuildContainsDynamicFilter(nameof(CaseFeeList.BillNo), FeeAlias))
            .WhereDynamicFilter(dto.PayBillNos.BuildContainsDynamicFilter(nameof(CaseFeeList.PayBillNo), FeeAlias))
            .WhereDynamicFilter(
                dto.OfficerStatus.BuildContainsDynamicFilter(nameof(CaseFeeList.OfficerStatus), FeeAlias))
            .WhereDynamicFilter(
                dto.ReceiverStatus.BuildContainsDynamicFilter(nameof(CaseFeeList.ReceiveStatus), FeeAlias))
            .WhereDynamicFilter(dto.PaymentNames.BuildContainsDynamicFilter(nameof(CaseFeeList.PaymentName), FeeAlias))
            .WhereDynamicFilter(dto.PayStatus.BuildContainsDynamicFilter(nameof(CaseFeeList.PayStatus), FeeAlias))
            .WhereDynamicFilter(
                dto.FeeTypeNameIds.BuildContainsDynamicFilter(nameof(CaseFeeList.FeeTypeNameId), FeeAlias))
            .WhereDynamicFilter(
                dto.PayOfficerLegalDatePeriod.BuildDynamicTimePeriodFilter(nameof(CaseFeeList.PayOfficerLegalDate),
                    FeeAlias))
            .WhereDynamicFilter(
                dto.PayOfficerDatePeriod.BuildDynamicTimePeriodFilter(nameof(CaseFeeList.PayOfficerDate), FeeAlias))
            .WhereDynamicFilter(
                dto.ReceiveDatePeriod.BuildDynamicTimePeriodFilter(nameof(CaseFeeList.ReceiveDate), FeeAlias))
            .WhereDynamicFilter(
                dto.RequestTimePeriod.BuildDynamicTimePeriodFilter(nameof(CaseFeeList.RequestDate), FeeAlias))
            .WhereIfDynamicFilter(dto.Enabled is not null,
                dto.Enabled.BuildEqualsDynamicFilter(nameof(CaseFeeList.IsEnabled), FeeAlias))
            .WhereDynamicFilter(dto.BalanceWays.BuildContainsDynamicFilter(nameof(CaseFeeList.BalanceWay), FeeAlias))
            .WhereDynamicFilter(
                dto.OfficerName.BuildLikeDynamicFilterInfo(nameof(CaseFeeList.OfficeNameZhCn), FeeAlias))
            .WhereDynamicFilter(dto.InvoiceNos.BuildContainsDynamicFilter(nameof(CaseFeeList.InvoiceNo), FeeAlias))
            .WhereDynamicFilter(
                dto.CreationTimePeriod.BuildDynamicTimePeriodFilter(nameof(CaseFeeList.CreateTime), FeeAlias))
            .WhereDynamicFilter(dto.FeeIds.BuildContainsDynamicFilter(nameof(CaseFeeList.FeeId), FeeAlias))
            .WhereDynamicFilter(dto.Currencies.BuildContainsDynamicFilter(nameof(CaseFeeList.CurrencyId), FeeAlias))
            .WhereDynamicFilter(
                dto.OfficialPaymentPublicationDate.BuildDynamicTimePeriodFilter(
                    nameof(CaseFeeList.OfficialPaymentPublicationDate), FeeAlias))
            .WhereIf(dto.OfficialFeeMark.HasValue, caseFeeList => caseFeeList.OfficialFeeMark == dto.OfficialFeeMark)
            .WhereIf(dto.OfficialNotificationChecked.HasValue,
                caseFeeList => caseFeeList.OfficialNotificationChecked == dto.OfficialNotificationChecked)
            .WhereIf(dto.PaymentListChecked.HasValue,
                caseFeeList => caseFeeList.PaymentListChecked == dto.PaymentListChecked);


    }
}