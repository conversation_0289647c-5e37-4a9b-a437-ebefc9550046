using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_allot", DisableSyncStructure = true)]
	public partial class CaseAllot {

		[ Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ProcId { get; set; }

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		/// <summary>
		/// 客户期限
		/// </summary>
		[ Column(Name = "cus_due_date")]
		public DateTime? CusDueDate { get; set; }

		[ Column(Name = "cus_finish_date")]
		public DateTime? CusFinishDate { get; set; }

		[ Column(Name = "cus_first_date")]
		public DateTime? CusFirstDate { get; set; }

		/// <summary>
		/// 客户要求返发明人期限
		/// </summary>
		[ Column(Name = "cus_inventor_date")]
		public DateTime? CusInventorDate { get; set; }

		/// <summary>
		/// 客户要求返接口人期限
		/// </summary>
		[ Column(Name = "cus_ipr_date")]
		public DateTime? CusIprDate { get; set; }

		[ Column(Name = "flow_cur_user_id", StringLength = 50)]
		public string FlowCurUserId { get; set; }

		[ Column(Name = "flow_update_time")]
		public DateTime? FlowUpdateTime { get; set; }

		[ Column(Name = "flow_update_user", StringLength = 50)]
		public string FlowUpdateUser { get; set; }

		/// <summary>
		/// 内部期限
		/// </summary>
		[ Column(Name = "int_due_date")]
		public DateTime? IntDueDate { get; set; }

		[ Column(Name = "int_finish_date")]
		public DateTime? IntFinishDate { get; set; }

		[ Column(Name = "int_first_date")]
		public DateTime? IntFirstDate { get; set; }

		[ Column(Name = "is_supply")]
		public bool IsSupply { get; set; } = false;

		/// <summary>
		/// 法定期限
		/// </summary>
		[ Column(Name = "legal_due_date")]
		public DateTime? LegalDueDate { get; set; }

		[ Column(Name = "status")]
		public int? Status { get; set; }

		[ Column(Name = "titular_write_user", StringLength = 50)]
		public string TitularWriteUser { get; set; }

		/// <summary>
		/// 承办部门
		/// </summary>
		[ Column(Name = "undertake_dept_id", StringLength = 50)]
		public string UndertakeDeptId { get; set; }

		/// <summary>
		/// 承办人
		/// </summary>
		[ Column(Name = "undertake_user_id", StringLength = 50)]
		public string UndertakeUserId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
