using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_service_fee_n", DisableSyncStructure = true)]
	public partial class RpServiceFeeN {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "apply_type", StringLength = 50)]
		public string ApplyType { get; set; }

		[ Column(Name = "belong_district", StringLength = 50)]
		public string BelongDistrict { get; set; }

		[ Column(Name = "case_type", StringLength = 50)]
		public string CaseType { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "dept_id", StringLength = 50)]
		public string DeptId { get; set; }

		[ Column(Name = "dept_name", StringLength = 500)]
		public string DeptName { get; set; }

		[ Column(Name = "month", StringLength = 5)]
		public string Month { get; set; }

		[ Column(Name = "num")]
		public int? Num { get; set; }

		[ Column(Name = "quarter", StringLength = 5)]
		public string Quarter { get; set; }

		[ Column(Name = "tech_classify", StringLength = 50)]
		public string TechClassify { get; set; }

		[ Column(Name = "user_district", StringLength = 50)]
		public string UserDistrict { get; set; }

		[ Column(Name = "year", StringLength = 5)]
		public string Year { get; set; }

	}

}
