﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File;

internal sealed class DeliveryFinishPackageFilesCommandHandler(IMediator mediator, ICaseFileRepository caseFileRepository) : IRequestHandler<DeliveryFinishPackageFilesCommand>
{
    public async Task Handle(DeliveryFinishPackageFilesCommand request, CancellationToken cancellationToken)
    {
        var freeSql = caseFileRepository.Orm;
        var procId = request.ProcId;
        
        var fileNo = await mediator.Send(new PackageDeliveryFilesCommand(procId), cancellationToken);

        if (fileNo is null)
        {
            return;
        }
        
        var fileInfo = await freeSql.Select<FileListA>(fileNo).ToOneAsync(a => new FileListA()
        {
            Id = a.Id,
            RealName = a.RealName
        }, cancellationToken);

        var caseFile = new CaseFile()
        {
            CreateTime = DateTime.Now,
            CreateUserId = UserIds.Administrator,
            DescId = string.Empty,
            FileNo = $"a002{fileInfo.Id}",
            ObjId = procId,
            FileName = fileInfo.RealName,
            FileEx = Path.GetExtension(fileInfo.RealName)
        };
        await caseFileRepository.InsertAsync(caseFile, cancellationToken);
    }
}