﻿using System;
using System.Collections.Generic;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{

    [Table(Name = "urgent_draft_history", DisableSyncStructure = true)]
    public partial class UrgentDraftHistory
    {

        /// <summary>
        /// 主键
        /// </summary>
        [Column(IsPrimary = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column(Name = "create_time")]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 日期类型：1-初稿期限内 2-初稿期限外 3-定稿期限内 4-定稿期限外 5-官方期限
        /// </summary>
        [Column(Name = "date_type", IsNullable = false)]
        public int DateType { get; set; }

        /// <summary>
        /// 催稿提示语
        /// </summary>
        [Column(Name = "msg", StringLength = 500)]
        public string Msg { get; set; }

        /// <summary>
        /// 任务id
        /// </summary>
        [Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
        public string ProcId { get; set; }

        /// <summary>
        /// 被催稿人
        /// </summary>
        [Column(Name = "reminder_id", StringLength = 50, IsNullable = false)]
        public string ReminderId { get; set; }

        /// <summary>
        /// 催稿人
        /// </summary>
        [Column(Name = "urgent_user_id", StringLength = 50, IsNullable = false)]
        public string UrgentUserId { get; set; }

        /// <summary>
        /// 警告类型:1-邮件 2-企微
        /// </summary>
        [Column(Name = "warning_type")]
        public int WarningType { get; set; }

    }

}
