<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>iPlatformExtension.Repository</RootNamespace>
    <LangVersion>13</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FreeSql.Extensions.AggregateRoot" Version="3.5.105" />
    <PackageReference Include="FreeSql.Repository" Version="3.5.105" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\iPlatformExtension.Common\iPlatformExtension.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="obj\**" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Remove="obj\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="obj\**" />
  </ItemGroup>

</Project>
