﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_send_record", DisableSyncStructure = true)]
	public partial class MailSendRecord {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		[Column(Name = "mail_id", StringLength = 100, IsNullable = false)]
		public string MailId { get; set; }

	}

}
