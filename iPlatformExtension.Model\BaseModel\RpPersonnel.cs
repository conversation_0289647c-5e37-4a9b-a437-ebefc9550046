using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_personnel", DisableSyncStructure = true)]
	public partial class RpPersonnel {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "month", StringLength = 50)]
		public string Month { get; set; }

		[ Column(Name = "quarter", StringLength = 50)]
		public string Quarter { get; set; }

		[ Column(Name = "rank_zh_cn", StringLength = 50)]
		public string RankZhCn { get; set; }

		[ Column(Name = "tech_classify", StringLength = 50)]
		public string TechClassify { get; set; }

		[ Column(Name = "total_num")]
		public int? TotalNum { get; set; }

		[ Column(Name = "year", StringLength = 50)]
		public string Year { get; set; }

	}

}
