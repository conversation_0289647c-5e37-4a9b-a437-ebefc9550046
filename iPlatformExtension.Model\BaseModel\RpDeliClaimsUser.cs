using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_deli_claims_user", DisableSyncStructure = true)]
	public partial class RpDeliClaimsUser {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "apply_type", StringLength = 50)]
		public string ApplyType { get; set; }

		[ Column(Name = "case_type", StringLength = 50)]
		public string CaseType { get; set; }

		[ Column(Name = "dept_id", StringLength = 50)]
		public string DeptId { get; set; }

		[ Column(Name = "dept_name", StringLength = 500)]
		public string DeptName { get; set; }

		[ Column(Name = "manage_district", StringLength = 200)]
		public string ManageDistrict { get; set; }

		[ Column(Name = "month", StringLength = 5)]
		public string Month { get; set; }

		[ Column(Name = "num", DbType = "money")]
		public decimal? Num { get; set; }

		[ Column(Name = "quarter", StringLength = 5)]
		public string Quarter { get; set; }

		[ Column(Name = "specification_pages")]
		public int? SpecificationPages { get; set; }

		[ Column(Name = "tech_classify", StringLength = 50)]
		public string TechClassify { get; set; }

		[ Column(Name = "total_claims")]
		public int? TotalClaims { get; set; }

		[ Column(Name = "u_district", StringLength = 50)]
		public string UDistrict { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

		[ Column(Name = "year", StringLength = 5)]
		public string Year { get; set; }

	}

}
