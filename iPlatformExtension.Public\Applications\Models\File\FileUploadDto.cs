﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.ModelBinders;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Applications.Models.File;

/// <summary>
/// 文件上传参数
/// </summary>
[ModelBinder(BinderType = typeof(LargeFileFormDataModelBinder))]
public class FileUploadDto
{
    /// <summary>
    /// 文件
    /// </summary>
    [BindProperty(Name = "file")]
    [Required]
    public IFormFile File { get; set; } = default!;

    /// <summary>
    /// 文件名
    /// </summary>
    [Required]
    [BindProperty(Name = "fileName")]
    public string FileName { get; set; } = default!;

    /// <summary>
    /// 业务参数路径
    /// 递交：/delivery/{procId}
    /// </summary>
    [Required]
    [BindProperty(Name = "serverPath")]
    public string ServerPath { get; set; } = default!;

    /// <summary>
    /// 备注
    /// </summary>
    [BindProperty(Name = "remark")]
    public string? Remark { get; set; }

    /// <summary>
    /// 桶名称
    /// </summary>
    [BindProperty(Name = "bucket")]
    public string? Bucket { get; set; }
}