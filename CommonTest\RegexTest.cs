﻿using System.Text;
using System.Text.RegularExpressions;
using Xunit.Abstractions;

namespace CommonTest;

public partial class RegexTest(ITestOutputHelper outputHelper)
{
    
    [Theory]
    [InlineData("+351 213 241 530")]
    [InlineData("+86 187211549633")]
    [InlineData("+271-187211549633")]
    public void TestInternationalDialingCode(string phoneNumber)
    {
        var regex = InternationalDialingCodeRegex();
        Assert.Matches(regex, phoneNumber);

        outputHelper.WriteLine(regex.Matches(phoneNumber)
            .Aggregate(new StringBuilder(), (current, match) => current.AppendLine(match.Value)).ToString());
    }
    
    [Theory]
    [InlineData("+351 ")]
    [InlineData("+86 ")]
    [InlineData("+351")]
    [InlineData("+86")]
    [InlineData("+271-")]
    public void TestNumbers(string areaCode)
    {
        var regex = NumbersRegex();
        Assert.Matches(regex, areaCode);

        outputHelper.WriteLine(regex.Matches(areaCode)
            .Aggregate(new StringBuilder(), (current, match) => current.AppendLine(match.Value)).ToString());
    }

    [GeneratedRegex(@"\+\d{1,3}[\s-]?", RegexOptions.IgnoreCase, "zh-CN")]
    private static partial Regex InternationalDialingCodeRegex();
    
    [GeneratedRegex(@"\d{1,3}", RegexOptions.IgnoreCase, "zh-CN")]
    private static partial Regex NumbersRegex();
}