﻿using iPlatformExtension.Commission.Application.Queries.Trademark.Domestic;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class DomesticCommissionImportQueryHandler(IFreeSql freeSql) : IRequestHandler<DomesticCommissionImportQuery, string?>
{
    public async Task<string?> Handle(DomesticCommissionImportQuery request, CancellationToken cancellationToken)
    {
        var dto = request.Dto;

        var procInfos = await freeSql.Select<DomesticTrademarkCommission>().WithLock()
            .Where(commission => commission.ProcNo == dto.ProcNo)
            .Where(commission => commission.Status == dto.ProcStatus)
            .Where(commission => commission.CtrlProcMarkCn == dto.ProcMark)
            .Where(commission => commission.Year == dto.Year)
            .Where(commission => commission.Month == dto.Month)
            .Where(commission => commission.CnName == dto.MainUndertakerName)
            .ToListAsync(cancellationToken);
        
        return procInfos.Count == 1 ? procInfos[0].ProcId : null;
    }
}