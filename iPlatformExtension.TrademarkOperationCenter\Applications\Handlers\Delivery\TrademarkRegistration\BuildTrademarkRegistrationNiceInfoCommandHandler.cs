﻿using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkRegistration;

internal sealed class BuildTrademarkRegistrationNiceInfoCommandHandler(IDeliveryNiceCategoryRepository deliveryNiceCategoryRepository)
    : IMatchTrademarkProcCommandHandler<BuildNiceInfoCommand>
{
    async Task IMatchNotificationHandler<BuildNiceInfoCommand>.HandleAsync(BuildNiceInfoCommand request, CancellationToken cancellationToken)
    {
        var deliveryInfo = request.DeliveryInfo;
        var caseId = deliveryInfo.CaseId;
        var procId = deliveryInfo.ProcId;
        var categories = await deliveryNiceCategoryRepository.Orm.Select<CaseTrademarkNiceCategory>().WithLock()
            .Where(category => category.CaseId == caseId).ToListAsync(category => new DeliveryNiceCategory()
            {
                Id = 0,
                CaseId = caseId,
                CategoryName = category.CategoryName,
                CategoryNumber = category.CategoryNumber,
                CategoryId = category.CategoryId,
                CreationTime = DateTime.Now,
                GrandNumber = category.GrandNumber,
                GrandName = category.GrandName,
                GrandId = category.GrandId,
                ParentNumber = category.ParentNumber,
                ParentName = category.ParentName,
                ParentId = category.ParentId,
                ProcId = procId,
                UpdateTime = DateTime.Now,
                IsStandard = category.IsStandard
            }, cancellationToken);

        deliveryInfo.IsStandardNice = categories.Count == 0 ? null : categories.TrueForAll(category => category.IsStandard);
        await deliveryNiceCategoryRepository.InsertAsync(categories, cancellationToken);
    }

    string IMatchTrademarkProcCommandHandler<BuildNiceInfoCommand>.CtrlProcId => CtrlProcIds.TrademarkRegistration;

    IEnumerable<string> IMatchTrademarkProcCommandHandler<BuildNiceInfoCommand>.CaseDirections => [CaseDirection.II];
}