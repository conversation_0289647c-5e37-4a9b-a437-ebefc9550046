﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;

/// <summary>
/// 团队列表统计
/// </summary>
/// <param name="TeamId">团队id</param>
/// <param name="TeamName">团队名称</param>
/// <param name="UserId">团队负责人id</param>
/// <param name="IsExclusive">是否专属</param>
public record HandoverTeamListDto(string TeamId, string TeamName,string UserId,bool IsExclusive)
{
    /// <summary>
    /// 团队成员计数
    /// </summary>
    public int TeamMemberCount { get; set; }

    /// <summary>
    /// 团队任务计数
    /// </summary>
    public int TeamTaskCount { get; set; }
};

