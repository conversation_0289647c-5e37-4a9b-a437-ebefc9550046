﻿using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;


namespace iPlatformExtension.Model.Dto.CaseEntrust
{
    /// <summary>
    /// 订单产品
    /// </summary>
    public class AppProductDto
    {
        /// <summary>
        /// 委案ID
        /// </summary>
        [ Column(StringLength = 50)]
        [Required(ErrorMessage = "产品实例id不能为空")]
        public string orderProductId { get; set; }

        /// <summary>
        /// 案件ID
        /// </summary>
        [ Column(StringLength = 50)]
        public string caseId { get; set; }

        /// <summary>
        /// 初始任务ID
        /// </summary>
        [ Column(StringLength = 50)]
        [Required(ErrorMessage = "初始任务不能为空")]
        public string ctrlProcId { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        [Required(ErrorMessage = "产品ID不能为空")]
        [ Column(StringLength = 50)]
        public string productId { get; set; }

        [ Column(StringLength = 50)]
        public string? ctrlProcCode { get; set; }
        /// <summary>
        /// 产品编码
        /// </summary>
        [ Column(StringLength = 50)]
        public string productCode { get; set; }

        /// <summary>
        /// 任务属性
        /// </summary>
        [ Column(StringLength = 50)]
        public string? ctrlProcProperty { get; set; }

        /// <summary>
        /// 委案ID
        /// </summary>
        [ Column(StringLength = 50)]
        public string? EntrustID { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [ Column(StringLength = 50)]
        public string? Remark { get; set; }


        /// <summary>
        /// 产品名称
        /// </summary>
        [ Column(StringLength = 100)]
        public string ProductName { get; set; }

        /// <summary>
        /// 承办部门
        /// </summary>
        // [Required(ErrorMessage = "承办部门不能为空")]
        public string? UndertakeDeptId { get; set; }

        /// <summary>
        /// 定稿期限（外）
        /// </summary>
        public DateTime? CusFinishDate { get; set; }

        /// <summary>
        /// 初稿期限（外）
        /// </summary>
        public DateTime? CusFirstDate { get; set; }


        /// <summary>
        /// 定稿期限(内)
        /// </summary>
        public DateTime? IntFinishDate { get; set; }

        /// <summary>
        /// 初稿期限(内)
        /// </summary>
        public DateTime? IntFirstDate { get; set; }

        /// <summary>
        /// 官方期限
        /// </summary>
        public DateTime? LegalDueDate { get; set; }

        /// <summary>
        /// 官方来文日
        /// </summary>
        public DateTime? ReceiveDate { get; set; }

        /// <summary>
        /// 发文序列号
        /// </summary>
        public string? ReceiveNo { get; set; }

        /// <summary>
        /// 官文名称
        /// </summary>
        public string? NoticeName { get; set; }
        
        /// <summary>
        /// 名义承办人
        /// </summary>
        public string? TitularWriteUser { get; set; }
        
        /// <summary>
        /// 承办人
        /// </summary>
        public string? UndertakeUserId { get; set; }
    }
}
