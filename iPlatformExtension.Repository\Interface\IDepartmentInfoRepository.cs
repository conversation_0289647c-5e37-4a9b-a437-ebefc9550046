﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Repository.Interface;

public interface IDepartmentInfoRepository : IBaseRepository<SysDeptInfo, string>, IRedisCacheableRepository<string, DepartmentInfo>, IScopeDependency
{
    Task<DepartmentInfo?> ICacheableRepository<string, DepartmentInfo>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Orm.Select<SysDeptInfo>().WithLock().Where(info => info.DeptId == key).FirstAsync(info =>
            new DepartmentInfo()
            {
                Id = info.DeptId,
                CnName = info.DeptName,
                FullName = info.FullName,
                DeptCode = info.DeptCode,
                DistrictCode = info.District
            }, cancellationToken)!;
    }

    async Task<IEnumerable<DepartmentInfo>> ICacheableRepository<string, DepartmentInfo>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Orm.Select<SysDeptInfo>().WithLock().ToListAsync(info =>
            new DepartmentInfo()
            {
                Id = info.DeptId,
                CnName = info.DeptName,
                FullName = info.FullName,
                DeptCode = info.DeptCode,
                DistrictCode = info.District
            }, cancellationToken);
    }

    string ICacheableRepository<string, DepartmentInfo>.GenerateKey(DepartmentInfo value)
    {
        return value.Id;
    }
}