﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <LangVersion>13</LangVersion>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DocumentationFile>bin\Debug\iPlatformExtension.Commission.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DocumentationFile>bin\Release\iPlatformExtension.Commission.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\iPlatformExtension.Repository\iPlatformExtension.Repository.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Grpc.Tools" Version="2.67.0">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="nacos-sdk-csharp.AspNetCore" Version="1.3.9" />
      <PackageReference Include="nacos-sdk-csharp.Extensions.Configuration" Version="1.3.9" />
      <PackageReference Include="NLog.DiagnosticSource" Version="5.2.0" />
      <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
      <PackageReference Include="SkyAPM.Agent.AspNetCore" Version="2.2.0" />
      <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    </ItemGroup>
    
    <ItemGroup>
        <Protobuf Include="..\Protos\common_messages.proto" GrpcServices="Client" Link="Protos\common_messages.proto" />
        <Protobuf Include="..\Protos\PeriodSetting.proto" GrpcServices="Client"  Link="Protos\PeriodSetting.proto" />
        <Protobuf Include="..\Protos\commission.proto" GrpcServices="Client" Link="Protos\commission.proto">
          <Link>Protos\commission.proto</Link>
        </Protobuf>
    </ItemGroup>
    
    <ItemGroup>
      <None Include="wwwroot\templates\批更新调整权值模板.xlsx" />
    </ItemGroup>

</Project>
