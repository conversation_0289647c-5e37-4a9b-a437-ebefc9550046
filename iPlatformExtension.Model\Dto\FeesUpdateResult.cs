﻿using System.Text;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 费项更新结果
/// </summary>
/// <param name="FeeId">费项id</param>
/// <param name="Message">更新结果信息</param>
public record FeesUpdateResult(
    string FeeId,
    string? Message = "操作成功")
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; internal set; }

    /// <summary>
    /// 到款状态
    /// </summary>
    public string ReceiveStatus { get; internal set; } = default!;
    
    /// <summary>
    /// 销售
    /// </summary>
    public UserBaseInfo? Sales { get;  set; }
        
    /// <summary>
    /// 跟案人
    /// </summary>
    public UserBaseInfo? Tracker { get;  set; }
    
    /// <summary>
    /// 商务
    /// </summary>
    public UserBaseInfo? BusinessUser { get;  set; }
    
    /// <summary>
    /// 线索提报人
    /// </summary>
    public UserBaseInfo? ClueUser { get;  set; }

    /// <summary>
    /// 案源类型
    /// </summary>
    public string? CaseSourceType { get; set; }

    /// <summary>
    /// 更新的操作信息
    /// </summary>
    public string? Message { get; internal set; }
}