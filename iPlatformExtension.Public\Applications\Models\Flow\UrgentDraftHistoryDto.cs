﻿using iPlatformExtension.Common.Extensions;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.Public.Applications.Models.Flow;

/// <summary>
/// 催稿历史
/// </summary>
/// <param name="UrgentUserId">催稿人id</param>
/// <param name="UrgentUserName">催告人名称</param>
/// <param name="ReminderId">被催告人id</param>
/// <param name="Reminder">被催稿人名称</param>
/// <param name="WarningTime">提醒时间</param>
/// <param name="DateType">期限</param>
public record UrgentDraftHistoryDto(
    string UrgentUserId,
    string ReminderId,
    DateTime WarningTime,
    int DateTypeValue)
{
    public string? UrgentUserName { get; set; }

    public string? Reminder { get; set; }

    public string DateType => ((ReportType)DateTypeValue).GetDescription();

}