﻿using FreeSql;
using FreeSql.Internal.Model;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Case;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Case;
using MediatR;
using System.Linq;
using System.Linq.Expressions;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Case
{
    /// <summary>
    /// 获取案件信息列表
    /// </summary>
    public class GetCaseInfoQueryHandler : IRequestHandler<GetCaseInfoQuery, IEnumerable<GetCaseInfoDto>>
    {
        private readonly IFreeSql _freeSql;

        /// <summary>
        /// 获取案件信息列表构造函数
        /// </summary>
        /// <param name="freeSql"></param>
        public GetCaseInfoQueryHandler(IFreeSql freeSql) => _freeSql = freeSql;

        /// <summary>
        /// 获取案件信息列表处理者
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<GetCaseInfoDto>> Handle(GetCaseInfoQuery request, CancellationToken cancellationToken)
        {
            request.Init();

            var dbSelect = _freeSql.Select<CaseInfo, CaseApplicantList, CusApplicant, CusCustomer>()
                .LeftJoin(it => it.t1.Id == it.t2.CaseId)
                .LeftJoin(it => it.t2.ApplicantId == it.t3.ApplicantId)
                .LeftJoin(it => it.t1.CustomerId == it.t4.CustomerId)
                .WhereIf(request.CaseDirection is not null, it => it.t1.CaseDirection == request.CaseDirection)
                .WhereIf(request.CaseTypeId is not null, it => it.t1.CaseTypeId == request.CaseTypeId)
                .WhereIf(request.CustomerId is not null, it => it.t1.CustomerId == request.CustomerId)
                .WhereIf(request.ExcludeCaseId is not null, it => it.t1.Id != request.ExcludeCaseId)
                .Where(it => it.t2.IsRepresent == true)
                .OrderByDescending(it => it.t1.CreateTime);
            if (request.Search is not null)
            {
                string[] searchStrings = request.Search.Split(' ', ';', ',');
                Expression<Func<HzyTuple<CaseInfo, CaseApplicantList, CusApplicant, CusCustomer>, bool>>? where = null;
                foreach (var search in searchStrings.Where(it => !string.IsNullOrWhiteSpace(it)))
                {
                    where = where.Or(it =>
                        it.t1.Volume.Contains(search) || it.t1.CaseName.Contains(search) ||
                        it.t3.ApplicantNameCn.Contains(search) || it.t1.ApplyNo.Contains(search));
                }

                dbSelect = dbSelect.Where(where);
            }
            dbSelect.Page(request.PageIndex.Value, request.PageSize.Value).Count(out var totalCount);
            var result = await dbSelect.Distinct().ToListAsync(it => new GetCaseInfoDto(it.t1.Id, it.t1.Volume, it.t1.CaseName, it.t1.CaseDirection, it.t1.AppNo, it.t1.TrademarkClass, it.t3.ApplicantNameCn, it.t4.CustomerName,it.t1.CreateTime), cancellationToken);

            return new PageResult<GetCaseInfoDto>()
            {
                Data = result,
                Page = request.PageIndex.Value,
                PageSize = request.PageSize.Value,
                Total = totalCount
            };

        }
    }
}

