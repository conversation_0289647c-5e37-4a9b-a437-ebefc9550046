﻿using System.Collections;

namespace iPlatformExtension.Common.TypeInfo;

public class EntityPropertyInfoCollection : Dictionary<string, EntityPropertyInfo>, IEnumerable<EntityPropertyInfo>
{
    public EntityPropertyInfoCollection(Dictionary<string, EntityPropertyInfo> infos) : base(infos)
    {
    }

    public EntityPropertyInfoCollection(int capacity) : base(capacity)
    {
        
    }
    
    public new IEnumerator<EntityPropertyInfo> GetEnumerator()
    {
        return new EntityPropertyEnumerator(this);
    }
    
    public struct EntityPropertyEnumerator(Dictionary<string, EntityPropertyInfo> entityPropertyInfos)
        : IEnumerator<EntityPropertyInfo>
    {
        private Enumerator _enumerator = entityPropertyInfos.GetEnumerator();


        public bool MoveNext()
        {
            return _enumerator.MoveNext();
        }

        public void Reset()
        {
            IEnumerator enumerator = _enumerator;
            enumerator.Reset();
        }

        public EntityPropertyInfo Current => _enumerator.Current.Value;

        object IEnumerator.Current => Current;

        public void Dispose()
        {
            _enumerator.Dispose();
        }
    }
}