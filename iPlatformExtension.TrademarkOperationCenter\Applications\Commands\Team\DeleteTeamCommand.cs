﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;

/// <summary>
/// 删除团队
/// </summary>
/// <param name="TeamId">团队id</param>
public record DeleteTeamCommand([Required] params string[] TeamId) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

