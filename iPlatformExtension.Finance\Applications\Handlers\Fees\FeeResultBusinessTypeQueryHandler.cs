﻿using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultBusinessTypeQueryHandler(IBusinessTypeRepository businessTypeRepository)
    : IRequestHandler<FeeResultBusinessTypeQuery>
{
    public async Task Handle(FeeResultBusinessTypeQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return;

        foreach (var feeItem in request.FeeResults)
        {
            feeItem.BusinessTypeInfo = await businessTypeRepository.GetCacheValueAsync(feeItem.BusinessTypeId ?? string.Empty);
            feeItem.BusinessType = await businessTypeRepository.GetChineseKeyValueAsync(feeItem.BusinessTypeId);
        }
    }
}