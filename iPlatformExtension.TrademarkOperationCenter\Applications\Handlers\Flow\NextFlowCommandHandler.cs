using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow;

internal sealed class NextFlowCommandHandler(IMediator mediator, ILogger<NextFlowCommandHandler> logger)
    : IRequestHandler<NextFlowCommand>
{
    public async Task Handle(NextFlowCommand request, CancellationToken cancellationToken)
    {
        var flowInfo = await mediator.Send(new FlowInfoQuery()
        {
            FlowType = FlowType.Delivery,
            FlowSubType = "TII",
            ObjId = request.ProcId
        }, cancellationToken);

        if (flowInfo is null)
        {
            throw new NotFoundException(request.ProcId, "国内商标递交流程");
        }
        
        logger.LogInformation("当前节点[{CurNodeId}]编码[{NodeCode}]", flowInfo.CurNodeId, flowInfo.NodeCode);

        var flowConfiguration = await mediator.Send(new FlowConfigQuery()
        {
            FlowType = FlowType.Delivery,
            FlowSubType = "TII"
        }, cancellationToken);


        var nextNode = flowConfiguration.FirstOrDefault(node => node.NodeCode == request.NodeCode);
        var flowHistories = await mediator.Send(new FlowHistoryQuery()
        {
            ObjId = request.ProcId,
            FlowType = FlowType.Delivery,
            FlowSubType = "TII"
        }, cancellationToken);

        string nextUser;
        if (request.SubmitType == FlowSubmitType.Reject)
        {
            nextUser = request.UndertakerId ?? flowHistories.FlowHistoryList.LastOrDefault(history => history.NodeCode == request.NodeCode)
                ?.AuditCnUserId ?? flowInfo.RejectUserId;
        }
        else if (request.SubmitType == FlowSubmitType.Submit)
        {
            nextUser = request.UndertakerId ?? flowHistories.FlowHistoryList.LastOrDefault(history => history.NodeCode == "review")
                ?.AuditCnUserId ?? flowInfo.RejectUserId;
        }
        else
        {
            throw new NotSupportedException("改方法暂时不支持移交功能");
        }

        if (nextNode != default)
        {
            await mediator.Send(new SubmitFlowProcessCommand(new FlowInfo
            {
                ProcID = request.ProcId,
                FStatus = 0,
                FFlowID = flowInfo.FlowId,
                FCurNodeID = flowInfo.CurNodeId,
                FNextNodeID = nextNode.NodeId,
                FNextUserID = nextUser,
                FAuditTypeID = request.SubmitType,
                FRemark = request.Remark,
                FFlowType = FlowType.Delivery,
                FAllowEdit = false,
                FFlowSubType = "TII"
            }, new UserBaseInfo(flowInfo.CurAuditUserID)
            {
                CnName = flowInfo.CurAuditName
            }), cancellationToken);
        }
    }
}