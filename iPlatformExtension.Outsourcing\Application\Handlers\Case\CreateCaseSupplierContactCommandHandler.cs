﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Commands.Case;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Case;

internal sealed class CreateCaseSupplierContactCommandHandler(ICaseForeignContactRepository caseForeignContactRepository) 
    : IRequestHandler<CreateCaseSupplierContactCommand>
{
    public async Task Handle(CreateCaseSupplierContactCommand request, CancellationToken cancellationToken)
    {
        var (caseId, contactIds) = request;

        var existContactIds = await caseForeignContactRepository.Where(list => list.CaseId == caseId)
            .Where(list => contactIds.Contains(list.ContactId))
            .ToListAsync(list => list.ContactId, cancellationToken);
        
        contactIds.ExceptWith(existContactIds);

        var caseForeignContactList = contactIds.Select(contactId => new CaseForeignContactList
        {
            CaseId = caseId,
            ContactId = contactId
        }).ToList();
        
        await caseForeignContactRepository.InsertAsync(caseForeignContactList, cancellationToken);
    }
}