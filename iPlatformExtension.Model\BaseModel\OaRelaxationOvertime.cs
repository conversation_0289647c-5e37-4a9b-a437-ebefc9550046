using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "oa_relaxation_overtime", DisableSyncStructure = true)]
	public partial class OaRelaxationOvertime {

		[ Column(Name = "cur_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CurId { get; set; }

		[ Column(Name = "overtime_id", StringLength = 50)]
		public string OvertimeId { get; set; }

		[ Column(Name = "relaxation_id", StringLength = 50)]
		public string RelaxationId { get; set; }

	}

}
