﻿using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Commission.Application.Queries.Trademark.Foreign;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class ForeignWeightUserDetailQueryHandler(IFreeSql freeSql) 
    : IRequestHandler<ForeignWeightUserDetailQuery, IEnumerable<UserCommissionWeightProcDetail>> 
{
    public async Task<IEnumerable<UserCommissionWeightProcDetail>> Handle(ForeignWeightUserDetailQuery request, CancellationToken cancellationToken)
    {
        var (userId, year, month, keyword) = request;

        return await freeSql.Select<RpForeignTrademarkBonus>().WithLock()
            .Where(commission => month == commission.Month && year == commission.Year)
            .Where(commission => commission.UndertakeUserId == userId)
            .WhereIf(!string.IsNullOrWhiteSpace(keyword), 
                commission => commission.Volume.Contains(keyword!) || commission.ProcNo.Contains(keyword!))
            .ToListAsync(commission => new UserCommissionWeightProcDetail
            {
                ProcId = commission.ProcId,
                Volume = commission.Volume,
                RegisterNo = commission.RegisterNo ?? string.Empty,
                AppNo = commission.AppNo,
                ProcNo = commission.ProcNo,
                CaseName = commission.CaseName,
                CtrlProcName = commission.CtrlProcZhCn,
                ProcPoint = commission.EditedProcPoint ?? commission.ProcPoint,
                CustomerName = commission.CustomerName,
                BigClient = commission.Bigclient,
                ProcStatus = commission.Status,
                ProcMark = commission.CtrlProcMarkCn,
                UndertakerName = commission.CnName,
                CommissionDate = new DateOnly(commission.ReceiveDate.Year, commission.ReceiveDate.Month, commission.ReceiveDate.Day),
                PushedStatus = commission.Pushed == true ? "已推送" : "未推送",
                TrademarkClasses = commission.TrademarkClasses
            }, cancellationToken);
    }
}