﻿using iPlatformExtension.Public.Applications.Queries.SystemDictionary;
using iPlatformExtension.Public.Infrastructure.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.SystemDictionary;

internal sealed class SystemDictionaryQueryHandler(ISystemDictionaryRepository dictionaryRepository) 
    : IRequestHandler<SystemDictionaryQuery, IEnumerable<KeyValuePair<string, string>>>
{
    public async Task<IEnumerable<KeyValuePair<string, string>>> <PERSON>le(SystemDictionaryQuery request, CancellationToken cancellationToken)
    {
        var (dictionaryName, keyType) = request;
        var values = await dictionaryRepository.GetCacheValueAsync(dictionaryName, cancellationToken: cancellationToken);

        return values is null
            ? Enumerable.Empty<KeyValuePair<string, string>>()
            : values.Select(dictionary => new KeyValuePair<string, string>(dictionary.GetKey(keyType), dictionary.TextZhCn));
    }
}