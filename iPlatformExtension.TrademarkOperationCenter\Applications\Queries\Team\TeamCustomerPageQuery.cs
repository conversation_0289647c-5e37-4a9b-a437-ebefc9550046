﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;

/// <summary>
/// 团队客户分页
/// </summary>
/// <param name="ExcludeCustomerId">排除团队成员对象</param>
/// <param name="Name">团队名称</param>
public record TeamCustomerPageQuery(List<string>? ExcludeCustomerId, string? Name) : PageModel, IRequest<IEnumerable<TeamCustomerPageDto>>;

