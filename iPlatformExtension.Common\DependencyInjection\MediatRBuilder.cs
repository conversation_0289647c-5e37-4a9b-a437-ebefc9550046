﻿using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.DependencyInjection;

public sealed class MediatRBuilder
{
    private readonly IList<Action<MediatRServiceConfiguration>> _configActions;
    
    public IServiceCollection Services { get; }

    internal MediatRBuilder(IServiceCollection services)
    {
        _configActions = new List<Action<MediatRServiceConfiguration>>();
        Services = services;
    }

    public MediatRBuilder Add(Action<MediatRServiceConfiguration> configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        _configActions.Add(configuration);

        return this;
    }

    public IServiceCollection Build()
    {
        Services.AddMediatR(configuration =>
        {
            foreach (var configAction in _configActions)
            {
                configAction(configuration);
            }
        });

        return Services;
    }
}