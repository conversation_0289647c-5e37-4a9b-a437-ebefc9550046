﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Enum
{
    public enum ReaderStatusEnum
    {
        /// <summary>
        /// 待阅读
        /// </summary>
        ToRead = 0,
        /// <summary>
        /// 已读
        /// </summary>
        AlreadyRead = 1,
        /// <summary>
        /// 作废
        /// </summary>
        Invalid = 2
    }
}
