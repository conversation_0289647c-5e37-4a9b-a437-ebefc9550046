using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_label", DisableSyncStructure = true)]
	public partial class MailLabel {

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50, IsNullable = false)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_public")]
		public bool IsPublic { get; set; } = false;

		[ Column(Name = "lab_id", StringLength = 50, IsNullable = false)]
		public string LabId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "lab_name", StringLength = 500, IsNullable = false)]
		public string LabName { get; set; }

		[ Column(Name = "mail_id", StringLength = 50, IsNullable = false)]
		public string MailId { get; set; }

	}

}
