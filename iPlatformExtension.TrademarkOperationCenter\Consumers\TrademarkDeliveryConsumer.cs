﻿using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Consumers;

[Consumer("trademark-delivery")]
internal sealed class TrademarkDeliveryConsumer(IMediator mediator)
{
    /// <summary>
    /// 手动从完成递交退回到递交官方。
    /// 重置自动递交状态为未递交。
    /// </summary>
    /// <param name="message">流程事件</param>
    /// <returns>协程任务</returns>
    [Operation("reset-status")]
    public Task ResetDeliveryStatusAsync(FlowEventMessage message)
    {
        return mediator.Send(new ResetDeliveryStatusCommand(message));
    }

    /// <summary>
    /// 完成递交到结束。
    /// 创建任务。
    /// 打包文件。
    /// </summary>
    /// <param name="message">流程事件</param>
    /// <returns>协程任务</returns>
    [Operation("finish")]
    public Task FinishDeliveryAsync(FlowEventMessage message)
    {
        return mediator.Send(new FinishDeliveryCommand(message));
    }

    /// <summary>
    /// 手动从递交官方到完成递交。
    /// 自动递交状态变成完成递交。
    /// </summary>
    /// <param name="message">流程事件</param>
    /// <returns>协程任务</returns>
    [Operation("manual-complete")]
    public Task ManualCompleteAsync(FlowEventMessage message)
    {
        return mediator.Send(new ManualCompleteCommand(message));
    }
}