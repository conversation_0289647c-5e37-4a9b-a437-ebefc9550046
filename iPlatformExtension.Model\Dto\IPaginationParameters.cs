using FreeSql.Internal.Model;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 分页参数
/// </summary>
public interface IPaginationParameters
{
    /// <summary>
    /// 页码
    /// </summary>
    int? PageIndex { get; }
    
    /// <summary>
    /// 页面大小
    /// </summary>
    int? PageSize { get; }

    /// <summary>
    /// freeSql分页参数
    /// </summary>
    BasePagingInfo? PagingInfo
    {
        get
        {
            if (PageIndex is not null && PageSize is not null)
            {
                return new BasePagingInfo
                {
                    PageNumber = PageIndex.Value,
                    PageSize = PageSize.Value
                };
            }

            return null;
        }
    }
}