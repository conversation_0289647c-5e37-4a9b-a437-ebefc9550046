using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_host_access", DisableSyncStructure = true)]
	public partial class MailHostAccess {

		[ Column(Name = "host_id", StringLength = 50, IsNullable = false)]
		public string HostId { get; set; }

		[ Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

	}

}
