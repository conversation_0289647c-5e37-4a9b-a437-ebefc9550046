using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel;

[ Table(Name = "bill_record_foreign", DisableSyncStructure = true)]
public class BillRecordForeign 
{

	/// <summary>
	/// 账单ID
	/// </summary>
	[ Column(Name = "bill_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
	public string BillId { get; set; } = Guid.NewGuid().ToString().ToUpper();

	[ Column(Name = "apply_batch_title", StringLength = 100)]
	public string ApplyBatchTitle { get; set; }

	/// <summary>
	/// 银行名称
	/// </summary>
	[ Column(Name = "bank_id", StringLength = 50)]
	public string BankId { get; set; }

	/// <summary>
	/// 本币金额
	/// </summary>
	[ Column(Name = "basic_amount", DbType = "money")]
	public decimal? BasicAmount { get; set; }

	/// <summary>
	/// 本币
	/// </summary>
	[ Column(Name = "basic_currency", StringLength = 50)]
	public string BasicCurrency { get; set; }

	/// <summary>
	/// 账单金额
	/// </summary>
	[ Column(Name = "bill_amount", DbType = "money")]
	public decimal? BillAmount { get; set; }

	/// <summary>
	/// 账单币别
	/// </summary>
	[ Column(Name = "bill_currency", StringLength = 50)]
	public string BillCurrency { get; set; }

	/// <summary>
	/// 账单日期
	/// </summary>
	[ Column(Name = "bill_date")]
	public DateTime? BillDate { get; set; }

	/// <summary>
	/// 账单编号
	/// </summary>
	[ Column(Name = "bill_no", StringLength = 50)]
	public string BillNo { get; set; }

	/// <summary>
	/// 帐单汇率
	/// </summary>
	[ Column(Name = "bill_rate", DbType = "money")]
	public decimal? BillRate { get; set; }

	/// <summary>
	/// 案件类型
	/// </summary>
	[ Column(Name = "case_type", StringLength = 50)]
	public string CaseType { get; set; }

	[ Column(Name = "create_time", InsertValueSql = "getdate()")]
	public DateTime CreateTime { get; set; }

	[ Column(Name = "create_user_id", StringLength = 50, IsNullable = false)]
	public string CreateUserId { get; set; }

	/// <summary>
	/// 客户名称
	/// </summary>
	[ Column(Name = "customer_id", StringLength = 50)]
	public string CustomerId { get; set; }

	[ Column(Name = "customer_names", StringLength = 2000)]
	public string CustomerNames { get; set; }

	/// <summary>
	/// 所属分所
	/// </summary>
	[ Column(Name = "district", StringLength = 50)]
	public string District { get; set; }

	[ Column(Name = "domestic_amount", DbType = "money")]
	public decimal? DomesticAmount { get; set; }

	[ Column(Name = "domestic_currency", StringLength = 50)]
	public string DomesticCurrency { get; set; }

	[ Column(Name = "exchange_rate_bill_to_domestic", DbType = "money")]
	public decimal? ExchangeRateBillToDomestic { get; set; }

	[ Column(Name = "exchange_rate_bill_to_pay", DbType = "money")]
	public decimal? ExchangeRateBillToPay { get; set; }

	[ Column(Name = "exchange_rate_pay_to_domestic", DbType = "money")]
	public decimal? ExchangeRatePayToDomestic { get; set; }

	/// <summary>
	/// 外所费用名称 
	/// </summary>
	[ Column(Name = "fee_type", StringLength = 50)]
	public string FeeType { get; set; }

	/// <summary>
	/// 当前处理人
	/// </summary>
	[ Column(Name = "flow_cur_user_id", StringLength = 50)]
	public string FlowCurUserId { get; set; }

	/// <summary>
	/// 流程结束时间
	/// </summary>
	[ Column(Name = "flow_finish_time")]
	public DateTime? FlowFinishTime { get; set; }

	/// <summary>
	/// 流程状态
	/// </summary>
	[ Column(Name = "flow_status_code", StringLength = 50)]
	public string FlowStatusCode { get; set; }

	/// <summary>
	/// 最后处理时间
	/// </summary>
	[ Column(Name = "flow_update_time")]
	public DateTime? FlowUpdateTime { get; set; }

	/// <summary>
	/// 应付日期
	/// </summary>
	[ Column(Name = "int_due_date")]
	public DateTime? IntDueDate { get; set; }

	[ Column(Name = "is_lock")]
	public bool IsLock { get; set; } = false;

	[ Column(Name = "paid_amount", DbType = "money")]
	public decimal? PaidAmount { get; set; }

	[ Column(Name = "paid_currency", StringLength = 50)]
	public string PaidCurrency { get; set; }

	[ Column(Name = "pay_batch_title", StringLength = 100)]
	public string PayBatchTitle { get; set; }

	[ Column(Name = "pay_date")]
	public DateTime? PayDate { get; set; }

	/// <summary>
	/// 收到日期
	/// </summary>
	[ Column(Name = "receive_date")]
	public DateTime? ReceiveDate { get; set; }

	/// <summary>
	/// 备注
	/// </summary>
	[ Column(Name = "remark", StringLength = 4000)]
	public string Remark { get; set; }

	[ Column(Name = "remittance_charges", DbType = "money")]
	public decimal? RemittanceCharges { get; set; }

	[ Column(Name = "remittance_charges_currency", StringLength = 50)]
	public string RemittanceChargesCurrency { get; set; }

	[ Column(Name = "update_time", InsertValueSql = "getdate()")]
	public DateTime UpdateTime { get; set; }

	[ Column(Name = "update_user_id", StringLength = 50, IsNullable = false)]
	public string UpdateUserId { get; set; }

	[ Column(Name = "volumes", StringLength = 2000)]
	public string Volumes { get; set; }
}