﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class BatchDeliveryValidationQueryHandler(IFreeSql freeSql)
    : IRequestHandler<BatchDeliveryValidationQuery, IEnumerable<DeliveryValidationDto>>
{
    public async Task<IEnumerable<DeliveryValidationDto>> Handle(BatchDeliveryValidationQuery request, CancellationToken cancellationToken)
    {
        var procIds = request.ProcIds;

        var deliveryList = await freeSql.Select<DeliInfo, CaseProcInfo, SysFlowActivity, SysFlowNode>()
            .WithLock(SqlServerLock.ReadPast)
            .LeftJoin((info, procInfo, activity, node) => info.ProcId == procInfo.ProcId)
            .LeftJoin((info, procInfo, activity, node) => info.ProcId == activity.ObjId && activity.FlowType == FlowType.Delivery)
            .LeftJoin((info, procInfo, activity, node) => activity.CurNodeId == node.NodeId)
            .Where((info, procInfo, activity, node) => procIds.Contains(info.ProcId))
            .WhereIf(request.Started, (info, procInfo, activity, node) => activity.FlowType == FlowType.Delivery &&
                                                                           activity.FlowSubType == "TII")
            .ToListAsync((info, procInfo, activity, node) =>
                new DeliveryValidationDto
                {
                    FlowId = activity.FlowId,
                    ProcId = info.ProcId,
                    ProcNo = string.IsNullOrEmpty(info.ProcNo) ? procInfo.ProcNo : info.ProcNo,
                    FlowStatus = activity.Status,
                    CurrentNodeId = activity.CurNodeId,
                    CurrentUserId = activity.CurUserId,
                    DeliveryStatus = info.Status!.Value,
                    OperationResult = info.OperationResult,
                    Version = info.Version,
                    CtrlProcId = info.CtrlProcId,
                    CurrentNodeCode = node.NodeCode,
                    FlowType = activity.FlowType,
                    FlowSubType = activity.FlowSubType,
                    DeliveryFlowSubType = info.FlowSubType,
                    IsAuto = info.IsAuto
                }, cancellationToken);

        switch (request.Started)
        {
            case true when deliveryList.Count == 0:
                throw new ApplicationException("选中的数据没有启动国内自动递交流程或者被锁定");
            case true:
            {
                var procIdSet = new HashSet<string>(procIds);
                procIdSet.ExceptWith(deliveryList.Select(x => x.ProcId));
                if (procIdSet.Count > 0)
                {
                    var ex = new ApplicationException("选中的数据被锁定！");
                    ex.Data.Add("ProcNos", procIdSet);
                    throw ex;
                }

                break;
            }
        }

        return deliveryList;
    }
}