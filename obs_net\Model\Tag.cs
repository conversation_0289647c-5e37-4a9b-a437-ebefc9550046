/*----------------------------------------------------------------------------------
// Copyright 2019 Huawei Technologies Co.,Ltd.
// Licensed under the Apache License, Version 2.0 (the "License"); you may not use
// this file except in compliance with the License.  You may obtain a copy of the
// License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations under the License.
//----------------------------------------------------------------------------------*/

namespace OBS.Model
{
    /// <summary>
    /// Bucket tagging
    /// </summary>
    public class Tag
    {
        

        /// <summary>
        /// Tag key 
        /// </summary>
        /// <remarks>
        /// <para>
        /// Mandatory parameter
        /// A tag key consists of up to 36 characters, chosen from A-Z, a-z, 0-9, underscores (_), hyphens (-), and Unicode (\u4E00-\u9FFF) characters. The tag keys in one bucket must be unique.
        /// </para>
        /// </remarks>
        public string Key
        {
            get;
            set;
        }

        /// <summary>
        /// Tag value 
        /// </summary>
        /// <remarks>
        /// <para>
        /// Mandatory parameter
        /// A tag value consists of up to 43 characters, chosen from A-Z, a-z, 0-9, underscores (_), hyphens (-), and Unicode (\u4E00-\u9FFF) characters. 
        /// </para>
        /// </remarks>
        public string Value
        {
            get;
            set;
        }

    }
}


