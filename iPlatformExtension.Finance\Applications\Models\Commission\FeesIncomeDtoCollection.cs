﻿using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Applications.Models.Commission;

/// <summary>
/// 收入数据集合
/// </summary>
public record FeesIncomeDtoCollection 
    // : IEnumerable<FeesIncomeDto>
{
    /// <summary>
    /// 划拨后的收入数据
    /// </summary>
    public IEnumerable<FeesIncomeDto> IncomeInfos { get; set; } = Array.Empty<FeesIncomeDto>();

    // public IEnumerator<FeesIncomeDto> GetEnumerator()
    // {
    //     return IncomeInfos.GetEnumerator();
    // }
    //
    // IEnumerator IEnumerable.GetEnumerator()
    // {
    //     return GetEnumerator();
    // }
}