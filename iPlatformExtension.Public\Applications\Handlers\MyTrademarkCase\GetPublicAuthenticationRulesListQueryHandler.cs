﻿using System.Linq;
using System.Linq.Expressions;
using System.Text.Json;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.MyTrademarkCase
{
    /// <summary>
    /// 获取公认证列表
    /// </summary>
    internal sealed class GetPublicAuthenticationRulesListQueryHandler(IFreeSql freeSql, IBaseCountryRepository baseCountryRepository, IUserInfoRepository userInfoRepository)
        : IRequestHandler<GetPublicAuthenticationRulesListQuery, IEnumerable<GetPublicAuthenticationRulesListDto>>
    {

        public async Task<IEnumerable<GetPublicAuthenticationRulesListDto>> Handle(GetPublicAuthenticationRulesListQuery request, CancellationToken cancellationToken)
        {
            var basCountries = await baseCountryRepository.WhereIf(!string.IsNullOrWhiteSpace(request.SearchKey), it => it.CountryZhCn.Contains(request.SearchKey!))
                .ToListAsync(it => new { it.CountryId }, cancellationToken);
            var recognizedCertificateConfigSelect = freeSql.Select<RecognizedCertificateConfig>().WithLock().OrderBy(it => it.CtrlProcId);

            if (request.SearchKey is not null)
            {
                Expression<Func<RecognizedCertificateConfig, bool>> wherePrivate = activity => false;
                foreach (var country in basCountries)
                {
                    wherePrivate = wherePrivate.Or(x => x.CountryIds.Contains(country.CountryId));
                }

                wherePrivate = wherePrivate.Or(it => it.BasCtrlProc.CtrlProcZhCn.Contains(request.SearchKey));
                recognizedCertificateConfigSelect = recognizedCertificateConfigSelect.Where(wherePrivate);
            }

            if (request.PageIndex is not null && request.PageSize is not null)
            {
                recognizedCertificateConfigSelect = recognizedCertificateConfigSelect
                    .Page(request.PageIndex.Value, request.PageSize.Value)
                    .Count(out var totalCount);
                var publicAuthenticationRulesList = await recognizedCertificateConfigSelect.ToListAsync(it =>
                    new GetPublicAuthenticationRulesListDto(it.Id, it.RecognizedCertificateId, it.CtrlProcId,
                        it.BasCtrlProc.CtrlProcZhCn, it.CountryIds, it.IsEnable, it.CreateUserId, it.UpdateUserId,
                        it.CreateTime, it.UpdateTime, it.Mark), cancellationToken);

                var publicAuthenticationRulesListResult = await publicAuthenticationRulesList.ToAsyncEnumerable().SelectAwait(async recognizedCertificateConfig =>
                {
                    if (!string.IsNullOrWhiteSpace(recognizedCertificateConfig.CreateUserId))
                    {
                        var userInfo = await userInfoRepository.GetTextValueAsync(recognizedCertificateConfig.CreateUserId);
                        recognizedCertificateConfig.CreateUserName = userInfo ?? "";
                    }
                    if (!string.IsNullOrWhiteSpace(recognizedCertificateConfig.UpdateUserId))
                    {
                        var userInfo = await userInfoRepository.GetTextValueAsync(recognizedCertificateConfig.UpdateUserId); ;
                        recognizedCertificateConfig.UpdateUserName = userInfo ?? "";
                    }
                    if (!string.IsNullOrWhiteSpace(recognizedCertificateConfig.CountryId))
                    {
                        var countryList = new List<string>();
                        recognizedCertificateConfig.CountryIds = JsonSerializer.Deserialize<List<string>>(recognizedCertificateConfig.CountryId)!;
                        foreach (var country in recognizedCertificateConfig.CountryIds)
                        {
                            var cacheValueAsync = await baseCountryRepository.GetCacheValueAsync(country);
                            if (cacheValueAsync != null)
                            {
                                countryList.Add(cacheValueAsync?.CountryZhCn);
                            }
                        }
                        recognizedCertificateConfig.CountryZhCn = countryList;
                    }
                    return recognizedCertificateConfig;
                }).ToListAsync(cancellationToken: cancellationToken);
                return new PageResult<GetPublicAuthenticationRulesListDto>()
                {
                    Data = publicAuthenticationRulesListResult,
                    Page = request.PageIndex.Value,
                    PageSize = request.PageSize.Value,
                    Total = totalCount
                };
            }

            var recognizedCertificateList = await recognizedCertificateConfigSelect.ToListAsync(it =>
                new GetPublicAuthenticationRulesListDto(it.Id, it.RecognizedCertificateId, it.CtrlProcId,
                    it.BasCtrlProc.CtrlProcZhCn, it.CountryIds, it.IsEnable, it.CreateUserId,
                    it.UpdateUserId, it.CreateTime, it.UpdateTime, it.Mark), cancellationToken);
            return await recognizedCertificateList.ToAsyncEnumerable().SelectAwait(async recognizedCertificateConfig =>
            {
                if (!string.IsNullOrWhiteSpace(recognizedCertificateConfig.CreateUserId))
                {
                    var userInfo = await userInfoRepository.GetTextValueAsync(recognizedCertificateConfig.CreateUserId);
                    recognizedCertificateConfig.CreateUserName = userInfo ?? "";
                }
                if (!string.IsNullOrWhiteSpace(recognizedCertificateConfig.UpdateUserId))
                {
                    var userInfo = await userInfoRepository.GetTextValueAsync(recognizedCertificateConfig.UpdateUserId);
                    recognizedCertificateConfig.UpdateUserName = userInfo ?? "";
                }
                if (!string.IsNullOrWhiteSpace(recognizedCertificateConfig.CountryId))
                {
                    var countryList = new List<string>();
                    recognizedCertificateConfig.CountryIds = JsonSerializer.Deserialize<List<string>>(recognizedCertificateConfig.CountryId)!;
                    foreach (var country in recognizedCertificateConfig.CountryIds)
                    {
                        var cacheValueAsync = await baseCountryRepository.GetCacheValueAsync(country);
                        if (cacheValueAsync != null)
                        {
                            countryList.Add(cacheValueAsync?.CountryZhCn);
                        }
                    }
                    recognizedCertificateConfig.CountryZhCn = countryList;
                }
                return recognizedCertificateConfig;
            }).ToListAsync(cancellationToken: cancellationToken);

        }
    }
}

