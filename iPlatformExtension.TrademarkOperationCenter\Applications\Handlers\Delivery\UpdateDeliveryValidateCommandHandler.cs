﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class UpdateDeliveryValidateCommandHandler(IMediator mediator)
    : IRequestHandler<UpdateDeliveryValidateCommand, BatchDeliveryValidateResult>
{
    public async Task<BatchDeliveryValidateResult> Handle(UpdateDeliveryValidateCommand request, CancellationToken cancellationToken)
    {
        var procIds = request.ProcIds;

        var deliveryList = await mediator.Send(new BatchDeliveryValidationQuery(procIds), cancellationToken);

        var validResult = await mediator.Send(new MultipartCtrlProcValidateCommand(deliveryList), cancellationToken);
        if (!validResult.Success)
        {
            return validResult;
        }

        validResult = await mediator.Send(new UndertakerValidateCommand(deliveryList), cancellationToken);
        if (!validResult.Success)
        {
            return validResult;
        }

        validResult = deliveryList.Where(dto =>
        {
            var deliveryStatus = (DeliveryStatus) dto.DeliveryStatus;
            return dto.OperationResult && deliveryStatus != DeliveryStatus.Ready;
        }).Aggregate(validResult, (result, dto) =>
        {
            result.ProcNos.Add(dto.ProcNo);
            result.Success = false;
            result.Message = "选中了不可更新的代办，请取消选择";

            return result;
        });

        return validResult;
    }
}