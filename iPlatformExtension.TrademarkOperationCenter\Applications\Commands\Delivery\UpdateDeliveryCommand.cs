using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.BaseModel;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

internal record UpdateDeliveryCommand(string ProcId, JsonPatchDocument<DeliInfo> DeliveryPatch, string OperatorId)
    : IFreeSqlUnitOfWorkCommand<PlatformFreeSql>, IRequest<DeliInfo>;