﻿using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Newtonsoft.Json.Serialization;

namespace iPlatformExtension.Finance.Applications.Models.Fees;

/// <summary>
/// 案件费项JsonPatch文档
/// </summary>
public sealed class CaseFeeJsonPatchDocument(List<Operation<CaseFeePatchDto>> operations) 
    : JsonPatchDocument<CaseFeePatchDto>(operations, contractResolver)
{

    private static readonly IContractResolver contractResolver = new CamelCasePropertyNamesContractResolver();
    
    /// <summary>
    /// 费项id
    /// </summary>
    [Required]
    public required string FeeId { get; set; }
    
    
}