﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultUserInfoQueryHandler : IRequestHandler<FeeResultUserInfoQuery>
{
    private readonly IUserInfoRepository _userInfoRepository;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="userInfoRepository">用户仓储</param>
    public FeeResultUserInfoQueryHandler(IUserInfoRepository userInfoRepository)
    {
        _userInfoRepository = userInfoRepository;
    }

    /// <summary>
    /// 处理用户信息转换
    /// </summary>
    /// <param name="request">费项结果数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task Handle(FeeResultUserInfoQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return;

        ICacheableRepository<string, UserBaseInfo> userRepository = _userInfoRepository;
        foreach (var feeItem in request.FeeResults)
        {
            var undertaker = await userRepository.GetCacheValueAsync(feeItem.Undertaker);
            feeItem.Undertaker = undertaker?.UserName ?? string.Empty;
            feeItem.UndertakerName = undertaker?.CnName ?? string.Empty;

            var titularWriter = await userRepository.GetCacheValueAsync(feeItem.TitularWriterId ?? string.Empty);
            feeItem.TitularWriterName = titularWriter?.CnName ?? string.Empty;

            var creator = await userRepository.GetCacheValueAsync(feeItem.CreatorId ?? string.Empty);
            feeItem.CreatorName = creator?.CnName ?? string.Empty;
            
            feeItem.ProcUndertaker = (await userRepository.GetCacheValueAsync(feeItem.ProcUndertaker))?.UserName ?? string.Empty;

            var currentSales = await userRepository.GetCacheValueAsync(feeItem.CurrentSales);
            feeItem.CurrentSales = currentSales?.UserName ?? string.Empty;
            feeItem.CurrentSalesName = currentSales?.CnName ?? string.Empty;

            var caseSales = await userRepository.GetCacheValueAsync(feeItem.CaseSalesId ?? string.Empty);
            feeItem.CaseSalesName = caseSales?.CnName ?? string.Empty;

            var currentBusinessUser = await userRepository.GetCacheValueAsync(feeItem.CurrentBusinessUser);
            feeItem.CurrentBusinessUser = currentBusinessUser?.UserName ?? string.Empty;
            feeItem.CurrentBusinessUserCnName = currentBusinessUser?.CnName ?? string.Empty;

            var currentTracker = await userRepository.GetCacheValueAsync(feeItem.CurrentTracker);
            feeItem.CurrentTracker = currentTracker?.UserName ?? string.Empty;
            feeItem.CurrentTrackerName = currentTracker?.CnName ?? string.Empty;
            
            feeItem.CurrentClueUser = (await userRepository.GetCacheValueAsync(feeItem.CurrentClueUser))?.UserName ??
                                      string.Empty;
        }
    }
}