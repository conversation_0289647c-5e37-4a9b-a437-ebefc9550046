﻿using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;
using Microsoft.CodeAnalysis.Operations;
using System.Linq.Expressions;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public class GetFlowProcessHistoryByEXHandler : IRequestHandler<GetFlowProcessHistoryByEXQuery, PageResult<GetFlowProcessHistoryDto>>
    {
        private readonly IFreeSql _freeSql;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public GetFlowProcessHistoryByEXHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor)
        {
            _freeSql = freeSql;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<PageResult<GetFlowProcessHistoryDto>> Handle(GetFlowProcessHistoryByEXQuery request, CancellationToken cancellationToken)
        {
            var userid = _httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var list = new List<GetFlowProcessHistoryDto>();
            ArgumentNullException.ThrowIfNull(userid);
            Expression<Func<SysFlowActivity, bool>> where = null;
            var sql = _freeSql.Select<SysFlowActivity>().Where((fa) => fa.FlowType == request.FFlowType).WithLock();  //&& fa.AuditTime >= DateTime.Now.Date.AddDays(-6)
            long totalCount = 0L;

            where = (fa) => fa.FlowHistory.AuditUserId == userid && fa.FlowHistory.FlowType == request.FFlowType && fa.FlowSubType == request.FSubType && fa.FlowHistory.AuditTime >= DateTime.Now.Date.AddDays(-6);

            if (!string.IsNullOrWhiteSpace(request.SearchKey))
            {
                where = where.And((fa) => fa.CaseProcFlow.CaseProcInfo.CaseInfo.CaseName.Contains(request.SearchKey) ||
                fa.CaseProcFlow.CaseProcInfo.CaseInfo.Volume.Contains(request.SearchKey) || fa.CaseProcFlow.CaseProcInfo.CaseInfo.Customer.CustomerName.Contains(request.SearchKey) || fa.CaseProcFlow.CaseProcInfo.BasCtrlProc.CtrlProcZhCn.Contains(request.SearchKey)
                || fa.CaseProcFlow.CaseProcInfo.CaseInfo.RegisterNo.Contains(request.SearchKey) || fa.CaseProcFlow.CaseProcInfo.CaseInfo.AppNo.Contains(request.SearchKey) ||
                fa.CaseProcFlow.CaseProcInfo.CaseInfo.Applicants.Any(app => app.CusApplicant.ApplicantNameCn.Contains(request.SearchKey))
                );
            }


            list = await sql.Where(where).OrderByDescending((fa) => fa.UpdateTime)
               .Aggregate(a => SqlExt.DistinctCount(a.Key.ObjId), out totalCount)
               .Take(request.PageSize).Skip(request.PageSize * (request.PageIndex - 1))
               .Distinct()
               .ToListAsync((fa) => new GetFlowProcessHistoryDto()
               {
                   procNo = fa.CaseProcFlow.CaseProcInfo.ProcNo,
                   caseName = fa.CaseProcFlow.CaseProcInfo.CaseInfo.CaseName,
                   UndertakeUserName = fa.CaseProcFlow.CaseProcInfo.UndertakeUserInfo.CnName,
                   ctrlProcName = fa.CaseProcFlow.CaseProcInfo.BasCtrlProc.CtrlProcZhCn,
                   appNo = fa.CaseProcFlow.CaseProcInfo.CaseInfo.AppNo,
                   TrademarkClass = fa.CaseProcFlow.CaseProcInfo.CaseInfo.TrademarkClass,
                   LegalDueDate = fa.CaseProcFlow.CaseProcInfo.LegalDueDate,
                   Status = fa.CaseProcFlow.CaseProcInfo.DeliInfo.Status,
                   CurNode = fa.CurFlowNode.NameZhCn,
                   CurHandler = fa.CurNodeUserInfo.CnName,
                   SubmitType = fa.PrevAuditTypeId,
                   UpdateTime = fa.UpdateTime,
                   ObjectId = fa.ObjId,
                   Country = fa.CaseProcFlow.CaseProcInfo.CaseInfo.Country.CountryZhCn,
                   ApplyTypeId = fa.CaseProcFlow.CaseProcInfo.CaseInfo.ApplyType.ApplyTypeZhCn,
                   RequirementSubmitDate = fa.CaseProcFlow.CaseProcInfo.RequirementSubmitDate,
                   CtrlProcMark = _freeSql.Select<SysDictionary>().Where(o => o.DictionaryName == "ctrl_proc_mark" && o.Value == fa.CaseProcFlow.CaseProcInfo.CtrlProcMark).ToOne(o => o.TextZhCn),
                   ReturnDocDate = fa.CaseProcFlow.CaseProcInfo.ReturnDocDate,
                   Volume = fa.CaseProcFlow.CaseProcInfo.CaseInfo.Volume,
                   ApplicantName = _freeSql.Select<CusApplicant, CaseApplicantList>().InnerJoin((a, b) => a.ApplicantId == b.ApplicantId).Where((a, b) => b.CaseId == fa.CaseProcFlow.CaseProcInfo.CaseId).OrderByDescending((a, b) => b.IsRepresent).ToOne((a, b) => a.ApplicantNameCn),
                   CustomerName = fa.CaseProcFlow.CaseProcInfo.CaseInfo.Customer.CustomerName,
                   RemainingDays = (fa.CaseProcFlow.CaseProcInfo.BasCtrlProc.TrademarkTab == "0" || fa.CaseProcFlow.CaseProcInfo.BasCtrlProc.TrademarkTab == "3") ? (!fa.CaseProcFlow.CaseProcInfo.ReturnDocDate.HasValue ? null : (fa.CaseProcFlow.CaseProcInfo.ReturnDocDate.Value - DateTime.Now.Date).Days + 1) :
                     (fa.CaseProcFlow.CaseProcInfo.CtrlProcId == "DD886CBE-D8D4-4BE0-97F7-FD521674269A" ? (!fa.CaseProcFlow.CaseProcInfo.RequirementSubmitDate.HasValue ? null : (fa.CaseProcFlow.CaseProcInfo.RequirementSubmitDate.Value - DateTime.Now.Date).Days + 1) :
                     (!fa.CaseProcFlow.CaseProcInfo.LegalDueDate.HasValue ? null : (fa.CaseProcFlow.CaseProcInfo.LegalDueDate.Value - DateTime.Now.Date).Days + 1)),
                   ProcID = fa.CaseProcFlow.CaseProcInfo.ProcId,
                   ProcFlowId = fa.CaseProcFlow.ProcFlowId
               });

            return new PageResult<GetFlowProcessHistoryDto>()
            {
                Data = list,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };
        }
    }
}
