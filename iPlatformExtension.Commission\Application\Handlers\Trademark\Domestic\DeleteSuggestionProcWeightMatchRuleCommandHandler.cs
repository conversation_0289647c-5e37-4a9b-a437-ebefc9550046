﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class DeleteSuggestionProcWeightMatchRuleCommandHandler(ISuggestionProcPointConfigRepository configRepository) 
    : IRequestHandler<DeleteSuggestionProcWeightMatchRuleCommand>
{
    public Task Handle(DeleteSuggestionProcWeightMatchRuleCommand request, CancellationToken cancellationToken)
    {
        var ruleId = request.RuleId;
        return configRepository.DeleteAsync(ruleId, cancellationToken);
    }
}