using System.Text;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR.Pipeline;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.PipelineBehaviors;

internal sealed class DefaultDeliveryBatchItemExceptionHandler<TRequest, TResponse, TException> : IRequestExceptionHandler<TRequest, TResponse, TException> 
    where TRequest : IDeliveryBatchItemCommand where TResponse : DeliveryItemOperationResult,new() where TException : Exception
{
    private readonly ObjectPool<StringBuilder> _stringBuilderPool;

    public DefaultDeliveryBatchItemExceptionHandler(ObjectPool<StringBuilder> stringBuilderPool)
    {
        _stringBuilderPool = stringBuilderPool;
    }


    public Task Handle(TRequest request, TException exception, RequestExceptionHandlerState<TResponse> state,
        CancellationToken cancellationToken)
    {
        var stringBuilder = _stringBuilderPool.Get();
        var result = new TResponse()
        {
            ProcId = request.ProcId,
            Success = false,
        };
        result.Fail(exception, stringBuilder);
        
        state.SetHandled(result);
        _stringBuilderPool.Return(stringBuilder);
        
        return Task.CompletedTask;
    }
}