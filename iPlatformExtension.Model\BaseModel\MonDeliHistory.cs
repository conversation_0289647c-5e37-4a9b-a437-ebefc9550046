using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mon_deli_history", DisableSyncStructure = true)]
	public partial class MonDeliHistory {

		[ Column(Name = "history_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string HistoryId { get; set; }

		[ Column(Name = "allot_count")]
		public int? AllotCount { get; set; }

		[ Column(Name = "deli_day")]
		public DateTime? DeliDay { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

	}

}
