﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <LangVersion>13</LangVersion>
    </PropertyGroup>

<!--    <PropertyGroup>-->
<!--        <ContainerBaseImage>mcr.microsoft.com/dotnet/aspnet:8.0</ContainerBaseImage>-->
<!--        <ContainerRuntimeIdentifier>linux-x64</ContainerRuntimeIdentifier>-->
<!--        <ContainerRegistry>harbor.aciplaw.com</ContainerRegistry>-->
<!--        <ContainerRepository>acip/iplatform.trademark-center</ContainerRepository>-->
<!--        <ContainerImageTag>1.0.0.RELEASE</ContainerImageTag>-->
<!--        <ContainerUser>root</ContainerUser>-->
<!--        <PublishProfile>DefaultContainer</PublishProfile>-->
<!--    </PropertyGroup>-->

<!--    <ItemGroup>-->
<!--        <ContainerPort Include="8087" Type="tcp" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_HTTP_PORTS" Value="8087" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_URLS" Value="http://+:8087" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_ENVIRONMENT" Value="Production" />-->
<!--        <ContainerEnvironmentVariable Include="TZ" Value="Asia/Shanghai" />-->
<!--    </ItemGroup>-->

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DocumentationFile>bin\Debug\iPlatformExtension.TrademarkOperationCenter.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DocumentationFile>bin\Release\iPlatformExtension.TrademarkOperationCenter.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
        <PackageReference Include="Hangfire" Version="1.8.6" />
        <PackageReference Include="Hangfire.MemoryStorage" Version="1.8.0" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.0" />
        <PackageReference Include="nacos-sdk-csharp.AspNetCore" Version="1.3.9" />
        <PackageReference Include="nacos-sdk-csharp.Extensions.Configuration" Version="1.3.9" />
        <PackageReference Include="NLog.DiagnosticSource" Version="5.2.0" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
      <Content Remove="temp\**" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\iPlatformExtension.Repository\iPlatformExtension.Repository.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Update="Applications\GenerateCode\GenerateCommand.tt">
        <LastGenOutput>GenerateCommand.cs</LastGenOutput>
        <Generator>TextTemplatingFileGenerator</Generator>
      </None>
      <None Update="Applications\GenerateCode\GenerateQuery.tt">
        <Generator>TextTemplatingFileGenerator</Generator>
        <LastGenOutput>GenerateQuery.cs</LastGenOutput>
      </None>
      <None Remove="temp\**" />
    </ItemGroup>

    <ItemGroup>
      <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
    </ItemGroup>

    <ItemGroup>
      <Compile Update="Applications\GenerateCode\GenerateCommand.cs">
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
        <DependentUpon>GenerateCommand.tt</DependentUpon>
      </Compile>
      <Compile Update="Applications\GenerateCode\GenerateQuery.cs">
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
        <DependentUpon>GenerateQuery.tt</DependentUpon>
      </Compile>
      <Compile Remove="Applications\Handlers\Delivery\ContactInfoQuery.cs" />
      <Compile Remove="Applications\Models\Flow\FlowAnalyseDto.cs" />
      <Compile Remove="Applications\SchedulerJob\JobRegistry\FlowAnalyseJobRegistry.cs" />
      <Compile Remove="temp\**" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Applications\SchedulerJob\JobRegistry\" />
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Remove="temp\**" />
    </ItemGroup>
    
    <ItemGroup>
        <InternalsVisibleTo Include="iPlatformExtension.TrademarkOperationCenter.Tests" />
    </ItemGroup>
</Project>
