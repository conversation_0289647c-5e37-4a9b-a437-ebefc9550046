﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Flow;
using iPlatformExtension.Public.Applications.Queries.Flow;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Flow
{
    /// <summary>
    /// 流程信息查询
    /// </summary>
    internal sealed class ProcessInformationQueryHandler(IFreeSql freeSql)
        : IRequestHandler<ProcessInformationQuery, IEnumerable<ProcessInformationDto>>
    {
        public async Task<IEnumerable<ProcessInformationDto>> Handle(
            ProcessInformationQuery request,
            CancellationToken cancellationToken
        )
        {
            ArgumentNullException.ThrowIfNull(request.ProcId);
            return await freeSql
                .Select<SysFlowActivity, SysDictionary, SysUserInfo>()
                .LeftJoin(it =>
                    it.t2.DictionaryName == "flow_type" && it.t1.FlowType == it.t2.Value
                )
                .LeftJoin(it => it.t1.CurUserId == it.t3.UserId)
                .Where(it => request.ProcId.Contains(it.t1.ObjId))
                .Where(it => it.t1.Status == 1000)
                .WhereIf(request.FlowType is not null, it => it.t1.FlowType == request.FlowType)
                .ToListAsync(
                    it => new ProcessInformationDto(
                        it.t1.ObjId,
                        it.t1.CurUserId,
                        it.t3.CnName,
                        it.t1.FlowType,
                        it.t2.TextZhCn,
                        it.t3.UserName
                    ),
                    cancellationToken
                );
        }
    }
}
