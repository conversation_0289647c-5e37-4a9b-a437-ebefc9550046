﻿using System.ComponentModel;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;

namespace iPlatformExtension.Outsourcing.Application.Models.Apply;

public class AppCaseOutsourcingDto
{
    /// <summary>
    /// 
    /// </summary>
    [Description("开案案件id")]
    public string AppCaseId { get; set; } = null!;

    /// <summary>
    /// 境外代理信息
    /// </summary>
    [Description("境外代理信息")]
    public ForeignAgencyInfo? AgencyInfo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Description("境外代理说明")]
    public string? ForeignAgencyRemark { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Description("是否可编辑")]
    public bool Editable { get; set; }
}