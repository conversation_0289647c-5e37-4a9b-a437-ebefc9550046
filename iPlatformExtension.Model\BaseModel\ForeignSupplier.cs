﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel 
{

	/// <summary>
	/// 外所供应商
	/// </summary>
	[Table(Name = "foreign_supplier", DisableSyncStructure = true)]
	public partial class ForeignSupplier : IVersionEntity
	{

		/// <summary>
		/// 主键
		/// </summary>
		[Column(Name = "id", IsPrimary = true)]
		public long Id { get; set; }

		/// <summary>
		/// 中文
		/// </summary>
		[Column(Name = "cn_name", StringLength = 1000, IsNullable = false)]
		public string CnName { get; set; } = "";

		/// <summary>
		/// 英文名称
		/// </summary>
		[Column(Name = "en_name", StringLength = 1, IsNullable = false)]
		public string EnName { get; set; } = "";

		/// <summary>
		/// 是否可用
		/// </summary>
		[Column(Name = "is_enable")]
		public bool IsEnable { get; set; } = true;

		/// <summary>
		/// 供应商id
		/// </summary>
		[Column(Name = "supplier_id", StringLength = 50, IsNullable = false)]
		public string SupplierId { get; set; } = null!;

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }
		
		/// <summary>
		/// 更新者
		/// </summary>
		[Column(Name = "updater", StringLength = 50, IsNullable = false)]
		public string Updater { get; set; } = string.Empty;

		/// <summary>
		/// 版本号
		/// </summary>
		[Column(Name = "version", IsVersion = true, IsNullable = false)]
		public int Version { get; set; }

	}

}
