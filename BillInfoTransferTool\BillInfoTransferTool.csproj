<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>13</LangVersion>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\iPlatformExtension.Common\iPlatformExtension.Common.csproj" />
      <ProjectReference Include="..\iPlatformExtension.Repository\iPlatformExtension.Repository.csproj" />
      <ProjectReference Include="..\iPlatformExtension.Service\iPlatformExtension.Service.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="NLog.Extensions.Logging" Version="5.3.8" />
      <PackageReference Include="System.Linq.Async" Version="6.0.1" />
    </ItemGroup>

    <ItemGroup>
      <Content Update="Nlog.config">
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <None Update="Nlog.config">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>
