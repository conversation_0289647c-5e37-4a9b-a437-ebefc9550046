﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_config_filter", DisableSyncStructure = true)]
	public partial class MailConfigFilter {

		[Column(Name = "filter_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FilterId { get; set; }

		[Column(Name = "config_id", StringLength = 50)]
		public string ConfigId { get; set; }

		/// <summary>
		/// 规则执行对象:mail_from:发件人,title:邮件主题,case_direction:邮件相关案件流向,mail_content:邮件正文,mail_sign:邮件签名
		/// </summary>
		[Column(Name = "filter_head", StringLength = 50)]
		public string FilterHead { get; set; }

		/// <summary>
		/// 规则逻辑:index:包含,equal:等于
		/// </summary>
		[Column(Name = "filter_type", StringLength = 50)]
		public string FilterType { get; set; }

		/// <summary>
		/// 规则内容
		/// </summary>
		[Column(Name = "filter_value", StringLength = -1)]
		public string FilterValue { get; set; }

		[Column(Name = "seq", DbType = "int")]
		public int? Seq { get; set; }

	}

}
