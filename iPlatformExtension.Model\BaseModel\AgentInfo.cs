using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "agent_info", DisableSyncStructure = true)]
	public partial class AgentInfo {

		[ Column(Name = "agency_name", StringLength = 200)]
		public string AgencyName { get; set; }

		[ Column(Name = "agent_id", StringLength = 50, IsNullable = false)]
		public string AgentId { get; set; }

		[ Column(Name = "agent_name", StringLength = 100)]
		public string AgentName { get; set; }

		[ Column(Name = "certificate_no", StringLength = 50)]
		public string CertificateNo { get; set; }

		[ Column(Name = "pageno")]
		public int? Pageno { get; set; }

		[ Column(Name = "record_no", StringLength = 50)]
		public string RecordNo { get; set; }

		[ Column(Name = "technical_field", StringLength = 100)]
		public string TechnicalField { get; set; }

		[ Column(Name = "years")]
		public int? Years { get; set; }

	}

}
