using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_host", DisableSyncStructure = true)]
	public partial class MailHost {

		[ Column(Name = "account", StringLength = 100)]
		public string Account { get; set; }

		[ Column(Name = "affair_email", StringLength = 200)]
		public string AffairEmail { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "district", StringLength = 50, IsNullable = false)]
		public string District { get; set; } = "SZ";

		[ Column(Name = "host_id", StringLength = 50, IsNullable = false)]
		public string HostId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "imap_host", StringLength = 100)]
		public string ImapHost { get; set; }

		[ Column(Name = "imap_port", StringLength = 50)]
		public string ImapPort { get; set; }

		[ Column(Name = "is_auto_down")]
		public bool IsAutoDown { get; set; } = false;

		[ Column(Name = "is_convey")]
		public bool IsConvey { get; set; } = false;

		[ Column(Name = "is_default")]
		public bool? IsDefault { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 是否个人邮箱
		/// </summary>
		[ Column(Name = "is_private")]
		public bool IsPrivate { get; set; } = true;

		[ Column(Name = "is_receive")]
		public bool IsReceive { get; set; } = true;

		[ Column(Name = "is_ssl")]
		public bool? IsSsl { get; set; } = false;

		[ Column(Name = "is_system")]
		public bool? IsSystem { get; set; } = false;

		[ Column(Name = "password", StringLength = 500)]
		public string Password { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "show_name", StringLength = 100)]
		public string ShowName { get; set; }

		[ Column(Name = "signature_cn", DbType = "ntext")]
		public string SignatureCn { get; set; }

		[ Column(Name = "signature_en", DbType = "ntext")]
		public string SignatureEn { get; set; }

		[ Column(Name = "signature_jp", DbType = "ntext")]
		public string SignatureJp { get; set; }

		[ Column(Name = "smtp_host", StringLength = 100)]
		public string SmtpHost { get; set; }

		[ Column(Name = "smtp_port", StringLength = 50)]
		public string SmtpPort { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
