﻿using FluentScheduler;
using iPlatformExtension.TrademarkOperationCenter.Applications.SchedulerJob.Jobs;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.SchedulerJob.JobRegistry
{
    public class FlowAnalyseJobRegistry : Registry
    {
        public FlowAnalyseJobRegistry()
        {
            //不允许重复执行（每一次任务都必须执行完成后，才能开启下一次执行，防止并发执行带来的问题）
            NonReentrantAsDefault();
            //每10秒一次循环
            Schedule<FlowAnalyseJob>().ToRunNow().AndEvery(10).Seconds();
        }
    }
}
