﻿using System.Text.Json;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Outsourcing.Infrastructure.Authorization.ProcOutsourcing;

internal sealed class BatchProcOutsourcingAuthorizationHandler(IFreeSql<PlatformFreeSql> freeSql, IOptions<JsonOptions> jsonOptions) : AuthorizationHandler<BatchProcOutsourcingAuthorizationRequirement, HttpContext>
{
    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, BatchProcOutsourcingAuthorizationRequirement requirement,
        HttpContext resource)
    {
        var user = context.User;
        
        var documents = await JsonSerializer.DeserializeAsync<List<ProcJsonPatchDocument>>(resource.Request.Body,
            jsonOptions.Value.JsonSerializerOptions, resource.RequestAborted);

        resource.Request.Body.Seek(0,SeekOrigin.Begin);
        
        var procIds = documents?.Select(x => x.ProcId).Distinct().ToList() ?? [];
        var procInfos = await freeSql.Select<CaseProcInfo>().WithLock().Where(info => procIds.Contains(info.ProcId))
            .ToListAsync(info => new CaseProcInfo()
            {
                ProcId = info.ProcId,
                ProcNo = info.ProcNo,
                UndertakeUserId = info.UndertakeUserId,
                FinishDate = info.FinishDate,
                CaseInfo = new CaseInfo()
                {
                    CaseTypeId = info.CaseInfo.CaseTypeId,
                    Id = info.CaseInfo.Id,
                }
            }, resource.RequestAborted);

        foreach (var failureReason in procInfos
                     .Where(procInfo => user.GetUserId() != procInfo.UndertakeUserId || procInfo.FinishDate is not null || procInfo.CaseInfo.CaseTypeId != CaseType.Trade)
                     .Where(procInfo => !requirement.TryGetRoles(procInfo.CaseInfo.CaseTypeId, out var roles) ||
                                        !roles.Any(user.IsInRole))
                     .Select(procInfo => new AuthorizationFailureReason(this, $"任务[{procInfo.ProcNo}]只有流程人员可以编辑任务委外信息")))
        {
            context.Fail(failureReason);
        }

        if (!context.HasFailed)
        {
            context.Succeed(requirement);
        }
    }
}