﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class UpdateCaseFeeCommandHandler(
    IMapper mapper,
    ICaseFeeRepository caseFeeRepository, 
    IHttpContextAccessor httpContextAccessor) 
    : IRequestHandler<UpdateCaseFeeCommand>
{
    public async Task Handle(UpdateCaseFeeCommand request, CancellationToken cancellationToken)
    {
        var (patchDocument, caseFeeList) = request;
        caseFeeList ??= await caseFeeRepository.GetAsync(patchDocument.FeeId, cancellationToken);

        var dto = mapper.Map<CaseFeePatchDto>(caseFeeList);
        patchDocument.ApplyTo(dto);
        mapper.Map(dto, caseFeeList);

        caseFeeList.CheckOfficialPaymentStatus();
        caseFeeList.UpdateTime = DateTime.Now;
        caseFeeList.UpdateUserId = (httpContextAccessor.HttpContext?.User).GetUserId();

        await caseFeeRepository.UpdateAsync(caseFeeList, cancellationToken);
    }
}