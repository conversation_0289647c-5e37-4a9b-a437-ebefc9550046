﻿using AutoMapper;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Commands.Customer.Contract;
using iPlatformExtension.Public.Applications.Commands.File;
using iPlatformExtension.Public.Applications.Models.File;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Customer.Contract;

internal sealed class CreateContractCommandHandler(
    ISender sender, 
    IMapper mapper,
    IHostEnvironment hostEnvironment,
    IUserInfoRepository userInfoRepository) : IRequestHandler<CreatContractCommand>
{
    public async Task Handle(CreatContractCommand request, CancellationToken cancellationToken)
    {
        var (crmCustomerId, dto) = request;
        var contractId = Guid.CreateVersion7().ToString();
        
        var freeSql = userInfoRepository.Orm;
        var customerId = await freeSql.Select<CusCustomer>().Where(customer => customer.CrmCustomerId == crmCustomerId)
            .ToOneAsync(customer => customer.CustomerId, cancellationToken);
        if (string.IsNullOrEmpty(customerId))
        {
            throw new NotFoundException(crmCustomerId, "客户信息");
        }

        var caseFiles = await dto.Files.Select(formFile => new UploadFileCommand(new FileUploadDto
            {
                File = formFile,
                FileName = formFile.FileName,
                ServerPath = contractId,
                Remark = string.Empty,
                Bucket = hostEnvironment.IsProduction() ? "customer-contract" : "customer-contract-test"
            })).ToAsyncEnumerable()
            .SelectAwaitWithCancellation(async (command, token) =>
            {
                var fileUpload = command.FileUpload;
                var fileId = await sender.Send(command, token);
                return new CaseFile
                {
                    FileId = Guid.CreateVersion7().ToString(),
                    CreateTime = DateTime.Now,
                    CreateUserId = UserIds.Crm,
                    DescId = string.Empty,
                    FileEx = Path.GetExtension(fileUpload.FileName),
                    FileName = fileUpload.FileName,
                    FileNo = $"a002{fileId}",
                    FileSize = fileUpload.File.Length,
                    ObjId = contractId,
                    UploadTimes = 1
                };
            }).ToListAsync(cancellationToken);
        
        var contract = mapper.Map<CusContract>(dto);
        var details = mapper.Map<List<ContractDetail>>(dto.Details);

        ICacheableRepository<string, UserInfoDto> userBaseRepository = userInfoRepository;
        var signatoryUser = await userBaseRepository.GetCacheValueAsync(dto.SignatoryUser, cancellationToken: cancellationToken);
        if (signatoryUser is null)
        {
            throw new NotFoundException(dto.SignatoryUser, "用户信息");
        }

        var operatorId = await userInfoRepository.GetUserIdByUserNameAsync(dto.Operator) ?? string.Empty;
        
        contract.CustomerId = customerId;
        contract.ContractSource = "CRM";
        contract.CreateUserId = operatorId;
        contract.Signatory = signatoryUser.CnName;
        contract.SignatoryUserId = signatoryUser.UserId;
        contract.ContractId = contractId;
        contract.UpdateUserId = operatorId;
        contract.UpdateTime = DateTime.Now;
        
        foreach (var detail in details)
        {
            detail.ContractId = contractId;
            detail.Updater = dto.Operator;
            detail.UpdaterId = contract.UpdateUserId;
            detail.PreSalesSupportId = await userInfoRepository.GetUserIdByUserNameAsync(detail.PreSalesUsername) ??
                                       string.Empty;
            detail.UpdateTime = DateTime.Now;
            detail.IsEnable = true;
        }

        await sender.Send(new SaveContractCommand(caseFiles, contract, details), cancellationToken);

    }
}