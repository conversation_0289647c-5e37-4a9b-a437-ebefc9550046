﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Outsourcing.Application.Models.Proc;

public class NotAssignedTrademarkProcExportDto
{
    /// <summary>
    /// 任务id
    /// </summary>
    [ExcelIgnore]
    public string ProcId { get; set; } = string.Empty;

    /// <summary>
    /// 我方文号
    /// </summary>
    [ExcelColumn(Name = "我方文号")]
    public string Volume { get; set; }=string.Empty;

    /// <summary>
    /// 任务编号
    /// </summary>
    [ExcelColumn(Name = "任务编号")]
    public string ProcNo { get; set; } = string.Empty;
    
    /// <summary>
    /// 任务名称
    /// </summary>
    [ExcelColumn(Name = "任务名称")]
    public string ProcName { get; set; } = string.Empty;

    /// <summary>
    /// 承办人
    /// </summary>
    [ExcelColumn(Name = "承办人")]
    public string Undertaker { get; set; } = string.Empty;
    
    /// <summary>
    /// 案件名称
    /// </summary>
    [ExcelColumn(Name = "商标名称")]
    public string CaseName { get; set; } = string.Empty;

    /// <summary>
    /// 申请途径
    /// </summary>
    [ExcelColumn(Name = "申请途径")]
    public string ApplicationChannel { get; set; } = string.Empty;

    /// <summary>
    /// 案件流向
    /// </summary>
    [ExcelColumn(Name = "案件流向")]
    public string CaseDirection { get; set; } = string.Empty;

    /// <summary>
    /// 国家
    /// </summary>
    [ExcelColumn(Name = "国家/地区")]
    public string Country { get; set; } = string.Empty;
    
    /// <summary>
    /// 客户id
    /// </summary>
    [ExcelIgnore]
    public string CustomerId { get; set; } = string.Empty;

    /// <summary>
    /// 客户名称
    /// </summary>
    [ExcelColumn(Name = "客户名称")]
    public string CustomerName { get; set; } = string.Empty;
   
}