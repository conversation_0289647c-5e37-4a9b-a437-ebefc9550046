﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using MediatR;

namespace iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery;

public interface IDeliveryCommand : IRequest<DeliInfo?>
{
    string ProcId { get; }
    
    int Version { get; }

    public const string AdminUserId = UserIds.Administrator;
}