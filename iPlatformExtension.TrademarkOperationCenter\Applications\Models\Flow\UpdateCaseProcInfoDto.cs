﻿using Microsoft.Identity.Client;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow
{
    public class UpdateCaseProcInfoDto
    {
        /// <summary>
        /// 任务id
        /// </summary>
        public string ProcId { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        public string ProcStatus { get; set; }

        /// <summary>
        /// 代理人状态
        /// </summary>
        public string SubProcStatus { get; set; }

        /// <summary>
        /// 案件id
        /// </summary>
        public string CaseId { get; set; }

        /// <summary>
        /// 商标文件编号
        /// </summary>
        public string LogoFileID { get; set; }

        /// <summary>
        /// 第一核稿人
        /// </summary>
        public string FirstExamineUserId { get; set; }

        /// <summary>
        /// 配案时间
        /// </summary>
        public DateTime? AllocateDate { get; set; }


        /// <summary>
        /// 是否新增任务
        /// </summary>
        public int? IsAddProc { get; set; }

        /// <summary>
        /// 任务备注
        /// </summary>
        public string? ProcNote { get; set; }
    }
}
