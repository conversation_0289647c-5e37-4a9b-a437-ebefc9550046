﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Common.Mediator.Notifications;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Commands.Case;
using iPlatformExtension.Outsourcing.Application.Models.Case;
using iPlatformExtension.Repository.Interface;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Newtonsoft.Json.Serialization;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Case;

internal sealed class CaseOutsourcingSynchronizationHandler(
    ISender sender,
    IFreeSql<PlatformFreeSql> freeSql,
    EntityTypeInfoProvider entityTypeInfoProvider) 
    : IMatchNotificationHandler<EntityChangeNotification>
{
    private Dictionary<string, CaseProcInfo> _procInfos = [];
    
    public ValueTask<bool> MatchAsync(EntityChangeNotification notification, CancellationToken cancellationToken)
    {
        var entityTypeInfo = entityTypeInfoProvider.Get(typeof(CaseProcInfo));
        var agencyPropertyInfo = entityTypeInfo.EntityPropertyInfos[nameof(CaseProcInfo.ForeginAgencyId)];
        var foreignCaseNoPropertyInfo = entityTypeInfo.EntityPropertyInfos[nameof(CaseProcInfo.ForeignNumber)];
        var reports = notification.Reports;
        
        _procInfos = reports
            .Where(report => report.EntityType == typeof(CaseProcInfo))
            .Where(report => report.Type == DbContext.EntityChangeType.Update)
            .Where(report => agencyPropertyInfo.Get!(report.BeforeObject) != agencyPropertyInfo.Get(report.Object) 
                             || foreignCaseNoPropertyInfo.Get!(report.BeforeObject) != foreignCaseNoPropertyInfo.Get(report.Object))
            .Select(report => report.Object).Cast<CaseProcInfo>()
            .GroupBy(x => x.CaseId)
            .ToDictionary(x => x.Key, x => x.OrderByDescending(y => y.CreateTime).First());

        return ValueTask.FromResult(_procInfos.Count > 0);
    }

    public async Task HandleAsync(EntityChangeNotification notification, CancellationToken cancellationToken)
    {
        foreach (var (caseId, caseProcInfo) in _procInfos)
        {
            var latestProcId = await freeSql.Select<CaseProcInfo>().WithLock()
                .Where(procInfo => procInfo.CaseId == caseId)
                .OrderByDescending(procInfo => procInfo.CreateTime)
                .FirstAsync(x => x.ProcId, cancellationToken);
            
            if (latestProcId == caseProcInfo.ProcId && await freeSql.Select<BasCtrlProc>(caseProcInfo.CtrlProcId)
                .AnyAsync(x => x.IsOutsourcing == true, cancellationToken))
            {
                await sender.Send(new UpdateCaseCommand(caseId, new JsonPatchDocument<CasePatchDto>([
                    new Operation<CasePatchDto>(nameof(OperationType.Replace).ToLower(), "/foreignAgencyId", null,
                        caseProcInfo.ForeginAgencyId),
                    new Operation<CasePatchDto>(nameof(OperationType.Replace).ToLower(), "/foreignCaseNo", null,
                        caseProcInfo.ForeignNumber)
                ], new CamelCasePropertyNamesContractResolver())), cancellationToken);
            }
        }
    }
}