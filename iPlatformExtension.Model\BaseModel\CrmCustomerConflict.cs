﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{
    /// <summary>
    /// 客户利冲表
    /// </summary>
    [Table(Name = "crm_customer_conflict", DisableSyncStructure = false)]
    public class CrmCustomerConflict
    {
        /// <summary>
        /// 客户id
        /// </summary>
        [Column(IsPrimary = true)]
        public string id { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(255)", IsNullable = true)]
        public string customItem40__c { get; set; }

        /// <summary>
        /// 关联公司
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(255)", IsNullable = true)]
        public string customItem38__c { get; set; }

        /// <summary>
        /// 客户负责人
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(50)", IsNullable = true)]
        public string customItem34__c { get; set; }

        /// <summary>
        /// 合作业务类型
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(255)", IsNullable = true)]
        public string customItem27__c { get; set; }

        /// <summary>
        /// 排除到期日期
        /// </summary>
        [Description]
        public long customItem11__c { get; set; }

        /// <summary>
        /// 排除业务类型
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(255)", IsNullable = true)]
        public string customItem28__c { get; set; }

        /// <summary>
        /// 排除对象补充说明
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(255)", IsNullable = true)]
        public string customItem37__c { get; set; }

        /// <summary>
        /// 排除对象名称
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(255)", IsNullable = true)]
        public string customItem41__c { get; set; }

        /// <summary>
        /// 其它合作业务类型说明
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(255)", IsNullable = true)]
        public string customItem22__c { get; set; }

        /// <summary>
        /// 其它排除业务类型说明
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(255)", IsNullable = true)]
        public string customItem19__c { get; set; }

        /// <summary>
        /// 排除理由及说明
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(2500)", IsNullable = true)]
        public string customItem14__c { get; set; }

        /// <summary>
        /// 排除到期日期说明
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(255)", IsNullable = true)]
        public string customItem20__c { get; set; }

        /// <summary>
        /// 违约责任
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(2500)", IsNullable = true)]
        public string customItem15__c { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(2500)", IsNullable = true)]
        public string customItem29__c { get; set; }

        /// <summary>
        /// 其他说明
        /// </summary>
        [Description]
        [Column(DbType = "nvarchar(2500)", IsNullable = true)]
        public string customItem17__c { get; set; }
    }
}
