﻿using iPlatformExtension.Public.Applications.Models;
using iPlatformExtension.Public.Applications.Queries.SystemDictionary;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 数据字典控制器
/// </summary>
/// <param name="mediator">中介者依赖注入</param>
[ApiController]
[Route("[controller]")]
public sealed class SystemDictionaryController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// 根据字典名成获取字典内容
    /// </summary>
    /// <param name="dictionaryName">字典名称<br/>
    /// 证件类型：card_type<br/>
    /// 许可类型：licenses_type<br/>
    /// 名义变更类型：nominal_changes_type<br/>
    /// 业务处理类型：trademark_delivery_business_type<br/>
    /// 递交信息法律条款：annulment_law_provision<br/>
    /// 法律事实与依据法律条款：objections_law_provision<br/>
    /// 管控标识：controls_identified<br/>
    /// 递交key：delivery_key<br/>
    /// 公认证标识：country_recognized<br/>
    /// 胜诉奖励日期类型：winning_reward_date_type<br/>
    /// 商标提成规则日期类型：trademark_bonus_date_type<br/>
    /// 裁定结果：determine_results<br/>
    /// </param>
    /// <param name="keyType">键的类型<br/>
    /// Value<br/>
    /// Id<br/>
    /// <remarks>默认不传按Value给值</remarks>
    /// </param>
    /// <returns>字典信息</returns>
    [HttpGet]
    [ResponseCache(Duration = 3600, VaryByQueryKeys = ["dictionaryName", "keyType"], Location = ResponseCacheLocation.Any)]
    public Task<IEnumerable<KeyValuePair<string, string>>> GetAsync(string dictionaryName, KeyType keyType = KeyType.Value)
    {
        return mediator.Send(new SystemDictionaryQuery(dictionaryName, keyType));
    }
}