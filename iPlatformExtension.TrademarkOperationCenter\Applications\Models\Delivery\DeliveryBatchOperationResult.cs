using System.Text;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

internal sealed class DeliveryBatchOperationResult
{
    public bool Success => Results.All(result => result.Success);
    
    public IList<DeliveryItemOperationResult> Results { get; } = new List<DeliveryItemOperationResult>();

    public string Message { get; set; } = "操作成功";

    internal string GetMessage(StringBuilder stringBuilder) => Results.Aggregate(stringBuilder,
        (builder, result) => builder.AppendLine($"任务：{result.ProcId}，{(result.Success ? "结果" : "错误")}：{result.Message}"))
        .ToString();
}