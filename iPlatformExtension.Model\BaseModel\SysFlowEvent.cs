﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 流程事件
/// </summary>
[Table(Name = "sys_flow_event", DisableSyncStructure = true)]
public class SysFlowEvent 
{

	/// <summary>
	/// 主键
	/// </summary>
	[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
	public int Id { get; set; }

	/// <summary>
	/// 创建时间
	/// </summary>
	[Column(Name = "creation_time", InsertValueSql = "getdate()")]
	public DateTime CreationTime { get; set; }

	/// <summary>
	/// 创建者
	/// </summary>
	[Column(Name = "creator", StringLength = 50, IsNullable = false)]
	public string Creator { get; set; } = string.Empty;

	/// <summary>
	/// 消息键
	/// </summary>
	[Column(Name = "message_key", StringLength = 100, IsNullable = false)]
	public string MessageKey { get; set; } = string.Empty;

	/// <summary>
	/// 流程节点id
	/// </summary>
	[Column(Name = "node_id", StringLength = 50, IsNullable = false)]
	public string NodeId { get; set; } = string.Empty;

	/// <summary>
	/// 流程id
	/// </summary>
	[Column(Name = "flow_id", StringLength = 50, IsNullable = false)]
	public string FlowId { get; set; } = string.Empty;

	/// <summary>
	/// 提交方式
	/// </summary>
	[Column(Name = "submit_type", StringLength = 50, IsNullable = false)]
	public string SubmitType { get; set; } = string.Empty;

	/// <summary>
	/// 主题
	/// </summary>
	[Column(Name = "topic", StringLength = 50, IsNullable = false)]
	public string Topic { get; set; } = string.Empty;

	/// <summary>
	/// 更新时间
	/// </summary>
	[Column(Name = "update_time", InsertValueSql = "getdate()")]
	public DateTime UpdateTime { get; set; }

	/// <summary>
	/// 更新者
	/// </summary>
	[Column(Name = "updater", StringLength = 50, IsNullable = false)]
	public string Updater { get; set; } = string.Empty;

	/// <summary>
	/// 事件触发类型
	/// </summary>
	[Column(Name = "event_trigger_type", MapType = typeof(string))]
	public FlowEventTriggerType EventTriggerType { get; set; }

}