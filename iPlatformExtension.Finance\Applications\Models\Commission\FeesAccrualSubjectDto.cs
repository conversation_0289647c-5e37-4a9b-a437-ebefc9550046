﻿using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Applications.Models.Commission;

/// <summary>
/// 费项计提对象
/// </summary>
public sealed class FeesAccrualSubjectDto
{
    /// <summary>
    /// 销售
    /// </summary>
    public UserBaseInfo? Sales { get;  set; }
    
    /// <summary>
    /// 跟案人
    /// </summary>
    public UserBaseInfo? Tracker { get;  set; }
    
    /// <summary>
    /// 商务
    /// </summary>
    public UserBaseInfo? BusinessUser { get;  set; }
    
    /// <summary>
    /// 线索提报人
    /// </summary>
    public UserBaseInfo? ClueUser { get;  set; }

    /// <summary>
    /// 案源类型
    /// </summary>
    public string? CaseSourceType { get; set; }
}