﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.Withdraw3Years;

internal sealed class Withdraw3YearsDeliveryCommandHandler(
    IMediator mediator, 
    IRedisCache<RedisCacheOptionsBase> redisCache, 
    IDeliveryInfoRepository deliveryInfoRepository) 
    : DeliveryCommandHandlerBase(mediator, redisCache, deliveryInfoRepository)
{
    public override Task<SendDeliveryInternalCommand?> HandleSendStartupCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        var deliveryInfo = context.DeliveryInfo;
        if (deliveryInfo.Applicants?.Any(applicant => applicant.IsOtherApplicant) ?? false)
        {
            throw new ApplicationException("暂不支持当前任务在“共同申请/共有商标”的场景下自动递交");
        }
        
        return base.HandleSendStartupCommandAsync(context, cancellationToken);
    }
}