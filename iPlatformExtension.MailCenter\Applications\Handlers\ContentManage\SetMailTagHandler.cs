﻿using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using iPlatformExtension.Common.Extensions;
using Microsoft.AspNetCore.Http;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Common.Helper;
using Microsoft.AspNetCore.Server.HttpSys;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class SetMailTagHandler(IMailTagRepository mailTagRepository, IMailTagListRepository mailTagListRepository, IMailReceiveRepository mailReceiveRepository, IHttpContextAccessor httpContextAccessor) : IRequestHandler<SetMailTagCommand>
    {
        public async Task Handle(SetMailTagCommand request, CancellationToken cancellationToken)
        {
            foreach (var item in request.list)
            {
                var mail = await mailReceiveRepository.GetAsync(item.MailId);
                if (mail != null)
                {
                    await mailTagListRepository.DeleteAsync(o => o.MailId == mail.MailId);
                    foreach (var tagId in item.MailTagIds)
                    {
                        var tag = await mailTagRepository.GetAsync(tagId);
                        if (tag != null)
                        {
                            await mailTagListRepository.InsertAsync(new MailTagList { Id = Guid.NewGuid().ToString(), MailId = item.MailId, TagId = tag.Id });
                        }
                    }
                }
            }
        }
    }
}
