﻿namespace iPlatformExtension.Public.Applications.Models.Customer;

/// <summary>
/// 销售人员模型
/// </summary>
/// <param name="trackUser">销售人员id</param>
/// <param name="trackUserName">销售人员名称</param>
/// <param name="caseType">案件类型</param>
/// <param name="caseDirection">案件流向</param>
/// /// <param name="isEnable">是否有效</param>
public record SearchFollowUserDto(string trackUser, string trackUserName, string caseType, string caseDirection, bool isEnable)
{
    /// <summary>
    /// 案件类型名称
    /// </summary>
    public string caseTypeName { get; set; }

    /// <summary>
    /// 案件流向名称
    /// </summary>
    public string caseDirectionName { get; set; }
};

