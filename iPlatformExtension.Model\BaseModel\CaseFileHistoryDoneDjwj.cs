using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_file_history_done_djwj", DisableSyncStructure = true)]
	public partial class CaseFileHistoryDoneDjwj {

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "create_time", StringLength = 50)]
		public string CreateTime { get; set; }

		[ Column(Name = "ctrl_proc", StringLength = 500)]
		public string CtrlProc { get; set; }

		[ Column(Name = "ctrl_proc_property", StringLength = 50)]
		public string CtrlProcProperty { get; set; }

		[ Column(Name = "ctrl_proc_real", StringLength = 50)]
		public string CtrlProcReal { get; set; }

		[ Column(Name = "file_ex", StringLength = 50)]
		public string FileEx { get; set; }

		[ Column(Name = "file_name", StringLength = 500)]
		public string FileName { get; set; }

		[ Column(Name = "file_no", StringLength = 50)]
		public string FileNo { get; set; }

		[ Column(Name = "file_size")]
		public long? FileSize { get; set; }

		[ Column(Name = "full_name", StringLength = 500)]
		public string FullName { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; }

		[ Column(Name = "name_0_num", StringLength = 50)]
		public string Name0Num { get; set; }

		[ Column(Name = "name_1_num", StringLength = 50)]
		public string Name1Num { get; set; }

		[ Column(Name = "name_2_num", StringLength = 50)]
		public string Name2Num { get; set; }

		[ Column(Name = "name_3_num", StringLength = 50)]
		public string Name3Num { get; set; }

		[ Column(Name = "name_num", StringLength = 50)]
		public string NameNum { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "upload_times")]
		public int? UploadTimes { get; set; }

		[ Column(Name = "volume", StringLength = 500)]
		public string Volume { get; set; }

		[ Column(Name = "volume_real", StringLength = 500)]
		public string VolumeReal { get; set; }

	}

}
