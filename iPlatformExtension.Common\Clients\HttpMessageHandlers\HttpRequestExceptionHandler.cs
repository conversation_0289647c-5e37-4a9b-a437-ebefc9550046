﻿using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.Clients.HttpMessageHandlers;

public class HttpRequestExceptionHandler<TClient>(ILoggerFactory loggerFactory, bool notSuccessThrowException)
    : DelegatingHandler
{
    private readonly ILogger<TClient> _logger = loggerFactory.CreateLogger<TClient>();

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        try
        {
            var response = await base.SendAsync(request, cancellationToken);
            if (!response.IsSuccessStatusCode && notSuccessThrowException)
            {
                throw new HttpRequestException($"非成功响应码{(int)response.StatusCode}", null, response.StatusCode);
            }

            return response;
        }
        catch (HttpRequestException requestException)
        {
            _logger.LogError(requestException, "请求{RequestUri}错误!响应码为：{StatusCode}", request.RequestUri,
                requestException.StatusCode);
            throw;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "请求{RequestUri}错误", request.RequestUri);
            throw;
        }
    }
}