﻿using FileMigration.Applications.Queries;
using iPlatformExtension.Common.Clients.HuaweiObs;
using MediatR;

namespace FileMigration.Applications.Handlers;

public sealed class ObsTemporaryUriQueryHandler : IRequestHandler<HuaweiObsTemporaryUriQuery, string>
{
    private readonly HuaweiObsClient _huaweiObsClient;

    public ObsTemporaryUriQueryHandler(HuaweiObsClient huaweiObsClient)
    {
        _huaweiObsClient = huaweiObsClient;
    }

    public Task<string> Handle(HuaweiObsTemporaryUriQuery request, CancellationToken cancellationToken)
    {
        var fileInfo = request.FileInfo;
        var objectName = Path.Combine(fileInfo.ServerPath, fileInfo.FileName).Replace('\\', '/');
        return Task.FromResult(_huaweiObsClient.GenerateTemporaryUrl(objectName, fileInfo.Bucket, TimeSpan.FromDays(60)).SignUrl);
    }
}