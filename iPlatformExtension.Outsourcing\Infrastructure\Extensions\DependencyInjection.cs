﻿using iPlatformExtension.Outsourcing.Application.Handlers.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Infrastructure.Authorization.SupplierAssignment;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace iPlatformExtension.Outsourcing.Infrastructure.Extensions;

internal static class DependencyInjection
{
    public static IServiceCollection AddSupplierAssignmentAuthorization(
        this IServiceCollection services, string authorizationType, Action<SupplierAssignmentAuthorizationOptions> configureOptions)
    {
        services.AddOptions<SupplierAssignmentAuthorizationOptions>(authorizationType).Configure(configureOptions);
        services.AddScoped<SupplierAssignmentAuthorizationContextFactory>();
        
        return services;
    }
    
    public static IServiceCollection AddNotAssignedQueryAuthorization(
        this IServiceCollection services, string authorizationType, Action<NotAssignedProcQueryOptions> configureOptions)
    {
        services.AddOptions<NotAssignedProcQueryOptions>(authorizationType).Configure(configureOptions);
        services.TryAddScoped<NotAssignedProcQueryContextFactory>();
        
        return services;
    }
}