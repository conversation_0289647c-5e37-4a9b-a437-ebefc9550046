﻿using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkTransfer;

internal sealed class TrademarkTransferInitializeFilesCommandHandler(
    IFreeSql freeSql,
    HuaweiObsClient huaweiObsClient,
    IHttpContextAccessor httpContextAccessor) : 
    BuildProcApplicantFilesCommandHandler(freeSql, huaweiObsClient, httpContextAccessor)
{
    
    protected override string CtrlProcId => CtrlProcIds.TrademarkTransfer;
}