using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_flow_config", DisableSyncStructure = true)]
	public partial class SysFlowConfig {

		[ Column(Name = "flow_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FlowId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "dept_id", StringLength = 50)]
		public string DeptId { get; set; }

		[ Column(Name = "flow_sub_type", StringLength = 50)]
		public string FlowSubType { get; set; }

		[ Column(Name = "flow_type", StringLength = 50)]
		public string FlowType { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_skip")]
		public bool IsSkip { get; set; } = false;

		[ Column(Name = "is_used")]
		public bool IsUsed { get; set; } = false;

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
