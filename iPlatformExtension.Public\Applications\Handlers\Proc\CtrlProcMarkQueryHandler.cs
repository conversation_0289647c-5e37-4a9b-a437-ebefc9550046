﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Proc;

internal sealed class CtrlProcMarkQueryHandler(
    IFreeSql<PlatformFreeSql> freeSql, 
    ISystemDictionaryRepository systemDictionaryRepository) 
    : IRequestHandler<CtrlProcMarkQuery, IEnumerable<INameInfo>>
{
    public async Task<IEnumerable<INameInfo>> Handle(CtrlProcMarkQuery request, CancellationToken cancellationToken)
    {
        var ctrlProcId = request.CtrlProcId;
        var procMarkString = await freeSql.Select<BasCtrlProc>(ctrlProcId).WithLock()
            .ToOneAsync(proc => proc.CtrlProcMark, cancellationToken);

        List<SystemDictionaryInfo> procMarks = [new (string.Empty, string.Empty, string.Empty)];

        if (!string.IsNullOrWhiteSpace(procMarkString))
        {
            foreach (var procMark in procMarkString.Split(';', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries))
            {
                var dictionary = await systemDictionaryRepository.GetAsync(procMark, cancellationToken);
                if (dictionary is not null)
                {
                    procMarks.Add(new SystemDictionaryInfo(dictionary.Value, dictionary.TextZhCn, dictionary.TextEnUs));
                }
            }
        }
        
        return procMarks;
    }
}