﻿using System.Net.Mime;
using System.Text.Json;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Formatters.Output;

public class JsonAsyncEnumerableWrapperOutputFormatter<T> : AsyncEnumerableOutputFormatter
    where T : ApiResult<object>, new()
{
    public JsonAsyncEnumerableWrapperOutputFormatter()
    {
        SupportedMediaTypes.Add(MediaTypeNames.Application.Json);
    }
    
    protected override Task WriteAsync(OutputFormatterWriteContext context, object item, CancellationToken cancellationToken)
    {
        var jsonOptions = GetJsonOptions(context);
        var result = item as T ?? new T().Succeed(item) as T;
        return JsonSerializer.SerializeAsync(context.HttpContext.Response.BodyWriter, result, jsonOptions.JsonSerializerOptions, cancellationToken);
    }

    protected override Task WriteErrorAsync(OutputFormatterWriteContext context, Exception exception, CancellationToken cancellationToken)
    {
        var jsonOptions = GetJsonOptions(context);
        var result = new T();
        result.Fail(exception);
        return JsonSerializer.SerializeAsync(context.HttpContext.Response.BodyWriter, result, jsonOptions.JsonSerializerOptions, cancellationToken);
    }

    private static JsonOptions GetJsonOptions(OutputFormatterWriteContext context)
    {
        var httpContext = context.HttpContext;
        return httpContext.RequestServices.GetRequiredService<IOptions<JsonOptions>>().Value;
    }
}