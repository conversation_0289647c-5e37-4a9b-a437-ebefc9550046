﻿using iPlatformExtension.Common.Mediator.PipelineBehaviors;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.PipelineBehaviors.FeeQuery;
using iPlatformExtension.Finance.Applications.PipelineBehaviors.FeeUpdate;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Extensions;

public static class FinanceDependency
{
    public static void ConfigFinanceMediatRServices(MediatRServiceConfiguration configuration)
    {
        configuration.RegisterServicesFromAssemblyContaining(typeof(FinanceDependency));
        configuration.AddBehavior<IPipelineBehavior<BuildProcInfoCommand, Unit>, CheckProcInfoParametersBehavior>();
        configuration
            .AddBehavior<IPipelineBehavior<BuildApplicantInfoCommand, Unit>, CheckApplicantsParametersBehavior>();
        configuration.AddBehavior<IPipelineBehavior<BuildCaseInfoCommand, Unit>, CheckCaseInfoParametersBehavior>();
        configuration.AddBehavior<IPipelineBehavior<BuildFeeTypesCommand, Unit>, CheckFeeTypeParametersBehavior>();
        configuration.AddBehavior<IPipelineBehavior<BuildCompanyInfoCommand, Unit>, CheckCompanyParametersBehavior>();
        configuration.AddOpenBehavior(typeof(LockBehavior<>), ServiceLifetime.Scoped);
        configuration.AddOpenBehavior(typeof(DefaultFreeSqlUnitOfWorkPipelineBehavior<,>), ServiceLifetime.Scoped);
    }
}