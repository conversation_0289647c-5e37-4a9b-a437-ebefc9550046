﻿using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;
using MediatR;
using Microsoft.AspNetCore.Http;
using System.Linq;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File
{
    public class UploadFileToCaseHandler(ICaseFileRepository caseFileRepository, ICaseProcFlowRepository caseProcFlowRepository,
         IFileDescriptionRepository fileDescriptionRepository,
        IHttpContextAccessor httpContextAccessor) : IRequestHandler<UploadFileToCaseCammand>
    {
        public async Task Handle(UploadFileToCaseCammand request, CancellationToken cancellationToken)
        {
            var currentUser = httpContextAccessor.HttpContext?.User.GetUserID();
            var fileIds = request.Files.Select(dto => dto.Id).ToArray();
            var fileList = await caseFileRepository.Orm.Select<FileListA>().Where(a => fileIds.Contains(a.Id))
                .ToListAsync(cancellationToken);

            List<CaseProcFlow> procFlows = null;
            procFlows = await caseProcFlowRepository.Where(o => request.Files.Any(f => f.ProcId == o.ProcId) && o.FlowType == "trademark_delivery").ToListAsync();

            if (!procFlows.Any())
            {
                procFlows = request.Files.Where(o => !procFlows.Any(b => b.ProcId == o.ProcId))
                    .Select(a =>
                    new CaseProcFlow
                    {
                        ProcFlowId = Guid.NewGuid().ToString(),
                        ProcId = a.ProcId,
                        FlowType = "trademark_delivery",
                        CreateTime = DateTime.Now,
                    }).DistinctBy(o=>o.ProcId).ToList();

                if (procFlows.Count() > 0)
                {
                    await caseProcFlowRepository.InsertAsync(procFlows);
                }
            }
            var files = await request.Files.ToAsyncEnumerable()
                .JoinAwait(fileList.ToAsyncEnumerable(), dto => new ValueTask<int>(dto.Id), a => new ValueTask<int>(a.Id),
            async (fileInfo, a) => new CaseFile()
            {
                FileNo = $"a002{fileInfo.Id}",
                ObjId = fileInfo.ProcId,
                FileName = fileInfo.FileName,
                FileSize = a.FileSize,
                DescId = fileInfo.FileDescriptionId,
                FileEx = Path.GetExtension(fileInfo.FileName),
                CreateTime = DateTime.Now,
                CreateUserId = currentUser ?? string.Empty,
                SFileName = fileInfo.FileName
            }).ToArrayAsync(cancellationToken);

            var files2 = await files.ToAsyncEnumerable()
              .JoinAwait(procFlows.ToAsyncEnumerable(), file => new ValueTask<string>(file.ObjId), flow => new ValueTask<string>(flow.ProcId),
            async (file, flow) =>
              new CaseFile()
              {
                  FileId = Guid.NewGuid().ToString(),
                  FileNo = file.FileNo,
                  ObjId = !string.IsNullOrEmpty(flow.ProcFlowId) ? flow.ProcFlowId : Guid.NewGuid().ToString(),
                  FileName = file.FileName,
                  FileSize = file.FileSize,
                  DescId = file.DescId,
                  FileEx = Path.GetExtension(file.FileName),
                  CreateTime = DateTime.Now,
                  CreateUserId = currentUser ?? string.Empty,
                  SFileName = file.FileName
              }).ToArrayAsync(cancellationToken);
            await caseFileRepository.InsertAsync(files2, cancellationToken);
        }
    }
}
