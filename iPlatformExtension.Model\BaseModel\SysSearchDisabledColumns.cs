using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_search_disabled_columns", DisableSyncStructure = true)]
	public partial class SysSearchDisabledColumns {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_id", StringLength = 50, IsNullable = false)]
		public string ColumnId { get; set; }

		[ Column(Name = "search_form_code", StringLength = 50, IsNullable = false)]
		public string SearchFormCode { get; set; }

		[ Column(Name = "table_name", StringLength = 50, IsNullable = false)]
		public string TableName { get; set; }

	}

}
