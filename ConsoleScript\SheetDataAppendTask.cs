﻿using System.Text;
using System.Xml;

namespace ConsoleScript;

public class SheetDataAppendTask
{
    private static readonly Encoding utf8WithBom = new UTF8Encoding(true);

    private const string RNamespaceUri = "http://schemas.openxmlformats.org/officeDocument/2006/relationships";

    private const string XNamespaceUri = "http://schemas.openxmlformats.org/spreadsheetml/2006/main";

    private StringBuilder _dataBuilder = new ();
    
    public async Task Work()
    {
        const string fileName = "sheet1.xml";

        await using var fileStream = new FileStream(fileName, FileMode.Open);

        var document = new XmlDocument();
        
        var nameManager = new XmlNamespaceManager(document.NameTable);
        nameManager.AddNamespace("r", RNamespaceUri);
        nameManager.AddNamespace("x", XNamespaceUri);
        
        document.Load(fileStream);
        
        var xmlNode = document.SelectSingleNode("x:worksheet/x:sheetData", nameManager);
        if (xmlNode is null) return;

        _dataBuilder.Append(xmlNode.InnerXml);
        
        xmlNode.RemoveAll();
        
        
    }
}