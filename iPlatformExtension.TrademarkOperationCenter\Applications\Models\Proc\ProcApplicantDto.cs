﻿using iPlatformExtension.Model.Dto;
using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc
{
    /// <summary>
    /// 修改申请人信息Dto
    /// </summary>
    public  class ProcApplicantDto: ApplicantDetailDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 任务id
        /// </summary>
        [Required]
        public string ProcId { get; set; } = null!;

        /// <summary>
        /// 变更类型
        /// </summary>
        public string ChangeType { get; set; } = null!;

        /// <summary>
        /// 变更类型中文
        /// </summary>
        public string ChangeTypeName { get; set; } = null!;

    }
}
