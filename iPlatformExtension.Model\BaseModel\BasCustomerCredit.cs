using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_customer_credit", DisableSyncStructure = true)]
	public partial class BasCustomerCredit {

		/// <summary>
		/// 信誉等级ID
		/// </summary>
		[ Column(Name = "level_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string LevelId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 英文名称
		/// </summary>
		[ Column(Name = "level_en_us", StringLength = 200)]
		public string LevelEnUs { get; set; }

		/// <summary>
		/// 日文名称
		/// </summary>
		[ Column(Name = "level_ja_jp", StringLength = 200)]
		public string LevelJaJp { get; set; }

		/// <summary>
		/// 中文名称
		/// </summary>
		[ Column(Name = "level_zh_cn", StringLength = 100)]
		public string LevelZhCn { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
