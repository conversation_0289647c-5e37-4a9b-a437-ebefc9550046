﻿
using Confluent.Kafka;
using Hangfire.Dashboard;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.MailCenter.Infrastructure.MQ;
using iPlatformExtension.Model.Dto.MailCenter;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using Microsoft.AspNetCore.Http;
using MimeKit;
using Nacos.V2.Utils;
using iPlatformExtension.Common.MQ.MailMQ;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Message
{
    /// <summary>
    /// 接收邮件阅读消息生产者
    /// </summary>
    /// <param name="producer"></param>
    /// <param name="httpContextAccessor"></param>
    /// <param name="sql"></param>
    public class ReaderMessageProducer<PERSON><PERSON>ler(IMediator mediator) : IRequestHandler<MailCenterMessageQuery>
    {
        public async Task Handle(MailCenterMessageQuery request, CancellationToken cancellationToken)
        {
            if (request.ReaderList != null && request.ReaderList.Count() > 0)
            {
                if (request.ReaderList.First().MailType == SysEnum.MailType.Receive.ToString())
                {
                    await mediator.Send(new ReceiveReaderMessageQuery(request.ReaderList, request.OperationType, request.MailId));
                }
                else
                {
                    await mediator.Send(new SentReaderMessageQuery(request.ReaderList, request.OperationType, request.MailId));
                }
            }
        }
    }
}
