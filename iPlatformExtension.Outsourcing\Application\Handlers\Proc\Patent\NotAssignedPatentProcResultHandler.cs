﻿using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc.Patent;

internal sealed class NotAssignedPatentProcResultHandler(
    IBaseCountryRepository countryRepository,
    IBaseCtrlProcRepository ctrlProcRepository,
    IApplyTypeRepository applyTypeRepository,
    ICustomerRepository customerRepository,
    ISystemDictionaryRepository dictionaryRepository) 
    : INotificationHandler<NotAssignedPatentProcResultNotification>
{
    public async Task Handle(NotAssignedPatentProcResultNotification notification, CancellationToken cancellationToken)
    {
        var results = notification.NotAssignedProcResults;
        
        foreach (var notAssignedPatentProcDto in results)
        {
            notAssignedPatentProcDto.ProcName =
                await ctrlProcRepository.GetChineseValueAsync(notAssignedPatentProcDto.ProcName) ?? string.Empty;
            notAssignedPatentProcDto.Country = await countryRepository.GetChineseValueAsync(notAssignedPatentProcDto.CountryId) ?? string.Empty;
            notAssignedPatentProcDto.CustomerName = await customerRepository.GetChineseValueAsync(notAssignedPatentProcDto.CustomerId) ?? string.Empty;
            notAssignedPatentProcDto.CaseDirection = await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.CaseDirection, notAssignedPatentProcDto.CaseDirection);
            notAssignedPatentProcDto.ApplyType =
                await applyTypeRepository.GetChineseValueAsync(notAssignedPatentProcDto.ApplyType) ?? string.Empty;
            notAssignedPatentProcDto.RemainingDays =
                ((notAssignedPatentProcDto.InternalInitialDraftDeadline?.Date ?? new DateTime(2099, 1, 1)) -
                 DateTime.Today.AddDays(5)).Days;
        }
    }
}