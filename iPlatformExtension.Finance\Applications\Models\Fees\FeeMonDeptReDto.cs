﻿namespace iPlatformExtension.Finance.Applications.Models.Fees
{
    /// <summary>
    /// 费项创收分类返回model
    /// </summary>
    public class FeeMonDeptReDto
    {
        /// <summary>
        /// 费项id
        /// </summary>
        public string FeeId { get; set; } = default!;

        /// <summary>
        /// 案件类型
        /// </summary>
        public string CaseType { get; set; } = default!;

        /// <summary>
        /// 案件流向
        /// </summary>
        public string CaseDirection { get; set; } = default!;

        /// <summary>
        ///  申请类型/商标类型
        /// </summary>
        public string? ApplyTypeId { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        public string BusinessTypeId { get; set; } = default!;

        /// <summary>
        /// 创收分类
        /// </summary>
        public string MonDept { get; set; } = default!;
    }
}
