﻿using iPlatformExtension.Common.Attributes;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.SysConfig
{
    public class SaveMailHostCommand : IRequest, IUnitOfWorkCommandMysql
    {

        public string? HostId { get; set; }

        public string Account { get; set; }

        //[CopyIgnoreField]
        //public string? OperatorUser { get; set; }

        //[CopyIgnoreField]
        //public DateTime CreateTime { get; set; } = DateTime.Now;

        public string ImapHost { get; set; }

        public int? ImapPort { get; set; }

        /// <summary>
        /// 是否转达
        /// </summary>
        public bool IsConvey { get; set; }

        public bool IsEnabled { get; set; }

        /// <summary>
        /// 是否个人邮箱
        /// </summary>
        public bool IsPrivate { get; set; }


        public bool IsSsl { get; set; }

        /// <summary>
        /// 是否系统邮箱
        /// </summary>
        public bool IsSystem { get; set; }

        /// <summary>
        /// 邮箱密码(编辑时:留空不会改变密码;新增时必填.)
        /// </summary>
        [CopyIgnoreField]
        public string? Password { get; set; }

        public string? Remark { get; set; }

        public string ShowName { get; set; }

        public string SmtpHost { get; set; }

        public int? SmtpPort { get; set; }

        //public string? UpdateBy { get; set; }

        //[CopyIgnoreField]
        //public DateTime? UpdateTime { get; set; } = DateTime.Now;


        public List<MailHostAccess> Accesses { get; set; } = new List<MailHostAccess>();
    }

}
