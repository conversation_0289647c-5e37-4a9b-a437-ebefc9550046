﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{

    public class GetMailHostListHandler(IMailHostRepository mailHostRepository, IFreeSql<PlatformFreeSql> msSql, IHttpContextAccessor httpContextAccessor) : IRequestHandler<GetMailHostListQuery, PageResult<GetMailHostListDto>>
    {
        public async Task<PageResult<GetMailHostListDto>> Handle(GetMailHostListQuery request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var userinfo = await msSql.Select<SysUserInfo>().Where(o => o.UserId == userId).ToOneAsync(cancellationToken);
            var isAdmin = await msSql.Select<SysUserInfo, SysUserRole, SysRoleInfo>()
                .InnerJoin(o => o.t1.UserId == o.t2.UserId)
                .InnerJoin(o => o.t2.RoleId == o.t3.RoleId)
                .AnyAsync(o => o.t1.UserId == userId && o.t3.RoleCode == "MailSuperManager", cancellationToken);
            var hostIds = await mailHostRepository.Orm.Select<MailAccess>()
                .WhereIf(!isAdmin,
                    o => (o.UseType == UseTypeEnum.User && o.UseId == userId) && o.AccessMode == HostAccessModeEnum.Manager)
                .WhereIf(!isAdmin,
                    o => (o.UseType == UseTypeEnum.User && o.UseId == userId || o.AccessMode == HostAccessModeEnum.Manager
                         || o.UseType == UseTypeEnum.Dept && o.UseId == userinfo.DeptId))
                .ToListAsync(o => o.HostId, cancellationToken);
            var lst = await mailHostRepository
                      .WhereIf(!string.IsNullOrWhiteSpace(request.Search), w => w.Account.Contains(request.Search) || w.ShowName.Contains(request.Search))
                      .WhereIf(!string.IsNullOrWhiteSpace(request.Account), w => w.Account == request.Account)
                      .WhereIf(!string.IsNullOrWhiteSpace(request.ShowName), w => w.ShowName.Contains(request.ShowName))
                      .WhereIf(!isAdmin, o => hostIds.Any(h => h == o.HostId))
                      .Where(o => !o.IsPrivate)
                .Count(out long totalCount)
                .OrderBy(o => o.Account)
                .Take(request.PageSize).Skip(request.PageSize * (request.PageIndex - 1))
                .ToListAsync<GetMailHostListDto>(cancellationToken);
            var userList = await msSql.Select<SysUserInfo>().Where(u => lst.Any(o => o.CreateBy == u.UserId || o.UpdateBy == u.UserId)).ToListAsync((u => new { UserName = u.CnName, UserId = u.UserId }), cancellationToken).ConfigureAwait(false);
            lst.ForEach(o =>
            {
                o.CreateBy = userList.FirstOrDefault(u => u.UserId == o.CreateBy)?.UserName;
                o.UpdateBy = userList.FirstOrDefault(u => u.UserId == o.UpdateBy)?.UserName;
            });


            return new PageResult<GetMailHostListDto>()
            {
                Data = lst,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };
        }
    }
}
