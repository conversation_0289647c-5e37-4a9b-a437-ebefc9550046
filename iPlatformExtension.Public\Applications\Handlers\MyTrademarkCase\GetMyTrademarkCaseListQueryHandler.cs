using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Company;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;
using iPlatformExtension.Repository.Interface;
using MediatR;
using Microsoft.AspNetCore.Http;
using System.Linq;
using System.Linq.Expressions;

namespace iPlatformExtension.Public.Applications.Handlers.MyTrademarkCase
{
    /// <summary>
    /// 我的商标列表
    /// </summary>
    internal sealed class GetMyTrademarkCaseListQueryHandler(
        IFreeSql freeSql,
        ICaseProcInfoRepository caseProcInfoRepository,
        IHttpContextAccessor httpContextAccessor,
        IBasCaseProcStatusRepository iBasCaseProcStatusRepository
    ) : IRequestHandler<GetMyTrademarkCaseListQuery, IEnumerable<GetMyTrademarkCaseListDto>>
    {
        private static readonly IReadOnlyList<string> foreignCaseDirections = [CaseDirection.IO, CaseDirection.OO];
        
        public async Task<IEnumerable<GetMyTrademarkCaseListDto>> Handle(GetMyTrademarkCaseListQuery request, CancellationToken cancellationToken)
        {
            var ctrlProcList = await freeSql.Select<SysDictionary, SysPaperTab>().LeftJoin(it => it.t1.DictionaryId == it.t2.DictionaryId)
                .WhereIf(request.PrivateValue != "BasicOther", it => it.t1.Value == request.PrivateValue)
                .WhereIf(request.PrivateValue == "BasicOther", it => it.t1.Value != request.PrivateValue)
                .Where(it => it.t1.DictionaryName == "trademark_tab_out" && it.t2.CtrlProcId != null).Distinct()
                .ToListAsync(it => it.t2.CtrlProcId, cancellationToken);
            //获取操作用户信息
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var caseProcList = caseProcInfoRepository.Where(it => it.CaseInfo.CaseTypeId == "T" && it.UndertakeUserId == userId && it.FinishDate == null
                                                 && !new List<string>() { CASE_PROC_STATUS.WC, CASE_PROC_STATUS.BCL, CASE_PROC_STATUS.YCL }.Contains(it.ProcStatusId)
                                                 && (it.OutUser == null || it.OutUser == "") && foreignCaseDirections.Contains(it.CaseInfo.CaseDirection))
                .LeftJoin(it => it.PrivateId == it.FlowPrivate.PrivateId && it.FlowPrivate.UserId == userId)
                .WhereIf(request.PrivateValue != "BasicOther", it => ctrlProcList.Contains(it.CtrlProcId))
                .WhereIf(request.PrivateValue == "BasicOther", it => !ctrlProcList.Contains(it.CtrlProcId))
                .WhereIf(!string.IsNullOrWhiteSpace(request.SearchKey), it => it.CaseInfo.CaseName.Contains(request.SearchKey) || it.ProcNo.Contains(request.SearchKey)
                    || it.BasCtrlProc.CtrlProcZhCn.Contains(request.SearchKey) || it.CaseInfo.AppNo.Contains(request.SearchKey) || it.CaseInfo.RegisterNo.Contains(request.SearchKey)
                    || it.CaseInfo.Customer.CustomerFullName.Contains(request.SearchKey) || it.CaseInfo.Country.CountryZhCn.Contains(request.SearchKey))
                .WhereIf(request.ProcStatusId is not null, it => it.ProcStatusId == request.ProcStatusId)
                .WithLock();

            if (request.FPrivate is not null)
            {
                Expression<Func<CaseProcInfo, bool>> wherePrivate = activity => request.FPrivate.Contains(activity.FlowPrivate.PrivateId);
                if (request.FPrivate.Contains("PENDING"))
                {
                    wherePrivate = wherePrivate.Or(x => x.FlowPrivate.PrivateId == null || x.FlowPrivate.PrivateId == "");
                }
                caseProcList = caseProcList.Where(wherePrivate);
            }
            //排序
            caseProcList = string.Equals(request.Order, "asc", comparisonType: StringComparison.OrdinalIgnoreCase) ? caseProcList.OrderByPropertyName(SortTranslation(request.Sort)) : caseProcList.OrderByPropertyName(SortTranslation(request.Sort), false);

            var caseApplicant = freeSql.Select<CaseApplicantList, CusApplicant>()
                .InnerJoin(it => it.t1.ApplicantId == it.t2.ApplicantId)
                .WithTempQuery(it => new
                {
                    it.t1.CaseId, 
                    it.t2.ApplicantNameCn
                }).Distinct();

            if (request.PageIndex is not null && request.PageSize is not null)
            {
                caseProcList = caseProcList
                    .Page(request.PageIndex.Value, request.PageSize.Value)
                    .Count(out var totalCount);
                var myTrademarkCaseListDtoResult = await caseProcList.ToListAsync(
                    it => new GetMyTrademarkCaseListDto(
                        it.ProcId,
                        it.BasCtrlProc.CtrlProcZhCn,
                        it.ProcStatusId,
                        it.CaseInfo.CaseName,
                        string.Join(
                            ";",
                            caseApplicant
                                .Where(x => x.CaseId == it.CaseId)
                                .ToList(y => y.ApplicantNameCn)
                        ),
                        it.CaseInfo.AppNo,
                        it.CaseInfo.RegisterNo,
                        it.CaseInfo.Volume,
                        it.CaseInfo.Country.CountryZhCn,
                        it.CaseInfo.TrademarkClass ?? "",
                        it.CaseInfo.OfficialStatus.CaseStatusZhCn,
                        it.EntrustDate,
                        it.ProcAppDate,
                        it.LegalDueDate,
                        it.DurationOutpost,
                        it.DurationCustomer,
                        it.FeedbackDay,
                        it.InstructDay,
                        it.VoucherDay,
                        it.NotificationReceiptDate,
                        it.CaseId,
                        it.SubProcStatusId,
                        it.FinishDate,
                        it.SubProcStatus.TextZhCn,
                        it.DeadlineDocuments!.Value.Date.ToString("yyyy-MM-dd"),
                        it.CaseFollowDeadline!.Value.Date.ToString("yyyy-MM-dd"),
                        it.ProcNo,
                        it.CaseInfo.Customer.CustomerName,
                        it.CaseInfo.Customer.CustomerId,
                        it.Version
                    ),
                    cancellationToken
                );
                var getMyTrademarkCaseListDtos = await myTrademarkCaseListDtoResult
                    .ToAsyncEnumerable()
                    .SelectAwait(async myTrademarkCaseListDto =>
                    {
                        if (myTrademarkCaseListDto.ProcStatusId is not null)
                        {
                            var chineseKeyValueAsync =
                                await iBasCaseProcStatusRepository.GetCacheValueAsync(
                                    myTrademarkCaseListDto.ProcStatusId,
                                    cancellationToken: cancellationToken
                                );
                            if (chineseKeyValueAsync is not null)
                            {
                                myTrademarkCaseListDto.ProcStatusName =
                                    chineseKeyValueAsync!.TextZhCn;
                            }
                        }
                        return myTrademarkCaseListDto;
                    })
                    .ToListAsync(cancellationToken: cancellationToken);
                return new PageResult<GetMyTrademarkCaseListDto>()
                {
                    Data = getMyTrademarkCaseListDtos,
                    Page = request.PageIndex.Value,
                    PageSize = request.PageSize.Value,
                    Total = totalCount
                };
            }

            var myTrademarkCaseListDtos = await caseProcList.ToListAsync(
                it => new GetMyTrademarkCaseListDto(
                    it.ProcId,
                    it.BasCtrlProc.CtrlProcZhCn,
                    it.ProcStatusId,
                    it.CaseInfo.CaseName,
                    string.Join(
                        ";",
                        caseApplicant
                            .Where(x => x.CaseId == it.CaseId)
                            .ToList(y => y.ApplicantNameCn)
                    ),
                    it.CaseInfo.AppNo,
                    it.CaseInfo.RegisterNo,
                    it.CaseInfo.Volume,
                    it.CaseInfo.Country.CountryZhCn,
                    it.CaseInfo.TrademarkClass ?? "",
                    it.CaseInfo.OfficialStatus.CaseStatusZhCn,
                    it.EntrustDate,
                    it.ProcAppDate,
                    it.LegalDueDate,
                    it.DurationOutpost,
                    it.DurationCustomer,
                    it.FeedbackDay,
                    it.InstructDay,
                    it.VoucherDay,
                    it.NotificationReceiptDate,
                    it.CaseId,
                    it.SubProcStatusId,
                    it.FinishDate,
                    it.SubProcStatus.TextZhCn,
                    it.DeadlineDocuments!.Value.Date.ToString("yyyy-MM-dd"),
                    it.CaseFollowDeadline!.Value.Date.ToString("yyyy-MM-dd"),
                    it.ProcNo,
                    it.CaseInfo.Customer.CustomerName,
                    it.CaseInfo.Customer.CustomerId,
                    it.Version
                ),
                cancellationToken
            );
            return await myTrademarkCaseListDtos
                .ToAsyncEnumerable()
                .SelectAwait(async myTrademarkCaseListDtoResult =>
                {
                    if (myTrademarkCaseListDtoResult.ProcStatusId is not null)
                    {
                        var chineseKeyValueAsync =
                            await iBasCaseProcStatusRepository.GetCacheValueAsync(
                                myTrademarkCaseListDtoResult.ProcStatusId,
                                cancellationToken: cancellationToken
                            );
                        myTrademarkCaseListDtoResult.ProcStatusName =
                            chineseKeyValueAsync!.TextZhCn;
                    }
                    return myTrademarkCaseListDtoResult;
                })
                .ToListAsync(cancellationToken: cancellationToken);
        }

        private string SortTranslation(string Sort)
        {
            switch (Sort)
            {
                case "CountryZhCn":
                    return "CaseInfo.Country.CountryZhCn";
                case "CtrlProcZhCn":
                    return "BasCtrlProc.CtrlProcZhCn";
                case "SubProcStatusId":
                    return "SubProcStatusId";
                case "customerName":
                    return "CaseInfo.Customer.CustomerName";

                default:
                    return Sort;
            }
        }
    }
}
