using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_info_standard_money", DisableSyncStructure = true)]
	public partial class BillInfoStandardMoney {

		[ Column(Name = "amount_with_tax", DbType = "money")]
		public decimal? AmountWithTax { get; set; }

		[ Column(Name = "annual_amount", DbType = "money")]
		public decimal? AnnualAmount { get; set; }

		[ Column(Name = "bill_amount", DbType = "money")]
		public decimal? BillAmount { get; set; }

		[ Column(Name = "bill_id", StringLength = 50, IsNullable = false)]
		public string BillId { get; set; }

		[ Column(Name = "service_amount", DbType = "money")]
		public decimal? ServiceAmount { get; set; }

		[ Column(Name = "tax", DbType = "money")]
		public decimal? Tax { get; set; }

		[ Column(Name = "third_party_amount", DbType = "money")]
		public decimal? ThirdPartyAmount { get; set; }

	}

}
