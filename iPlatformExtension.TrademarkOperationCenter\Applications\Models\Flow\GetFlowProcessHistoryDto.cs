﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.TrademarkDelivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto
{
    public class GetFlowProcessHistoryDto
    {
        /// <summary>
        /// 任务编号
        /// </summary>
        public string procNo { get; set; }

        /// <summary>
        /// 商标名称
        /// </summary>
        public string caseName { get; set; }

        /// <summary>
        /// 承办人
        /// </summary>
        public string UndertakeUserName { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string ctrlProcName { get; set; }

        /// <summary>
        /// 申请号
        /// </summary>
        public string appNo { get; set; }

        /// <summary>
        /// 商标类别
        /// </summary>
        public string TrademarkClass { get; set; }

        /// <summary>
        /// 官方期限
        /// </summary>
        public DateTime? LegalDueDate { get; set; }

        /// <summary>
        /// 流程状态
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 非标商品
        /// </summary>
        public bool? NonStandard { get; set; }

        /// <summary>
        /// 已检查递交无误
        /// </summary>
        public bool IsCheck { get; set; }

        /// <summary>
        /// 当前节点
        /// </summary>
        public string CurNode { get; set; }

        /// <summary>
        /// 当前节点办理人
        /// </summary>
        public string CurHandler { get; set; }

        /// <summary>
        /// 到达方式
        /// </summary>
        public string SubmitType { get; set; }


        /// <summary>
        /// 到达时间/最后办理时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 上一节点
        /// </summary>
        public string PreNode { get; set; }

        /// <summary>
        /// 关联的对象id
        /// </summary>
        public string ObjectId { get; set; } = default!;

        ///// <summary>
        ///// 递交状态
        ///// </summary>
        //public string DeliveryStatus { get; set; }


        /// <summary>
        /// 操作结果
        /// </summary>
        public bool OperationResult { get; set; }

        /// <summary>
        /// 递交状态
        /// </summary>
        public string DeliveryStatusText { get { return GetDeliveryStatus(); } }


        public bool? IsAuto { get; set; } = false;

        /// <summary>
        /// 获取递交状态描述
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ArgumentOutOfRangeException">当前状态不在定义的枚举范围内</exception>
        public string GetDeliveryStatus()
        {
            var deliveryStatus = (DeliveryStatus)(Status ?? 0);
            if (!OperationResult)
            {
                return "递交失败";
            }

            var statusDescription = deliveryStatus switch
            {
                DeliveryStatus.Ready => "未发起",
                DeliveryStatus.Ordered => "递交中",
                DeliveryStatus.Delivering => "递交中",
                DeliveryStatus.Complete => "递交成功",
                DeliveryStatus.Confirmed => "递交成功",
                DeliveryStatus.Stopped => "未发起",
                _ => throw new ArgumentOutOfRangeException()
            };

            if (IsAuto ?? false)
            {
                return statusDescription;
            }

            return "未发起";
        }

        /// <summary>
        /// 国家/地区
        /// </summary>
        public string Country { get; set; }

        /// <summary>
        /// 商标类型
        /// </summary>
        public string ApplyTypeId { get; set; }

        /// <summary>
        /// 要求递交日
        /// </summary>
        public DateTime? RequirementSubmitDate { get; set; }

        /// <summary>
        /// 任务标识
        /// </summary>
        public string CtrlProcMark { get; set; }

        /// <summary>
        /// 返稿期限
        /// </summary>
        public DateTime? ReturnDocDate { get; set; }

        /// <summary>
        /// 申请人(第一申请人)
        /// </summary>
        public string ApplicantName { get; set; }

        /// <summary>
        /// 我方文号
        /// </summary>
        public string Volume { get; set; }


        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }


        /// <summary>
        /// 剩余天数
        /// null 时,表示此任务没有期限.
        /// </summary>
        public int? RemainingDays { get; set; }


        /// <summary>
        /// 核稿任务流程id
        /// </summary>
        public string ProcFlowId { get; set; }


        /// <summary>
        /// 任务ID
        /// </summary>
        public string ProcID { get; set; }


    }
}
