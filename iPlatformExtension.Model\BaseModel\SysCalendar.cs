using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_calendar", DisableSyncStructure = true)]
	public partial class SysCalendar {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "calendar_date", DbType = "date")]
		public DateTime? CalendarDate { get; set; }

		[ Column(Name = "day")]
		public int? Day { get; set; }

		[ Column(Name = "month")]
		public int? Month { get; set; }

		[ Column(Name = "week_name_en_us", StringLength = 50)]
		public string WeekNameEnUs { get; set; }

		[ Column(Name = "week_name_ja_jp", StringLength = 50)]
		public string WeekNameJaJp { get; set; }

		[ Column(Name = "week_name_zh_cn", StringLength = 50)]
		public string WeekNameZhCn { get; set; }

		[ Column(Name = "week_no")]
		public int? WeekNo { get; set; }

		[ Column(Name = "year")]
		public int? Year { get; set; }

	}

}
