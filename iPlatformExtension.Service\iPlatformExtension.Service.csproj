<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>iPlatformExtension.Service</RootNamespace>
    <LangVersion>13</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="obj\**" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Remove="obj\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="obj\**" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\iPlatformExtension.Common\iPlatformExtension.Common.csproj" />
    <ProjectReference Include="..\iPlatformExtension.Repository\iPlatformExtension.Repository.csproj" />
  </ItemGroup>

</Project>
