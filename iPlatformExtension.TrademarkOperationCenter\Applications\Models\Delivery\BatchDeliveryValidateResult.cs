﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 批量递交验证结果
/// </summary>
public class BatchDeliveryValidateResult
{
    /// <summary>
    /// 请勿同时选中多种任务
    /// </summary>
    public const string MultipartCtrlProcMessage = "请勿同时选中多种任务";

    /// <summary>
    /// 请勿选中多个节点的递交代办
    /// </summary>
    public const string MultipartNodeMessage = "请勿选中多个节点的递交代办";

    /// <summary>
    /// 以下待办不可退回，请先取消选中
    /// </summary>
    public const string CannotRejectMessage = "以下待办不可退回，请先取消选中";

    /// <summary>
    /// 选中了不可提交的代办，请取消选中
    /// </summary>
    public const string CannotSubmitMessage = "选中了不可提交的代办，请取消选中";

    /// <summary>
    /// 以下待办不可移交，请先取消选中
    /// </summary>
    public const string CannotHandOverMessage = "以下待办不可移交，请先取消选中";

    /// <summary>
    /// 选中了已启动流程的任务，请取消选中
    /// </summary>
    public const string CannotStartupMessage = "选中了已启动流程的任务，请取消选中";

    /// <summary>
    /// 选中了不可发起自动递交的待办，请先取消选中
    /// </summary>
    public const string CannotLaunchDeliveryMessage = "选中了不可发起自动递交的待办，请先取消选中";

    /// <summary>
    /// 任务承办人发生变更，你已不是以下任务的承办人，请取消选择
    /// </summary>
    public const string InvalidUndertakerMessage = "任务承办人发生变更，你已不是以下任务的承办人，请取消选择";
    
    /// <summary>
    /// 验证成功与否
    /// </summary>
    public bool Success { get; set; } = true;

    /// <summary>
    /// 任务编号
    /// </summary>
    public ICollection<string> ProcNos { get; set; }

    /// <summary>
    /// 结果信息
    /// </summary>
    public string Message { get; set; } = "验证成功";

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="capacity"></param>
    public BatchDeliveryValidateResult(int capacity)
    {
        ProcNos = new HashSet<string>(capacity);
    }

    /// <summary>
    /// 强制抛出异常
    /// </summary>
    public void ThrowIfValidateFail()
    {
        if (Success)
            return;

        var ex = new ApplicationException(Message);
        if (ProcNos.Count != 0)
        {
            ex.Data[nameof(ProcNos)] = ProcNos;
        }

        throw ex;
    }

    /// <summary>
    /// 结果失败
    /// </summary>
    /// <param name="message">失败信息</param>
    /// <param name="procNo">任务编号</param>
    /// <returns>批量递交验证结果</returns>
    public BatchDeliveryValidateResult Fail(string message, string? procNo = null)
    {
        Success = false;
        Message = message;
        if (!string.IsNullOrWhiteSpace(procNo))
        {
            ProcNos.Add(procNo);
        }

        return this;
    }
}