﻿using iPlatformExtension.Public.Applications.Models.Customer;
using MediatR;

namespace iPlatformExtension.Public.Applications.Queries.Customer;

/// <summary>
/// 销售查询
/// </summary>
/// <param name="CrmCustomerId">crm客户id</param>
/// <param name="Enable">默认查所有，true为只查有效</param>
public record SearchFollowUserQuery(string CrmCustomerId, bool Enable = false) : IRequest<IEnumerable<SearchFollowUserDto>>;

