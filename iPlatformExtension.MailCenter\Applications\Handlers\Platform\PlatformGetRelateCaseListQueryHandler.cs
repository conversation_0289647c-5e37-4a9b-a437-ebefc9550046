﻿using System.Data.Common;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Platform;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;
using PlatformGetRelateCaseListDto = iPlatformExtension.MailCenter.Applications.Models.Platform.PlatformGetRelateCaseListDto;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Platform;

/// <summary>
/// 关联案件
/// </summary>
internal sealed class PlatformGetRelateCaseListQueryHandler(
    IFreeSql<MailCenterFreeSql> freeSql,
    IFreeSql<PlatformFreeSql> platformFreeSql,
    IBaseCtrlProcRepository baseCtrlProcRepository,
    IUserInfoRepository userInfoRepository
) : IRequestHandler<PlatformGetRelateCaseListQuery, IEnumerable<PlatformGetRelateCaseListDto>>
{
    public async Task<IEnumerable<PlatformGetRelateCaseListDto>> Handle(
        PlatformGetRelateCaseListQuery request,
        CancellationToken cancellationToken
    )
    {
        var procList = await platformFreeSql
            .Select<CaseProcInfo>()
            .WithLock()
            .Where(it => it.CaseId == request.CaseId)
            .ToListAsync(it => new { it.ProcId, it.CtrlProcId }, cancellationToken);
        var dap = new List<DbParameter>();
        var tempQuery = freeSql
            .Select<MailCorrelative, MailReceive, MailSend, MailReceiveFlow>()
            .LeftJoin(it => it.t1.MailId == it.t2.MailId)
            .LeftJoin(it => it.t1.MailId == it.t3.MailId)
            .LeftJoin(it => it.t2.MailId == it.t4.MailId)
            .WithParameters(dap)
            .WithLock()
            .Where(it =>
                it.t1.ObjId == request.CaseId
                && it.t1.CorrelateType == SysEnum.CorrelateType.Case.ToString()
            )
            .WithTempQuery(it => new PlatformGetRelateCaseListDto(
                it.t1.MailId,
                it.t2.MailNo ?? it.t3.MailTo,
                it.t2.MailSubject ?? it.t3.MailSubject,
                it.t2.MailFrom ?? it.t3.MailFrom,
                it.t2.MailDate ?? it.t3.MailDate,
                it.t2.Status ?? 0,
                it.t1.CreateBy,
                it.t1.CreateTime,
                it.t1.ObjId,
                it.t2.MailId == null ? "发件" : "收件",
                it.t4.SendName
            ));
        var proList = procList.Select(x => x.ProcId);

        var withTempQuery = await freeSql
            .Select<MailCorrelative, MailReceive, MailSend, MailReceiveFlow>()
            .LeftJoin(it => it.t1.MailId == it.t2.MailId)
            .LeftJoin(it => it.t1.MailId == it.t3.MailId)
            .LeftJoin(it => it.t2.MailId == it.t4.MailId)
            .WithLock()
            .WithParameters(dap)
            .Where(it =>
                proList.Contains(it.t1.ObjId)
                && it.t1.CorrelateType == SysEnum.CorrelateType.Proc.ToString()
            )
            .WithTempQuery(it => new PlatformGetRelateCaseListDto(
                it.t1.MailId,
                it.t2.MailNo ?? it.t3.MailTo,
                it.t2.MailSubject ?? it.t3.MailSubject,
                it.t2.MailFrom ?? it.t3.MailFrom,
                it.t2.MailDate ?? it.t3.MailDate,
                it.t2.Status ?? 0,
                it.t1.CreateBy ?? it.t3.MailFrom,
                it.t1.CreateTime,
                it.t1.ObjId,
                it.t2.MailId == null ? "发件" : "收件",
                it.t4.SendName
            ))
            .UnionAll(tempQuery)
            .OrderByDescending(it => it.CreateTime)
            .ToListAsync(cancellationToken);

        return await withTempQuery
            .ToAsyncEnumerable()
            .SelectAwait(async it =>
            {
                if (it.ObjId is not null)
                {
                    var ctrlProcId = procList.FirstOrDefault(x => x.ProcId == it.ObjId)?.CtrlProcId;
                    if (ctrlProcId != null)
                    {
                        var ctrlProc = await baseCtrlProcRepository.GetCacheValueAsync(ctrlProcId);
                        it.CtrlProc = new
                        {
                            CtrlProcId = ctrlProcId,
                            cnName = ctrlProc?.CtrlProcZhCn,
                        };
                    }
                }
                if (it.CreateByTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                        userInfoRepository;
                    it.CreateBy = new
                    {
                        CnName = (
                            await userBaseInfoRepository.GetCacheValueAsync(it.CreateByTemp)
                        )?.CnName ?? "",
                        UserId = it.CreateByTemp,
                    };
                }
                return it;
            })
            .ToListAsync(cancellationToken: cancellationToken);
    }
}
