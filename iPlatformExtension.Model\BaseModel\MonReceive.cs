using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mon_receive", DisableSyncStructure = true)]
	public partial class MonReceive {

		[ Column(Name = "receive_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ReceiveId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "account_id", StringLength = 50)]
		public string AccountId { get; set; }

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "balance", DbType = "money")]
		public decimal? Balance { get; set; }

		[ Column(Name = "client_id", StringLength = 50)]
		public string ClientId { get; set; }

		[ Column(Name = "client_id_text", StringLength = 500)]
		public string ClientIdText { get; set; }

		[ Column(Name = "client_id2", StringLength = 200)]
		public string ClientId2 { get; set; }

		[ Column(Name = "client_id2_text", StringLength = 2000)]
		public string ClientId2Text { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "district", StringLength = 20)]
		public string District { get; set; }

		[ Column(Name = "manage_company", StringLength = 50)]
		public string ManageCompany { get; set; }

		[ Column(Name = "payer", StringLength = 100)]
		public string Payer { get; set; }

		[ Column(Name = "rate")]
		public double? Rate { get; set; }

		[ Column(Name = "receive_bank", StringLength = 50)]
		public string ReceiveBank { get; set; }

		[ Column(Name = "receive_date")]
		public DateTime? ReceiveDate { get; set; }

		[ Column(Name = "receive_type", StringLength = 20)]
		public string ReceiveType { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "sales_user_id", StringLength = 50)]
		public string SalesUserId { get; set; }

		[ Column(Name = "status")]
		public int? Status { get; set; } = 0;

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
