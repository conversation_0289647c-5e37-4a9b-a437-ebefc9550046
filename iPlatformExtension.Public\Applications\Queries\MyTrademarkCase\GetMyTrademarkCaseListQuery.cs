﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using MediatR;

namespace iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;

/// <summary>
/// 获取我的商标列表
/// </summary>
/// <param name="FPrivate">个人标签</param>
/// <param name="PrivateValue">标签值</param>
/// <param name="Sort">排序</param>
/// <param name="ProcStatusId">任务状态id</param>
/// <param name="SearchKey">搜索词</param>
/// <param name="Order">排序顺序,SubProcStatusId:代理人状态</param>
public record GetMyTrademarkCaseListQuery(List<string>? FPrivate, string? PrivateValue, string? SearchKey, string? ProcStatusId, string? Sort = nameof(CaseProcInfo.LegalDueDate), string? Order = "asc") : PageModel, IRequest<IEnumerable<GetMyTrademarkCaseListDto>>;

