﻿using Confluent.Kafka;
using iPlatformExtension.Common.MQ.KafKa.Consumer;
using iPlatformExtension.Common.MQ.KafKa.Producer;
using iPlatformExtension.Common.MQ.KafKa.Route;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics;
using System.Text;
using iPlatformExtension.Common.MQ.KafKa.Handlers.Consumer;
using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.Common.MQ.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;

namespace iPlatformExtension.Common.Extensions;

public static class KafkaExtension
{
    public static ConsumersBuilder<TMessage> AddKafKaConsumers<TMessage>(this IServiceCollection services)
    {
        services.AddHostedService<ConsumerService<TMessage>>();
        var builder = new ConsumersBuilder<TMessage>(services, new ConsumeDelegatesBuilder<TMessage>(),
            services.AddOptions<ConsumerOptions<TMessage>>());
        services.AddHostedService<ConcurrentConsumeBackgroundService<TMessage>>();
        return builder;
    }

    public static ProducerBuilder<TMessage> AddKafKaProducer<TMessage>(this IServiceCollection services)
    {
        return new ProducerBuilder<TMessage>(services.AddOptions<ProducerOptions<TMessage>>(), services);
    }

    public static Task<DeliveryResult<MessageKey, TMessage>> ProduceMessageAsync<TMessage>(this IProducer<MessageKey, TMessage> producer, string topic, MessageKey messageKey,
        TMessage message, CancellationToken cancellationToken = default)
    {
        return producer.ProduceAsync(topic, new Message<MessageKey, TMessage>()
        {
            Key = messageKey,
            Value = message,
            Headers = []
        }, cancellationToken);
    }

    internal static async Task<bool> TryConsumerAsync<TMessage>(this ConsumeDelegates<TMessage> delegates, 
        IServiceScopeFactory scopeFactory, ConsumeResult<MessageKey, TMessage> messageResult)
    {
        var result = false;
        
        var key = messageResult.Message.Key;
        var value = messageResult.Message.Value;
                    
        var activity = new Activity(key);
        var headers = messageResult.Message.Headers;
        if (headers is not null && headers.TryGetLastBytes(HeaderNames.TraceParent, out var traceContext) && traceContext?.Length > 0)
        {
            activity.SetParentId(Encoding.UTF8.GetString(traceContext));
        }

        var scope = scopeFactory.CreateScope();
        var serviceProvider = scope.ServiceProvider;
        
        var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
        var logger = loggerFactory.CreateLogger("iPlatformExtension.Common.Extensions.KafkaExtension");

        var contextAssessor = serviceProvider.GetRequiredService<ConsumeContextAccessor>();
        var consumeContext = new KafkaConsumeContext<MessageKey, TMessage>(messageResult, serviceProvider);
        await consumeContext.SetUserAsync();
        contextAssessor.ConsumeContext = consumeContext;
        
        activity.Start();
                    
        try
        {
            if (!delegates.TryGetDelegate(key, out var consumeDelegate))
            {
                logger.LogWarning("消息路由{Key}查找失败!追踪ID{TraceId}", key, key.TraceId);
                return result;
            }

            var service = serviceProvider.GetRequiredService(consumeDelegate.ConsumerType);
            await consumeDelegate.ConsumeAsync(service, value);
            result = true;
        }
        catch (Exception e)
        {
            logger.LogError(e, "消息[{Key}]:[{Value}]消费失败！追踪ID{TraceId}", key, value, key.TraceId);
            var handlers = serviceProvider.GetServices<IConsumeFailedHandler>();
            result = (await handlers.ToAsyncEnumerable().AggregateAwaitAsync(new ConsumeFailedContext<MessageKey, TMessage>(messageResult, e), async (context, handler) =>
            {
                if (!context.Handled)
                {
                    await handler.HandleAsync(context);
                }
                return context;
            })).Handled;
        }
        finally
        {
            activity.Stop();
            activity.Dispose();

            contextAssessor.ConsumeContext = null;
            
            scope.Dispose();
        }

        return result;
    }

    public static ProducerBuilder<TMessage> ConfigureServerOptions<TMessage>(
        this ProducerBuilder<TMessage> producerBuilder, string configSectionPath)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(configSectionPath);

        return producerBuilder.ConfigureServerOptions(_ => { }, configSectionPath);
    }

    public static ProducerBuilder<TMessage> ConfigureProducer<TMessage>(this ProducerBuilder<TMessage> producerBuilder,
        Action<ProducerOptions<TMessage>> configureOptions)
    {
        return producerBuilder.ConfigureProducer((producerOptions, _) => configureOptions(producerOptions));
    }
}