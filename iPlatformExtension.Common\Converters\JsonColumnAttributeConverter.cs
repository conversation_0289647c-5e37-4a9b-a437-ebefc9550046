using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Common.Converters
{
    /// <summary>
    /// 处理带有Column特性的JSON转换器
    /// </summary>
    public class JsonColumnAttributeConverter<T> : JsonConverter<T> where T : class
    {
        private readonly JsonSerializerOptions _baseOptions;

        /// <summary>
        /// 初始化JsonColumnAttributeConverter
        /// </summary>
        /// <param name="options">基础JSON序列化选项</param>
        public JsonColumnAttributeConverter(JsonSerializerOptions? options = null)
        {
            // 创建一个新的选项实例，避免循环引用
            _baseOptions = new JsonSerializerOptions(options ?? new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseUpper,
                WriteIndented = false,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                DictionaryKeyPolicy = JsonNamingPolicy.CamelCase
            });

            // 移除自身，避免循环引用
            foreach (var converter in _baseOptions.Converters)
            {
                if (converter is JsonColumnAttributeConverter<T>)
                {
                    _baseOptions.Converters.Remove(converter);
                    break;
                }
            }
        }

        /// <inheritdoc />
        public override T Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            // 使用基础选项进行反序列化
            var result = JsonSerializer.Deserialize<T>(ref reader, _baseOptions);
            if (result == null)
            {
                throw new JsonException($"无法将JSON反序列化为{typeToConvert.Name}类型");
            }

            // 处理Column特性的属性映射
            ProcessColumnAttributes(result, reader);

            return result;
        }

        /// <inheritdoc />
        public override void Write(Utf8JsonWriter writer, T value, JsonSerializerOptions options)
        {
            // 使用基础选项进行序列化
            JsonSerializer.Serialize(writer, value, _baseOptions);
        }

        /// <summary>
        /// 处理对象的Column特性
        /// </summary>
        /// <param name="obj">要处理的对象</param>
        /// <param name="reader">JSON读取器</param>
        private void ProcessColumnAttributes(T obj, Utf8JsonReader reader)
        {
            var type = typeof(T);
            // 处理当前类型的属性及其嵌套对象
            ProcessObjectProperties(obj, reader);
        }

        /// <summary>
        /// 递归处理对象属性中的Column特性，包括嵌套对象
        /// </summary>
        /// <param name="obj">要处理的对象</param>
        /// <param name="reader">JSON读取器</param>
        private void ProcessObjectProperties(object obj, Utf8JsonReader reader)
        {
            if (obj == null) return;

            var type = obj.GetType();
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            
            foreach (var property in properties)
            {
                // 处理带有Column特性的属性
                var columnAttr = property.GetCustomAttribute<ColumnAttribute>();
                if (columnAttr != null && !string.IsNullOrEmpty(columnAttr.Name))
                {
                    // 如果属性有Column特性，尝试从JSON中获取对应的值
                    var columnName = columnAttr.Name;
                    var propertyValue = TryGetPropertyValue(obj, reader, columnName, property.PropertyType);
                    if (propertyValue != null)
                    {
                        property.SetValue(obj, propertyValue);
                    }
                }

                // 递归处理复杂类型属性（嵌套对象）
                var propertyValues = property.GetValue(obj);
                if (propertyValues != null)
                {
                    if (IsComplexType(property.PropertyType))
                    {
                        // 处理单个复杂对象
                        ProcessObjectProperties(propertyValues, reader);
                    }
                    else if (IsCollectionType(property.PropertyType))
                    {
                        // 处理集合类型
                        ProcessCollectionProperty(propertyValues, reader);
                    }
                }
            }
        }

        /// <summary>
        /// 处理集合类型属性
        /// </summary>
        /// <param name="collection">集合对象</param>
        /// <param name="reader">JSON读取器</param>
        private void ProcessCollectionProperty(object collection, Utf8JsonReader reader)
        {
            // 处理数组
            if (collection is Array array)
            {
                foreach (var item in array)
                {
                    if (item != null && IsComplexType(item.GetType()))
                    {
                        ProcessObjectProperties(item, reader);
                    }
                }
                return;
            }

            // 处理IEnumerable类型的集合
            if (collection is System.Collections.IEnumerable enumerable)
            {
                foreach (var item in enumerable)
                {
                    if (item != null && IsComplexType(item.GetType()))
                    {
                        ProcessObjectProperties(item, reader);
                    }
                }
            }
        }

        /// <summary>
        /// 判断类型是否为复杂类型（可能包含嵌套对象）
        /// </summary>
        /// <param name="type">要判断的类型</param>
        /// <returns>是否为复杂类型</returns>
        private bool IsComplexType(Type type)
        {
            // 排除基本类型、字符串、枚举等简单类型
            if (type.IsPrimitive || type == typeof(string) || type == typeof(decimal) || 
                type == typeof(DateTime) || type == typeof(DateTimeOffset) || 
                type == typeof(TimeSpan) || type == typeof(Guid) || 
                type.IsEnum || type == typeof(object))
            {
                return false;
            }

            // 排除集合类型，因为它们需要特殊处理
            if (IsCollectionType(type))
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 判断类型是否为集合类型
        /// </summary>
        /// <param name="type">要判断的类型</param>
        /// <returns>是否为集合类型</returns>
        private bool IsCollectionType(Type type)
        {
            // 检查是否为数组
            if (type.IsArray)
            {
                return true;
            }

            // 检查是否为泛型集合
            if (type.IsGenericType)
            {
                var genericType = type.GetGenericTypeDefinition();
                return genericType == typeof(List<>) || 
                       genericType == typeof(Dictionary<,>) ||
                       genericType == typeof(HashSet<>) ||
                       genericType == typeof(IEnumerable<>) ||
                       genericType == typeof(ICollection<>) ||
                       genericType == typeof(IList<>);
            }

            // 检查是否实现了IEnumerable接口（非泛型）
            return typeof(System.Collections.IEnumerable).IsAssignableFrom(type) &&
                   type != typeof(string); // 排除字符串，虽然它实现了IEnumerable
        }

        /// <summary>
        /// 尝试从JSON中获取属性值
        /// </summary>
        /// <param name="obj">对象实例</param>
        /// <param name="reader">JSON读取器</param>
        /// <param name="columnName">列名</param>
        /// <param name="propertyType">属性类型</param>
        /// <returns>属性值</returns>
        private object? TryGetPropertyValue(object obj,Utf8JsonReader reader, string columnName, Type propertyType)
        {
            try
            {
                // 这里需要根据实际情况实现从JSON中提取指定列名的值
                // 由于Utf8JsonReader是前向只读的，这里简化处理
                // 实际应用中可能需要更复杂的逻辑

                var result = JsonSerializer.Deserialize<T>(ref reader, _baseOptions);
                // 获取当前对象的JSON表示
                var json = JsonSerializer.Serialize(obj, _baseOptions);
                var document = JsonDocument.Parse(json);

                // 尝试获取指定列名的属性
                if (document.RootElement.TryGetProperty(columnName, out var element))
                {
                    return JsonSerializer.Deserialize(element.GetRawText(), propertyType, _baseOptions);
                }

                return null;
            }
            catch
            {
                return null;
            }
        }
    }
}