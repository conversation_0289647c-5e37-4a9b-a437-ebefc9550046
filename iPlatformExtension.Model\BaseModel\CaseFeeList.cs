using System.Linq.Expressions;
using System.Text.Json.Serialization;
using FreeSql.DataAnnotations;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Model.BaseModel;

[Table(Name = "case_fee_list", DisableSyncStructure = true)]
public class CaseFeeList : ISystemLoggable<CaseFeeList>
{
	#region 划拨特殊处理编码

		/// <summary>
		/// 法律特殊业务类型编码
		/// </summary>
		private static readonly string[] lawCodes = ["LS", "LF", "LZ", "SS-P", "SS-T"];

	/// <summary>
	/// 商标特殊业务编码
	/// </summary>
	private const string TrademarkCode = "BS";

	#endregion

	/// <summary>
	/// 费用主键ID
	/// </summary>
	[ Column(Name = "fee_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
	public string FeeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

	/// <summary>
	/// 标准金额
	/// </summary>
	[ Column(Name = "amount", DbType = "money")]
	public decimal? Amount { get; set; }

	/// <summary>
	/// 待确认
	/// </summary>
	[ Column(Name = "auto_rule_id", StringLength = 50)]
	public string AutoRuleId { get; set; }

	/// <summary>
	/// 是否自动规则产生
	/// </summary>
	[ Column(Name = "auto_type", StringLength = 50, IsNullable = false)]
	public string AutoType { get; set; } = "0";

	/// <summary>
	/// 结算方式 d
	/// </summary>
	[ Column(Name = "balance_way", StringLength = 50)]
	public string BalanceWay { get; set; }

	/// <summary>
	/// 待确认?
	/// </summary>
	[ Column(Name = "base_type", StringLength = 50)]
	public string BaseType { get; set; }

	/// <summary>
	/// 账单日期
	/// </summary>
	[ Column(Name = "bill_date")]
	public DateTime? BillDate { get; set; }

	/// <summary>
	/// 账单号码
	/// </summary>
	[ Column(Name = "bill_no", StringLength = 50)]
	public string BillNo { get; set; }

	/// <summary>
	/// 案件ID/案件标识?
	/// </summary>
	[ Column(Name = "case_id", StringLength = 50)]
	public string CaseId { get; set; }

	/// <summary>
	/// 系数
	/// </summary>
	[ Column(Name = "coefficient")]
	public int? Coefficient { get; set; }

	/// <summary>
	/// 创建时间
	/// </summary>
	[ Column(Name = "create_time")]
	public DateTime? CreateTime { get; set; }

	/// <summary>
	/// 创建用户
	/// </summary>
	[ Column(Name = "create_user_id", StringLength = 50)]
	public string? CreateUserId { get; set; }

	/// <summary>
	/// 币别
	/// </summary>
	[ Column(Name = "currency_id", StringLength = 50)]
	public string CurrencyId { get; set; }

	/// <summary>
	/// 日期限?
	/// </summary>
	[ Column(Name = "date_day")]
	public int DateDay { get; set; } = 0;

	/// <summary>
	/// 月期限?
	/// </summary>
	[ Column(Name = "date_month")]
	public int DateMonth { get; set; } = 0;

	/// <summary>
	/// 日期类型
	/// </summary>
	[ Column(Name = "date_type", StringLength = 50)]
	public string DateType { get; set; }

	/// <summary>
	/// 年日期?
	/// </summary>
	[ Column(Name = "date_year")]
	public int DateYear { get; set; } = 0;

	/// <summary>
	/// 折扣(%)
	/// </summary>
	[ Column(Name = "discount", StringLength = 50)]
	public string Discount { get; set; }

	/// <summary>
	/// 费用类型 OA 等 待确认
	/// </summary>
	[ Column(Name = "fee_class", StringLength = 50)]
	public string FeeClass { get; set; }

	/// <summary>
	/// 费减金额
	/// </summary>
	[ Column(Name = "fee_reduce_amount")]
	public double? FeeReduceAmount { get; set; }

	/// <summary>
	/// 费减类型
	/// </summary>
	[ Column(Name = "fee_reduce_type", StringLength = 50)]
	public string FeeReduceType { get; set; }

	/// <summary>
	/// 费用名称 关联表 bas_fee_type_name
	/// </summary>
	[ Column(Name = "fee_type_name_id", StringLength = 50)]
	public string FeeTypeNameId { get; set; }

	/// <summary>
	/// 费用类型名称导航属性
	/// </summary>
	[Navigate(nameof(BasFeeTypeName.NameId))]
	public virtual BasFeeTypeName FeeTypeName { get; set; } = default!;

	/// <summary>
	/// 未使用?
	/// </summary>
	[ Column(Name = "head_user_type", StringLength = 50)]
	public string? HeadUserType { get; set; }

	/// <summary>
	/// 发票编号
	/// </summary>
	[ Column(Name = "invoice_no", StringLength = 300)]
	public string InvoiceNo { get; set; }

	/// <summary>
	/// 发票类型 d
	/// </summary>
	[ Column(Name = "invoice_type", StringLength = 50)]
	public string InvoiceType { get; set; }

	/// <summary>
	/// 是否自动生成费用
	/// </summary>
	[ Column(Name = "is_auto")]
	public bool IsAuto { get; set; } = false;

	/// <summary>
	/// 是否启用编辑(未使用?)
	/// </summary>
	[ Column(Name = "is_edit")]
	public bool IsEdit { get; set; } = true;

	/// <summary>
	/// 是否有效
	/// </summary>
	[ Column(Name = "is_enabled")]
	public bool? IsEnabled { get; set; } = true;

	/// <summary>
	/// 首缴年度
	/// </summary>
	[ Column(Name = "is_first_pay_annual")]
	public bool IsFirstPayAnnual { get; set; } = false;

	/// <summary>
	/// 收入
	/// </summary>
	[ Column(Name = "is_income")]
	public bool? IsIncome { get; set; } = false;

	/// <summary>
	/// 向官方缴费
	/// </summary>
	[ Column(Name = "is_officer")]
	public bool IsOfficer { get; set; } = true;

	/// <summary>
	/// 向客户请款
	/// </summary>
	[ Column(Name = "is_request")]
	public bool IsRequest { get; set; } = true;

	/// <summary>
	/// 是否被锁定
	/// </summary>
	[Column(IsIgnore = true)]
	public bool Locked => ReceiveStatus != "0" || (OfficerStatus != "0" && OfficerStatus != "2000") || PayStatus != "0";

	/// <summary>
	/// 待确认
	/// </summary>
	[ Column(Name = "item_value", StringLength = 50)]
	public string ItemValue { get; set; }

	/// <summary>
	/// 32420 待确认
	/// </summary>
	[ Column(Name = "n_FeeID", DbType = "numeric(18,0)")]
	public decimal? NFeeID { get; set; }

	/// <summary>
	/// XXNNN 待确认
	/// </summary>
	[ Column(Name = "n_StatusID", StringLength = 50)]
	public string NStatusID { get; set; }

	/// <summary>
	/// 66628 待确认
	/// </summary>
	[ Column(Name = "n_YearFeeID", DbType = "numeric(18,0)")]
	public decimal? NYearFeeID { get; set; }

	/// <summary>
	/// 公布时间
	/// </summary>
	[ Column(Name = "notice_date")]
	public DateTime? NoticeDate { get; set; }

	/// <summary>
	/// 本所名称
	/// </summary>
	[ Column(Name = "office_name", StringLength = 2000)]
	public string OfficeName { get; set; }

	/// <summary>
	/// 官方名称英文
	/// </summary>
	[ Column(Name = "office_name_en_us", StringLength = 200)]
	public string OfficeNameEnUs { get; set; }

	/// <summary>
	/// 官方名称
	/// </summary>
	[ Column(Name = "office_name_zh_cn", StringLength = 200)]
	public string OfficeNameZhCn { get; set; }

	[ Column(Name = "officer_lock_del")]
	public bool OfficerLockDel { get; set; } = false;

	/// <summary>
	/// 缴官费状态
	/// </summary>
	[ Column(Name = "officer_status", StringLength = 50, IsNullable = false)]
	public string OfficerStatus { get; set; } = "0";

	/// <summary>
	/// 官费标识
	/// </summary>
	[Column(Name = "official_fee_mark", MapType = typeof(int))]
	public OfficialFeeMark OfficialFeeMark { get; set; }
		
	/// <summary>
	/// 官费标识说明
	/// </summary>
	[Column(Name = "official_fee_mark_description")]
	public string? OfficialFeeMarkDescription { get; set; }

	/// <summary>
	/// 缴官费通知书核查状态
	/// </summary>
	[Column(Name = "official_notification_checked")]
	public bool OfficialNotificationChecked { get; set; }

	/// <summary>
	/// 待支出清单核查状态
	/// </summary>
	[Column(Name = "payment_list_checked")]
	public bool PaymentListChecked { get; set; }

	/// <summary>
	/// 不缴费说明
	/// </summary>
	[Column(Name = "not_payment_instructions")]
	public string? NotPaymentInstructions { get; set; }

	/// <summary>
	/// 缴费发文日
	/// </summary>
	[Column(Name = "official_payment_publication_date")]
	public DateTime? OfficialPaymentPublicationDate { get; set; }

	/// <summary>
	/// 付款单号
	/// </summary>
	[ Column(Name = "pay_bill_no", StringLength = 50)]
	public string PayBillNo { get; set; }

	/// <summary>
	/// 实付日期
	/// </summary>
	[ Column(Name = "pay_cooperation_date")]
	public DateTime? PayCooperationDate { get; set; }

	/// <summary>
	/// 付款日期
	/// </summary>
	[ Column(Name = "pay_date")]
	public DateTime? PayDate { get; set; }

	/// <summary>
	/// 应付期限
	/// </summary>
	[ Column(Name = "pay_due_date")]
	public DateTime? PayDueDate { get; set; }

	[ Column(Name = "pay_lock_del")]
	public bool PayLockDel { get; set; } = false;

	/// <summary>
	/// 缴费期限
	/// </summary>
	[ Column(Name = "pay_office", StringLength = 50)]
	public string PayOffice { get; set; }

	/// <summary>
	/// 缴费公司 未使用
	/// </summary>
	[ Column(Name = "pay_office_company", StringLength = 50)]
	public string PayOfficeCompany { get; set; }

	/// <summary>
	/// 缴费编号
	/// </summary>
	[ Column(Name = "pay_office_serial", StringLength = 50)]
	public string PayOfficeSerial { get; set; }

	/// <summary>
	/// 缴费日期
	/// </summary>
	[ Column(Name = "pay_officer_date")]
	public DateTime? PayOfficerDate { get; set; }

	/// <summary>
	/// 缴费日期
	/// </summary>
	[ Column(Name = "pay_officer_due_date")]
	public DateTime? PayOfficerDueDate { get; set; }

	/// <summary>
	/// 缴费期限
	/// </summary>
	[ Column(Name = "pay_officer_legal_date")]
	public DateTime? PayOfficerLegalDate { get; set; }

	/// <summary>
	/// 到款状态 d
	/// </summary>
	[ Column(Name = "pay_status", StringLength = 50, IsNullable = false)]
	public string PayStatus { get; set; } = "0";

	/// <summary>
	/// 支付状态
	/// </summary>
	[ Column(Name = "pay_status_id", StringLength = 50)]
	public string PayStatusId { get; set; }

	/// <summary>
	/// 应付第三方日期
	/// </summary>
	[ Column(Name = "pay_third_due_date")]
	public DateTime? PayThirdDueDate { get; set; }

	/// <summary>
	/// 支付类型
	/// </summary>
	[ Column(Name = "pay_type")]
	public bool? PayType { get; set; }

	/// <summary>
	/// 支付方式
	/// </summary>
	[ Column(Name = "pay_way", StringLength = 50)]
	public string? PayWay { get; set; }

	/// <summary>
	/// 缴费人名称
	/// </summary>
	[Column(Name = "payment_agency", StringLength = 500)]
	public string? PaymentAgency { get; set; }

	/// <summary>
	/// 缴费名称 d
	/// </summary>
	[ Column(Name = "payment_name", StringLength = 50)]
	public string? PaymentName { get; set; }

	/// <summary>
	/// 预请款日期
	/// </summary>
	[ Column(Name = "pre_request_date")]
	public DateTime? PreRequestDate { get; set; }

	/// <summary>
	/// 任务ID
	/// </summary>
	[ Column(Name = "proc_id", StringLength = 50)]
	public string ProcId { get; set; }

	/// <summary>
	/// 任务导航属性
	/// </summary>
	[Navigate(nameof(ProcId))]
	public virtual CaseProcInfo CaseProcInfo { get; set; } = default!;

	[ Column(Name = "proc_property", StringLength = 100)]
	public string ProcProperty { get; set; }

	/// <summary>
	/// 到款日期
	/// </summary>
	[ Column(Name = "receive_date")]
	public DateTime? ReceiveDate { get; set; }

	/// <summary>
	/// 应收日期
	/// </summary>
	[ Column(Name = "receive_due_date")]
	public DateTime? ReceiveDueDate { get; set; }

	/// <summary>
	/// 收费规则
	/// </summary>
	[ Column(Name = "receive_rule", StringLength = 50)]
	public string? ReceiveRule { get; set; }

	/// <summary>
	/// 到款状态 d
	/// </summary>
	[ Column(Name = "receive_status", StringLength = 50, IsNullable = false)]
	public string ReceiveStatus { get; set; } = "0"; 

	/// <summary>
	/// 应收金额
	/// </summary>
	[ Column(Name = "recieve_amount", DbType = "money")]
	public decimal? ReceiveAmount { get; set; }

	/// <summary>
	/// 备注
	/// </summary>
	[ Column(Name = "remark", StringLength = 4000)]
	public string? Remark { get; set; }

	/// <summary>
	/// 请款日期(未使用)
	/// </summary>
	[ Column(Name = "request_date")]
	public DateTime? RequestDate { get; set; }

	/// <summary>
	/// 请款ID（未使用）
	/// </summary>
	[ Column(Name = "request_id", StringLength = 50)]
	public string RequestId { get; set; }

	[ Column(Name = "request_lock_del")]
	public bool RequestLockDel { get; set; } = false;

	/// <summary>
	/// 请款编号（未使用）
	/// </summary>
	[ Column(Name = "request_no", StringLength = 50)]
	public string RequestNo { get; set; }

	/// <summary>
	/// sales_user_id
	/// </summary>
	[ Column(Name = "sales_user_id", StringLength = 50)]
	public string? SalesUserId { get; set; }

	/// <summary>
	/// 排序
	/// </summary>
	[ Column(Name = "seq")]
	public int Seq { get; set; } = 1;

	/// <summary>
	/// 标准金额
	/// </summary>
	[ Column(Name = "standard_amount", DbType = "money")]
	public decimal? StandardAmount { get; set; }

	/// <summary>
	/// 税率
	/// </summary>
	[ Column(Name = "tax_rate", StringLength = 50)]
	public string TaxRate { get; set; }

	/// <summary>
	/// 跟案人
	/// </summary>
	[ Column(Name = "track_user_id", StringLength = 50)]
	public string? TrackUserId { get; set; }

	/// <summary>
	/// 更新时间
	/// </summary>
	[ Column(Name = "update_time")]
	public DateTime? UpdateTime { get; set; }

	/// <summary>
	/// 更新用户
	/// </summary>
	[ Column(Name = "update_user_id", StringLength = 50)]
	public string? UpdateUserId { get; set; }

	/// <summary>
	/// 用于排序的行号
	/// </summary>
	[Column(IsIgnore = true)]
	public long RowNumber { get; set; }

	/// <summary>
	/// 锁定费项到请款单中
	/// </summary>
	/// <param name="billNo">请款单号</param>
	/// <param name="operator">操作人员id</param>
	/// <returns>费项更新结果</returns>
	public FeesUpdateResult LockFeeToBill(string? billNo, string @operator)
	{
		ArgumentNullException.ThrowIfNull(billNo);
		var updateResult = new FeesUpdateResult(FeeId);

		BillNo = billNo;
		SetReceiveStatus("1000", updateResult);

		if (updateResult.Success)
		{
			UpdateFeesOperationRecords(@operator);
		}
			
		return updateResult;
	}

	/// <summary>
	/// 请款操作更新费项信息
	/// </summary>
	/// <param name="requestDate">请款日期</param>
	/// <param name="receiveDueDate">应收款日期</param>
	/// <param name="operator">操作人</param>
	/// <returns>费项更新结果</returns>
	public FeesUpdateResult Request(DateTime? requestDate, DateTime? receiveDueDate, string @operator)
	{
		ArgumentNullException.ThrowIfNull(requestDate);
		ArgumentNullException.ThrowIfNull(receiveDueDate);
		var updateResult = new FeesUpdateResult(FeeId);

		RequestDate = requestDate.Value.Date;
		ReceiveDueDate = receiveDueDate.Value.Date;
		updateResult.Success = true;
		if (updateResult.Success)
		{
			UpdateFeesOperationRecords(@operator);
		}

		return updateResult;
	}

	/// <summary>
	/// 解除费项和请款单的关联
	/// </summary>
	/// <param name="operator">操作人</param>
	/// <returns>费项更新结果</returns>
	public FeesUpdateResult UnlockFee(string @operator)
	{
		var updateResult = new FeesUpdateResult(FeeId);

		RequestDate = null;
		BillNo = string.Empty;
		SetReceiveStatus("0", updateResult);
			
		if (updateResult.Success)
		{
			UpdateFeesOperationRecords(@operator);
		}

		return updateResult;
	}

	/// <summary>
	/// 费项划拨操作
	/// </summary>
	/// <param name="receiveDate">到款日期</param>
	/// <param name="customerId">客户id</param>
	/// <param name="operator">操作人id</param>
	/// <returns>费项更新结果</returns>
	public FeesUpdateResult Allocate(DateTime? receiveDate, string? customerId, string @operator)
	{
		ArgumentNullException.ThrowIfNull(receiveDate);
		ArgumentNullException.ThrowIfNull(customerId);
		var updateResult = new FeesUpdateResult(FeeId);
			
		if (ReceiveStatus != "1000")
		{
			updateResult.Message = InvalidOperationMessage(FinanceOperation.Allocate);
			updateResult.Success = false;
			return updateResult;
		}
			
		var customerFollows = CaseProcInfo.CaseInfo.Customer.CustomerFollows;
		var caseInfo = CaseProcInfo.CaseInfo;

		//  项目商标案件特殊处理
		var caseType = caseInfo.CaseTypeId;
		switch (caseInfo.CaseTypeId)
		{
			case CaseType.Project:
			{
				var businessType = caseInfo.BusinessType;
				if (businessType is not null && lawCodes.Any(code => code.Equals(businessType.BusinessTypeCode, StringComparison.OrdinalIgnoreCase)))
				{
					caseType = CaseType.Legal;
				}

				break;
			}
			case CaseType.Trade:
			{
				var businessType = CaseProcInfo.BusinessType;
				if (businessType is not null && TrademarkCode.Equals(businessType.BusinessTypeCode, StringComparison.OrdinalIgnoreCase))
				{
					caseType = CaseType.Copy;
				}

				break;
			}
		}
			
		var followers = customerFollows.Where(follow =>
			follow.IsEnabled && follow.CaseDirection == caseInfo.CaseDirection &&
			follow.CaseType == caseType);

		ReceiveDate = receiveDate.Value.Date;
		var salesInfo = followers.FirstOrDefault(follow =>
			follow.CustomerUserType.Equals("ay", StringComparison.OrdinalIgnoreCase));
		SalesUserId = salesInfo?.TrackUser;
		updateResult.Sales = SalesUserId is null ? null : new UserBaseInfo(SalesUserId);

		TrackUserId = followers
			.FirstOrDefault(follow => follow.CustomerUserType.Equals("ga", StringComparison.OrdinalIgnoreCase))
			?.TrackUser;
		updateResult.Tracker = TrackUserId is not null ? new UserBaseInfo(TrackUserId) : null;
		HeadUserType = salesInfo?.HeadUserType;
		updateResult.CaseSourceType = HeadUserType;

		var customer = caseInfo.Customer;
		updateResult.BusinessUser = customer.BusiUserId is not null ? new UserBaseInfo(customer.BusiUserId) : null;
		updateResult.ClueUser = customer.ClueUserId is not  null ? new UserBaseInfo(customer.ClueUserId) : null;
						
		SetReceiveStatus("5000", updateResult);
			
		if (updateResult.Success)
		{
			UpdateFeesOperationRecords(@operator);
		}

		return updateResult;
	}

	/// <summary>
	/// 撤销划拨
	/// </summary>
	/// <param name="operator">操作人id</param>
	/// <returns>更新结果</returns>
	public FeesUpdateResult CancelAllocation(string @operator)
	{
		var updateResult = new FeesUpdateResult(FeeId);
			
		if (ReceiveStatus != "5000")
		{
			updateResult.Message = InvalidOperationMessage(FinanceOperation.CancelAllocation);
			updateResult.Success = false;
			return updateResult;
		}
			
		ReceiveDate = null;
		SalesUserId = null;
		SetReceiveStatus("1000", updateResult);
			
		if (updateResult.Success)
		{
			UpdateFeesOperationRecords(@operator);
		}

		return updateResult;
	}

	/// <summary>
	/// 记录发票信息
	/// </summary>
	/// <param name="invoiceNo">发票号</param>
	/// <param name="operator">操作人</param>
	/// <returns>更新结果</returns>
	public FeesUpdateResult RecordInvoiceInfo(string? invoiceNo, string @operator)
	{
		ArgumentNullException.ThrowIfNull(invoiceNo);
		var updateResult = new FeesUpdateResult(FeeId);

		InvoiceNo = invoiceNo;
		updateResult.Success = true;
		if (updateResult.Success)
		{
			UpdateFeesOperationRecords(@operator);
		}

		return updateResult;
	}

	/// <summary>
	/// 清除发票信息
	/// </summary>
	/// <param name="operator">操作人id</param>
	/// <returns>费项更新结果</returns>
	public FeesUpdateResult ClearInvoiceInfo(string @operator)
	{
		var updateResult = new FeesUpdateResult(FeeId);
		InvoiceNo = string.Empty;
		updateResult.Success = true;
		UpdateFeesOperationRecords(@operator);

		return updateResult;
	}

	/// <summary>
	/// 标记为坏账
	/// </summary>
	/// <param name="operator">操作人id</param>
	/// <returns>费项更新结果</returns>
	public FeesUpdateResult MarkBadDept(string @operator)
	{
		var updateResult = new FeesUpdateResult(FeeId);
			
		if (ReceiveStatus != "8500")
		{
			updateResult.Message = InvalidOperationMessage(FinanceOperation.BadDept);
			updateResult.Success = false;
			return updateResult;
		}

		SetReceiveStatus("4000", updateResult);
		if (updateResult.Success)
		{
			UpdateFeesOperationRecords(@operator);
		}

		return updateResult;
	}
		
		
	/// <summary>
	/// 标记为坏账处理中
	/// </summary>
	/// <param name="operator">操作人id</param>
	/// <returns>费项更新结果</returns>
	public FeesUpdateResult LockToBadDept(string @operator)
	{
		var updateResult = new FeesUpdateResult(FeeId);
			
		if (ReceiveStatus != "1000")
		{
			updateResult.Message = InvalidOperationMessage(FinanceOperation.LockBadDept);
			updateResult.Success = false;
			return updateResult;
		}

		SetReceiveStatus("8500", updateResult);
		if (updateResult.Success)
		{
			UpdateFeesOperationRecords(@operator);
		}

		return updateResult;
	}
		
	/// <summary>
	/// 撤回坏账处理中
	/// </summary>
	/// <param name="operator">操作人id</param>
	/// <returns>费项更新结果</returns>
	public FeesUpdateResult CancelBadDept(string @operator)
	{
		var updateResult = new FeesUpdateResult(FeeId);
			
		if (ReceiveStatus != "8500")
		{
			updateResult.Message = InvalidOperationMessage(FinanceOperation.CancelBadDept);
			updateResult.Success = false;
			return updateResult;
		}

		SetReceiveStatus("1000", updateResult);
		if (updateResult.Success)
		{
			UpdateFeesOperationRecords(@operator);
		}

		return updateResult;
	}

	/// <summary>
	/// 标记为不到款处理中
	/// </summary>
	/// <param name="operator">操作人id</param>
	/// <returns>费项更新结果</returns>
	public FeesUpdateResult LockToNotPaid(string @operator)
	{
		var updateResult = new FeesUpdateResult(FeeId);
		if (ReceiveStatus != "0")
		{
			updateResult.Message = InvalidOperationMessage(FinanceOperation.LockToNotPaid);
			updateResult.Success = false;
			return updateResult;
		}

		SetReceiveStatus("7000", updateResult);
		if (updateResult.Success)
		{
			UpdateFeesOperationRecords(@operator);
		}

		return updateResult;
	}

	/// <summary>
	/// 标记为不到款
	/// </summary>
	/// <param name="operator">操作人id</param>
	/// <returns>费项更新结果</returns>
	public FeesUpdateResult MarkNotPaid(string @operator)
	{
		var updateResult = new FeesUpdateResult(FeeId);
		// if (ReceiveStatus != "7000")
		// {
		// 	updateResult.Message = InvalidOperationMessage(FinanceOperation.NotPaid);
		// 	updateResult.Success = false;
		// 	return updateResult;
		// }

		SetReceiveStatus("3000", updateResult);
		if (updateResult.Success)
		{
			UpdateFeesOperationRecords(@operator);
		}

		return updateResult;
	}
		
		

	/// <summary>
	/// 撤销不到款
	/// </summary>
	/// <param name="operator">操作人员</param>
	/// <returns>费项更新结果</returns>
	public FeesUpdateResult CancelNotPaid(string @operator)
	{
		var updateResult = new FeesUpdateResult(FeeId);
		// if (ReceiveStatus != "7000")
		// {
		// 	updateResult.Message = InvalidOperationMessage(FinanceOperation.CancelNotPaid);
		// 	updateResult.Success = false;
		// 	return updateResult;
		// }

		SetReceiveStatus("0", updateResult);
		if (updateResult.Success)
		{
			UpdateFeesOperationRecords(@operator);
		}

		return updateResult;
	}

	/// <summary>
	/// 更新到款状态
	/// </summary>
	/// <param name="receiveStatus">到款状态</param>
	/// <param name="updateResult">更新结果</param>
	/// <returns>更新结果</returns>
	private void SetReceiveStatus(string receiveStatus, FeesUpdateResult updateResult)
	{
		if (receiveStatus == ReceiveStatus)
		{
			updateResult.Message = $"到款状态为{ReceiveStatus}, 不能做重复操作";
			updateResult.Success = false;
		}
		else
		{
			ReceiveStatus = receiveStatus;
			updateResult.Success = true;
		}
			
		updateResult.ReceiveStatus = ReceiveStatus;
	}

	private void UpdateFeesOperationRecords(string @operator)
	{
		UpdateTime = DateTime.Now;
		UpdateUserId = @operator;
	}
		
	private string InvalidOperationMessage(FinanceOperation operation)
		=> $"当前费项状态为[{ReceiveStatus}], 不能做[{operation}]操作。";

	
	/// <summary>
	/// 缴官费状态核查
	/// </summary>
	public void CheckOfficialPaymentStatus()
	{
		if (OfficialNotificationChecked && PaymentListChecked && OfficerStatus == "0")
		{
			OfficerStatus = "2000";
		}
	}

	/// <inheritdoc />
	[JsonIgnore]
	[Column(IsIgnore = true)]
	public Expression<Func<CaseFeeList, string>> BaseKey => feeList => feeList.CaseProcInfo.CaseId;
}