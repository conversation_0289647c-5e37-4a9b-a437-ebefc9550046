using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_meetrend_Area", DisableSyncStructure = true)]
	public partial class VwMeetrendArea {

		
		public bool? FENABLE { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string FID { get; set; }

		[ Column(StringLength = 50)]
		public string FNAME { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string FNUMBER { get; set; }

	}

}
