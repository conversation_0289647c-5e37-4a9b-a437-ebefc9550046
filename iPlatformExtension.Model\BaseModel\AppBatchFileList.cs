using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_batch_file_list", DisableSyncStructure = true)]
	public partial class AppBatchFileList {

		[ Column(Name = "file_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FileId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "batch_id", StringLength = 50)]
		public string BatchId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "desc_id", StringLength = 50)]
		public string DescId { get; set; }

		[ Column(Name = "file_ex", StringLength = 50)]
		public string FileEx { get; set; }

		[ Column(Name = "file_name", StringLength = 500)]
		public string FileName { get; set; }

		[ Column(Name = "file_no", StringLength = 50)]
		public string FileNo { get; set; }

		[ Column(Name = "file_size")]
		public long? FileSize { get; set; }

		[ Column(Name = "is_extract")]
		public bool? IsExtract { get; set; } = false;

	}

}
