using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_regex_rule", DisableSyncStructure = true)]
	public partial class MailRegexRule {

		[ Column(Name = "rule_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string RuleId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "name", StringLength = 200)]
		public string Name { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "value", StringLength = 2000)]
		public string Value { get; set; }

		[ Column(Name = "value_type", StringLength = 50)]
		public string ValueType { get; set; }

	}

}
