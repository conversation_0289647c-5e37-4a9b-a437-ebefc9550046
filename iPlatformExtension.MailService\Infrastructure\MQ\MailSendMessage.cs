﻿using iPlatformExtension.Model.Enum;

namespace iPlatformExtension.MailService.Infrastructure.MQ
{
    public class MailSendMessage
    {/// <summary>
     /// 邮件ID
     /// </summary>
        public string MailId { get; set; }

        /// <summary>
        /// 收件编号
        /// </summary>
        public string MailNo { get; set; }
        /// <summary>
        /// 邮件主题
        /// </summary>
        public string MailTitle { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public string OperatorUser { get; set; }


        /// <summary>
        /// 时间
        /// </summary>
        public string DateTime { get; set; }

        /// <summary>
        /// 接收人
        /// </summary>
        public string RecipientBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
