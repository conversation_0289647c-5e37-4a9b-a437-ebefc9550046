﻿using System.Collections;
using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.Common.Validation.Attributes;

public class NoEmptyItemsAttribute : ValidationAttribute
{
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is IEnumerable enumerable)
        {
            foreach (var item in enumerable)
            {
                switch (item)
                {
                    case null:
                    case string str when string.IsNullOrWhiteSpace(str):
                        return new ValidationResult(ErrorMessage);
                }
            }
        }
        
        return ValidationResult.Success;
    }
}