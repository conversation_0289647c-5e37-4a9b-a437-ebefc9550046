﻿using System.Net.Security;
using System.Reflection;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Unicode;
using Grpc.Core;
using Hangfire;
using Hangfire.MemoryStorage;
using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Common.Authentication.Providers;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Clients.Holiday;
using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Db;
using iPlatformExtension.Common.Db.Log;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Filters;
using iPlatformExtension.Common.Formatters.Input;
using iPlatformExtension.Common.GrpcServices;
using iPlatformExtension.Common.Mediator.PipelineBehaviors;
using iPlatformExtension.Common.Schedule.Hangfire;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Messages;
using iPlatformExtension.Messages.Mails;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models;
using iPlatformExtension.Public.Hubs;
using iPlatformExtension.Public.Infrastructure.Jobs;
using iPlatformExtension.Public.Infrastructure.OpenApi.Document;
using iPlatformExtension.Public.MQ.FlowNotificationConsumer;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Nacos.AspNetCore.V2;
using NLog.Web;
using Scalar.AspNetCore;
using SkyApm.AspNetCore.Diagnostics;

[assembly: ApplicationPart("iPlatformExtension.Common")]

var builder = WebApplication.CreateBuilder(args);

var configuration = builder.Configuration;
var environment = builder.Environment;
var logging = builder.Logging;
var webHost = builder.WebHost;
builder.Services.AddSignalR();
webHost.ConfigureKestrel(options =>
{
    options.Limits.MaxRequestBodySize = long.MaxValue;
    options.Limits.MaxRequestBufferSize = long.MaxValue;
});

logging.ClearProviders();
logging.AddSimpleConsole(options => options.IncludeScopes = true);
logging.AddNLogWeb(new NLogAspNetCoreOptions
{
    RemoveLoggerFactoryFilter = false,
    LoggingConfigurationSectionName = "NLog"
});

if (!environment.IsLocal())
{
    configuration.AddNacosV2Configuration(configuration.GetSection("nacos"));
}
else
{
    configuration.AddJsonFile($"clients.{environment.EnvironmentName}.json", true, true);
    configuration.AddJsonFile($"mqs.{environment.EnvironmentName}.json", true, true);
}

// Add services to the container.
if (!environment.IsLocal())
{
    builder.Services.AddNacosAspNet(configuration);
    builder.Services.AddNacosServiceDiscovery(options => options.AllowedSchemes = ["http", "https"]);
}
builder.Services.AddControllers(options =>
{
    options.Filters.Add<ActionExceptionFilter<ResultData>>();
    options.Filters.Add<ActionResultFilter<ResultData>>();
    options.InputFormatters.Insert(0, JsonPatchInputFormatterExtension.GetJsonPatchInputFormatter());
}).AddJsonOptions(options =>
{
    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
    options.JsonSerializerOptions.Converters.Add(new DateTimeJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new NullableDateTimeJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter<KeyType>());
    if (environment.IsLocal())
    {
        options.JsonSerializerOptions.WriteIndented = true;
    }
});
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();


if (environment.IsLocal())
{
    builder.Services.AddGrpcClient<Notification.NotificationClient>("iplatform-mail",
            options => options.Address = new Uri("https://localhost:7295"))
        .ConfigurePrimaryHttpMessageHandler(() =>
        {
            var handler = new SocketsHttpHandler()
            {
                SslOptions = new SslClientAuthenticationOptions()
                {
                    RemoteCertificateValidationCallback = delegate { return true; }
                }
            };

            handler.Properties["__GrpcLoadBalancingDisabled"] = true;

            return handler;
        });
}
else
{
    builder.Services.AddGrpcClientFromNacos<Notification.NotificationClient>("iplatform-mail");
}

builder.Services.AddAutoMapper(typeof(Program));
builder.Services.AddObjectPools();
builder.Services.AddPlatformFreeSql(configuration.GetConnectionString("Default") ?? string.Empty)
    .ConfigureGlobalCurdLogging("Logging:FreeSql:Curd");
    
    
if (environment.IsProduction())
{
    builder.Services.AddMongoDbContext<PlatformMongoDbContext>(configuration.GetConnectionString("Log")
                                                     ?? throw new ArgumentNullException("ConnectionStrings:Log", "缺少数据库连接字符串"));
    builder.Services.AddEntityChangeLogs();
}

builder.Services.AddHuaweiObsClient();
builder.Services.AddDataService();
builder.Services.AddCache().ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
{
    options.ConnectionString = configuration.GetConnectionString("Redis");
    options.Converters.Add(new ClaimsIdentityConverter());
});
builder.Services.AddValidation();
builder.Services.AddMediatRServices().Add(serviceConfiguration =>
{
    serviceConfiguration.AddOpenBehavior(typeof(DefaultFreeSqlUnitOfWorkPipelineBehavior<,>));
    serviceConfiguration.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
}).Build();
builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = PlatformAuthOptions.SchemeName;
    options.DefaultChallengeScheme = BladeAuthOptions.SchemeName;
    options.DefaultForbidScheme = BladeAuthOptions.SchemeName;
}).AddJwtBearer(PlatformAuthOptions.SchemeName,
    options =>
    {
        var platformAuthenticationOptions =
            configuration.Get<PlatformAuthOptions>(sectionKey: "IPlatformAuth");
        options.TokenValidationParameters = new TokenValidationParameters
        {
            IssuerSigningKey = new SymmetricSecurityKey(
                platformAuthenticationOptions?.SecurityKey.GetBytes(Encoding.UTF8) ??
                throw new ArgumentNullException(nameof(configuration))),
            ValidateIssuerSigningKey = true,
            ValidAudiences = platformAuthenticationOptions.Audiences,
            ValidIssuers = platformAuthenticationOptions.Issuers
        };
        options.Events = new JwtBearerEvents
        {
            OnTokenValidated = AuthenticationExtension.ValidateUserAsync,
            OnMessageReceived = AuthenticationExtension.GetTokenAsync,
            OnAuthenticationFailed = AuthenticationExtension.AuthenticateFailedAsync
        };
        options.SaveToken = true;
        options.ForwardChallenge = BladeAuthOptions.SchemeName;
        options.ForwardForbid = BladeAuthOptions.SchemeName;
        options.ForwardDefaultSelector = context =>
            context.Request.Headers.Authorization.Count > 0 ? PlatformAuthOptions.SchemeName : null;
    }).AddScheme<BladeAuthOptions, BladeAuthenticationHandler>(BladeAuthOptions.SchemeName, "bladeX token验证", options =>
    {
        options.Events.OnTokenValidated = AuthenticationExtension.ValidateUserAsync;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            ValidateIssuer = false,
            ValidateAudience = false,
            IssuerSigningKey = new SymmetricSecurityKey(
                configuration["BladeAuth:SecurityKey"]?.GetBytes(Encoding.UTF8) ??
                throw new ArgumentNullException("BladeAuth:SecurityKey", "缺少blade-auth秘钥"))
        };
        options.ForwardDefaultSelector = context =>
            context.Request.Headers.ContainsKey(options.TokenHeaderName) ? BladeAuthOptions.SchemeName : null;
    }).ReplaceAuthenticationSchemeProvider<ForwardAuthenticationSchemeProvider>();

builder.Services.AddHttpLogging(options =>
{
    options.LoggingFields |= HttpLoggingFields.RequestHeaders;
    options.LoggingFields |= HttpLoggingFields.RequestPath;
    options.LoggingFields |= HttpLoggingFields.RequestQuery;
    options.LoggingFields |= HttpLoggingFields.RequestBody;
    options.LoggingFields |= HttpLoggingFields.ResponsePropertiesAndHeaders;
    options.LoggingFields |= HttpLoggingFields.ResponseBody;
    options.RequestBodyLogLimit = 327680;
});

builder.Services.AddHttpContextAccessor();

builder.Services.AddResponseCaching();

builder.Services.AddCors(options =>
{
    options.AddPolicy("signalR", policy =>
    {
        // policy.AllowAnyOrigin();
        policy.WithOrigins(configuration.GetValue<string[]>("SignalRWithOrigins") ?? []);
        policy.AllowCredentials();
        policy.AllowAnyMethod();
        policy.AllowAnyHeader();
    });
});

if (!environment.IsProduction())
{
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = builder.Environment.ApplicationName,
            Version = "2.8.2.RELEASE-v2.7.11",
            Description = "公共中心接口文档"
        });
        var documentPath = Path.Combine(AppContext.BaseDirectory, $"{builder.Environment.ApplicationName}.xml");
        c.IncludeXmlComments(documentPath, true);

        var modelPath = Path.Combine(AppContext.BaseDirectory, "iPlatformExtension.Model.xml");
        c.IncludeXmlComments(modelPath, true);
        c.OrderActionsBy(o => o.RelativePath);
    });
    builder.Services.AddSingleton<EntityTypeInfoProvider>();
    builder.Services.AddOpenApi("crm", options =>
    {
        options.AddDocumentTransformer(new CrmDocumentTransformer());
        options.ShouldInclude = description =>
            description.ActionDescriptor.EndpointMetadata.Any(data => data is EndpointNameAttribute);
    });
}

if (!environment.IsLocal())
{
    builder.Services.AddHostedService<FlowNotificationHostedService>();
}

builder.Services.AddHangfire(hangfireConfiguration => hangfireConfiguration.SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
    .UseMemoryStorage());
builder.Services.AddHangfireServer(serverOptions =>
{
    serverOptions.Queues = ["law-provision", "nice"];
});
builder.Services.AddWxConfiguration(configuration);
builder.Services.AddPhoenixClient();

builder.Services.AddBladeCommonClient();
builder.Services.AddHttpClient<HolidayClient>();

if (!environment.IsLocal())
{
    builder.Services.AddSkyAPM(extensions => extensions.AddAspNetCoreHosting());
}

var app = builder.Build();

app.UseExceptionHandler("/Error");

app.UseW3CTraceResponse();

// Configure the HTTP request pipeline.
if (!app.Environment.IsProduction())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.MapOpenApi();
    app.MapScalarApiReference(options => options.AddDocument("crm"));
}

app.HandleUnsuccessfulResponse();

app.UseRequestEnableBuffering();

app.UseHttpLogging();

app.UseRouting();

app.UseCors();

app.UseResponseCaching();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
//app.MapHub<ChatHub>("/chatHub");
//app.UseCors(MyAllowSpecificOrigins);

app.MapGet("/mail-test/{template}", async (BodyTemplate template, Notification.NotificationClient client, ILoggerFactory loggerFactory) =>
{
    var logger = loggerFactory.CreateLogger("mail-test");

    var notificationMail = new NotificationMail()
    {
        Subject = string.Empty,
        Sender = "【正式版本】系统邮箱",
        BodyText = string.Empty,
        BodyTemplate = template,
        MessageId = "ef0409bb-5baf-4ed0-984a-1b4c2da87666",
        IsHtmlBody = false
    };

    notificationMail.Receivers.Add("75166ce3-3059-468c-90fa-b21963c9bb41");

    var context = client.NotifyAsync();
    var request = context.RequestStream;
    var response = context.ResponseStream;

    var responseTask = Task.Run(HandleResultAsync);

    await request.WriteAsync(notificationMail);
    await request.CompleteAsync();
    return await responseTask;

    async IAsyncEnumerable<MessageResult> HandleResultAsync()
    {
        await foreach (var result in response.ReadAllAsync())
        {
            var mailId = result.Data.UnpackToString();
            if (result.Success)
            {
                logger.LogInformation("发送邮件：【{MailId}】成功", mailId);
            }
            else
            {
                logger.LogError("发送邮件：【{MailId}】失败！原因：{Message}", mailId, result.Message);
            }

            yield return result;
        }
    }
}).AllowAnonymous();

app.MapHub<NotificationHub>("/chatHub").RequireCors("signalR");
app.MapHub<FlowHub>("/flowHub");

app.AddRecurringJob<LawProvisionSynchronizationJob>(
    "法律条款同步",
    job => job.UpdateLawProvisionAsync(),
    Cron.Monthly(1, 2),
    new RecurringJobOptions()
    {
        TimeZone = TimeZoneInfo.Local
    });

app.AddRecurringJob<NiceCategoriesSynchronizationJob>("尼斯分类定时同步",
    handler => handler.UpdateNiceCategoriesAsync(),
    Cron.Daily(2),
    new RecurringJobOptions()
    {
        TimeZone = TimeZoneInfo.Local,
    });

app.Run();

/// <summary>
/// Public程序启动入口
/// </summary>
public partial class Program;