﻿using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery.TrademarkRenewal;
using MediatR;

namespace iPlatformExtension.TrademarkDelivery.Consumers;

[Consumer("TrademarkRenewal")]
public sealed class TrademarkRenewalDeliveryConsumer(IMediator mediator)
{
    [Operation("order")]
    public Task CreateOrderAsync(DeliveryMessage message)
    {
        var (procId, version) = message;
        return mediator.Send(new CreateOrderCommand<TrademarkRenewalOrderCommand>(procId, version));
    }

    /// <summary>
    /// 中止递交
    /// </summary>
    /// <param name="message">递交消息</param>
    /// <returns></returns>
    [Operation("stop")]
    public Task StopOrderAsync(DeliveryMessage message)
    {
        var (procId, version) = message;
        return mediator.Send(new StopOrderCommand(procId, version));
    }

    /// <summary>
    /// 从官网撤回递交
    /// </summary>
    /// <param name="message">递交消息</param>
    /// <returns></returns>
    [Operation("withdraw")]
    public Task WithdrawOrderAsync(DeliveryMessage message)
    {
        var (procId, version) = message;
        return mediator.Send(new WithdrawOrderCommand(procId, version));
    }
    
    /// <summary>
    /// 从权大师取消订单
    /// </summary>
    /// <param name="message">递交消息</param>
    /// <returns></returns>
    [Operation("cancel")]
    public Task CancelOrderAsync(DeliveryMessage message)
    {
        var (procId, version, orderNo, deliveryKey) = message;
        return mediator.Send(new CancelOrderCommand(procId, version, orderNo, deliveryKey));
    }
}