﻿<#@ template debug="false" hostspecific="True" language="C#" #>
<#@ assembly name="System.Core" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Text" #>
<#@ import namespace="System.IO" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ output extension=".cs" #>
<#@ include file="ModelAuto.ttinclude"#>


<# var manager = new Manager(Host, GenerationEnvironment, true); #>
<#
	//生成Query(inputModel) QueryDto(outputModel) Handle(处理函数)
	var FileName = "KafkaMailReceiveCount"; //文件名字
	var FileFolderName = "Count"; //文件夹名字
	string templateDirectory = Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..");
	string outputFilePath = Path.Combine(templateDirectory, "Queries",FileFolderName);
	if (!Directory.Exists(outputFilePath))
	{
	    Directory.CreateDirectory(outputFilePath);
	}
	manager.StartBlock(FileName+"Query.cs",outputFilePath);
#>
using iPlatformExtension.MailCenter.Applications.Models.<#=FileFolderName#>;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.<#=FileFolderName#>;

public record <#=FileName#>Query() : IRequest<IEnumerable<<#=FileName#>Dto>>;

<# 
	manager.EndBlock(); 
	string DtoPath = Path.Combine(templateDirectory, "Models",FileFolderName);
	if (!Directory.Exists(DtoPath))
	{
	    Directory.CreateDirectory(DtoPath);
	}
	manager.StartBlock(FileName+"Dto.cs",DtoPath);
#>
namespace iPlatformExtension.MailCenter.Applications.Models.<#=FileFolderName#>;

public record <#=FileName#>Dto();

<#  manager.EndBlock();
string HandlerPath = Path.Combine(templateDirectory, "Handlers",FileFolderName);
if (!Directory.Exists(HandlerPath))
{
	Directory.CreateDirectory(HandlerPath);
}
manager.StartBlock(FileName+"QueryHandler.cs",HandlerPath);
#>
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.MailCenter.Applications.Models.<#=FileFolderName#>;
using iPlatformExtension.MailCenter.Applications.Queries.<#=FileFolderName#>;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.<#=FileFolderName#>;

/// <summary>
/// 
/// </summary>
internal sealed class <#=FileName#>QueryHandler(IFreeSql freeSql) : IRequestHandler<<#=FileName#>Query, IEnumerable<<#=FileName#>Dto>>
{
    public async Task<IEnumerable<<#=FileName#>Dto>> Handle(<#=FileName#>Query request, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }
}


<# manager.EndBlock();#>
<# manager.Process(true); //输出文件   #>