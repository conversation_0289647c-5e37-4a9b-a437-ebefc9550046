﻿using AutoMapper;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkObjections;

internal sealed class TrademarkObjectionsOtherInfoResultQueryHandler(
    ISystemDictionaryRepository dictionaryRepository, IDeliveryLawBasisRepository lawBasisRepository, IMapper mapper) 
    : OtherInfoResultNotificationHandlerBase(dictionaryRepository)
{
    protected override ValueTask<bool> MatchAsync(OtherInfoResultQuery query, CancellationToken cancellationToken)
    {
        return new ValueTask<bool>(StringComparer.OrdinalIgnoreCase.Equals(query.Ctrl<PERSON>rocId, CtrlProcIds.TrademarkObjections));
    }

    protected override async Task HandleAsync(OtherInfoResultQuery query, CancellationToken cancellationToken)
    {
        var dto = query.TrademarkDeliveryDto;
        var otherInfoSnapshot = dto.OtherInfoSnapshot;

        var lawBasis = await lawBasisRepository.Where(basis => basis.ProcId == dto.ProcId).WithLock()
            .ToListAsync(cancellationToken);
        otherInfoSnapshot.FactualBasis = mapper.Map<List<LegalFactualBasis>>(lawBasis);
        foreach (var legalFactualBasis in otherInfoSnapshot.FactualBasis)
        {
            legalFactualBasis.LegalBasis =
                (await _dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.ObjectionsLawProvision,
                    legalFactualBasis.LegalBasis)).Value;
        }
        
        await base.HandleAsync(query, cancellationToken);
    }
}