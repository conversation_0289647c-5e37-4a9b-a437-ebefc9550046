﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using MediatR;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using iPlatformExtension.Repository.Interface;
using Microsoft.AspNetCore.Server.HttpSys;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public class SubmitFlowProcessHandler : IRequestHandler<SubmitFlowProcessCommand>
    {
        private readonly IFreeSql _freeSql;
        private readonly IMediator _mediator;
        private readonly ILogger _logger;
        private readonly IFlowActivityRepository _sysFlowActivityRepository;
        private readonly IFlowHistoryRepository _flowHistoryRepository;
        private readonly IDeliveryInfoRepository _deliveryInfoRepository;
        private readonly ICaseProcInfoRepository _caseProcInfoRepository;
        private readonly ISysFlowNodeRepository _sysFlowNodeRepository;
        private readonly ICaseExtendInfoRepository _caseExtendInfoRepository;
        private readonly IDeliveryFilesRepository _deliFilesRepository;
        private readonly ICaseFileRepository _caseFileRepository;
        private readonly ICaseAllotRepository _caseAllotRepository;
        private readonly IUserInfoRepository _userInfoRepository;




        public SubmitFlowProcessHandler(IMediator mediator,
            ILoggerFactory loggerFactory, IFlowActivityRepository sysFlowActivityRepository,
            IDeliveryInfoRepository deliveryInfoRepository, ICaseProcInfoRepository caseProcInfoRepository,
            ISysFlowNodeRepository sysFlowNodeRepository, ICaseExtendInfoRepository caseExtendInfoRepository,
            IDeliveryFilesRepository deliFilesRepository, ICaseFileRepository caseFileRepository, IFlowHistoryRepository flowHistoryRepository,
             ICaseAllotRepository caseAllotRepository, IFreeSql freeSql, IUserInfoRepository userInfoRepository)
        {
            _logger = loggerFactory.CreateLogger(GetType());
            _freeSql = freeSql;
            _mediator = mediator;
            _sysFlowActivityRepository = sysFlowActivityRepository;
            _deliveryInfoRepository = deliveryInfoRepository;
            _caseProcInfoRepository = caseProcInfoRepository;
            _sysFlowNodeRepository = sysFlowNodeRepository;
            _deliFilesRepository = deliFilesRepository;
            _caseExtendInfoRepository = caseExtendInfoRepository;
            _caseFileRepository = caseFileRepository;
            _flowHistoryRepository = flowHistoryRepository;
            _caseAllotRepository = caseAllotRepository;
            _userInfoRepository = userInfoRepository;
        }

        public async Task Handle(SubmitFlowProcessCommand request, CancellationToken cancellationToken)
        {
            var userInfo = request.CurrentUser;
            var userid = userInfo.UserId;
            var ProcStatus = "TCLZ";
            var SubProcStatus = "t_in_process";
            SysFlowActivity fa;
            var cnName = userInfo.CnName;
            var curUserID = "";
            //if (!string.IsNullOrEmpty(request.FlowInfo.ProcID))
            //{
            //    var procInfo = await _caseProcInfoRepository.GetAsync(request.FlowInfo.ProcID);

            //}

            if (request.FlowInfo.FFlowType == "EX")
            {
                fa = await _mediator.Send(new ExFlowCommand(request.FlowInfo, request.CurrentUser));
                return;
            }
            else
            {
                //新旧流程兼容
                request.FlowInfo.ObjId = request.FlowInfo.ProcID;
                fa = await _mediator.Send(new FlowActivityCommand(request.FlowInfo, request.CurrentUser));
                var deliInfo = await _deliveryInfoRepository.Where(o => o.ProcId == request.FlowInfo.ProcID).FirstAsync(cancellationToken);
                if (deliInfo != null)
                {
                    if (!string.IsNullOrWhiteSpace(request.FlowInfo.ProcNote))
                    {
                        deliInfo.ProcNote = request.FlowInfo.ProcNote;
                    }
                    deliInfo.FlowSubType = request.FlowInfo.FFlowSubType;
                    deliInfo.FlowUpdateTime = DateTime.Now;
                    deliInfo.FlowCurUserId = request.FlowInfo.FNextUserID;
                    await _deliveryInfoRepository.UpdateAsync(deliInfo);
                }

                if (request.FlowInfo.FFlowType == "AT")
                {
                    var nodeInfo = await _sysFlowNodeRepository.GetAsync(request.FlowInfo.FNextNodeID, cancellationToken);
                    int status = nodeInfo.NodeCode == "END" ? 5000 : 1000;
                    var allotInfo = await _caseAllotRepository.Where(o => o.ProcId == request.FlowInfo.ProcID).ToOneAsync();
                    if (allotInfo != null)
                    {
                        if (!string.IsNullOrEmpty(request.FlowInfo.UnderTakerUserId))
                        {
                            allotInfo.UndertakeUserId = request.FlowInfo.UnderTakerUserId;
                        }
                        allotInfo.Status = status;
                        allotInfo.FlowCurUserId = request.FlowInfo.FNextUserID;
                        allotInfo.FlowUpdateTime = DateTime.Now;
                        allotInfo.UpdateUserId = userid;
                        if (request.FlowInfo.FAuditTypeID == FlowAuditType.Reject) { allotInfo.UndertakeUserId = string.Empty; }
                        await _caseAllotRepository.UpdateAsync(allotInfo);
                    }
                }
                if (!string.IsNullOrEmpty(request.FlowInfo.UnderTakerUserId))
                {
                    var caseprocinfo = await _caseProcInfoRepository.GetAsync(request.FlowInfo.ProcID, cancellationToken);
                    if (caseprocinfo != null)
                    {
                        caseprocinfo.UndertakeUserId = request.FlowInfo.UnderTakerUserId;

                        var undertakeUserInfo = await _userInfoRepository.GetAsync(request.FlowInfo.UnderTakerUserId);
                        caseprocinfo.ProcDeptId = undertakeUserInfo?.DeptId;

                        if (caseprocinfo.BasCtrlProc.AttributeId == "tm_review")
                        {
                            var proc_no = caseprocinfo.ProcNo.Split('-');
                            caseprocinfo.ProcUndertakeMainUserId = request.FlowInfo.UnderTakerUserId;
                            if (proc_no[1] == "1")
                            {
                                var appInfo = await _freeSql.Select<AppCaseList>().Where(o => !string.IsNullOrEmpty(o.ProcUndertakeMainUserId) && o.ApplyId == caseprocinfo.CaseInfo.ApplyId).ToOneAsync(cancellationToken);
                                if (appInfo != null)
                                {
                                    caseprocinfo.ProcUndertakeMainUserId = appInfo.ProcUndertakeMainUserId;
                                }
                            }
                        }
                        await _caseProcInfoRepository.UpdateAsync(caseprocinfo);
                    }
                }
            }
        }

        [Obsolete]
        public async Task SetProcStatusAsync(Models.Flow.FlowInfo info, SysFlowActivity fa, string ProcStatus, string SubProcStatus, string curUserID, CancellationToken cancellationToken)
        {
            var logoNo = "";
            ProcStatus = "TCLZ";
            SubProcStatus = "t_in_process";
            var firstExamineUserId = "";
            fa.Status = FLOW_STATUS.S1000;
            DateTime? AllocateDate = null;

            var nodeInfo = await _sysFlowNodeRepository.GetAsync(fa.CurNodeId, cancellationToken);
            if (info.FAuditTypeID == "submit")
            {
                if (fa.FlowType == FlowTypeEnum.DE)
                {
                    ProcStatus = "TCLZ";
                    SubProcStatus = "t_submiting";
                }
                else if (fa.FlowType == FlowTypeEnum.EX)
                {
                    ProcStatus = "TCLZ";
                    SubProcStatus = "t_internal_audit";
                }
                else if (fa.FlowType == FlowTypeEnum.AT)
                {
                    ProcStatus = "TCLZ";
                    SubProcStatus = "t_allocating";
                    if (fa.PrevNodeId == "9C03ABC4-960C-41D2-A92C-52B39B1CA585")
                    {
                        firstExamineUserId = curUserID;
                    }
                }
                else
                {
                    ProcStatus = "TCLZ";
                    SubProcStatus = "t_in_process";
                }
            }
            else if (info.FAuditTypeID == "reject")
            {
                //回退保持原来状态
                ProcStatus = fa.CaseProcInfo.ProcStatusId;
                SubProcStatus = fa.CaseProcInfo.SubProcStatusId;
            }

            if (nodeInfo.NodeCode == "BEGIN")
            {
                // fa.Status = info.FAuditTypeID == "reject" ? FLOW_STATUS.S1000 : FLOW_STATUS.START;
                //退回保留原本案件状态和代理人状态
                ProcStatus = fa.CaseProcInfo.ProcStatusId;
                SubProcStatus = fa.CaseProcInfo.SubProcStatusId;
            }
            else if (nodeInfo.NodeCode == "END")
            {
                fa.Status = FLOW_STATUS.S5000;

                //注册申请任务获取商标图样
                if (fa.FlowType == FlowTypeEnum.DE)
                {
                    ProcStatus = "TYCL";
                    SubProcStatus = "t_finish";
                    fa.Status = FLOW_STATUS.S5000;
                    if (fa.CaseProcInfo.BasCtrlProc.CtrlProcCode == "TAP")
                    {
                        var file = await _deliFilesRepository.Where(o => o.ProcId == fa.ObjId && o.FileDesc == "商标图样").FirstAsync(cancellationToken);
                        if (file != null)
                        {
                            logoNo = file.Id.ToString();
                            var caseId = fa?.CaseProcInfo?.CaseId;
                            if (!string.IsNullOrEmpty(caseId))
                            {
                                var ext = await _caseExtendInfoRepository.Where(o => o.CaseId == caseId).FirstAsync(cancellationToken);
                                if (ext != null)
                                {
                                    ext.PicFileNo = $"a002{logoNo}";
                                    // await _freeSql.Update<CaseExtendInfo>().SetSource(ext).ExecuteUpdatedAsync(cancellationToken);
                                    await _caseExtendInfoRepository.UpdateAsync(ext, cancellationToken);
                                }
                            }
                        }
                    }

                    // try
                    // {
                    //     await _mediator.Send(new InsertProcCommand(fa.ObjId), cancellationToken);
                    //
                    //     var fileNo = await _mediator.Send(new PackageDeliveryFilesCommand(fa.ObjId), cancellationToken);
                    //     var caseFile = new CaseFile
                    //     {
                    //         CreateTime = DateTime.Now,
                    //         CreateUserId = UserIds.Administrator,
                    //         DescId = "",
                    //         FileNo = $"a002{fileNo}",
                    //         ObjId = fa.ObjId,
                    //         FileName = $"{fa.CaseProcInfo.ProcNo}_{fa.CaseProcInfo.BasCtrlProc.CtrlProcZhCn}_提交文件.zip",//[任务编号]_[任务名称]_提交文件
                    //         FileEx = ".zip",
                    //     };
                    //     //await _freeSql.Insert(caseFile).ExecuteAffrowsAsync(cancellationToken);
                    //     await _caseFileRepository.InsertAsync(caseFile, cancellationToken);
                    // }
                    // catch (Exception ex)
                    // {
                    //     _logger.LogError(ex, $"获取递交压缩文件失败");
                    // }

                }
                else if (fa.FlowType == FlowTypeEnum.EX)
                {
                    ProcStatus = "TCLZ";
                    SubProcStatus = "t_internal_review";
                }
                else if (fa.FlowType == FlowTypeEnum.AT)
                {
                    AllocateDate = DateTime.Now;
                    ProcStatus = "TCLZ";
                    SubProcStatus = "t_untreated";

                    var tmlist = new List<string> { "0", "3" };
                    if (tmlist.Contains(fa.CaseProcInfo.BasCtrlProc.TrademarkTab) && fa.CaseProcInfo.BasCtrlProc.CaseTypeId == "T" && fa.CaseProcInfo.CaseInfo.CaseDirection == "II")
                    {
                        SubProcStatus = "t_in_process";
                    }

                    else if (fa.CaseProcInfo.CtrlProcId == "DD886CBE-D8D4-4BE0-97F7-FD521674269A")
                    {
                        int? leftTime =
                        !fa.CaseProcInfo.RequirementSubmitDate.HasValue ? null : (fa.CaseProcInfo.RequirementSubmitDate.Value - DateTime.Now.Date).Days + 1;
                        if (leftTime.HasValue && leftTime.Value > 0)
                        {
                            SubProcStatus = "t_not_time";
                        }
                    }
                }
                else
                {
                    ProcStatus = "TYCL";
                    SubProcStatus = "t_finish";
                }
            }

            // await _mediator.Send(new ExFlowCommand(info, nodeInfo.NodeCode));
            await _mediator.Send(new UpdateCaseProcInfoCommand(new UpdateCaseProcInfoDto { ProcId = fa.ObjId, ProcStatus = ProcStatus, SubProcStatus = SubProcStatus, CaseId = fa?.CaseProcInfo?.CaseId, LogoFileID = logoNo, FirstExamineUserId = firstExamineUserId, AllocateDate = AllocateDate }), cancellationToken);
        }
    }
}
