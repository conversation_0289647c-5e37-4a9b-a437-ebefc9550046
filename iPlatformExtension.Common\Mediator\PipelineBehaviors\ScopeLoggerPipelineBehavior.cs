﻿using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.Mediator.PipelineBehaviors;

public class ScopeLoggerPipelineBehavior<TRequest, TResult>(ILogger<ScopeLoggerPipelineBehavior<TRequest, TResult>> logger) 
    : IPipelineBehavior<TRequest, TResult> where TRequest : IScopeLoggerCommand
{
    public Task<TResult> Handle(TRequest request, RequestHandlerDelegate<TResult> next, CancellationToken cancellationToken)
    {
        using var scope = logger.BeginScope(request);
        return next();
    }
}