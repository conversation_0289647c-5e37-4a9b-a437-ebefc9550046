﻿using System.Text.Json;
using Google.Protobuf;
using Google.Protobuf.WellKnownTypes;

namespace iPlatformExtension.Common.Extensions;

public static class ProtobufExtension
{
    public static Any Pack(this string str)
    {
        return new Any()
        {
            Value = ByteString.CopyFromUtf8(str)
        };
    }

    public static string UnpackToString(this Any any)
    {
        return any.Value.ToStringUtf8();
    }

    public static Any Pack<T>(this T value)
    {
        if (value is IMessage message)
        {
            return Any.Pack(message);
        }

        return Pack(value?.ToString() ?? JsonSerializer.Serialize(value));
    }

    public static int UnpackToInt32(this Any any)
    {
        return Convert.ToInt32(any.UnpackToString());
    }

    public static bool UnpackToBoolean(this Any any) => Convert.ToBoolean(any.UnpackToString());
}