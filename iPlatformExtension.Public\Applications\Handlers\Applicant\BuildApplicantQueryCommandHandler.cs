﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Commands.Applicant;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Applicant;

internal sealed class BuildApplicantQueryCommandHandler(IFreeSql freeSql) : 
    IRequestHandler<BuildApplicantQueryCommand, ISelect<CusApplicant>>
{
    
    public Task<ISelect<CusApplicant>> Handle(BuildApplicantQueryCommand request, CancellationToken cancellationToken)
    {
        var keyword = request.KeywordQuery.Keyword;

        var query = freeSql.Select<CusApplicant>().WithLock();
        
        query.WhereIf(!string.IsNullOrWhiteSpace(keyword), applicant => 
            applicant.ApplicantNameCn.Contains(keyword!) || applicant.ApplicantNameEn.Contains(keyword!));

        return Task.FromResult(query);
    }
}