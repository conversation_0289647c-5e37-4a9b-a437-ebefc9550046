﻿using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    /// <summary>
    /// 
    /// </summary>
    internal sealed class SaveCountryCommandHandler(IFreeSql freeSql, IBaseCountryRepository baseCountryRepository) : IRequestHandler<SaveCountryCommand>
    {
        public async Task Handle(SaveCountryCommand request, CancellationToken cancellationToken)
        {
            var firstAsync = await baseCountryRepository.Where(it => true).FirstAsync(cancellationToken);
            var insertAsync = await baseCountryRepository.InsertAsync(new BasCountry
            {
                CountryId = Guid.NewGuid().ToString(),
                CountryCode = null,
                CountryEnUs = null,
                CountryJaJp = null,
                CountryZhCn = null,
                CreateTime = null,
                CreateUserId = null,
                Id = Guid.NewGuid().ToString(),
                IsEnabled = true,
                IsEp = null,
                IsMadrid = false,
                Seq = null,
                UpdateTime = null,
                UpdateUserId = null,
                TrademarkDeliveryCode = "1",
                TrademarkDeliveryCountryName = null
            }, cancellationToken);
            return;
            throw new NotImplementedException();
        }
    }
}

