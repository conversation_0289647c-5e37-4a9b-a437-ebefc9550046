﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 引证商标信息
/// </summary>
public sealed class CitedTrademarkInfo
{
    /// <summary>
    /// 商标注册号
    /// </summary>
    public string RegisterNumber { get; set; } = string.Empty;

    /// <summary>
    /// 尼斯分类大类
    /// </summary>
    public string FirstCategoryNumber { get; set; } = string.Empty;

    /// <summary>
    /// 联系人姓名
    /// </summary>
    public string ContactName { get; set; } = string.Empty;

    /// <summary>
    /// 联系地址
    /// </summary>
    public string ContactAddress { get; set; } = string.Empty;
}