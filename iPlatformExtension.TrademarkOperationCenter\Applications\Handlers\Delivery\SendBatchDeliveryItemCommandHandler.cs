﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class SendBatchDeliveryItemCommandHandler(IMediator mediator)
    : IRequestHandler<SendBatchDeliveryItemCommand, DeliveryItemOperationResult>
{
    public async Task<DeliveryItemOperationResult> Handle(SendBatchDeliveryItemCommand request, CancellationToken cancellationToken)
    {
        var (procId, button, operatorId) = request;
        var internalCommand = await mediator.Send(new SendDeliveryCommand(procId, button, operatorId), cancellationToken);

        if (internalCommand is not null)
        {
            await mediator.Send(internalCommand, cancellationToken);
        }

        return new DeliveryItemOperationResult(procId);
    }
}