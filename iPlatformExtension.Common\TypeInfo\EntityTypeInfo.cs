﻿using System.Reflection;

namespace iPlatformExtension.Common.TypeInfo;

public class EntityTypeInfo
{
    /// <summary>
    /// 表名
    /// </summary>
    public string? TableName { get; internal set; }

    /// <summary>
    /// 有无参的构造函数
    /// </summary>
    internal bool HasNoArgumentsConstructor { get; set; }

    /// <summary>
    /// 原始类型
    /// </summary>
    public System.Reflection.TypeInfo TypeInfo { get; init; } = default!;

    /// <summary>
    /// 属性信息
    /// </summary>
    public EntityPropertyInfoCollection EntityPropertyInfos { get; set; } = default!;

    public object? CreateInstance(
        BindingFlags bindingFlags = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.CreateInstance,
        params object?[] args)
    {
        if (HasNoArgumentsConstructor && args.Length == 0)
        {
            return Activator.CreateInstance(TypeInfo.AsType(), true);
        }

        return Activator.CreateInstance(TypeInfo.AsType(), bindingFlags, null, args, null);
    }
}