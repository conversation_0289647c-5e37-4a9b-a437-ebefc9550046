using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.MailCenter.Applications.Models.Template;
using iPlatformExtension.MailCenter.Applications.Queries.Template;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Template
{
    /// <summary>
    /// 获取邮件模板详情查询处理程序
    /// </summary>
    internal sealed class GetMailTemplateDetailQueryHandler(
        IMailTemplateRepository mailTemplateRepository
    ) : IRequestHandler<GetMailTemplateDetailQuery, GetMailTemplateDetailDto>
    {
        public async Task<GetMailTemplateDetailDto> Handle(GetMailTemplateDetailQuery request, CancellationToken cancellationToken)
        {
            var template = await mailTemplateRepository
                .Where(t => t.TemplateId == request.TemplateId)
                .FirstAsync(cancellationToken)
                ?? throw new NotFoundException(request.TemplateId, "邮件模板");

            return new GetMailTemplateDetailDto(
                template.TemplateId,
                template.TemplateNo,
                template.Name,
                template.Title,
                template.Body,
                template.Sql2,
                template.CreateTime,
                template.CreateBy,
                template.UpdateTime,
                template.UpdateBy,
                template.IsEnabled
            );
        }
    }
}
