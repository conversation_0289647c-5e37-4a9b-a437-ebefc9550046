using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_process", DisableSyncStructure = true)]
	public partial class MailProcess {

		[ Column(Name = "allot_remark", StringLength = 500)]
		public string AllotRemark { get; set; }

		[ Column(Name = "allot_status")]
		public int? AllotStatus { get; set; } = 0;

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "config_type", StringLength = 50)]
		public string ConfigType { get; set; }

		[ Column(Name = "hand_user", StringLength = 50)]
		public string HandUser { get; set; }

		[ Column(Name = "hand_user_type", StringLength = 50)]
		public string HandUserType { get; set; }

		[ Column(Name = "legal_due_date")]
		public DateTime? LegalDueDate { get; set; }

		[ Column(Name = "mail_id", StringLength = 50)]
		public string MailId { get; set; }

		[ Column(Name = "read_user", StringLength = 2000)]
		public string ReadUser { get; set; }

		[ Column(Name = "read_user_type", StringLength = 500)]
		public string ReadUserType { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
