using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_proc_change_info_back", DisableSyncStructure = true)]
	public partial class CaseProcChangeInfoBack {

		[ Column(Name = "after_change", StringLength = 500)]
		public string AfterChange { get; set; }

		[ Column(Name = "before_change", StringLength = 500)]
		public string BeforeChange { get; set; }

		[ Column(Name = "change_time")]
		public DateTime? ChangeTime { get; set; }

		[ Column(Name = "change_type", StringLength = 50)]
		public string ChangeType { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "proc_change_id", StringLength = 50, IsNullable = false)]
		public string ProcChangeId { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
