﻿FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
ENV ASPNETCORE_URLS=http://+:8055
ENV ASPNETCORE_ENVIRONMENT=Staging
ENV TZ=Asia/Shanghai
ENV DOTNET_GCHeapHardLimit=80000000
ENV COMPlus_DbgEnableMiniDump=1
ENV COMPlus_DbgMiniDumpName=/tmp/iplatform-finance.dmp
ENV COMPlus_CreateDumpDiagnostics=1
ENV ASPNETCORE_HOSTINGSTARTUPASSEMBLIES=SkyAPM.Agent.AspNetCore
ENV SKYWALKING__SERVICENAME=test::iplatform-finance
WORKDIR /app
EXPOSE 8055

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["obs_net/esdk_obs_.net_core.csproj", "obs_net/"]
COPY ["iPlatformExtension.Finance/iPlatformExtension.Finance.csproj", "iPlatformExtension.Finance/"]
COPY ["iPlatformExtension.Common/iPlatformExtension.Common.csproj", "iPlatformExtension.Common/"]
COPY ["iPlatformExtension.Model/iPlatformExtension.Model.csproj", "iPlatformExtension.Model/"]
COPY ["iPlatformExtension.Repository/iPlatformExtension.Repository.csproj", "iPlatformExtension.Repository/"]
COPY ["iPlatformExtension.Service/iPlatformExtension.Service.csproj", "iPlatformExtension.Service/"]
RUN dotnet restore "iPlatformExtension.Finance/iPlatformExtension.Finance.csproj"
COPY . .
WORKDIR "/src/iPlatformExtension.Finance"
RUN dotnet build "iPlatformExtension.Finance.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "iPlatformExtension.Finance.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
RUN mkdir "logs"
RUN mkdir "temp"
ENTRYPOINT ["dotnet", "iPlatformExtension.Finance.dll"]
