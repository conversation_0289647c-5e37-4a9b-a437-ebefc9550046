﻿using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.SysConfig
{
    public class GetUserInfoQuery : QueryBase, IRequest<PageResult<GetUserInfoDto>>
    {

        public string? DeptId { get; set; }

        public string? SearchKey { get; set; }

        public string? SearchCode { get; set; }

        public bool? IsCharge { get; set; }

        public string? SearchType { get; set; }

        public string? RoleCode { get; set; }
    }
}
