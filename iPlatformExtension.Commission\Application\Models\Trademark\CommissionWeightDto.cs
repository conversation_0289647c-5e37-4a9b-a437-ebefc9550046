﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.Commission.Application.Models.Trademark;

/// <summary>
/// 提成权值DTO
/// </summary>
public sealed class CommissionWeightDto
{
    // /// <summary>
    // /// 任务id
    // /// </summary>
    // [Required]
    // public string ProcId { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 案件id
    // /// </summary>
    // [Required]
    // public string CaseId { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 我方文号
    // /// </summary>
    // [Required]
    // public string Volume { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 申请号
    // /// </summary>
    // public string? AppNo { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 任务名称id
    // /// </summary>
    // [Required]
    // public string CtrlProcId { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 任务名称
    // /// </summary>
    // [Required]
    // public string CtrProcName { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 客户id
    // /// </summary>
    // [Required]
    // public string CustomerId { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 客户名称
    // /// </summary>
    // [Required]
    // public string CustomerName { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 任务状态id
    // /// </summary>
    // [Required]
    // public string ProcStatusId { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 任务状态
    // /// </summary>
    // [Required]
    // public string ProcStatusText { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 承办人id
    // /// </summary>
    // [Required]
    // public string UndertakerId { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 任务承办人
    // /// </summary>
    // [Required]
    // public string UndertakerName { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 注册号
    // /// </summary>
    // public string? RegisterNo { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 任务编号
    // /// </summary>
    // [Required]
    // public string ProcNo { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 商标名称
    // /// </summary>
    // [Required]
    // public string CaseName { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 任务标识id
    // /// </summary>
    // public string? CtrlProcMark { get; set; } = string.Empty;
    //
    // /// <summary>
    // /// 任务标识
    // /// </summary>
    // public string? ProcMarkText { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否大客户
    /// </summary>
    public bool? IsBigClient { get; set; }

    /// <summary>
    /// 权值
    /// </summary>
    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "请输入以0.01为倍数的正数权值")]
    public decimal EditedProcPoint { get; set; }

    /// <summary>
    /// 权值计提日
    /// </summary>
    [Required]
    public DateOnly CommissionDate { get; set; }
}