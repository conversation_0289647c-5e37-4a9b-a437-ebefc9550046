{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/iPlatformExtension.Finance/iPlatformExtension.Finance.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/iPlatformExtension.Finance/iPlatformExtension.Finance.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/iPlatformExtension.Finance/iPlatformExtension.Finance.csproj"], "problemMatcher": "$msCompile"}, {"label": "clean & build", "command": "dotnet", "type": "shell", "args": ["build", "--no-incremental", "${workspaceFolder}/iPlatformExtension.MailCenter/iPlatformExtension.MailCenter.csproj"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}]}