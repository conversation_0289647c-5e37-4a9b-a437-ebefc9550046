﻿using iPlatformExtension.Commission.Application.Notifications.Trademark.Foreign;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class ForeignCommissionProcResultHandler(IBaseCtrlProcRepository baseCtrlProcRepository) 
    : INotificationHandler<ForeignCommissionResultNotification>
{
    public async Task Handle(ForeignCommissionResultNotification notification, CancellationToken cancellationToken)
    {
        var commission = notification.ForeignTrademarkBonus;
        var ctrlProcId = commission.CtrlProcId;
        
        commission.CtrlProcZhCn = await baseCtrlProcRepository.GetTextValueAsync(ctrlProcId) ?? string.Empty;
        
        commission.TrademarkItemsNum = commission.TrademarkClasses.Split(';').Length;
    }
}