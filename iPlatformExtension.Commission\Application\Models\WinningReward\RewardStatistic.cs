﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Commission.Application.Models.WinningReward;

/// <summary>
/// 用户提成权重统计
/// </summary>
public class RewardStatistics
{
    /// <summary>
    /// 序号
    /// </summary>
    [ExcelColumn(Name = "序号", IndexName = "A")]
    public int SerialNumber { get; set; }
    
    /// <summary>
    /// 用户id
    /// </summary>
    [ExcelColumn(Ignore = true)]
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 工号
    /// </summary>
    [ExcelColumn(Name = "员工编号", IndexName = "E")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 姓名
    /// </summary>
    [ExcelColumn(Name = "受益人", IndexName = "F")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 地区
    /// </summary>
    [ExcelColumn(Name = "地区", IndexName = "D")]
    public string District { get; set; } = string.Empty;

    /// <summary>
    /// 胜诉奖励
    /// </summary>
    [ExcelColumn(Name = "胜诉奖励", IndexName = "G")]
    public decimal Reward { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    [ExcelColumn(Name = "所属部门", IndexName = "C")]
    public string DeptName { get; set; } = string.Empty;

    /// <summary>
    /// 月份
    /// </summary>
    [ExcelColumn(Name = "月份", IndexName = "B")]
    public int Month { get; set; }

    /// <summary>
    /// 年度
    /// </summary>
    [ExcelColumn(Ignore = true)]
    public int Year { get; set; }
}