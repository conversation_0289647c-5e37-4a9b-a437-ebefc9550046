﻿using System.Text;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// excel导出选项
/// </summary>
/// <param name="fileName">下载的文件名</param>
/// <param name="tempDirectory">导出过程中的缓存路径</param>
public class ExcelExportOptions<TRequest, TResult>(string fileName, string tempDirectory)
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; } = fileName;

    /// <summary>
    /// 中间缓存的目录
    /// </summary>
    public string TempDirectory { get; } = tempDirectory;

    /// <summary>
    /// 字符串构建者
    /// </summary>
    public StringBuilder? StringBuilder { get; set; }
    
    /// <summary>
    /// 每次分页查询完后的事件处理
    /// </summary>
    public Func<object, IEnumerable<TResult>, CancellationToken, ValueTask> OnQueried { get; set; } = delegate { return ValueTask.CompletedTask; };

    /// <summary>
    /// 每次分页查询前的事件处理
    /// </summary>
    public Func<object, TRequest, CancellationToken,  ValueTask> OnQuerying { get; set; } = delegate { return ValueTask.CompletedTask; };
}