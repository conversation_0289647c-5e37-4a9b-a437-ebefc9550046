using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "receipt_list", DisableSyncStructure = true)]
	public partial class ReceiptList {

		[ Column(Name = "agency_code", StringLength = 50)]
		public string AgencyCode { get; set; }

		[ Column(Name = "agency_name", StringLength = 200)]
		public string AgencyName { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "error", StringLength = 50)]
		public string Error { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "list_id", StringLength = 50, IsNullable = false)]
		public string ListId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "receipt_code", StringLength = 50, IsNullable = false)]
		public string ReceiptCode { get; set; }

		[ Column(Name = "status")]
		public int Status { get; set; } = 0;

		[ Column(Name = "totals", StringLength = 50)]
		public string Totals { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

	}

}
