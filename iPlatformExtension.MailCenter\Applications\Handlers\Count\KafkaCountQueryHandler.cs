﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Count;
using iPlatformExtension.MailCenter.Applications.Queries.Count;
using iPlatformExtension.Model.Enum;
using MediatR;
using Microsoft.AspNetCore.SignalR.Client;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Count;

/// <summary>
///
/// </summary>
internal sealed class KafkaCountQueryHandler(IRedisCache<RedisCacheOptionsBase> redisCache, HubConnection hubConnection, IMediator mediator, IFreeSql<MailCenterFreeSql> freeSql)
    : RedisCacheHandle(redisCache, mediator, freeSql), IRequestHandler<KafkaFlowRecordCountQuery, IEnumerable<KafkaCountDto>>
{
    public async Task<IEnumerable<KafkaCountDto>> Handle(
        KafkaFlowRecordCountQuery request,
        CancellationToken cancellationToken
    )
    {
        var cdcModelPayload = request.cdcModel.Payload;
        var userId = string.Empty;

        if (cdcModelPayload.Op.ToLower() == "c"
            && cdcModelPayload.After?.IsCurrent == MailFlowActionStatus.Enable.GetHashCode()
        )
        {
            if (cdcModelPayload.After.CurNodeId == MailFlowAction.Handle.ToString())
            {
                userId = cdcModelPayload.After.AuditUser;
                await UpdateHandleCache("Handle", cdcModelPayload.After.AuditUser, 1, cdcModelPayload.TsDateTime, cancellationToken);
            }
            if (cdcModelPayload.After.CurNodeId == MailFlowAction.Allot.ToString())
            {
                userId = cdcModelPayload.After.AuditUser;
                await UpdateHandleCache("Handle", cdcModelPayload.After.AuditUser, 1, cdcModelPayload.TsDateTime, cancellationToken);
            }
        }

        if (cdcModelPayload.Op.ToLower() == "u"
            && cdcModelPayload.After?.IsCurrent == MailFlowActionStatus.DisEnable.GetHashCode())
        {
            if (cdcModelPayload.After.CurNodeId == MailFlowAction.Handle.ToString())
            {
                userId = cdcModelPayload.After.AuditUser;
                await UpdateHandleCache("Handle", cdcModelPayload.After.AuditUser, -1, cdcModelPayload.TsDateTime, cancellationToken);
            }
        }

        if (cdcModelPayload.Op.ToLower() == "d"
            && cdcModelPayload.Before?.IsCurrent == MailFlowActionStatus.Enable.GetHashCode())
        {
            userId = cdcModelPayload.Before.AuditUser;
            await UpdateHandleCache("Handle", cdcModelPayload.Before.AuditUser, -1, cdcModelPayload.TsDateTime, cancellationToken);
        }

        if (!string.IsNullOrWhiteSpace(userId))
        {
            var count = await GetCount(userId, cancellationToken);
            await hubConnection.InvokeAsync("MailCountMessageAsync", "MailCenterCount", userId, count, cancellationToken: cancellationToken);
        }

        return new List<KafkaCountDto>();
    }
}
