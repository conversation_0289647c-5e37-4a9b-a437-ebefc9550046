using iPlatformExtension.Finance.Applications.Queries.Public;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Public;

/// <summary>
/// 账期配置查询
/// </summary>
public sealed class MonDeliConfigQueryHandler : IRequestHandler<MonDeliConfigQuery, MonDeliConfig>
{
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="freeSql">数据库组件</param>
    public MonDeliConfigQueryHandler(IFreeSql freeSql)
    {
        _freeSql = freeSql;
    }

    /// <summary>
    /// 账期配置查询处理
    /// </summary>
    /// <param name="request"></param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>账期配置信息</returns>
    public Task<MonDeliConfig> Handle(MonDeliConfigQuery request, CancellationToken cancellationToken)
    {
        return _freeSql.Select<MonDeliConfig>().FirstAsync(cancellationToken);
    }
}