﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;

internal sealed class DefaultSendPolicyProvider
{
    private readonly IList<ISendPolicy> _policies;

    public DefaultSendPolicyProvider(IEnumerable<ISendPolicy> policies)
    {
        _policies = new List<ISendPolicy>(policies.Count());
        foreach (var sendPolicy in policies)
        {
            _policies.Add(sendPolicy);
        }
    }

    public async ValueTask<ISendPolicy> GetPolicyAsync(DeliInfo deliveryInfo)
    {
        foreach (var sendPolicy in _policies)
        {
            if (await sendPolicy.MatchPolicyAsync(deliveryInfo))
            {
                return sendPolicy;
            }
        }

        return _policies.Cast<DefaultPolicy>().First();
    }
}