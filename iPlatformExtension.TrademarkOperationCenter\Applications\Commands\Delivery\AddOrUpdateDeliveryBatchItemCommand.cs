﻿using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

internal sealed record AddOrUpdateDeliveryBatchItemCommand(string ProcId, int? Version, string OperatorId, bool Refresh) : 
    IDeliveryBatchItemCommand, IRequest<DeliveryItemOperationResult>;