using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ud_task_details_case_proc_info", DisableSyncStructure = true)]
	public partial class UdTaskDetailsCaseProcInfo {

		[ Column(Name = "obj_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ObjId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(StringLength = 1000)]
		public string 案件任务ID { get; set; }

		[ Column(Name = "承办部门(商标)", StringLength = 1000)]
		public string 承办部门商标 { get; set; }

		[ Column(StringLength = 1000)]
		public string 承办人 { get; set; }

		[ Column(Name = "初稿期限(内)", StringLength = 100)]
		public string 初稿期限内 { get; set; }

		[ Column(Name = "初稿期限(外)", StringLength = 100)]
		public string 初稿期限外 { get; set; }

		[ Column(StringLength = 100)]
		public string 初稿日 { get; set; }

		[ Column(StringLength = 100)]
		public string 创建时间 { get; set; }

		[ Column(StringLength = 1000)]
		public string 代理人处理状态 { get; set; }

		[ Column(StringLength = 1000)]
		public string 递交方式 { get; set; }

		[ Column(Name = "定稿期限(内)", StringLength = 100)]
		public string 定稿期限内 { get; set; }

		[ Column(Name = "定稿期限(外)", StringLength = 100)]
		public string 定稿期限外 { get; set; }

		[ Column(StringLength = 100)]
		public string 定稿日 { get; set; }

		[ Column(StringLength = 100)]
		public string 返发明人期限 { get; set; }

		[ Column(StringLength = 100)]
		public string 返发明人日 { get; set; }

		[ Column(StringLength = 100)]
		public string 返接口人期限 { get; set; }

		[ Column(StringLength = 100)]
		public string 返接口人日 { get; set; }

		[ Column(Name = "跟案人(商标)", StringLength = 1000)]
		public string 跟案人商标 { get; set; }

		[ Column(StringLength = 100)]
		public string 官方来文日 { get; set; }

		[ Column(StringLength = 100)]
		public string 官方期限 { get; set; }

		[ Column(StringLength = 1000)]
		public string 官方信息 { get; set; }

		[ Column(StringLength = 1000)]
		public string 核稿人 { get; set; }

		[ Column(StringLength = 1000)]
		public string 境外代理 { get; set; }

		[ Column(StringLength = 100)]
		public string 客户期限 { get; set; }

		[ Column(StringLength = 1000)]
		public string 名义承办人 { get; set; }

		[ Column(StringLength = 100)]
		public string 内部期限 { get; set; }

		[ Column(StringLength = 100)]
		public string 配案日 { get; set; }

		[ Column(StringLength = 1000)]
		public string 任务备注 { get; set; }

		[ Column(StringLength = 1000)]
		public string 任务编号 { get; set; }

		[ Column(StringLength = 1000)]
		public string 任务标识 { get; set; }

		[ Column(StringLength = 1000)]
		public string 任务名称 { get; set; }

		[ Column(StringLength = 1000)]
		public string 任务属性 { get; set; }

		[ Column(StringLength = 1000)]
		public string 任务状态 { get; set; }

		[ Column(StringLength = 1000)]
		public string 申请编号 { get; set; }

		[ Column(Name = "申请日(任务)", StringLength = 100)]
		public string 申请日任务 { get; set; }

		[ Column(StringLength = 100)]
		public string 送官方日期 { get; set; }

		[ Column(StringLength = 100)]
		public string 送外所日 { get; set; }

		[ Column(StringLength = 1000)]
		public string 提成比例 { get; set; }

		[ Column(StringLength = 1000)]
		public string 外派处理人 { get; set; }

		[ Column(StringLength = 100)]
		public string 完成日 { get; set; }

		[ Column(StringLength = 100)]
		public string 委案日期 { get; set; }

		[ Column(StringLength = 1000)]
		public string 我方文号 { get; set; }

		[ Column(Name = "业务类型(商标)", StringLength = 1000)]
		public string 业务类型商标 { get; set; }

		[ Column(Name = "allocate_date")]
		public DateTime? AllocateDate { get; set; }

		[ Column(Name = "back_inventor_date")]
		public DateTime? BackInventorDate { get; set; }

		[ Column(Name = "back_ipr_date")]
		public DateTime? BackIprDate { get; set; }

		[ Column(Name = "bus_type_id", StringLength = 50)]
		public string BusTypeId { get; set; }

		[ Column(Name = "case_id", StringLength = 100)]
		public string CaseId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "ctrl_proc_mark", StringLength = 50)]
		public string CtrlProcMark { get; set; }

		[ Column(Name = "ctrl_proc_property", StringLength = 50)]
		public string CtrlProcProperty { get; set; }

		[ Column(Name = "cus_due_date")]
		public DateTime? CusDueDate { get; set; }

		[ Column(Name = "cus_finish_date")]
		public DateTime? CusFinishDate { get; set; }

		[ Column(Name = "cus_first_date")]
		public DateTime? CusFirstDate { get; set; }

		[ Column(Name = "cus_inventor_date")]
		public DateTime? CusInventorDate { get; set; }

		[ Column(Name = "cus_ipr_date")]
		public DateTime? CusIprDate { get; set; }

		[ Column(Name = "entrust_date")]
		public DateTime? EntrustDate { get; set; }

		[ Column(Name = "error_columns", StringLength = 2000)]
		public string ErrorColumns { get; set; }

		[ Column(Name = "error_message", StringLength = -2)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "examine_percentage", StringLength = 8)]
		public string ExaminePercentage { get; set; }

		[ Column(Name = "filing_type", StringLength = 100)]
		public string FilingType { get; set; }

		[ Column(Name = "finish_date")]
		public DateTime? FinishDate { get; set; }

		[ Column(Name = "finish_doc_date")]
		public DateTime? FinishDocDate { get; set; }

		[ Column(Name = "first_doc_date")]
		public DateTime? FirstDocDate { get; set; }

		[ Column(Name = "first_examine_user_id", StringLength = 50)]
		public string FirstExamineUserId { get; set; }

		[ Column(Name = "foregin_agency_id", StringLength = 50)]
		public string ForeginAgencyId { get; set; }

		[ Column(Name = "int_due_date")]
		public DateTime? IntDueDate { get; set; }

		[ Column(Name = "int_finish_date")]
		public DateTime? IntFinishDate { get; set; }

		[ Column(Name = "int_first_date")]
		public DateTime? IntFirstDate { get; set; }

		[ Column(Name = "legal_due_date")]
		public DateTime? LegalDueDate { get; set; }

		[ Column(Name = "o_allocate_date")]
		public DateTime? OAllocateDate { get; set; }

		[ Column(Name = "o_bus_type_id", StringLength = 50)]
		public string OBusTypeId { get; set; }

		[ Column(Name = "o_case_id", StringLength = 50)]
		public string OCaseId { get; set; }

		[ Column(Name = "o_create_time")]
		public DateTime? OCreateTime { get; set; }

		[ Column(Name = "o_ctrl_proc_id", StringLength = 50)]
		public string OCtrlProcId { get; set; }

		[ Column(Name = "o_ctrl_proc_mark", StringLength = 50)]
		public string OCtrlProcMark { get; set; }

		[ Column(Name = "o_ctrl_proc_property", StringLength = 50)]
		public string OCtrlProcProperty { get; set; }

		[ Column(Name = "o_cus_due_date")]
		public DateTime? OCusDueDate { get; set; }

		[ Column(Name = "o_cus_finish_date")]
		public DateTime? OCusFinishDate { get; set; }

		[ Column(Name = "o_cus_first_date")]
		public DateTime? OCusFirstDate { get; set; }

		[ Column(Name = "o_customer_case_no", StringLength = 50)]
		public string OCustomerCaseNo { get; set; }

		[ Column(Name = "o_entrust_date")]
		public DateTime? OEntrustDate { get; set; }

		[ Column(Name = "o_examine_percentage", DbType = "money")]
		public decimal? OExaminePercentage { get; set; }

		[ Column(Name = "o_filing_type", StringLength = 50)]
		public string OFilingType { get; set; }

		[ Column(Name = "o_finish_date")]
		public DateTime? OFinishDate { get; set; }

		[ Column(Name = "o_finish_doc_date")]
		public DateTime? OFinishDocDate { get; set; }

		[ Column(Name = "o_first_doc_date")]
		public DateTime? OFirstDocDate { get; set; }

		[ Column(Name = "o_first_examine_user_id", StringLength = 50)]
		public string OFirstExamineUserId { get; set; }

		[ Column(Name = "o_foregin_agency_id", StringLength = 50)]
		public string OForeginAgencyId { get; set; }

		[ Column(Name = "o_int_due_date")]
		public DateTime? OIntDueDate { get; set; }

		[ Column(Name = "o_int_finish_date")]
		public DateTime? OIntFinishDate { get; set; }

		[ Column(Name = "o_int_first_date")]
		public DateTime? OIntFirstDate { get; set; }

		[ Column(Name = "o_legal_due_date")]
		public DateTime? OLegalDueDate { get; set; }

		[ Column(Name = "o_official_note", StringLength = 4000)]
		public string OOfficialNote { get; set; }

		[ Column(Name = "o_out_user", StringLength = 50)]
		public string OOutUser { get; set; }

		[ Column(Name = "o_proc_app_date")]
		public DateTime? OProcAppDate { get; set; }

		[ Column(Name = "o_proc_app_no", StringLength = 50)]
		public string OProcAppNo { get; set; }

		[ Column(Name = "o_proc_dept_id", StringLength = 50)]
		public string OProcDeptId { get; set; }

		[ Column(Name = "o_proc_id", StringLength = 50)]
		public string OProcId { get; set; }

		[ Column(Name = "o_proc_no", StringLength = 50)]
		public string OProcNo { get; set; }

		[ Column(Name = "o_proc_note", StringLength = 4000)]
		public string OProcNote { get; set; }

		[ Column(Name = "o_proc_status_id", StringLength = 50)]
		public string OProcStatusId { get; set; }

		[ Column(Name = "o_receive_date")]
		public DateTime? OReceiveDate { get; set; }

		[ Column(Name = "o_send_official_date")]
		public DateTime? OSendOfficialDate { get; set; }

		[ Column(Name = "o_send_partner_date")]
		public DateTime? OSendPartnerDate { get; set; }

		[ Column(Name = "o_seq")]
		public short? OSeq { get; set; }

		[ Column(Name = "o_sub_proc_status_id", StringLength = 50)]
		public string OSubProcStatusId { get; set; }

		[ Column(Name = "o_titular_write_user", StringLength = 50)]
		public string OTitularWriteUser { get; set; }

		[ Column(Name = "o_track_user_id", StringLength = 50)]
		public string OTrackUserId { get; set; }

		[ Column(Name = "o_undertake_user_id", StringLength = 50)]
		public string OUndertakeUserId { get; set; }

		[ Column(Name = "official_note", StringLength = 4000)]
		public string OfficialNote { get; set; }

		[ Column(Name = "out_user", StringLength = 50)]
		public string OutUser { get; set; }

		[ Column(Name = "pa_dept_id", StringLength = 50)]
		public string PaDeptId { get; set; }

		[ Column(Name = "pa_flow_id", StringLength = 50)]
		public string PaFlowId { get; set; }

		[ Column(Name = "pa_node_id", StringLength = 50)]
		public string PaNodeId { get; set; }

		[ Column(Name = "pa_user_id", StringLength = 50)]
		public string PaUserId { get; set; }

		[ Column(Name = "proc_app_date")]
		public DateTime? ProcAppDate { get; set; }

		[ Column(Name = "proc_app_no", StringLength = 100)]
		public string ProcAppNo { get; set; }

		[ Column(Name = "proc_dept_id", StringLength = 50)]
		public string ProcDeptId { get; set; }

		[ Column(Name = "proc_id", StringLength = 100)]
		public string ProcId { get; set; }

		[ Column(Name = "proc_no", StringLength = 100)]
		public string ProcNo { get; set; }

		[ Column(Name = "proc_note", StringLength = 4000)]
		public string ProcNote { get; set; }

		[ Column(Name = "proc_status_id", StringLength = 50)]
		public string ProcStatusId { get; set; }

		[ Column(Name = "receive_date")]
		public DateTime? ReceiveDate { get; set; }

		[ Column(Name = "send_official_date")]
		public DateTime? SendOfficialDate { get; set; }

		[ Column(Name = "send_partner_date")]
		public DateTime? SendPartnerDate { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "sub_proc_status_id", StringLength = 50)]
		public string SubProcStatusId { get; set; }

		[ Column(Name = "task_id", StringLength = 50)]
		public string TaskId { get; set; }

		[ Column(Name = "titular_write_user", StringLength = 50)]
		public string TitularWriteUser { get; set; }

		[ Column(Name = "track_user_id", StringLength = 50)]
		public string TrackUserId { get; set; }

		[ Column(Name = "ud_create_time", InsertValueSql = "getdate()")]
		public DateTime? UdCreateTime { get; set; }

		[ Column(Name = "undertake_user_id", StringLength = 50)]
		public string UndertakeUserId { get; set; }

	}

}
