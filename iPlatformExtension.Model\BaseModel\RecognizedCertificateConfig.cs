﻿using System;
using System.Collections.Generic;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{

    [Table(Name = "recognized_certificate_config", DisableSyncStructure = true)]
    public partial class RecognizedCertificateConfig
    {

        /// <summary>
        /// 标签id
        /// </summary>
        [Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

        /// <summary>
        /// 国家列表
        /// </summary>
        [Column(Name = "country_ids", StringLength = 500)]
        public string CountryIds { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column(Name = "create_time")]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建用户id
        /// </summary>
        [Column(Name = "create_user_id", StringLength = 50, IsNullable = false)]
        public string CreateUserId { get; set; }

        /// <summary>
        /// 任务id
        /// </summary>
        [Column(Name = "ctrl_proc_id", StringLength = 50, IsNullable = false)]
        public string CtrlProcId { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>
        [Column(Name = "is_enable")]
        public bool IsEnable { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column(Name = "mark", StringLength = 500)]
        public string Mark { get; set; }

        /// <summary>
        /// 公认证标识
        /// </summary>
        [Column(Name = "recognized_certificate_id", StringLength = 50, IsNullable = false)]
        public string RecognizedCertificateId { get; set; }

        /// <summary>
        /// 更新时间
        /// 更新时间
        /// </summary>
        [Column(Name = "update_time")]
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 更新用户id
        /// </summary>
        [Column(Name = "update_user_id", StringLength = 50)]
        public string UpdateUserId { get; set; }


        /// <summary>
        ///  任务类型信息
        /// </summary>
        [Navigate(nameof(CtrlProcId))]
        public virtual BasCtrlProc BasCtrlProc { get; set; } = default!;

    }

}
