﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Copy;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Copy;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Copy
{
    /// <summary>
    /// 获取复制列表
    /// </summary>
    public class GetCopyListQueryHandler : IRequestHandler<GetCopyListQuery, IEnumerable<GetCopyListDto>>
    {
        private readonly IFreeSql _freeSql;

        public GetCopyListQueryHandler(IFreeSql freeSql)
        {
            _freeSql = freeSql;
        }

        public async Task<IEnumerable<GetCopyListDto>> Handle(GetCopyListQuery request, CancellationToken cancellationToken)
        {
            return await _freeSql.Select<CopyFiledList>().Where(it => request.Type.Contains(it.Type) && it.FatherId == null)
                  .ToListAsync(it => new GetCopyListDto(it.Id, it.Name, it.Type, it.Mark) { }, cancellationToken);
        }
    }
}

