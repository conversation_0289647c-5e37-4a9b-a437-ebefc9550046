﻿using System.Collections.Generic;
using System.Linq.Expressions;
using FreeSql.Internal.Model;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class ProcIdsQueryHandler(
    IFreeSql freeSql,
    IAgencyRepository agencyRepository,
    IUserInfoRepository infoRepository,
    IBaseCtrlProcRepository baseCtrlProcRepository
) : IRequestHandler<ProcIdsQuery, IEnumerable<ProcItemDto>>
{
    public async Task<IEnumerable<ProcItemDto>> Handle(
        ProcIdsQuery request,
        CancellationToken cancellationToken
    )
    {
        var procIds = request.ProcIds;
        ICacheableRepository<string, UserBaseInfo> userInfoRepository = infoRepository;

        var dbSelect = freeSql
            .Select<CaseProcInfo, CaseInfo, CaseApplicantList, CusApplicant>()
            .WithLock()
            .LeftJoin<CaseInfo>((procInfo, caseInfo) => procInfo.CaseId == caseInfo.Id)
            .LeftJoin<CaseApplicantList>(
                (procInfo, applicant) =>
                    procInfo.CaseId == applicant.CaseId && applicant.IsRepresent == true
            )
            .LeftJoin(it => it.t3.ApplicantId == it.t4.ApplicantId)
            .WhereIf(
                request.ProcIds is not null,
                (procInfo, caseInfo, applicant, cusApplicant) => procIds!.Contains(procInfo.ProcId)
            )
            .WhereIf(request.ExcludeProcId is not null, it => it.t1.ProcId != request.ExcludeProcId)
            .WhereIf(
                request.CtrlProcId is not null,
                (procInfo, caseInfo, applicant, cusApplicant) =>
                    procInfo.CtrlProcId == request.CtrlProcId
            )
            .OrderByDescending((procInfo, caseInfo, applicant, cusApplicant) => procInfo.CreateTime);

        if (request.Search is not null)
        {
            var searchStrings = request
                .Search.Split(' ', ';', ',')
                .Where(it => !string.IsNullOrWhiteSpace(it));
            var where = searchStrings.Aggregate(
                default(Expression<
                    Func<HzyTuple<CaseInfo, CaseApplicantList, CusApplicant>, bool>
                >),
                (current, search) =>
                    current.Or(it =>
                        it.t1.Volume.Contains(search)
                        || it.t1.CaseName.Contains(search)
                        || it.t3.ApplicantNameCn.Contains(search)
                        || it.t1.ApplyNo.Contains(search)
                    )
            );

            var caseList = await freeSql
                .Select<CaseInfo, CaseApplicantList, CusApplicant>()
                .WithLock()
                .LeftJoin(
                    (caseInfo, caseApplicantList, applicant) =>
                        caseInfo.Id == caseApplicantList.CaseId
                        && caseApplicantList.IsRepresent == true
                )
                .LeftJoin(it => it.t2.ApplicantId == it.t3.ApplicantId)
                .Where(where)
                .WhereIf(
                    request.CustomerId is not null,
                    (caseInfo, applicant, cusApplicant) =>
                        caseInfo.CustomerId == request.CustomerId
                )
                .WhereIf(
                    request.CaseDirection is not null,
                    (caseInfo, applicant, cusApplicant) =>
                        caseInfo.CaseDirection == request.CaseDirection
                )
                .WhereIf(
                    request.CaseTypeId is not null,
                    (caseInfo, applicant, cusApplicant) =>
                        caseInfo.CaseTypeId == request.CaseTypeId
                )
                .ToListAsync(it => it.t1.Id, cancellationToken);

            dbSelect.Where(
                (procInfo, caseInfo, applicant, cusApplicant) => caseList.Contains(caseInfo.Id)
            );
        }

        var result = await dbSelect.ToPageableResultAsync(
            request,
            (procInfo, caseInfo, applicant, cusApplicant) =>
                new ProcItemDto
                {
                    ProcId = procInfo.ProcId,
                    ProcNo = procInfo.ProcNo,
                    Name = caseInfo.CaseName,
                    AppNo = caseInfo.AppNo,
                    AgencyId = procInfo.TrademarkDeliveryAgencyId,
                    UndertakerId = procInfo.UndertakeUserId,
                    CtrlProcId = procInfo.CtrlProcId,
                    TrademarkClass = caseInfo.TrademarkClass ?? string.Empty,
                    ApplicantId = applicant.ApplicantId,
                    ApplicantInfo = new ApplicantInfo(
                        applicant.ApplicantId,
                        cusApplicant.ApplicantNameCn,
                        cusApplicant.ApplicantNameEn
                    ),
                },
            cancellationToken
        );

        var data = await result
            .ToAsyncEnumerable()
            .SelectAwait(async procInfo =>
            {
                var agencyInfo = await agencyRepository.GetCacheValueAsync(
                    procInfo.AgencyId ?? string.Empty
                );
                var undertakerInfo = await userInfoRepository.GetCacheValueAsync(
                    procInfo.UndertakerId ?? string.Empty
                );
                var baseCtrlProcInfo = await baseCtrlProcRepository.GetCacheValueAsync(
                    procInfo.CtrlProcId
                );

                procInfo.UndertakerInfo = undertakerInfo;
                procInfo.ProcName = baseCtrlProcInfo?.CtrlProcZhCn ?? string.Empty;

                if (agencyInfo is not null)
                {
                    procInfo.AgencyName = agencyInfo.AgencyNameCn;
                }

                return procInfo;
            })
            .ToListAsync(cancellationToken);

        if (result is not PageResult<ProcItemDto> pageResult)
            return data;

        pageResult.Data = data;
        return pageResult;
    }
}
