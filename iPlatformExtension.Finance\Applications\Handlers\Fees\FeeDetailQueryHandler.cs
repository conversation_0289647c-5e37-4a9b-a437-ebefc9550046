﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeDetailQueryHandler(
    IFreeSql freeSql,
    ISystemDictionaryRepository dictionaryRepository,
    IBaseFeeTypeNameRepository feeTypeNameRepository,
    IBaseCtrlProcRepository ctrlProcRepository)
    : IRequestHandler<FeeDetailQuery, CaseFeeDetailDto>
{
    public async Task<CaseFeeDetailDto> Handle(FeeDetailQuery request, CancellationToken cancellationToken)
    {
        var feeId = request.FeeId;
        ArgumentNullException.ThrowIfNull(feeId);
        
        var feeDetail = await freeSql.Select<CaseFeeList>().WithLock()
            .From<CaseProcInfo, SysUserInfo, SysUserInfo>(
                (feeList, procInfo, creator, updater) =>
                    feeList.LeftJoin(fee => fee.ProcId == procInfo.ProcId)
                        .LeftJoin(fee => fee.CreateUserId == creator.UserId)
                        .LeftJoin(fee => fee.UpdateUserId == updater.UserId))
            .Where((feeList, procInfo, creator, updater) => feeList.FeeId == feeId)
            .FirstAsync(
                (fee, procInfo, creator, updater) => new CaseFeeDetailDto()
                    {
                        FeeId = fee.FeeId,
                        Amount = fee.Amount!.Value,
                        BalanceWay = new KeyValuePair<string, string>(SystemDictionaryName.BalanceWay.ToSqlStringConstant(), fee.BalanceWay),
                        BillNo = fee.BillNo,
                        CreationTime = fee.CreateTime!.Value,
                        Creator = new KeyValuePair<string, string>(creator.UserId, creator.CnName),
                        Currency = fee.CurrencyId,
                        Discount = fee.Discount,
                        FeeClass = new KeyValuePair<string, string>(SystemDictionaryName.FeeType.ToSqlStringConstant(), fee.FeeClass),
                        FeeTypeNameId = fee.FeeTypeNameId,
                        IsEdit = fee.IsEdit,
                        IsAuto = fee.IsAuto,
                        IsEnable = fee.IsEnabled!.Value,
                        OfficeName = fee.OfficeName,
                        IsFirstPayAnnual = fee.IsFirstPayAnnual,
                        StandardAmount = fee.StandardAmount!.Value,
                        ReceiveRule = new KeyValuePair<string, string>(SystemDictionaryName.ChargeRule.ToSqlStringConstant(), fee.ReceiveRule ?? string.Empty),
                        Seq = fee.Seq,
                        Remark = fee.Remark,
                        IsOfficer = fee.IsOfficer,
                        PayWay = new KeyValuePair<string, string>(SystemDictionaryName.PayWay.ToSqlStringConstant(), fee.PayWay ?? string.Empty),
                        OfficerStatus = new KeyValuePair<string, string>(SystemDictionaryName.OfficerStatus.ToSqlStringConstant(), fee.OfficerStatus),
                        PayOfficerSerial = fee.PayOfficeSerial,
                        PayOfficerDate = fee.PayOfficerDate,
                        PayOfficerLegalDate = fee.PayOfficerLegalDate,
                        PaymentName = new KeyValuePair<string, string>(SystemDictionaryName.PaymentName.ToSqlStringConstant(), fee.PaymentName ?? string.Empty),
                        PaymentAgency = fee.PaymentAgency,
                        IsRequest = fee.IsRequest,
                        PreRequestDate = fee.PreRequestDate,
                        ReceiveStatus = new KeyValuePair<string, string>(SystemDictionaryName.ReceiveStatus.ToSqlStringConstant(), fee.ReceiveStatus),
                        ReceiveDueDate = fee.ReceiveDueDate,
                        ReceiveDate = fee.ReceiveDate,
                        TaxRate = fee.TaxRate,
                        InvoiceNo = fee.InvoiceNo,
                        PayBillNo = fee.PayBillNo,
                        PayCooperationDate = fee.PayCooperationDate,
                        PayStatus = new KeyValuePair<string, string>(SystemDictionaryName.PayStatus.ToSqlStringConstant(), fee.PayStatus),
                        PayDueDate = fee.PayDueDate,
                        Updater = new KeyValuePair<string, string>(updater.UserId, updater.CnName),
                        UpdateTime = fee.UpdateTime!.Value,
                        ProcName = procInfo.CtrlProcId,
                        RequestDate = fee.RequestDate
                    }, cancellationToken);

        if (feeDetail is null) throw NotFoundException.FeesNotFound(feeId);

        ICacheableRepository<SystemDictionaryValueKey, SysDictionary> systemDictionaryRepository =
            dictionaryRepository;
        IStringKeyCacheableRepository<FeeNameInfo> baseFeeTypeNameRepository = feeTypeNameRepository;
        IStringKeyCacheableRepository<BasCtrlProc> baseCtrlProcRepository = ctrlProcRepository;
        
        feeDetail.BalanceWay = await systemDictionaryRepository.GetChineseKeyValueAsync(feeDetail.BalanceWay);
        feeDetail.ReceiveRule = await systemDictionaryRepository.GetChineseKeyValueAsync(feeDetail.ReceiveRule);
        feeDetail.PayWay = await systemDictionaryRepository.GetChineseKeyValueAsync(feeDetail.PayWay);
        feeDetail.PaymentName = await systemDictionaryRepository.GetChineseKeyValueAsync(feeDetail.PaymentName);
        feeDetail.ReceiveStatus = await systemDictionaryRepository.GetChineseKeyValueAsync(feeDetail.ReceiveStatus);
        feeDetail.FeeClass = await systemDictionaryRepository.GetChineseKeyValueAsync(feeDetail.FeeClass);
        feeDetail.OfficerStatus = await systemDictionaryRepository.GetChineseKeyValueAsync(feeDetail.OfficerStatus);
        feeDetail.PayStatus = await systemDictionaryRepository.GetChineseKeyValueAsync(feeDetail.PayStatus);
        feeDetail.FeeTypeName = await baseFeeTypeNameRepository.GetChineseKeyValueAsync(feeDetail.FeeTypeNameId);
        feeDetail.ProcName = await baseCtrlProcRepository.GetChineseValueAsync(feeDetail.ProcName) ?? string.Empty;
        return feeDetail;
    }
}