﻿using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace iPlatform.Extension.Public.Test;

public class ProcControllerTest(PublicWebApplicationFactory factory) : IClassFixture<PublicWebApplicationFactory>
{
    private readonly HttpClient _httpClient = factory.CreateClient();
    
    private readonly IOptions<JsonOptions> _jsonOptions = factory.Services.GetRequiredService<IOptions<JsonOptions>>();

    // 测试根据关键字查询任务名称的API
    [Theory]
    [InlineData("注册申请", "", "", null)]
    [InlineData("注册申请", "OO", "T", null)]
    [InlineData("新申请", "II", "P", true)]
    public async Task GetCtrlProcInfoAsync_ReturnsOkResult(string keyword, string caseDirection, string caseType, bool? isEnabled)
    {
        // Arrange
        var jsonSerializerOptions = _jsonOptions.Value.JsonSerializerOptions;

        // Act
        var response = await _httpClient.GetAsync($"proc/base-ctrl-proc?keyword={keyword}&caseDirection={caseDirection}&caseType={caseType}&isEnabled={isEnabled}");
        var result = await response.Content.ReadFromJsonAsync<ResultData<IEnumerable<CtrlProcInfo>>>(jsonSerializerOptions);

        // Assert
        response.EnsureSuccessStatusCode();
        // 这里可以添加对响应内容的进一步验证，例如反序列化并检查数据
        
        Assert.NotNull(result);
        Assert.NotNull(result.Data);
        Assert.NotEmpty(result.Data!);
        Assert.True(result.Data!.All(info => info.IsEnabled is not null));
    }

    // 测试获取任务标识信息的API
    [Fact]
    public async Task GetCtrlProcMarkInfoAsync_ReturnsOkResult()
    {
        // Arrange
        var ctrlProcId = "12345";

        // Act
        var response = await _httpClient.GetAsync($"proc/{ctrlProcId}/mark");

        // Assert
        response.EnsureSuccessStatusCode();
        // 这里可以添加对响应内容的进一步验证，例如反序列化并检查数据
    }
}