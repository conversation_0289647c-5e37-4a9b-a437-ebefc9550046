using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_allot_handover", DisableSyncStructure = true)]
	public partial class CaseAllotHandover {

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "flow_id", StringLength = 50, IsNullable = false)]
		public string FlowId { get; set; }

		[ Column(Name = "flow_type", StringLength = 50)]
		public string FlowType { get; set; }

		[ Column(Name = "prev_audit_type_id", StringLength = 50)]
		public string PrevAuditTypeId { get; set; }

		[ Column(Name = "private_id", StringLength = 50)]
		public string PrivateId { get; set; }

		[ Column(Name = "status")]
		public int? Status { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
