﻿using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.Receive;

/// <summary>
/// 设置关联关系
/// </summary>
/// <param name="MailId">邮件id</param>
/// <param name="CorrelateType">关联类型:发件:SendMail,收件:ReceiveMail,案件:Case,任务:Proc,客户:Customer</param>
/// <param name="RelateIdList">关联id</param>
public record SetRelateCommand(string MailId, string CorrelateType, List<string> RelateIdList) : IRequest, IUnitOfWorkCommandMysql;

