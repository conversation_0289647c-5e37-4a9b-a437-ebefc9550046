using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rec_batch_detail_info", DisableSyncStructure = true)]
	public partial class RecBatchDetailInfo {

		[ Column(Name = "info_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string InfoId { get; set; }

		[ Column(Name = "control_date")]
		public DateTime? ControlDate { get; set; }

		[ Column(Name = "control_remark", StringLength = 2000)]
		public string ControlRemark { get; set; }

		[ Column(Name = "daili_wenhao", StringLength = 50)]
		public string DailiWenhao { get; set; }

		[ Column(Name = "detail_id", StringLength = 50)]
		public string DetailId { get; set; }

		[ Column(Name = "file_id", StringLength = 50)]
		public string FileId { get; set; }

		[ Column(Name = "info_status", StringLength = 50)]
		public string InfoStatus { get; set; }

		[ Column(Name = "is_success")]
		public bool? IsSuccess { get; set; } = true;

		[ Column(Name = "is_user")]
		public bool? IsUser { get; set; } = false;

		[ Column(Name = "jiaofei_ren", StringLength = 200)]
		public string JiaofeiRen { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "shenqing_hao", StringLength = 50)]
		public string ShenqingHao { get; set; }

		[ Column(Name = "shouju_biaohao", StringLength = 50)]
		public string ShoujuBiaohao { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
