using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class BuildTrademarkInfoCommandHandler(ISystemDictionaryRepository systemDictionaryRepository)
    : IRequestHandler<BuildTrademarkInfoCommand>
{
    public async Task Handle(BuildTrademarkInfoCommand request, CancellationToken cancellationToken)
    {
        var deliveryInfo = request.DeliveryInfo;
        var procInfo = request.ProcInfo;
        var caseInfo = procInfo.CaseInfo;
        var extendInfo = caseInfo.ExtendInfo;

        deliveryInfo.CtrlProcId = procInfo.CtrlProcId;
        deliveryInfo.CaseName = caseInfo.CaseName;
        deliveryInfo.CaseNameEn = caseInfo.CaseNameEn;
        deliveryInfo.CaseTypeId = CaseType.Trade;
        deliveryInfo.Description = extendInfo?.CaseDescriptions ?? string.Empty;
        deliveryInfo.ApplyTypeId = caseInfo.ApplyTypeId;
        deliveryInfo.IsMultipartColor = caseInfo.ShowMode?.Contains(TrademarkMarkingType.Color) ?? false;
        deliveryInfo.IsThreeD = caseInfo.ShowMode?.Contains(TrademarkMarkingType.Solid) ?? false;
        deliveryInfo.IsVoice = caseInfo.ShowMode?.Contains(TrademarkMarkingType.Sound) ?? false;
        deliveryInfo.IsPortraits = caseInfo.ShowMode?.Contains(TrademarkMarkingType.Portraits) ?? false;
        deliveryInfo.Volume = caseInfo.Volume;
        deliveryInfo.UndertakerId = procInfo.UndertakeUserId ?? string.Empty;
        deliveryInfo.ProcNo = procInfo.ProcNo;
        deliveryInfo.AppNo = caseInfo.AppNo;
        deliveryInfo.OfficialDeadline = procInfo.LegalDueDate;
        deliveryInfo.CustomerId = caseInfo.CustomerId;
        deliveryInfo.DeliveryType = procInfo.FilingType;
        deliveryInfo.MarkingType = string.Join(';',
            await (caseInfo.ShowMode?.Split(';',
                    StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>())
                .ToAsyncEnumerable().SelectAwait(
                    async showMode => (await systemDictionaryRepository.GetChineseKeyValueAsync(
                        SystemDictionaryName.TrademarkingType,
                        showMode)).Value).ToArrayAsync(cancellationToken));
        deliveryInfo.AgentUser = procInfo.AgentUser;
        deliveryInfo.ApplicationType = caseInfo.MultiType;
    }
}