using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ant_url_config", DisableSyncStructure = true)]
	public partial class AntUrlConfig {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "code", StringLength = 50)]
		public string Code { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "title", StringLength = 500, IsNullable = false)]
		public string Title { get; set; }

		[ Column(Name = "url", StringLength = 500, IsNullable = false)]
		public string Url { get; set; }

	}

}
