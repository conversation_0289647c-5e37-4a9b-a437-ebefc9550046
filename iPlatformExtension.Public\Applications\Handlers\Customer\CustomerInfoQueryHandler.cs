﻿using System.Linq.Expressions;
using System.Reflection;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models.Customer;
using iPlatformExtension.Public.Applications.Queries.Customer;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Customer;

internal sealed class CustomerInfoQueryHandler(IFreeSql<PlatformFreeSql> freeSql) : IRequestHandler<CustomerInfoQuery, IEnumerable<CustomerInfoDto>>
{
    private static readonly IEnumerable<Expression<Func<CusCustomer, string>>> customerPropertyExpressions =
    [
        customer => customer.CustomerName,
        customer => customer.CustomerFullName,
        customer => customer.CustomerNameEn,
        customer => customer.CustomerCode
    ];

    private static readonly MethodInfo? containsMethodInfo = typeof(string).GetMethod(nameof(string.Contains), [typeof(string)]);
    
    public async Task<IEnumerable<CustomerInfoDto>> Handle(CustomerInfoQuery request, CancellationToken cancellationToken)
    {
        var keyword = request.Keyword;
        Expression<Func<CusCustomer, bool>> seed = customer => 1 == 0;
        var queryExpression = customerPropertyExpressions.Aggregate(seed, (expression, propertyExpression) =>
        {
            if (propertyExpression.Body is not MemberExpression memberExpression) return expression;
            var constantExpression = Expression.Constant(keyword, typeof(string));
            var body = Expression.Call(memberExpression, containsMethodInfo!, constantExpression);
            var containsExpression = Expression.Lambda<Func<CusCustomer, bool>>(body, propertyExpression.Parameters);
            return expression.Or(containsExpression);

        });
        
        return await freeSql.Select<CusCustomer>().WithLock()
            .WhereIf(!string.IsNullOrWhiteSpace(request.Keyword), queryExpression)
            .WhereIf(request.IsCooperation.HasValue, customer => customer.IsCooperation == request.IsCooperation)
            .Take(100)
            .ToListAsync(customer => new CustomerInfoDto
            {
                Id = customer.CustomerId,
                CnName = customer.CustomerName,
                EnName = customer.CustomerNameEn,
                IsCooperation = customer.IsCooperation ?? false,
                CustomerCode = customer.CustomerCode,
                FullName = customer.CustomerFullName
            }, cancellationToken);
    }
}