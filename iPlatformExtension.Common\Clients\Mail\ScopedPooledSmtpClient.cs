using iPlatformExtension.Common.ObjectPools.SMTP;
using MailKit;
using MailKit.Net.Smtp;

namespace iPlatformExtension.Common.Clients.Mail;

public sealed class ScopedPooledSmtpClient(IProtocolLogger logger) : SmtpClient(logger)
{
    private int _isExpired;

    internal bool IsExpired
    {
        get => _isExpired == 1;
        set => Interlocked.Exchange(ref _isExpired, value ? 1 : 0);
    }
    
    public ScopedPooledSmtpClient() : this(new NullProtocolLogger())
    {
        
    }

    internal SmtpClientAccessor? Accessor { get; set; }

    protected override void Dispose(bool disposing)
    {
        if (!disposing) return;
        
        if (Accessor is not null)
        {
            Accessor.Dispose();
            Accessor = null;
        }
        else
        {
            Disconnect(true);
            ProtocolLogger.Dispose();
            base.Dispose(false);
        }
    }
}

public sealed class SmtpClientAccessor : IDisposable
{
    private ScopedPooledSmtpClient? _client;

    private SmtpClientPool? _pool;

    public SmtpClient Client => _client ?? throw new ObjectDisposedException(nameof(_client));

    public SmtpClientAccessor(SmtpClientPool pool)
    {
        _pool = pool;
        _client = pool.Get();
        _client.Accessor = this;
    }
    
    public void Dispose()
    {
        if (_client == null) return;
        _pool?.Return(_client);
        _client = null;
        _pool = null;
    }
}