using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Clients.HuaweiObs.Options;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.Send;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using System.Net;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send
{
    /// <summary>
    /// 上传邮件附件命令处理程序
    /// </summary>
    internal sealed class UploadMailAttachmentCommandHandler(
        HuaweiObsClient huaweiObsClient,
        IMailAttachmentsRepository mailAttachmentsRepository,
        IOptionsMonitor<ObsClientOptions> obsOptions,
        IConfiguration configuration,
        IHttpContextAccessor httpContextAccessor) : IRequestHandler<UploadMailAttachmentCommand, UploadMailAttachmentResult>
    {
        public async Task<UploadMailAttachmentResult> Handle(UploadMailAttachmentCommand request, CancellationToken cancellationToken)
        {
            // 获取当前用户ID
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var now = DateTime.Now;

            // 从配置中获取最大文件大小限制，默认为200MB
            const long defaultMaxFileSize = 200 * 1024 * 1024; // 200MB
            var maxFileSize = configuration.GetValue<long>("MailCenter:MaxFileSize", defaultMaxFileSize);

            // 生成附件ID
            var attachmentId = Guid.NewGuid().ToString();

            // 获取文件信息
            var file = request.File;
            var originalFileName = file.FileName;
            var fileExtension = Path.GetExtension(originalFileName);
            var fileName = $"{attachmentId}{fileExtension}";

            // 检查文件大小
            if (file.Length > maxFileSize)
            {
                throw new ApplicationException($"文件大小超过限制，最大允许大小为{maxFileSize / (1024 * 1024)}MB");
            }

            // 确定存储路径
            string folderPath = string.IsNullOrEmpty(request.MailId)
                ? $"attachments/temp/{DateTime.Now:yyyyMMdd}+{Guid.NewGuid()}"
                : $"attachments/{request.MailId}";

            var filePath = $"{folderPath}/{fileName}";

            // 上传文件到华为云OBS
            using var stream = file.OpenReadStream();
            var result = await huaweiObsClient.PutFileAsync(filePath, stream, fileName: originalFileName);

            if (result.StatusCode != HttpStatusCode.OK)
            {
                throw new ApplicationException($"文件上传失败，状态码：{result.StatusCode}");
            }

            // 创建附件记录
            var mailAttachment = new MailAttachments
            {
                AttachmentId = attachmentId,
                Bucket = obsOptions.CurrentValue.DefaultBucketName,
                FileName = fileName,
                RealName = originalFileName,
                FileSize = file.Length,
                Extension = fileExtension,
                InputTime = now,
                ServerPath = folderPath,
                FileType = "2", // 2表示发件附件
                MailId = request.MailId ?? "temp" // 如果没有邮件ID，则使用temp作为临时标记
            };

            // 保存附件记录
            await mailAttachmentsRepository.InsertAsync(mailAttachment, cancellationToken);

            // 生成临时URL
            var fileUrl = huaweiObsClient.GenerateTemporaryUrl(filePath).SignUrl;

            // 返回结果
            return new UploadMailAttachmentResult
            {
                AttachmentId = attachmentId,
                FileName = originalFileName,
                FileSize = file.Length,
                FilePath = filePath,
                FileUrl = fileUrl
            };
        }
    }
}
