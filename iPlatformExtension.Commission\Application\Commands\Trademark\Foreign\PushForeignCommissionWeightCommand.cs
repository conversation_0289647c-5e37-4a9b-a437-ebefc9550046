﻿using iPlatformExtension.Commission.Infrastructure.Models;
using MediatR;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;

/// <summary>
/// 推送出口商标权重
/// </summary>
public sealed record PushForeignCommissionWeightCommand(int Year, int Month, string UserId, IEnumerable<string> DeptIds) 
    : IRequest, IBackgroundTracingCommand
{
    /// <inheritdoc />
    public string TraceParentId { get; set; } = null!;

    /// <inheritdoc />
    public string OperationName { get; set; } = "出口商标权值推送";
}