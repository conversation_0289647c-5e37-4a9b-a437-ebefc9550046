﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Mappers
{
    /// <summary>
    /// 商标作业平台的对象映射
    /// </summary>
    public sealed class FlowMapper : Profile
    {
        /// <summary>
        /// 对象映射配置
        /// </summary>
        public FlowMapper()
        {
            //个人标签映射
            CreateMap<PrivateListDto, SysFlowPrivate>()
                .ForMember((target) => target.Seq, memberOptions: option => option.MapFrom(source => source.Seq))
                .ForMember((target) => target.IsShow, memberOptions: option => option.MapFrom(source => source.IsShow))
                .ForMember((target) => target.PrivateName, memberOptions: option => option.MapFrom(source => source.PrivateName))
                .ForMember((target) => target.PrivateId, memberOptions: option => option.MapFrom(source => source.PrivateId));
        }
    }
}
