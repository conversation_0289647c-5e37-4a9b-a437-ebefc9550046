﻿using Hangfire.Common;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Encryption;
using iPlatformExtension.Common.Interceptor;
using iPlatformExtension.MailService.Applications.Commands;
using iPlatformExtension.MailService.Applications.Queries;
using iPlatformExtension.MailService.Extensions;
using iPlatformExtension.MailService.HostedService;
using iPlatformExtension.MailService.Infrastructure;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.MailCenter;
using MailKit;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using System.Threading;

namespace iPlatformExtension.MailService.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class MailController(
        ILogger<MailController> logger, 
        IConfiguration configuration, IMediator mediator, 
        DefaultRedisCache redisCache, MailTool mailTool,
        IServiceScopeFactory _serviceScopeFactory, 
        IWxWorkClient wxWorkClientExtension
        ) : ControllerBase
    {

        /// <summary>
        ///  测试
        /// </summary>
        /// <returns></returns>
        [HttpGet("test")]
        public async Task<int> testAsync(CancellationToken cancellationToken)
        {

            await wxWorkClientExtension.SentRobotMessageAsync($"邮箱启动失败，确认最近是否有修改密码，请及时处理。", "MailReceive");

            var receiveMaillst = await mediator.Send(new MailHostQuery(false), cancellationToken);
            if (receiveMaillst.Count() == 0)
            {
                return 0;
            }
            else
            {
                return receiveMaillst.Count();
            }
        }

        /// <summary>
        ///  测试2
        /// </summary>
        /// <returns></returns>
        [HttpGet("test2")]
        public async Task<int> test2(CancellationToken cancellationToken)
        {

            throw new ApplicationException("程序错误");
        }



        /// <summary>
        ///  获取同步中邮件
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetMailList")]
        public string GetMailList()
        {
            if (mailTool.TryGetRecord().Count() == 0)
            {
                return "0";
            }
            return string.Join(",", mailTool.TryGetRecord());
        }

        /// <summary>
        ///  公邮接收器
        /// </summary>
        /// <returns></returns>
        [HttpGet("MaiReceive")]
        [RepeatedActionFilter]
        public async Task<IActionResult> MaiReceive(int repeatLimit = 10, CancellationToken cancellationToken = default)
        {
            logger.LogInformation("公共邮件接收器启动");
            try
            {
                var receiveMaillst = await mediator.Send(new MailHostQuery(false), cancellationToken);

                _ = Parallel.ForEachAsync(receiveMaillst, async (host, ct) =>
                {
                    if (mailTool.TryGetRecord().TryAdd(host.Account.ToString(), Thread.CurrentThread.ManagedThreadId.ToString()))
                    {
                        try
                        {
                            using (var scope = _serviceScopeFactory.CreateScope())
                            {
                                var _mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
                                Console.WriteLine($"{host.Account}进入");
                                var receiveConfig = new Applications.Models.MailConfigDto
                                {
                                    HostId = host.HostId,
                                    ImapHost = host.ImapHost,
                                    ImapPort = host.ImapPort,
                                    SenderAccount = host.Account,
                                    SenderPassword = DESHelper.DESDecrypt(host.Password),
                                    ShowName = host.ShowName,
                                    IsPrivate = host.IsPrivate
                                };
                                if (host.ImapHost.Contains("imap"))
                                {
                                    await _mediator.Send(new ReceiveMailCommand(receiveConfig, repeatLimit), ct);
                                }
                                else
                                {
                                    await _mediator.Send(new ReceiveMailByPopCommand(receiveConfig, repeatLimit), ct);
                                }
                            }
                            Thread.Sleep(200);
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, $"{host.Account}邮箱异常");
                        }
                        finally
                        {
                            mailTool.TryRemove(host.Account.ToString());
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "邮件接收异常,请检查程序运行是否正确.");
                throw;
            }
            return Ok("启动成功!");
        }



        /// <summary>
        /// 个人邮件接收器
        /// </summary>
        /// <returns></returns>
        [HttpGet("MaiReceiveByPersonal")]
        [RepeatedActionFilter]
        public async Task<IActionResult> MaiReceiveByPersonal(int repeatLimit = 10, CancellationToken cancellationToken = default)
        {
            logger.LogInformation("个人邮件接收器启动");
            try
            {
                ParallelOptions po = new ParallelOptions
                {
                    MaxDegreeOfParallelism = 5
                };
                var receiveMaillst = await mediator.Send(new MailHostQuery(true));

                _ = Parallel.ForEachAsync(receiveMaillst, po, async (host, ct) =>
                {
                    if (mailTool.TryGetRecord().TryAdd(host.Account.ToString(), Thread.CurrentThread.ManagedThreadId.ToString()))
                    {
                        try
                        {
                            Console.WriteLine($"{host.Account.ToString()}开始获取邮件");
                            using (var scope = _serviceScopeFactory.CreateScope())
                            {
                                var _mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

                                var receiveConfig = new Applications.Models.MailConfigDto
                                {
                                    HostId = host.HostId,
                                    ImapHost = host.ImapHost,
                                    ImapPort = host.ImapPort,
                                    SenderAccount = host.Account,
                                    SenderPassword = DESHelper.DESDecrypt(host.Password),
                                    ShowName = host.ShowName,
                                    IsPrivate = host.IsPrivate
                                };
                                if (host.ImapHost.Contains("imap"))
                                {
                                    await _mediator.Send(new ReceiveMailCommand(receiveConfig, repeatLimit));
                                }
                                else
                                {
                                    await _mediator.Send(new ReceiveMailByPopCommand(receiveConfig, repeatLimit));
                                }
                            }
                            Thread.Sleep(200);
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, $"{host.Account}邮箱异常");
                        }
                        finally
                        {
                            mailTool.TryRemove(host.Account.ToString());
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "邮件接收异常");
                throw;
            }
            return Ok("启动成功!");
        }

        /// <summary>
        ///  邮件重收(测试用)
        /// </summary>
        /// <returns></returns>
        [HttpGet("ReReceiveMail")]
        [RepeatedActionFilter]
        public async Task<IActionResult> ReReceiveMail(string HostId, bool IsPrivate = false, bool IsEnabled = true)
        {
            var receiveMaillst = await mediator.Send(new MailHostQuery(IsPrivate, HostId, IsEnabled));

            var host = receiveMaillst.FirstOrDefault();
            try
            {
                var receiveConfig = new Applications.Models.MailConfigDto
                {
                    HostId = host.HostId,
                    ImapHost = host.ImapHost,
                    ImapPort = host.ImapPort,
                    SenderAccount = host.Account,
                    SenderPassword = DESHelper.DESDecrypt(host.Password),
                    ShowName = host.ShowName,
                };
                if (host.ImapHost.Contains("imap"))
                {
                    await mediator.Send(new ReceiveMailCommand(receiveConfig, 1000));
                }
                else
                {
                    await mediator.Send(new ReceiveMailByPopCommand(receiveConfig, 1000));
                }
                Thread.Sleep(200);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"{host.Account}邮箱异常");
            }
            return Ok("启动成功!");
        }

        /// <summary>
        /// 发送邮件
        /// </summary>
        /// <returns>发送结果</returns>
        [HttpPost("SendMail")]
        [RepeatedActionFilter]
        public async Task<IActionResult> SendMail(CancellationToken cancellationToken = default)
        {
            try
            {
                await mediator.Send(new MailSendQuery(), cancellationToken);
                return Ok("启动发件成功");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "邮件发送异常");
                return StatusCode(500, $"邮件发送异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 快速生成测试数据,使用前,请关闭测试邮箱线上接收
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost("GenerateMailTestData")]
        public async Task<IActionResult> GenerateMailTestData(GenerateMailTestDataQuery query,CancellationToken cancellationToken = default)
        {
            try
            {
                await mediator.Send(query, cancellationToken);
                return Ok("成功");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "异常");
                return StatusCode(500, $"异常：{ex.Message}");
            }
        }

        

    }
}
