﻿using FreeSql;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Common.Db.FreeSQL;

namespace iPlatformExtension.MailCenterRepository.Implement
{
    public class MailConfigFilterRepository(
        IFreeSql<MailCenterFreeSql> fsql,
        UnitOfWorkManage<MailCenterFreeSql> manager)
        : DefaultRepository<MailConfigFilter, string>(fsql, manager), IMailConfigFilterRepository;
}
