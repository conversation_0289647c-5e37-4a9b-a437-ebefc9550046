using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_invoice", DisableSyncStructure = true)]
	public partial class BillInvoice {

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "flow_cur_user_id", StringLength = 50)]
		public string FlowCurUserId { get; set; }

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		[ Column(Name = "flow_update_time")]
		public DateTime? FlowUpdateTime { get; set; }

		[ Column(Name = "invoice_date")]
		public DateTime? InvoiceDate { get; set; }

		[ Column(Name = "invoice_id", StringLength = 50, IsNullable = false)]
		public string InvoiceId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "invoice_no", StringLength = 300)]
		public string InvoiceNo { get; set; }

		[ Column(Name = "invoice_status", StringLength = 50)]
		public string InvoiceStatus { get; set; } = "0";

		[ Column(Name = "invoice_type", StringLength = 50)]
		public string InvoiceType { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "receiver", StringLength = 50)]
		public string Receiver { get; set; }

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		[ Column(Name = "title", StringLength = 100)]
		public string Title { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
