﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class MyProcInfoQueryHandler(
    IFreeSql<PlatformFreeSql> freeSql,
    IBaseCtrlProcRepository baseCtrlProcRepository,
    IBaseCountryRepository countryRepository,
    ICustomerRepository customerRepository,
    ICompanyRepository companyRepository) : IRequestHandler<MyProcInfoQuery, MyProcInfoDto>
{
    public async Task<MyProcInfoDto> Handle(MyProcInfoQuery request, CancellationToken cancellationToken)
    {
        var procId = request.ProcId;
        var dto = await freeSql.Select<CaseProcInfo>(procId).WithLock()
            .ToOneAsync(procInfo => new MyProcInfoDto
            {
                ProcNo = procInfo.ProcNo,
                ProcName = procInfo.CtrlProcId,
                CaseName = procInfo.CaseInfo.CaseName,
                TrademarkClasses = procInfo.CaseInfo.TrademarkClass ?? string.Empty,
                EntrustDate = procInfo.EntrustDate,
                OfficialDeadline = procInfo.LegalDueDate,
                Country = procInfo.CaseInfo.CountryId ?? string.Empty,
                CustomerId = procInfo.CaseInfo.CustomerId,
                Customer = procInfo.CaseInfo.CustomerId,
                ManageCompany = procInfo.CaseInfo.ManageCompany,
                Version = procInfo.Version
            }, cancellationToken);
        
        dto.Country = await countryRepository.GetTextValueAsync(dto.Country) ?? string.Empty;
        dto.ProcName = await baseCtrlProcRepository.GetTextValueAsync(dto.ProcName) ?? string.Empty;
        dto.Customer = await customerRepository.GetTextValueAsync(dto.Customer) ?? string.Empty;
        dto.ManageCompany = await companyRepository.GetTextValueAsync(dto.ManageCompany) ?? string.Empty;
        
        return dto;
    }
}