﻿using iPlatformExtension.Common.ObjectPools.ApiResult;
using iPlatformExtension.Model.Dto;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Common.Extensions;

/// <summary>
/// 
/// </summary>
public static class ObjectPoolExtension
{
    /// <summary>
    /// 添加各类对象池
    /// </summary>
    /// <remarks>对象池适用于引用类型对象的重复利用，从而减少GC</remarks>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddObjectPools(this IServiceCollection services)
    {
        return services.AddStringBuilderPool().AddApiResultPool();
    }
    
    /// <summary>
    /// DI添加<c>ObjectPool&lt;StringBuilder&gt;</c>
    /// <seealso cref="ObjectPoolProvider"/>
    /// <seealso cref="StringBuilderPooledObjectPolicy"/>
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns></returns>
    public static IServiceCollection AddStringBuilderPool(this IServiceCollection services)
    {
        services.TryAddDefaultObjectPoolProvider();

        services.TryAddSingleton(serviceProvider =>
        {
            var provider = serviceProvider.GetRequiredService<ObjectPoolProvider>();
            return provider.CreateStringBuilderPool();
        });

        return services;
    }

    /// <summary>
    /// DI添加<see cref="ApiResultPool{T}"/>
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddApiResultPool(this IServiceCollection services)
    {
        services.TryAddSingleton(typeof(ApiResultPooledObjectPolicy<>));
        services.TryAddSingleton(typeof(ApiResultPool<>));
        return services;
    }
    
    private static void TryAddDefaultObjectPoolProvider(this IServiceCollection services)
    {
        services.TryAddSingleton<ObjectPoolProvider, DefaultObjectPoolProvider>();
    }
}