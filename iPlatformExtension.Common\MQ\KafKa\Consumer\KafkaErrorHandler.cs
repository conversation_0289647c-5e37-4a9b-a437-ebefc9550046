using Confluent.Kafka;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.MQ.KafKa.Consumer
{
    /// <summary>
    /// Kafka错误处理器，负责处理消费过程中的异常情况
    /// </summary>
    public class KafkaErrorHandler
    {
        private readonly ILogger<KafkaErrorHandler>? _logger;
        private readonly int _maxRetryCount;
        private readonly TimeSpan _retryDelay;

        /// <summary>
        /// 初始化Kafka错误处理器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="maxRetryCount">最大重试次数</param>
        /// <param name="retryDelayMilliseconds">重试延迟（毫秒）</param>
        public KafkaErrorHandler(
            ILogger<KafkaErrorHandler>? logger = null,
            int maxRetryCount = 3,
            int retryDelayMilliseconds = 1000)
        {
            _logger = logger;
            _maxRetryCount = maxRetryCount;
            _retryDelay = TimeSpan.FromMilliseconds(retryDelayMilliseconds);
        }

        /// <summary>
        /// 处理消费异常
        /// </summary>
        /// <param name="ex">异常</param>
        /// <param name="topicPartitionOffset">主题分区偏移量</param>
        public void HandleConsumeException(Exception ex, TopicPartitionOffset? topicPartitionOffset)
        {
            string errorMessage = $"消费异常: {ex.Message}";
            if (topicPartitionOffset != null)
            {
                errorMessage += $" 在 {topicPartitionOffset.Topic}, 分区 {topicPartitionOffset.Partition}, 偏移量 {topicPartitionOffset.Offset}";
            }

            _logger?.LogError(ex, errorMessage);
            Console.WriteLine(errorMessage);
        }

        /// <summary>
        /// 处理反序列化异常
        /// </summary>
        /// <param name="ex">异常</param>
        /// <param name="messageValue">消息值</param>
        public void HandleDeserializationException(Exception ex, string messageValue)
        {
            var errorMessage = $" - {DateTime.Now:yyyy-MM-dd HH:mm:ss}【Exception 消息反序列化失败，Value：{messageValue}】 ：{ex.StackTrace?.ToString()}";
            _logger?.LogError(ex, errorMessage);
            Console.WriteLine(errorMessage);
        }

        /// <summary>
        /// 处理偏移量存储异常
        /// </summary>
        /// <param name="ex">异常</param>
        /// <param name="topicPartitionOffset">主题分区偏移量</param>
        public void HandleOffsetStoreException(KafkaException ex, TopicPartitionOffset? topicPartitionOffset)
        {
            string errorMessage = $"存储偏移量异常: {ex.Message}";
            if (topicPartitionOffset != null)
            {
                errorMessage += $" 在 {topicPartitionOffset.Topic}, 分区 {topicPartitionOffset.Partition}, 偏移量 {topicPartitionOffset.Offset}";
            }

            _logger?.LogError(ex, errorMessage);
            Console.WriteLine(errorMessage);
        }

        /// <summary>
        /// 使用重试机制执行操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">操作</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        public async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, CancellationToken cancellationToken)
        {
            int retryCount = 0;
            Exception? lastException = null;

            while (retryCount < _maxRetryCount)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex) when (IsTransientException(ex))
                {
                    lastException = ex;
                    retryCount++;

                    if (retryCount < _maxRetryCount)
                    {
                        _logger?.LogWarning(ex, $"操作失败，将在 {_retryDelay.TotalMilliseconds} 毫秒后重试 ({retryCount}/{_maxRetryCount})。错误: {ex.Message}");
                        await Task.Delay(_retryDelay, cancellationToken);
                    }
                }
            }

            _logger?.LogError(lastException, $"操作在 {_maxRetryCount} 次重试后仍然失败。最后错误: {lastException?.Message}");
            throw new Exception($"操作在 {_maxRetryCount} 次重试后仍然失败", lastException);
        }

        /// <summary>
        /// 判断异常是否为临时异常
        /// </summary>
        /// <param name="ex">异常</param>
        /// <returns>是否为临时异常</returns>
        private bool IsTransientException(Exception ex)
        {
            // 可以根据实际情况添加更多的临时异常类型
            return ex is KafkaException kafkaEx && 
                   (kafkaEx.Error.IsLocalError || 
                    kafkaEx.Error.IsBrokerError || 
                    kafkaEx.Error.IsFatal);
        }
    }
}