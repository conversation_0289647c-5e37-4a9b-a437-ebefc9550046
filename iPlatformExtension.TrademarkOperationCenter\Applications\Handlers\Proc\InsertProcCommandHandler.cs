﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class InsertProcCommandHandler(ICaseProcInfoRepository caseProcInfoRepository) : IRequestHandler<InsertProcCommand>
{

    public async Task Handle(InsertProcCommand request, CancellationToken cancellationToken)
    {
        if (await caseProcInfoRepository.Orm.Select<DeliOtherInfo>(request.ProcId)
                .AnyAsync(info => info.CtrlProcMark == CtrlProcMark.BasicMaterial, cancellationToken))
        {
            var procInfo = await caseProcInfoRepository.GetAsync(request.ProcId, cancellationToken);
            if (procInfo is not null)
            {
                var procInfoNew = new CaseProcInfo
                {
                    ProcId = Guid.NewGuid().ToString().ToUpper(),
                    CaseId = procInfo.CaseId,
                    ProcNo = procInfo.ProcNo, //任务编号
                    CtrlProcId = procInfo.CtrlProcId, //任务类型
                    BusTypeId = procInfo.BusTypeId, //业务类型
                    ProcDeptId = procInfo.ProcDeptId, //承办部门
                    UndertakeUserId = procInfo.UndertakeUserId, //承办人
                    TrackUserId = procInfo.TrackUserId, //跟案人
                    FilingType = procInfo.FilingType, //递交方式               
                    EntrustDate = procInfo.EntrustDate, //委案日期
                    ProcUndertakeMainUserId = procInfo.ProcUndertakeMainUserId, //任务主承办人
                    ProcStatusId = "TCLZ", //任务状态：处理中
                    CtrlProcMark = "Sup", //任务标识：补充材料
                    AllocateDate = DateTime.Now.Date, //配案日
                    LegalDueDate = DateTime.Now.Date.AddMonths(3), //官方期限：创建日+3个自然月
                    CreateUserId = UserIds.Administrator, //创建人：“系统”
                    UpdateUserId = UserIds.Administrator, //更新人：“系统”
                    CreateTime = DateTime.Now, //创建时间
                    UpdateTime = DateTime.Now, //更新时间
                    ReturnDocDate = DateTime.Now.Date.AddDays(29), //返稿期限：创建日+29 自然日
                    SubProcStatusId = "t_untreated", //代理人处理状态：未处理
                    TrademarkNiceClasses = string.Empty,
                    TeamId = procInfo.TeamId,//团队id
                    DeliveryKey = procInfo.DeliveryKey,
                    TrademarkDeliveryAgencyId = procInfo.TrademarkDeliveryAgencyId
                };


                await caseProcInfoRepository.InsertAsync(procInfoNew, cancellationToken);
            }
        }
    }
}