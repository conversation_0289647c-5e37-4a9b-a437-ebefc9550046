using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_oa_class", DisableSyncStructure = true)]
	public partial class BasOaClass {

		[ Column(Name = "class_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ClassId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "name", StringLength = 200)]
		public string Name { get; set; }

	}

}
