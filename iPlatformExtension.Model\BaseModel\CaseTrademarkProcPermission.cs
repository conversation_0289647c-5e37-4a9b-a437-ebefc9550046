using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_trademark_proc_permission", DisableSyncStructure = true)]
	public partial class CaseTrademarkProcPermission {

		[ Column(Name = "permission_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string PermissionId { get; set; }

		[ Column(Name = "case_date")]
		public DateTime? CaseDate { get; set; }

		[ Column(Name = "case_no", StringLength = 50)]
		public string CaseNo { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "permission_address", StringLength = 500)]
		public string PermissionAddress { get; set; }

		[ Column(Name = "permission_begin_date")]
		public DateTime? PermissionBeginDate { get; set; }

		[ Column(Name = "permission_date")]
		public DateTime? PermissionDate { get; set; }

		[ Column(Name = "permission_items", StringLength = 50)]
		public string PermissionItems { get; set; }

		[ Column(Name = "permission_tier", StringLength = 1000)]
		public string PermissionTier { get; set; }

		[ Column(Name = "permission_user", StringLength = 200)]
		public string PermissionUser { get; set; }

		[ Column(Name = "permission_user_id", StringLength = 50)]
		public string PermissionUserId { get; set; }

		[ Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
		public string ProcId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "use_range_id", StringLength = -2)]
		public string UseRangeId { get; set; }

	}

}
