using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_keyword_classify_config", DisableSyncStructure = true)]
	public partial class SysKeywordClassifyConfig {

		[ Column(Name = "classify_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ClassifyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "classify_name", StringLength = 50)]
		public string ClassifyName { get; set; }

		[ Column(Name = "classify_type", StringLength = 50)]
		public string ClassifyType { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
