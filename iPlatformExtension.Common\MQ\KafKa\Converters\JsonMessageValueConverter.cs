using System.Text.Json;
using Confluent.Kafka;

namespace iPlatformExtension.Common.MQ.KafKa.Converters;

public class JsonMessageValueConverter<T> : ISerializer<T>, IDeserializer<T>
{
    private readonly JsonSerializerOptions _serializerOptions;

    public JsonMessageValueConverter(JsonSerializerOptions serializerOptions)
    {
        _serializerOptions = serializerOptions;
    }


    public byte[] Serialize(T data, SerializationContext context)
    {
        return JsonSerializer.SerializeToUtf8Bytes(data, _serializerOptions);
    }

    public T Deserialize(ReadOnlySpan<byte> data, bool isNull, SerializationContext context)
    {
        return JsonSerializer.Deserialize<T>(data, _serializerOptions) ?? throw new JsonException("反序列化失败");
    }
}