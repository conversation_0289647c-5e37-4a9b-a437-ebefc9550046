﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class TrademarkDeliveryInfoRepository 
    : DefaultRepository<TrademarkDeliveryInfo, long>, 
        ITrademarkDeliveryInfoRepository
{
    public TrademarkDeliveryInfoRepository(IFreeSql freeSql, UnitOfWorkManager uowManger) : base(freeSql, uowManger)
    {
    }
}