﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using iPlatformExtension.Common.Interceptor;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers
{
    /// <summary>
    /// 流程控制器
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    [Authorize]
    public class FlowController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// 构造函数依赖注入
        /// </summary>
        /// <param name="mediator">中介者组件</param>
        public FlowController(IMediator mediator)
        {
            _mediator = mediator;
        }


        /// <summary>
        /// 自定义标签数量
        /// </summary>
        /// <param name="flowType"></param>
        /// <returns></returns>
        [HttpGet("MyCasePrivateCount")]
        public Task<IEnumerable<MyCasePrivateCountDto>> MyCasePrivateCount([FromQuery] string flowType, string flowSubType)
        {
            return _mediator.Send(new MyCasePrivateCountQuery(flowType, flowSubType));
        }

        /// <summary>
        /// 自定义标签列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetMyTrademarkCaseList")]
        public Task<IEnumerable<GetMyTrademarkCaseListDto>> GetMyTrademarkCaseList([FromQuery] GetMyTrademarkCaseListQuery query)
        {
            return _mediator.Send(query);
        }

        /// <summary>
        /// 保存自定义标签
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("SaveFlowPrivate")]
        public Task SaveFlowPrivate([FromBody] SaveFlowPrivateCommand command)
        {
            return _mediator.Send(command);
        }

        /// <summary>
        /// 流程归入分类
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("MoveProcess")]
        public Task MoveProcess([FromBody] MoveProcessCommand command)
        {
            return _mediator.Send(command);
        }

        /// <summary>
        /// 我的商标待办/处理中的流程
        /// </summary>
        /// <param name="flowType"></param>
        /// <returns></returns>
        [HttpGet("GetFlowProcess")]
        public Task<PageResult<GetFlowProcessDto>> GetFlowProcess([FromQuery] GetFlowProcessQuery query)
        {
            return _mediator.Send(query);
        }

        /// <summary>
        /// 我的商标已办
        /// </summary>
        /// <param name="flowType"></param>
        /// <returns></returns>
        [HttpGet("GetFlowProcessHistory")]
        public Task<PageResult<GetFlowProcessHistoryDto>> GetFlowProcessHistory([FromQuery] GetFlowProcessHistoryQuery query)
        {
            return _mediator.Send(query);
        }


        /// <summary>
        /// 商标递交流程提交
        /// </summary>
        /// <param name="flowType"></param>
        /// <returns></returns>
        [HttpPost("FlowSubmit")]
        [RepeatedActionFilter]
        public  Task FlowSubmit([FromBody] Applications.Models.Flow.FlowInfo info)
        {
            var user = HttpContext.User;
            var userInfo = new UserBaseInfo(user.GetUserId())
            {
                CnName = user.GetGivenName() ?? string.Empty,
                UserName = user.GetUserName() ?? string.Empty
            };
            return  _mediator.Send(new SubmitFlowProcessCommand(info, userInfo));
        }

        /// <summary>
        /// 批量商标递交流程提交
        /// </summary>
        /// <param name="flowType"></param>
        /// <returns></returns>
        [HttpPost("FlowSubmitByBatch")]
        [RepeatedActionFilter]
        public Task<List<FlowSubmitByBatchDto>> FlowSubmitByBatch([FromBody] Applications.Models.Flow.FlowInfo[] infos)
        {
            var user = HttpContext.User;
            var userInfo = new UserBaseInfo(user.GetUserId())
            {
                CnName = user.GetGivenName() ?? string.Empty,
                UserName = user.GetUserName() ?? string.Empty
            };

            return _mediator.Send(new FlowSubmitByBatchProcessCommand(infos, userInfo));

        }

        /// <summary>
        /// 获取流程信息
        /// </summary>
        /// <param name="flowType"></param>
        /// <returns></returns>
        [HttpGet("GetFlowInfo")]
        public Task<Applications.Models.Flow.GetFlowInfoDto> GetFlowInfo([FromQuery] FlowInfoQuery query)
        {
            return _mediator.Send(query);
        }

        /// <summary>
        /// 获取流程配置
        /// </summary>
        /// <param name="flowType"></param>
        /// <returns></returns>
        [HttpGet("GetFlowConfig")]
        public Task<List<GetFlowConfigDto>> GetFlowConfig([FromQuery] FlowConfigQuery query)
        {
            return _mediator.Send(query);
        }

        /// <summary>
        /// 任务流程历史
        /// (historyID=null时为办理中流程)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetFlowHistory")]
        public Task<GetFlowHistoryDto> GetFlowHistory([FromQuery] FlowHistoryQuery query)
        {
            return _mediator.Send(query);
        }


        /// <summary>
        ///  流程移交
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("FlowHandOver")]
        public Task<bool> FlowHandOver([FromBody] FlowHandOverCommand query)
        {
            return _mediator.Send(query);
        }


        /// <summary>
        ///  流程移交批量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("FlowHandOverByBatch")]
        public Task<List<FlowSubmitByBatchDto>> FlowHandOverByBatch([FromBody] FlowHandOverByBatchCommand query)
        {
            return _mediator.Send(query); 
        }

        /// <summary>
        /// 流程删除
        /// </summary>
        /// <param name="objectId">任务id</param>
        /// <param name="flowId">流程id</param>
        /// <returns>统一公共接口</returns>
        [HttpDelete]
        public Task DeleteAsync(string objectId, string flowId)
        {
            return _mediator.Send(new DeleteFlowCommand(objectId, flowId));
        }
    }
}
