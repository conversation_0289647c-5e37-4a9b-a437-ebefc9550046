﻿using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultDepartmentQueryHandler(IDepartmentInfoRepository departmentInfoRepository)
    : IRequestHandler<FeeResultDepartmentQuery>
{
    public async Task Handle(FeeResultDepartmentQuery request, CancellationToken cancellationToken)
    {
        foreach (var feeListItemDto in request.FeeResults)
        {
            feeListItemDto.UndertakeDepartmentName =
                (await departmentInfoRepository.GetCacheValueAsync(
                    feeListItemDto.UndertakeDepartmentId ?? string.Empty, cancellationToken: cancellationToken))?.FullName ?? string.Empty;
        }
    }
}