using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "com_log_history", DisableSyncStructure = true)]
	public partial class ComLogHistory {

		[ Column(Name = "history_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string HistoryId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "finish_time")]
		public DateTime? FinishTime { get; set; }

		[ Column(Name = "is_current")]
		public bool IsCurrent { get; set; } = true;

		[ Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

		[ Column(Name = "start_time")]
		public DateTime? StartTime { get; set; }

		[ Column(Name = "status", StringLength = 50)]
		public string Status { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
