﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Newtonsoft.Json.Serialization;

namespace iPlatformExtension.Outsourcing.Application.Commands.Proc;

/// <summary>
/// 案件任务JsonPatch文档
/// </summary>
/// <param name="operations">JsonPatch操作集合</param>
public sealed class ProcJsonPatchDocument(List<Operation<ProcPatchDto>> operations) 
    : JsonPatchDocument<ProcPatchDto>(operations, contractResolver), IRequest<BatchUpdateProcItemResult>
{
    private static readonly IContractResolver contractResolver = new CamelCasePropertyNamesContractResolver();

    /// <summary>
    /// 任务id
    /// </summary>
    [Required]
    [Description("任务id")]
    public required string ProcId { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    [Required]
    [Description("版本号")]
    public required int Version { get; set; }

    public void Deconstruct(out string procId, out int version)
    {
        procId = ProcId;
        version = Version;
    }
}