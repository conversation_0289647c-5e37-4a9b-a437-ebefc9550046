﻿using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Infrastructure.Extensions;

/// <summary>
/// 排序扩展方法
/// </summary>
public static class SortExtension
{
    /// <summary>
    /// 是否正序
    /// </summary>
    /// <param name="sortOrder">排序枚举</param>
    /// <returns>是否正序</returns>
    public static bool IsAscending(this SortOrder sortOrder) => sortOrder == SortOrder.Ascending;
}