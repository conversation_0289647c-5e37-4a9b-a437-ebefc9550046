﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;

internal sealed record UpdateDeliveryFilesCommand(IEnumerable<AddDeliFileDto> Files, DeliveryFilesOperation Operation) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;