using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_ctrl_proc_direction", DisableSyncStructure = true)]
	public partial class BasCtrlProcDirection {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50, IsNullable = false)]
		public string CtrlProcId { get; set; }

	}

}
