﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.Dto.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

internal sealed record ManualCompleteCommand(FlowEventMessage EventMessage) : IFreeSqlUnitOfWorkCommand<PlatformFreeSql>, IRequest, IFlowEventRequest
{
    public bool Checked => false;
    
    public string? ObjectId { get; set; }
}