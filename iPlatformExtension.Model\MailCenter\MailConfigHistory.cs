﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_config_history", DisableSyncStructure = true)]
	public partial class MailConfigHistory {

		/// <summary>
		/// 主键
		/// </summary>
		[Column(Name = "mail_config_history_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string MailConfigHistoryId { get; set; }

		/// <summary>
		/// 配置id
		/// </summary>
		[Column(Name = "config_id", StringLength = 50, IsNullable = false)]
		public string ConfigId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime CreateTime { get; set; }

		/// <summary>
		/// 邮件id
		/// </summary>
		[Column(Name = "mail_id", StringLength = 50, IsNullable = false)]
		public string MailId { get; set; }

	}

}
