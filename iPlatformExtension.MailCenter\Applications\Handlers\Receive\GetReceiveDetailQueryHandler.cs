﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 获取收件详情
    /// </summary>
    internal sealed class GetReceiveDetailQueryHandler(IFreeSql<MailCenterFreeSql> freeSql)
        : IRequestHandler<GetReceiveDetailQuery, GetReceiveDetailDto>
    {
        public async Task<GetReceiveDetailDto> Handle(
            GetReceiveDetailQuery request,
            CancellationToken cancellationToken
        )
        {
            var mailUser = await freeSql
                .Select<MailUser>()
                .Where(it => it.MailId == request.MailId)
                .ToListAsync(cancellationToken);

            var mailCc = mailUser
                .Where(it => it.AddressType == "cc")
                .Select(it => it.DisplayName + $"<{it.MailAddress}>")
                .DefaultIfEmpty()
                .Aggregate(
                    (next, current) => string.IsNullOrWhiteSpace(next) ? "" : next + ";" + current
                );

            var mailTo = mailUser
                .Where(it => it.AddressType == "to")
                .Select(it => it.DisplayName + $"<{it.MailAddress}>")
                .DefaultIfEmpty()
                .Aggregate(
                    (next, current) => string.IsNullOrWhiteSpace(next) ? "" : next + ";" + current
                );

            return await freeSql
                .Select<MailReceive>()
                .WithLock()
                .Where(it => it.MailId == request.MailId)
                .FirstAsync(
                    it => new GetReceiveDetailDto(
                        it.MailSubject,
                        it.MailHtmlBody ?? it.MailTextBody,
                        mailTo ?? it.MailTo,
                        it.MailFrom,
                        it.MailDate,
                        it.Status,
                        it.MailPriority,
                        it.MailEmlUrl,
                        mailCc ?? it.MailCc,
                        it.CreateTime
                    ),
                    cancellationToken
                );
        }
    }
}
