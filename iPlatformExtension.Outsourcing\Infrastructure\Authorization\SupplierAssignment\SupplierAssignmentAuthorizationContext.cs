﻿using System.Collections.Frozen;
using System.Text.RegularExpressions;
using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using MediatR;

namespace iPlatformExtension.Outsourcing.Infrastructure.Authorization.SupplierAssignment;

internal class SupplierAssignmentAuthorizationContext(
    IEnumerable<string> caseTypes,
    IEnumerable<string> caseDirections,
    IEnumerable<SysAuthFilter> filters) : 
    INotification, IRequest<(IEnumerable<NotAssignedTrademarkProcDto>?, IEnumerable<NotAssignedTrademarkProcDto>)>, IDisposable
{
    private bool? _wholeQuery;
    
    private bool? _personalQuery;

    public bool Disposed {get; private set;}

    /// <summary>
    /// 查询类型
    /// </summary>
    public QueryType QueryType { get; set; }
    
    public HashSet<string> CaseTypes { get; } = [..caseTypes];
    
    public HashSet<string> CaseDirections { get; } = [..caseDirections];

    public bool CanQueryAll
    {
        get => _wholeQuery ?? false;
        private set
        {
            if (_wholeQuery == null)
            {
                _wholeQuery = value;
            }
            else
            {
                throw new InvalidOperationException("查询范围标识只允许初始化一次");
            }
        }
    }

    public bool CanQueryMine
    {
        get => _personalQuery ?? false;
        private set
        {
            if (_personalQuery == null)
            {
                _personalQuery = value;
            }
            else
            {
                throw new InvalidOperationException("查询范围标识只允许初始化一次");
            }
        }
    }

    /// <summary>
    /// 过滤项
    /// </summary>
    public IReadOnlyDictionary<string, FilterEntry> FilterEntries { get; } = 
        filters.ToFrozenDictionary(
            filter => filter.FilterId, 
            filter => new FilterEntry(filter.FilterType, filter.FilterValue));

    /// <summary>
    /// 待分配查询
    /// </summary>
    public ISelect<CaseProcInfo> PersonalQuery { get; internal init; } = null!;
    
    /// <summary>
    /// 所有未分配查询
    /// </summary>
    public ISelect<CaseProcInfo> WholeQuery { get; internal init; } = null!;

    public ISelect<CaseProcInfo> Query => QueryType switch
    {
        QueryType.Personal => PersonalQuery,
        QueryType.Whole => WholeQuery,
        _ => throw new ArgumentOutOfRangeException(nameof(QueryType), QueryType, "查询类型超出枚举范围")
    };

    /// <summary>
    /// 任务名称id集合
    /// </summary>
    public HashSet<string> CtrlProcIds { get; init; } = null!;

    internal void SetPersonalQuery(KeyValuePair<string, Regex> filterEntry)
    {
        var (filterKey, filterRegex) = filterEntry;
        CanQueryMine = FilterEntries.TryGetValue(filterKey, out var entry) && filterRegex.IsMatch(entry.LogicOperator);
    }

    internal void SetWholeQuery(KeyValuePair<string, Regex> filterEntry)
    {
        var (filterKey, filterRegex) = filterEntry;
        CanQueryAll = FilterEntries.TryGetValue(filterKey, out var entry) && filterRegex.IsMatch(entry.LogicOperator);
    }
    
    public void Deconstruct(
        out HashSet<string> caseTypes, 
        out HashSet<string> caseDirections, 
        out HashSet<string> ctrlProcIds,
        out IReadOnlyDictionary<string, FilterEntry> filters,
        out ISelect<CaseProcInfo> query)
    {
        caseTypes = CaseTypes;
        caseDirections = CaseDirections;
        ctrlProcIds = CtrlProcIds;
        filters = FilterEntries;
        query = Query;
    }

    public void Deconstruct(out IReadOnlyDictionary<string, FilterEntry> filters, out ISelect<CaseProcInfo> query,
        out ISelect<CaseProcInfo> allQuery)
    {
        filters = FilterEntries;
        query = PersonalQuery;
        allQuery = WholeQuery;
    }

    public void Dispose()
    {
        Disposed = true;
        CaseTypes.Clear();
        CaseDirections.Clear();
    }
}