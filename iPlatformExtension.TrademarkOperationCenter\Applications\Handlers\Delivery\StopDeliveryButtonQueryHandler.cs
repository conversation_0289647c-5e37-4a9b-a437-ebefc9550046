﻿using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class StopDeliveryButtonQueryHandler : IRequestHandler<StopDeliveryButtonQuery, bool>
{
    public Task<bool> Handle(StopDeliveryButtonQuery request, CancellationToken cancellationToken)
    {
        bool result;
        var deliveryInfo = request.DeliveryInfo;
        var deliveryStatus = (DeliveryStatus) (deliveryInfo.Status ?? 0);
        switch (deliveryStatus)
        {
            case DeliveryStatus.Delivering:
                result = TimeOnly.FromDateTime(DateTime.Now) <= new TimeOnly(19, 45);
                break;
            case DeliveryStatus.Ordered:
            case DeliveryStatus.Complete:
            case DeliveryStatus.Stopped:
            case DeliveryStatus.Ready:
            default:
                result = false;
                break;
        }

        return Task.FromResult(result);
    }
}