using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_sign_info", DisableSyncStructure = true)]
	public partial class MailSignInfo {

		[ Column(Name = "sign_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string SignId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "host_id", StringLength = 50, IsNullable = false)]
		public string HostId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "sign_cn", StringLength = -2)]
		public string SignCn { get; set; }

		[ Column(Name = "sign_desc", StringLength = 50)]
		public string SignDesc { get; set; }

		[ Column(Name = "sign_en", StringLength = -2)]
		public string SignEn { get; set; }

		[ Column(Name = "sign_jp", StringLength = -2)]
		public string SignJp { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

	}

}
