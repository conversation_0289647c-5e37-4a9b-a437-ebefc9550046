{"nacos": {"ServerAddresses": ["************:8848"], "DefaultTimeOut": 15000, "Namespace": "prod", "ListenInterval": 1000, "ServiceName": "iPlatformExtension.MailService", "UserName": "aciplaw", "Password": "nalawcos2024410", "Ip": "************", "Port": 5078, "Listeners": [{"Optional": true, "DataId": "appsettings.json", "Group": "iPlatformExtension"}, {"Optional": true, "DataId": "clients.json", "Group": "iPlatformExtension.MailCenter"}, {"Optional": true, "DataId": "mqs.json", "Group": "iPlatformExtension.MailCenter"}], "Metadata": {"ASPNETCORE_HTTPS_PORTS": "5078"}}}