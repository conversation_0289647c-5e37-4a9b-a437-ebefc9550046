using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_agency", DisableSyncStructure = true)]
	public partial class BasAgency {

		/// <summary>
		/// 代理机构ID（关联邮箱表时与邮箱表user_id关联）
		/// </summary>
		[ Column(Name = "agency_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string AgencyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 中文地址
		/// </summary>
		[ Column(Name = "address_cn", StringLength = 200)]
		public string AddressCn { get; set; }

		/// <summary>
		/// 英文地址
		/// </summary>
		[ Column(Name = "address_en", StringLength = 500)]
		public string AddressEn { get; set; }

		/// <summary>
		/// 机构代码
		/// </summary>
		[ Column(Name = "agency_code", StringLength = 50)]
		public string AgencyCode { get; set; }

		/// <summary>
		/// 中文名称
		/// </summary>
		[ Column(Name = "agency_name_cn", StringLength = 100)]
		public string AgencyNameCn { get; set; }

		/// <summary>
		/// 代理机构编码
		/// </summary>
		[ Column(Name = "agency_name_code", StringLength = 50)]
		public string AgencyNameCode { get; set; }

		/// <summary>
		/// 英文名称
		/// </summary>
		[ Column(Name = "agency_name_en", StringLength = 500)]
		public string AgencyNameEn { get; set; }

		/// <summary>
		/// 日文名称（页面未使用？）
		/// </summary>
		[ Column(Name = "agency_name_jp", StringLength = 500)]
		public string AgencyNameJp { get; set; }

		/// <summary>
		/// 联系人
		/// </summary>
		[ Column(Name = "contacter", StringLength = 50)]
		public string Contacter { get; set; }

		/// <summary>
		/// 国家（地区）ID
		/// </summary>
		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人（user_id）
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "credit_code", StringLength = 50)]
		public string CreditCode { get; set; }

		/// <summary>
		/// 客户(ID)（未用？）
		/// </summary>
		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		/// <summary>
		/// 邮件
		/// </summary>
		[ Column(Name = "email", StringLength = 100)]
		public string Email { get; set; }

		/// <summary>
		/// 创立时间
		/// </summary>
		[ Column(Name = "estab_day", StringLength = 50)]
		public string EstabDay { get; set; }

		/// <summary>
		/// 传真
		/// </summary>
		[ Column(Name = "fax", StringLength = 100)]
		public string Fax { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		/// <summary>
		/// 是否本地（未用？）
		/// </summary>
		[ Column(Name = "is_local")]
		public bool? IsLocal { get; set; } = false;

		/// <summary>
		/// 法人
		/// </summary>
		[ Column(Name = "legal_person", StringLength = 50)]
		public string LegalPerson { get; set; }

		[ Column(Name = "password", StringLength = 50)]
		public string Password { get; set; }

		/// <summary>
		/// 邮编
		/// </summary>
		[ Column(Name = "post_code", StringLength = 50)]
		public string PostCode { get; set; }

		/// <summary>
		/// 备注（未使用？）
		/// </summary>
		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		/// <summary>
		/// 税率
		/// </summary>
		[ Column(Name = "tax_rate", DbType = "money")]
		public decimal? TaxRate { get; set; }

		/// <summary>
		/// 联系电话
		/// </summary>
		[ Column(Name = "tel", StringLength = 100)]
		public string Tel { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人（ID）
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// （未用？）
		/// </summary>
		[ Column(Name = "url", StringLength = 200)]
		public string Url { get; set; }

		/// <summary>
		/// 权大师递交配置key
		/// </summary>
		[Column(Name = "phoenix_delivery_key")]
		public string PhoenixDeliveryKey { get; set; } = default!;

		/// <summary>
		/// 国内商标递交联系电话
		/// </summary>
		[Column(Name = "trademark_tel")]
		public string TrademarkTel { get; set; } = default!;

		/// <summary>
		/// 国内商标递交联系人
		/// </summary>
		[Column(Name = "trademark_contacter")]
		public string TrademarkContactor { get; set; } = default!;
		
		/// <summary>
		/// 国内商标联系邮箱
		/// </summary>
		[Column(Name = "trademark_mailbox")]
		public string TrademarkMailbox { get; set; } = default!;
	}

}
