﻿using iPlatformExtension.MailCenter.Applications.Commands.MapManage;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.MapManage
{
    /// <summary>
    /// 删除映射
    /// </summary>
    internal sealed class DeleteMapCommandHandler(IMapConfigRepository mapConfigRepository) : IRequestHandler<DeleteMapCommand>
    {
        public async Task Handle(DeleteMapCommand request, CancellationToken cancellationToken)
        {
            await mapConfigRepository.DeleteAsync(request.MapId, cancellationToken);
        }
    }
}

