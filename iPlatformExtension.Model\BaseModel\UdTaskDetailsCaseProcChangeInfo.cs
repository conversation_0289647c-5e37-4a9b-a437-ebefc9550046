using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ud_task_details_case_proc_change_info", DisableSyncStructure = true)]
	public partial class UdTaskDetailsCaseProcChangeInfo {

		[ Column(StringLength = 1000)]
		public string 变更后 { get; set; }

		[ Column(StringLength = 1000)]
		public string 变更类型 { get; set; }

		[ Column(StringLength = 1000)]
		public string 变更前 { get; set; }

		[ Column(StringLength = 100)]
		public string 变更时间 { get; set; }

		[ Column(StringLength = 100)]
		public string 任务编号 { get; set; }

		[ Column(StringLength = 1000)]
		public string 任务变更ID { get; set; }

		[ Column(StringLength = 100)]
		public string 任务名称 { get; set; }

		[ Column(StringLength = 1000)]
		public string 任务ID { get; set; }

		[ Column(StringLength = 100)]
		public string 我方文号 { get; set; }

		[ Column(Name = "after_change", StringLength = 100)]
		public string AfterChange { get; set; }

		[ Column(Name = "before_change", StringLength = 1000)]
		public string BeforeChange { get; set; }

		[ Column(Name = "change_time")]
		public DateTime? ChangeTime { get; set; }

		[ Column(Name = "change_type", StringLength = 100)]
		public string ChangeType { get; set; }

		[ Column(Name = "error_columns", StringLength = 2000)]
		public string ErrorColumns { get; set; }

		[ Column(Name = "error_message", StringLength = -2)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "proc_change_id", StringLength = 100)]
		public string ProcChangeId { get; set; }

		[ Column(Name = "proc_id", StringLength = 100)]
		public string ProcId { get; set; }

		[ Column(Name = "task_id", StringLength = 50)]
		public string TaskId { get; set; }

		[ Column(Name = "ud_create_time", InsertValueSql = "getdate()")]
		public DateTime? UdCreateTime { get; set; }

	}

}
