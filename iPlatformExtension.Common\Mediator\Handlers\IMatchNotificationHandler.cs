﻿using MediatR;

namespace iPlatformExtension.Common.Mediator.Handlers;

public interface IMatchNotificationHandler<in TNotification> : INotificationHandler<TNotification> 
    where TNotification : INotification
{
    protected ValueTask<bool> MatchAsync(TNotification notification, CancellationToken cancellationToken);

    protected Task HandleAsync(TNotification notification, CancellationToken cancellationToken);

    async Task INotificationHandler<TNotification>.Handle(TNotification notification, CancellationToken cancellationToken)
    {
        if (await MatchAsync(notification, cancellationToken))
        {
            await <PERSON>leAsync(notification, cancellationToken);
        }
    }
}