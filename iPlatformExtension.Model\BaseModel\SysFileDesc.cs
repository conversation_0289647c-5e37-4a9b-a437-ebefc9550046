using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_file_desc", DisableSyncStructure = true)]
	public partial class SysFileDesc {

		[ Column(Name = "file_desc_id", StringLength = 50)]
		public string FileDescId { get; set; }

		[ Column(Name = "input_time")]
		public DateTime? InputTime { get; set; }

		[ Column(Name = "input_user_id", StringLength = 50)]
		public string InputUserId { get; set; }

	}

}
