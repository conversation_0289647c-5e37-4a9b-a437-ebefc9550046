﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;

namespace iPlatformExtension.Common.Authentication.BladeAuth;

public sealed class BladeAuthenticatedContext : ResultContext<BladeAuthOptions>
{
    public BladeAuthenticatedContext(HttpContext context, AuthenticationScheme scheme, BladeAuthOptions options) 
        : base(context, scheme, options)
    {
       
    }
    
    public SecurityToken? SecurityToken { get; set; }

    public Exception? Exception { get; set; }

    public string? FailureMessage { get; set; }


    public AuthenticateResult Fail()
    {
        if (Exception is not null)
        {
            Fail(Exception);
        }
        else if (FailureMessage is not null)
        {
            Fail(FailureMessage);
        }
        else
        {
            throw new InvalidOperationException("缺少相关错误参数");
        }
        return Result;
    }
}