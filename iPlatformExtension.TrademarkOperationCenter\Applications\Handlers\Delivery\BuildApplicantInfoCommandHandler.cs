﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

/// <summary>
/// 
/// </summary>
internal sealed class BuildApplicantInfoCommandHandler(
    IDeliveryApplicantRepository deliveryApplicantRepository,
    IMediator mediator)
    : IRequestHandler<BuildApplicantInfoCommand>
{
    /// <summary>
    /// 构建申请人信息
    /// </summary>
    /// <param name="request">任务和递交数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <exception cref="NotFoundException">申请人不存在</exception>
    public async Task Handle(BuildApplicantInfoCommand request, CancellationToken cancellationToken)
    {
        var (procInfo, isFirstTime) = request;

        var applicants = await deliveryApplicantRepository.Orm.Select<CaseApplicantList>()
            .From<CusApplicant, CusAddressList>().WithLock()
            .InnerJoin(
                (caseApplicant, cusApplicant, cusAddress) => caseApplicant.ApplicantId == cusApplicant.ApplicantId)
            .LeftJoin((caseApplicant, cusApplicant, cusAddress) => caseApplicant.AddressId == cusAddress.AddressId)
            .Where((caseApplicant, cusApplicant, cusAddress) => caseApplicant.CaseId == procInfo.CaseId)
            .ToListAsync((caseApplicant, cusApplicant, cusAddress) => new DeliApplicant()
            {
                AddressId = cusAddress.AddressId,
                AddressCn = cusAddress.AddressCn,
                AddressEn = cusAddress.AddressEn,
                AddressType = cusAddress.AddressType,
                ApplicantId = cusApplicant.ApplicantId,
                AddressDetail = cusAddress.AddressDetail,
                ApplicantNameCn = cusApplicant.ApplicantNameCn,
                ApplicantNameEn = cusApplicant.ApplicantNameEn,
                CardNo = cusApplicant.CardNo,
                CardType = cusApplicant.CardType,
                CountryId = cusApplicant.CountryId,
                Postcode = cusAddress.Postcode,
                ProvinceId = cusAddress.ProvinceId,
                CityId = cusAddress.CityId,
                ProcId = procInfo.ProcId,
                TypeId = cusApplicant.TypeId,
                IsRepresent = caseApplicant.IsRepresent,
                IsChineseIdentity = cusApplicant.IsChineseIdentity
            }, cancellationToken);

        await mediator.Publish(new BuildProcApplicantInfoCommand(procInfo, applicants), cancellationToken);
        
        applicants = await deliveryApplicantRepository.InsertAsync(applicants, cancellationToken);

        await mediator.Send(new InitializeDeliveryFilesCommand(procInfo, applicants, isFirstTime), cancellationToken);
    }
}