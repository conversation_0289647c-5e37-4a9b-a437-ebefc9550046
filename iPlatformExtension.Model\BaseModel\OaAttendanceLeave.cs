using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "oa_attendance_leave", DisableSyncStructure = true)]
	public partial class OaAttendanceLeave {

		[ Column(Name = "cur_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CurId { get; set; }

		[ Column(Name = "att_list_id", StringLength = 50)]
		public string AttListId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "duration")]
		public double? Duration { get; set; }

		[ Column(Name = "end_time")]
		public DateTime? EndTime { get; set; }

		[ Column(Name = "is_sell_off")]
		public bool? IsSellOff { get; set; } = false;

		[ Column(Name = "leave_type", StringLength = 50)]
		public string LeaveType { get; set; }

		[ Column(Name = "overtime_id", DbType = "varchar(200)")]
		public string OvertimeId { get; set; }

		[ Column(Name = "relaxation_time")]
		public double? RelaxationTime { get; set; }

		[ Column(Name = "remain")]
		public double? Remain { get; set; }

		[ Column(Name = "start_time")]
		public DateTime? StartTime { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
