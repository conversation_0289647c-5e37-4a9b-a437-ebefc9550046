﻿using iPlatformExtension.Commission.Application.Commands.Patent;
using iPlatformExtension.Commission.Application.Models.Patent;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using Microsoft.Extensions.Options;
using RulesEngine.Models;

namespace iPlatformExtension.Commission.Application.Handlers.Patent;

internal sealed class PushPatentExcludeCommandHandler(
    ILogger<PushPatentExcludeCommandHandler> logger,
    IOptionsMonitor<PatentPushingOptions> optionsMonitor)
    : IRequestHandler<PushPatentExcludeCommand, bool>
{
    public async Task<bool> Handle(PushPatentExcludeCommand request, CancellationToken cancellationToken)
    {
        var pushOptions = optionsMonitor.CurrentValue;
        var workFlows = pushOptions.ExcludeRules;
        var excludeEngine = pushOptions.ExcludeRulesEngine;
        var (billBonusCaseList, commissionWeight) = request;

        return await workFlows.ToAsyncEnumerable().SelectAwait(async workFlow =>
        {

            var listParameter = new RuleParameter("list", billBonusCaseList);
            var weightParameter = new RuleParameter("commissionWeight", commissionWeight);
            var ruleResultList =
                await excludeEngine.ExecuteAllRulesAsync(workFlow.WorkflowName, listParameter, weightParameter);
            
            if (!ruleResultList.All(tree => tree.IsSuccess)) 
                return false;
            
            logger.LogPushingPatentCommissionExclude(workFlow.WorkflowName, billBonusCaseList, commissionWeight);
            return true;

        }).AllAsync(result => result, cancellationToken);
    }
}