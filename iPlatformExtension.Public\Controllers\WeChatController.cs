﻿using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Common.Encryption;
using iPlatformExtension.Public.Applications.Models.WeChat;
using iPlatformExtension.Public.Applications.Queries.SystemDictionary;
using iPlatformExtension.Public.Applications.Queries.WeChat;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Collections;

namespace iPlatformExtension.Public.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class WeChatController(IMediator mediator, IWxWorkClient wxWorkClientExtension) : ControllerBase
    {
        [HttpGet("GetWeiXinPermissionsValidationConfig")]
        public async Task<ValidationConfig> GetWeiXinPermissionsValidationConfig(string url)
        {
            return await mediator.Send(new ValidationConfigQuery(url));
        }

        [HttpGet("GetUserInfoByCode")]
        public async Task<WechatUserInfoDto> GetUserInfoByCodeAsync(string code)
        {
            return await mediator.Send(new GetUserInfoQuery(code));
        }
    }
}
