using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_cus_customer_update", DisableSyncStructure = true)]
	public partial class ACusCustomerUpdate {

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "is_del")]
		public bool? IsDel { get; set; }

		[ Column(Name = "is_do")]
		public bool? IsDo { get; set; } = false;

		[ Column(Name = "merge_id", StringLength = 50)]
		public string MergeId { get; set; }

		[ Column(Name = "new_id", StringLength = 50)]
		public string NewId { get; set; }

		[ Column(Name = "new_name", StringLength = 500)]
		public string NewName { get; set; }

		[ Column(Name = "old_id", StringLength = 50)]
		public string OldId { get; set; }

		[ Column(Name = "old_name", StringLength = 500)]
		public string OldName { get; set; }

	}

}
