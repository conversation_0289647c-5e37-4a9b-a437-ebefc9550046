﻿using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    /// <summary>
    /// 删除处理者
    /// </summary>
    internal sealed class DeleteAnalysisRuleCommandHandler(IMailConfigRepository mailConfigRepository, IMailConfigFilterRepository mailConfigFilterRepository) : IRequestHandler<DeleteAnalysisRuleCommand>
    {
        public async Task Handle(DeleteAnalysisRuleCommand request, CancellationToken cancellationToken)
        {
            await mailConfigRepository.DeleteAsync(request.Id, cancellationToken);
            await mailConfigFilterRepository.DeleteAsync(it => it.ConfigId == request.Id, cancellationToken);
        }
    }
}

