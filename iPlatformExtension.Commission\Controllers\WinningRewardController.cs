﻿using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Net.Mime;
using System.Threading.Channels;
using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Commission.Application.Queries.Authrorization;
using iPlatformExtension.Commission.Application.Queries.WinningReward;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Commission.Controllers;

/// <summary>
/// 胜诉奖励控制器
/// </summary>
[ApiController]
[Route("winning-reward")]
[Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
public sealed class WinningRewardController(ISender mediator) : ControllerBase
{
    /// <summary>
    /// 创建胜诉奖励规则
    /// </summary>
    /// <param name="ruleDto">规则参数</param>
    /// <returns>统一响应接口</returns>
    [HttpPost("rule")]
    [Authorize(Roles = "系统管理员, IT测试员")]
    public Task CreateRuleAsync(RuleDto ruleDto)
    {
        return mediator.Send(new CreateRuleCommand(ruleDto), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 更新胜诉奖励规则
    /// </summary>
    /// <param name="ruleId">胜诉规则id</param>
    /// <param name="ruleDto">胜诉奖励更新信息</param>
    /// <returns></returns>
    [HttpPatch("rule/{ruleId:int}")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    [Authorize(Roles = "系统管理员, IT测试员")]
    public Task UpdateRuleAsync(int ruleId, JsonPatchDocument<RulePatchDto> ruleDto)
    {
        return mediator.Send(new UpdateRuleCommand(ruleId, ruleDto), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 查询任务规则
    /// </summary>
    /// <param name="ctrlProcIds">任务名称id</param>
    /// <returns></returns>
    [HttpGet("rules")]
    public Task<IEnumerable<RuleDisplayDto>> GetRulesAsync([FromQuery] string[] ctrlProcIds)
    {
        return mediator.Send(new WinningRewardRulesQuery(ctrlProcIds), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 获取胜诉奖励规则详情
    /// </summary>
    /// <param name="ruleId">规则id</param>
    /// <returns></returns>
    [HttpGet("rule/{ruleId:int}")]
    public Task<RuleDto> GetRuleAsync(int ruleId)
    {
        return mediator.Send(new WinningRewardRuleQuery(ruleId), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 根据日期分页查询胜诉奖励统计
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="districtCode">地区编码</param>
    /// <param name="keyword">关键字</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页面大小</param>
    /// <returns>胜诉奖励统计</returns>
    [HttpGet]
    public async Task<IEnumerable<RewardStatistics>> GetRewardStatisticsAsync(
        [Required] DateOnly startDate,
        [Required] DateOnly endDate,
        string? districtCode,
        string? keyword,
        int pageIndex,
        int pageSize)
    {
        var deptIds = await mediator.Send(new AuthorizationDeptIdsQuery(), HttpContext.RequestAborted);
        return await mediator.Send(new WinningRewardStatisticsQuery(new DateRange(startDate, endDate), districtCode, deptIds, keyword)
        {
            PageIndex = pageIndex,
            PageSize = pageSize
        }, HttpContext.RequestAborted);
    }

    /// <summary>
    /// 查询用户胜诉奖励明细
    /// </summary>
    /// <param name="userId">用户id</param>
    /// <param name="year">年</param>
    /// <param name="month">月</param>
    /// <param name="procNo">任务编号</param>
    /// <param name="volume">我方文号</param>
    /// <returns>用户胜诉奖励明细</returns>
    [HttpGet("{userId}/{year:int}/{month:int}")]
    [Authorize("国内商标胜诉奖励用户明细")]
    public Task<IEnumerable<RewardUserDetail>> GetUserRewardsAsync(
        [Required] string userId,
        [Required] int year,
        [Required] int month,
        string? procNo,
        string? volume)
    {
        return mediator.Send(new WinningRewardUserDetailQuery(userId, year, month, procNo, volume), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 固化胜诉奖励数据
    /// </summary>
    /// <param name="channel">固化队列</param>
    /// <returns>统一响应接口</returns>
    [HttpPost]
    [AllowAnonymous]
    public ValueTask CreateRewardsAsync([FromServices] Channel<CreateRewardsCommand> channel)
    {
        return channel.Writer.WriteAsync(new CreateRewardsCommand()
        {
            TraceParentId = Activity.Current?.ParentId ?? HttpContext.TraceIdentifier,
        });
    }

    /// <summary>
    /// 手动创建单条胜诉奖励
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <param name="dto">胜诉奖励信息</param>
    /// <returns>统一响应接口</returns>
    [HttpPost("{procId}")]
    public Task CreateUserRewardAsync(string procId, RewardCreateDto dto)
    {
        return mediator.Send(new CreateRewardCommand(procId, dto), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 更新胜诉奖励
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <param name="document">更新信息</param>
    /// <returns>统一响应接口</returns>
    [HttpPatch("{procId}")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    public Task UpdateUserRewardAsync(string procId, JsonPatchDocument<RewardPatchDto> document)
    {
        return mediator.Send(new UpdateRewardCommand(procId, document), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 导出胜诉奖励统计数据
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="districtCode">地区编码</param>
    /// <param name="keyword">查询关键字</param>
    /// <returns>excel文件流</returns>
    [HttpGet("statistics/excel")]
    public async Task<IActionResult> ExportRewardStatisticsAsync(
        [Required] DateOnly startDate,
        [Required] DateOnly endDate,
        string? districtCode,
        string? keyword)
    {
        var deptIds = await mediator.Send(new AuthorizationDeptIdsQuery(), HttpContext.RequestAborted);
        const int pageIndex = 1;
        const int pageSize = 500;
        
        const int currentIndex = 0;
        const string dataIndexName = nameof(currentIndex);
        
        HttpContext.Items[dataIndexName] = currentIndex;
        
        const string downloadName = "国内商标胜诉奖励统计.xlsx";

        var exportOptions =
            new ExcelExportOptions<WinningRewardStatisticsQuery, RewardStatistics>(downloadName,
                TempDirectory.DomesticTrademarkReward)
            {
                OnQueried = (sender, statistics, _) =>
                {
                    if (sender is not ControllerBase controller)
                    {
                        return ValueTask.CompletedTask;
                    }

                    var items = controller.HttpContext.Items;
                    var index = items[dataIndexName] as int? ?? currentIndex;
                    var i = 0;
                    foreach (var data in statistics)
                    {
                        data.SerialNumber += index;
                        ++i;
                    }

                    index += i;
                    items[dataIndexName] = index;

                    return ValueTask.CompletedTask;
                }
            };

        var query = new WinningRewardStatisticsQuery(new DateRange(startDate, endDate), districtCode, deptIds, keyword)
        {
            PageIndex = pageIndex,
            PageSize = pageSize
        };
        
        return await this.PageExportExcelAsync(query, exportOptions, HttpContext.RequestAborted);
    }   
    
        /// <summary>
    /// 导出胜诉奖励明细数据
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="districtCode">地区编码</param>
    /// <param name="keyword">查询关键字</param>
    /// <returns>excel文件流</returns>
    [HttpGet("details/excel")]
    public async Task<IActionResult> ExportRewardDetailsAsync(
        [Required] DateOnly startDate,
        [Required] DateOnly endDate,
        string? districtCode,
        string? keyword)
    {
        var deptIds = await mediator.Send(new AuthorizationDeptIdsQuery(), HttpContext.RequestAborted);
        const int pageIndex = 1;
        const int pageSize = 500;
        
        const int currentIndex = 0;
        const string dataIndexName = nameof(currentIndex);
        
        HttpContext.Items[dataIndexName] = currentIndex;
        
        const string downloadName = "国内商标胜诉奖励明细.xlsx";

        var exportOptions =
            new ExcelExportOptions<WinningRewardDetailQuery, RewardUserDetail>(downloadName,
                TempDirectory.DomesticTrademarkReward)
            {
                OnQueried = (sender, statistics, _) =>
                {
                    if (sender is not ControllerBase controller)
                    {
                        return ValueTask.CompletedTask;
                    }

                    var items = controller.HttpContext.Items;
                    var index = items[dataIndexName] as int? ?? currentIndex;
                    var i = 0;
                    foreach (var data in statistics)
                    {
                        data.SerialNumber += index;
                        ++i;
                    }

                    index += i;
                    items[dataIndexName] = index;

                    return ValueTask.CompletedTask;
                }
            };

        var query = new WinningRewardDetailQuery(new DateRange(startDate, endDate), districtCode, deptIds, keyword)
        {
            PageIndex = pageIndex,
            PageSize = pageSize
        };
        
        return await this.PageExportExcelAsync(query, exportOptions, HttpContext.RequestAborted);
    }

    /// <summary>
    /// 推送国内商标胜诉奖励到提成系统
    /// </summary>
    /// <param name="channel">队列</param>
    /// <param name="year">年份</param>
    /// <param name="month">月份</param>
    [HttpPost("/commission-service/trademark-domestic/rewards/{year:int}/{month:int}")]
    public async ValueTask PushRewardsAsync([FromServices] Channel<PushDomesticRewardCommand> channel, int year, int month)
    {
        var deptIds = await mediator.Send(new AuthorizationDeptIdsQuery(), HttpContext.RequestAborted);
        await channel.Writer.WriteAsync(new PushDomesticRewardCommand(User.GetUserId(), deptIds, year, month));
    }
    
    
}