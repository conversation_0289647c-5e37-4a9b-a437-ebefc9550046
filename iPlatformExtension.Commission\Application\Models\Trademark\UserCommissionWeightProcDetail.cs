﻿namespace iPlatformExtension.Commission.Application.Models.Trademark;

/// <summary>
/// 提成明细
/// </summary>
public sealed class UserCommissionWeightProcDetail
{
    /// <summary>
    /// 任务id
    /// </summary>
    public string ProcId { get; set; }=string.Empty;
    
    /// <summary>
    /// 我方文号
    /// </summary>
    public string Volume { get; set; } = string.Empty;

    /// <summary>
    /// 注册号
    /// </summary>
    public string RegisterNo { get; set; }=string.Empty;

    /// <summary>
    /// 申请号
    /// </summary>
    public string AppNo { get; set; }=string.Empty;

    /// <summary>
    /// 任务编号
    /// </summary>
    public string ProcNo { get; set; } = string.Empty;

    /// <summary>
    /// 商标名称
    /// </summary>
    public string CaseName { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    public string CtrlProcName { get; set; }=string.Empty;

    /// <summary>
    /// 任务权值
    /// </summary>
    public decimal ProcPoint { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    public string CustomerName { get; set; }=string.Empty;

    /// <summary>
    /// 是否大客户
    /// </summary>
    public string BigClient { get; set; } = "否";

    /// <summary>
    /// 任务状态
    /// </summary>
    public string ProcStatus { get; set; } = string.Empty;

    /// <summary>
    /// 任务标识
    /// </summary>
    public string ProcMark { get; set; } = "/";

    /// <summary>
    /// 承办人
    /// </summary>
    public string UndertakerName { get; set; }=string.Empty;

    /// <summary>
    /// 提成生效日
    /// </summary>
    public DateOnly CommissionDate { get; set; }

    /// <summary>
    /// 国际分类
    /// </summary>
    public string TrademarkClasses { get; set; } = string.Empty;

    /// <summary>
    /// 推送状态
    /// </summary>
    public string PushedStatus { get; set; } = "未推送";
}