﻿using iPlatformExtension.Common.Mediator.PipelineBehaviors;

namespace iPlatformExtension.TrademarkDelivery.Infrastructure.Extensions;

public static class ApplicationDependencyInjectionExtension
{
    public static void AddApplicationServices(MediatRServiceConfiguration configuration)
    {
        configuration.RegisterServicesFromAssemblyContaining(typeof(ApplicationDependencyInjectionExtension));
        configuration.AddOpenBehavior(typeof(DefaultFreeSqlUnitOfWorkPipelineBehavior<,>), ServiceLifetime.Scoped);
    }
}