using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_applicant_proxy_no", DisableSyncStructure = true)]
	public partial class CusApplicantProxyNo {

		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		[ Column(Name = "applicant_id", StringLength = 50)]
		public string ApplicantId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		/// <summary>
		/// 总委编号
		/// </summary>
		[ Column(Name = "proxy_no", StringLength = 50)]
		public string ProxyNo { get; set; }

		[ Column(Name = "proxy_no_id", StringLength = 50, IsNullable = false)]
		public string ProxyNoId { get; set; }

		/// <summary>
		/// 总委编号类型
		/// </summary>
		[ Column(Name = "proxy_no_type", StringLength = 50)]
		public string ProxyNoType { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
