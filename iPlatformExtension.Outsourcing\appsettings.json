{"nacos": {"ServerAddresses": ["************:8848"], "DefaultTimeOut": 15000, "Namespace": "prod", "ListenInterval": 1000, "ServiceName": "iplatform-outsourcing", "UserName": "aciplaw", "Password": "nalawcos2024410", "Listeners": [{"Optional": true, "DataId": "appsettings.json", "Group": "iPlatformExtension"}, {"Optional": true, "DataId": "kafka.json", "Group": "iPlatformExtension"}], "Metadata": {"ASPNETCORE_HTTPS_PORTS": "7162", "gRPC_port": "7162"}}, "iplatform-outsourcing": {"Brokers": ["************:9092"], "Consumers": [{"GroupId": "iplatform-outsourcing", "TopicInfos": [{"TopicName": "Release.acip_iplatform_1009.dbo.case_proc_info"}]}]}}