﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Helper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;
using MediatR;
using Microsoft.AspNetCore.Http.HttpResults;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 编辑客户
    /// </summary>
    public class EditCustomerCommandHandler(IFreeSql freeSql, ISysTeamCustomerRepository sysTeamCustomerRepository, 
        IHttpContextAccessor content,ICustomerRepository customerRepository) : IRequestHandler<EditCustomerCommand>
    {
        public async Task Handle(EditCustomerCommand request, CancellationToken cancellationToken)
        {
            var sysTeamCustomers = await sysTeamCustomerRepository.Where(it => it.TeamId == request.TeamId).ToListAsync(cancellationToken);
            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userId);
            var team = await freeSql.Select<SysTeam>().WithLock().Where(it => it.TeamId == request.TeamId).FirstAsync(cancellationToken);
            if (team == null)
            {
                ArgumentNullException.ThrowIfNull(team);
            }

            if (team is { IsEffect: true, IsExclusive: true } && (request.CustomerId == null || request.CustomerId?.Count == 0))
            {
                throw new ApplicationException("请补充关联客户");
            }
            //不存在删除
            sysTeamCustomers.ForEach((sysTeamCustomer) =>
            {
                if (!request.CustomerId.Any(it => it == sysTeamCustomer.CustomerId))
                    sysTeamCustomerRepository.Delete(sysTeamCustomer);
            });

            var insertList = new List<SysTeamCustomer>();
            request.CustomerId?.ForEach((customerId) =>
            {
                if (sysTeamCustomers.FirstOrDefault(it => it.CustomerId == customerId) == null)
                {
                    insertList.Add(new SysTeamCustomer
                    {
                        SysTeamCustomerId = TimestampAndRandom.GenerateId(),
                        CreateTime = DateTime.Now,
                        CreateUserId = userId,
                        CustomerId = customerId,
                        TeamId = request.TeamId,
                        CustomerName = (customerRepository.GetCacheValueAsync(customerId, cancellationToken: cancellationToken)).GetAwaiter().GetResult()?.CustomerName ?? string.Empty
                    });
                }
            });
            await sysTeamCustomerRepository.InsertAsync(insertList, cancellationToken);
        }
    }
}

