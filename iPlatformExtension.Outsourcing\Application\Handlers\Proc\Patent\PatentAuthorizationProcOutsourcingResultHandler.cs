﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc.Patent;

internal sealed class PatentAuthorizationProcOutsourcingResultHandler(
    IHttpContextAccessor httpContextAccessor) : IProcOutsourcingResultHandler
{
    public ValueTask<bool> MatchAsync(ProcOutsourcingResultNotification notification, CancellationToken cancellationToken)
    {
        var result = notification.Result;
        return ValueTask.FromResult(CaseType.IsBelongPatent(result.CaseType));
    }

    public Task HandleAsync(ProcOutsourcingResultNotification notification, CancellationToken cancellationToken)
    {
        var user = httpContextAccessor.HttpContext?.User ?? throw new NotAuthenticatedException();
        var result = notification.Result;

        result.Editable = user.IsInRole("专利流程人员");
        return Task.CompletedTask;
    }
}