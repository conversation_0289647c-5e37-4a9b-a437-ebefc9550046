using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_dept_change", DisableSyncStructure = true)]
	public partial class RpDeptChange {

		[ Column(Name = "cn_name", StringLength = 50, IsNullable = false)]
		public string CnName { get; set; }

		[ Column(Name = "dept_id", StringLength = 50)]
		public string DeptId { get; set; }

		[ Column(Name = "rp_dept_id", StringLength = 50, IsNullable = false)]
		public string RpDeptId { get; set; }

		[ Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

	}

}
