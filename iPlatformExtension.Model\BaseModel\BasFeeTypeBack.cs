using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_fee_type_back", DisableSyncStructure = true)]
	public partial class BasFeeTypeBack {

		[ Column(Name = "apply_type_id", StringLength = 1000)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "basic_fee", DbType = "money")]
		public decimal? BasicFee { get; set; }

		[ Column(Name = "basic_fee_micro", DbType = "money")]
		public decimal? BasicFeeMicro { get; set; }

		[ Column(Name = "basic_fee_small", DbType = "money")]
		public decimal? BasicFeeSmall { get; set; }

		[ Column(Name = "case_type_id", StringLength = 500)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "country_id", StringLength = 1000)]
		public string CountryId { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		[ Column(Name = "fee_code", StringLength = 50)]
		public string FeeCode { get; set; }

		[ Column(Name = "fee_name_id", StringLength = 50)]
		public string FeeNameId { get; set; }

		[ Column(Name = "fee_type_id", StringLength = 50, IsNullable = false)]
		public string FeeTypeId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "is_fee_reduce")]
		public bool? IsFeeReduce { get; set; }

		[ Column(Name = "is_item_fee")]
		public bool? IsItemFee { get; set; }

		[ Column(Name = "item_end", StringLength = 50)]
		public string ItemEnd { get; set; }

		[ Column(Name = "item_exceed")]
		public short? ItemExceed { get; set; }

		[ Column(Name = "item_fee", DbType = "money")]
		public decimal? ItemFee { get; set; }

		[ Column(Name = "item_fee_micro", DbType = "money")]
		public decimal? ItemFeeMicro { get; set; }

		[ Column(Name = "item_fee_small", DbType = "money")]
		public decimal? ItemFeeSmall { get; set; }

		[ Column(Name = "item_id", StringLength = 50)]
		public string ItemId { get; set; }

		[ Column(Name = "item_name", StringLength = 50)]
		public string ItemName { get; set; }

		[ Column(Name = "item_start", StringLength = 50)]
		public string ItemStart { get; set; }

		[ Column(Name = "item_type", StringLength = 50)]
		public string ItemType { get; set; }

		[ Column(Name = "office_name", StringLength = 1000)]
		public string OfficeName { get; set; }

		[ Column(Name = "pay_office", StringLength = 50)]
		public string PayOffice { get; set; }

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		[ Column(Name = "text_oa", StringLength = 50)]
		public string TextOa { get; set; }

		[ Column(Name = "text_office", StringLength = 300)]
		public string TextOffice { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
