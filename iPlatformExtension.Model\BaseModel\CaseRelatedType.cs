﻿using System.ComponentModel;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 相关案类型
/// </summary>
public enum CaseRelatedType
{
    /// <summary>
    /// 1+1套案
    /// </summary>
    [Description("same_day")]
    SameDay,
    
    /// <summary>
    /// 同日递交
    /// </summary>
    [Description("same_submit")]
    SameSubmit,
    
    /// <summary>
    /// 其他家族案
    /// </summary>
    [Description("family_other")]
    FamilyOther,
    
    /// <summary>
    /// 新案重提
    /// </summary>
    [Description("case_resubmit")]
    CaseResubmit,
    
    /// <summary>
    /// 其他相关
    /// </summary>
    [Description("other")]
    Other
}