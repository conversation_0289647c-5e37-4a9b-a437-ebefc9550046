﻿using iPlatformExtension.Model.BaseModel;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;

/// <summary>
/// 发送策略
/// </summary>
public interface ISendPolicy
{

    /// <summary>
    /// 策略匹配
    /// </summary>
    /// <param name="deliveryInfo">递交数据</param>
    /// <returns>匹配返回true，否则返回false</returns>
    public ValueTask<bool> MatchPolicyAsync(DeliInfo deliveryInfo);

    /// <summary>
    /// 处理器类型
    /// </summary>
    public Type HandlerType { get; }

    /// <summary>
    /// 发起递交消息命令
    /// </summary>
    public string StartupDeliveryMessageKey { get; }

    /// <summary>
    /// 中止递交消息命令
    /// </summary>
    public string StopDeliveryMessageKey { get; }

    /// <summary>
    /// 撤回递交消息命令
    /// </summary>
    public string WithdrawDeliveryMessageKey { get; }

    /// <summary>
    /// 取消递交消息命令
    /// </summary>
    public string CancelDeliveryMessageKey { get; }
}

/// <summary>
/// 发送策略
/// </summary>
/// <typeparam name="THandler">发送处理者类型</typeparam>
public class SendPolicy<THandler> : ISendPolicy where THandler : ISendDeliveryCommandHandler
{
    /// <summary>
    ///
    /// </summary>
    protected readonly IOptions<SendPolicyOptions<THandler>> _options;

    /// <summary>
    /// 构造函数
    /// </summary>
    public SendPolicy(IOptions<SendPolicyOptions<THandler>> options)
    {
        _options = options;
    }

    /// <inheritdoc />
    public virtual ValueTask<bool> MatchPolicyAsync(DeliInfo deliveryInfo)
    {
        var options = _options.Value;
        return new ValueTask<bool>(options.PolicyName == deliveryInfo.CtrlProcId);
    }

    /// <summary>
    /// 处理器类型
    /// </summary>
    public Type HandlerType => _options.Value.HandlerType;

    /// <summary>
    /// 发起递交消息命令
    /// </summary>
    public string StartupDeliveryMessageKey => _options.Value.StartupDeliveryMessageKey;

    /// <summary>
    /// 中止递交消息命令
    /// </summary>
    public string StopDeliveryMessageKey => _options.Value.StopDeliveryMessageKey;

    /// <summary>
    /// 撤回递交消息命令
    /// </summary>
    public string WithdrawDeliveryMessageKey => _options.Value.WithdrawDeliveryMessageKey;

    /// <summary>
    /// 取消递交消息命令
    /// </summary>
    public string CancelDeliveryMessageKey => _options.Value.CancelDeliveryMessageKey;
}