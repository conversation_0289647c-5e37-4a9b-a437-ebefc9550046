using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "file_list_a_bak", DisableSyncStructure = true)]
	public partial class FileListABak {

		[ Column(Name = "file_name", StringLength = 100)]
		public string FileName { get; set; }

		[ Column(Name = "file_size")]
		public long? FileSize { get; set; }

		[ Column(Name = "id", IsIdentity = true)]
		public int Id { get; set; }

		[ Column(Name = "import_remark", StringLength = 50)]
		public string ImportRemark { get; set; }

		[ Column(Name = "input_time")]
		public DateTime? InputTime { get; set; }

		[ Column(Name = "instance_id")]
		public int? InstanceId { get; set; }

		[ Column(Name = "real_name", StringLength = 200)]
		public string RealName { get; set; }

		[ Column(Name = "server_path")]
		public string ServerPath { get; set; }

	}

}
