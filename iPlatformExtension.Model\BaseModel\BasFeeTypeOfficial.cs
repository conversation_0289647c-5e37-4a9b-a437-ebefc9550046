using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_fee_type_official", DisableSyncStructure = true)]
	public partial class BasFeeTypeOfficial {

		[ Column(Name = "amount", StringLength = 50)]
		public string Amount { get; set; }

		[ Column(Name = "apply_type", StringLength = 500)]
		public string ApplyType { get; set; }

		[ Column(Name = "fee_code", StringLength = 50)]
		public string FeeCode { get; set; }

		[ Column(Name = "fee_name", StringLength = 50)]
		public string FeeName { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

	}

}
