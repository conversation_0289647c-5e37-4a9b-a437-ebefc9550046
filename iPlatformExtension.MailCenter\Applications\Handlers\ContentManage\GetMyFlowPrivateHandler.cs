﻿
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MailKit.Search;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Server.IISIntegration;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class GetMyFlowPrivateHandler(IFreeSql<MailCenterFreeSql> mySql, IHttpContextAccessor httpContextAccessor, IMailFlowPrivateRepository mailFlowPrivateRepository) : IRequestHandler<GetMyFlowPrivateQuery, List<MailFlowPrivate>>
    {
        public async Task<List<MailFlowPrivate>> Handle(GetMyFlowPrivateQuery request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var lst = await mailFlowPrivateRepository.Where(o => o.UserId == userId)
                .WhereIf(!string.IsNullOrEmpty(request.MailType), o => o.MailType == request.MailType)
                .Count(out long totalCount)
                .OrderBy(o => o.Sort).OrderBy(o => o.CreateTime)
                .ToListAsync(cancellationToken);
            return lst;
        }
    }
}
