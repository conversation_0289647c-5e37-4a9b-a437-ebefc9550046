﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;

namespace iPlatformExtension.MailCenterRepository.Implement;

internal class MailReceiveRepository(
IFreeSql<MailCenterFreeSql> fsql,
UnitOfWorkManage<MailCenterFreeSql> manager)
: DefaultRepository<MailReceive, string>(fsql, manager), IMailReceiveRepository;
