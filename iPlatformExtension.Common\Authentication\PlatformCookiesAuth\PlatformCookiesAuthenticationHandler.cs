using System.Security.Cryptography;
using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Authentication.PlatformCookiesAuth;

public sealed class PlatformCookiesAuthenticationHandler(
    IOptionsMonitor<PlatformCookiesAuthOptions> options,
    ILoggerFactory logger,
    UrlEncoder encoder)
    : AuthenticationHandler<PlatformCookiesAuthOptions>(options, logger, encoder)
{
    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var aes = Aes.Create();
        aes.CreateDecryptor();
        throw new NotImplementedException();
    }
}