﻿using iPlatformExtension.Common.MQ.KafKa.Consumer;
using iPlatformExtension.MailCenter.Applications.Models.Count;
using iPlatformExtension.MailCenter.Applications.Queries.Count;
using iPlatformExtension.Model.MailCenter;
using KafkaProvider;
using MediatR;

namespace iPlatformExtension.MailCenter.HostedService
{
    public class KafkaFlowRecordCountService(IKafkaService kafkaService, IConfiguration configuration, IHostEnvironment environment, IMediator mediator) : BackgroundService
    {
        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            kafkaService.GetConsumerConfig(configuration.GetSection("KafKa:MailCenterConsumer:BootstrapServers").Value ?? throw new ArgumentNullException("kafka连接字符串为空"), environment.EnvironmentName);
            kafkaService.SubscribeAsync([configuration.GetSection("KafKa:MailCenterConsumer:FlowRecord").Value ?? 
            throw new ArgumentNullException("kafka连接字符串为空")], 
            (CdcModel<FlowRecord> c) => mediator.Send(new KafkaFlowRecordCountQuery(c), stoppingToken).GetAwaiter().GetResult(),
                stoppingToken);
            return Task.CompletedTask;
        }
    }
}
