﻿using System.Net.Mime;
using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Finance.Applications.Commands.Proc;
using iPlatformExtension.Finance.Applications.Models.Proc;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Finance.Controllers;

/// <summary>
/// 任务控制器
/// </summary>
[ApiController]
[Route("[controller]")]
public sealed class ProcController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// 批量补丁更新案件任务信息
    /// </summary>
    /// <param name="documents">案件任务补丁文档集合</param>
    /// <returns>更新失败的结果</returns>
    [HttpPatch("/procInfos")]
    [Authorize(AuthenticationSchemes = BladeAuthOptions.SchemeName)]
    [Consumes(MediaTypeNames.Application.JsonPatch, MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    public Task<IEnumerable<UpdateCaseProcResult>> UpdateAsync(List<CaseProcJsonPatchDocument> documents) =>
        mediator.Send(new UpdateCaseProcInfosCommand(documents), HttpContext.RequestAborted);
}