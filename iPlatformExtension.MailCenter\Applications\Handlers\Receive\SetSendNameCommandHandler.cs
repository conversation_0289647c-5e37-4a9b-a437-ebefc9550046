﻿using iPlatformExtension.MailCenter.Applications.Commands.Receive;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 设置发件名称命令处理器
    /// </summary>
    internal sealed class SetSendNameCommandHandler(IMailReceiveFlowRepository mailReceiveFlowRepository,IMailSendFlowRepository mailSendFlowRepository) : IRequestHandler<SetSendNameCommand>
    {
        public async Task Handle(SetSendNameCommand request, CancellationToken cancellationToken)
        {
            switch (request.MailActionType)
            {
                case "Send":
                    {
                        var mailSendFlows = await mailSendFlowRepository.Where(it => it.MailId == request.MailId).ToListAsync(cancellationToken);
                        if (mailSendFlows.Count != 1)
                        {
                            throw new ArgumentException($"{request.MailId}流程参数有误,请找管理员咨询");
                        }
                        var mailSendFlow = mailSendFlows.First();
                        mailSendFlow.DisplayName = request.SendName ?? "";
                        await mailSendFlowRepository.UpdateAsync(mailSendFlow, cancellationToken);
                        return;
                    }

                default:
                    {
                        var mailReceiveFlows = await mailReceiveFlowRepository.Where(it => it.MailId == request.MailId).ToListAsync(cancellationToken);
                        if (mailReceiveFlows.Count != 1)
                        {
                            throw new ArgumentException($"{request.MailId}流程参数有误,请找管理员咨询");
                        }
                        var mailReceiveFlow = mailReceiveFlows.First();
                        mailReceiveFlow.SendName = request.SendName ?? "";
                        await mailReceiveFlowRepository.UpdateAsync(mailReceiveFlow, cancellationToken);
                        break;
                    }
            }

        }
    }
}

