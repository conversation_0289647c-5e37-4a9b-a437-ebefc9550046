﻿using System.Text.Json;
using System.Text.Json.Serialization;
using iPlatformExtension.Model.Dto.Proc;

namespace iPlatformExtension.Common.Converters;

public sealed class ProcExtensionKeyConverter : JsonConverter<ProcExtensionKey>
{
    public override ProcExtensionKey Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        return JsonSerializer.Deserialize<ProcExtensionKey>(ref reader, options);
    }

    public override void Write(Utf8JsonWriter writer, ProcExtensionKey value, JsonSerializerOptions options)
    {
        JsonSerializer.Serialize(writer, value, options);
    }

    public override void WriteAsPropertyName(Utf8JsonWriter writer, ProcExtensionKey value, JsonSerializerOptions options)
    {
        writer.WritePropertyName(value.Key);
    }

    public override ProcExtensionKey ReadAsPropertyName(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var key = reader.GetString();
        return new ProcExtensionKey(key ?? throw new JsonException($"反序列化[{nameof(ProcExtensionKey)}]无法读取字符串"));
    }
}