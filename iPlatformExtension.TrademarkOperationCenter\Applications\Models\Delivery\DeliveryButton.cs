﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 递交操作按钮
/// </summary>
public enum DeliveryButton
{
    /// <summary>
    /// 启动递交
    /// </summary>
    StartUpDelivery = 0,
    
    /// <summary>
    /// 中止递交
    /// </summary>
    StopDelivery = 1,
    
    /// <summary>
    /// 撤回已递交
    /// </summary>
    WithdrawDelivery = 2,

    /// <summary>
    /// 确认无误
    /// </summary>
    ConfirmDelivery = 3,
    
    /// <summary>
    /// 取消递交
    /// </summary>
    CancelDelivery = 4
}