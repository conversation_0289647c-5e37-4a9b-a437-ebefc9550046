namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 
/// </summary>
public class FeeStatusDtoEqualityComparer : IEqualityComparer<FeeStatusDto>
{
    /// <summary>
    /// 判断两个<see cref="FeeStatusDto"/>是否相等
    /// </summary>
    /// <param name="x"></param>
    /// <param name="y"></param>
    /// <returns></returns>
    public bool Equals(FeeStatusDto? x, FeeStatusDto? y)
    {
        ArgumentNullException.ThrowIfNull(x);
        ArgumentNullException.ThrowIfNull(y);

        return x.BenchFeeId == y.BenchFeeId && x.Id == y.Id;
    }

    /// <summary>
    /// 获取<see cref="FeeStatusDto"/>的哈希值
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public int GetHashCode(FeeStatusDto obj)
    {
        ArgumentNullException.ThrowIfNull(obj);

        if (string.IsNullOrWhiteSpace(obj.Id))
        {
            return obj.BenchFeeId.GetHashCode();
        }

        return obj.BenchFeeId.GetHashCode() ^ obj.Id.GetHashCode();
    }
}