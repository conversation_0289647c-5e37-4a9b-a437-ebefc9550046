using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_gz_yesterday_feishenqin", DisableSyncStructure = true)]
	public partial class VwGzYesterdayFeishenqin {

		[ Column(StringLength = 500)]
		public string 案件名称 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 承办人 { get; set; }

		[ Column(StringLength = 200, IsNullable = false)]
		public string 承办人邮箱 { get; set; }

		[ Column(StringLength = 10, IsNullable = false)]
		public string 官方期限 { get; set; }

		[ Column(StringLength = 4000, IsNullable = false)]
		public string 任务备注 { get; set; }

		[ Column(StringLength = 50)]
		public string 任务创建人 { get; set; }

		[ Column(StringLength = 50)]
		public string 任务名称 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 任务属性 { get; set; }

		[ Column(StringLength = 50)]
		public string 申请号 { get; set; }

		[ Column(StringLength = -2)]
		public string 申请人 { get; set; }

		[ Column(StringLength = 50)]
		public string 我方案号 { get; set; }

	}

}
