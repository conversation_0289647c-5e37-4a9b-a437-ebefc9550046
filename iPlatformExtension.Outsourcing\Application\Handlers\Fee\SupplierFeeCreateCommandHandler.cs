﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Commands.Fee;
using iPlatformExtension.Outsourcing.Application.Models.Fee;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Fee;

internal sealed class SupplierFeeCreateCommandHandler(
    IFreeSql<PlatformFreeSql> freeSql,
    ISystemDictionaryRepository systemDictionaryRepository,
    IBaseFeeTypeNameRepository baseFeeTypeNameRepository,
    IForeignSupplierFeeRepository foreignSupplierFeeRepository) : IRequestHandler<SupplierFeeCreateCommand, SupplierFeesDto>
{
    public async Task<SupplierFeesDto> Handle(SupplierFeeCreateCommand request, CancellationToken cancellationToken)
    {
        var (ctrlProcId, list) = request;
        var feeNameId = await freeSql.Select<FeeNameCtrlProc>()
            .InnerJoin<BasCtrlProc>((proc, ctrlProc) => proc.CtrlProcId == ctrlProc.CtrlProcId)
            .InnerJoin<BasFeeTypeName>((proc, name) => proc.FeeNameId == name.NameId)
            .Where(x => x.CtrlProcId == ctrlProcId)
            .Where<BasFeeTypeName>(name => name.IsEnabled)
            .Where<BasCtrlProc>(ctrlProc => ctrlProc.IsEnabled == true)
            .FirstAsync(proc => proc.FeeNameId, cancellationToken);

        if (feeNameId is null)
        {
            throw new NotFoundException(ctrlProcId, "有效任务名称关联的费项名称配置");
        }

        var feeNameInfo = await baseFeeTypeNameRepository.GetAsync(feeNameId, cancellationToken);
        if (feeNameInfo is null)
        {
            throw new NotFoundException(feeNameId, "费项名称");
        }
        
        var feeClasses = feeNameInfo.FeeClass.Split(";", StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries);
        
        var feesId = Guid.CreateVersion7(DateTimeOffset.Now);
        var fees = list.Where(x =>
        {
            if (feeClasses.Contains(x.FeeClass))
            {
                return true;
            }

            throw new ApplicationException("费项规则配置缺失，请联系系统管理员！");
        }).Select(x => new CaseFeeList
        {
            FeeId = Guid.CreateVersion7(DateTimeOffset.Now).ToString(),
            FeeTypeNameId = feeNameId,
            FeeClass = x.FeeClass,
            Amount = x.FeeAmount,
            IsAuto = false,
            CurrencyId = "CNY",
            StandardAmount = x.FeeAmount,
            IsEnabled = true,
            IsEdit = true,
            IsFirstPayAnnual = false,
            CreateTime = DateTime.Now,
            CreateUserId = UserIds.Administrator,
            UpdateTime = DateTime.Now,
            UpdateUserId = UserIds.Administrator,
            IsRequest = true
        }).ToList();

        if (await foreignSupplierFeeRepository.AddAsync(feesId.ToString(), fees))
        {
            return new SupplierFeesDto() 
            { 
                FeesId = feesId, 
                SupplierFees = await fees.ToAsyncEnumerable().SelectAwait(async feeList => new SupplierFeeDto
                {
                    FeeClass = await systemDictionaryRepository.GetChineseValueAsync(SystemDictionaryName.FeeType, feeList.FeeClass),
                    FeeName = feeNameInfo.NameZhCn,
                    FeeAmount = feeList.Amount ?? 0,
                    Currency = feeList.CurrencyId
                }).ToListAsync(cancellationToken) 
            };
        }

        throw new ApplicationException("费项生成失败，请重新打开页面");
    }
}