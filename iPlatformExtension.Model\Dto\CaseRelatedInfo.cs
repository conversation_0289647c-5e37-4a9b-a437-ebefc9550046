using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 相关类型案件
/// </summary>
public class CaseRelatedInfo
{
    /// <summary>
    /// 我方文号
    /// </summary>
    [JsonPropertyName("relateCaseCode")]
    public string Volume { get; set; }

    /// <summary>
    /// 申请号
    /// </summary>
    
    public string ApplyNo { get; set; }

    /// <summary>
    /// 国家号
    /// </summary>
    public string CountryId { get; set; }

    /// <summary>
    /// 申请类型ID
    /// </summary>
    public string ApplyTypeId { get; set; }

    /// <summary>
    /// 案件名称
    /// </summary>
    [JsonPropertyName("relateCaseName")]
    public string CaseName { get; set; }

    /// <summary>
    /// 同日
    /// </summary>
    public string SameSubmitId { get; set; }

    /// <summary>
    /// 套案
    /// </summary>
    public string SameDayId { get; set; }

    /// <summary>
    /// 是否套案
    /// </summary>
    [JsonPropertyName("ifPackageCase")]
    public bool IsSetCases => !string.IsNullOrWhiteSpace(SameDayId);

    /// <summary>
    /// 是否同日
    /// </summary>
    [JsonPropertyName("ifSameDayCase")]
    public bool IsSameDay => !string.IsNullOrWhiteSpace(SameSubmitId);

    /// <summary>
    /// 其他归类
    /// </summary>
    [JsonPropertyName("ifFamilyCase")]
    public bool IsOther => !(IsSameDay || IsSetCases);
}