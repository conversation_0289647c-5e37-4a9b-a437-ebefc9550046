﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{

    /// <summary>
    /// 节假日
    /// </summary>
    [Table(Name = "sys_holiday", DisableSyncStructure = true)]
    public partial class SysHoliday
    {

        [Column(Name = "id", StringLength = 255, IsPrimary = true, IsNullable = false)]
        public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

        [Column(Name = "holiday")]
        public bool Holiday { get; set; }

        [Column(Name = "date")]
        public DateTime Date { get; set; }

        [Column(Name = "name", StringLength = 50)]
        public string Name { get; set; }

    }
}
