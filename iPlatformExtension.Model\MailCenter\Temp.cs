﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "temp", DisableSyncStructure = true)]
	public partial class Temp {

		[Column(Name = "handler")]
		public string Handler { get; set; }

		[Column(Name = "mail_name")]
		public string MailName { get; set; }

		[Column(Name = "pass")]
		public string Pass { get; set; }

		[Column(Name = "user")]
		public string User { get; set; }

	}

}
