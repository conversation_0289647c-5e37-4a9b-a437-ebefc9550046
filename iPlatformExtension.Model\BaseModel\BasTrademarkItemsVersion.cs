using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_trademark_items_version", DisableSyncStructure = true)]
	public partial class BasTrademarkItemsVersion {

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "end_date")]
		public DateTime? EndDate { get; set; }

		[ Column(Name = "is_default")]
		public bool? IsDefault { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "start_date")]
		public DateTime? StartDate { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "version_id", StringLength = 50, IsNullable = false, IsPrimary = true)]
		public string VersionId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "version_name", StringLength = 50, IsNullable = false)]
		public string VersionName { get; set; }

	}

}
