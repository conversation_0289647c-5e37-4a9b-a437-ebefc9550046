using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_log_table", DisableSyncStructure = true)]
	public partial class SysLogTable {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_id", StringLength = 50)]
		public string ColumnId { get; set; }

		[ Column(Name = "column_name", StringLength = 50)]
		public string ColumnName { get; set; }

		[ Column(Name = "data_code", StringLength = 50)]
		public string DataCode { get; set; }

		[ Column(Name = "table_id", StringLength = 50)]
		public string TableId { get; set; }

	}

}
