using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "express_track_detail", DisableSyncStructure = true)]
	public partial class ExpressTrackDetail {

		[ Column(Name = "detail_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DetailId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "list_id", StringLength = 50, IsNullable = false)]
		public string ListId { get; set; }

		[ Column(Name = "remark", StringLength = 3000)]
		public string Remark { get; set; }

		[ Column(Name = "remind_date")]
		public DateTime? RemindDate { get; set; }

		[ Column(Name = "track_date")]
		public DateTime? TrackDate { get; set; }

		[ Column(Name = "track_desc", StringLength = 500)]
		public string TrackDesc { get; set; }

		[ Column(Name = "track_status", StringLength = 50)]
		public string TrackStatus { get; set; }

		[ Column(Name = "track_user", StringLength = 50, IsNullable = false)]
		public string TrackUser { get; set; }

		[ Column(Name = "track_way", StringLength = 50)]
		public string TrackWay { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
