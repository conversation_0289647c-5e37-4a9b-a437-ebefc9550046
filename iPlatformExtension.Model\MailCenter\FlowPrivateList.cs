﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "flow_private_list", DisableSyncStructure = true)]
	public partial class FlowPrivateList {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 流程id
		/// </summary>
		[Column(Name = "mail_id", StringLength = 50)]
		public string MailId { get; set; }

		[Column(Name = "private_id", StringLength = 50)]
		public string PrivateId { get; set; }

	}

}
