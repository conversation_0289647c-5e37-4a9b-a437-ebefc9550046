﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 商品项信息
/// </summary>
public sealed class GoodsItem
{
    /// <summary>
    /// 小项编号
    /// </summary>
    [JsonPropertyName("code")]
    public string Code { get; set; } = default!;

    /// <summary>
    /// 小项名称
    /// </summary>
    [JsonPropertyName("name")]
    public string Name { get; set; } = default!;

    /// <summary>
    /// 是否有效
    /// </summary>
    [JsonPropertyName("status")]
    public string Status { get; set; } = default!;
}