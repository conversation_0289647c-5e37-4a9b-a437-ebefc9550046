﻿using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc.Patent;

internal sealed class NotAssignedPatentProcExportResultHandler(
    IBaseCountryRepository countryRepository,
    IBaseCtrlProcRepository baseCtrlProcRepository,
    ICustomerRepository customerRepository,
    ISystemDictionaryRepository dictionaryRepository,
    IApplyTypeRepository applyTypeRepository) : INotificationHandler<NotAssignedPatentProcExportResultNotification>
{
    public async Task Handle(NotAssignedPatentProcExportResultNotification notification, CancellationToken cancellationToken)
    {
        foreach (var exportDto in notification.Results)
        {
            exportDto.Country = await countryRepository.GetChineseValueAsync(exportDto.Country) ?? string.Empty;
            exportDto.ProcName = await baseCtrlProcRepository.GetChineseValueAsync(exportDto.ProcName) ?? string.Empty;
            exportDto.CustomerName = await customerRepository.GetChineseValueAsync(exportDto.CustomerId) ?? string.Empty;
            exportDto.CaseDirection = await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.CaseDirection, exportDto.CaseDirection);
            exportDto.ApplyType = await applyTypeRepository.GetChineseValueAsync(exportDto.ApplyType) ?? string.Empty;
        }
    }
}