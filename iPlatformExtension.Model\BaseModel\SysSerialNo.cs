using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_serial_no", DisableSyncStructure = true)]
	public partial class SysSerialNo {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "serial_id", StringLength = 50)]
		public string SerialId { get; set; }

		[ Column(Name = "sn")]
		public int? Sn { get; set; }

		[ Column(Name = "sn_type", StringLength = 50)]
		public string SnType { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

	}

}
