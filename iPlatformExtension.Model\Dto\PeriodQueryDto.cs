﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 时间段查询参数
/// </summary>
public record struct PeriodQueryDto
{
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 是否需要查询
    /// </summary>
    [JsonIgnore]
    public bool NeedToQuery => StartTime.HasValue || EndTime.HasValue;
}