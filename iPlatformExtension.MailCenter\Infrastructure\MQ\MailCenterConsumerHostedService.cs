﻿using Confluent.Kafka;
using iPlatformExtension.Model.Dto.MailCenter;

namespace iPlatformExtension.MailCenter.Infrastructure.MQ
{
    public class MailCenterConsumerHostedService : BackgroundService
    {

        private readonly KafkaConsumerService<Null, MailCenterMessageContent> _kafkaConsumerService;
        public MailCenterConsumerHostedService(KafkaConsumerService<Null, MailCenterMessageContent> kafkaConsumerService)
        {
            _kafkaConsumerService = kafkaConsumerService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await _kafkaConsumerService.StartConsumingAsync("MailCenterConsumer", stoppingToken);
        }
    }
}
