﻿using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;

internal sealed record CreateRewardCommand(string ProcId, RewardCreateDto? Dto = null) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;