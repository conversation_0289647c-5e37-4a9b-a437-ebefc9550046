﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Case;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;
using iPlatformExtension.Outsourcing.Application.Queries.Case;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Case;

internal sealed class CaseOutsourcingQueryHandler(
    IFreeSql<PlatformFreeSql> freeSql,
    IHttpContextAccessor httpContextAccessor,
    IForeignSupplierRepository foreignSupplierRepository) : IRequestHandler<CaseOutsourcingQuery, CaseOutsourcingDto>
{
    public async Task<CaseOutsourcingDto> Handle(CaseOutsourcingQuery request, CancellationToken cancellationToken)
    {
        var caseId = request.CaseId;
        var dto = await freeSql.Select<CaseInfo>(caseId).WithLock()
            .ToOneAsync(info => new CaseOutsourcingDto()
            {
                SupplierId = info.ForeginAgencyId,
                ForeignNumber = info.ForeginCaseNo ?? string.Empty
            }, cancellationToken);
        
        if (dto is null)
        {
            throw new NotFoundException(caseId, "案件");
        }

        if (!string.IsNullOrWhiteSpace(dto.SupplierId))
        {
            var supplier = await foreignSupplierRepository.GetCacheValueAsync(dto.SupplierId, cancellationToken: cancellationToken);
            if (supplier is not null)
            {
                dto.AgencyInfo = new ForeignAgencyInfo(dto.SupplierId, supplier.CnName, supplier.EnName);
            }
            
        }
        
        dto.ContactIds = await freeSql.Select<CaseForeignContactList>().WithLock()
            .Where(c => c.CaseId == caseId)
            .ToListAsync(c => c.ContactId, cancellationToken);

        var user = httpContextAccessor.HttpContext?.User;
        if (user is null || !(user.Identity?.IsAuthenticated ?? false))
        {
            throw new NotAuthenticatedException();
        }
        
        dto.Editable = user.IsInRole("专利流程人员");
        
        return dto;
    }
}