using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "proc_trademark_bonus_detail_all", DisableSyncStructure = true)]
	public partial class ProcTrademarkBonusDetailAll {

		[ Column(Name = "@send_official_date_begin")]
		public DateTime SendOfficialDateBegin { get; set; }

		[ Column(Name = "@send_official_date_end")]
		public DateTime SendOfficialDateEnd { get; set; }

	}

}
