﻿using iPlatformExtension.Common.Cache;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;

namespace iPlatformExtension.MailCenterRepository.Implement
{
    public class MailCorrelativeRepository(IFreeSql<MailCenterFreeSql> fsql,
    UnitOfWorkManage<MailCenterFreeSql> manager)
    : DefaultRepository<MailCorrelative, string>(fsql, manager), IMailCorrelativeRepository
    {

    }
}
