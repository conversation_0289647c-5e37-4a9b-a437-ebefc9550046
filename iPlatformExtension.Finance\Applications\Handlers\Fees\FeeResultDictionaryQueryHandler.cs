﻿using iPlatformExtension.Finance.Applications.Queries;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultDictionaryQueryHandler(ISystemDictionaryRepository systemDictionaryRepository)
    : IRequestHandler<FeeResultDictionaryQuery>
{
    public async Task Handle(FeeResultDictionaryQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return;

        foreach (var feeItem in request.FeeResults)
        {
            feeItem.FeeClass = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.FeeClass);
            feeItem.ChargeRule = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.ChargeRule);
            feeItem.PayWay = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.PayWay);
            feeItem.PayStatus = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.PayStatus);
            feeItem.ReceiverStatus = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.ReceiverStatus);
            feeItem.OfficerStatus = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.OfficerStatus);
            feeItem.CaseType = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.CaseType);
            feeItem.PaymentName = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.PaymentName);
            feeItem.CaseDirection = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.CaseDirection);
            feeItem.CaseSourceType = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.CaseSourceType);
            feeItem.FeesCaseSourceType = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.FeesCaseSourceType);
            feeItem.DeliveryKey = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.DeliveryKey);
            feeItem.TrademarkApplicationType =
                await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.TrademarkApplicationType);
            feeItem.ApplyChannel = await systemDictionaryRepository.GetChineseKeyValueAsync(feeItem.ApplyChannel);
        }
    }
}