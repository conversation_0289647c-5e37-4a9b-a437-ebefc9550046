﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 申请人详情信息
/// </summary>
public class ApplicantDetailDto
{
    /// <summary>
    /// 申请人id
    /// </summary>
    public string? ApplicantId { get; set; }

    /// <summary>
    /// 国家id
    /// </summary>
    public string CountryId { get; set; } = null!;

    /// <summary>
    /// 申请人中文名
    /// </summary>
    public string ApplicantNameCn { get; set; } = null!;

    /// <summary>
    /// 申请人英文名
    /// </summary>
    public string? ApplicantNameEn { get; set; }

    /// <summary>
    /// 中文地址
    /// </summary>
    public string AddressCn { get; set; } = null!;

    /// <summary>
    /// 英文地址
    /// </summary>
    public string? AddressEn { get; set; }

    /// <summary>
    /// 申请人类型id
    /// </summary>
    public string TypeId { get; set; } = null!;

    /// <summary>
    /// 原件是否为中文
    /// </summary>
    public bool? IsChineseIdentity { get; set; }

    /// <summary>
    /// 邮编
    /// </summary>
    public string? PostCode { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public string CertificationType { get; set; } = null!;

    /// <summary>
    /// 证件编号
    /// </summary>
    public string CertificationNumber { get; set; } = null!;
}