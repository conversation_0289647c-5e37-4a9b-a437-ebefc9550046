﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DeliveryFileSnapshotQueryHandler(IFreeSql freeSql, IMapper mapper)
    : IRequestHandler<DeliveryFileSnapshotQuery, IEnumerable<FileInfoSnapshot>>
{
    public async Task<IEnumerable<FileInfoSnapshot>> Handle(DeliveryFileSnapshotQuery request, CancellationToken cancellationToken)
    {
        var deliveryFiles = await freeSql.Select<DeliFiles>().Where(file => file.ProcId == request.ProcId)
            .ToListAsync(cancellationToken);

        return mapper.Map<List<FileInfoSnapshot>>(deliveryFiles);
    }
}