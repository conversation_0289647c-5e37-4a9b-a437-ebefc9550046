using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_agent", DisableSyncStructure = true)]
	public partial class BasAgent {

		/// <summary>
		/// 代理人ID
		/// </summary>
		[ Column(Name = "agent_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string AgentId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 代理机构ID（关联）
		/// </summary>
		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		/// <summary>
		/// 执业证号
		/// </summary>
		[ Column(Name = "agent_code", StringLength = 50)]
		public string AgentCode { get; set; }

		/// <summary>
		/// 代理人姓名
		/// </summary>
		[ Column(Name = "agent_name", StringLength = 50)]
		public string AgentName { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		/// <summary>
		/// 据点编码
		/// </summary>
		[ Column(Name = "district_code", StringLength = 50)]
		public string DistrictCode { get; set; }

		[ Column(Name = "i_is_import")]
		public bool IIsImport { get; set; } = false;

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 50)]
		public string Remark { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		/// <summary>
		/// 联系电话
		/// </summary>
		[ Column(Name = "tel", StringLength = 50)]
		public string Tel { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// 用户ID（关联）
		/// </summary>
		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; } = Guid.NewGuid().ToString().ToUpper();

	}

}
