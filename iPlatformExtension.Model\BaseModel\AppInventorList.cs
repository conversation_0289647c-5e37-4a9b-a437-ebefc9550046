using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_inventor_list", DisableSyncStructure = true)]
	public partial class AppInventorList {

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "inventor_id", StringLength = 50, IsNullable = false)]
		public string InventorId { get; set; }

		[ Column(Name = "is_represent")]
		public bool IsRepresent { get; set; } = false;

		[ Column(Name = "is_unpub")]
		public bool IsUnpub { get; set; } = false;

		[ Column(Name = "seq")]
		public short? Seq { get; set; }

	}

}
