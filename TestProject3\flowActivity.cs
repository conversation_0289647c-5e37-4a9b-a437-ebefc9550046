﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace TestProject3
{
    internal class flowActivity
    {
        // <summary>
        /// 流程活动主键
        /// </summary>

        [JsonProperty("activity_id")]
        public string ActivityId { get; set; } = Guid.NewGuid().ToString().ToUpper();

        [JsonProperty("allow_edit")]
        public bool AllowEdit { get; set; } = true;

        [JsonProperty("create_time")]
        public string CreateTime { get; set; }

        [JsonProperty("create_user_id")]
        public string CreateUserId { get; set; }

        [JsonProperty("cur_node_id")]
        public string CurNodeId { get; set; }

        [JsonProperty("cur_user_id")]
        public string CurUserId { get; set; }

        [JsonProperty("flow_id")]
        public string FlowId { get; set; }

        [JsonProperty("flow_sub_type")]
        public string FlowSubType { get; set; }

        [JsonProperty("flow_type")]
        public string FlowType { get; set; }

        [JsonProperty("int_due_date")]
        public DateTime? IntDueDate { get; set; }

        [JsonProperty("obj_id")]
        public string ObjId { get; set; } = Guid.NewGuid().ToString().ToUpper();

        [JsonProperty("prev_audit_type_id")]
        public string PrevAuditTypeId { get; set; }

        [JsonProperty("prev_node_id")]
        public string PrevNodeId { get; set; }

        [JsonProperty("private_id")]
        public string PrivateId { get; set; }

        [JsonProperty("status")]
        public int? Status { get; set; }

        [JsonProperty("update_time")]
        public string UpdateTime { get; set; }

        [JsonProperty("update_user_id")]
        public string UpdateUserId { get; set; }
    }
}
