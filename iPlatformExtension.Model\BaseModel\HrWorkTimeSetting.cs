using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "hr_work_time_setting", DisableSyncStructure = true)]
	public partial class HrWorkTimeSetting {

		[ Column(Name = "am_off", StringLength = 50)]
		public string AmOff { get; set; }

		[ Column(Name = "am_on", StringLength = 50)]
		public string AmOn { get; set; }

		[ Column(Name = "is_default")]
		public bool IsDefault { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_partner")]
		public bool IsPartner { get; set; } = false;

		[ Column(Name = "is_suckle")]
		public bool IsSuckle { get; set; } = false;

		[ Column(Name = "on_time", StringLength = 50)]
		public string OnTime { get; set; }

		[ Column(Name = "pm_off", StringLength = 50)]
		public string PmOff { get; set; }

		[ Column(Name = "pm_on", StringLength = 50)]
		public string PmOn { get; set; }

		[ Column(Name = "rest_off", StringLength = 50)]
		public string RestOff { get; set; }

		[ Column(Name = "rest_on", StringLength = 50)]
		public string RestOn { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "setting_id", StringLength = 50, IsNullable = false)]
		public string SettingId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "title", StringLength = 200)]
		public string Title { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
