﻿namespace iPlatformExtension.MailCenter.Infrastructure.Provide
{
    /// <summary>
    /// 信号量
    /// </summary>
    public interface ISemaphoreProvider
    {
        /// <summary>
        /// 信号量
        /// </summary>
        SemaphoreSlim Semaphore { get; }

        /// <summary>
        /// 锁方法
        /// </summary>
        /// <param name="action"></param>
        /// <param name="cancelToken"></param>
        public async Task LockActionAsync(Func<Task> action, CancellationToken cancelToken = default)
        {
            await Semaphore.WaitAsync(cancelToken);
            try
            {
                await action.Invoke();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                Semaphore.Release();
            }
        }
    }
}
