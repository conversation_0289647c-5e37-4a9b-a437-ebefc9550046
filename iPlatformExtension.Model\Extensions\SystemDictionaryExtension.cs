﻿using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Model.Extensions;

/// <summary>
/// 数据字典扩展程序
/// </summary>
public static class SystemDictionaryExtension
{
    /// <summary>
    /// 返回中文的键值对
    /// </summary>
    /// <param name="sysDictionary">数据字典值</param>
    /// <returns>包含中文的键值对</returns>
    public static KeyValuePair<string, string> ToChineseKeyValue(this SysDictionary sysDictionary)
    {
        ArgumentNullException.ThrowIfNull(sysDictionary);
        return KeyValuePair.Create(sysDictionary.Value, sysDictionary.TextZhCn);
    }
}