﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters
{
    /// <summary>
    /// 商标更正
    /// </summary>
    public partial class CorrectionTrademarkParameters : PhoenixOrderBaseRequestParameters
    {
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("appKey")]
        public string AppKey { get; set; }

        /// <summary>
        /// 申请人详情地址（中文）
        /// </summary>
        [JsonPropertyName("applicantAddress")]
        public string ApplicantAddress { get; set; }

        /// <summary>
        /// 申请人详情地址（英文）
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("applicantEnglishAddress")]
        public string ApplicantEnglishAddress { get; set; }

        /// <summary>
        /// 申请人名称
        /// </summary>
        [JsonPropertyName("applicantName")]
        public string ApplicantName { get; set; }

        /// <summary>
        /// 更正前信息
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("applyProve")]
        public string ApplyProve { get; set; }

        /// <summary>
        /// 更正后信息
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("applyProveAfter")]
        public string ApplyProveAfter { get; set; }

        /// <summary>
        /// 书式类型,1表示大陆,2表示海外,3表示台湾,4表示香港,5表示澳门
        /// </summary>
        [JsonPropertyName("bookType")]
        public int BookType { get; set; }

        /// <summary>
        /// 证件类型,0不是任何类型,1身份证,2护照,3其他,
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("certificatesType")]
        public int? CertificatesType { get; set; }

        /// <summary>
        /// 邮编
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("code")]
        public string Code { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        [JsonPropertyName("contactEmail")]
        public string ContactEmail { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [JsonPropertyName("contactName")]
        public string ContactName { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [JsonPropertyName("contactTel")]
        public string ContactTel { get; set; }

        /// <summary>
        /// 国家或地区
        /// </summary>
        [JsonPropertyName("country")]
        public string Country { get; set; }

        /// <summary>
        /// 英文申请人名称
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("englishApplicantName")]
        public string EnglishApplicantName { get; set; }

        /// <summary>
        /// 证件编号
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("idCard")]
        public string IdCard { get; set; }

        /// <summary>
        /// 申请人资质：0个人 1公司
        /// </summary>
        [JsonPropertyName("ownerType")]
        public int OwnerType { get; set; }

        /// <summary>
        /// 委托人姓名
        /// </summary>
        [JsonPropertyName("principalName")]
        public string PrincipalName { get; set; }

        /// <summary>
        /// 委托人电话
        /// </summary>
        [JsonPropertyName("principalTel")]
        public string PrincipalTel { get; set; }

        /// <summary>
        /// 主体资格类型:1表示中文,0表示非中文
        /// </summary>
        [JsonPropertyName("subjectType")]
        public int SubjectType { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("userName")]
        public string UserName { get; set; }
    }

}