﻿using System.Collections;
using System.Collections.Concurrent;
using System.Reflection;
using System.Text.Json;
using AutoMapper;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.Proc;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class BuildDynamicItemsCommandHandler(
    IServiceProvider serviceProvider,
    EntityTypeInfoProvider typeInfoProvider,
    IMediator mediator,
    IMapper mapper)
    : IRequestHandler<BuildDynamicItemsCommand, IReadOnlyDictionary<ProcExtensionKey, ProcExtensionItem>>
{
    private static readonly Assembly baseModelAssembly = typeof(CaseProcInfo).Assembly;

    private static readonly ConcurrentDictionary<string, Type?> entityCacheRepositoryTypes = new();
    
    public async Task<IReadOnlyDictionary<ProcExtensionKey, ProcExtensionItem>> Handle(BuildDynamicItemsCommand request, CancellationToken cancellationToken)
    {
        var (entity, configuration, userReadonly, isRoot) = request;
        
        var typeInfo = typeInfoProvider.Get(entity.GetType());
        var properties = typeInfo.EntityPropertyInfos;
        var items = new SortedList<ProcExtensionKey, ProcExtensionItem>(configuration.Count(), ProcExtensionKeyComparer.Default);

        if (isRoot)
        {
            var rootItemValue = await mediator.Send(
                new BuildDynamicItemsCommand(entity,
                    configuration.Where(config =>
                        config.Include == NavigationType.None && config.BelongClassName == entity.GetType().Name), userReadonly),
                cancellationToken);

            if (rootItemValue.Any())
            {
                var rootItem = new ProcExtensionItem()
                {
                    DisplayName = "递交信息",
                    Key = JsonNamingPolicy.CamelCase.ConvertName(nameof(CaseProcInfo)),
                    Type = nameof(Object),
                    Value = rootItemValue,
                    IsReadonly = userReadonly
                };
                items.Add(new ProcExtensionKey(rootItem.Key, int.MinValue), rootItem);
            }
        }
        
        foreach (var dynamicConfig in configuration.DistinctBy(config => config.Key).OrderBy(config => config.Order))
        {
            if (!properties.TryGetValue(dynamicConfig.PropertyName, out var propertyInfo) || propertyInfo.Get is null)
            {
                continue;
            }

            var item = mapper.Map<ProcExtensionItem>(dynamicConfig);
            
            switch (dynamicConfig.Include)
            {
                case NavigationType.None:
                {
                    if (isRoot)
                        break;

                    item.Value = propertyInfo.Get(entity);
                    if (dynamicConfig.IsKeyValue)
                    {
                        if (dynamicConfig.IsDictionary)
                        {
                            var dictionaryRepository =
                                serviceProvider.GetRequiredService<ISystemDictionaryRepository>() as
                                    ICacheableRepository<SystemDictionaryValueKey, SysDictionary>;
                            item.DisplayValue = string.Join(';',
                                await (item.Value?.ToString() ?? string.Empty)
                                .Split(';', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries)
                                .ToAsyncEnumerable().SelectAwait(async value =>
                                    (await dictionaryRepository.GetCacheValueAsync(
                                        new SystemDictionaryValueKey(dynamicConfig.DictionaryName,
                                            value)))?.TextZhCn).ToArrayAsync(cancellationToken));
                        }
                        else
                        {
                            var repositoryType = entityCacheRepositoryTypes.GetOrAdd(dynamicConfig.DisplayValueSourceType,
                                typeName =>
                                {
                                    var entityType = Type.GetType(
                                        $"iPlatformExtension.Model.BaseModel.{typeName},{baseModelAssembly}");
                                    return entityType is null
                                        ? null
                                        : typeof(IStringKeyCacheableRepository<>).MakeGenericType(entityType);
                                });
                            if (repositoryType is not null)
                            {
                                item.DisplayValue =
                                    serviceProvider.GetRequiredService(repositoryType) is not IStringKeyValueRepository
                                        cacheRepository
                                        ? null
                                        : string.Join(';',
                                            await (item.Value?.ToString() ?? string.Empty)
                                                .Split(';', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries)
                                                .ToAsyncEnumerable()
                                                .SelectAwait(cacheRepository.GetTextValueAsync)
                                                .ToArrayAsync(cancellationToken));
                            }
                        }
                    }
                    else if (item.Value is bool booleanValue)
                    {
                        item.DisplayValue = booleanValue.GetBooleanChineseDescription();
                    }
                    item.Type = propertyInfo.PropertyType.Name;
                    break;
                }
                case NavigationType.One:
                {
                    var navigatedEntity = propertyInfo.Get(entity);
                    if (navigatedEntity is null)
                        break;

                    var entityItems =
                        await mediator.Send(
                            new BuildDynamicItemsCommand(navigatedEntity,
                                configuration.Where(config => config.BelongClassName == propertyInfo.PropertyType.Name), userReadonly),
                            cancellationToken);
                    item.Value = entityItems;
                    item.Type = nameof(Object);
                    break;
                }
                case NavigationType.Many:
                {
                    var propertyType = propertyInfo.PropertyType;
                    if (typeof(IEnumerable).IsAssignableFrom(propertyType.GetGenericTypeDefinition()))
                    {
                        var navigatedEntityType = propertyType.GetGenericArguments()[0];
                        item.Headers = configuration.Where(config => config.BelongClassName == navigatedEntityType.Name)
                            .DistinctBy(config => config.Key).OrderBy(config => config.Order)
                            .Select(config => new ProcExtensionListHeader()
                            {
                                DisplayName = config.DisplayName,
                                Key = config.Key,
                                IsShow = config.IsShow
                            }).ToArray();
                    }
                    
                    var navigatedCollection = propertyInfo.Get(entity) as IEnumerable<object>;
                    if (navigatedCollection?.Any() ?? false)
                    {
                        item.Value = await navigatedCollection.ToAsyncEnumerable().SelectAwait(async navigatedEntity =>
                        {
                            var entityItems =
                                await mediator.Send(
                                    new BuildDynamicItemsCommand(navigatedEntity,
                                        configuration.Where(config =>
                                            config.BelongClassName == navigatedEntity.GetType().Name), userReadonly),
                                    cancellationToken);
                            return entityItems;
                        }).ToArrayAsync(cancellationToken);
                        item.Type = nameof(Array);
                    }
                    else
                    {
                        item.Value = Array.Empty<object>();
                    }
                    break;
                }
                default:
                    throw new ArgumentOutOfRangeException();
            }

            item.IsReadonly = dynamicConfig.IsReadonly || userReadonly;
            
            if (dynamicConfig.Include != NavigationType.None || !isRoot)
            {
                items.Add(new ProcExtensionKey(dynamicConfig.Key, dynamicConfig.Order), item);
            }
        }
        
        return items;
    }
}