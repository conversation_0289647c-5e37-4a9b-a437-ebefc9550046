﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery.NominalChange;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Exceptions;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery.NominalChange;

internal sealed class NominalChangeOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IDeliveryInfoRepository deliveryInfoRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    IBaseCountryRepository countryRepository,
    IApplicantTypeRepository applicantTypeRepository,
    UnitOfWorkManager unitOfWorkManager,
    ILoggerFactory loggerFactory,
    IOptionsMonitor<PhoenixClientOptions> options,
    PhoenixClientFactory phoenixClientFactory) : 
    PhoenixDeliveryHandleBase<NominalChangeOrderCommand, TrademarkChangeOrder,
            PhoenixResponseParameters<OrderData>>(stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository,
            unitOfWorkManager, loggerFactory, phoenixClientFactory), 
    IDeliveryHandler<NominalChangeOrderCommand, TrademarkChangeOrder, PhoenixResponseParameters<OrderData>>
{
    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.StartupDelivery;

    public override async Task InitializeAsync(NominalChangeOrderCommand command, CancellationToken cancellationToken)
    {
        var procId = command.ProcId;
        _deliveryInfo = await DeliveryInfoRepository.Select
            .IncludeMany(deliInfo => deliInfo.Applicants)
            .IncludeMany(deliInfo => deliInfo.Priorities)
            .IncludeMany(deliInfo => deliInfo.NiceCategories)
            .IncludeMany(deliInfo => deliInfo.Files)
            .Where(info => info.ProcId == procId).ToOneAsync(cancellationToken);
        if (_deliveryInfo is null)
        {
            throw new NotFoundException(procId, "递交任务");
        }

        if (command.Version != _deliveryInfo.Version)
        {
            throw new VersionException(command.Version, _deliveryInfo.Version);
        }

        _phoenixClient = _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
    }

    public override async Task<TrademarkChangeOrder> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        
        var currentApplicant = _deliveryInfo.Applicants!.Single(applicant => applicant.IsRepresent ?? false);

        var countryId = currentApplicant.CountryId ??
                        throw new PropertyMissingException(currentApplicant.ApplicantId, currentApplicant.CountryId, "申请人");
        var applicantBookType = ApplicantBookType.GetApplicantBookType(countryId);
        
        string country;
        if (applicantBookType == ApplicantBookType.Overseas)
        {
            var countryInfo = await countryRepository.GetCacheValueAsync(countryId);
            if (countryInfo is null)
            {
                throw new NotFoundException(countryId, "国家");
            }

            if (string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCode) || string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCountryName))
            {
                throw new ApplicationException("商标局没有对应的国家编码和名称");
            }

            country = $"{countryInfo.TrademarkDeliveryCode}  {countryInfo.TrademarkDeliveryCountryName}";
        }
        else
        {
            country = applicantBookType.Description;
        }

        var applicantType = await applicantTypeRepository.GetCacheValueAsync(currentApplicant.TypeId.GetOrDefaultEmpty());
        if (applicantType is null)
        {
            throw new ApplicantTypeMissingException(currentApplicant, "当前申请人");
        }

        var formerApplicant = _deliveryInfo.Applicants!.First(applicant => applicant.DeliveryBusinessType is not null);
        
        var attachments = _deliveryInfo.Files?
                              .Where(file => int.TryParse(file.FileCode, out _) && file.BaseFileType is "申请人文件" or "递交官方")
                              .OrderBy(files => files.FileDesc)
                              .Select(file => 
                                  new ApplicantAttachment(
                                      file.FileName, 
                                      file.Url ?? string.Empty, 
                                      ApplicantAttachmentType.OfficialAttachment, 
                                      new ApplicantAttachmentSubType(int.Parse(file.FileCode!), file.FileDesc ?? string.Empty))) 
                          ?? Array.Empty<ApplicantAttachment>();

        var clientOptions = options.Get(_deliveryInfo.DeliveryKey);
        ArgumentNullException.ThrowIfNull(clientOptions);

        var order = new TrademarkChangeOrder()
        {
            CurrentApplicant = new DeliveryApplicantInfo()
            {
                BookType = applicantBookType.Code.ToString(),
                Country = country,
                OwnerType = applicantType.ApplicantTypeCode == "5" ? "0" : "1",
                ApplicantName = currentApplicant.ApplicantNameCn,
                UnifiedSocialCreditCode = currentApplicant.CardNo,
                ApplicantEnglishName = currentApplicant.ApplicantNameEn,
                ApplicantAddress = currentApplicant.AddressCn ?? throw new PropertyMissingException(currentApplicant.Id, currentApplicant.AddressCn, "递交任务申请人信息", "中文地址"),
                ApplicantEnglishAddress = currentApplicant.AddressEn,
                Code = currentApplicant.Postcode,
                DomesticReceiverAddress = _deliveryInfo.ContactAddress,
                DomesticReceiverCode = _deliveryInfo.ContactPostCode,
                DomesticReceiverEmail = _deliveryInfo.ContactMailBox,
                CertificatesType = applicantType.ApplicantTypeCode == "5" ? ApplicantCertificationType.GetApplicantCertificationType(currentApplicant.CardType).Code
                    .ToString() : string.Empty,
                IdCard = currentApplicant.CardNo,
                SubjectType = currentApplicant.IsChineseIdentity ? "1" : "0",
            },
            
            TransferType = formerApplicant.DeliveryBusinessType!,
            
            OrderInfo = new OrderInfo()
            {
                ContactTel = _deliveryInfo.ContactTel ?? string.Empty,
                ContactName = _deliveryInfo.ContactPerson ?? string.Empty,
                ContactEmail = _deliveryInfo.ContactMailBox ?? string.Empty,
                PrincipalName = _deliveryInfo.ContactPerson,
                PrincipalTel = _deliveryInfo.ContactTel,
                AgentOrganConName = _deliveryInfo.AgentUser ?? throw new PropertyMissingException(_deliveryInfo.ProcId, _deliveryInfo.AgentUser, "递交任务信息", "代理人"),
                AgentOrganId = clientOptions.OrganizationId,
                AgentOrganName = clientOptions.UserName,
                DomesticReceiverAddress = _deliveryInfo.ContactAddress,
                DomesticReceiverCode = _deliveryInfo.ContactPostCode,
                DomesticReceiverName = _deliveryInfo.AgencyName,
            },
            
            FormerApplicant = new DeliveryApplicantInfo()
            {
                ApplicantName = formerApplicant.ApplicantNameCn,
                ApplicantEnglishName = formerApplicant.ApplicantNameEn,
                ApplicantAddress = formerApplicant.AddressCn,
                ApplicantEnglishAddress = formerApplicant.AddressEn,
                DomesticReceiverAddress = formerApplicant.AddressCn,
                OwnerType = applicantType.ApplicantTypeCode == "5" ? "0" : "1",
                BookType = applicantBookType.Code.ToString(),
                IdCard = formerApplicant.CardNo
            },
            
            Attachments = attachments,
             
            BrandInfos = [new BrandInfo()
            {
                BrandRegisterNo = _deliveryInfo.AppNo
            }],
            OrderToken = _deliveryInfo.ProcId
        };

        return order;
    }

    public override Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(TrademarkChangeOrder request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        var client = _phoenixClient ?? _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
        return client.CreateOrderAsync(PhoenixUri.ChangeOrder, request);
    }

    public override  Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        var orderNo = response.Data?.OrderNo!;
        const int newStatus = (int) DeliveryStatus.Ordered;
        
        return Handler.UpdateDeliveryInfoInternalAsync(deliveryInfo =>
        {
            deliveryInfo.Status = newStatus;
            deliveryInfo.OrderNo = orderNo;
            deliveryInfo.OperationResult = true;
            return ValueTask.CompletedTask;
        }, cancellationToken);
    }
    
    public async Task<bool> HandleExceptionAsync(Exception ex, CancellationToken cancellationToken = default)
    {
        await Handler.HandleExceptionInternalAsync(ex, true, false, cancellationToken);
        return false;
    }
}