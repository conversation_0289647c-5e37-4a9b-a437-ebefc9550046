﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_flow_private", DisableSyncStructure = true)]
	public partial class MailFlowPrivate {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 默认选中
		/// </summary>
		[Column(Name = "is_default")]
		public bool IsDefault { get; set; } = false;

		[Column(Name = "label_name", StringLength = 100)]
		public string LabelName { get; set; }

		/// <summary>
		/// 分类标签类型:receive:收件标签,send:发件标签.
		/// </summary>
		[Column(Name = "mail_type", StringLength = 10, IsNullable = false)]
		public string MailType { get; set; } = "receive";

		/// <summary>
		/// 排序
		/// </summary>
		[Column(Name = "sort", DbType = "int")]
		public int Sort { get; set; }

		[Column(Name = "update_by", StringLength = 50)]
		public string UpdateBy { get; set; }

		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

		[Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
