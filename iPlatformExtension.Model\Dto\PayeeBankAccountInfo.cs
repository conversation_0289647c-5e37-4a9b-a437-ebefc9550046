﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 收款主体银行账户信息
/// </summary>
public record PayeeBankAccountInfo
{
    /// <summary>
    /// 银行id
    /// </summary>
    public string BankId { get; init; } = default!;

    /// <summary>
    /// 开户行中文名
    /// </summary>
    public string? CnName { get; init; }

    /// <summary>
    /// 开户行英文名
    /// </summary>
    public string? EnName { get; init; }

    /// <summary>
    /// 币种
    /// </summary>
    public string Currency { get; init; } = default!;

    /// <summary>
    /// 银行账户
    /// </summary>
    public string BankAccount { get; init; } = default!;

    /// <summary>
    /// 银行国际代码
    /// </summary>
    public string SwiftCode { get; init; } = default!;

    /// <summary>
    /// 开户行中文地址
    /// </summary>
    public string? AddressCn { get; init; }

    /// <summary>
    /// 开户行英文地址
    /// </summary>
    public string? AddressEn { get; init; }

    /// <summary>
    /// 银行户名
    /// </summary>
    public string AccountName { get; init; } = default!;

    /// <summary>
    /// 英文银行账户名
    /// </summary>
    public string? AccountNameEn { get; init; }
}