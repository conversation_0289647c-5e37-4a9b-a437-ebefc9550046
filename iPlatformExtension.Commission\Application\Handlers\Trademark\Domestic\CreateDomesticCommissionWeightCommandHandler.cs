using Google.Protobuf.WellKnownTypes;
using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Commission.Clients;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class CreateDomesticCommissionWeightCommandHandler(
    ISender sender,
    IFreeSql<PlatformFreeSql> freeSql
#if !DEBUG
        , 
    PeriodSettingService.PeriodSettingServiceClient client
#endif
    ) 
    : IRequestHandler<CreateDomesticCommissionWeightCommand>
{
    public async Task Handle(CreateDomesticCommissionWeightCommand request, CancellationToken cancellationToken)
    {
        var endDate = DateTime.Today;

#if DEBUG
        var startDate = endDate.AddMonths(-1);
        startDate = new DateTime(startDate.Year, startDate.Month, 1);
#endif

#if !DEBUG
        var settings = (await client.getPeriodSettingListAsync(new Empty(), cancellationToken:cancellationToken)).PeriodSettings;
        
        var todaySetting = settings.FirstOrDefault(setting => setting.Month == endDate.Month);
        if (todaySetting == null)
        {
            return;
        }
        
        var startDateMonth = endDate.Day <= todaySetting.DeadlinePushingPermissionData
            ? endDate.AddMonths(-1)
            : endDate;
        
        var startDate = new DateTime(startDateMonth.Year, startDateMonth.Month, 1);
#endif
        //
        // DateTime startDate, endDate;
        //
        // if (otherSettings.Month == 12 && todaySetting.Month == 1)
        // {
        //     startDate = new DateTime(today.Year - 1, otherSettings.Month, otherSettings.DeadlinePushingPermissionData);
        //     endDate = new DateTime(today.Year, today.Month, today.Day);
        // }
        // else if (todaySetting.Month < otherSettings.Month)
        // {
        //     startDate = new DateTime(today.Year, today.Month, todaySetting.DeadlinePushingPermissionData);
        //     endDate = new DateTime(today.Year, otherSettings.Month, otherSettings.DeadlinePushingPermissionData);
        // }
        // else
        // {
        //     startDate = new DateTime(today.Year, otherSettings.Month, otherSettings.DeadlinePushingPermissionData);
        //     endDate = new DateTime(today.Year, today.Month, today.Day);
        // }
        
        var procIds = await freeSql.Select<CaseProcInfo>().WithLock()
            .Where(info => startDate <= info.CommissionEffectiveDate && endDate > info.CommissionEffectiveDate)
            .Where(info => info.CaseInfo.CaseDirection == CaseDirection.II)
            .Where(info => info.CaseInfo.CaseTypeId == CaseType.Trade)
            .Where(info => info.IsEnabled == true)
            .Where(info => !freeSql.Select<DomesticTrademarkCommission>().Any(commission => commission.ProcId == info.ProcId))
            .ToListAsync(info => info.ProcId, cancellationToken);
        
        foreach (var procId in procIds)
        {
            await sender.Send(new CreateDomesticCommissionCommand(procId), cancellationToken);
        }
        
    }
}