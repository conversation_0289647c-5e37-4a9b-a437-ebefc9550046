using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_cus_applicant_list", DisableSyncStructure = true)]
	public partial class ACusApplicantList {

		[ Column(Name = "applicant_id", StringLength = 50, IsNullable = false)]
		public string ApplicantId { get; set; }

		[ Column(Name = "applicant_name_cn", StringLength = 500)]
		public string ApplicantNameCn { get; set; }

		[ Column(Name = "applicant_name_en", StringLength = 500)]
		public string ApplicantNameEn { get; set; }

		[ Column(Name = "card_no", StringLength = 50)]
		public string CardNo { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "fee_reduce_year", StringLength = 500)]
		public string FeeReduceYear { get; set; }

		[ Column(Name = "is_del")]
		public bool? IsDel { get; set; } = false;

		[ Column(Name = "is_do")]
		public bool? IsDo { get; set; } = false;

		[ Column(Name = "new_applicant_id", StringLength = 50)]
		public string NewApplicantId { get; set; }

		[ Column(Name = "num")]
		public int? Num { get; set; } = 0;

		[ Column(Name = "proxy_no", StringLength = 50)]
		public string ProxyNo { get; set; }

		[ Column(Name = "proxy_no_pct", StringLength = 50)]
		public string ProxyNoPct { get; set; }

	}

}
