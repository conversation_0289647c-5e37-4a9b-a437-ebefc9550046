using iPlatformExtension.MailCenter.Applications.Models.Template;
using iPlatformExtension.MailCenter.Applications.Queries.Template;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Template
{
    /// <summary>
    /// 获取邮件模板列表查询处理程序
    /// </summary>
    internal sealed class GetMailTemplateListQueryHandler(
        IMailTemplateRepository mailTemplateRepository
    ) : IRequestHandler<GetMailTemplateListQuery, PageResult<GetMailTemplateListDto>>
    {
        public async Task<PageResult<GetMailTemplateListDto>> Handle(GetMailTemplateListQuery request, CancellationToken cancellationToken)
        {
            var query = mailTemplateRepository
                .WhereIf(!string.IsNullOrWhiteSpace(request.Search), t =>
                    t.Name.Contains(request.Search!) ||
                    t.TemplateNo.Contains(request.Search!))
                .WhereIf(request.IsEnabled.HasValue, t => t.IsEnabled == request.IsEnabled);

            var totalCount = await query.CountAsync(cancellationToken);

            var data = await query
                .OrderByDescending(t => t.CreateTime)
                .Page(request.PageIndex, request.PageSize)
                .ToListAsync(t => new GetMailTemplateListDto(
                    t.TemplateId,
                    t.TemplateNo,
                    t.Name,
                    t.Title,
                    t.CreateTime,
                    t.CreateBy,
                    t.UpdateTime,
                    t.UpdateBy,
                    t.IsEnabled
                ), cancellationToken);

            return new PageResult<GetMailTemplateListDto>
            {
                Data = data,
                Total = totalCount,
                Page = request.PageIndex,
                PageSize = request.PageSize
            };
        }
    }
}
