﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DeleteDeliveryCommandHandler : IRequestHandler<DeleteDeliveryCommand, DeliInfo?>
{
    private readonly IDeliveryInfoRepository _deliveryInfoRepository;

    private readonly IDeliveryHistoryRepository _deliveryHistoryRepository;

    private readonly IDeliveryFilesRepository _deliveryFilesRepository;

    private readonly IDeliveryOtherInfoRepository _otherInfoRepository;

    public DeleteDeliveryCommandHandler(
        IDeliveryInfoRepository deliveryInfoRepository, 
        IDeliveryOtherInfoRepository otherInfoRepository,
        IDeliveryHistoryRepository deliveryHistoryRepository, 
        IDeliveryFilesRepository deliveryFilesRepository)
    {
        _deliveryInfoRepository = deliveryInfoRepository;
        _deliveryHistoryRepository = deliveryHistoryRepository;
        _deliveryFilesRepository = deliveryFilesRepository;
        _otherInfoRepository = otherInfoRepository;
        _deliveryInfoRepository.DbContextOptions.EnableCascadeSave = true;
    }

    public async Task<DeliInfo?> Handle(DeleteDeliveryCommand request, CancellationToken cancellationToken)
    {
        var deliveryInfo = await _deliveryInfoRepository.Where(info => info.ProcId == request.ProcId)
            .Include(info => info.OtherInfo)
            .IncludeMany(info => info.Applicants)
            .IncludeMany(info => info.Priorities)
            .IncludeMany(info => info.NiceCategories)
            .IncludeMany(info => info.LawBasis)
            .IncludeMany(info => info.CitedTrademarkAnnulments)
            .IncludeMany(info => info.Files,
                files => files.Where(file => file.IsIdentity == true))
            .ToOneAsync(cancellationToken);

        if (deliveryInfo is null) return deliveryInfo;

        if (request.Version is not null)
        {
            deliveryInfo.ValidateVersion(request.Version.Value);
        }

        await _deliveryInfoRepository.DeleteAsync(deliveryInfo, cancellationToken);
        await _otherInfoRepository.DeleteAsync(request.ProcId, cancellationToken);

        if (!request.Refresh)
        {
            await _deliveryFilesRepository.DeleteAsync(files => files.ProcId == request.ProcId, cancellationToken);
            await _deliveryHistoryRepository.DeleteAsync(history => history.ProcId == request.ProcId,
                cancellationToken);
        }

        return deliveryInfo;
    }
}