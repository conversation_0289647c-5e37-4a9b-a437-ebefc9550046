﻿namespace iPlatformExtension.MailCenter.Applications.Models.SysConfig
{
    public class GetMailAccessDto
    {
        public string Id { get; set; }

        /// <summary>
        /// 访问权限,查看:read/发件:write/分拣:sorter/管理:manager
        /// </summary>
        public string AccessMode { get; set; }

        public string CreateBy { get; set; }

        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 邮箱id
        /// </summary>
        public string HostId { get; set; }

        /// <summary>
        /// 对应权限使用类型具体ID:
        /// 部门:sys_dept_info.dept_id/
        /// 个人用户:sys_user_Info.user_id/
        /// 角色:sys_role_info.role_id
        /// </summary>
        public string UseId { get; set; }

        /// <summary>
        /// 权限使用类型,部门:dept/个人用户:user/角色:role
        /// </summary>
        public string UseType { get; set; }

        public string DisplayName { get; set; }
    }
}
