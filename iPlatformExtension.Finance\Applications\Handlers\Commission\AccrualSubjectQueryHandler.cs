﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Finance.Applications.Models.Commission;
using iPlatformExtension.Finance.Applications.Queries.Commission;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Commission;

internal sealed class AccrualSubjectQueryHandler(IFreeSql freeSql, IUserInfoRepository userInfoRepository)
    : IRequestHandler<AccrualSubjectQuery, FeesAccrualSubjectDto>
{
    
    /// <summary>
    /// 法律特殊业务类型编码
    /// </summary>
    private static readonly string[] lawCodes = ["LS", "LF", "LZ", "SS-P", "SS-T"];

    /// <summary>
    /// 商标特殊业务编码
    /// </summary>
    private const string TrademarkCode = "BS";

    private readonly ICacheableRepository<string, UserBaseInfo> _userInfoRepository = userInfoRepository;

    public async Task<FeesAccrualSubjectDto> Handle(AccrualSubjectQuery request, CancellationToken cancellationToken)
    {
        var feeInfo = await freeSql.Select<CaseFeeList>(request.FeeId)
            .Include(fee => fee.CaseProcInfo.CaseInfo.Customer)
            .FirstAsync(cancellationToken);

        var procInfo = feeInfo.CaseProcInfo;
        var caseInfo = procInfo.CaseInfo;
        var caseType = caseInfo.CaseTypeId;
        switch (caseInfo.CaseTypeId)
        {
            case CaseType.Project:
            {
                var businessType = caseInfo.BusinessType;
                if (businessType is not null && lawCodes.Any(code => code.Equals(businessType.BusinessTypeCode, StringComparison.OrdinalIgnoreCase)))
                {
                    caseType = CaseType.Legal;
                }

                break;
            }
            case CaseType.Trade:
            {
                var businessType = procInfo.BusinessType;
                if (businessType is not null && TrademarkCode.Equals(businessType.BusinessTypeCode, StringComparison.OrdinalIgnoreCase))
                {
                    caseType = CaseType.Copy;
                }

                break;
            }
        }

        var followers = feeInfo.CaseProcInfo.CaseInfo.Customer.CustomerFollows
            .Where(list => list.CaseType == caseType && list.CaseDirection == caseInfo.CaseDirection && list.IsEnabled);

        var salesInfo = followers
            .FirstOrDefault(follow => follow.CustomerUserType.Equals("ay", StringComparison.OrdinalIgnoreCase));
        var salesUserId = salesInfo?.TrackUser;
        var sales = salesUserId is null ? null : await _userInfoRepository.GetCacheValueAsync(salesUserId, cancellationToken: cancellationToken);
        var caseSourceType = salesInfo?.HeadUserType;
        
        var trackUserId = followers
            .FirstOrDefault(follow => follow.CustomerUserType.Equals("ga", StringComparison.OrdinalIgnoreCase))
            ?.TrackUser;
        var tracker = trackUserId is null ? null : await _userInfoRepository.GetCacheValueAsync(trackUserId, cancellationToken: cancellationToken);

        var customer = caseInfo.Customer;
        var businessUser = customer.BusiUserId is null
            ? null
            : await _userInfoRepository.GetCacheValueAsync(customer.BusiUserId, cancellationToken: cancellationToken);
        var clueUser = customer.ClueUserId is null
            ? null
            : await _userInfoRepository.GetCacheValueAsync(customer.ClueUserId, cancellationToken: cancellationToken);

        return new FeesAccrualSubjectDto()
        {
            BusinessUser = businessUser,
            CaseSourceType = caseSourceType,
            ClueUser = clueUser,
            Tracker = tracker,
            Sales = sales
        };
    }
}