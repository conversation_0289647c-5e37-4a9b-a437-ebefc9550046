﻿using iPlatformExtension.Public.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.Public.Applications.Queries.Flow;


/// <summary>
/// 催稿历史
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="DateType">日期类型：1-初稿期限内 2-初稿期限外 3-定稿期限内 4-定稿期限外 5-官方期限</param>
public record UrgentDraftHistoryQuery(string ProcId, int DateType) : IRequest<IEnumerable<UrgentDraftHistoryDto>>;

