﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Commands;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class SupplierTrademarkProcCountCommandHandler(
    IFreeSql<PlatformFreeSql> freeSql,
    ISender sender) 
    : IRequestHandler<SupplierTrademarkProcCountCommand, IEnumerable<SupplierTrademarkProcCountDto>>
{
    private static readonly IEnumerable<string> caseDirections = [CaseDirection.IO, CaseDirection.OO];
    
    public async Task<IEnumerable<SupplierTrademarkProcCountDto>> Handle(SupplierTrademarkProcCountCommand request, CancellationToken cancellationToken)
    {
        var procId = request.ProcId;
        var procInfo = await freeSql.Select<CaseProcInfo>(procId).WithLock()
            .ToOneAsync(info => new CaseProcInfo()
            {
                ProcId = info.ProcId,
                CtrlProcId = info.CtrlProcId,
                CaseInfo = new CaseInfo()
                {
                    Id = info.CaseInfo.Id,
                    CustomerId = info.CaseInfo.CustomerId,
                    CountryId = info.CaseInfo.CountryId,
                    ApplyChannel = info.CaseInfo.ApplyChannel,
                    MemberCountry = info.CaseInfo.MemberCountry
                }
            }, cancellationToken);

        if (procInfo is null)
        {
            return [];
        }

        var caseInfo = procInfo.CaseInfo;
        var countries = caseInfo.ApplyChannel == "madrid" ? caseInfo.MemberCountry?.Split(",") ?? [] : [caseInfo.CountryId ?? string.Empty];


        return await sender.Send(new SupplierProcCountQuery(caseInfo.CustomerId, procInfo.CtrlProcId, countries),
            cancellationToken);
    }
}