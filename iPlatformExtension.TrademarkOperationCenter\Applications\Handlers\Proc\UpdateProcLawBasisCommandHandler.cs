﻿using AutoMapper;
using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class UpdateProcLawBasisCommandHandler(
    IMapper mapper,
    IHttpContextAccessor httpContextAccessor,
    ITrademarkLawBasisRepository trademarkLawBasisRepository, 
    IFileListARepository fileListARepository, 
    HuaweiObsClient huaweiObsClient) 
    : IRequestHandler<UpdateProcLawBasisCommand>
{
    public async Task Handle(UpdateProcLawBasisCommand request, CancellationToken cancellationToken)
    {
        var dto = request.Dto;
        var userId = (httpContextAccessor.HttpContext?.User).GetUserId();
        
        var lawBasis = await trademarkLawBasisRepository.GetAsync(request.Id, cancellationToken);
        if (lawBasis is null)
        {
            throw new NotFoundException(request.Id, "法律依据与事实");
        }

        if (dto.FileId != lawBasis.FileId)
        {
            if (dto.FileId.HasValue)
            {
                var fileInfo = await fileListARepository.GetAsync(dto.FileId.Value, cancellationToken);
                if (fileInfo is null)
                {
                    throw new NotFoundException(dto.FileId, "证据材料");
                }
                
                lawBasis.Url = huaweiObsClient
                    .GenerateTemporaryUrl(fileInfo.GetObjectName(), fileInfo.Bucket, TimeSpan.FromDays(7 * 365)).SignUrl;
                lawBasis.FileName = fileInfo.RealName;
            }
            else
            {
                lawBasis.Url = string.Empty;
                lawBasis.FileName = string.Empty;
            }
        }

        mapper.Map(dto, lawBasis);
        lawBasis.Updater = userId;
        lawBasis.UpdateTime=DateTime.Now;

        await trademarkLawBasisRepository.UpdateAsync(lawBasis, cancellationToken);
    }
}