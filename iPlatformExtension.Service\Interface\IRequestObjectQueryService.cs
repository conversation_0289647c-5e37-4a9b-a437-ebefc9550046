﻿using FreeSql;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Service.Interface;

public interface IRequestObjectQueryService : IFreeSqlQueryService, ITransientDependency
{
    internal ISystemDictionaryRepository SystemDictionaryRepository { get; }
    
    async Task<RequestObjectInfoDto?> GetRequestObjectInfoAsync(string requestObjectId)
    {
        var requestObjectInfo = await DbQuery.Select<CusRequestObject>().WithLock()
            .Where(requestObject => requestObject.RequestObjectId == requestObjectId)
            .FirstAsync(o => new RequestObjectInfoDto()
            {
                RequestObjectId = o.RequestObjectId,
                Address = o.AddressCn,
                BankName = o.BankName,
                IdentifierNumber = o.IdentifyNumber,
                InvoiceTitle = o.InvoicesTitle,
                TaxpayerQualificationType = new KeyValuePair<string, string>(SystemDictionaryName.TaxpayerType.ToSqlStringConstant(), o.IsGeneralTaxpayer),
                Name = o.RequestObjectName,
                Telephone = o.Tel,
                AccountNo = o.AccountNo,
                IsOutbound = o.IsOutbound
            });

        if (requestObjectInfo is not null)
        {
            requestObjectInfo.TaxpayerQualificationType =
                await SystemDictionaryRepository.GetChineseKeyValueAsync(requestObjectInfo.TaxpayerQualificationType);
        }
        else
        {
            throw new NotFoundException(requestObjectId, "请款对象");
        }

        return requestObjectInfo;
    }

    Task<IEnumerable<RequestObjectReDto>> GetRequestObjectListByCustomerAsync(List<string> customerIds)
    {
        var requestObjectQuery = new RequestObjectQueryBuilder(DbQuery).AddCustomerIds(customerIds).Build();
        return QueryRequestObjectsAsync(requestObjectQuery);
    }

    Task<IEnumerable<RequestObjectReDto>> GetRequestObjectsByBillIdAsync(string billId)
    {
        var requestObjectQuery = new RequestObjectQueryBuilder(DbQuery).AddBillId(billId).Build();
        return QueryRequestObjectsAsync(requestObjectQuery);
    }

    Task<IEnumerable<RequestObjectReDto>> GetRequestObjectsByNameAsync(string name)
    {
        var requestObjectQuery = new RequestObjectQueryBuilder(DbQuery).AddRequestObjectName(name).Build();
        return QueryRequestObjectsAsync(requestObjectQuery.Take(30));
    }

    private static async Task<IEnumerable<RequestObjectReDto>> QueryRequestObjectsAsync(ISelect<CusRequestObject> requestObjectQuery)
    {
        var requestObjectInfos = await requestObjectQuery.ToListAsync(requestObject => new RequestObjectReDto()
        {
            IsGeneralTaxpayer = requestObject.IsGeneralTaxpayer,
            SettlementCurrency = requestObject.SettlementCurrency,
            BillLanguage = requestObject.BillLanguage,
            Value = requestObject.RequestObjectId,
            TextZhCn =requestObject.RequestObjectName,
            TextEnUs = requestObject.RequestObjectName,
            TextJaJp = requestObject.RequestObjectName,
            InvoicesTitle = requestObject.InvoicesTitle,
            IdentifyNumber = requestObject.IdentifyNumber,
            AddressCn = requestObject.AddressCn,
            BankName = requestObject.BankName,
            InvoicesCode = requestObject.InvoicesCode,
            IsEnabled = requestObject.IsEnabled,
            AccountNo = requestObject.AccountNo,
            Tel = requestObject.Tel,
            IsOutbound = requestObject.IsOutbound
        });

        return requestObjectInfos.DistinctBy(info => info.Value).ToArray();
    }
        

    internal readonly struct RequestObjectQueryBuilder
    {
        private readonly ISelect<CusRequestObject> _requestObjectQuery;

        private readonly ISelect<CusCustomer> _customerQuery;

        private const string RequestObjectAlias = nameof(CusRequestObject);

        private const string CustomerAlias = nameof(CusCustomer);

        private const string BillInfoAlias = nameof(BillInfo);

        public RequestObjectQueryBuilder(IFreeSql freeSql)
        {
            _requestObjectQuery =
                freeSql.Select<CusRequestObject>().As(RequestObjectAlias).WithLock();
            _customerQuery = freeSql.Select<CusCustomer>().As(CustomerAlias)
                .WithLock();
        }

        internal RequestObjectQueryBuilder AddCustomerIds(IEnumerable<string>? customerIds)
        {
            if (customerIds is not null)
            {
                // if (customerIds.Any())
                // {
                //     var customerIdSet = new HashSet<string>(customerIds.Count());
                //     var parentIds = customerIds;
                //     while (parentIds.Any())
                //     {
                //         customerIdSet.UnionWith(parentIds);
                //         var ids = parentIds;
                //         parentIds = await _freeSql.Select<CusCustomer>().WithLock()
                //             .Where(customer => ids.Contains(customer.CustomerId) && customer.ParentId != null)
                //             .ToListAsync(customer => customer.CustomerId);
                //     }
                //
                //     var childrenIds = customerIds;
                //     while (childrenIds.Any())
                //     {
                //         customerIdSet.UnionWith(childrenIds);
                //         var ids = childrenIds;
                //         childrenIds=await _freeSql.Select<CusCustomer>().WithLock().Where(customer => ids.Contains(customer.ParentId) && customer.ParentId != null)
                //     }
                // }

                var customerQuery = _customerQuery;
                _requestObjectQuery
                    .Where(requestObject => customerQuery.Where(customer => 
                        (customer.CustomerId == requestObject.CustomerId || customer.ParentId == requestObject.CustomerId) && customerIds.Contains(customer.CustomerId))
                        .Any())
                    .Where(requestObject => !string.IsNullOrEmpty(requestObject.RequestObjectCode));
            }

            return this;
        }

        internal RequestObjectQueryBuilder AddRequestObjectName(string? requestObjectName)
        {
            _requestObjectQuery.WhereDynamicFilter(
                requestObjectName.BuildLikeDynamicFilterInfo(nameof(CusRequestObject.RequestObjectName),
                    RequestObjectAlias));

            return this;
        }

        internal RequestObjectQueryBuilder AddBillId(string? billId)
        {
            if (string.IsNullOrWhiteSpace(billId)) return this;
            var customerQuery = _customerQuery;
            _requestObjectQuery.Where(requestObject =>
                customerQuery
                    .InnerJoin<BillInfo>((customer, billInfo) => billInfo.CustomerId == customer.CustomerId)
                    .WhereDynamicFilter(billId.BuildEqualsDynamicFilter(nameof(BillInfo.BillId), BillInfoAlias))
                    .Any(customer =>
                        customer.CustomerId == requestObject.CustomerId ||
                        customer.ParentId == requestObject.CustomerId));

            return this;
        }

        internal RequestObjectQueryBuilder AddIsEnable(bool isEnable = true)
        {
            _requestObjectQuery.WhereDynamicFilter(
                isEnable.BuildEqualsDynamicFilter(nameof(CusRequestObject.IsEnabled), RequestObjectAlias));

            return this;
        }

        internal ISelect<CusRequestObject> Build() => AddIsEnable()._requestObjectQuery;
    }
}