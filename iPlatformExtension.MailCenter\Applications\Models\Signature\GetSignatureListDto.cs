﻿﻿using System;

namespace iPlatformExtension.MailCenter.Applications.Models.Signature
{
    /// <summary>
    /// 签名列表DTO
    /// </summary>
    public class GetSignatureListDto
    {
        /// <summary>
        /// 签名ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 签名名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
    }
}
