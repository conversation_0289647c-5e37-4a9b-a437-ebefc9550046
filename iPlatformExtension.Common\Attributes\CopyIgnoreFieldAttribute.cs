﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.Attributes
{
    /// <summary>
    /// 忽略字段复制功能
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter | AttributeTargets.All, AllowMultiple = true)]
    public class CopyIgnoreFieldAttribute : Attribute
    {
        public bool IsIngore { get; }

        public CopyIgnoreFieldAttribute(bool isIngore = true)
        {
            IsIngore = isIngore;
        }
    }
}
