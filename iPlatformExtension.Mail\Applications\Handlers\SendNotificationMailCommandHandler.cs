using iPlatformExtension.Common.Clients.Mail;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Mail.Applications.Commands;
using iPlatformExtension.Mail.Infrastructure;
using iPlatformExtension.Model.BaseModel;
using MailKit.Net.Smtp;
using MediatR;
using Microsoft.Extensions.Options;
using MimeKit;

namespace iPlatformExtension.Mail.Applications.Handlers;

internal sealed class SendNotificationMailCommandHandler(
    IServiceProvider provider,
    IFreeSql freeSql,
    IOptionsMonitor<MailAccount> accountOptions) :
    IRequestHandler<SendNotificationMailCommand>
{
    public async Task Handle(SendNotificationMailCommand request, CancellationToken cancellationToken)
    {
        var message = request.Message;
        var senderOptions = accountOptions.Get(message.Sender);
        var smtpClient = provider.GetRequiredKeyedService<SmtpClient>(message.Sender);

        var receiverMailBoxAddresses = message.Receivers.Count > 0
            ? await freeSql.Select<SysUserInfo>().WithLock()
                .Where(userInfo => userInfo.IsEnabled == true)
                .Where(userInfo => !string.IsNullOrWhiteSpace(userInfo.Email))
                .WhereDynamic(message.Receivers.BuildContainsDynamicFilter(nameof(SysUserInfo.UserId)))
                .ToListAsync(userInfo => new MailboxAddress(userInfo.CnName, userInfo.Email), cancellationToken)
            : [];
        receiverMailBoxAddresses.AddRange(message.ReceiverAccounts.Select(account => new MailboxAddress(account.DisplayName, account.MailBoxAddress)));

        var attachments = message.Attachments.Select(mailAttachment =>
        {
            var attachment = new MimePart(MimeTypes.GetMimeType(mailAttachment.FileName))
            {
                Content = new MimeContent(new MemoryStream(mailAttachment.Data.ToByteArray())),
                ContentDisposition = new ContentDisposition(ContentDisposition.Attachment)
            };
            attachment.ContentDisposition.Parameters.Add(new Parameter("filename", mailAttachment.FileName)
            {
                EncodingMethod = ParameterEncodingMethod.Rfc2047
            });
            attachment.ContentType.Parameters.Add(new Parameter("name", mailAttachment.FileName)
            {
                EncodingMethod = ParameterEncodingMethod.Rfc2047
            });

            return attachment;
        }).ToList();
        
        foreach (var receiverMailBoxAddress in receiverMailBoxAddresses)
        {
            var mailMessage = new MimeMessage();
            
            mailMessage.To.Add(receiverMailBoxAddress);
            mailMessage.From.Add(new MailboxAddress(message.Sender, senderOptions.Username));
    
            var bodyBuilder = new BodyBuilder();
            attachments.ForEach(attachment => bodyBuilder.Attachments.Add(attachment));
    
            if (message.BodyTemplate.TryGetMailTemplate(out var template))
            {
                mailMessage.Subject = template.Subject;
                
                var body = template.NeedToFormatReceiver ? string.Format(template.Body, receiverMailBoxAddress.Name) : template.Body;
                if (template.IsHtmlBody)
                {
                    bodyBuilder.HtmlBody = body;
                }
                else
                {
                    bodyBuilder.TextBody = body;
                }
            }
            else if (!string.IsNullOrWhiteSpace(message.BodyText))
            {
                mailMessage.Subject = message.Subject;
                if (message.IsHtmlBody)
                {
                    bodyBuilder.HtmlBody = message.BodyText;
                }
                else
                {
                    bodyBuilder.TextBody = message.BodyText;
                }
            }
    
            mailMessage.Body = bodyBuilder.ToMessageBody();
            
            await smtpClient.SendAsync(mailMessage, cancellationToken);
        }
        
        
    }
}