using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_form_dynamic_config", DisableSyncStructure = true)]
	public partial class SysFormDynamicConfig {

		[ Column(Name = "obj_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ObjId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "business_type_id", StringLength = 50)]
		public string BusinessTypeId { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "default_container_id", StringLength = 50)]
		public string DefaultContainerId { get; set; }

		[ Column(Name = "dynamic_form_code", StringLength = 50)]
		public string DynamicFormCode { get; set; }

		[ Column(Name = "id", IsIdentity = true)]
		public int Id { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
