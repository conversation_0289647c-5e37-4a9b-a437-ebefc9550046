﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.Applicant;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Applicant;

internal sealed class ApplicantDetailQueryHandler(IFreeSql freeSql) : 
    IRequestHandler<ApplicantDetailQuery, ApplicantDetailDto>
{
    public async Task<ApplicantDetailDto> Handle(ApplicantDetailQuery request, CancellationToken cancellationToken)
    {
        var (applicantId, caseId) = request;
        var applicant = await freeSql.Select<CusApplicant>(applicantId).WithLock()
            .ToOneAsync(applicant => new ApplicantDetailDto
            {
                ApplicantId = applicant.ApplicantId,
                CountryId = applicant.CountryId,
                ApplicantNameCn = applicant.ApplicantNameCn,
                ApplicantNameEn = applicant.ApplicantNameEn,
                AddressCn = string.Empty,
                AddressEn = null,
                TypeId = applicant.TypeId,
                IsChineseIdentity = applicant.IsChineseIdentity,
                PostCode = string.Empty,
                CertificationType = applicant.CardType,
                CertificationNumber = applicant.CardNo
            }, cancellationToken);

        if (applicant is null)
        {
            throw new NotFoundException(applicantId, "申请人信息");
        }
        
        if (string.IsNullOrWhiteSpace(caseId)) return applicant;
        
        var address = await freeSql.Select<CusAddressList>().WithLock()
            .InnerJoin<CaseApplicantList>((address, applicantList) => address.AddressId == applicantList.AddressId)
            .Where<CaseApplicantList>(caseApplicant => caseApplicant.ApplicantId == applicantId)
            .Where<CaseApplicantList>(caseApplicant => caseApplicant.CaseId == caseId)
            .Where<CaseApplicantList>(caseApplicant => caseApplicant.IsRepresent == true)
            .FirstAsync(cancellationToken);

        applicant.AddressCn = address.AddressCn ?? string.Empty;
        applicant.AddressEn = address.AddressEn;
        applicant.PostCode = address.Postcode;

        return applicant;
    }
}