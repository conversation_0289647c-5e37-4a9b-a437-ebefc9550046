using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_batch_fee_list", DisableSyncStructure = true)]
	public partial class CaseBatchFeeList {

		[ Column(Name = "obj_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ObjId { get; set; }

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "balance_way", StringLength = 50)]
		public string BalanceWay { get; set; }

		[ Column(Name = "batch_id", StringLength = 50)]
		public string BatchId { get; set; }

		[ Column(Name = "bill_no", StringLength = 50)]
		public string BillNo { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "discount", StringLength = 50)]
		public string Discount { get; set; }

		[ Column(Name = "error_columns", StringLength = 200)]
		public string ErrorColumns { get; set; }

		[ Column(Name = "error_message", StringLength = 200)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		[ Column(Name = "fee_type_name_id", StringLength = 50)]
		public string FeeTypeNameId { get; set; }

		[ Column(Name = "is_officer")]
		public bool? IsOfficer { get; set; }

		[ Column(Name = "is_request")]
		public bool? IsRequest { get; set; }

		[ Column(Name = "office_name_zh_cn", StringLength = 500)]
		public string OfficeNameZhCn { get; set; }

		[ Column(Name = "officer_status", StringLength = 50)]
		public string OfficerStatus { get; set; }

		[ Column(Name = "pay_officer_date")]
		public DateTime? PayOfficerDate { get; set; }

		[ Column(Name = "pay_officer_legal_date")]
		public DateTime? PayOfficerLegalDate { get; set; }

		[ Column(Name = "payment_agency", StringLength = 500)]
		public string PaymentAgency { get; set; }

		[ Column(Name = "payment_name", StringLength = 50)]
		public string PaymentName { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "proc_property", StringLength = 100)]
		public string ProcProperty { get; set; }

		[ Column(Name = "receive_date")]
		public DateTime? ReceiveDate { get; set; }

		[ Column(Name = "receive_rule", StringLength = 50)]
		public string ReceiveRule { get; set; }

		[ Column(Name = "receive_status", StringLength = 50)]
		public string ReceiveStatus { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "standard_amount", DbType = "money")]
		public decimal? StandardAmount { get; set; }

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

	}

}
