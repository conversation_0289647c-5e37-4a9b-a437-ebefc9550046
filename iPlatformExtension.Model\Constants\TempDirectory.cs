﻿namespace iPlatformExtension.Model.Constants;

/// <summary>
/// 缓存目录
/// </summary>
public static class TempDirectory
{
    /// <summary>
    /// 默认值
    /// </summary>
    public const string Default = "temp";

    /// <summary>
    /// 当前目录
    /// </summary>
    public const string Current = ".";

    /// <summary>
    /// 费项目录
    /// </summary>
    public const string Fees = "fees";
    
    /// <summary>
    /// 国内商标提成
    /// </summary>
    public const string DomesticTrademarkCommission = "domestic-trademark-commission";
    
    /// <summary>
    /// 出口商标提成
    /// </summary>
    public const string ForeignTrademarkCommission = "foreign-trademark-commission";
    
    /// <summary>
    /// 国内商标胜诉奖励
    /// </summary>
    public const string DomesticTrademarkReward = "domestic-trademark-reward";
    
    /// <summary>
    /// 待分配外所
    /// </summary>
    public const string NotAssignedForeignSupplier = "not-assigned-foreign-supplier";
}