using System.Net.Http.Json;
using iPlatformExtension.Model.Dto;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Clients.BladeCommon;

public sealed class EnterpriseWechatNotificationApplicationCodeHandler(IOptionsMonitor<BladeCommonClientOptions> clientOptions) 
    : DelegatingHandler
{
    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var options = clientOptions.CurrentValue;

        switch (request.Content)
        {
            case JsonContent {Value: EnterpriseWechatNotification notification}:
                notification.ApplicationCode = options.EnterpriseWechatServiceCode;
                break;
            case JsonContent {Value: EnterpriseWechatNotificationWithdraw notificationWithdraw}:
                notificationWithdraw.ApplicationCode = options.EnterpriseWechatServiceCode;
                break;
        }
        
        
        return base.SendAsync(request, cancellationToken);
    }
}