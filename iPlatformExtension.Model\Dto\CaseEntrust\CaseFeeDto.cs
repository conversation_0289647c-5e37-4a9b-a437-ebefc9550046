﻿using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;


namespace iPlatformExtension.Model.Dto.CaseEntrust
{
    public class CaseFeeDto
    {

        /// <summary>
        /// 费用主键ID
        /// </summary>
        public string FeeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

        /// <summary>
        /// 费减金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 任务
        /// </summary>
        public string? ctrlPorID { get; set; }


        /// <summary>
        /// 产品类型ID
        /// </summary>
        public string? ProductId { get; set; }

        /// <summary>
        /// 实例产品ID
        /// </summary>
        public string? orderProductId { get; set; }

        /// <summary>
        ///费项类型ID
        /// </summary>
        public string feeLineId { get; set; }


        /// <summary>
        ///费用名称
        /// </summary>
        [StringLength(255)]
        public string feeName { get; set; }

        /// <summary>
        ///费用类别
        /// </summary>
        public string feeType { get; set; }

        /// <summary>
        ///币种
        /// </summary>
        public string currencyId { get; set; }

        /// <summary>
        ///请款状态
        /// </summary>
        public string payStatus { get; set; }

        /// <summary>
        ///备注
        /// </summary>
        [StringLength(255)]
        public string? remark { get; set; }

        /// <summary>
        /// 标准金额
        /// </summary>
        [ Column(DbType = "decimal(18,0)")]
        public decimal StandardAmount { get; set; }

        /// <summary>
        /// 就业务系统的费项id
        /// </summary>
        public string? CaseFeeId { get; set; }

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool IsEnable { get; set; } = true;

        /// <summary>
        /// 费减比例%
        /// </summary>
        public string? FeeReduce { get; set; }
    }
}

