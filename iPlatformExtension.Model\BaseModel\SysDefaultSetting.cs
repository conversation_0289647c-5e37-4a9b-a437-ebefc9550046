using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_default_setting", DisableSyncStructure = true)]
	public partial class SysDefaultSetting {

		[ Column(Name = "aspx_code", StringLength = 50)]
		public string AspxCode { get; set; }

		[ Column(Name = "case_type", StringLength = 50)]
		public string CaseType { get; set; }

		[ Column(Name = "column_id", StringLength = 50)]
		public string ColumnId { get; set; }

		[ Column(Name = "column_type", StringLength = 50)]
		public string ColumnType { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "key_id", StringLength = 50, IsNullable = false)]
		public string KeyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "text_en_us", StringLength = 500)]
		public string TextEnUs { get; set; }

		[ Column(Name = "text_id", StringLength = 50)]
		public string TextId { get; set; }

		[ Column(Name = "text_jz_jp", StringLength = 500)]
		public string TextJzJp { get; set; }

		[ Column(Name = "text_zh_cn", StringLength = 500)]
		public string TextZhCn { get; set; }

	}

}
