﻿using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.Withdraw3Years;

internal sealed class Withdraw3YearsOtherInfoResultQueryHandler(ISystemDictionaryRepository dictionaryRepository) : OtherInfoResultNotificationHandlerBase(dictionaryRepository)
{
    protected override ValueTask<bool> MatchAsync(OtherInfoResultQuery query, CancellationToken cancellationToken)
    {
        return new ValueTask<bool>(StringComparer.OrdinalIgnoreCase.Equals(query.CtrlProcId, CtrlProcIds.TrademarkWithdraw3Years));
    }
}