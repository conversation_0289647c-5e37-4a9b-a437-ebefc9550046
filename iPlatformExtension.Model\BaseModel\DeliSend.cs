using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_send", DisableSyncStructure = true)]
	public partial class DeliSend {

		[ Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ProcId { get; set; }

		[ Column(Name = "begin_time")]
		public DateTime? BeginTime { get; set; }

		[ Column(Name = "end_time")]
		public DateTime? EndTime { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "status")]
		public int? Status { get; set; }

		/// <summary>
		/// 电子申请案件编号
		/// </summary>
		[Column(Name = "dianzisqajbh")]
		public string? DeliCaseNo { get; set; }

		/// <summary>
		/// 我方文号
		/// </summary>
		[Column(Name = "volume")]
		public string? Volume { get; set; }
	}

}
