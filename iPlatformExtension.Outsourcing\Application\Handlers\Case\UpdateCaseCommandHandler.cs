﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Commands.Case;
using iPlatformExtension.Outsourcing.Application.Models.Case;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Case;

internal sealed class UpdateCaseCommandHandler(
    IMapper mapper,
    ICaseInfoRepository caseInfoRepository,
    ICaseForeignContactRepository caseForeignContactRepository) : IRequestHandler<UpdateCaseCommand>
{
    public async Task Handle(UpdateCaseCommand request, CancellationToken cancellationToken)
    {
        var (caseId, document) = request;
        var caseInfo = await caseInfoRepository.GetAsync(caseId, cancellationToken);

        if (caseInfo is null)
        {
            return;
        }
        
        var formerSupplierId = caseInfo.ForeginAgencyId;

        var casePatchDto = mapper.Map<CasePatchDto>(caseInfo);
        document.ApplyTo(casePatchDto);
        mapper.Map(casePatchDto, caseInfo);
        
        caseInfo.UpdateTime = DateTime.Now;
        caseInfo.UpdateUserId = UserIds.Administrator;
        
        await caseInfoRepository.UpdateAsync(caseInfo, cancellationToken);
        
        var currentSupplierId = caseInfo.ForeginAgencyId;
        
        if (formerSupplierId != currentSupplierId && !string.IsNullOrWhiteSpace(formerSupplierId))
        {
            await caseForeignContactRepository.DeleteAsync(contact => contact.CaseId == caseId, cancellationToken);
        }

        if (casePatchDto.ContactorIds is not null)
        {
            var olderContacts = await caseForeignContactRepository.Where(contact => contact.CaseId == caseId)
                .ToListAsync(cancellationToken);
            await caseForeignContactRepository.DeleteAsync(olderContacts, cancellationToken);

            if (casePatchDto.ContactorIds.Any())
            {
                var contacts = casePatchDto.ContactorIds.Select(contactId => new CaseForeignContactList
                {
                    ContactId = contactId,
                    CaseId = caseId,
                    Id = Guid.CreateVersion7().ToString()
                }).ToList();
                await caseForeignContactRepository.InsertAsync(contacts, cancellationToken);
            }
        }
    }
}