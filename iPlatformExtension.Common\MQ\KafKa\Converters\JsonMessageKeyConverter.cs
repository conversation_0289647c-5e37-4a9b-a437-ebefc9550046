﻿using System.Text.Json;
using Confluent.Kafka;
using iPlatformExtension.Common.MQ.KafKa.Models;

namespace iPlatformExtension.Common.MQ.KafKa.Converters;

public class JsonMessageKeyConverter(JsonSerializerOptions serializerOptions)
    : IDeserializer<MessageKey>, ISerializer<MessageKey>
{
    public MessageKey Deserialize(ReadOnlySpan<byte> data, bool isNull, SerializationContext context)
    {
        var messageKey = JsonSerializer.Deserialize<MessageKey>(data, serializerOptions);
        return messageKey;
    }

    public byte[] Serialize(MessageKey data, SerializationContext context)
    {
        return JsonSerializer.SerializeToUtf8Bytes(data, serializerOptions);
    }
}