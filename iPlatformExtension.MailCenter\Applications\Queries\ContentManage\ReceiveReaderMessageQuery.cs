﻿using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.ContentManage
{
    /// <summary>
    ///  收件阅读消息
    /// </summary>
    /// <param name="ReaderList"></param>
    /// <param name="OperationType"></param>
    /// <param name="MailId"></param>
    public record ReceiveReaderMessageQuery(List<MailReaderList> ReaderList, OperationTypeEnum OperationType, string MailId = null) : IRequest;

}
