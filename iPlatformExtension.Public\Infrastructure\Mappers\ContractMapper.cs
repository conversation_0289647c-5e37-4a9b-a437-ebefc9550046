﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models.Customer.Contract;

namespace iPlatformExtension.Public.Infrastructure.Mappers;

internal class ContractMapper : Profile
{
    public ContractMapper()
    {
        CreateMap<CrmContractCreateDto, CusContract>()
            .ForMember(contract => contract.SigningDate, expression => expression.MapFrom(dto => dto.SigningDate.ToDateTime(TimeOnly.MinValue)))
            .ForMember(contract => contract.EffectiveDate, expression => expression.MapFrom(dto => dto.EffectiveDate.ToDateTime(TimeOnly.MinValue)))
            .ForMember(contract => contract.EndDate, expression => expression.MapFrom(dto => dto.EndDate.ToDateTime(TimeOnly.MinValue)));
        
        CreateMap<ContractDetailDto, ContractDetail>()
            .ForMember(detail => detail.ContractBusinessType, expression => expression.MapFrom(dto => dto.BusinessType))
            .ForMember(detail => detail.PreSalesUsername, expression => expression.MapFrom(dto => dto.PreSalesUser))
            .ForMember(detail => detail.Remark, expression => expression.Condition(dto => dto.Remark is not null))
            .ForMember(detail => detail.StartDate,
                expression => expression.MapFrom(dto => dto.EffectiveDate.ToDateTime(TimeOnly.MinValue)));
        
        CreateMap<CrmContractPatchDto, CusContract>()
            .ForMember(contract => contract.IsEnabled, expression => expression.MapFrom(dto => !dto.IsVoid))
            .ForMember(contract => contract.InvalidationReason, expression => expression.MapFrom(dto => dto.VoidReason))
            .ForMember(contract => contract.SigningDate, expression => expression.MapFrom(dto => dto.SigningDate.ToDateTime(TimeOnly.MinValue)))
            .ForMember(contract => contract.EffectiveDate, expression => expression.MapFrom(dto => dto.EffectiveDate.ToDateTime(TimeOnly.MinValue)))
            .ForMember(contract => contract.EndDate, expression => expression.MapFrom(dto => dto.EndDate.ToDateTime(TimeOnly.MinValue)));
        
        CreateMap<CusContract, CrmContractPatchDto>()
            .ForMember(dto => dto.IsVoid, expression => expression.MapFrom(contract => !contract.IsEnabled))
            .ForMember(dto => dto.VoidReason, expression => expression.MapFrom(contract => contract.InvalidationReason))
            .ForMember(dto => dto.SigningDate, expression =>
            {
                expression.Condition(contract => contract.SigningDate != null);
                expression.MapFrom(contract => DateOnly.FromDateTime(contract.SigningDate ?? DateTime.MinValue));
            })
            .ForMember(dto => dto.EffectiveDate, expression =>
            {
                expression.Condition(contract => contract.EffectiveDate != null);
                expression.MapFrom(contract => DateOnly.FromDateTime(contract.EffectiveDate ?? DateTime.MinValue));
            })
            .ForMember(dto => dto.EndDate, expression =>
            {
                expression.Condition(contract => contract.EndDate != null);
                expression.MapFrom(contract => DateOnly.FromDateTime(contract.EndDate ?? DateTime.MinValue));
            });

        CreateMap<CreateContractDetailDto, ContractDetail>()
            .ForMember(detail => detail.PreSalesUsername, expression =>
            {
                expression.Condition(dto => dto.PreSalesUser is not null);
                expression.MapFrom(dto => dto.PreSalesUser);
            })
            .ForMember(detail => detail.PreSalesUsername, expression =>
            {
                expression.Condition(dto => string.IsNullOrWhiteSpace(dto.PreSalesUser));
                expression.MapFrom(dto => string.Empty);
            })
            .ForMember(detail => detail.ContractBusinessType, expression => expression.MapFrom(dto => dto.BusinessType))
            .ForMember(detail => detail.StartDate,
                expression => expression.MapFrom(dto => dto.EffectiveDate.ToDateTime(TimeOnly.MinValue)));
    }
}