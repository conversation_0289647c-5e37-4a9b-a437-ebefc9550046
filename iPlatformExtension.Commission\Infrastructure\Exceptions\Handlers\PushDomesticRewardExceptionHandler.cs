﻿using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class PushDomesticRewardExceptionHandler(ILogger<PushDomesticRewardCommand> logger) 
    : IRequestExceptionHandler<PushDomesticRewardCommand, Unit, Exception>
{
    public Task Handle(PushDomesticRewardCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        logger.LogPushingDomesticTrademarkRewardsError(exception);
        state.SetHandled(Unit.Value);
        return Task.CompletedTask;
    }
}