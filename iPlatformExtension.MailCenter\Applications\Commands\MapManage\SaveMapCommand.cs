using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.MapManage;

/// <summary>
/// 保存映射命令
/// </summary>
/// <param name="MapId">映射id</param>
/// <param name="Scope">解析范围</param>
/// <param name="CtrlProcId">任务id</param>
/// <param name="IsEnabled">是否生效</param>
/// <param name="MapValue">提取字符</param>
public record SaveMapCommand(string? MapId,
    string Scope,
    string CtrlProcId,
    int IsEnabled,
    string MapValue) : IRequest, IUnitOfWorkCommandMysql;
