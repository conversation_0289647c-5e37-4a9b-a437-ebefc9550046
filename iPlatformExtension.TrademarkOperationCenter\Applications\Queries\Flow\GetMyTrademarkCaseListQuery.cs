﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow
{
    /// <summary>
    /// 获取自定义标签列表
    /// </summary>
    /// <param name="flowType">流程类型</param>
    /// <param name="flowSubType">流程子类型</param>
    public record GetMyTrademarkCaseListQuery(string flowType, string? flowSubType) : IRequest<IEnumerable<GetMyTrademarkCaseListDto>>;

}
