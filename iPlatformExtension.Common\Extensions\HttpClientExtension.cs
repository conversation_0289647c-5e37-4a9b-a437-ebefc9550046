using Google.Protobuf.WellKnownTypes;
using System.Net.Http.Json;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;
using iPlatformExtension.Common.Logging.HttpClient;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Http;
using Microsoft.Extensions.Http.Logging;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Extensions;

/// <summary>
/// HTTPclient扩展方法
/// </summary>
public static class HttpClientExtension
{
    /// <summary>
    /// 旧业务系统旧接口调用
    /// </summary>
    /// <param name="client">httpClient</param>
    /// <param name="uri">一般处理程序的名称</param>
    /// <param name="call">call参数</param>
    /// <param name="requestParameters">表单参数</param>
    /// <typeparam name="T">响应参数的实体类型</typeparam>
    /// <returns>相应参数对应的DTO</returns>
    public static async Task<T?> PostOldPlatformOldApiAsync<T>(this HttpClient client, string uri, string call, ICollection<KeyValuePair<string, string>> requestParameters)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(call);
        ArgumentNullException.ThrowIfNull(uri);

        requestParameters.Add(new KeyValuePair<string, string>(nameof(call), call));
        var content = new FormUrlEncodedContent(requestParameters);
        var response = (await client.PostAsync(uri, content)).EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<T>();
    }

    /// <summary>
    /// 旧业务系统webapi口调用
    /// </summary>
    /// <param name="client">httpClient</param>
    /// <param name="uri">请求url</param>
    /// <param name="requestParameters">请求参数</param>
    /// <param name="options">json序列化选项参数</param>
    /// <typeparam name="TRequest">请求参数的实体类型</typeparam>
    /// <typeparam name="TResponse">响应参数的对应的实体类型</typeparam>
    /// <returns>响应实体</returns>
    public static async Task<TResponse?> PostJsonAsync<TRequest, TResponse>(this HttpClient client, string uri, TRequest requestParameters, JsonSerializerOptions? options = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(requestParameters);
        ArgumentNullException.ThrowIfNull(uri);
        
        var response = (await client.PostAsJsonAsync(uri, requestParameters, options)).EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<TResponse>(options);
    }


    /// <summary>
    /// 特殊接口调用调用
    /// </summary>
    /// <typeparam name="TResponse">响应参数的对应的实体类型</typeparam>
    /// <param name="client">httpClient</param>
    /// <param name="uri">请求url</param>
    /// <param name="content">特殊的请求体</param>
    /// <returns>响应实体</returns>
    public static async Task<TResponse?> PostAsync<TResponse>(this HttpClient client, string uri, HttpContent content)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(uri);
        var response = (await client.PostAsync(uri, content)).EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<TResponse>();
    }

    public static StringBuilder AddParameter(this StringBuilder uriBuilder, string name, string value)
    {
        uriBuilder.Append($"&{name}={value}");
        if (uriBuilder[0] == '&')
        {
            uriBuilder[0] = '?';
        }
        return uriBuilder;
    }
    
    public static StringBuilder AddParameter<T>(this StringBuilder uriBuilder, T? parameter, [CallerArgumentExpression("parameter")]string? parameterName = null)
    {
        if (parameter is null || string.IsNullOrWhiteSpace(parameterName))
        {
            return uriBuilder;
        }

        return uriBuilder.AddParameter(parameterName, parameter.ToString()!);
    }

    public static IHttpClientBuilder AddHttpClientLogger<TLogger>(this IHttpClientBuilder httpClientBuilder,
        Func<IServiceProvider, TLogger> createLogger) where TLogger : class, IHttpClientLogger
    {
        var services = httpClientBuilder.Services;
        services.AddTransient<TLogger>();

        return httpClientBuilder.AddLogger(createLogger);
    }
    
    public static IHttpClientBuilder AddHttpClientLogger<TLogger>(this IHttpClientBuilder httpClientBuilder) where TLogger : class, IHttpClientLogger
    {
        var services = httpClientBuilder.Services;
        services.AddTransient<TLogger>();

        return httpClientBuilder.AddLogger<TLogger>();
    }

    public static IHttpClientBuilder AddRequestHeaderLogger(this IHttpClientBuilder httpClientBuilder)
    {
        return httpClientBuilder.AddLogger(provider =>
        {
            var options = provider.GetRequiredService<IOptionsMonitor<HttpClientFactoryOptions>>()
                .Get(httpClientBuilder.Name);
            var logger = provider.GetRequiredService<ILogger<RequestHeaderLogger>>();
            var stringBuilderPool = provider.GetRequiredService<ObjectPool<StringBuilder>>();
            return new RequestHeaderLogger(options.ShouldRedactHeaderValue, logger, stringBuilderPool);
        });
    }

    public static Task<HttpResponseMessage> DeleteAsJsonAsync<TValue>(this HttpClient httpClient, Uri requestUri,
        TValue value, JsonSerializerOptions? jsonSerializerOptions = null)
    {
        jsonSerializerOptions ??= JsonSerializerOptions.Web;
        var content = JsonContent.Create(value, null, jsonSerializerOptions);
        var requestMessage = new HttpRequestMessage(HttpMethod.Delete, requestUri)
        {
            Content = content
        };
        
        return httpClient.SendAsync(requestMessage);
    }
    
    public static Task<HttpResponseMessage> DeleteAsJsonAsync<TValue>(this HttpClient httpClient, string requestUri,
        TValue value, JsonSerializerOptions? jsonSerializerOptions = null)
    {
        return httpClient.DeleteAsJsonAsync(new Uri(requestUri), value, jsonSerializerOptions);
    }
    
    public static Task<HttpResponseMessage> HeadAsync(this HttpClient httpClient, Uri requestUri, CancellationToken cancellationToken = default)
    {
        var requestMessage = new HttpRequestMessage(HttpMethod.Head, requestUri);
        return httpClient.SendAsync(requestMessage, cancellationToken);
    }
    
    public static Task<HttpResponseMessage> HeadAsync(this HttpClient httpClient, string requestUri, CancellationToken cancellationToken = default)
    {
        return httpClient.HeadAsync(new Uri(requestUri), cancellationToken);
    }
}