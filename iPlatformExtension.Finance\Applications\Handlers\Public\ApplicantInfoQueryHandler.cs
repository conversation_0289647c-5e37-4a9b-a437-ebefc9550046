using iPlatformExtension.Finance.Applications.Commands.Public;
using iPlatformExtension.Finance.Applications.Queries.Public;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Public;

public sealed class ApplicantInfoQueryHandler : IRequestHandler<ApplicantInfoQuery, IEnumerable<ApplicantInfo>>
{
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="freeSql">数据库查询组件</param>
    public ApplicantInfoQueryHandler(IFreeSql freeSql)
    {
        _freeSql = freeSql;
    }

    /// <summary>
    /// 根据关键字活客户ID查询申请人信息
    /// </summary>
    /// <param name="request">关键子和客户id参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>申请人信息集合</returns>
    public async Task<IEnumerable<ApplicantInfo>> Handle(ApplicantInfoQuery request, CancellationToken cancellationToken)
    {
        var keyword = request.Keyword;
        var customerId = request.CustomerId;
        
        var query = _freeSql.Select<CusApplicant>().WithLock()
            .WhereIf(!string.IsNullOrWhiteSpace(keyword), applicant =>
                applicant.ApplicantNameCn.Contains(keyword!) || applicant.ApplicantNameEn.Contains(keyword!));
        if (!string.IsNullOrWhiteSpace(customerId))
        {
            query.InnerJoin<CusJoinList>((applicant, joinList) => applicant.ApplicantId == joinList.FormObjId)
                .Where<CusJoinList>((applicant, joinList) => joinList.JoinObjId == customerId);
        }
        return await query.Where(applicant => applicant.IsEnabled == true).Page(1, 20)
            .ToListAsync(applicant =>
                new ApplicantInfo(applicant.ApplicantId, applicant.ApplicantNameCn, applicant.ApplicantNameEn), cancellationToken);
    }
}