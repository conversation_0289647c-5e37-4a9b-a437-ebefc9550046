using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "receipt_item", DisableSyncStructure = true)]
	public partial class ReceiptItem {

		[ Column(Name = "info_id", StringLength = 50, IsNullable = false)]
		public string InfoId { get; set; }

		[ Column(Name = "item_id", StringLength = 50, IsNullable = false)]
		public string ItemId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "itemamt", StringLength = 50)]
		public string Itemamt { get; set; }

		[ Column(Name = "itemcnt", StringLength = 50)]
		public string Itemcnt { get; set; }

		[ Column(Name = "itemcode", StringLength = 50)]
		public string Itemcode { get; set; }

		[ Column(Name = "itemname", StringLength = 200)]
		public string Itemname { get; set; }

	}

}
