﻿using iPlatformExtension.Model.Enum;

namespace iPlatformExtension.MailCenter.Applications.Models.ContentManage
{
    public class SetMailTagDto
    {
        public string MailId { get; set; }
        public List<string> MailTagIds { get; set; }
        /// <summary>
        /// 邮件类型:Receive:收件标签,Send:发件标签.
        /// </summary>
        public string MailType { get; set; } = SysEnum.MailType.Receive.ToString();
    }
}
