﻿using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal abstract class BuildProcApplicantFilesCommandHandler(
    IFreeSql freeSql, 
    HuaweiObsClient huaweiObsClient,
    IHttpContextAccessor httpContextAccessor) : 
    BuildDeliveryInitializeFilesCommandHandler
{
     protected override async Task HandleAsync(BuildDeliveryInitializeFilesCommand request, CancellationToken cancellationToken)
    {
        var (procInfo, applicants, _, files) = request;
        
        var applicant = applicants.FirstOrDefault(deliApplicant => deliApplicant.DeliveryBusinessType is not null);
        if (applicant is null) return;

        var applicantFiles = await freeSql.Select<CaseFile>().WithLock()
            .Include(file => file.FileDescription!.FileType)
            .Where(file => file.ObjId == applicant.ApplicantId).ToListAsync(cancellationToken);

        var fileIds = applicantFiles.Select(file => Convert.ToInt32(file.FileNo[4..]));
        var fileInfos = await freeSql.Select<FileListA>().WithLock()
            .Where(a => fileIds.Contains(a.Id)).ToDictionaryAsync(a => a.Id, cancellationToken);

        if (fileInfos.Count == 0) return;

        var currentUser = httpContextAccessor.HttpContext?.User;
        
        for (var i = 0; i < applicantFiles.Count; i++)
        {
            var file = applicantFiles[i];
            var deliverFile = new DeliFiles()
            {
                FileDesc = file.FileDescription?.FileDescZhCn,
                FileEx = file.FileEx,
                FileName = file.FileName,
                FileNo = file.FileNo,
                IsShow = true,
                FileCode = file.FileDescription?.TextCode,
                ProcId = procInfo.ProcId,
                BaseFileType = file.FileDescription?.FileType?.FileTypeZhCn,
                CaseFileId = file.FileId,
                Uploader = currentUser?.GetGivenName() ?? throw new NotFoundException(currentUser.GetUserId(), "用户信息"),
            };

            var fileNo = Convert.ToInt32(file.FileNo[4..]);
            if (fileInfos.TryGetValue(fileNo, out var fileInfo) && fileInfo is not null)
            {
                deliverFile.Url = huaweiObsClient
                    .GenerateTemporaryUrl(fileInfo.GetObjectName(), fileInfo.Bucket, TimeSpan.FromDays(7 * 365)).SignUrl;
                deliverFile.UploadTime = fileInfo.InputTime;
                deliverFile.Id = fileInfo.Id;
            }
            
            files.Add(deliverFile);
        }
        
    }
}