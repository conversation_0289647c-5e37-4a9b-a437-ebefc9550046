﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.Customer;

namespace iPlatformExtension.Repository.Interface;

public interface IBigClientRepository : IBaseRepository<SysCustomerBigClient, string>, IScopeDependency, IRedisCacheableRepository<BigClientKey, SysCustomerBigClient>
{
    private static IEqualityComparer<BigClientKey> EqualityComparer { get; } = new BigClientKeyEqualityComparer();

    IEqualityComparer<BigClientKey>? ICacheableRepository<BigClientKey, SysCustomerBigClient>.KeyEqualityComparer => EqualityComparer;

    async Task<IEnumerable<SysCustomerBigClient>> ICacheableRepository<BigClientKey, SysCustomerBigClient>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.ToListAsync(cancellationToken);
    }

    Task<SysCustomerBigClient?> ICacheableRepository<BigClientKey, SysCustomerBigClient>.GetValueFromDbAsync(BigClientKey key, CancellationToken cancellationToken)
    {
        var (customerId, caseDirection, isEnabled) = key;
        return Where(client => client.CustomerId == customerId)
            .Where(client => client.CaseDirection == caseDirection)
            .Where(client => client.IsEnabled == isEnabled)
            .ToOneAsync(cancellationToken)!;
    }

    BigClientKey ICacheableRepository<BigClientKey, SysCustomerBigClient>.GenerateKey(SysCustomerBigClient value)
    {
        return new BigClientKey(value.CustomerId, value.CaseDirection, value.IsEnabled);
    }
    
    private sealed class BigClientKeyEqualityComparer : IEqualityComparer<BigClientKey>
    {
        public bool Equals(BigClientKey x, BigClientKey y)
        {
            var (customerId, caseDirection, isEnabled) = x;
            var (customerId2, caseDirection2, isEnabled2) = y;
            
            return customerId == customerId2 && caseDirection == caseDirection2 && isEnabled == isEnabled2;
        }

        public int GetHashCode(BigClientKey obj)
        {
            var (customerId, caseDirection, isEnabled) = obj;
            return HashCode.Combine(customerId, caseDirection, isEnabled);
        }
    }
}