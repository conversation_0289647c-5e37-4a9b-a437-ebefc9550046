﻿using System.Configuration;
using System.Runtime.CompilerServices;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.ServiceDiscovery;
using Nacos.V2;

namespace iPlatformExtension.Common.ServiceDiscovery.Nacos;

internal sealed class NacosHttpsServiceEndpointProvider(
    string serviceName,
    string groupName,
    INacosNamingService namingService,
    NacosServiceEndpointReloadToken reloadToken)
    : NacosServiceEndpointProviderBase(groupName, serviceName, reloadToken, namingService)
{

    protected override async IAsyncEnumerable<ServiceEndpoint> ProvideEndpointsAsync([EnumeratorCancellation] CancellationToken cancellationToken)
    {
        var instances = await _namingService.GetAllInstances(_serviceName, _groupName);
        
        foreach (var instance in instances)
        {
            var featureCollection = new FeatureCollection(3);
            featureCollection.Set(instance);

            var metaData = instance.Metadata;
            featureCollection.Set(metaData);
            
            if (metaData.TryGetValue("secure", out var secureValue) && Convert.ToBoolean(secureValue))
            {
                featureCollection.Set(new NacosServiceNameFeature(_groupName, _serviceName, instance.ToInetAddr(), Uri.UriSchemeHttps));
                yield return ServiceEndpoint.Create(instance.GetEndPoint(), featureCollection);
            }

            if (metaData.TryGetValue("gRPC_port", out var grpcPort))
            {
                foreach (var endPoint in instance.GetEndPoints(grpcPort))
                {
                    featureCollection.Set(new NacosServiceNameFeature(_groupName, _serviceName, endPoint.ToString() ?? string.Empty, Uri.UriSchemeHttps));
                    yield return ServiceEndpoint.Create(endPoint, featureCollection);
                }
            }

            if (!metaData.TryGetValue("ASPNETCORE_HTTPS_PORTS", out var portString)) continue;
            
            var ports = portString.Split(';',
                StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries);
            foreach (var endPoint in instance.GetEndPoints(ports))
            {
                featureCollection.Set(new NacosServiceNameFeature(_groupName, _serviceName, endPoint.ToString() ?? string.Empty, Uri.UriSchemeHttps));
                yield return ServiceEndpoint.Create(endPoint, featureCollection);
            }
        }
    }
}