﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;

/// <summary>
/// 尼斯分类
/// </summary>
/// <param name="Id">尼斯分类id</param>
/// <param name="Level">级别</param>
/// <param name="CategoryNumber">尼斯分类编码</param>
/// <param name="CategoryName">尼斯分类名称</param>
/// <param name="ParentNumber">尼斯分类父级编码</param>
public record NiceCategoryDto(string Id, int? Level, string CategoryNumber, string CategoryName, string? ParentNumber)
{
    /// <summary>
    /// 是否被选中
    /// </summary>
    public bool Selected { get; set; }

    /// <summary>
    /// 用户自定义分类
    /// </summary>
    public IEnumerable<string> CustomCategories { get; set; } = Array.Empty<string>();
}
