﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models;

namespace iPlatformExtension.Public.Infrastructure.Extensions;

internal static class KeyTypeExtension
{
    public static string GetKey(this SysDictionary dictionary, KeyType keyType)
    {
        return keyType switch
        {
            KeyType.Id => dictionary.DictionaryId,
            KeyType.Value => dictionary.Value,
            _ => throw new ArgumentOutOfRangeException(nameof(keyType), keyType, "keyType的值不在给定枚举范围中")
        };
    }
}