﻿namespace iPlatformExtension.Public.Applications.Models.Proc;

/// <summary>
/// 费项关联任务名称信息
/// </summary>
public class FeeNameCtrlProcInfo
{
    /// <summary>
    /// 任务名称id
    /// </summary>
    public string CtrlProcId { get; set; } = null!;

    /// <summary>
    /// 任务名称
    /// </summary>
    public string ProcName { get; set; } = string.Empty;

    /// <summary>
    /// 页签
    /// </summary>
    public string Tab { get; set; } = string.Empty;

    /// <summary>
    /// 案件方向
    /// </summary>
    public string CaseDirections { get; set; } = string.Empty;

    /// <summary>
    /// 任务标识
    /// </summary>
    public string Attribute { get; set; } = string.Empty;

    /// <summary>
    /// 是否有效
    /// </summary>
    public string IsEnabled { get; set; } = string.Empty;
}
