﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_send_flow", DisableSyncStructure = true)]
	public partial class MailSendFlow {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 收件人名称
		/// </summary>
		[Column(Name = "display_name", StringLength = 50)]
		public string DisplayName { get; set; }

		/// <summary>
		/// 原邮件id
		/// </summary>
		[Column(Name = "mail_id", StringLength = 50, IsNullable = false)]
		public string MailId { get; set; }

		/// <summary>
		/// -1:取消,0:待处理,1:排队中,5000:处理完成;500:出错
		/// </summary>
		[Column(Name = "status", DbType = "int")]
		public int? Status { get; set; }

		/// <summary>
		/// 承办人
		/// </summary>
		[Column(Name = "undertake_user_id", StringLength = 50)]
		public string UndertakeUserId { get; set; }

		[Column(Name = "update_by", StringLength = 50)]
		public string UpdateBy { get; set; }

		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

		[Column(Name = "version")]
		public string Version { get; set; }

        [Column(Name = "audit_user")]
        public string AuditUser { get; set; }

		[Column(Name = "discard_time", DbType = "datetime")]
		public DateTime? DiscardTime { get; set; }

    }

}
