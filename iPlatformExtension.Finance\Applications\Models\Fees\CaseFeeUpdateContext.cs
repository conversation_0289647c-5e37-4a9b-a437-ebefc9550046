﻿using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Models.Fees;

internal sealed class CaseFeeUpdateContext(FinanceOperation updateFeesOperation, string feeId, FeeUpdateInfoDto updateInfo) : INotification
{
    public FinanceOperation UpdateFeesOperation { get; private set; } = updateFeesOperation;

    public string FeeId { get; private set; } = feeId;

    public FeeUpdateInfoDto UpdateInfoDto { get; private set; } = updateInfo;

    public FeesUpdateResult FeesUpdateResult { get; set; } = default!;

    public void Deconstruct(out string feeId, out FinanceOperation operation, out FeeUpdateInfoDto updateInfo)
    {
        feeId = FeeId;
        operation = UpdateFeesOperation;
        updateInfo = UpdateInfoDto;
    }
}