﻿using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class LaunchDeliveryValidateCommandHandler(IMediator mediator)
    : IRequestHandler<LaunchDeliveryValidateCommand, BatchDeliveryValidateResult>
{
    public async Task<BatchDeliveryValidateResult> Handle(LaunchDeliveryValidateCommand request, CancellationToken cancellationToken)
    {
        var procIds = request.ProcIds;

        var deliveryList = await mediator.Send(new BatchDeliveryValidationQuery(procIds), cancellationToken);

        var validResult = await mediator.Send(new MultipartCtrlProcValidateCommand(deliveryList), cancellationToken);
        if (!validResult.Success)
        {
            return validResult;
        }

        validResult = await mediator.Send(new UndertakerValidateCommand(deliveryList), cancellationToken);
        if (!validResult.Success)
        {
            return validResult;
        }

        validResult = deliveryList.Where(dto =>
        {
            var deliveryStatus = (DeliveryStatus) dto.DeliveryStatus;
            return dto.OperationResult && deliveryStatus != DeliveryStatus.Ready || dto.CurrentNodeCode != "DJ";
        }).Aggregate(validResult, (result, dto) => result.CannotLaunch(dto.ProcNo));

        return validResult;
    }
}