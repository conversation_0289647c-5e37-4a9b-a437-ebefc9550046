using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_form", DisableSyncStructure = true)]
	public partial class RpForm {

		[ Column(Name = "report_form_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ReportFormId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "next_refresh_time")]
		public DateTime? NextRefreshTime { get; set; }

		[ Column(Name = "refresh_frequency_num")]
		public int? RefreshFrequencyNum { get; set; }

		[ Column(Name = "refresh_frequency_unit", StringLength = 50)]
		public string RefreshFrequencyUnit { get; set; }

		[ Column(Name = "refresh_sql", StringLength = -2)]
		public string RefreshSql { get; set; }

		[ Column(Name = "refresh_time")]
		public DateTime? RefreshTime { get; set; }

		[ Column(Name = "report_form_code", StringLength = 50)]
		public string ReportFormCode { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "table_name", StringLength = 50)]
		public string TableName { get; set; }

		[ Column(Name = "title", StringLength = 100)]
		public string Title { get; set; }

	}

}
