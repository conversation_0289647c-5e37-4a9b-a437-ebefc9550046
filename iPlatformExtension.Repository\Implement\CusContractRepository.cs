﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class CusContractRepository(IFreeSql<PlatformFreeSql> freeSql, UnitOfWorkManage<PlatformFreeSql> unitOfWorkManager)
    : DefaultRepository<CusContract, string>(freeSql, unitOfWorkManager), ICusContractRepository;