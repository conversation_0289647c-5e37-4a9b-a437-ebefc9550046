using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_company", DisableSyncStructure = true)]
	public partial class BasCompany {

		/// <summary>
		/// 内部机构ID
		/// </summary>
		[ Column(Name = "company_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CompanyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 中文地址
		/// </summary>
		[ Column(Name = "address_cn", StringLength = 200)]
		public string AddressCn { get; set; }

		[ Column(Name = "address_detail", StringLength = 500)]
		public string AddressDetail { get; set; }

		/// <summary>
		/// 英文地址
		/// </summary>
		[ Column(Name = "address_en", StringLength = 500)]
		public string AddressEn { get; set; }

		/// <summary>
		/// 代理机构ID（未使用？）
		/// </summary>
		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		[ Column(Name = "area", StringLength = 50)]
		public string Area { get; set; }

		[ Column(Name = "city", StringLength = 50)]
		public string City { get; set; }

		/// <summary>
		/// 机构代码
		/// </summary>
		[ Column(Name = "company_code", StringLength = 50)]
		public string CompanyCode { get; set; }

		/// <summary>
		/// 中文名称
		/// </summary>
		[ Column(Name = "company_name_cn", StringLength = 50)]
		public string CompanyNameCn { get; set; }

		/// <summary>
		/// 英文名称
		/// </summary>
		[ Column(Name = "company_name_en", StringLength = 500)]
		public string CompanyNameEn { get; set; }

		/// <summary>
		/// 日文名称
		/// </summary>
		[ Column(Name = "company_name_jp", StringLength = 50)]
		public string CompanyNameJp { get; set; }

		/// <summary>
		/// 联系人
		/// </summary>
		[ Column(Name = "contacter", StringLength = 50)]
		public string Contacter { get; set; }

		/// <summary>
		/// 国家ID
		/// </summary>
		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人（关联用户ID）
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "credit_code", StringLength = 50)]
		public string CreditCode { get; set; }

		[ Column(Name = "crm_value", StringLength = 50)]
		public string CrmValue { get; set; }

		/// <summary>
		/// 所属地区ID
		/// </summary>
		[ Column(Name = "district_id", StringLength = 50)]
		public string? DistrictId { get; set; }

		/// <summary>
		/// 邮箱
		/// </summary>
		[ Column(Name = "email", StringLength = 100)]
		public string Email { get; set; }

		/// <summary>
		/// 创立时间
		/// </summary>
		[ Column(Name = "estab_day")]
		public DateTime? EstabDay { get; set; }

		/// <summary>
		/// 传真
		/// </summary>
		[ Column(Name = "fax", StringLength = 100)]
		public string Fax { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		/// <summary>
		/// 是否管理？（未使用？）
		/// </summary>
		[ Column(Name = "is_manage")]
		public bool IsManage { get; set; } = false;

		/// <summary>
		/// 法人
		/// </summary>
		[ Column(Name = "legal_person", StringLength = 50)]
		public string LegalPerson { get; set; }

		/// <summary>
		/// 要点金额？（未使用？）
		/// </summary>
		[ Column(Name = "point_price", DbType = "money")]
		public decimal? PointPrice { get; set; }

		/// <summary>
		/// 邮编
		/// </summary>
		[ Column(Name = "post_code", StringLength = 50)]
		public string PostCode { get; set; }

		[ Column(Name = "province", StringLength = 50)]
		public string Province { get; set; }

		/// <summary>
		/// 备注（未使用？）
		/// </summary>
		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		/// <summary>
		/// 中文简称
		/// </summary>
		[ Column(Name = "short_name_cn", StringLength = 200)]
		public string ShortNameCn { get; set; }

		/// <summary>
		/// 英国简称（未使用？）
		/// </summary>
		[ Column(Name = "short_name_en", StringLength = 200)]
		public string ShortNameEn { get; set; }

		/// <summary>
		/// 日文简称（未使用？）
		/// </summary>
		[ Column(Name = "short_name_jp", StringLength = 200)]
		public string ShortNameJp { get; set; }

		/// <summary>
		/// 税率
		/// </summary>
		[ Column(Name = "tax_rate", DbType = "money")]
		public decimal? TaxRate { get; set; }

		/// <summary>
		/// 联系电话
		/// </summary>
		[ Column(Name = "tel", StringLength = 100)]
		public string Tel { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人（关联用户ID）
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// 开票人
		/// </summary>
		[Column(Name = "kp_person")]
		public string? Invoicer { get; set; }

		/// <summary>
		/// 收款人
		/// </summary>
		[Column(Name = "sk_person")]
		public string? Payee { get; set; }

		/// <summary>
		/// 复核人
		/// </summary>
		[Column(Name = "fh_person")]
		public string? Reviewer { get; set; }

		/// <summary>
		/// 金蝶对应的公司编码
		/// </summary>
		[Column(Name = "k3c_company_code")]
		public string? K3CCompanyCode { get; set; }

		/// <summary>
		/// oa地区
		/// </summary>
		[Column(Name = "oa_district")]
		public string? OaDistrict { get; set; }
	}

}
