﻿using System.Runtime.CompilerServices;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Filters;

/// <summary>
/// 统一结果过滤器
/// </summary>
/// <typeparam name="T">返回结果的类型参数</typeparam>
public class ActionResultFilter<T>(IOptions<JsonOptions> jsonOptions)
    : IAsyncResultFilter
    where T : ApiResult<object>, new()
{
    private readonly JsonOptions _jsonOptions = jsonOptions.Value;

    /// <summary>
    /// 统一返回结果处理
    /// </summary>
    /// <param name="context">结果执行上下文</param>
    /// <param name="next">结果执行委托</param>
    public async Task OnResultExecutionAsync(ResultExecutingContext context, ResultExecutionDelegate next)
    {
        T apiResult;
        
        switch (context.Result)
        {
            case ObjectResult objectResult:
            {
                var value = objectResult.Value;
                var declaredType = objectResult.DeclaredType;

                if (value is PageResultBase pageResult && !_jsonOptions.JsonSerializerOptions.Converters.Any(converter => converter is PageResultJsonConverterFactory))
                {
                    context.Result = new ObjectResult(new PaginationResult(pageResult).Succeed());
                }
                else if (declaredType?.IsGenericType == true && declaredType.GetGenericTypeDefinition() == typeof(IAsyncEnumerable<>))
                {
                    var dataType = declaredType.GenericTypeArguments[0];
                    objectResult.DeclaredType = typeof(IAsyncEnumerable<T>);
                    if (dataType != typeof(T))
                    {
                        objectResult.Value = ((IAsyncEnumerable<object>)objectResult.Value!).Select(item =>
                        {
                            var t = new T();
                            t.Succeed(item);
                            return t;
                        });
                    }
                }
                else if (value is not (ProblemDetails or ApiResult<object>))
                {
                    apiResult = new T();
                    objectResult.Value = apiResult.Succeed(value);
                    objectResult.DeclaredType = typeof(T);
                
                }

                if (value is T result)
                {
                    context.Result = new ObjectResult(result);
                }

                break;
            }
            case EmptyResult:
                apiResult = new T();
                context.Result = new ObjectResult(apiResult.Succeed());
                break;
        }
        
        await next();

        switch (context.Result)
        {
            case PhysicalFileResult fileResult:
                File.Delete(fileResult.FileName);
                break;
        }
    }
}