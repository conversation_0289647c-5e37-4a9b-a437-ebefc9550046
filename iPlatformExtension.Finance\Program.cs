using System.Text;
using System.Text.Encodings.Web;
using Autofac.Extensions.DependencyInjection;
using System.Text.Json;
using System.Text.Json.Serialization;
using Confluent.Kafka;
using iPlatformExtension.Common.Converters;
using Microsoft.AspNetCore.HttpLogging;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Filters;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.Dto;
using Microsoft.OpenApi.Models;
using Nacos.AspNetCore.V2;
using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db;
using iPlatformExtension.Common.Formatters.Input;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Common.Validation;
using iPlatformExtension.Finance.Applications.Extensions;
using iPlatformExtension.Finance.Applications.Models.CurrencyRate;
using iPlatformExtension.Finance.Consumers;
using iPlatformExtension.Finance.Infrastructure.Converters;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.IdentityModel.Tokens;
using NLog.Web;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using SkyApm.AspNetCore.Diagnostics;
using SkyApm.Diagnostics.MSLogging;
using SkyApm.Utilities.DependencyInjection;

[assembly:ApplicationPart("iPlatformExtension.Common")]

var builder = WebApplication.CreateBuilder(args);
var environment = builder.Environment;

#region Logging

var logging = builder.Logging;
logging.ClearProviders();
logging.AddSimpleConsole(options => options.IncludeScopes = true);
logging.AddNLogWeb(new NLogAspNetCoreOptions()
{
    RemoveLoggerFactoryFilter = false,
    LoggingConfigurationSectionName = "NLog"
});

#endregion

#region Configuration

var configuration = builder.Configuration;
//  不是本地调试环境使用nacos的配置中心
if (!environment.IsLocal())
{
    configuration.AddNacosV2Configuration(configuration.GetSection("nacos"));
}
#endregion

#region DependencyInjection

builder.Services.TryAddSingleton<EntityTypeInfoProvider>();

builder.Services
    .AddPlatformFreeSql("data source=***************,7433;initial catalog=acip_iplatform;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true;Pooling=false;" ?? throw new ArgumentException("缺少数据库连接字符串"))
    .ConfigureGlobalCurdLogging("Logging:FreeSql:Curd");

if (environment.IsProduction())
{
    builder.Services.AddMongoDbContext<PlatformMongoDbContext>(configuration.GetConnectionString("Log") 
                                                     ?? throw new ArgumentNullException($"ConnectionStrings:Log", "缺少数据库连接字符串"));

    builder.Services.AddEntityChangeLogs();
}

builder.Services.AddDataService();

builder.Services.AddCache().ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
{
    options.ConnectionString = configuration.GetConnectionString("Redis");
    options.Converters.Add(new ClaimsIdentityConverter());
});

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = BladeAuthOptions.SchemeName;
    options.DefaultChallengeScheme = BladeAuthOptions.SchemeName;
}).AddScheme<BladeAuthOptions, BladeAuthenticationHandler>(BladeAuthOptions.SchemeName, "bladeX token验证", options =>
{
    options.Events.OnTokenValidated = AuthenticationExtension.ValidateUserAsync;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        ValidateIssuer = false,
        ValidateAudience = false,
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(configuration["BladeAuth:SecurityKey"] ?? 
                                   throw new ArgumentNullException($"BladeAuth:SecurityKey", "缺少blade-auth秘钥")))
    };
}).AddJwtBearer(PlatformAuthOptions.SchemeName,
    options =>
    {
        var platformAuthenticationOptions =
            configuration.Get<PlatformAuthOptions>(sectionKey: "IPlatformAuth");
        options.TokenValidationParameters = new TokenValidationParameters
        {
            IssuerSigningKey = new SymmetricSecurityKey(
                platformAuthenticationOptions?.SecurityKey.GetBytes(Encoding.UTF8) ??
                throw new ArgumentNullException(nameof(configuration))),
            ValidateIssuerSigningKey = true,
            ValidAudiences = platformAuthenticationOptions.Audiences,
            ValidIssuers = platformAuthenticationOptions.Issuers
        };
        options.Events = new JwtBearerEvents
        {
            OnTokenValidated = AuthenticationExtension.ValidateUserAsync,
            OnMessageReceived = AuthenticationExtension.GetTokenAsync,
            OnAuthenticationFailed = AuthenticationExtension.AuthenticateFailedAsync
        };
        options.SaveToken = true;
        options.ForwardChallenge = BladeAuthOptions.SchemeName;
        options.ForwardForbid = BladeAuthOptions.SchemeName;
        options.ForwardDefaultSelector = context =>
            context.Request.Headers.Authorization.Count > 0 ? PlatformAuthOptions.SchemeName : null;
    });

builder.Services.AddControllers(options =>
{
    options.Filters.Add<ActionExceptionFilter<ResultData>>();
    options.Filters.Add<ActionResultFilter<ResultData>>();
    options.InputFormatters.Insert(0, JsonPatchInputFormatterExtension.GetJsonPatchInputFormatter());
}).AddJsonOptions(options =>
{
    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
    options.JsonSerializerOptions.Converters.Add(new DateTimeJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new NullableDateTimeJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new PageResultJsonConverterFactory());
    options.JsonSerializerOptions.Converters.Add(new JsonPatchOperationConverterFactory());
    options.JsonSerializerOptions.Converters.Add(new OfficialPaymentStatusJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter<SortOrder>(options.JsonSerializerOptions.PropertyNamingPolicy));
    if (environment.IsLocal())
    {
        options.JsonSerializerOptions.WriteIndented = true;
    }
});

builder.Services.AddObjectPools();

builder.Services.AddValidation(new ModelValidationOptions());

if (environment.IsProduction())
{
    builder.Services.AddKafKaConsumers<KingdeeCurrencyRate>().ConfigureServerOptions(serverOptions =>
    {
        serverOptions.GroupId = $"{environment.ApplicationName}-{environment.EnvironmentName}";
        serverOptions.AutoOffsetReset = AutoOffsetReset.Latest;
        serverOptions.EnableAutoCommit = false;
    }, "KafKa:Consumer:ServerOptions").ConfigureConsumerOptions((consumerOptions, _) =>
    {
        var jsonSerializerOptions = new JsonSerializerOptions(JsonSerializerDefaults.Web);
        jsonSerializerOptions.Converters.Add(new DateTimeJsonConverter());
        jsonSerializerOptions.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
        
        var consumerBuilder = consumerOptions.ConsumerBuilder!;
        consumerBuilder.SetKeyDeserializer(new StringMessageKeyConverter());
        consumerBuilder.SetValueDeserializer(new JsonMessageValueConverter<KingdeeCurrencyRate>(jsonSerializerOptions));

    }, "KafKa:Consumer").AddConsumer<CurrencyRateConsumer>().Build();
}


if (!environment.IsProduction())
{
    builder.Services.AddSwaggerGen(c => 
    { 
        c.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = builder.Environment.ApplicationName, 
            Version = "v1",
            Description = "账务中心扩展程序描述"
        });
        var documentPath = Path.Combine(AppContext.BaseDirectory, $"{builder.Environment.ApplicationName}.XML");
        c.IncludeXmlComments(documentPath, true);

        var modelPath = Path.Combine(AppContext.BaseDirectory, "iPlatformExtension.Model.xml");
        c.IncludeXmlComments(modelPath, true);
        c.OrderActionsBy(o => o.RelativePath);
    });
}

builder.Services.AddHttpContextAccessor();

// 如果不是本地调试环境就使用nacos的服务发现
if (!environment.IsLocal())
{
    builder.Services.AddNacosAspNet(configuration);
    builder.Services.AddNacosServiceDiscovery(options => options.AllowedSchemes = [Uri.UriSchemeHttp]);
    builder.Services.Configure<ForwardedHeadersOptions>(configuration.GetSection(nameof(ForwardedHeadersOptions)));
}

builder.Services.AddHttpLogging(options =>
{
    options.LoggingFields |= HttpLoggingFields.RequestHeaders;
    options.LoggingFields |= HttpLoggingFields.RequestPath;
    options.LoggingFields |= HttpLoggingFields.RequestQuery;
    options.LoggingFields |= HttpLoggingFields.RequestBody;
    options.LoggingFields |= HttpLoggingFields.ResponsePropertiesAndHeaders;
    options.LoggingFields |= HttpLoggingFields.ResponseBody;
    options.RequestBodyLogLimit = 327680;
});

builder.Services.AddMediatRServices().Add(FinanceDependency.ConfigFinanceMediatRServices).Build();

builder.Services.AddAutoMapper(typeof(Program));

// builder.Services.AddOpenTelemetry()
//     .ConfigureResource(resourceBuilder => resourceBuilder.AddService(serviceName: environment.ApplicationName))
//     .WithTracing(providerBuilder => providerBuilder.AddAspNetCoreInstrumentation().AddConsoleExporter())
//     .WithMetrics(providerBuilder => providerBuilder.AddAspNetCoreInstrumentation()
//         .AddProcessInstrumentation()
//         .AddRuntimeInstrumentation()
//         .AddPrometheusExporter());

builder.Services.AddHealthChecks();

builder.Services.AddSkyAPM(extensions => extensions.AddAspNetCoreHosting());

#endregion

#region autofac
builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory());
#endregion


var app = builder.Build();

#region MiddleWare

app.UseExceptionHandler("/Error");

if (!environment.IsLocal())
{
    app.UseForwardedHeaders();
}

app.UseW3CTraceResponse();

app.UseHealthChecks("/actuator/health");

if (!environment.IsProduction())
{
    app.UseIcon($"/{app.Environment.ApplicationName}");
    app.UseFileBrowser(app.Environment.ContentRootPath, $"/{app.Environment.ApplicationName}");
}
else
{
    app.UseFileBrowser($"{app.Environment.ContentRootPath}/logs", "/logs");
}

//Configure the HTTP request pipeline.
if (!environment.IsProduction())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.Use((context, next) =>
{
    context.Request.EnableBuffering();
    return next(context);
});

app.UseHttpLogging();

app.UseIpAddressLogging();

// app.UseOpenTelemetryPrometheusScrapingEndpoint();

app.UseRouting();

app.UseAuthentication();

app.UseAuthorization();

app.UseResponseCaching();

app.MapControllers();

#endregion

app.Run();
