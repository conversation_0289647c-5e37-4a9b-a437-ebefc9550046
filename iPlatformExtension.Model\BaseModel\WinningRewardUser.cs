﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 胜诉奖励受益人信息
	/// </summary>
	[Table(Name = "winning_reward_user", DisableSyncStructure = true)]
	public partial class WinningRewardUser {

		/// <summary>
		/// 受益人类型
		/// </summary>
		[Column(Name = "beneficiary_type", IsPrimary = true)]
		public WinningRewardBeneficiaryType BeneficiaryType { get; set; }

		/// <summary>
		/// 任务id
		/// </summary>
		[Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ProcId { get; set; } = null!;

		/// <summary>
		/// 姓名
		/// </summary>
		[Column(Name = "cn_name", StringLength = 50, IsNullable = false)]
		public string CnName { get; set; } = string.Empty;

		/// <summary>
		/// 部门id
		/// </summary>
		[Column(Name = "dept_id", StringLength = 50, IsNullable = false)]
		public string DeptId { get; set; } = null!;

		/// <summary>
		/// 部门名称
		/// </summary>
		[Column(Name = "dept_name", StringLength = 500, IsNullable = false)]
		public string DeptName { get; set; } = string.Empty;

		/// <summary>
		/// 奖励金额
		/// </summary>
		[Column(Name = "reward", DbType = "money")]
		public decimal Reward { get; set; } = 0.00M;

		/// <summary>
		/// 编辑后的奖金奖励
		/// </summary>
		[Column(Name = "edited_reward", DbType = "money")]
		public decimal? EditedReward { get; set; }

		/// <summary>
		/// 用户id
		/// </summary>
		[Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; } = null!;

		/// <summary>
		/// 工号
		/// </summary>
		[Column(Name = "user_name", StringLength = 50, IsNullable = false)]
		public string UserName { get; set; } = string.Empty;

		
		/// <summary>
		/// 地区编码
		/// </summary>
		[Column(Name = "district_code")]
		public string DistrictCode { get; set; } = string.Empty;

		/// <summary>
		/// 地区名称
		/// </summary>
		[Column(Name = "district_name", StringLength = 50, IsNullable = false)]
		public string DistrictName { get; set; } = string.Empty;

	}

}
