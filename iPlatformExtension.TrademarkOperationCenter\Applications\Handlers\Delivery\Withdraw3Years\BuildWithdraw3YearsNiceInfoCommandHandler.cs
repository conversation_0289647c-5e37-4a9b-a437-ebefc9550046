﻿using AutoMapper;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.Withdraw3Years;

internal sealed class BuildWithdraw3YearsNiceInfoCommandHandler(
    IDeliveryNiceCategoryRepository niceCategoryRepository, IMapper mapper) 
    : BuildProcNiceInfoCommandHandler(mapper, niceCategoryRepository)
{

    public override string CtrlProcId => CtrlProcIds.TrademarkWithdraw3Years;

    public override IEnumerable<string> CaseDirections => [CaseDirection.II];
}