﻿using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Logging.HttpClient;
using iPlatformExtension.Model.BaseModel;
using Microsoft.AspNetCore.Http.Json;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;

namespace ConsoleScript;

public partial class InitForeignSupplierTask
{

    public async Task RunAsync()
    {
        var services = new ServiceCollection();
        services.AddFreeSql<PlatformFreeSql>(options =>
        {
            options.ConnectionString = "data source=***************,7433;initial catalog=acip_iplatform_1009;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true";
            options.DbType = DataType.SqlServer;
            options.CommandTimeout = TimeSpan.FromSeconds(10);
        }).ConfigureGlobal((freeSql, provider) =>
        {
            var logger = provider.GetRequiredService<ILogger<InitForeignSupplierTask>>();
            freeSql.Aop.CurdAfter += (_, args) =>
            {
                logger.LogInformation(args.Sql);
            };
        });

        services.AddLogging(builder =>
            builder.AddSimpleConsole(options => options.ColorBehavior = LoggerColorBehavior.Enabled));

        services.AddOptions<JsonOptions>().Configure(options =>
        {
            options.SerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            options.SerializerOptions.Converters.Add(new DateTimeJsonConverter());
        });

        services.AddStringBuilderPool();

        services.AddSingleton<TextRequestBodyLogger>();

        services.AddHttpClient<SupplierClient>()
            .ConfigureHttpClient(client =>
            {
                client.BaseAddress = new Uri("http://patas-test.aciplaw.com");

                var userAgent = client.DefaultRequestHeaders.UserAgent;
                userAgent.Add(new ProductInfoHeaderValue("Mozilla", "5.0"));
                userAgent.Add(new ProductInfoHeaderValue("AppleWebKit", "537.36"));
                userAgent.Add(new ProductInfoHeaderValue("Chrome", "*********"));
                userAgent.Add(new ProductInfoHeaderValue("Safari", "537.36"));
                userAgent.Add(new ProductInfoHeaderValue("Edg", "*********"));

                client.DefaultRequestHeaders.Add("blade-auth",
                    "bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZW5hbnRfaWQiOiIwMDAwMDAiLCJiZWxvbmdfdG9fZGlzdHJpY3QiOiJTWiIsInVzZXJfbmFtZSI6ImFkbWluIiwibWFuYWdlX2Rpc3RyaWN0IjoiQkosQ1MsREcsRlMsR1osSFosS0YsTkosU0gsU1UsU1osVVMtU0YsWEEsWkgsWkosWlMiLCJyZWFsX25hbWUiOiLnrqHnkIblkZgiLCJjbGllbnRfaWQiOiJjYXNlY2MiLCJyb2xlX2lkIjoiMTEyMzU5ODgxNjczODY3NTIwMSwxNTE2OTcxMzUxNzMzMjg4OTYxLDE1MTY5NzE1NTY3Njc2NDU2OTgsMTU0MDE2NTEyNzg3MTkyNjI3MywxNTE2OTcxNDE0NzM1OTI5MzQ1LDE0ODMzNjc3MDk3ODQ3OTcxODUsMTU2NjY2ODYyNzcwMzIzODY1NywxNTY2NjY3NTE4NTk1MDQzMzMwLDE1NjY2NjkyNDgxNjY2MjkzNzcsMTYwOTE2NjI0NDc5NDA3NzE4NSwxNjA5MTY4MzQwODUxMzYzODQyLDE2MDkxNjg0NTEwMTg5NTI3MDUsMTYyMDMzMDAwMDQ4Mzg4MDk2MiwxNjIyNDgxNDUzNzY3OTg3MjAyLDE3MTkyODgzODg0NjIzMTc1NzAsMTcxOTI4ODUzODk4NzQ5OTUyMSwxNzE5Mjg4NzAzODc4MTcyNjczLDE3MTkyODg3NTQ1MDc2MTYyNTgsMTgzMDc5NjQ1NDA4NDU3MTEzNywxODM0NDAxMDM4MTQ5MzY1NzYyIiwic2NvcGUiOlsiYWxsIl0sIm9hdXRoX2lkIjoiIiwiZXhwIjoxNzM4ODQ1Mzk3LCJqdGkiOiJRVURhbDdVSHpCcHZ0WkN3R3c0SjZPRnZFekEiLCJ1c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzMi4wLjAuMCBTYWZhcmkvNTM3LjM2IEVkZy8xMzIuMC4wLjAiLCJkZXB0X2JlbG9uZ19pZCI6IjUwMDAwIiwiYmVsb25nX3RvX2NvbXBhbnkiOiI5MTQ0MDMwMDc0ODg3NTQ3MlQiLCJhdXRoX3V1aWQiOiJaVEpsTm1NMk56TXROalZrWkMwME9HVmtMVGt4WmprdE1tSTFNVEV4T0RnelpEUXgiLCJhdmF0YXIiOiIvbWVzc2FnZXIvdXNlcmljb24vYTg3NWJhYjZjYzU4NDEyMWIxY2YxYzZmYTZlN2QyMWEuanBnIiwiYXV0aG9yaXRpZXMiOlsi5Lq66LWEKOaPkOaIkCkiLCLmtYHnqIvnu4_nkIYiLCLotKbliqHkuK3lv4PotKLliqHnibnmrormnYPpmZAiLCJJVOS6uuWRmCIsIuWHuuWPo-WVhuagh-aKpeS7tyIsIuaPkOaIkOaJp-ihjOiAhS3kuLvnrqEiLCLotKLliqEo6K6i5Y2VKSIsIui0puWKoeS4reW_g-WfuuehgCIsIumUgOWUrijmj5DmiJApIiwi6LSm5Yqh5Lit5b-D6YOo6Zeo566h55CG5ZGYIiwiYWRtaW5pc3RyYXRvciIsIuaZrumAmuWRmOW3pe-8iOaIkOacrOaRiuWIhu-8iSIsIui0ouWKoS3muKDpgZPmiJDmnKwiLCLmtYHnqIvkurrlkZgiLCLotKLliqEt5oiQ5pys566h55CGIiwi5ZWG5Yqh5Lq65ZGYIiwi5ZWG5Yqh57uP55CGIiwi5o-Q5oiQ5omn6KGM6ICFLee7j-eQhiIsIlJFUE9SVF9BRE1JTiIsIuWVhuacuuebkea1i-ezu-e7n-euoeeQhuWRmCJdLCJyb2xlX25hbWUiOiJhZG1pbmlzdHJhdG9yLOa1geeoi-S6uuWRmCzllYbliqHnu4_nkIYs5rWB56iL57uP55CGLOWVhuWKoeS6uuWRmCzotKLliqEo6K6i5Y2VKSzplIDllK4o5o-Q5oiQKSzkurrotYQo5o-Q5oiQKSxJVOS6uuWRmCzotKbliqHkuK3lv4Pln7rnoYAs6LSm5Yqh5Lit5b-D6YOo6Zeo566h55CG5ZGYLOi0puWKoeS4reW_g-i0ouWKoeeJueauiuadg-mZkCxSRVBPUlRfQURNSU4s5Ye65Y-j5ZWG5qCH5oql5Lu3LOaZrumAmuWRmOW3pe-8iOaIkOacrOaRiuWIhu-8iSzotKLliqEt5oiQ5pys566h55CGLOaPkOaIkOaJp-ihjOiAhS3nu4_nkIYs5o-Q5oiQ5omn6KGM6ICFLeS4u-euoSzotKLliqEt5rig6YGT5oiQ5pysLOWVhuacuuebkea1i-ezu-e7n-euoeeQhuWRmCIsInJvbGV0eXBlcyI6WyJhZG1pbmlzdHJhdG9yIiwiZmxvd1VzZXIiLCJidXNpbmVzc01hbmFnZXIiLCJmbG93TWFuYWdlciIsImRlcGFydG1lbnQiLCJmaW5hbmNlIiwicXVvdGUiLCJvcmlnaW5hdG9yIiwidHJhZGUtYWRtaW4iLCJ0cmFkZS1hcmVhIl0sInBvc3RfaWQiOiIxMTIzNTk4ODE3NzM4Njc1MjAxIiwidXNlcl9pZCI6IjExMjM1OTg4MjE3Mzg2NzUyMDEiLCJuaWNrX25hbWUiOiLnrqHnkIblkZgiLCJtYW5hZ2VfY29tcGFueSI6IjkxNDQwMTEyTUFDVUhQMlEyUCwsMzExMTAwMDAzMzU1NzAyODNVLDkxMTEwMTA4NzU5NjIzMzM0WCw5MTQ0MDMwMDc0ODg3NTQ3MlQsOTE2MTAxMzFNQTZVN1A2QzhLLDkxMzEwMDAwMDc4MTc4MzI3Nyzml6AsOTE0MjA1MDBNQTRGNVgzRDBSLDE1MTE2NjIsOTE0NDA2MDY1Nzk2NTQyM1hZLDMxNDQwMDAwNjYxODAyMDZYWSw5MTQ0MDEwMU1BNTlFRjNUODks5pegLDkxNDQwNDAwNTc3OTI4MTEwRCw5MTQ0MDMwMDM1OTc2MzM3MDEsMzE0NDAwMDBNRDAxMDM5OThVLDkxNDQwMTA2NzE2MzY4MjY1Siw5MTExMDEwODY2MjE1MjkxOVUsOTE1MTAxMDBNQUNINEIxWDIwLDkxNDQxOTAwNzQ3MDY3MTMxUiw3MTYzNjgyNi01LDkxMzIwNTk0NTc1MzY4ODY5TCw5MTMzMDEwOE1BMjhSQUxINUQsOTEzMzAxMDhNQUNYNzc4RDMyLDkxMzMwMTA2MzI4MjU5ODVYMSw5MTMzMDEwOU1BQ1RSOUVMOEIsOTE0NDE5MDBNQTRXMkRZNDlNLOaXoCw5MTQ0MDExMk1BQ1Q2TFA3MVcsOTEzNDAxMDBNQThMTENXVTdQIiwiZGV0YWlsIjp7InR5cGUiOiJ3ZWIiLCJkZXB0QmVsb25nSWQiOiI1MDAwMCIsImNvZGUiOiJhZG1pbiJ9LCJkZXB0X2lkIjoiMTAwMDAsMTEyMDAwMCwxMTQwMDAwLDEzMDAwMCwxMzgwMDAwLDEzOTAwMDAsMTQwMDAwLDI2MDAwMCwzMDAwMCw0MDAwMCw1MDAwMCw1OTAwMDAsNjAwMDAsNzIwMDAwLDc2MDAwMCw4NTYsODU3LDg2MSw5MDAwMCw5MTAwMDAiLCJhY2NvdW50IjoiYWRtaW4ifQ.CIILqNlYaH9-9DlVtfFxzhbj8eyckYW06V9BPORbVdM");
            }).AddRequestHeaderLogger().AddLogger<TextRequestBodyLogger>();
        
        services.AddHuaweiObsClient(options =>
        {
            options.AccessKey = "XIJB954KKXWSOMKA4LRQ";
            options.SecretKey = "0pXYV9UKUgH4Xdz0k3WIcjNEKafNhNYnWcR32uoG";
            options.Host = "http://obs.cn-south-1.myhuaweicloud.com";
            options.DefaultBucketName = "iplatform-test";
        });
        
        services.AddDataService();

        services.AddCache().ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
        {
            options.ConnectionString = "*************:6379,password=Acip_cc@54,defaultDatabase=10";
        });

        var serviceProvider = services.BuildServiceProvider();

        var freeSql = serviceProvider.GetRequiredService<IFreeSql<PlatformFreeSql>>();
        var countryRepository = serviceProvider.GetRequiredService<IStringKeyCacheableRepository<BasCountry>>();
        var userInfoRepository = serviceProvider.GetRequiredService<IStringKeyCacheableRepository<SysUserInfo>>();
        var fileDescriptionRepository = serviceProvider.GetRequiredService<ICacheableRepository<string, BasFileDesc>>();
        var huaweiObsClient = serviceProvider.GetRequiredService<HuaweiObsClient>();
        var logger = serviceProvider.GetRequiredService<ILogger<InitForeignSupplierTask>>();

        var customers = await freeSql.Select<CusCustomer>().WithLock()
            .LeftJoin<ForeignSupplier>((customer, supplier) => customer.CustomerId == supplier.SupplierId)
            .Where<ForeignSupplier>(supplier => supplier.SupplierId == null)
            .Where(customer => customer.IsEnabled == true)
            .Where(customer => customer.IsCooperation == true)
            .OrderBy(customer => customer.CustomerId)
            // .Take(3)
            .ToListAsync();
        foreach (var customer in customers)
        {
            var updater = await userInfoRepository.GetCacheValueAsync(customer.UpdateUserId);
            var creator = await userInfoRepository.GetCacheValueAsync(customer.CreateUserId);
            var customerInfo = new Customer
            {
                CustomerId = customer.CustomerId,
                CustomerCode = customer.CustomerCode,
                CustomerNameEn = customer.CustomerNameEn,
                CustomerName = customer.CustomerName,
                CountryId = customer.CountryId ?? string.Empty,
                CountryName = await countryRepository.GetTextValueAsync(customer.CountryId ?? string.Empty) ?? string.Empty,
                IsEnabled = customer.IsEnabled ?? false,
                UpdateUserCode = updater?.UserName ?? string.Empty,
                UpdateUserName = updater?.CnName ?? string.Empty,
                UpdateTime = customer.UpdateTime ?? default,
                CreateTime = customer.CreateTime ?? default,
                CreateUserCode = creator?.UserName ?? string.Empty,
                CreateUserName = creator?.CnName ?? string.Empty,
                Website = customer.Website,
                EnterpriseEmail = customer.Email ?? string.Empty,
            };
            
            var requestList = await freeSql.Select<CusCustomer, CusJoinList, CusJoinList, CusRequest, SysDictionary>()
                .WithLock()
                .LeftJoin((cc, cj, cj2, cr, dic) => cc.CustomerId == cj.JoinObjId)
                .LeftJoin((cc, cj, cj2, cr, dic) => cj.FormObjId == cj2.JoinObjId)
                .InnerJoin((cc, cj, cj2, cr, dic) => cr.RequestId == cj2.FormObjId || cr.RequestId == cj.FormObjId)
                .LeftJoin((cc, cj, cj2, cr, dic) => dic.Value == cr.RequestType && dic.DictionaryName == "job_type")
                .Where((cc, cj, cj2, cr, dic) => cc.CustomerId == customer.CustomerId || cc.ParentId == customer.CustomerId)
                .ToListAsync((cc, cj, cj2, cr, dic) => cr);

            var requestInfos = await requestList.ToAsyncEnumerable().SelectAwait(async request =>
            {
                var requestCreator = await userInfoRepository.GetCacheValueAsync(request.CreateUserId);
                var requestUpdater = await userInfoRepository.GetCacheValueAsync(request.UpdateUserId);
                var requestInfo = new RequestList
                {
                    RequestId = request.RequestId,
                    Title = request.Title,
                    Content = request.Content,
                    CreateTime = request.CreateTime ?? default,
                    CreateUserCode = requestCreator?.UserName ?? string.Empty,
                    CreateUserName = requestCreator?.CnName ?? string.Empty,
                    UpdateTime = request.UpdateTime ?? default,
                    UpdateUserCode = requestUpdater?.UserName ?? string.Empty,
                    UpdateUserName = requestUpdater?.CnName ?? string.Empty,
                    IsEnabled = request.IsEnabled,
                    CaseTypeList = await freeSql.Select<CusJoinList>().WithLock()
                        .Where(list => list.FormObjId == request.RequestId)
                        .Where(list => list.FormType == "request")
                        .Where(list => list.JoinType == "case_type")
                        .ToListAsync(list => list.JoinObjId)
                };

                var caseFiles = await freeSql.Select<CaseFile>().WithLock()
                    .InnerJoin<CusJoinList>((file, list) => file.FileId == list.FormObjId)
                    .Where<CusJoinList>(list => list.FormType == "file")
                    .Where<CusJoinList>(list => list.JoinType == "customer_request")
                    .Where<CusJoinList>(list => list.JoinObjId == request.RequestId)
                    .ToListAsync();

                requestInfo.RequestFileList = await caseFiles.ToAsyncEnumerable().SelectAwait(async file =>
                {
                    var uploader = await userInfoRepository.GetCacheValueAsync(file.CreateUserId);
                    var fileListA = await freeSql.Select<FileListA>(int.Parse(file.FileNo[4..])).WithLock()
                        .ToOneAsync();
                    return new RequestFileList
                    {
                        RequestFileId = file.FileId,
                        FileName = file.FileName,
                        FileDescribe =
                            (await fileDescriptionRepository.GetCacheValueAsync(file.DescId))?.FileDescZhCn ??
                            string.Empty,
                        UploadTime = file.CreateTime,
                        UploadUserCode = uploader?.UserName ?? string.Empty,
                        UploadUserName = uploader?.CnName ?? string.Empty,
                        FileUrl = huaweiObsClient.GenerateTemporaryUrl(fileListA.GetObjectName(), fileListA.Bucket,
                            TimeSpan.FromDays(3)).SignUrl
                    };
                }).ToArrayAsync();

                return requestInfo;
            }).ToArrayAsync();

            var banks = await freeSql.Select<CusBank>().WithLock().Where(bank => bank.CustomerId == customer.CustomerId)
                .ToListAsync();

            var bankInfos = await banks.ToAsyncEnumerable().SelectAwait(async bank =>
            {
                var bankCreator = await userInfoRepository.GetCacheValueAsync(bank.CreateUserId ?? string.Empty);
                var bankUpdater = await userInfoRepository.GetCacheValueAsync(bank.UpdateUserId ?? string.Empty);
                return new BankList
                {
                    BankId = bank.BankId,
                    CountryId = bank.CountryId ?? string.Empty,
                    CountryName = await countryRepository.GetTextValueAsync(bank.CountryId ?? string.Empty) ?? string.Empty,
                    AccountNumber = bank.AccountNumber,
                    ReceiverAccountName = bank.BeneficiaryName,
                    IbanCode = bank.IbanCode,
                    BeneficiaryAddressReal = bank.BeneficiaryAddressReal,
                    TransferBankName = bank.CorrespondentBank,
                    BankSwiftCode = bank.SwiftCode,
                    TransferBankAddress = bank.CorrespondentBankAddress,
                    CreateTime = bank.CreateTime ?? default,
                    CreateUserCode = bankCreator?.UserName ?? string.Empty,
                    CreateUserName = bankCreator?.CnName ?? string.Empty,
                    UpdateUserCode = bankUpdater?.UserName ?? string.Empty,
                    UpdateUserName = bankUpdater?.CnName ?? string.Empty,
                    UpdateTime = bank.UpdateTime,
                    Remark = bank.Remark,
                    IsEnabled = bank.IsEnabled,
                    ReceiveBankAddress = bank.BankAddressReal,
                    DepositBankName = bank.BankNameReal
                };
            }).ToArrayAsync();
            
            var contacts = await freeSql.Select<CusCustomer, CusJoinList, CusJoinList, CusContact>().WithLock()
                .LeftJoin((cc, cj, cj2, c) => cc.CustomerId == cj.JoinObjId)
                .LeftJoin((cc, cj, cj2, c) => cj.FormObjId == cj2.JoinObjId)
                .InnerJoin((cc, cj, cj2, c) => c.ContactId == cj.FormObjId || cj2.JoinObjId == c.ContactId)
                .Where((cc, cj, cj2, c) => cc.CustomerId == customer.CustomerId && c.IsEnabled == true)
                .ToListAsync((cc, cj, cj2, c) => c);

            var contactInfos = await contacts.ToAsyncEnumerable().SelectAwait(async contact =>
            {
                var contactCreator = await userInfoRepository.GetCacheValueAsync(contact.CreateUserId ?? string.Empty);
                var contactUpdater = await userInfoRepository.GetCacheValueAsync(contact.UpdateUserId ?? string.Empty);

                var telAreaCodeMatchResult = InternationalDialingCodeRegex().Match(contact.Tel ?? string.Empty);
                var mobileAreaCodeMatchResult = InternationalDialingCodeRegex().Match(contact.Mobile ?? string.Empty);
                
                return new ContactList
                {
                    ContactId = contact.ContactId,
                    ContactName = contact.ContactName,
                    CallName = contact.CallName,
                    ContactTypeList = await freeSql.Select<BasContactType>().WithLock()
                        .InnerJoin<CusJoinList>((type, list) => type.ContactTypeId == list.JoinObjId)
                        .Where<CusJoinList>(list => list.FormType == "contact")
                        .Where<CusJoinList>(list => list.JoinType == "contact_type")
                        .Where<CusJoinList>(list => list.FormObjId == contact.ContactId)
                        .ToListAsync(type => new SupplierContactType
                        {
                            SupplierContactTypeCn = type.ContactTypeZhCn,
                            SupplierContactTypeCode = type.ContactTypeCode
                        }),

                    Position = contact.Position,
                    ContactMailList = [
                        new SupplierContactMail()
                        {
                            ContactMail = contact.Email,
                            SupplierContactMailTypeCn = "工作邮箱"
                        },
                        new SupplierContactMail()
                        {
                            ContactMail = contact.PersonalEmail,
                            SupplierContactMailTypeCn = "个人邮箱"
                        }
                    ],
                    ContactPhoneList = [
                        new SupplierContactPhone()
                        {
                            PhoneAreaCode = telAreaCodeMatchResult.Success ? NumbersRegex().Match(telAreaCodeMatchResult.Value).Value : string.Empty,
                            ContactPhone = InternationalDialingCodeRegex().Replace(contact.Tel ?? string.Empty, string.Empty)
                        },
                        new SupplierContactPhone()
                        {
                            PhoneAreaCode = mobileAreaCodeMatchResult.Success ? NumbersRegex().Match(mobileAreaCodeMatchResult.Value).Value : string.Empty,
                            ContactPhone = InternationalDialingCodeRegex().Replace(contact.Mobile ?? string.Empty, string.Empty)
                        }
                    ],
                    ContactAddressList = [
                        new SupplierContactAddress()
                        {
                            ContactAddress = string.IsNullOrWhiteSpace(contact.AddressCn) ? contact.AddressEn : contact.AddressCn
                        }
                    ],
                    UpdateUserCode = contactUpdater?.UserName ?? string.Empty,
                    UpdateUserName = contactUpdater?.CnName ?? string.Empty,
                    UpdateTime = contact.UpdateTime ?? default,
                    CreateTime = contact.CreateTime ?? default,
                    CreateUserCode = contactCreator?.UserName ?? string.Empty,
                    CreateUserName = contactCreator?.CnName ?? string.Empty,
                    Remark = contact.Remark,
                    IsEnabled = contact.IsEnabled,
                    UseTypeList = await freeSql.Select<CusJoinList>().WithLock()
                        .Where(list => list.FormObjId == contact.ContactId)
                        .Where(list => list.FormType == "contact")
                        .Where(list => list.JoinType == "case_type")
                        .Where(list => list.JoinObjId != "ALL")
                        .ToListAsync(list => list.JoinObjId)
                };
            }).ToArrayAsync();

            var supplier = new Supplier
            {
                Customer = customerInfo,
                RequestList = requestInfos,
                BankList = bankInfos,
                ContactList = contactInfos
            };

            var client = serviceProvider.GetRequiredService<SupplierClient>();
            var response = await client.PostSupplierAsync(supplier);
            var content = await response.Content.ReadAsStringAsync();
            if (response.IsSuccessStatusCode)
            {
                logger.LogInformation("response: {statusCode}, content: {content}",response.StatusCode, content);
            }
            else
            {
                logger.LogError("response: {statusCode}, content: {content}",response.StatusCode, content);
            }
        }
    }

    [GeneratedRegex(@"\+\d{1,3}[\s-]?", RegexOptions.IgnoreCase, "zh-CN")]
    private static partial Regex InternationalDialingCodeRegex();

    [GeneratedRegex(@"\d{1,3}", RegexOptions.IgnoreCase, "zh-CN")]
    private static partial Regex NumbersRegex();
}

/// <summary>
/// 
/// </summary>
class SupplierClient(HttpClient httpClient, IOptions<JsonOptions> jsonOptions)
{
    public Task<HttpResponseMessage> PostSupplierAsync(Supplier supplier)
    {
        return httpClient.PostAsJsonAsync("/api/supplier-purchase-service/initial/initializationData", supplier, jsonOptions.Value.SerializerOptions);
    }
}
