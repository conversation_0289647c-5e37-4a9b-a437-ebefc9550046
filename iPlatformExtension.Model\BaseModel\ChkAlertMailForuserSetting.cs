using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "chk_alert_mail_foruser_setting", DisableSyncStructure = true)]
	public partial class ChkAlertMailForuserSetting {

		[ Column(Name = "content", StringLength = -2, IsNullable = false)]
		public string Content { get; set; }

		[ Column(Name = "content_user", StringLength = -2, IsNullable = false)]
		public string ContentUser { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "html_body", DbType = "ntext")]
		public string HtmlBody { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "interval_time")]
		public int? IntervalTime { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "next_run_time")]
		public DateTime? NextRunTime { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "subject", StringLength = 1000, IsNullable = false)]
		public string Subject { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
