{"nacos": {"ServerAddresses": ["************:18848"], "DefaultTimeOut": 15000, "Namespace": "test", "ListenInterval": 1000, "ServiceName": "iplatform-outsourcing", "UserName": "nacos", "Password": "Acip1234", "Listeners": [{"Optional": true, "DataId": "appsettings.json", "Group": "iPlatformExtension"}, {"Optional": true, "DataId": "kafka.json", "Group": "iPlatformExtension"}], "Metadata": {"ASPNETCORE_HTTPS_PORTS": "7162", "gRPC_port": "7162"}}}