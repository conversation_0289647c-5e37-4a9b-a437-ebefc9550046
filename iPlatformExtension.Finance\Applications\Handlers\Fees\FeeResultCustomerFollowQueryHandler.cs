﻿using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultCustomerFollowQueryHandler(IFreeSql freeSql)
    : IRequestHandler<FeeResultCustomerFollowQuery>
{
    #region 划拨特殊处理编码

    /// <summary>
    /// 法律特殊业务类型编码
    /// </summary>
    private static readonly string[] lawCodes = {"LS", "LF", "LZ"};

    /// <summary>
    /// 商标特殊业务编码
    /// </summary>
    private const string TrademarkCode = "BS";

    #endregion


    public async Task Handle(FeeResultCustomerFollowQuery request, CancellationToken cancellationToken)
    {
        foreach (var feeListItemDto in request.FeeResults.Where(dto => dto.CaseType.Key == CaseType.Project && lawCodes.Contains(dto.BusinessTypeInfo?.Code ?? string.Empty)))
        {
            await SetFollowerInfoAsync(feeListItemDto, CaseType.Legal);
        }
        
        foreach (var feeListItemDto in request.FeeResults.Where(dto => dto.CaseType.Key == CaseType.Trade && TrademarkCode.Equals(dto.BusinessTypeInfo?.Code ?? string.Empty)))
        {
            await SetFollowerInfoAsync(feeListItemDto, CaseType.Copy);
        }

        return;

        async Task SetFollowerInfoAsync(FeeListItemDto feeListItemDto, string caseType)
        {
            var followers = await freeSql.Select<CusFollowList>().WithLock()
                .Where(list => list.CaseType == caseType)
                .Where(list => list.CaseDirection == feeListItemDto.CaseDirection.Key)
                .Where(list => list.CustomerId == feeListItemDto.CustomerInfo.Id)
                .Where(list => list.IsEnabled == true).ToListAsync(cancellationToken);
            
            feeListItemDto.CurrentSales = followers.FirstOrDefault(follow =>
                follow.CustomerUserType.Equals("ay", StringComparison.OrdinalIgnoreCase))?.TrackUser ?? string.Empty;
            feeListItemDto.CurrentTracker = followers.FirstOrDefault(follow =>
                follow.CustomerUserType.Equals("ga", StringComparison.OrdinalIgnoreCase))?.TrackUser ?? string.Empty;
        }
    }
}