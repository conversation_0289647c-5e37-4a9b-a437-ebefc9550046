﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Behaviors;

namespace iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery;

public record CreateOrderCommand<TOrderCommand>(string ProcId, int Version) : IDeliveryVersionCommand
    where TOrderCommand : OrderCommandBase, new()
{
    internal TOrderCommand CreateCommand(DeliInfo? deliveryInfo)
    {
        return new TOrderCommand()
        {
            ProcId = ProcId,
            Version = deliveryInfo?.Version ?? Version
        };
    }
}