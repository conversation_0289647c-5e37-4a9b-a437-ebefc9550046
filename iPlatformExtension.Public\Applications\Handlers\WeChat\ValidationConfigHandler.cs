﻿using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Common.Encryption;
using iPlatformExtension.Public.Applications.Queries.SystemDictionary;
using iPlatformExtension.Public.Applications.Queries.WeChat;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.WeChat
{
    public sealed class ValidationConfigHandler(IWxWorkClient wxWorkClient) : IRequestHandler<ValidationConfigQuery, ValidationConfig>
    {
        public  Task<ValidationConfig> Handle(ValidationConfigQuery request, CancellationToken cancellationToken)
        {
            TimeSpan mTimeSpan = DateTime.Now.ToUniversalTime() - new DateTime(1970, 1, 1, 0, 0, 0);
            //得到精确到秒的时间戳（长度10位）
            long time = (long)mTimeSpan.TotalSeconds;
            var ticket =  wxWorkClient.GetTicket();
            var noncestr = Guid.NewGuid().ToString().Replace("-", "");
            var signstr = $"jsapi_ticket={ticket}&noncestr={noncestr}&timestamp={time}&url={request.Url}";
            var sign = Sha1Helper.Sha1Encrypt(signstr);
            //5b80dc2448baf73bf16e0cb66597dc9370f8e313
            ValidationConfig config = new ValidationConfig()
            {
                Corpid = wxWorkClient.wxConfig.Corpid,
                signature = sign,
                Agentid = wxWorkClient.wxConfig.Agentid,
                nonceStr = noncestr,
                timestamp = time
            };
            return Task.FromResult(config);
        }
    }
}
