﻿using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using System.Text;
using System.Web;
using AutoMapper;
using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Office.Excel;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Finance.Applications.Models.Proc;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Finance.Applications.Queries.ForeignBill;
using iPlatformExtension.Finance.Applications.Queries.Proc;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.ObjectPool;
using MiniExcelLibs;

namespace iPlatformExtension.Finance.Controllers;

/// <summary>
/// 费项控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public sealed class FeeController(
    IMediator mediator, 
    IMapper mapper, 
    EntityTypeInfoProvider typeInfoProvider,
    ObjectPool<StringBuilder> stringBuilderPool) : ControllerBase
{
    /// <summary>
    /// 查询费项详情
    /// </summary>
    /// <param name="feeId">费项id</param>
    /// <returns>费项详情</returns>
    [HttpGet("/fee/{feeId}")]
    public Task<CaseFeeDetailDto> GetFeeDetailAsync(string feeId)
    {
        return mediator.Send(new FeeDetailQuery(feeId));
    }

    /// <summary>
    /// 分页查询费项列表
    /// </summary>
    /// <param name="dto"></param>
    /// <returns>费项分页结果</returns>
    [HttpPost("/fee")]
    [Authorize(AuthenticationSchemes = BladeAuthOptions.SchemeName)]
    public async Task<IEnumerable<FeeListItemDto>> GetPagingFeeListAsync(FeeQueryDto dto)
    {
        dto = await mediator.Send(new FeesQueryAuthorizationCommand(dto), HttpContext.RequestAborted);
        return await mediator.Send(new FeesQuery(dto), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 批量更新费项
    /// </summary>
    /// <param name="documents">批量的JsonPatchDocument</param>
    /// <returns></returns>
    [HttpPatch("/fees")]
    [Consumes(MediaTypeNames.Application.JsonPatch, MediaTypeNames.Application.Json)]
    [Authorize(AuthenticationSchemes = BladeAuthOptions.SchemeName)]
    public Task UpdateBatchAsync(List<CaseFeeJsonPatchDocument> documents)
    {
        return mediator.Send(new UpdateCaseFeesCommand(documents), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 更新单个费项
    /// </summary>
    /// <param name="feeId">费项id</param>
    /// <param name="document">费项更新字段</param>
    /// <returns>统一返回接口</returns>
    [HttpPatch("/fee/{feeId}")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
    public Task UpdateAsync([FromRoute]string feeId, JsonPatchDocument<CaseFeePatchDto> document)
    {
        var caseFeePatchDocument = new CaseFeeJsonPatchDocument(document.Operations)
        {
            FeeId = feeId,
            ContractResolver = document.ContractResolver
        };
        
        return mediator.Send(new UpdateCaseFeeCommand(caseFeePatchDocument), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 导出费项
    /// </summary>
    /// <param name="dto"></param>
    /// <returns>费项csv文件</returns>
    [HttpPost("/fees/csv")]
    [Authorize(AuthenticationSchemes = BladeAuthOptions.SchemeName)]
    [Produces("application/msexcel", MediaTypeNames.Application.Json)]
    public Task<IActionResult> ExportFeesAsync(FeeQueryDto dto)
    {
        const string downloadName = "费项明细列表.xlsx";

        return ExportFeesAsync<FeeListItemDto>(dto, downloadName);
    }

    /// <summary>
    /// 导出待缴官费费项
    /// </summary>
    /// <param name="dto">查询参数</param>
    /// <returns>待缴官费excel文件</returns>
    [HttpPost("/fees/unpaid-official/excel")]
    [AllowAnonymous]
    [Produces("application/msexcel", MediaTypeNames.Application.Json)]
    public async Task<IActionResult> ExportUnpaidOfficialFeesAsync(FeeQueryDto dto)
    {
        const string downloadName = "待缴官费费项清单.xlsx";
        
        var result = await mediator.Send(new UnpaidOfficialProcQuery(dto), HttpContext.RequestAborted);

        var exportResults = mapper.Map<List<UnpaidOfficialProcExportDto>>(result);
        
        var filePath = Path.Combine(TempDirectory.Default, TempDirectory.Fees, $"{DateTimeOffset.Now.Ticks}-{downloadName}");
        Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
        await using var fileStream = System.IO.File.Create(filePath);
        
        await fileStream.SaveAsAsync(exportResults, excelType: ExcelType.XLSX);
        
        SheetDataAppendWriter? writer = null;
        var stringBuilder = stringBuilderPool.Get();
        var exportTypeInfo = typeInfoProvider.Get(typeof(UnpaidOfficialProcExportDto));
       
        while (result is PageResult<UnpaidOfficialProcDto> {HasNextPage:true} pageResult)
        {
            pageResult.Dispose();
            dto.Page = pageResult.Page + 1;
            dto.Total = pageResult.Total;
            result = await mediator.Send(new UnpaidOfficialProcQuery(dto), HttpContext.RequestAborted);
            
            exportResults = mapper.Map<List<UnpaidOfficialProcExportDto>>(result);

            writer ??= await fileStream.CreateSheetDataWriterAsync();
            if (writer is null)
            {
                fileStream.Append(exportResults, exportTypeInfo, stringBuilder);
            }
            else
            {
                await writer.AppendAsync(exportResults, exportTypeInfo, stringBuilder);
            }
            
            stringBuilder.Clear();
            exportResults.Clear();
        }
        fileStream.Close();

        if (writer is not null)
        {
            await writer.CombineAsync();
            await writer.DisposeAsync();
        }
        stringBuilderPool.Return(stringBuilder);
        
        if (result is IDisposable disposable)
        {
            disposable.Dispose();
        }

        return PhysicalFile(Path.GetFullPath(filePath), "application/msexcel;charset=utf-8", HttpUtility.UrlEncode(downloadName));
    }

    /// <summary>
    /// 导出外所账单费项
    /// </summary>
    /// <param name="dto">查询参数</param>
    /// <returns>导出的文件流</returns>
    [HttpPost("/fees/foreign-bill/excel")]
    [AllowAnonymous]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/msexcel", MediaTypeNames.Application.Json)]
    public async Task<IActionResult> ExportForeignBillFeesAsync(ForeignBillFeeQueryDto dto)
    {
        var downloadName = $"外所账单费项明细表{DateTime.Today:yyyy-MM-dd}.xlsx";
        
        var result = await mediator.Send(new ForeignBillFeesQuery(dto), HttpContext.RequestAborted);
        
        var filePath = Path.Combine(TempDirectory.Default, TempDirectory.Fees, $"{DateTimeOffset.Now.Ticks}-{downloadName}");
        Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
        await using var fileStream = System.IO.File.Create(filePath);
        
        await fileStream.SaveAsAsync(result, excelType: ExcelType.XLSX);
        
        SheetDataAppendWriter? writer = null;
        var stringBuilder = stringBuilderPool.Get();
        var exportTypeInfo = typeInfoProvider.Get(typeof(ForeignBillFeeExportDto));
       
        while (result is PageResult<ForeignBillFeeExportDto> {HasNextPage:true} pageResult)
        {
            pageResult.Dispose();
            
            dto.PageIndex = pageResult.Page + 1;
            
            result = await mediator.Send(new ForeignBillFeesQuery(dto), HttpContext.RequestAborted);

            writer ??= await fileStream.CreateSheetDataWriterAsync();
            if (writer is null)
            {
                fileStream.Append(result, exportTypeInfo, stringBuilder);
            }
            else
            {
                await writer.AppendAsync(result, exportTypeInfo, stringBuilder);
            }
            
            stringBuilder.Clear();
            if (result is List<ForeignBillFeeExportDto> foreignBillFeeList)
            {
                foreignBillFeeList.Clear();
            }
        }
        fileStream.Close();

        if (writer is not null)
        {
            await writer.CombineAsync();
            await writer.DisposeAsync();
        }
        stringBuilderPool.Return(stringBuilder);
        
        if (result is IDisposable disposable)
        {
            disposable.Dispose();
        }

        return PhysicalFile(Path.GetFullPath(filePath), "application/msexcel;charset=utf-8", downloadName.UrlEncode());
    }

    /// <summary>
    /// 导出缴费单的费项
    /// </summary>
    /// <param name="dto">查询参数</param>
    /// <param name="number">缴费单号</param>
    /// <returns>excel文件</returns>
    [HttpPost("/fees/paid-official/excel")]
    [Produces("application/msexcel", MediaTypeNames.Application.Json)]
    public Task<IActionResult> ExportPaidOfficialFeesAsync([FromBody] FeeQueryDto dto, [FromQuery, Required] string number)
    {
        return ExportFeesAsync<PaymentOfficialFeesExportDto>(dto, $"{number}缴费清单.xlsx");
    }

    /// <summary>
    /// 导出不缴费单的费项
    /// </summary>
    /// <param name="dto">查询参数</param>
    /// <param name="number">不缴费单号</param>
    /// <returns>excel文件</returns>
    [HttpPost("/fees/not-paid/official/excel")]
    [Produces("application/msexcel", MediaTypeNames.Application.Json)]
    public Task<IActionResult> ExportNotPaidOfficialFeesAsync([FromBody] FeeQueryDto dto, [FromQuery, Required] string number)
    {
        return ExportFeesAsync<NotPaidOfficialFeesExportDto>(dto, $"{number}不缴费单.xlsx");
    }

    private async Task<IActionResult> ExportFeesAsync<T>(FeeQueryDto dto, string downloadName)
    {
        var needToConvert = false;
        
        dto = await mediator.Send(new FeesQueryAuthorizationCommand(dto), HttpContext.RequestAborted);
        var result = await mediator.Send(new FeesQuery(dto), HttpContext.RequestAborted);

        if (result is not IEnumerable<T> exportResults)
        {
            exportResults = mapper.Map<List<T>>(result);
            needToConvert = true;
        }
        
        var filePath = Path.Combine(TempDirectory.Default, TempDirectory.Fees, $"{DateTimeOffset.Now.Ticks}-{downloadName}");
        Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
        await using var fileStream = System.IO.File.Create(filePath);
        
        await fileStream.SaveAsAsync(exportResults, excelType: ExcelType.XLSX);

        SheetDataAppendWriter? writer = null;
        var stringBuilder = stringBuilderPool.Get();
        
        while (result is PageResult<FeeListItemDto> {HasNextPage:true} pageResult)
        {
            pageResult.Dispose();
            dto.Page = pageResult.Page + 1;
            dto.Total = pageResult.Total;
            result = await mediator.Send(new FeesQuery(dto), HttpContext.RequestAborted);
            
            if (needToConvert)
            {
                exportResults = mapper.Map<List<T>>(result);
            }
            else
            {
                exportResults = (result as IEnumerable<T>)!;
            }

            writer ??= await fileStream.CreateSheetDataWriterAsync();
            if (writer is null)
            {
                fileStream.Append(exportResults, typeInfoProvider.Get(typeof(T)), stringBuilder);
            }
            else
            {
                await writer.AppendAsync(exportResults, typeInfoProvider.Get(typeof(T)), stringBuilder);
            }
            
            stringBuilder.Clear();
            (exportResults as List<T>)?.Clear();
        }
        fileStream.Close();

        if (writer is not null)
        {
            await writer.CombineAsync();
            await writer.DisposeAsync();
        }
        stringBuilderPool.Return(stringBuilder);
        
        if (result is IDisposable disposable)
        {
            disposable.Dispose();
        }

        return PhysicalFile(Path.GetFullPath(filePath), "application/msexcel;charset=utf-8", HttpUtility.UrlEncode(downloadName));
    }

    /// <summary>
    /// 费项创收分类
    /// </summary>
    /// <param name="feeIds">费项id集合</param>
    /// <returns></returns>
    [HttpPost("monDept")]
    public Task<IEnumerable<FeeMonDeptReDto>> GetFeeMonDept(List<string> feeIds)
    {
        return mediator.Send(new FeesMonDeptQuery(feeIds));
    }

    /// <summary>
    /// 更新费项
    /// </summary>
    /// <param name="dto">费项更新参数</param>
    [HttpPost("/api/fees")]
    [Authorize]
    public  Task<IEnumerable<FeesUpdateResult>> UpdateFeesAsync(FeeUpdateInfoDtoCollection dto)
    {
        return mediator.Send(new FeesUpdateCommand(dto));
    }


    /// <summary>
    /// 费项发票配置
    /// </summary>
    /// <param name="feeIds">费项id集合</param>
    /// <returns></returns>
    [HttpPost("invoiceConfiguration")]
    public Task<IEnumerable<FeeKingdeeMaterialDto>> GetFeeInvoiceConfigurationAsync(List<string> feeIds)
    {
        return mediator.Send(new FeesKingdeeMaterialQuery(feeIds));
    }
}