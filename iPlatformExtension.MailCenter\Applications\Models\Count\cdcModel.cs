﻿using FreeSql.DataAnnotations;
using iPlatformExtension.Model.MailCenter;

namespace iPlatformExtension.MailCenter.Applications.Models.Count
{
    public class CdcModel<T>
    {
        public payload<T> Payload { get; set; }

        public class payload<T>
        {
            public T? Before { get; set; }
            public T? After { get; set; }
            public Source Source { get; set; }
            public string Op { get; set; }
            private long _tsMs;
            public long TsMs
            {
                get => _tsMs + (8 * 3600 * 1000); // 添加8小时的毫秒数
                set => _tsMs = value;
            }
            public object Transaction { get; set; }
            public DateTime TsDateTime
            {
                get => DateTimeOffset.FromUnixTimeMilliseconds(TsMs).DateTime;
            }
        }

        public class Source
        {
            public string Version { get; set; }
            public string Connector { get; set; }
            public string Name { get; set; }
            private long _tsMs;
            public long TsMs
            {
                get => _tsMs + (8 * 3600 * 1000); // 添加8小时的毫秒数
                set => _tsMs = value;
            }
            public DateTime TsDateTime
            {
                get => DateTimeOffset.FromUnixTimeMilliseconds(TsMs).DateTime;
            }
            public string Snapshot { get; set; }
            public string Db { get; set; }
            public object Sequence { get; set; }
            public string Schema { get; set; }
            public string Table { get; set; }
            public string ChangeLsn { get; set; }
            public string CommitLsn { get; set; }
            public int EventSerialNo { get; set; }
        }

    }

    public class FlowRecordDto
    {
        [Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string Id { get; set; }

        /// <summary>
        /// 审核备注
        /// </summary>
        [Column(Name = "audit_remark")]
        public string AuditRemark { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        [Column(Name = "audit_time", DbType = "datetime")]
        public long? AuditTime { get; set; }

        /// <summary>
        /// 提交类型:submit,reject,Transfer
        /// </summary>
        [Column(Name = "audit_type", StringLength = 50)]
        public string AuditType { get; set; }

        /// <summary>
        /// 审核用户id
        /// </summary>
        [Column(Name = "audit_user", StringLength = 50)]
        public string AuditUser { get; set; }

        /// <summary>
        /// 当前节点id
        /// </summary>
        [Column(Name = "cur_node_id", StringLength = 50)]
        public string CurNodeId { get; set; }

        /// <summary>
        /// 流程配置id
        /// </summary>
        [Column(Name = "flow_id", StringLength = 50)]
        public string FlowId { get; set; }

        /// <summary>
        /// 是否当前流程
        /// </summary>
        [Column(Name = "is_current", DbType = "int")]
        public int? IsCurrent { get; set; }

        /// <summary>
        /// 邮件id
        /// </summary>
        [Column(Name = "mail_id", StringLength = 50)]
        public string MailId { get; set; }

        /// <summary>
        /// 上一处理记录
        /// </summary>
        [Column(Name = "pre_record_id", StringLength = 50)]
        public string PreRecordId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Column(Name = "status", StringLength = 50)]
        public string Status { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [Column(Name = "version", StringLength = 50)]
        public string Version { get; set; }
    }
}