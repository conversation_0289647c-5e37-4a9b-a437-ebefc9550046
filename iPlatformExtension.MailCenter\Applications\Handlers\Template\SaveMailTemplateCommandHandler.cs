using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.Template;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Template
{
    /// <summary>
    /// 保存邮件模板命令处理程序
    /// </summary>
    internal sealed class SaveMailTemplateCommandHandler(
        IMailTemplateRepository mailTemplateRepository,
        DefaultRedisCache redisCache,
        IHttpContextAccessor httpContextAccessor
    ) : IRequestHandler<SaveMailTemplateCommand, string>
    {
        private static readonly string lockTemplateNumber = "TemplateNumber";

        /// <summary>
        /// 获取模板编号
        /// </summary>
        /// <returns>模板编号</returns>
        private string GetTemplateNumber()
        {
            lock (lockTemplateNumber)
            {
                var key = "TemplateNumber";
                // 模板编号：FJMB+年份后两位+两位数月份+两位数日期+四位数序号
                var no = redisCache.CurrentDatabase.HashIncrement(key, 1);
                var dt = DateTime.Now;
                if (no < 10)
                {
                    var ts = DateTime.Now.Date.AddDays(1).AddSeconds(-10) - dt;
                    redisCache.CurrentDatabase.KeyExpire(key, ts);
                }
                return $"FJMB{dt:yyMMdd}{no.ToString().PadLeft(4, '0')}";
            }
        }

        public async Task<string> Handle(SaveMailTemplateCommand request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var now = DateTime.Now;

            // 检查是否存在同名模板
            var existingNameTemplate = await mailTemplateRepository
                .Where(t => t.Name == request.Name)
                .WhereIf(!string.IsNullOrWhiteSpace(request.TemplateId), t => t.TemplateId != request.TemplateId)
                .FirstAsync(cancellationToken);

            if (existingNameTemplate != null)
            {
                throw new ApplicationException($"已存在名称为 {request.Name} 的模板");
            }

            if (string.IsNullOrWhiteSpace(request.TemplateId))
            {
                // 生成模板编号
                var templateNo = GetTemplateNumber();

                // 新增模板
                var template = new MailTemplate
                {
                    TemplateId = Guid.NewGuid().ToString(),
                    TemplateNo = templateNo,
                    Name = request.Name,
                    Title = request.Title,
                    Body = request.Body,
                    Sql2 = request.Sql2 ?? string.Empty,
                    IsEnabled = request.IsEnabled,
                    CreateBy = userId,
                    CreateTime = now,
                    UpdateBy = userId,
                    UpdateTime = now
                };

                await mailTemplateRepository.InsertAsync(template, cancellationToken);
                return template.TemplateId;
            }
            else
            {
                // 修改模板
                var template = await mailTemplateRepository
                    .Where(t => t.TemplateId == request.TemplateId)
                    .FirstAsync(cancellationToken)
                    ?? throw new ApplicationException($"未找到ID为 {request.TemplateId} 的模板");

                // 模板编号不变，只修改其他字段
                template.Name = request.Name;
                template.Title = request.Title;
                template.Body = request.Body;
                template.Sql2 = request.Sql2 ?? string.Empty;
                template.IsEnabled = request.IsEnabled;
                template.UpdateBy = userId;
                template.UpdateTime = now;

                await mailTemplateRepository.UpdateAsync(template, cancellationToken);

                return template.TemplateId;
            }
        }
    }
}
