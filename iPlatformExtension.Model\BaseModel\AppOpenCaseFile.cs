using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_open_case_file", DisableSyncStructure = true)]
	public partial class AppOpenCaseFile {

		[ Column(Name = "app_obj_id", StringLength = 50)]
		public string AppObjId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "desc_id", StringLength = 50)]
		public string DescId { get; set; }

		[ Column(Name = "dt_WriDate")]
		public DateTime? DtWriDate { get; set; }

		[ Column(Name = "file_ex", StringLength = 50)]
		public string FileEx { get; set; }

		[ Column(Name = "file_id", StringLength = 50, IsNullable = false)]
		public string FileId { get; set; }

		[ Column(Name = "file_name", StringLength = 500)]
		public string FileName { get; set; }

		[ Column(Name = "file_no", StringLength = 50)]
		public string FileNo { get; set; }

		[ Column(Name = "file_size")]
		public long? FileSize { get; set; }

		[ Column(Name = "from_type", StringLength = 50)]
		public string FromType { get; set; }

		[ Column(Name = "n_CameFileID", StringLength = 50)]
		public string NCameFileID { get; set; }

		[ Column(Name = "n_CaseID", StringLength = 50)]
		public string NCaseID { get; set; }

		[ Column(Name = "n_CStatusID", StringLength = 50)]
		public string NCStatusID { get; set; }

		[ Column(Name = "n_FileID", StringLength = 50)]
		public string NFileID { get; set; }

		[ Column(Name = "notice_date")]
		public DateTime? NoticeDate { get; set; }

		[ Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

		[ Column(Name = "s_FileName", StringLength = 400)]
		public string SFileName { get; set; }

		[ Column(Name = "s_FilePath", StringLength = 500)]
		public string SFilePath { get; set; }

		[ Column(Name = "s_FileType", StringLength = 50)]
		public string SFileType { get; set; }

		[ Column(Name = "s_FileTypeName", StringLength = 50)]
		public string SFileTypeName { get; set; }

		[ Column(Name = "s_Writer", StringLength = 50)]
		public string SWriter { get; set; }

		[ Column(Name = "upload_times")]
		public int? UploadTimes { get; set; }

	}

}
