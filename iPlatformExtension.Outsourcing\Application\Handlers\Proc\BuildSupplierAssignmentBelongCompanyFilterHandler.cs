﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class BuildSupplierAssignmentBelongCompanyFilterHandler(IFreeSql<PlatformFreeSql> freeSql) 
    : IMatchNotificationHandler<NotAssignedProcQueryContext>
{
    private string[] _includeCompanies = [];

    private string[] _excludeCompanies = [];
    
    public ValueTask<bool> MatchAsync(NotAssignedProcQueryContext notification, CancellationToken cancellationToken)
    {
        var filters = notification.Filters;
        if (filters.TryGetValue("belong_company", out var companyFilter) 
            && "custom".Equals(companyFilter.FilterType, StringComparison.OrdinalIgnoreCase))
        {
            _includeCompanies = companyFilter.FilterValue.Split(';');
        }
        
        if (filters.TryGetValue("belong_company_not", out var excludeCompanyFilter) 
            && "custom".Equals(excludeCompanyFilter.FilterType, StringComparison.OrdinalIgnoreCase))
        {
            _excludeCompanies = excludeCompanyFilter.FilterValue.Split(';');
        }
        
        return ValueTask.FromResult(_includeCompanies.Length > 0 || _excludeCompanies.Length > 0);
    }

    public Task HandleAsync(NotAssignedProcQueryContext notification, CancellationToken cancellationToken)
    {
        var includeCompanies = new HashSet<string>(_includeCompanies);
        IEnumerable<string> excludeCompanies = _excludeCompanies;
        var query = notification.Query;

        if (includeCompanies.Count > 0)
        {
            includeCompanies.ExceptWith(_excludeCompanies);
            query.Where(info => includeCompanies.Contains(info.CaseInfo.BelongCompany));
            notification.CountQueryBuilder.Where(info => includeCompanies.Contains(info.CaseInfo.BelongCompany));
        }
        else
        {
            query.Where(info => !freeSql.Select<CaseInfo>()
                .Where(caseInfo => excludeCompanies.Contains(caseInfo.BelongCompany))
                .Any(caseInfo => caseInfo.Id == info.CaseId));
            notification.CountQueryBuilder.Where(info => !freeSql.Select<CaseInfo>()
                .Where(caseInfo => excludeCompanies.Contains(caseInfo.BelongCompany))
                .Any(caseInfo => caseInfo.Id == info.CaseId));
        }
        
        return Task.CompletedTask;
    }
}