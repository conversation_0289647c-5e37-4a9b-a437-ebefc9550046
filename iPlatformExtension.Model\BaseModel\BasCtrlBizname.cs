using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_ctrl_bizname", DisableSyncStructure = true)]
	public partial class BasCtrlBizname {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "attribute", StringLength = 50)]
		public string Attribute { get; set; }

		[ Column(Name = "n_name", StringLength = 500)]
		public string NName { get; set; }

		[ Column(Name = "num")]
		public int? Num { get; set; }

		[ Column(Name = "o_name", StringLength = 500)]
		public string OName { get; set; }

	}

}
