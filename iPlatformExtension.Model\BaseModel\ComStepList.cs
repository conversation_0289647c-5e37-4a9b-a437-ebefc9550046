using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "com_step_list", DisableSyncStructure = true)]
	public partial class ComStepList {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "error_code", StringLength = 50)]
		public string ErrorCode { get; set; }

		[ Column(Name = "form_code", StringLength = 50)]
		public string FormCode { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "name_en_us", StringLength = 50)]
		public string NameEnUs { get; set; }

		[ Column(Name = "name_ja_jp", StringLength = 50)]
		public string NameJaJp { get; set; }

		[ Column(Name = "name_zh_cn", StringLength = 50)]
		public string NameZhCn { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "step_code", StringLength = 50)]
		public string StepCode { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
