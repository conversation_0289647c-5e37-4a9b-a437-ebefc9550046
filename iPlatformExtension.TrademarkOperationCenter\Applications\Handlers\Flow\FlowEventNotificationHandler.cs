﻿using Confluent.Kafka;
using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Common.Mediator.Notifications;
using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.Flow;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow;

internal sealed class FlowEventNotificationHandler(
    IProducer<MessageKey, FlowEventMessage> producer, 
    IFreeSql freeSql, 
    IHostEnvironment hostEnvironment) 
    : IMatchNotificationHandler<EntityChangeNotification>
{
    public ValueTask<bool> MatchAsync(EntityChangeNotification notification, CancellationToken cancellationToken)
    {
        var reports = notification.Reports;
        return new ValueTask<bool>(reports.Any(info => info.EntityType == typeof(SysFlowActivity) && info.Type == DbContext.EntityChangeType.Update) 
                                   && !hostEnvironment.IsDevelopment());
    }

    public async Task HandleAsync(EntityChangeNotification notification, CancellationToken cancellationToken)
    {
        var reports = notification.Reports;
        var changeInfoList = reports.Where(info =>
            info.EntityType == typeof(SysFlowActivity) && info.Type == DbContext.EntityChangeType.Update).ToList();
        
        foreach (var changeInfo in changeInfoList)
        {
            if (changeInfo is not
                {BeforeObject: SysFlowActivity formerActivity, Object: SysFlowActivity currentActivity, Type: DbContext.EntityChangeType.Update}) 
                continue;
            
            // 承办人变化或者流程节点变化
            if (formerActivity.CurUserId == currentActivity.CurUserId &&
                formerActivity.CurNodeId == currentActivity.CurNodeId) 
                continue;
            
            var flowEvents = await freeSql.Select<SysFlowEvent>().WithLock()
                .Where(@event => @event.NodeId == currentActivity.PrevNodeId)
                .Where(@event => @event.SubmitType == currentActivity.PrevAuditTypeId)
                .Where(@event => @event.FlowId == currentActivity.FlowId)
                .Where(@event => @event.EventTriggerType == FlowEventTriggerType.Executed)
                .ToListAsync(@event => new SysFlowEvent()
                {
                    Id = @event.Id,
                    MessageKey = @event.MessageKey,
                    Topic = @event.Topic
                }, cancellationToken);
            
            flowEvents.AddRange(await freeSql.Select<SysFlowEvent>().WithLock()
                .Where(@event => @event.NodeId == currentActivity.CurNodeId)
                .Where(@event => @event.SubmitType == currentActivity.PrevAuditTypeId)
                .Where(@event => @event.FlowId == currentActivity.FlowId)
                .Where(@event => @event.EventTriggerType == FlowEventTriggerType.Executing)
                .ToListAsync(@event => new SysFlowEvent()
                {
                    Id = @event.Id,
                    MessageKey = @event.MessageKey,
                    Topic = @event.Topic
                }, cancellationToken));

            foreach (var sysFlowEvent in flowEvents)
            {
                var eventMessage = new FlowEventMessage()
                {
                    ActivityId = currentActivity.ActivityId,
                    CreationTime = DateTime.Now,
                    NodeId = currentActivity.CurNodeId,
                    PreviousNodeId = currentActivity.PrevNodeId,
                    SubmitType = currentActivity.PrevAuditTypeId,
                    Version = currentActivity.Version
                };

                await producer.ProduceMessageAsync(sysFlowEvent.Topic, sysFlowEvent.MessageKey, eventMessage,
                    cancellationToken);
            }
        }
    }
}