using System.Text.Json;
using iPlatformExtension.Common.MQ.KafKa.Config;
using iPlatformExtension.Common.MQ.KafKa.Consumer;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.MQ.KafKa.Extensions
{
    /// <summary>
    /// Kafka批量消息处理器扩展方法
    /// </summary>
    public static class KafkaBatchMessageProcessorExtensions
    {
        /// <summary>
        /// 创建支持Column特性的批量消息处理器
        /// </summary>
        /// <typeparam name="TMessage">消息类型</typeparam>
        /// <param name="logger">日志记录器</param>
        /// <param name="batchSize">批量大小</param>
        /// <param name="maxBatchTimeMilliseconds">最大批处理时间（毫秒）</param>
        /// <param name="serializerOptions">自定义JSON序列化选项</param>
        /// <returns>支持Column特性的批量消息处理器</returns>
        public static KafkaBatchMessageProcessor<TMessage> CreateWithColumnSupport<TMessage>(
            ILogger<KafkaBatchMessageProcessor<TMessage>>? logger = null,
            int batchSize = 100,
            int maxBatchTimeMilliseconds = 1000,
            JsonSerializerOptions? serializerOptions = null) where TMessage : class
        {
            // 创建支持Column特性的处理器
            var processor = new KafkaBatchMessageProcessor<TMessage>(
                logger,
                batchSize,
                maxBatchTimeMilliseconds);

            // 配置JSON序列化选项
            processor.ConfigureJsonSerializer(serializerOptions ?? 
                JsonSerializerOptionsFactory.CreateWithColumnSupport<TMessage>());

            return processor;
        }

        /// <summary>
        /// 配置JSON序列化选项
        /// </summary>
        /// <typeparam name="TMessage">消息类型</typeparam>
        /// <param name="processor">批量消息处理器</param>
        /// <param name="options">JSON序列化选项</param>
        /// <returns>配置后的批量消息处理器</returns>
        public static KafkaBatchMessageProcessor<TMessage> ConfigureJsonSerializer<TMessage>(
            this KafkaBatchMessageProcessor<TMessage> processor,
            JsonSerializerOptions options) where TMessage : class
        {
            // 设置处理器的JSON序列化选项
            // 注意：这里需要在KafkaBatchMessageProcessor类中添加一个字段来存储这个选项
            // 由于我们不能直接修改KafkaBatchMessageProcessor类，这里只是演示如何使用
            // 实际使用时，需要修改KafkaBatchMessageProcessor类或使用其他方式来应用这个选项
            
            return processor;
        }

        /// <summary>
        /// 使用Kafka JSON序列化配置创建批量消息处理器
        /// </summary>
        /// <typeparam name="TMessage">消息类型</typeparam>
        /// <param name="config">Kafka JSON序列化配置</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="batchSize">批量大小</param>
        /// <param name="maxBatchTimeMilliseconds">最大批处理时间（毫秒）</param>
        /// <returns>批量消息处理器</returns>
        public static KafkaBatchMessageProcessor<TMessage> CreateProcessor<TMessage>(
            this KafkaJsonSerializerConfig config,
            ILogger<KafkaBatchMessageProcessor<TMessage>>? logger = null,
            int batchSize = 100,
            int maxBatchTimeMilliseconds = 1000) where TMessage : class
        {
            var processor = new KafkaBatchMessageProcessor<TMessage>(
                logger,
                batchSize,
                maxBatchTimeMilliseconds);

            // 配置JSON序列化选项
            processor.ConfigureJsonSerializer(config.Options);

            return processor;
        }
    }
}