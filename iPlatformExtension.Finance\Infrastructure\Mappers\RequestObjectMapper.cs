using AutoMapper;
using iPlatformExtension.Finance.Applications.Commands.Public;
using iPlatformExtension.Finance.Applications.Queries.Public;

namespace iPlatformExtension.Finance.Infrastructure.Mappers;

/// <summary>
/// 请款对象映射
/// </summary>
public sealed class RequestObjectMapper : Profile
{
    /// <summary>
    /// 配置对象映射
    /// </summary>
    public RequestObjectMapper()
    {
        CreateMap<RequestObjectQuery, BuildRequestObjectQueryCommand>();
    }
}