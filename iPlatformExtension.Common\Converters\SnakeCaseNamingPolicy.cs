using System.Text;
using System.Text.Json;

namespace iPlatformExtension.Common.Converters
{
    /// <summary>
    /// 下划线命名策略
    /// </summary>
    public class SnakeCaseNamingPolicy : JsonNamingPolicy
    {
        /// <summary>
        /// 将属性名称转换为下划线命名方式
        /// </summary>
        /// <param name="name">属性名称</param>
        /// <returns>转换后的名称</returns>
        public override string ConvertName(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                return name;
            }

            var result = new StringBuilder();
            result.Append(char.ToLowerInvariant(name[0]));

            for (var i = 1; i < name.Length; i++)
            {
                var current = name[i];
                if (char.IsUpper(current))
                {
                    result.Append('_');
                    result.Append(char.ToLowerInvariant(current));
                }
                else
                {
                    result.Append(current);
                }
            }

            return result.ToString();
        }
    }
}