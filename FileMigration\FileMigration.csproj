<Project Sdk="Microsoft.NET.Sdk.Worker">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UserSecretsId>dotnet-FileMigration-DD7E70E1-18AD-4393-A4FC-373D06A957A7</UserSecretsId>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <LangVersion>13</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
        <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\iPlatformExtension.Common\iPlatformExtension.Common.csproj" />
    </ItemGroup>
</Project>
