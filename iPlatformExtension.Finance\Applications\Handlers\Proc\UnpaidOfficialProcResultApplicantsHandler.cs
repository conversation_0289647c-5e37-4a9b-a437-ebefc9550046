﻿using iPlatformExtension.Finance.Applications.Queries.Proc;
using iPlatformExtension.Service.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Proc;

internal sealed class UnpaidOfficialProcResultApplicantsHandler(IApplicantQueryService applicantQueryService) 
    : INotificationHandler<UnpaidOfficialProcResultNotification>
{
    public async Task Handle(UnpaidOfficialProcResultNotification notification, CancellationToken cancellationToken)
    {
        var results = notification.Results;
        var caseIds = results.Select(dto => dto.CaseId).ToList();

        var applicants = await applicantQueryService.GetApplicantsByCaseIdsAsync(caseIds);
        foreach (var dto in results)
        {
            if (applicants.TryGetValue(dto.CaseId, out var infos))
            {
                dto.Applicants = infos;
            }
        }
    }
}