﻿using System.ComponentModel;
using System.Net.Mime;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Outsourcing.Application.Commands.Case;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Outsourcing.Controllers;

[Tags("境外代理联系人", "供应商联系人")]
[Description("境外代理联系人控制器")]
[ApiController]
[Route("supplier-contact")]
[Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
[ProducesResponseType<ResultData>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
[Consumes(MediaTypeNames.Application.Json)]
public sealed class SupplierContactController(ISender sender) : ControllerBase
{
    [HttpPost("proc")]
    [EndpointName(nameof(AddProcForeignSupplierContactAsync))]
    [EndpointSummary("添加任务境外代理联系人")]
    [EndpointDescription("用户手动添加任务下的境外代理联系人。目前只有专利任务和项目任务在用")]
    public Task AddProcForeignSupplierContactAsync([FromBody] AddProcSupplierContactCommand dto) 
        => sender.Send(dto, HttpContext.RequestAborted);
    
    [HttpDelete("proc")]
    [EndpointName(nameof(RemoveProcForeignSupplierContactAsync))]
    [EndpointSummary("移除任务境外代理联系人")]
    [EndpointDescription("用户手动移除任务下的境外代理联系人。目前只有专利和项目任务在用")]
    public Task RemoveProcForeignSupplierContactAsync([FromBody] RemoveProcSupplierContactCommand dto) 
        => sender.Send(dto, HttpContext.RequestAborted);
    
    [HttpPost("case")]
    [EndpointName(nameof(AddCaseForeignSupplierContactAsync))]
    [EndpointSummary("添加案件境外代理联系人")]
    [EndpointDescription("用户手动添加案件下的境外代理联系人。目前只有专利案件和项目案件在用")]
    public Task AddCaseForeignSupplierContactAsync([FromBody] CreateCaseSupplierContactCommand dto)
        => sender.Send(dto, HttpContext.RequestAborted);
    
    [HttpDelete("case")]
    [EndpointName(nameof(RemoveCaseForeignSupplierContactAsync))]
    [EndpointSummary("移除案件境外代理联系人")]
    [EndpointDescription("用户手动移除案件下的境外代理联系人。目前只有专利和项目案件在用")]
    public Task RemoveCaseForeignSupplierContactAsync([FromBody] RemoveCaseSupplierContactCommand dto) 
        => sender.Send(dto, HttpContext.RequestAborted);
}