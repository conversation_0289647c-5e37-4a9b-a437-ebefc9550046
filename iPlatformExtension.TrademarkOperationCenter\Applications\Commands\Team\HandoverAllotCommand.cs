﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;

/// <summary>
/// 移交团队
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="TeamId">移交团队id</param>
/// <param name="UserId">移交用户id</param>
/// <param name="Remark">备注</param>
public record HandoverAllotCommand(List<string> ProcId, string TeamId,string UserId, string? Remark) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

