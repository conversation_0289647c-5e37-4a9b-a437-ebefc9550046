﻿using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Net.Mime;
using System.Text.Json;
using System.Text.Json.Serialization;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Model.Dto;
using Xunit.Abstractions;

namespace CommonTest;

public class HttpClientTest
{
    private readonly HttpClient _httpClient = new ();

    private readonly JsonSerializerOptions _serializerOptions = new();

    private readonly ITestOutputHelper _testOutputHelper;

    public HttpClientTest(ITestOutputHelper testOutputHelper)
    {
        _testOutputHelper = testOutputHelper;
        _serializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        _serializerOptions.Converters.Add(new DateTimeJsonConverter());
        _serializerOptions.Converters.Add(new DateOnlyJsonConverter());
        _serializerOptions.Converters.Add(new JsonStringEnumConverter());
    }
    
    [Theory]
    [InlineData("excel_test/批更新调整权值模板-空数据.xlsx")]
    [InlineData("excel_test/批更新调整权值模板-纯文本数据.xlsx")]
    public async Task TestPostExcel(string excelFilePath)
    {
        _httpClient.BaseAddress = new Uri("http://localhost:4871");
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer",
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3Mzc3NzQzOTcsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6ImQ3ZWQ5MzdmLTJhZGEtNDJiZS1hZTdhLTRmYWRlNzllZGEwMyIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.JcyGwgNpN_2V-a7KUl7_bKkQFt-h1-u8cI6AYCnx2pg");

        var content = new MultipartFormDataContent();
        
        var fileContent = new StreamContent(File.OpenRead(excelFilePath));
        fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        content.Add(fileContent, "file", Path.GetFileName(excelFilePath));

        var response = await _httpClient.PatchAsync("/trademark-commission/domestic-weight/points", content);
        var result = await response.Content.ReadFromJsonAsync<ResultData>(_serializerOptions);
        _testOutputHelper.WriteLine("Response Status Code:{0}", response.StatusCode);
        _testOutputHelper.WriteLine(result?.Message);
        
        Assert.True(response.IsSuccessStatusCode);
        Assert.True(result?.Success);
    }

    [InlineData("http://************:8848")]
    [InlineData("http://************:8848/")]
    [InlineData("************:8848")]
    [Theory]
    public void TestUriFormat(string uri)
    {
        var result = new Uri(uri);
    }
}