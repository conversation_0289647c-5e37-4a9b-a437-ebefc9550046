﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 文件上传到旧系统DTO
/// </summary>
public class CaseEntrustUploadDto
{
    /// <summary>
    /// 文件描述id
    /// </summary>
    public string DescId { get; set; } = null!;

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = null!;

    /// <summary>
    /// 关联对象id
    /// </summary>
    public string ObjectId { get; set; } = null!;

    /// <summary>
    /// 创建人
    /// </summary>
    public string Creator { get; set; } = default!;


    /// <summary>
    /// 文件
    /// </summary>
    public byte[] File { get; set; } = Array.Empty<byte>();
}