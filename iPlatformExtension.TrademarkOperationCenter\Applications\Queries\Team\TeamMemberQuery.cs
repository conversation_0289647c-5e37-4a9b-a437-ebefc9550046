﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;

/// <summary>
/// 团队成员查询
/// </summary>
/// <param name="Name"></param>
public record TeamMemberQuery(string? Name, string TeamId) : PageModel, IRequest<IEnumerable<TeamMemberDto>>;

