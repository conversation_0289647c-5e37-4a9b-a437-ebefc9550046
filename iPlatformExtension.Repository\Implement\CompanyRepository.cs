﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class CompanyRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    DefaultRedisCache redisCache,
    IMemoryCache memoryCache,
    CacheExpirationToken<BasCompany> expirationToken)
    : BaseRepository<BasCompany, string>(freeSql), ICompanyRepository
{
    IMemoryCache ICacheableRepository<string, BasCompany>.MemoryCache => memoryCache;

    CacheExpirationToken<BasCompany> ICacheableRepository<string, BasCompany>.ExpirationToken => expirationToken;
    
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}