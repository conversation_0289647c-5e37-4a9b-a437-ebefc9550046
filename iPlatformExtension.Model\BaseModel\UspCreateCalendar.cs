using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "usp_create_calendar", DisableSyncStructure = true)]
	public partial class UspCreateCalendar {

		[ Column(Name = "@end_date", DbType = "date")]
		public DateTime EndDate { get; set; }

		[ Column(Name = "@start_date", DbType = "date")]
		public DateTime StartDate { get; set; }

	}

}
