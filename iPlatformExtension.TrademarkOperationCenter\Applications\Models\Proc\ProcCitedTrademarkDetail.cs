﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;

/// <summary>
/// 引证商标详情
/// </summary>
public class ProcCitedTrademarkDetail 
{
    /// <summary>
    /// id主键
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 注册号
    /// </summary>
    public string RegisterNumber { get; set; } = string.Empty;

    /// <summary>
    /// 尼斯分类类别
    /// </summary>
    public string TrademarkClasses { get; set; } = string.Empty;

    /// <summary>
    /// 联系人名称
    /// </summary>
    public string Contactor { get; set; } = string.Empty;

    /// <summary>
    /// 联系人地址
    /// </summary>
    public string ContactAddress { get; set; } = string.Empty;

    /// <summary>
    /// 任务id
    /// </summary>
    public string ProcId { get; set; } = string.Empty;
}