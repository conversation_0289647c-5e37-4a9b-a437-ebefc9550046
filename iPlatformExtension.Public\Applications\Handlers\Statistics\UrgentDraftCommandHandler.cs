﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Commands.Statistics;
using iPlatformExtension.Public.Applications.Models.Flow;
using iPlatformExtension.Public.Applications.Queries.Flow;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Statistics
{
    /// <summary>
    /// 发送消息通知
    /// </summary>
    internal sealed class UrgentDraftCommandHandler(IFreeSql freeSql, IMediator mediator)
        : IRequestHandler<UrgentDraftCommand>
    {
        public async Task Handle(UrgentDraftCommand request, CancellationToken cancellationToken)
        {
            var sendList = new List<GetUrgentDraftUserListDto>();

            foreach (var proc in request.ProcId)
            {
                sendList.AddRange(await mediator.Send(new GetUrgentDraftUserListQuery(proc), cancellationToken));
            }
            foreach (var warningType in request.WarningType)
            {
                switch (warningType)
                {
                    case 1:
                        await mediator.Send(new UrgentDraftMailCommand(ProcId: request.ProcId, DateType: request.DateType, sendList, request.Warning), cancellationToken);
                        break;
                    case 2:
                        await mediator.Send(new UrgentDraftWeChatCommand(ProcId: request.ProcId, DateType: request.DateType, sendList, request.Warning), cancellationToken);
                        break;
                }
            }

            return;

        }
    }
}

