using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_invoice_batch_list", DisableSyncStructure = true)]
	public partial class BillInvoiceBatchList {

		[ Column(Name = "list_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ListId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; }

		[ Column(Name = "bill_no", StringLength = 500)]
		public string BillNo { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 100)]
		public string CreateUserId { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "error_columns", StringLength = 500)]
		public string ErrorColumns { get; set; }

		[ Column(Name = "error_message", StringLength = 4000)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		[ Column(Name = "invoice_amount_gs", DbType = "money")]
		public decimal? InvoiceAmountGs { get; set; }

		[ Column(Name = "invoice_amount_p", DbType = "money")]
		public decimal? InvoiceAmountP { get; set; }

		[ Column(Name = "invoice_amount_ps", DbType = "money")]
		public decimal? InvoiceAmountPs { get; set; }

		[ Column(Name = "invoice_amount_z", DbType = "money")]
		public decimal? InvoiceAmountZ { get; set; }

		[ Column(Name = "invoice_date")]
		public DateTime? InvoiceDate { get; set; }

		[ Column(Name = "invoice_no", StringLength = 200)]
		public string InvoiceNo { get; set; }

		[ Column(Name = "invoice_no_gs", StringLength = 100)]
		public string InvoiceNoGs { get; set; }

		[ Column(Name = "invoice_no_p", StringLength = 300)]
		public string InvoiceNoP { get; set; }

		[ Column(Name = "invoice_no_ps", StringLength = 100)]
		public string InvoiceNoPs { get; set; }

		[ Column(Name = "invoice_no_z", StringLength = 300)]
		public string InvoiceNoZ { get; set; }

		[ Column(Name = "invoice_type", StringLength = 50)]
		public string InvoiceType { get; set; }

		[ Column(Name = "receiver", StringLength = 50)]
		public string Receiver { get; set; }

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		[ Column(Name = "submit_is_enabled")]
		public bool? SubmitIsEnabled { get; set; } = true;

		[ Column(Name = "title", StringLength = 100)]
		public string Title { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 100)]
		public string UpdateUserId { get; set; }

	}

}
