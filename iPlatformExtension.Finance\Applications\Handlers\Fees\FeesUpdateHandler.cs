﻿using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeesUp<PERSON><PERSON><PERSON><PERSON>(IMediator mediator)
    : IRequestHandler<FeesUpdateCommand, IEnumerable<FeesUpdateResult>>
{
    public async Task<IEnumerable<FeesUpdateResult>> Handle(FeesUpdateCommand request, CancellationToken cancellationToken)
    {
        var dto = request.Dto;
        var results = new List<FeesUpdateResult>(dto.UpdateInfo.Count());
        
        foreach (var updateDto in dto.UpdateInfo)
        {
            var updateContext = new CaseFeeUpdateContext(updateDto.Operation, updateDto.FeeId, updateDto);
            await mediator.Publish(updateContext, cancellationToken);
            results.Add(updateContext.FeesUpdateResult);
        }

        return results;
    }
}