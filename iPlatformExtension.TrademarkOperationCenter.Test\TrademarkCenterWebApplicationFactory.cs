﻿using System.Net.Http.Headers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;

namespace iPlatformExtension.TrademarkOperation.Test;

public class TrademarkCenterWebApplicationFactory : WebApplicationFactory<Program>
{
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.UseEnvironment("Local");
        base.ConfigureWebHost(builder);
    }

    protected override void ConfigureClient(HttpClient client)
    {
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer",
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDMwNjM1MDMsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6IjllNWMyZGNiLWZhMDQtNGU1MC05NDNiLTcwMGZjYjIxY2RlNiIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.QU-kt6gHk-TPIaOexvbIrkJcb70gQdzdK48himZxrBc");
        base.ConfigureClient(client);
    }
}