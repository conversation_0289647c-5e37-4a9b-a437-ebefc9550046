﻿using iPlatformExtension.Public.Applications.Commands.SignLR;
using iPlatformExtension.Public.Hubs;
using MediatR;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Public.Applications.Handlers.SignLR
{
    /// <summary>
    /// 消息发送命令处理程序
    /// </summary>
    internal sealed class MessageSendCommandHandler : IRequestHandler<MessageSendCommand>
    {
        private readonly IHubContext<NotificationHub> _hubContext;
        private readonly IHubContext<FlowHub> _flowHubContext;
        private readonly ILogger<MessageSendCommandHandler> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MessageSendCommandHandler(
            IHubContext<NotificationHub> hubContext,
            IHubContext<FlowHub> flowHubContext,
            ILogger<MessageSendCommandHandler> logger)
        {
            _hubContext = hubContext;
            _flowHubContext = flowHubContext;
            _logger = logger;
        }

        /// <summary>
        /// 处理消息发送命令
        /// </summary>
        public async Task Handle(MessageSendCommand request, CancellationToken cancellationToken)
        {
                    await _flowHubContext.Clients.User(request.UserId)
                        .SendAsync(request.Method, request.Messages, cancellationToken);
        }
    }
}

