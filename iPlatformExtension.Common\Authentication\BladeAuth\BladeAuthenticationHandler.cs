﻿using System.Security.Claims;
using System.Text.Encodings.Web;
using iPlatformExtension.Common.ObjectPools.ApiResult;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;


namespace iPlatformExtension.Common.Authentication.BladeAuth;

/// <summary>
/// Blade X身份验证
/// </summary>
/// <remarks>
/// 构造函数依赖注入
/// </remarks>
/// <param name="options">BladeAuth方案选项</param>
/// <param name="logger">日志组件</param>
/// <param name="encoder">urlEncoder</param>
/// <param name="apiResultPool">同意返回结果对象池</param>
/// <param name="jsonOptions">json序列化选项</param>
public sealed class BladeAuthenticationHandler(
    IOptionsMonitor<BladeAuthOptions> options,
    ILoggerFactory logger, UrlEncoder encoder,
    ApiResultPool<ResultData> apiResultPool, IOptions<JsonOptions> jsonOptions) : 
    AuthenticationHandler<BladeAuthOptions>(options, logger, encoder)
{
    /// <summary>
    /// 返回结果对象池
    /// </summary>
    private readonly ApiResultPool<ResultData> _apiResultPool = apiResultPool;

    /// <summary>
    /// json序列化选项配置
    /// </summary>
    private readonly IOptions<JsonOptions> _jsonOptions = jsonOptions;

    /// <summary>
    /// 验证处理的事件
    /// </summary>
    /// <value></value>
    internal new BladeAuthEvents Events
    {
        get => (BladeAuthEvents) (base.Events ??= new BladeAuthEvents());
        set => base.Events = value;
    }

    /// <summary>
    /// 处理身份验证。
    /// 验证token合法性，并回调相关事件
    /// </summary>
    /// <returns>验证结果</returns>
    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var messageReceivedContext = new BladeAuthMessageReceivedContext(Context, Scheme, Options);
        await Events.MessageReceived(messageReceivedContext);
        
        if (messageReceivedContext.Result?.Succeeded ?? false)
        {
            return messageReceivedContext.Result;
        }

        Logger.LogInformation("TokenPath: {TokenPath}\nToken => {TokenName}:{Token}", messageReceivedContext.TokenPath,
            messageReceivedContext.TokenName, messageReceivedContext.Token);

     
        if (string.IsNullOrEmpty(messageReceivedContext.Token))
        {
            Logger.LogWarning("token为空");
            messageReceivedContext.NoResult();
            return messageReceivedContext.Result!;
        }

        var token = messageReceivedContext.Token;
        var validationParameters = Options.TokenValidationParameters.Clone();
        ClaimsPrincipal? principal = null;
        SecurityToken? validatedToken = null;
        Exception? ex = null;
        foreach (var tokenValidator in Options.SecurityTokenValidators)
        {
            if (!tokenValidator.CanValidateToken || !tokenValidator.CanReadToken(token)) continue;
            try
            {
                principal = tokenValidator.ValidateToken(token, validationParameters, out validatedToken);
                break;
            }
            catch (Exception e)
            {
                ex = e;
            }
        }

        var authenticatedContext = new BladeAuthenticatedContext(Context, Scheme, Options);

        if (principal is not null && validatedToken is not null)
        {
            authenticatedContext.Principal = principal;
            authenticatedContext.SecurityToken = validatedToken;

            if (Options.SaveToken)
            {
                authenticatedContext.Properties.StoreTokens([
                    new AuthenticationToken()
                    {
                        Name = BladeAuthOptions.SchemeName,
                        Value = token
                    }
                ]);
            }
                
            var result = await Events.TokenValidated(authenticatedContext);

            if (result is not null)
            {
                return result;
            }

            authenticatedContext.Success();
            await Events.AuthenticationSuccess(authenticatedContext);

            return authenticatedContext.Result!;
        }
        
        if (ex is not null)
        {
            authenticatedContext.Exception = ex;
        }
        else
        {
            authenticatedContext.FailureMessage = $"token[{token}]验证失败";
        }

        authenticatedContext.Fail();
        await Events.AuthenticationFailed(authenticatedContext);

        return authenticatedContext.Result!;
    }

    /// <summary>
    /// 挑战处理
    /// </summary>
    /// <param name="properties">身份验证属性。</param>
    /// <returns>返回401和统一返回结果</returns>
    protected override async Task HandleChallengeAsync(AuthenticationProperties properties)
    {
        const string authenticatedFailedMessage = "用户信息异常或过期，请重新登录";
        var authenticationResult = Context.Features.Get<IAuthenticateResultFeature>()?.AuthenticateResult;
        if (authenticationResult is not null)
        {
            Logger.LogError(authenticationResult.Failure, authenticatedFailedMessage);
        }
        
        if (!Response.HasStarted)
        {
            Response.StatusCode = StatusCodes.Status401Unauthorized;
            var jsonOptions = _jsonOptions.Value;
            var apiResult = _apiResultPool.Get().Fail(authenticatedFailedMessage);
            await Response.WriteAsJsonAsync(apiResult, jsonOptions.JsonSerializerOptions, Context.RequestAborted);
            _apiResultPool.Return(apiResult);
        }
    }

    protected override async Task HandleForbiddenAsync(AuthenticationProperties properties)
    {
        if (!Response.HasStarted)
        {
            Response.StatusCode = StatusCodes.Status403Forbidden;
            var jsonOptions = _jsonOptions.Value;
            var apiResult = _apiResultPool.Get().Fail("没权限");
            await Response.WriteAsJsonAsync(apiResult, jsonOptions.JsonSerializerOptions, Context.RequestAborted);
            _apiResultPool.Return(apiResult);
        }
    }
}