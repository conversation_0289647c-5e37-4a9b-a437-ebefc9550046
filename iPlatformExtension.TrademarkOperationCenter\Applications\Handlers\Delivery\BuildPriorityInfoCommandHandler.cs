﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

/// <summary>
/// 优先权构建处理者
/// </summary>
internal sealed class BuildPriorityInfoCommandHandler(IDeliveryPriorityRepository deliveryPriorityRepository)
    : IRequestHandler<BuildPriorityInfoCommand>
{
    /// <summary>
    /// 构建优先权信息
    /// </summary>
    /// <param name="request">任务和递交西信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task Handle(BuildPriorityInfoCommand request, CancellationToken cancellationToken)
    {
        
        var caseId = request.CaseId;
        var priorityInfos = await deliveryPriorityRepository.Orm.Select<CasePriorityInfo>().NoTracking().WithLock()
            .Where(info => info.CaseId == caseId).ToListAsync(info => new DeliPriority()
            {
                Id = 0,
                DasCode = info.DasCode,
                CountryId = info.PriorityCountryId,
                PriorityDate = info.PriorityDate,
                PriorityNo = info.PriorityNo,
                PriorityId = info.PriorityId,
                Volume = info.Volume,
                ProcId = request.ProcId
            }, cancellationToken);

        await deliveryPriorityRepository.InsertAsync(priorityInfos, cancellationToken);
    }
}