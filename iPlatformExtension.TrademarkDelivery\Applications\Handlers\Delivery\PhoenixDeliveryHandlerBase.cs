﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery;

public abstract class PhoenixDeliveryHandleBase<TCommand, TRequest, TResponse> : IDeliveryHandler<TCommand, TRequest, TResponse>
where TCommand : IDeliveryCommand
{
    protected DeliInfo? _deliveryInfo;

    protected PhoenixClient? _phoenixClient;

    protected readonly PhoenixClientFactory _phoenixClientFactory;

    protected PhoenixDeliveryHandleBase(
        ObjectPool<StringBuilder> stringBuilderPool, 
        IDeliveryInfoRepository deliveryInfoRepository, 
        IDeliveryHistoryRepository deliveryHistoryRepository, 
        UnitOfWorkManager unitOfWorkManager, ILoggerFactory loggerFactory, 
        PhoenixClientFactory phoenixClientFactory)
    {
        StringBuilderPool = stringBuilderPool;
        DeliveryInfoRepository = deliveryInfoRepository;
        DeliveryHistoryRepository = deliveryHistoryRepository;
        UnitOfWorkManager = unitOfWorkManager;
        Logger = loggerFactory.CreateLogger(GetType());
        _phoenixClientFactory = phoenixClientFactory;
    }
    
    public ObjectPool<StringBuilder> StringBuilderPool { get; }

    public IDeliveryInfoRepository DeliveryInfoRepository { get; }

    public IDeliveryHistoryRepository DeliveryHistoryRepository { get; }

    public UnitOfWorkManager UnitOfWorkManager { get; }

    public abstract TrademarkDeliveryOperation DeliveryOperation { get; }

    public DeliInfo? DeliveryInfo
    {
        get => _deliveryInfo;
        set => _deliveryInfo = value;
    }

    public ILogger Logger { get; }

    public IDeliveryHandler<TCommand, TRequest, TResponse> Handler => this;

    public abstract Task<TRequest> CreateParameterAsync(CancellationToken cancellationToken = default);

    public abstract Task<TResponse?> HandleRemoteDeliveryAsync(TRequest request, CancellationToken cancellationToken = default);

    public abstract Task HandleDeliveryInfoAsync(TResponse response, CancellationToken cancellationToken = default);
    
    public virtual async Task InitializeAsync(TCommand command, CancellationToken cancellationToken)
    {
        await Handler.InitializeInternalAsync(command, cancellationToken);

        ArgumentNullException.ThrowIfNull(DeliveryInfo);
        
        if (string.IsNullOrWhiteSpace(DeliveryInfo.DeliveryKey))
        {
            throw new PropertyMissingException(DeliveryInfo.ProcId, DeliveryInfo.DeliveryKey, "递交任务", "递交key");
        }

        _phoenixClient = _phoenixClientFactory.CreateClient(DeliveryInfo.DeliveryKey);
    }
}