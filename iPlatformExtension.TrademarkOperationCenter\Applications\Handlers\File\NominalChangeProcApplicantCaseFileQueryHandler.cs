﻿using System.Collections.ObjectModel;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.File;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File;

internal sealed class NominalChangeProcApplicantCaseFileQueryHandler : 
    IMatchTrademarkDeliveryBusinessTypeNotificationHandler<ProcApplicantCaseFileDescriptionQuery>
{
    private static readonly IEnumerable<string> matchBusinessTypes = ["3", "4", "5"];
    
    private static readonly IReadOnlyDictionary<string, FileDescriptionKey> descriptionKeys = ReadOnlyDictionary<string, FileDescriptionKey>.Empty;

    IEnumerable<string> IMatchTrademarkDeliveryBusinessTypeNotificationHandler<ProcApplicantCaseFileDescriptionQuery>.MatchBusinessTypes => matchBusinessTypes;

    IReadOnlyDictionary<string, FileDescriptionKey> IMatchTrademarkDeliveryBusinessTypeNotificationHandler<ProcApplicantCaseFileDescriptionQuery>.DescriptionKeys => descriptionKeys;

    IFileDescriptionRepository IMatchTrademarkDeliveryBusinessTypeNotificationHandler<ProcApplicantCaseFileDescriptionQuery>.FileDescriptionRepository => throw new NotImplementedException();

    Task IMatchNotificationHandler<ProcApplicantCaseFileDescriptionQuery>.HandleAsync(ProcApplicantCaseFileDescriptionQuery notification, CancellationToken cancellationToken)
    {
        notification.Files = Array.Empty<FileListDto>();
        
        return Task.CompletedTask;
    }
}