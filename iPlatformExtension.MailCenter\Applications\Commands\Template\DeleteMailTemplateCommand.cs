using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.MailCenter.Applications.Commands.Template
{
    /// <summary>
    /// 删除邮件模板命令
    /// </summary>
    /// <param name="TemplateId">模板ID</param>
    public record DeleteMailTemplateCommand(
        [Required(ErrorMessage = "模板ID不能为空")] string TemplateId
    ) : IRequest, IUnitOfWorkCommandMysql;
}
