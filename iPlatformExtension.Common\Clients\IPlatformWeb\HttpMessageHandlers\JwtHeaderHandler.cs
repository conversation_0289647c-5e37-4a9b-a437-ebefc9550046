using System.IdentityModel.Tokens.Jwt;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using iPlatformExtension.Common.Clients.IPlatformWeb.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace iPlatformExtension.Common.Clients.IPlatformWeb.HttpMessageHandlers;

/// <summary>
/// JWT头部处理
/// </summary>
public class JwtHeaderHandler : DelegatingHandler
{
    private readonly ILogger _logger;

    private readonly JwtSecurityTokenHandler _tokenHandler;

    private readonly IOptionsMonitor<PlatformHttpClientOptions> _clientOptions;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="clientOptions">client选项</param>
    /// <param name="loggerFactory">日志</param>
    public JwtHeaderHandler(IOptionsMonitor<PlatformHttpClientOptions> clientOptions, ILoggerFactory loggerFactory)
    {
        _clientOptions = clientOptions;
        _logger = loggerFactory.CreateLogger(GetType());
        _tokenHandler = new JwtSecurityTokenHandler();
    }

    /// <summary>
    /// 组装并添加JWT
    /// </summary>
    /// <param name="request">请求信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应信息</returns>
    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var options = _clientOptions.CurrentValue;
        var tokenOptions = options.Token;
        var tokenDescriptor = new SecurityTokenDescriptor()
        {
            IssuedAt = DateTime.UtcNow,
            Expires = DateTime.UtcNow.Add(tokenOptions.TokenLifeTime),
            Issuer = tokenOptions.Issuer,
            Audience = tokenOptions.Audience,
            NotBefore = tokenOptions.NotBefore,
            Subject = new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.Name, "admin")
            }),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(Encoding.UTF8.GetBytes(tokenOptions.SecretKey)), SecurityAlgorithms.HmacSha256Signature),
        };
        var token = _tokenHandler.CreateEncodedJwt(tokenDescriptor);
        _logger.LogDebug("JWT:  {Token}", token);
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
        return base.SendAsync(request, cancellationToken);
    }
}