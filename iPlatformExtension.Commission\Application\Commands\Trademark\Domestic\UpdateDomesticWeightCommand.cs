﻿using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;

internal sealed record UpdateDomesticWeightCommand(
    string ProcId,
    JsonPatchDocument<DomesticWeightPatchDto> DomesticWeightPatch,
    bool HandleException = false)
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>
{
    public void Deconstruct(out string procId, out JsonPatchDocument<DomesticWeightPatchDto> patch)
    {
        procId = ProcId;
        patch = DomesticWeightPatch;
    }
}