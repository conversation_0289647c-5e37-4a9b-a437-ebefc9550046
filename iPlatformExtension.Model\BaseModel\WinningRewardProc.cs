﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 胜诉奖励任务
	/// </summary>
	[Table(Name = "winning_reward_proc", DisableSyncStructure = true)]
	public partial class WinningRewardProc
	{

		/// <summary>
		/// 任务id
		/// </summary>
		[Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ProcId { get; set; } = null!;

		/// <summary>
		/// 申请号
		/// </summary>
		[Column(Name = "app_no", StringLength = 50, IsNullable = false)]
		public string AppNo { get; set; } = "";

		/// <summary>
		/// 案件id
		/// </summary>
		[Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; } = null!;

		/// <summary>
		/// 案件流向
		/// </summary>
		[Column(Name = "case_direction")]
		public string CaseDirection { get; set; } = string.Empty;

		/// <summary>
		/// 案件类型
		/// </summary>
		[Column(Name = "case_type")]
		public string CaseType { get; set; } = string.Empty;

		/// <summary>
		/// 案件名称
		/// </summary>
		[Column(Name = "case_name", StringLength = 1000, IsNullable = false)]
		public string CaseName { get; set; } = "";

		/// <summary>
		/// 计提时间
		/// </summary>
		[Column(Name = "commission_date")]
		public DateTime CommissionDate { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "creation_time", InsertValueSql = "getdate()")]
		public DateTime CreationTime { get; set; }

		/// <summary>
		/// 创建者
		/// </summary>
		[Column(Name = "creator", StringLength = 50, IsNullable = false)]
		public string Creator { get; set; } = "";

		/// <summary>
		/// 任务名称id
		/// </summary>
		[Column(Name = "ctrl_proc_id", StringLength = 50, IsNullable = false)]
		public string CtrlProcId { get; set; } = null!;

		/// <summary>
		/// 客户id
		/// </summary>
		[Column(Name = "customer_id", StringLength = 50, IsNullable = false)]
		public string CustomerId { get; set; } = null!;

		/// <summary>
		/// 客户名称
		/// </summary>
		[Column(Name = "customer_name", StringLength = 1000, IsNullable = false)]
		public string CustomerName { get; set; } = "";

		/// <summary>
		/// 月
		/// </summary>
		[Column(Name = "month")]
		public int Month { get; set; }

		/// <summary>
		/// 任务名称
		/// </summary>
		[Column(Name = "proc_name", StringLength = 1000, IsNullable = false)]
		public string ProcName { get; set; } = "";

		/// <summary>
		/// 任务编号
		/// </summary>
		[Column(Name = "proc_no", StringLength = 50, IsNullable = false)]
		public string ProcNo { get; set; } = "";

		/// <summary>
		/// 推送状态
		/// </summary>
		[Column(Name = "pushed")]
		public bool Pushed { get; set; } = false;

		/// <summary>
		/// 注册号
		/// </summary>
		[Column(Name = "register_no", StringLength = 50, IsNullable = false)]
		public string RegisterNo { get; set; } = "";

		/// <summary>
		/// 裁定结果
		/// </summary>
		[Column(Name = "ruling_result", StringLength = 50, IsNullable = false)]
		public string RulingResult { get; set; } = "";

		/// <summary>
		/// 是否情势变更
		/// </summary>
		[Column(Name = "situation_changed")]
		public bool? SituationChanged { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 更新者
		/// </summary>
		[Column(Name = "updater", StringLength = 50, IsNullable = false)]
		public string Updater { get; set; } = "";

		/// <summary>
		/// 我方文号
		/// </summary>
		[Column(Name = "volume", StringLength = 50, IsNullable = false)]
		public string Volume { get; set; } = string.Empty;

		/// <summary>
		/// 年
		/// </summary>
		[Column(Name = "year")]
		public int Year { get; set; }

		/// <summary>
		/// 受益人
		/// </summary>
		[Navigate(nameof(WinningRewardUser.ProcId))]
		public List<WinningRewardUser>? RewardUsers
		{
			get => Beneficiaries.Select(pair => pair.Value).ToList();

			set
			{
				Beneficiaries = new SortedList<WinningRewardBeneficiaryType, WinningRewardUser>(2);
				foreach (var winningRewardUser in value ?? Enumerable.Empty<WinningRewardUser>())
				{
					Beneficiaries.Add(winningRewardUser.BeneficiaryType, winningRewardUser);
				}
			}
		}

		/// <summary>
		/// 受益人
		/// </summary>
		public SortedList<WinningRewardBeneficiaryType, WinningRewardUser> Beneficiaries { get; set; } = null!;

	}

}
