syntax = "proto3";

option csharp_namespace = "iPlatformExtension.Messages.Mails";

package mail;

message NotificationMail {
  string subject = 1;
  repeated string receivers = 2;
  string sender = 3;
  string bodyText = 4;
  repeated MailAttachment attachments = 5;
  string messageId = 6;
  BodyTemplate bodyTemplate = 7;
  bool isHtmlBody = 8;
  repeated MailReceiverAccount receiverAccounts = 9;
}

message MailAttachment {
  string fileName = 1;
  bytes data = 2;
}

message MailReceiverAccount {
  string displayName = 1;
  string mailBoxAddress = 2;
}

enum BodyTemplate {
  None = 0;
  DeadlineLogicalException = 1;
  MineExpired15Days = 2;
  MineDeadlineExpired15Days = 3;
  MyTeamExpired15Days = 4;
  MyTeamDeadlineExpired15Days = 5;
  InternalDeadlineExpiredDays = 6;
  CheckDelivery = 7;
}