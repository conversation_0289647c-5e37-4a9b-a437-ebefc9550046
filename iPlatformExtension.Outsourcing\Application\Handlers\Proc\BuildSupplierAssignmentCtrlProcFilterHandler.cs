﻿using System.Collections.Immutable;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using iPlatformExtension.Outsourcing.Infrastructure.Authorization.SupplierAssignment;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

// internal sealed class BuildSupplierAssignmentCtrlProcFilterHandler : IBuildSupplierAssignmentFilterHandler
// {
//     public Task HandleAsync(SupplierAssignmentAuthorizationContext context, CancellationToken cancellationToken)
//     {
//         var (_, _, ctrlProcIds, filters, query) = context;
//
//         if (filters.TryGetValue("ctrl_proc_id", out var filterEntry) && filterEntry.Values.Length > 0)
//         {
//             ctrlProcIds.IntersectWith(filterEntry.Values.ToArray());
//         }
//         
//         query.Where(info => ctrlProcIds.Contains(info.CtrlProcId));
//         return Task.CompletedTask;
//     }
// }

internal sealed class BuildSupplierAssignmentCtrlProcFilterHandler : IMatchNotificationHandler<NotAssignedProcQueryContext>
{
    private string[] _ctrlProcIds = [];
    
    public ValueTask<bool> MatchAsync(NotAssignedProcQueryContext notification, CancellationToken cancellationToken)
    {
        var filters = notification.Filters;
        if (filters.TryGetValue("ctrl_proc_id", out var filter) && "custom".Equals(filter.FilterType, StringComparison.CurrentCultureIgnoreCase))
        {
            _ctrlProcIds = filter.FilterValue.Split(';');
            return ValueTask.FromResult(true);
        }
        
        return ValueTask.FromResult(notification.CtrlProcIds.Count > 0);
    }

    public Task HandleAsync(NotAssignedProcQueryContext notification, CancellationToken cancellationToken)
    {
        var query = notification.Query;
        
        var ctrlProcIds = notification.CtrlProcIds;
        if (_ctrlProcIds.Length > 0)
        {
            ctrlProcIds.IntersectWith(_ctrlProcIds);
        }
        
        query.Where(info => ctrlProcIds.Contains(info.CtrlProcId));

        var specialCtrlProcIds = notification.Options.SpecialCtrlProcIds.ToImmutableHashSet();
        
        foreach (var specialCtrlProcId in specialCtrlProcIds.Intersect(ctrlProcIds))
        {
            if (notification.CountQueryBuilder.OtherCountQueries.TryGetValue(specialCtrlProcId, out var countQuery))
            {
                countQuery.Where(info => info.CtrlProcId == specialCtrlProcId);
            }
        }
        
        foreach (var specialCtrlProcId in specialCtrlProcIds.Except(ctrlProcIds))
        {
            notification.CountQueryBuilder.OtherCountQueries.Remove(specialCtrlProcId);
        }
        
        var includeCtrlProcIds = ctrlProcIds.ToImmutableHashSet();
        var mainCtrlProcIds = includeCtrlProcIds.Except(specialCtrlProcIds);
        if (mainCtrlProcIds.Count > 0)
        {
            notification.CountQueryBuilder.MainQuery.Where(info => mainCtrlProcIds.Contains(info.CtrlProcId));
        }
        else
        {
            notification.CountQueryBuilder.MainQuery.Where(info => false);
        }
        
        return Task.CompletedTask;
    }
}