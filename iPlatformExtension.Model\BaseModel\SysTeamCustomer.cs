﻿using System;
using System.Collections.Generic;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[Table(Name = "sys_team_customer", DisableSyncStructure = true)]
	public partial class SysTeamCustomer {

		/// <summary>
		/// 团队关联客户信息主键
		/// </summary>
		[Column(Name = "sys_team_customer_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string SysTeamCustomerId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "create_time")]
		public DateTime CreateTime { get; set; }

		/// <summary>
		/// 创建用户id
		/// </summary>
		[Column(Name = "create_user_id", StringLength = 50, IsNullable = false)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 客户id
		/// </summary>
		[Column(Name = "customer_id", StringLength = 50, IsNullable = false)]

		public string CustomerId { get; set; }

		/// <summary>
		/// 客户名称
		/// </summary>
		[Column(Name = "customer_name", StringLength = 100, IsNullable = true)]

		public string? CustomerName { get; set; }

		/// <summary>
		/// 团队id
		/// </summary>
		[Column(Name = "team_id", StringLength = 50, IsNullable = false)]
		public string TeamId { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户id
		/// </summary>
		[Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }


        ///// <summary>
        ///// 团队成员信息导航
        ///// </summary>
        //[Navigate(nameof(TeamId))]
        //public SysTeamAssociateMember SysTeamAssociateMember { get; set; }

    }

}
