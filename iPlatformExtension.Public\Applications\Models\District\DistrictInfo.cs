﻿using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Public.Applications.Models.District;

/// <summary>
/// 地区信息
/// </summary>
/// <param name="DistrictId">地区id</param>
/// <param name="DistrictCode">地区编码</param>
/// <param name="DistrictCnName">地区中文名</param>
/// <param name="DistrictEnName">地区英文名</param>
/// <param name="CrmValue">CRM值</param>
/// <param name="IsManage"></param>
/// <param name="IsEnabled">是否可用</param>
public sealed record DistrictInfo(
    string DistrictId,
    string? DistrictCode,
    string? DistrictCnName,
    string? DistrictEnName,
    string? CrmValue,
    bool IsManage,
    bool? IsEnabled) : INameInfo
{
    /// <summary>
    /// 地区编码
    /// </summary>
    public string Id => DistrictCode ?? string.Empty;
    
    /// <summary>
    /// 中文名称
    /// </summary>
    public string? CnName => DistrictCnName;
    
    /// <summary>
    /// 英文名称
    /// </summary>
    public string? EnName => DistrictEnName;
}