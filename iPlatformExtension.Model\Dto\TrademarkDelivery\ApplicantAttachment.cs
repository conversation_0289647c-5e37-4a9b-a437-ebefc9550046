﻿namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 
/// </summary>
public struct ApplicantAttachment
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="name"></param>
    /// <param name="uri"></param>
    /// <param name="attachmentType"></param>
    /// <param name="attachmentSubType"></param>
    public ApplicantAttachment(string name, string uri, ApplicantAttachmentType attachmentType, ApplicantAttachmentSubType attachmentSubType)
    {
        Name = name;
        AttachmentPic = uri;
        AttachmentType = attachmentType.Code;
        SubType = attachmentSubType.Code;
        SubTypeName = attachmentSubType.Description;
    }
    
    /// <summary>
    /// 附件名称
    /// </summary>
    public string Name { get; }
    
    /// <summary>
    /// 附件URL
    /// </summary>
    public string AttachmentPic { get; }
    
    /// <summary>
    /// 附件类型
    /// </summary>
    public int AttachmentType { get; }
    
    /// <summary>
    /// 附件子类型
    /// </summary>
    public int SubType { get; }
    
    /// <summary>
    /// 附件子类型名称
    /// </summary>
    public string SubTypeName { get; }
}
