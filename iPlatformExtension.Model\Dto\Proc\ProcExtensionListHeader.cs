﻿namespace iPlatformExtension.Model.Dto.Proc;

/// <summary>
/// 任务动态扩展信息列表表头
/// </summary>
public class ProcExtensionListHeader
{
    /// <summary>
    /// 显示的名称
    /// </summary>
    public string DisplayName { get; set; } = default!;

    /// <summary>
    /// 列所对应的的键名
    /// </summary>
    public string Key { get; set; } = default!;

    /// <summary>
    /// 是否显示
    /// </summary>
    public bool IsShow { get; set; } = true;
}