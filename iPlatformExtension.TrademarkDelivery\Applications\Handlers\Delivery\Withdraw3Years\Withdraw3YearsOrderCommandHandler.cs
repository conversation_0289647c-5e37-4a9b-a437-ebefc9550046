﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery.Withdraw3Years;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Exceptions;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Extensions;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery.Withdraw3Years;

internal sealed class Withdraw3YearsOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IBaseCountryRepository countryRepository,
    IDeliveryInfoRepository deliveryInfoRepository,
    IApplicantTypeRepository applicantTypeRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    UnitOfWorkManager unitOfWorkManager,
    ILoggerFactory loggerFactory,
    PhoenixClientFactory phoenixClientFactory)
    : PhoenixDeliveryHandleBase<Withdraw3YearsOrderCommand, TrademarkWithdraw3Order,
            PhoenixResponseParameters<OrderData>>(stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository,
            unitOfWorkManager, loggerFactory, phoenixClientFactory),
        IDeliveryHandler<Withdraw3YearsOrderCommand, TrademarkWithdraw3Order, PhoenixResponseParameters<OrderData>>
{
    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.StartupDelivery;
    
    public override async Task<TrademarkWithdraw3Order> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        ArgumentNullException.ThrowIfNull(_deliveryInfo.OtherInfo);
        
        var grandNumbers = _deliveryInfo.OtherInfo.TrademarkNiceClasses?.Split(';') ?? Array.Empty<string>();

        var niceCategories = grandNumbers.GroupJoin(_deliveryInfo.NiceCategories ?? Array.Empty<DeliveryNiceCategory>(),
            Convert.ToInt32, category => Convert.ToInt32(category.GrandNumber), (grandNumber, categories) =>
                new BrandInfo
                {
                    BrandRegisterNo = _deliveryInfo.AppNo,
                    FirstCgNo = grandNumber,
                    DeleteType = categories.Any() ? 2 : 1,
                    CategoryFlowStateName = "已注册",
                    GoodsItems = categories.Any()
                        ? categories.Select(category => new GoodsItem
                        {
                            Code = "0",
                            Name = category.CategoryName,
                            Status = "0"
                        })
                        : default
                }).ToArray();
        
        var currentApplicant = _deliveryInfo.Applicants!.Single(applicant => applicant.IsRepresent ?? false);

        var countryId = currentApplicant.CountryId ??
                        throw new PropertyMissingException(currentApplicant.ApplicantId, currentApplicant.CountryId, "申请人");
        var applicantBookType = ApplicantBookType.GetApplicantBookType(countryId);
        
        string country;
        if (applicantBookType == ApplicantBookType.Overseas)
        {
            var countryInfo = await countryRepository.GetCacheValueAsync(countryId);
            if (countryInfo is null)
            {
                throw new NotFoundException(countryId, "国家");
            }

            if (string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCode) || string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCountryName))
            {
                throw new ApplicationException("商标局没有对应的国家编码和名称");
            }

            country = $"{countryInfo.TrademarkDeliveryCode}  {countryInfo.TrademarkDeliveryCountryName}";
        }
        else
        {
            country = applicantBookType.Description;
        }

        var applicantType = await applicantTypeRepository.GetCacheValueAsync(currentApplicant.TypeId.GetOrDefaultEmpty(), cancellationToken: cancellationToken);
        if (applicantType is null)
        {
            throw new ApplicantTypeMissingException(currentApplicant, "当前申请人类型");
        }
        
        var attachments = _deliveryInfo.Files?
                              .Where(file => int.TryParse(file.FileCode, out _) && file.BaseFileType is "申请人文件" or "递交官方")
                              .Select(file => 
                                  new ApplicantAttachment(
                                      file.FileName, 
                                      file.Url ?? string.Empty, 
                                      ApplicantAttachmentType.OfficialAttachment, 
                                      new ApplicantAttachmentSubType(int.Parse(file.FileCode!), file.FileDesc ?? string.Empty))) 
                          ?? Array.Empty<ApplicantAttachment>();

        var order = new TrademarkWithdraw3Order
        {
            BookType = applicantBookType.Code.ToString(),
            Country = country,
            OwnerType = applicantType.GetOwnerType(),
            ApplicantName = currentApplicant.ApplicantNameCn,
            IdCard = currentApplicant.CardNo,
            ApplicantAddress = currentApplicant.AddressCn,
            Code = currentApplicant.Postcode,
            PrincipalName = _deliveryInfo.ContactPerson,
            PrincipalTel = _deliveryInfo.ContactTel,
            AgentOrganConName = _deliveryInfo.AgentUser,
            SubjectType = currentApplicant.GetSubjectType(),
            CertificatesType = currentApplicant.CardType.GetApplicantCertificationType(),
            BrandInfos = niceCategories,
            ApplicantAttachments = attachments,
            ContactEmail = _deliveryInfo.ContactMailBox,
            ContactName = _deliveryInfo.ContactPerson,
            ContactTel = _deliveryInfo.ContactTel,
            AgentOrganTel = _deliveryInfo.ContactTel ?? throw new PropertyMissingException(_deliveryInfo.ProcId, _deliveryInfo.ContactTel, "递交任务", "联系电话"),
            OrderToken = _deliveryInfo.ProcId
        };

        return order;
    }

    public override Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(TrademarkWithdraw3Order request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        _phoenixClient ??= _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
        return _phoenixClient.CreateOrderAsync(PhoenixUri.WithdrawThreeYears, request);
    }

    public override Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        var orderNo = response.Data?.OrderNo!;
        const int newStatus = (int) DeliveryStatus.Ordered;
        
        return Handler.UpdateDeliveryInfoInternalAsync(deliveryInfo =>
        {
            deliveryInfo.Status = newStatus;
            deliveryInfo.OrderNo = orderNo;
            deliveryInfo.OperationResult = true;
            return ValueTask.CompletedTask;
        }, cancellationToken);
    }

    public override async Task InitializeAsync(Withdraw3YearsOrderCommand command, CancellationToken cancellationToken)
    {
        var procId = command.ProcId;
        _deliveryInfo = await DeliveryInfoRepository.Where(info => info.ProcId == procId)
            .Include(info => info.OtherInfo)
            .IncludeMany(info => info.Applicants)
            .IncludeMany(info => info.Files)
            .IncludeMany(info => info.NiceCategories)
            .ToOneAsync(cancellationToken);

        if (_deliveryInfo is null)
        {
            throw new NotFoundException(procId, "递交任务");
        }
        
        _deliveryInfo.ValidateVersion(command.Version);

        _phoenixClient = _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
    }

    public async Task<bool> HandleExceptionAsync(Exception ex, CancellationToken cancellationToken = default)
    {
        await Handler.HandleExceptionInternalAsync(ex, true, false, cancellationToken);
        return false;
    }
}