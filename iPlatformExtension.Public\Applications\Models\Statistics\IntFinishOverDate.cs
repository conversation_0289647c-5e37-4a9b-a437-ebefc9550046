﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Public.Applications.Models.Statistics
{
    /// <summary>
    /// 定稿期限内
    /// </summary>
    public class IntFinishOverDate : InOverDate
    {
        [ExcelColumn(Name = "定稿期限(内)"), ExcelColumnIndex("N")]

        public DateTime? IntFinishDate { get; init; }

        [ExcelColumn(Name = "超期天数"), ExcelColumnIndex("M")]

        public int? OverDate => (DateTime.Now - IntFinishDate!.Value).Days;
    }
}
