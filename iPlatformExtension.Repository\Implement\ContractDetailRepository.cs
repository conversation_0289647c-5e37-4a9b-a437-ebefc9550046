﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class ContractDetailRepository(IFreeSql<PlatformFreeSql> freeSql, UnitOfWorkManage<PlatformFreeSql> unitOfWorkManager)
    : DefaultRepository<ContractDetail, int>(freeSql, unitOfWorkManager), IContractDetailRepository;