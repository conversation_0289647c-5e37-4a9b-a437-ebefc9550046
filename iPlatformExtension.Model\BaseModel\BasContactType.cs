using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_contact_type", DisableSyncStructure = true)]
	public partial class BasContactType {

		/// <summary>
		/// 联系人类型ID
		/// </summary>
		[ Column(Name = "contact_type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ContactTypeId { get; set; }

		/// <summary>
		/// 联系人类型编号
		/// </summary>
		[ Column(Name = "contact_type_code", StringLength = 50)]
		public string ContactTypeCode { get; set; }

		/// <summary>
		/// 联系人类型英文
		/// </summary>
		[ Column(Name = "contact_type_en_us", StringLength = 200)]
		public string ContactTypeEnUs { get; set; }

		/// <summary>
		/// 联系人类型日文
		/// </summary>
		[ Column(Name = "contact_type_ja_jp", StringLength = 200)]
		public string ContactTypeJaJp { get; set; }

		/// <summary>
		/// 联系人类型中文
		/// </summary>
		[ Column(Name = "contact_type_zh_cn", StringLength = 50)]
		public string ContactTypeZhCn { get; set; }

		/// <summary>
		/// 创建人时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人ID
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "crm_type_value")]
		public int? CrmTypeValue { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人ID
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
