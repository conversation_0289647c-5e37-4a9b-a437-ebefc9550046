﻿using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;

/// <summary>
/// 递交数据扩展
/// </summary>
public static class DeliveryInfoExtension
{
    /// <summary>
    /// 获取递交状态描述
    /// </summary>
    /// <param name="status">递交状态枚举</param>
    /// <param name="operationResult">操作结果</param>
    /// <param name="isAuto">是否自动</param>
    /// <returns>递交状态描述</returns>
    /// <exception cref="ArgumentOutOfRangeException">递交状态值不在枚举范围内</exception>
    public static string GetStatusDescription(this DeliveryStatus status, bool operationResult, bool? isAuto)
    {
        if (!operationResult)
        {
            return "递交失败";
        }
        
        var statusDescription = status switch
        {
            DeliveryStatus.Ready => "未发起",
            DeliveryStatus.Ordered => "递交中",
            DeliveryStatus.Delivering => "递交中",
            DeliveryStatus.Complete => "递交成功",
            DeliveryStatus.Confirmed => "递交成功",
            DeliveryStatus.Stopped => "未发起",
            DeliveryStatus.Error => "递交失败",
            _ => throw new ArgumentOutOfRangeException()
        };

        if (isAuto ?? false)
        {
            return statusDescription;
        }

        return "未发起";
    }

    /// <summary>
    /// 获取递交状态描述
    /// </summary>
    /// <param name="statusValue">递交状态值</param>
    /// <param name="operationResult">操作结果</param>
    /// <param name="isAuto">是否自动</param>
    /// <returns>递交状态描述</returns>
    public static string GetStatusDescription(int statusValue, bool operationResult, bool? isAuto)
    {
        return GetStatusDescription((DeliveryStatus) statusValue, operationResult, isAuto);
    }

    /// <summary>
    /// 获取递交状态描述
    /// </summary>
    /// <param name="dto">商标递交验证DTO</param>
    /// <returns>递交状态描述</returns>
    public static string GetStatusDescription(this DeliveryValidationDto dto)
    {
        return GetStatusDescription(dto.DeliveryStatus, dto.OperationResult, dto.IsAuto);
    }

    /// <summary>
    /// 判断当前状态是否递交中
    /// </summary>
    /// <param name="dto">商标递交验证DTO</param>
    /// <returns>是否自动递交中</returns>
    public static bool IsDelivering(this DeliveryValidationDto dto)
    {
        var description = GetStatusDescription(dto);
        return description == "递交中";
    }

    /// <summary>
    /// 判断当前状态是否递交成功
    /// </summary>
    /// <param name="dto">商标递交验证DTO</param>
    /// <returns>是否递交成功</returns>
    public static bool IsDeliverySuccess(this DeliveryValidationDto dto)
    {
        var description = GetStatusDescription(dto);
        return description == "递交成功";
    }

    /// <summary>
    /// 批量提交验证失败
    /// </summary>
    /// <param name="result">验证结果</param>
    /// <param name="procNo">任务编号</param>
    /// <returns>验证失败结果</returns>
    public static BatchDeliveryValidateResult CannotSubmit(this BatchDeliveryValidateResult result, string procNo)
    {
        return result.Fail(BatchDeliveryValidateResult.CannotSubmitMessage, procNo);
    }

    /// <summary>
    /// 批量退回验证失败
    /// </summary>
    /// <param name="result">验证结果</param>
    /// <param name="procNo">任务编号</param>
    /// <returns>验证失败结果</returns>
    public static BatchDeliveryValidateResult CannotReject(this BatchDeliveryValidateResult result, string procNo)
    {
        return result.Fail(BatchDeliveryValidateResult.CannotRejectMessage, procNo);
    }

    /// <summary>
    /// 批量移交验证失败
    /// </summary>
    /// <param name="result">验证结果</param>
    /// <param name="procNo">任务编号</param>
    /// <returns>验证失败结果</returns>
    public static BatchDeliveryValidateResult CannotHandOver(this BatchDeliveryValidateResult result, string procNo)
    {
        return result.Fail(BatchDeliveryValidateResult.CannotHandOverMessage, procNo);
    }

    /// <summary>
    /// 批量启动递交流程验证失败
    /// </summary>
    /// <param name="result">验证结果</param>
    /// <param name="procNo">任务编号</param>
    /// <returns>验证失败结果</returns>
    public static BatchDeliveryValidateResult CannotStartup(this BatchDeliveryValidateResult result, string procNo)
    {
        return result.Fail(BatchDeliveryValidateResult.CannotStartupMessage, procNo);
    }

    /// <summary>
    /// 批量启动自动递交验证失败
    /// </summary>
    /// <param name="result">验证结果</param>
    /// <param name="procNo">任务编号</param>
    /// <returns>验证失败结果</returns>
    public static BatchDeliveryValidateResult CannotLaunch(this BatchDeliveryValidateResult result, string procNo)
    {
        return result.Fail(BatchDeliveryValidateResult.CannotLaunchDeliveryMessage, procNo);
    }
    
    /// <summary>
    /// 批量验证失败多节点
    /// </summary>
    /// <param name="result">验证结果</param>
    /// <returns>验证失败结果</returns>
    public static BatchDeliveryValidateResult MultipartNode(this BatchDeliveryValidateResult result)
    {
        return result.Fail(BatchDeliveryValidateResult.MultipartNodeMessage);
    }
    
    /// <summary>
    /// 批量验证失败多任务
    /// </summary>
    /// <param name="result">验证结果</param>
    /// <returns>验证失败结果</returns>
    public static BatchDeliveryValidateResult MultipartCtrlProc(this BatchDeliveryValidateResult result)
    {
        return result.Fail(BatchDeliveryValidateResult.MultipartCtrlProcMessage);
    }

    /// <summary>
    /// 批量验证失败非流程承办人
    /// </summary>
    /// <param name="result">验证结果</param>
    /// <param name="procNo">任务编号</param>
    /// <returns>验证失败结果</returns>
    public static BatchDeliveryValidateResult InvalidUndertaker(this BatchDeliveryValidateResult result, string procNo)
    {
        return result.Fail(BatchDeliveryValidateResult.InvalidUndertakerMessage, procNo);
    }
}