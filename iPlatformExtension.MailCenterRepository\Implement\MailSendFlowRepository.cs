﻿﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;

namespace iPlatformExtension.MailCenterRepository.Implement;

internal class MailSendFlowRepository(
    IFreeSql<MailCenterFreeSql> fsql,
    UnitOfWorkManage<MailCenterFreeSql> manager)
    : DefaultRepository<MailSendFlow, string>(fsql, manager), IMailSendFlowRepository;
