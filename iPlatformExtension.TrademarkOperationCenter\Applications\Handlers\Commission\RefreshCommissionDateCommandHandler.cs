﻿using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Commission;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Commission;

internal sealed class RefreshCommissionDateCommandHandler(
    ICaseProcInfoRepository caseProcInfoRepository,
    ILogger<RefreshCommissionDateCommandHandler> logger,
    EntityTypeInfoProvider entityTypeInfoProvider) 
    : IRequestHandler<RefreshCommissionDateCommand>
{
    public async Task Handle(RefreshCommissionDateCommand request, CancellationToken cancellationToken)
    {
        var repository = caseProcInfoRepository;
        var procId = request.ProcId;
        
        var procInfo = await repository.GetAsync(procId, cancellationToken);
        if (procInfo == null) return;

        var freeSql = repository.Orm;
        var trademarkBonusConfigs = await freeSql.Select<TrademarkBonusConfig>().WithLock()
            .Where(config => config.CtrlProcId == procInfo.CtrlProcId)
            .Where(config => config.IsEnabled == true)
            .Where(config => config.CtrlProcStatusId == procInfo.ProcStatusId)
            .Where(config => config.CaseDirection == procInfo.CaseInfo.CaseDirection)
            .ToListAsync(cancellationToken);

        List<TrademarkBonusConfig> configs;
        if (procInfo.CtrlProcMark is null)
        {
            configs = trademarkBonusConfigs.Where(config => string.IsNullOrEmpty(config.CtrlProcMark)).ToList();
        }
        else
        {
            configs = trademarkBonusConfigs
                .Where(config => procInfo.CtrlProcMark.Equals(config.CtrlProcMark, StringComparison.OrdinalIgnoreCase))
                .ToList();
            if (configs.Count == 0)
            {
                configs = trademarkBonusConfigs.Where(config => string.IsNullOrEmpty(config.CtrlProcMark)).ToList();
            }
        }

        if (configs.Count != 1)
        {
            logger.LogError("提成规则查询数量为{Count}, 不等于1。不做更新", configs.Count);
            return;
        }
        
        var config = configs[0];
        var dateType = config.DateType;
        
        var typeInfo = entityTypeInfoProvider.Get(typeof(CaseProcInfo));
        var properties = typeInfo.EntityPropertyInfos;

        var property = properties.Values.FirstOrDefault(info => info.ColumnName == dateType);
            
        if (await freeSql.Select<DomesticTrademarkCommission>().AnyAsync(bonus => bonus.ProcId == procId, cancellationToken))
        {
            return;
        }

        if (property?.Get?.Invoke(procInfo) is DateTime)
        {
            procInfo.CommissionEffectiveDate = DateTime.Now;
            await repository.UpdateAsync(procInfo, cancellationToken);
        }
        
    }
}