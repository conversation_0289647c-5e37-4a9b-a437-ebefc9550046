using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.MailCenter.Applications.Commands.Send;

/// <summary>
/// 重新发送邮件命令
/// </summary>
/// <param name="MailIds">邮件ID列表</param>
public record ResendMailCommand(
    [Required(ErrorMessage = "邮件ID列表不能为空")] List<string> MailIds) : IRequest, IUnitOfWorkCommandMysql;
