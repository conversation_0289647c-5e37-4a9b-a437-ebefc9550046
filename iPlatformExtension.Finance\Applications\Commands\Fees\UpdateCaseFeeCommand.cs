﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Commands.Fees;

internal sealed record UpdateCaseFeeCommand(CaseFeeJsonPatchDocument PatchDocument, CaseFeeList? CaseFeeList = null) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;