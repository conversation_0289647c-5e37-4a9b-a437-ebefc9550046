using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.Send;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;
using System.IO;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send;

/// <summary>
/// 写邮件命令处理程序
/// </summary>
internal sealed class WriteMailCommandHandler(
        IMailSendRepository mailSendRepository,
        IMailUserRepository mailUserRepository,
        IMailSendFlowRepository mailSendFlowRepository,
        IMailAttachmentsRepository mailAttachmentsRepository,
        HuaweiObsClient huaweiObsClient,
        IHttpContextAccessor httpContextAccessor) : IRequestHandler<WriteMailCommand, string>
{
    public async Task<string> Handle(WriteMailCommand request, CancellationToken cancellationToken)
    {
        // 获取当前用户ID
        var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
        var now = DateTime.Now;

        // 确定邮件状态
        int mailStatus = SysEnum.SendStatusType.Draft.GetHashCode(); // 默认为草稿状态


        string mailId;

        if (string.IsNullOrEmpty(request.MailId))
        {
            // 新增邮件
            mailId = Guid.NewGuid().ToString();

            // 创建邮件主体
            var mailSend = new MailSend
            {
                MailId = mailId,
                MailSubject = request.MailSubject,
                MailHtmlBody = request.MailHtmlBody,
                MailRelayBody = request.MailRelayBody ?? "",  // 添加转发邮件正文
                HostId = request.HostId,
                IsImportant = request.IsImportant,
                IsRead = request.IsRead,
                MailDate = now,
                Status = mailStatus,
                Attachments = request.Attachments?.Count ?? 0,
                SignatureBody = request.SignatureBody ?? "",
                MailFrom = request.MailFrom.FirstOrDefault()?.MailAddress ?? "",  // 添加发件人
                SendTime = request.SendTime,
                IsRequiredProcessTime = request.IsRequiredProcessTime
            };

            // 保存邮件主体
            await mailSendRepository.InsertAsync(mailSend, cancellationToken);

            var mailSendFlow = new MailSendFlow
            {
                Id = Guid.NewGuid().ToString(),
                CreateBy = userId,
                CreateTime = now,
                MailId = mailId,
                UpdateBy = userId,
                UpdateTime = now,
                UndertakeUserId = userId
            };

            await mailSendFlowRepository.InsertAsync(mailSendFlow, cancellationToken);

            // 同步 MailFrom、MailTo、MailCc 和 MailBcc 到 mail_user 表
            await SyncMailUsersAsync(mailId, request.MailTo, request.MailCc, request.MailBcc, request.MailFrom, cancellationToken);

        }
        else
        {
            // 编辑现有邮件
            mailId = request.MailId;

            // 获取现有邮件
            var existingMail = await mailSendRepository.Where(m => m.MailId == mailId)
                .FirstAsync(cancellationToken);

            ArgumentNullException.ThrowIfNull(existingMail, nameof(existingMail));

            // 查找并处理不再使用的附件
            if (request.MailId != null)
            {
                // 获取当前邮件的所有附件
                var existingAttachments = await mailAttachmentsRepository
                    .Where(a => a.MailId == mailId)
                    .ToListAsync(cancellationToken);

                // 如果请求中有附件，找出不在请求中的附件
                if (request.Attachments != null && request.Attachments.Count > 0)
                {
                    var requestAttachmentIds = request.Attachments.Select(a => a.AttachmentId).ToList();
                    var attachmentsToRemove = existingAttachments
                        .Where(a => !requestAttachmentIds.Contains(a.AttachmentId))
                        .ToList();

                    // 删除不再使用的附件
                    foreach (var attachment in attachmentsToRemove)
                    {
                        try
                        {
                            // 构建文件路径
                            var filePath = Path.Combine(attachment.ServerPath, attachment.FileName).Replace('\\', '/');

                            // 从华为OBS删除文件
                            await huaweiObsClient.DeleteObjectAsync(filePath, attachment.Bucket);

                            // 从数据库中删除附件记录
                            await mailAttachmentsRepository.DeleteAsync(attachment, cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            // 记录错误但继续处理其他附件
                            Console.WriteLine($"删除附件 {attachment.AttachmentId} 时出错: {ex.Message}");
                        }
                    }
                }
                // 如果请求中没有附件，删除所有附件
                else if (existingAttachments.Count > 0)
                {
                    foreach (var attachment in existingAttachments)
                    {
                        try
                        {
                            // 构建文件路径
                            var filePath = Path.Combine(attachment.ServerPath, attachment.FileName).Replace('\\', '/');

                            // 从华为OBS删除文件
                            await huaweiObsClient.DeleteObjectAsync(filePath, attachment.Bucket);

                            // 从数据库中删除附件记录
                            await mailAttachmentsRepository.DeleteAsync(attachment, cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            // 记录错误但继续处理其他附件
                            Console.WriteLine($"删除附件 {attachment.AttachmentId} 时出错: {ex.Message}");
                        }
                    }
                }
            }

            // 更新邮件主体
            existingMail.MailSubject = request.MailSubject;
            existingMail.MailHtmlBody = request.MailHtmlBody;
            existingMail.MailRelayBody = request.MailRelayBody ?? "";  // 更新转发邮件正文
            existingMail.HostId = request.HostId;
            existingMail.IsImportant = request.IsImportant;
            existingMail.IsRead = request.IsRead;
            existingMail.Status = mailStatus;
            existingMail.Attachments = request.Attachments?.Count ?? 0;
            existingMail.SignatureBody = request.SignatureBody ?? "";
            existingMail.MailFrom = request.MailFrom.FirstOrDefault()?.MailAddress ?? "";  // 更新发件人
            existingMail.SendTime = request.SendTime;

            // 保存更新后的邮件主体
            await mailSendRepository.UpdateAsync(existingMail, cancellationToken);
            if (!await mailSendFlowRepository.Where(o => o.MailId == mailId).AnyAsync(cancellationToken))
            {
                var mailSendFlow = new MailSendFlow
                {
                    Id = Guid.NewGuid().ToString(),
                    CreateBy = userId,
                    CreateTime = now,
                    MailId = mailId,
                };

                await mailSendFlowRepository.InsertAsync(mailSendFlow, cancellationToken);
            }

            // 同步 MailFrom、MailTo、MailCc 和 MailBcc 到 mail_user 表
            await SyncMailUsersAsync(mailId, request.MailTo, request.MailCc, request.MailBcc, request.MailFrom, cancellationToken);

        }
        // 保存附件
        if (request.Attachments != null && request.Attachments.Count > 0)
        {
            var mailAttachmentIds = request.Attachments.Select(a => a.AttachmentId);
            var mailAttachments = await mailAttachmentsRepository.Where(it => mailAttachmentIds.Contains(it.AttachmentId)).ToListAsync(cancellationToken);
            mailAttachments.ForEach(it=>it.MailId = mailId);
            await mailAttachmentsRepository.UpdateAsync(mailAttachments, cancellationToken);
        }
        // 返回邮件ID
        return mailId;
    }


    /// <summary>
    /// 同步邮件发件人、收件人、抄送人和密送人到 mail_user 表
    /// </summary>
    /// <param name="mailId">邮件ID</param>
    /// <param name="mailTo">收件人列表</param>
    /// <param name="mailCc">抄送人列表</param>
    /// <param name="mailBcc">密送人列表</param>
    /// <param name="mailFrom">发件人列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task SyncMailUsersAsync(string mailId, List<MailAddressList> mailTo, List<MailAddressList>? mailCc, List<MailAddressList>? mailBcc, List<MailAddressList> mailFrom, CancellationToken cancellationToken)
    {
        // 删除现有的发件人、收件人、抄送人和密送人记录
        await mailUserRepository.DeleteAsync(u => u.MailId == mailId && (u.AddressType == "from" || u.AddressType == "to" || u.AddressType == "cc" || u.AddressType == "bcc"), cancellationToken);

        var mailUsers = new List<MailUser>();

        // 处理发件人
        if (mailFrom != null && mailFrom.Count > 0)
        {
            mailUsers.AddRange(ParseEmailAddresses(mailId, mailFrom, "from"));
        }

        // 处理收件人
        if (mailTo != null && mailTo.Count > 0)
        {
            mailUsers.AddRange(ParseEmailAddresses(mailId, mailTo, "to"));
        }

        // 处理抄送人
        if (mailCc != null && mailCc.Count > 0)
        {
            mailUsers.AddRange(ParseEmailAddresses(mailId, mailCc, "cc"));
        }

        // 处理密送人
        if (mailBcc != null && mailBcc.Count > 0)
        {
            mailUsers.AddRange(ParseEmailAddresses(mailId, mailBcc, "bcc"));
        }

        // 批量插入邮件用户记录
        if (mailUsers.Count > 0)
        {
            await mailUserRepository.InsertAsync(mailUsers, cancellationToken);
        }
    }

    /// <summary>
    /// 解析邮箱地址列表
    /// </summary>
    /// <param name="mailId">邮件ID</param>
    /// <param name="addressList">邮箱地址列表</param>
    /// <param name="addressType">地址类型：to-收件人，cc-抄送，bcc-密送</param>
    /// <returns>解析后的邮件用户列表</returns>
    private static List<MailUser> ParseEmailAddresses(string mailId, List<MailAddressList> addressList, string addressType)
    {
        var result = new List<MailUser>();

        for (int i = 0; i < addressList.Count; i++)
        {
            var address = addressList[i];
            if (address == null || string.IsNullOrWhiteSpace(address.MailAddress))
                continue;

            string displayName = address.DisplayName ?? address.MailAddress;
            string mailAddress = address.MailAddress.Trim();

            // 验证邮箱地址格式
            if (!string.IsNullOrEmpty(mailAddress) && mailAddress.Contains('@'))
            {
                result.Add(new MailUser
                {
                    Id = Guid.NewGuid().ToString(),
                    MailId = mailId,
                    AddressType = addressType,
                    DisplayName = displayName,
                    MailAddress = mailAddress,
                    Seq = i + 1
                });
            }
        }

        return result;
    }

}
