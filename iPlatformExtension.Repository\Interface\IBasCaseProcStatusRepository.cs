﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface
{
    public interface IBasCaseProcStatusRepository :
        IBaseRepository<BasCaseProcStatus, string>,
        IStringKeyCacheableRepository<BasCaseProcStatus>,
        IScopeDependency,
        IRedisCacheableRepository<string, BasCaseProcStatus>
    {
        Task<BasCaseProcStatus?> ICacheableRepository<string, BasCaseProcStatus>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
        {
            return Select.WithLock().Where(status => status.Value == key).FirstAsync(cancellationToken)!;
        }

        async Task<IEnumerable<BasCaseProcStatus>> ICacheableRepository<string, BasCaseProcStatus>.
            GetValuesFromDbAsync(CancellationToken cancellationToken)
        {
            return await Select.WithLock().ToListAsync(cancellationToken);
        }

        string ICacheableRepository<string, BasCaseProcStatus>.GenerateKey(BasCaseProcStatus value)
        {
            return value.Value;
        }

        string IStringKeyCacheableRepository<BasCaseProcStatus>.GetCacheTextValue(BasCaseProcStatus value)
        {
            return value.TextZhCn;
        }
    }
}