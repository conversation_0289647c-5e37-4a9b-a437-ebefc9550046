﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class CaseProcInfoRepository(IFreeSql<PlatformFreeSql> freeSql, UnitOfWorkManage<PlatformFreeSql> uowManger)
    : DefaultRepository<CaseProcInfo, string>(freeSql, uowManger), ICaseProcInfoRepository;