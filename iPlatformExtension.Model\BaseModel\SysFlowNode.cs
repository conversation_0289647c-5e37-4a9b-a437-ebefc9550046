using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_flow_node", DisableSyncStructure = true)]
	public partial class SysFlowNode {

		[ Column(Name = "node_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string NodeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "flow_type", StringLength = 50)]
		public string FlowType { get; set; }

		[ Column(Name = "is_default")]
		public bool? IsDefault { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_must")]
		public bool? IsMust { get; set; }

		[ Column(Name = "is_skip")]
		public bool? IsSkip { get; set; }

		[ Column(Name = "name_en_us", StringLength = 50)]
		public string NameEnUs { get; set; }

		[ Column(Name = "name_ja_jp", StringLength = 50)]
		public string NameJaJp { get; set; }

		[ Column(Name = "name_zh_cn", StringLength = 50)]
		public string NameZhCn { get; set; }

		[ Column(Name = "node_code", StringLength = 50)]
		public string NodeCode { get; set; }

		[ Column(Name = "node_name", StringLength = 50)]
		public string NodeName { get; set; }

		[ Column(Name = "node_type")]
		public int? NodeType { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "user_type", StringLength = 50)]
		public string UserType { get; set; }

	}

}
