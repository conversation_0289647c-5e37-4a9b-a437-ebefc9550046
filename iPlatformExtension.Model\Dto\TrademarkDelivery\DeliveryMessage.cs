namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 递交信息
/// </summary>
public class DeliveryMessage
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string ProcId { get; set; } = default!;

    /// <summary>
    /// 版本号
    /// </summary>
    public int Version { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    public string? OrderNo { get; set; }

    /// <summary>
    /// 递交key
    /// </summary>
    public string? DeliveryKey { get; set; }

    /// <summary>
    /// 结构函数
    /// </summary>
    /// <param name="procId">任务ID</param>
    /// <param name="version">版本号</param>
    public void Deconstruct(out string procId, out int version)
    {
        procId = ProcId;
        version = Version;
    }
    
    /// <summary>
    /// 结构函数
    /// </summary>
    /// <param name="procId">任务ID</param>
    /// <param name="version">版本号</param>
    /// <param name="orderNo">订单号</param>
    /// <param name="deliveryKey">递交key</param>
    public void Deconstruct(out string procId, out int version, out string? orderNo, out string? deliveryKey)
    {
        procId = ProcId;
        version = Version;
        orderNo = OrderNo;
        deliveryKey = DeliveryKey;
    }

    /// <inheritdoc />
    public override string ToString()
    {
        return $$"""
                {"procId":"{{ProcId}}", "version":{{Version}}}
                """;
    }
}