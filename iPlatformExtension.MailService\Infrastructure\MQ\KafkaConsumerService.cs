﻿using Confluent.Kafka;
using iPlatformExtension.Common.MQ.MailMQ;
using iPlatformExtension.MailService.Applications.Commands;
using iPlatformExtension.Model.Dto.MailCenter;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.MailService.Infrastructure.MQ
{
    public class KafkaConsumerService<TKey, TValue> : IDisposable
    {
        private readonly KafkaConsumerFactory<TKey, TValue> _consumerFactory;
        private readonly ILogger<KafkaConsumerService<TKey, TValue>> _logger;
        private readonly IOptionsMonitor<KafkaConsumerSettings> _config;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private IConsumer<TKey, TValue> _consumer;
        private Task _consumeTask;
        private KafkaConsumerSettings _setting;

        public KafkaConsumerService(
            KafkaConsumerFactory<TKey, TValue> consumerFactory, 
            ILogger<KafkaConsumerService<TKey, TValue>> logger,
            IOptionsMonitor<KafkaConsumerSettings> config, 
            IServiceScopeFactory serviceScopeFactory)
        {
            _consumerFactory = consumerFactory;
            _logger = logger;
            _config = config;
            _serviceScopeFactory = serviceScopeFactory;
        }

        public async Task StartConsumingAsync(string configName,CancellationToken stoppingToken)
        {
            var topic = "";
            try
            {
                _setting = _config.Get(configName);
                topic = _setting.Topic;
                _consumer = _consumerFactory.CreateConsumer(configName);
                _consumer.Subscribe(topic);

                _logger.LogInformation($"Kafka consumer started for topic: {topic}");

                _consumeTask = Task.Run(() => ConsumeMessagesAsync(stoppingToken));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to start Kafka consumer for topic: {topic}");
                throw;
            }
        }

        private async Task ConsumeMessagesAsync(CancellationToken stoppingToken)
        {
            try
            {
                while (!stoppingToken.IsCancellationRequested)
                {
                    try
                    {
                        var consumeResult = _consumer.Consume(stoppingToken);

                        if (consumeResult.IsPartitionEOF)
                        {
                            _logger.LogDebug($"Reached end of partition for topic {_setting.Topic}");
                            continue;
                        }

                        if (consumeResult.Message.Value is MailCenterMessageContent mailMessage)
                        {
                            await ProcessMessageAsync(mailMessage);
                        }
                    }
                    catch (ConsumeException ex)
                    {
                        _logger.LogError(ex, $"Consume error in topic {_setting.Topic}: {ex.Error.Reason}");
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常的取消操作
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Unexpected error in Kafka consumer for topic: {_setting.Topic}");
            }
            finally
            {
                _consumer?.Close();
            }
        }

        private async Task ProcessMessageAsync(MailCenterMessageContent mailMessage)
        {
            try
            {
                using (var scope = _serviceScopeFactory.CreateScope())
                {
                    var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
                    //todo:补充生产者逻辑
                    //await mediator.Send(new SendMailBySmtpCommand(mailMessage));

                    _logger.LogInformation($"Successfully processed message: {mailMessage.MailNo}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing message {mailMessage.MailNo}");
            }
        }

        public async Task StopConsumingAsync()
        {
            _logger.LogInformation($"Stopping Kafka consumer for topic: {_setting.Topic}");
           // _cancellationTokenSource.Cancel();

            if (_consumeTask != null)
            {
                await _consumeTask;
            }
        }

        public void Dispose()
        {
            //_cancellationTokenSource?.Cancel();
            _consumer?.Dispose();
           // _cancellationTokenSource?.Dispose();
        }
    }
}
