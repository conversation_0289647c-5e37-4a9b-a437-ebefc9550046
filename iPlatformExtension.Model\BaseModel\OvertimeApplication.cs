using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "overtime_application", DisableSyncStructure = true)]
	public partial class OvertimeApplication {

		[ Column(Name = "cur_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CurId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "dept_id", StringLength = 50, IsNullable = false)]
		public string DeptId { get; set; }

		[ Column(Name = "end_time")]
		public DateTime? EndTime { get; set; }

		[ Column(Name = "overtime_reason", StringLength = 500)]
		public string OvertimeReason { get; set; }

		[ Column(Name = "overtime_time")]
		public int? OvertimeTime { get; set; }

		[ Column(Name = "start_time")]
		public DateTime? StartTime { get; set; }

		[ Column(Name = "status")]
		public int? Status { get; set; } = 0;

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

	}

}
