﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 提交到权大师的递交队列
/// </summary>
public sealed class OrderSubmitParameters : PhoenixOrderOperationParameters
{
    /// <summary>
    /// 代理文号
    /// 我方文号
    /// </summary>
    [JsonPropertyName("proxyNo")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ProxyNumber { get; set; }

    /// <summary>
    /// 递交key
    /// </summary>
    public string SubmitKey { get; set; } = string.Empty;
}