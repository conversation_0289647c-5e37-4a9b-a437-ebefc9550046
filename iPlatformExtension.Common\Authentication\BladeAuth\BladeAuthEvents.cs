﻿using System.Security.Claims;
using iPlatformExtension.Common.Cache;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Authentication.BladeAuth;

public sealed class BladeAuthEvents
{
    public Func<BladeAuthenticatedContext, ValueTask> OnAuthenticationFailed { get; set; } = context =>
    {
        if (context.Exception is not null)
        {
            context.Fail(context.Exception);
        }
        else
        {
            context.Fail(context.FailureMessage ?? "token验证失败");
        }

        context.HttpContext.Features.Set<IAuthenticateResultFeature>(new BladeAuthenticationResultFeature(context.Result));

        return new ValueTask();
    };

    /// <summary>
    /// Invoked after the security token has passed validation and a ClaimsIdentity has been generated.
    /// </summary>
    public Func<BladeAuthenticatedContext, ValueTask<AuthenticateResult?>> OnTokenValidated { get; set; } = async context =>
    {
        if (!(context.Principal?.HasClaim(claim => claim.Type == "user_name") ?? false))
            return context.Fail();

        var cache = context.HttpContext.RequestServices.GetService<IRedisCache<RedisCacheOptionsBase>>();
        if (cache is not null && context.Options.SaveToken)
        {
            await cache.SetCacheValueAsync(BladeAuthOptions.SchemeName,
                context.Properties.GetTokenValue(BladeAuthOptions.SchemeName), context.Principal, false);
        }

        context.Success();
        return context.Result;

    };

    public Func<BladeAuthenticatedContext, ValueTask> OnAuthenticationSuccess { get; set; } = context =>
    {
        context.HttpContext.User = context.Principal!;
        context.Success();
        return new ValueTask();
    };

    public Func<BladeAuthMessageReceivedContext, ValueTask> OnMessageReceived { get; set; } = async context =>
    {
        const string accessToken = "accessToken";
        const string accessToken1 = "access_token";
        var request = context.HttpContext.Request;
        var options = context.Options;

        if (!string.IsNullOrEmpty(request.Headers[options.TokenHeaderName]))
        {
            context.Token = request.Headers[options.TokenHeaderName];
            context.TokenName = options.TokenHeaderName;
            context.TokenPath = nameof(request.Headers);
        }
        else if (!string.IsNullOrEmpty(request.Headers.Authorization))
        {
            context.Token = request.Headers.Authorization;
            context.TokenName = nameof(request.Headers.Authorization);
            context.TokenPath = nameof(request.Headers);
        }
        else if (!string.IsNullOrEmpty(request.Query[accessToken1]))
        {
            context.Token = request.Query[accessToken1];
            context.TokenName = accessToken1;
            context.TokenPath = nameof(request.QueryString);
        }
        else if (!string.IsNullOrEmpty(request.Query[accessToken]))
        {
            context.Token = request.Query[accessToken];
            context.TokenName = accessToken;
            context.TokenPath = nameof(request.QueryString);
        }

        if (context.Token?.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase) ?? false)
        {
            context.Token = context.Token["Bearer ".Length..].Trim();

            var token = context.Token;
            var cache = context.HttpContext.RequestServices.GetService<IRedisCache<RedisCacheOptionsBase>>();

            if (cache is not null)
            {
                var identity = await cache.GetCacheValueAsync<string, ClaimsIdentity>(BladeAuthOptions.SchemeName, token);

                if (identity is not null)
                {
                    var exp = identity.FindFirst(ClaimTypes.Expiration)?.Value;
                    var lifetimeSeconds = long.TryParse(exp, out var expires) ? expires - DateTimeOffset.UtcNow.ToUnixTimeSeconds() : -1;

                    if (lifetimeSeconds > 0)
                    {
                        context.Principal = new ClaimsPrincipal(identity);
                        context.Success();
                        return;
                    }

                    await cache.RemoveCacheValueAsync(BladeAuthOptions.SchemeName, token);

                }
            }
            
        }
        
        context.NoResult();

    };


    /// <summary>
    /// Invoked if exceptions are thrown during request processing. The exceptions will be re-thrown after this event unless suppressed.
    /// </summary>
    public ValueTask AuthenticationFailed(BladeAuthenticatedContext context) => OnAuthenticationFailed(context);

    /// <summary>
    /// Invoked after the security token has passed validation and a ClaimsIdentity has been generated.
    /// </summary>
    public ValueTask<AuthenticateResult?> TokenValidated(BladeAuthenticatedContext context) => OnTokenValidated(context);

    public ValueTask AuthenticationSuccess(BladeAuthenticatedContext context) => OnAuthenticationSuccess(context);

    public ValueTask MessageReceived(BladeAuthMessageReceivedContext context) => OnMessageReceived(context);
}
