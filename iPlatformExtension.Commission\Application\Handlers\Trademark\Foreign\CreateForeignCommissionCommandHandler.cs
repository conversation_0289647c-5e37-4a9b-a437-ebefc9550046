using AutoMapper;
using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;
using iPlatformExtension.Commission.Application.Notifications.Trademark.Foreign;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class CreateForeignCommissionCommandHandler(
    IMediator mediator,
    IMapper mapper,
    IHttpContextAccessor httpContextAccessor,
    IForeignTrademarkBonusRepository foreignTrademarkBonusRepository) 
    : IRequestHandler<CreateForeignCommissionCommand>
{
    public async Task Handle(CreateForeignCommissionCommand request, CancellationToken cancellationToken)
    {
        var (procId, dto) = request;
        var freeSql = foreignTrademarkBonusRepository.Orm;
        var foreignTrademarkCommission = await freeSql.Select<CaseProcInfo>(procId).ToOneAsync(info => new RpForeignTrademarkBonus()
        {
            ProcId = info.ProcId,
            AppNo = info.CaseInfo.AppNo,
            CaseName = info.CaseInfo.CaseName,
            ReceiveDate = info.CommissionEffectiveDate!.Value,
            CtrlProcId = info.CtrlProcId,
            CtrlProcMark = info.CtrlProcMark ?? string.Empty,
            PointDate = DateTime.Now,
            ProcNo = info.ProcNo,
            ProcStatusId = info.ProcStatusId,
            RegisterNo = info.CaseInfo.RegisterNo,
            TrademarkClasses = info.CaseInfo.TrademarkClass ?? string.Empty,
            TrademarkItemsNum = 0,
            UndertakeUserId = info.UndertakeUserId ?? string.Empty,
            Volume = info.CaseInfo.Volume,
            CustomerId = info.CaseInfo.CustomerId,
            Year = info.CommissionEffectiveDate!.Value.Year,
            Month = info.CommissionEffectiveDate!.Value.Month,
            CaseId = info.CaseId,
            CaseDirection = info.CaseInfo.CaseDirection,
            Creator = UserIds.Administrator.ToSqlStringConstant(),
            Updater = UserIds.Administrator.ToSqlStringConstant()
        }, cancellationToken);
        
        await mediator.Publish(new ForeignCommissionResultNotification(foreignTrademarkCommission), cancellationToken);

        if (dto is not null)
        {
            if (await freeSql.Select<RpForeignTrademarkBonus>(procId).AnyAsync(cancellationToken))
            {
                throw new ApplicationException("数据已存在，不能重复添加");
            }
            
            mapper.Map(dto, foreignTrademarkCommission);
            
            var userId = httpContextAccessor.HttpContext?.User.GetUserId() ?? string.Empty;
            foreignTrademarkCommission.Creator = userId;
            foreignTrademarkCommission.Updater = userId;
            
            await foreignTrademarkBonusRepository.InsertAsync(foreignTrademarkCommission, cancellationToken);
        }
        else if (await mediator.Send(new ComputeForeignCommissionPointCommand(foreignTrademarkCommission), cancellationToken))
        {
            await foreignTrademarkBonusRepository.InsertAsync(foreignTrademarkCommission, cancellationToken);
        }
    }
}