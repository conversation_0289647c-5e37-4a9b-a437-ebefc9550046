﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class FixDomesticCommissionCommandHandler(
    ISender sender,
    EntityTypeInfoProvider entityTypeInfoProvider,
    ICaseProcInfoRepository caseProcInfoRepository) : IRequestHandler<FixDomesticCommissionCommand, bool>
{
    public async Task<bool> Handle(FixDomesticCommissionCommand reqeust, CancellationToken cancellationToken)
    {
        var procId = reqeust.ProcId;
        var caseProcInfo = await caseProcInfoRepository.GetAsync(procId, cancellationToken);
        var freeSql = caseProcInfoRepository.Orm;

        if (caseProcInfo is null)
        {
            return false;
        }
        
        var configs = await freeSql.Select<TrademarkBonusConfig>()
            .Where(config => config.CtrlProcId == caseProcInfo.CtrlProcId)
            .Where(config => config.IsEnabled == true)
            .Where(config => config.CaseDirection == CaseDirection.II)
            .Where(config => config.CtrlProcStatusId == caseProcInfo.ProcStatusId)
            .ToListAsync(cancellationToken);

        List<TrademarkBonusConfig> matchConfigs;
        if (!string.IsNullOrWhiteSpace(caseProcInfo.CtrlProcMark))
        {
            matchConfigs = configs.Where(config => config.CtrlProcMark == caseProcInfo.CtrlProcMark).ToList();
            if (matchConfigs.Count == 0)
            {
                matchConfigs = configs.Where(config => string.IsNullOrWhiteSpace(config.CtrlProcMark)).ToList();
            }
        }
        else
        {
            matchConfigs = configs.Where(config => string.IsNullOrWhiteSpace(config.CtrlProcMark)).ToList();
        }

        if (matchConfigs.Count is not 1)
        {
            return false;
        }
        
        var matchedConfig = matchConfigs[0];
        var dateType = matchedConfig.DateType;
        
        DateTime? commissionDate = null;
            
        var entityTypeInfo = entityTypeInfoProvider.Get(typeof(CaseProcInfo));
        var properties = entityTypeInfo.EntityPropertyInfos;
        var property = properties.Values.FirstOrDefault(info => info.ColumnName == dateType);
            
        if (property == null)
            return false;
            
        commissionDate = property.Get?.Invoke(caseProcInfo) as DateTime?;
        if (commissionDate is null)
        {
            return false;
        }

        if (commissionDate.Value.Month != 11)
        {
            commissionDate = DateTime.Now;
        }
        else
        {
            commissionDate = commissionDate.Value;
        }
        caseProcInfo.CommissionEffectiveDate = commissionDate;

        var result = await caseProcInfoRepository.UpdateAsync(caseProcInfo, cancellationToken) > 0;

        // await sender.Send(new CreateDomesticCommissionCommand(procId), cancellationToken);
        
        return result;
    }
}