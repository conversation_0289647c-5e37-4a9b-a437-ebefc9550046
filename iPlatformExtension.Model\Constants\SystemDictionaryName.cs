﻿namespace iPlatformExtension.Model.Constants;

/// <summary>
/// 字典表中的各种类型
/// </summary>
public static class SystemDictionaryName
{
    public const string FeeType = "fee_type";

    public const string BalanceWay = "balance_way";

    public const string ChargeRule = "charge_rule";

    public const string PayWay = "pay_way";

    public const string OfficerStatus = "officer_status";

    public const string PaymentName = "payment_name";

    public const string ReceiveStatus = "receive_status";

    public const string PayStatus = "pay_status";

    public const string CaseType = "case_type";

    public const string ApplyType = "applyType";

    public const string CaseDirection = "case_direction";

    public const string TaxpayerType = "taxpayer_type";

    public const string MonDept = "mon_dept";

    public const string HeadUserType = "head_user_type";

    public const string Attribute = "attribute";

    public const string CardType = "card_type";

    public const string TrademarkingType = "tm_show_mode";

    /// <summary>
    /// 递交key
    /// </summary>
    public const string DeliveryKey = "delivery_key";

    public const string ApplicationType = "multi_type";

    public const string ProcStatus = "proc_status";

    /// <summary>
    /// 名义变更类型
    /// </summary>
    public const string NominalChangesType = "nominal_changes_type";

    /// <summary>
    /// 任务标识
    /// </summary>
    public const string CtrlProcMark = "ctrl_proc_mark";

    /// <summary>
    /// 商标递交业务类型
    /// </summary>
    public const string TrademarkDeliveryBusinessType = "trademark_delivery_business_type";

    /// <summary>
    /// 无效宣告法律条款
    /// </summary>
    public const string AnnulmentLawProvision = "annulment_law_provision";

    /// <summary>
    /// 商标异议法律条款
    /// </summary>
    public const string ObjectionsLawProvision = "objections_law_provision";

    /// <summary>
    /// 许可类型
    /// </summary>
    public const string LicenseType = "licenses_type";

    /// <summary>
    /// 客户管控标识
    /// </summary>
    public const string CustomerControlIdentifier = "controls_identified";

    /// <summary>
    /// 汇款费用分摊方式
    /// </summary>
    public const string RemittanceFeeSharingMethod = "remittance_charges_todetail";

    /// <summary>
    /// 国家认证
    /// </summary>
    public const string CountryRecognized = "country_recognized";

    /// <summary>
    /// 外所账单付款状态
    /// </summary>
    public const string ForeignBillPaymentStatus = "fbill_pay_status";

    /// <summary>
    /// 商标申请途径
    /// </summary>
    public const string TrademarkApplyChannel = "tm_apply_channel";

    /// <summary>
    /// 裁定结果
    /// </summary>
    public const string RulingResult = "determine_results";
    
    /// <summary>
    /// 胜诉奖励规则日期类型
    /// </summary>
    public const string WinningRewardDateType = "winning_reward_date_type";

    /// <summary>
    /// 商标标签
    /// </summary>
    public const string TrademarkTab = "trademark_tab";

}