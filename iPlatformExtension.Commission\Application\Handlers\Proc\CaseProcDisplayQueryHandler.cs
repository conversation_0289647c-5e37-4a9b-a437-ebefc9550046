﻿using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Commission.Application.Queries.Proc;
using iPlatformExtension.Commission.Infrastructure.Constant;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.Customer;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Proc;

internal sealed class CaseProcDisplayQueryHandler(
    IFreeSql freeSql,
    ISystemDictionaryRepository dictionaryRepository,
    ICustomerRepository customerRepository,
    IBaseCtrlProcRepository baseCtrlProcRepository,
    IUserInfoRepository userInfoRepository,
    IBigClientRepository clientRepository) : IRequestHandler<CaseProcDisplayQuery, CaseProcDisplayInfo>
{
    public async Task<CaseProcDisplayInfo> Handle(CaseProcDisplayQuery request, CancellationToken cancellationToken)
    {
        var procId = request.ProcId;
        var procInfo = await freeSql.Select<CaseProcInfo>(procId).WithLock().Include(info => info.CaseInfo)
            .ToOneAsync(info => new CaseProcInfo()
            {
                ProcId = info.ProcId,
                CtrlProcId = info.CtrlProcId,
                ProcStatusId = info.ProcStatusId,
                ProcUndertakeMainUserId = info.ProcUndertakeMainUserId,
                UndertakeUserId = info.UndertakeUserId,
                ProcNo = info.ProcNo,
                CtrlProcMark = info.CtrlProcMark,
                ResultRemark = info.ResultRemark,
                SituationChanged = info.SituationChanged,
                CaseInfo = new CaseInfo()
                {
                    Id = info.CaseInfo.Id,
                    AppNo = info.CaseInfo.AppNo,
                    RegisterNo = info.CaseInfo.RegisterNo,
                    CaseName = info.CaseInfo.CaseName,
                    CaseDirection = info.CaseInfo.CaseDirection,
                    CustomerId = info.CaseInfo.CustomerId,
                    Volume = info.CaseInfo.Volume,
                }
            }, cancellationToken);

        if (procInfo == null)
        {
            throw new NotFoundException(procId, "商标任务");
        }

        var caseProcDisplayInfo = new CaseProcDisplayInfo
        {
            ProcId = procInfo.ProcId,
            CaseId = procInfo.CaseInfo.Id,
            Volume = procInfo.CaseInfo.Volume,
            AppNo = procInfo.CaseInfo.AppNo,
            RegisterNo = procInfo.CaseInfo.RegisterNo,
            ProcNo = procInfo.ProcNo,
            SituationChanged = procInfo.SituationChanged,
            CaseName = procInfo.CaseInfo.CaseName,
            CtrlProcId = procInfo.CtrlProcId,
            CtrProcName = await baseCtrlProcRepository.GetTextValueAsync(procInfo.CtrlProcId) ?? string.Empty,
            CustomerId = procInfo.CaseInfo.CustomerId,
            CustomerName = (await customerRepository.GetCacheValueAsync(procInfo.CaseInfo.CustomerId, cancellationToken:cancellationToken))?.CustomerName ?? string.Empty,
            CtrlProcMark = procInfo.CtrlProcMark ?? string.Empty,
            ProcMarkText = await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.CtrlProcMark, procInfo.CtrlProcMark),
            ProcStatusId = procInfo.ProcStatusId,
            ProcStatusText = await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.ProcStatus, procInfo.ProcStatusId),
            UndertakerId = (CaseDirections.ForeignCaseDirections.Contains(procInfo.CaseInfo.CaseDirection) ? procInfo.UndertakeUserId : procInfo.ProcUndertakeMainUserId) ?? string.Empty,
            IsBigClient = await clientRepository.GetCacheValueAsync(new BigClientKey(procInfo.CaseInfo.CustomerId, procInfo.CaseInfo.CaseDirection, true), cancellationToken:cancellationToken) is not null,
            RulingResult = await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.RulingResult, procInfo.ResultRemark)
        };

        caseProcDisplayInfo.UndertakerName =
            await userInfoRepository.GetTextValueAsync(caseProcDisplayInfo.UndertakerId) ?? string.Empty;
        
        return caseProcDisplayInfo;
    }
}