﻿using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    public class DeleteMailHostHandler(IMailHostRepository mailHostRepository) : IRequestHandler<DeleteMailHostCommand>
    {
        public Task Handle(DeleteMailHostCommand request, CancellationToken cancellationToken)
        {
            ArgumentException.ThrowIfNullOrEmpty(request.HostId);
            return mailHostRepository.DeleteAsync(request.HostId, cancellationToken);
        }
    }
}
