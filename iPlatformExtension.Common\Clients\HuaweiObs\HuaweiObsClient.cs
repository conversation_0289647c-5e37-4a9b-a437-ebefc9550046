﻿using System.Web;
using Confluent.Kafka;
using iPlatformExtension.Common.Clients.HuaweiObs.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OBS;
using OBS.Model;

namespace iPlatformExtension.Common.Clients.HuaweiObs;

public class HuaweiObsClient
{
    private readonly ObsClient _obsClient;

    private readonly IOptionsMonitor<ObsClientOptions> _options;

    private readonly ILogger<HuaweiObsClient> _logger;

    public string Bucket => _options.CurrentValue.DefaultBucketName;

    public HuaweiObsClient(IOptionsMonitor<ObsClientOptions> options, ILogger<HuaweiObsClient> logger)
    {
        _options = options;
        _logger = logger;

        var (accessKey, secretKey, securityToken, endpoint) = options.CurrentValue;
        _obsClient = new ObsClient(accessKey, secretKey, securityToken, endpoint);

        options.OnChange(RefreshClientConfiguration);
    }

    internal void RefreshClientConfiguration(ObsClientOptions newOptions)
    {
        _logger.LogInformation("refresh huawei cloud obs client options");
        var (accessKey, secretKey, securityToken) = newOptions;
        _obsClient.Refresh(accessKey, secretKey, securityToken);
    }

    public Task<PutObjectResponse> PutFileAsync(string objectName, Stream fileStream, string? bucketName = null, string? fileName = null)
    {
        var options = _options.CurrentValue;
        bucketName = string.IsNullOrWhiteSpace(bucketName) ? options.DefaultBucketName : bucketName;
        var contentDisposition = string.IsNullOrWhiteSpace(fileName) ? "attachment" : $"attachment;filename={HttpUtility.UrlEncode(Path.GetFileName(fileName))}";
        var request = new PutObjectRequest()
        {
            BucketName = bucketName,
            InputStream = fileStream,
            ObjectKey = objectName,
            ContentDisposition = contentDisposition
        };

        return Task.Factory.FromAsync(_obsClient.BeginPutObject, _obsClient.EndPutObject, request, null);
    }

    public Task<CreateBucketResponse> CreateBucketAsync(string bucketName)
    {
        ArgumentNullException.ThrowIfNull(bucketName);

        var request = new CreateBucketRequest()
        {
            BucketName = bucketName
        };

        return Task.Factory.FromAsync(_obsClient.BeginCreateBucket, _obsClient.EndCreateBucket, request, null);
    }

    public Task<ListBucketsResponse> ListBucketAsync()
    {
        var request = new ListBucketsRequest();

        return Task.Factory.FromAsync(_obsClient.BeginListBuckets, _obsClient.EndListBuckets, request, null);
    }

    public Task<bool> HasBucketAsync(string bucketName)
    {
        ArgumentException.ThrowIfNullOrEmpty(bucketName);
        var request = new HeadBucketRequest()
        {
            BucketName = bucketName
        };

        return Task.Factory.FromAsync(_obsClient.BeginHeadBucket, _obsClient.EndHeadBucket, request, null);
    }

    public Task<bool> HasObjectAsync(string bucketName, string objectName)
    {
        ArgumentException.ThrowIfNullOrEmpty(bucketName);
        ArgumentException.ThrowIfNullOrEmpty(objectName);

        var request = new HeadObjectRequest()
        {
            BucketName = bucketName,
            ObjectKey = objectName
        };

        return Task.Factory.FromAsync(_obsClient.BeginHeadObject, _obsClient.EndHeadObject, request, null);
    }

    public CreateTemporarySignatureResponse GenerateTemporaryUrl(string objectName, string? bucketName = null,
        TimeSpan? expirationSpan = null)
    {
        ArgumentException.ThrowIfNullOrEmpty(objectName);

        var options = _options.CurrentValue;
        bucketName = string.IsNullOrWhiteSpace(bucketName) ? options.DefaultBucketName : bucketName;
        expirationSpan ??= options.DefaultTemporaryUriExpirationTime;

        var request = new CreateTemporarySignatureRequest()
        {
            BucketName = bucketName,
            Expires = (long?)expirationSpan.Value.TotalSeconds,
            Method = HttpVerb.GET,
            ObjectKey = objectName
        };

        return _obsClient.CreateTemporarySignature(request);
    }


    public Task<DeleteObjectResponse> DeleteObjectAsync(string objectName, string? bucketName = null)
    {
        ArgumentException.ThrowIfNullOrEmpty(objectName);

        var options = _options.CurrentValue;
        bucketName = string.IsNullOrWhiteSpace(bucketName) ? options.DefaultBucketName : bucketName;
        var request = new DeleteObjectRequest()
        {
            BucketName = bucketName,
            ObjectKey = objectName
        };
        return Task.Factory.FromAsync(_obsClient.BeginDeleteObject, _obsClient.EndDeleteObject, request, null);
    }

    /// <summary>
    /// 获取OBS文件对象
    /// </summary>
    /// <param name="objectName"></param>
    /// <param name="bucketName"></param>
    /// <returns></returns>
    public Task<GetObjectResponse> GetObjectAsync(string objectName, string? bucketName = null)
    {
        try
        {
            var options = _options.CurrentValue;
            bucketName = string.IsNullOrWhiteSpace(bucketName) ? options.DefaultBucketName : bucketName;
            GetObjectRequest request = new GetObjectRequest()
            {
                BucketName = bucketName,
                ObjectKey = objectName,
            };
            return Task.Factory.FromAsync(_obsClient.BeginGetObject, _obsClient.EndGetObject, request, null);
        }
        catch (ObsException ex)
        {
            Console.WriteLine("GetObjectAsync下载异常: {0}", ex.Message);
        }
        return Task.FromResult(new GetObjectResponse());
    }



    /// <summary>
    /// 获取OBS文件对象byte[]类型
    /// </summary>
    /// <param name="objectName"></param>
    /// <param name="bucketName"></param>
    /// <returns></returns>
    public async Task<byte[]?> GetObjectToByteAsync(string objectName, string? bucketName = null)
    {
        var objectResponse = await GetObjectAsync(objectName, bucketName);
        if (objectResponse.OutputStream == null) return default;

        var stream = objectResponse.OutputStream;
        using (var ms = new MemoryStream())
        {
            //stream.Position = 0; // 重置流的位置
            await stream.CopyToAsync(ms);
            return ms.ToArray();
        }
    }

}