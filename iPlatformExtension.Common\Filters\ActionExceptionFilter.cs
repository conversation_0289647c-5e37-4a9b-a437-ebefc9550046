﻿using System.Diagnostics;
using System.Text;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Common.Filters;

public class ActionExceptionFilter<T> : IAsyncExceptionFilter where T : ApiResult<object>, new()
{
    private readonly ILogger _logger;

    private readonly ObjectPool<StringBuilder> _stringBuilderPool;

    private readonly IWebHostEnvironment _environment;

    /// <summary>
    /// 构造函数注入
    /// </summary>
    /// <param name="loggerFactory">日志工厂</param>
    /// <param name="stringBuilderPool">stringBuilder对象池</param>
    /// <param name="environment">环境变量</param>
    public ActionExceptionFilter(ILoggerFactory loggerFactory, ObjectPool<StringBuilder> stringBuilderPool,
         IWebHostEnvironment environment)
    {
        _stringBuilderPool = stringBuilderPool;
        _environment = environment;
        _logger = loggerFactory.CreateLogger(GetType());
    }

    /// <summary>
    /// 请求异常处理。
    /// 只能处理controller域的异常。
    /// 中间件的异常需要ExceptionHandler
    /// </summary>
    /// <param name="context">异常处理上下文</param>
    /// <returns></returns>
    public async Task OnExceptionAsync(ExceptionContext context)
    {
        var ex = context.Exception;
        var displayName = context.ActionDescriptor.DisplayName;
        var traceId = Activity.Current?.TraceId.ToString() ?? context.HttpContext.TraceIdentifier;
        var httpContext = context.HttpContext;

        var parameterTipsBuilder = _stringBuilderPool.Get();

        foreach (var parameter in context.ActionDescriptor.Parameters)
        {
            var bindSourceDisplayName = parameter.BindingInfo?.BindingSource?.DisplayName;
            switch (bindSourceDisplayName)
            {
                case "Body":
                    parameterTipsBuilder.AppendLine();
                    if (httpContext.Request.HasFormContentType)
                    {
                        foreach (var (name, value) in httpContext.Request.Form)
                        {
                            parameterTipsBuilder.Append('@').Append(name).Append('=')
                                .Append(value);
                        }
                    }
                    else
                    {
                        httpContext.Request.Body.Position = 0;
                        var result = await httpContext.Request.BodyReader.ReadAsync();
                        var bodyString = Encoding.UTF8.GetString(result.Buffer);
                        parameterTipsBuilder.Append('@').Append(parameter.Name).Append('=').Append(bodyString)
                            .AppendLine();
                    }

                    break;

                case "Path":
                    parameterTipsBuilder.Append('@').Append(parameter.Name).Append('=')
                        .Append(httpContext.Request.RouteValues[parameter.Name]);
                    break;
                case "Query":
                    parameterTipsBuilder.Append('@').Append(parameter.Name).Append('=')
                        .Append(httpContext.Request.Query[parameter.Name]);
                    break;
            }
        }
        
        var errorMessage = parameterTipsBuilder.ToString();
        _logger.LogActionFailed(ex, traceId, displayName, errorMessage);

        var apiResult = new T().Fail(ex);

        if (_environment.IsProduction() && ex is not ApplicationException)
        {
            apiResult = apiResult.Fail(traceId);
        }

        context.Result = new ObjectResult(apiResult);

        context.ExceptionHandled = true;
        _stringBuilderPool.Return(parameterTipsBuilder);

        httpContext.Response.StatusCode = ex switch
        {
            NotAuthenticatedException => 401,
            NotFoundException => 404,
            ApplicationException => 400,
            _ => 500
        };
    }
}