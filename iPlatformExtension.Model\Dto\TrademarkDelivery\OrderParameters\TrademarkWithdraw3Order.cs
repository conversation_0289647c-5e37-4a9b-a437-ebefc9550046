﻿using System.Text.Json.Serialization;
using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;

/// <summary>
/// 商标撤销3年下单
/// </summary>
public sealed class TrademarkWithdraw3Order : PhoenixOrderRequestParameters
{
    /// <summary>
    /// 申请人资质
    /// 申请人类型
    /// </summary>
    public string OwnerType { get; set; } = null!;
    
    /// <summary>
    /// 国家
    /// </summary>
    public string Country { get; set; } = null!;

    /// <summary>
    /// 申请人邮编
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Code { get; set; }

    /// <summary>
    /// （企业）统一社会信用代码
    /// （个人）身 份证号
    /// </summary>
    public string IdCard { get; set; } = null!;

    /// <summary>
    /// 委托人电话
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? PrincipalTel { get; set; }

    /// <summary>
    /// 代理人姓名
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? AgentOrganConName { get; set; }

    /// <summary>
    /// 客户的联系电话
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ContactTel { get; set; }

    /// <summary>
    /// 主体资格类型:1表示中文,0表示非中文
    /// </summary>
    public string SubjectType { get; set; } = null!;
    
    /// <summary>
    /// 申请人国内接受人电邮
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? DomesticReceiverEmail { get; set; }

    /// <summary>
    /// 申请人国内接受姓名
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? DomesticReceiverName { get; set; }

    
    /// <summary>
    /// 申请人国内接收人地址
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? DomesticReceiverAddress { get; set; }

    /// <summary>
    /// 客户的联系邮箱
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ContactEmail { get; set; }

    /// <summary>
    /// 申请人地址
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ApplicantAddress { get; set; }

    /// <summary>
    /// 申请人英文地址
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ApplicantEnglishAddress { get; set; }

    /// <summary>
    /// 客户的联系名称
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ContactName { get; set; }

    /// <summary>
    /// 申请人国内接收人地址邮编
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? DomesticReceiverCode { get; set; }

    /// <summary>
    /// 委托人姓名
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? PrincipalName { get; set; }

    /// <summary>
    /// 申请主体名称(中文)
    /// </summary>
    public string ApplicantName { get; set; } = null!;

    /// <summary>
    /// 申请主体名称(英文)
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? EnglishApplicantName { get; set; }

    /// <summary>
    /// 申请人书式类型,1表示中国大陆,2表示海外,3表示台湾,4表示香港,5表示澳门
    /// </summary>
    public string BookType { get; set; } = null!;

    /// <summary>
    /// "申请人证件类型,
    /// 0不是任何类型,
    /// 1身份证,
    /// 2护照,
    /// 3其他(ownerType为企业可以忽略)"
    /// </summary>
    public string CertificatesType { get; set; } = null!;

    /// <summary>
    /// 
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? BrandBlackFile { get; set; }

    /// <summary>
    /// 商标logo下载地址
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? BrandFile { get; set; }

    /// <summary>
    /// 商标说明
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? BrandExplain { get; set; }

    /// <summary>
    /// 商标代理委托书下载地址
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? PowerOfAttorneyFile { get; set; }

    /// <summary>
    /// 申请人递交资料
    /// 不做参数序列化
    /// </summary>
    public IEnumerable<ApplicantAttachment> ApplicantAttachments { get; set; } = Array.Empty<ApplicantAttachment>();
    
    /// <summary>
    /// 申请人材料
    /// </summary>
    [JsonSerializationSource(nameof(ApplicantAttachments))]
    public string? Attachments { get; private set; }

    /// <summary>
    /// 商标商品信息
    /// </summary>
    public IEnumerable<BrandInfo> BrandInfos { get; set; } = Array.Empty<BrandInfo>();

    /// <summary>
    /// 序列化后商品信息
    /// </summary>
    [JsonSerializationSource(nameof(BrandInfos))]
    public string? BrandTransferParam { get; private set; }

    /// <summary>
    /// 商标名称
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? BrandName { get; set; }

    /// <summary>
    /// 商标类型:1文字2文字及图3图形
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public TrademarkType? BrandType { get; set; }

    /// <summary>
    /// 尼斯分类大类个数
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? Number { get; set; }
}