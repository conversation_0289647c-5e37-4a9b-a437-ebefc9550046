using System.Reflection;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Unicode;
using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Common.Authorization;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Db;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Filters;
using iPlatformExtension.Common.Formatters.Input;
using iPlatformExtension.Common.Formatters.Output;
using iPlatformExtension.Common.Mediator.PipelineBehaviors;
using iPlatformExtension.Common.OpenApi.Document;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Common.Validation;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.CDC;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Consumers;
using iPlatformExtension.Outsourcing.GrpcServices;
using iPlatformExtension.Outsourcing.Infrastructure.Authorization.ApplyOutsourcing;
using iPlatformExtension.Outsourcing.Infrastructure.Authorization.NotAssignedOutsourcing;
using iPlatformExtension.Outsourcing.Infrastructure.Authorization.ProcOutsourcing;
using iPlatformExtension.Outsourcing.Infrastructure.Constants;
using iPlatformExtension.Outsourcing.Infrastructure.ExceptionHandlers;
using iPlatformExtension.Outsourcing.Infrastructure.Extensions;
using iPlatformExtension.Outsourcing.Infrastructure.MessageQueue;
using KafkaFlow;
using KafkaFlow.Serializer;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.IdentityModel.Tokens;
using Nacos.AspNetCore.V2;
using NLog.Web;
using Scalar.AspNetCore;

[assembly:ApplicationPart("iPlatformExtension.Common")]

var applicationBuilder = WebApplication.CreateBuilder(args);
var services = applicationBuilder.Services;
var configuration = applicationBuilder.Configuration;
var logging = applicationBuilder.Logging;
var environment = applicationBuilder.Environment;
var webHost = applicationBuilder.WebHost;

webHost.ConfigureKestrel(options =>
{
    options.Limits.MaxRequestLineSize = int.MaxValue;
    options.Limits.MaxRequestBufferSize = long.MaxValue;
    options.Limits.MaxRequestBodySize = long.MaxValue;
});

logging.ClearProviders();
logging.AddSimpleConsole(options => options.IncludeScopes = true);
logging.AddNLogWeb(new NLogAspNetCoreOptions
{
    RemoveLoggerFactoryFilter = false,
    LoggingConfigurationSectionName = "NLog"
});

if (!environment.IsLocal())
{
    services.AddNacosAspNet(configuration);
    configuration.AddNacosV2Configuration(configuration.GetSection("nacos"));
    
    services.AddNacosServiceDiscovery(options =>
    {
        options.Groups = ["DEFAULT_GROUP"];
    });
}
else
{
    configuration.AddJsonFile("kafka.json", true, true);
}

services.AddDataService().AddCache().ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
{
    options.ConnectionString = configuration.GetConnectionString("Redis");
    options.Converters.Add(new ClaimsIdentityConverter());
});

services.AddPlatformFreeSql(configuration.GetConnectionString("Default") ??
                            throw new NullReferenceException("Default connection string"))
    .ConfigureGlobalCurdLogging("Logging:FreeSql:Curd");

services.AddAutoMapper(typeof(Program));
services.AddObjectPools();
services.AddHttpLogging(options =>
{
    options.LoggingFields |= HttpLoggingFields.RequestHeaders;
    options.LoggingFields |= HttpLoggingFields.RequestPath;
    options.LoggingFields |= HttpLoggingFields.RequestQuery;
    options.LoggingFields |= HttpLoggingFields.RequestBody;
    options.LoggingFields |= HttpLoggingFields.ResponsePropertiesAndHeaders;
    if (environment.IsDevelopment())
    {
        options.LoggingFields |= HttpLoggingFields.ResponseBody;
    }
    options.RequestBodyLogLimit = 327680;
});
services.AddHttpContextAccessor();

services.TryAddSingleton<EntityTypeInfoProvider>();

services.AddMediatR(options =>
{
    options.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
    options.AddOpenBehavior(typeof(ScopeLoggerPipelineBehavior<,>));
    options.AddOpenBehavior(typeof(DefaultFreeSqlUnitOfWorkPipelineBehavior<,>), ServiceLifetime.Scoped);
});

if (!environment.IsProduction())
{
    
    services.AddOpenApi(options => options.AddDocumentTransformer<AuthenticationSecuritySchemeTransformer>());
}
else
{
    services.AddEntityChangeLogs();
    services.AddMongoDbContext<PlatformMongoDbContext>(configuration.GetConnectionString("Log") ?? throw new NullReferenceException("Log connection string"));
}

services.AddAuthentication().AddScheme<BladeAuthOptions, BladeAuthenticationHandler>(BladeAuthOptions.SchemeName, options =>
{
    options.TokenValidationParameters = new TokenValidationParameters()
    {
        ValidateIssuer = false,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidateAudience = false,
        IssuerSigningKey = new SymmetricSecurityKey(configuration["BladeAuth:SecurityKey"]?.GetBytes(Encoding.UTF8))
    };
    options.Events.OnTokenValidated = AuthenticationExtension.ValidateUserAsync;
}).AddJwtBearer(PlatformAuthOptions.SchemeName, options =>
{
    var platformAuthOptions = configuration.GetSection("IPlatformAuth").Get<PlatformAuthOptions>();
    ArgumentNullException.ThrowIfNull(platformAuthOptions);
    options.TokenValidationParameters = new TokenValidationParameters()
    {
        ValidAlgorithms = [SecurityAlgorithms.HmacSha256],
        ValidateLifetime = true,
        ValidIssuers = platformAuthOptions.Issuers,
        ValidateIssuerSigningKey = true,
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidAudiences = platformAuthOptions.Audiences,
        IssuerSigningKey = new SymmetricSecurityKey(platformAuthOptions.SecurityKey.GetBytes(Encoding.UTF8))
    };

    options.Events = new JwtBearerEvents()
    {
        OnTokenValidated = AuthenticationExtension.ValidateUserAsync,
        OnChallenge = AuthenticationExtension.OnChallengeAsync,
    };
    options.ForwardChallenge = BladeAuthOptions.SchemeName;
    options.ForwardForbid = BladeAuthOptions.SchemeName;
});

services.AddControllers(options =>
{
    options.Filters.Add<ActionExceptionFilter<ResultData>>();
    options.Filters.Add<ModelValidationExceptionFilter>();
    options.Filters.Add<ActionResultFilter<ResultData>>();
    options.InputFormatters.Insert(0, JsonPatchInputFormatterExtension.GetJsonPatchInputFormatter());
    options.OutputFormatters.Insert(0, new JsonAsyncEnumerableWrapperOutputFormatter<ResultData>());
}).AddJsonOptions(options =>
{
    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.DictionaryKeyPolicy = JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
    options.JsonSerializerOptions.Converters.Add(new DateTimeJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new DateOnlyJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new NullableDateTimeJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new JsonPatchOperationConverterFactory());
    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter(JsonNamingPolicy.CamelCase));

    if (!environment.IsProduction())
    {
        options.JsonSerializerOptions.WriteIndented = true;
    }
});

services.AddValidation(new ModelValidationOptions()
{
    Lifetime = ServiceLifetime.Scoped,
    ParameterBinder = typeof(AsyncValidationParameterBinder)
});

services.AddGrpc().AddServiceOptions<ForeignSupplierService>(options =>
{
    options.Interceptors.Add<ForeignSupplierServiceExceptionHandler>();
});

services.AddNotAssignedQueryAuthorization(AuthorizationType.TrademarkOutsourcing, options =>
{
    options.BuildDefaultQuery =
        query => query.Where(info => info.ProcStatusId != ProcStatusId.TrademarkHandled)
            .Where(info => info.ProcStatusId != ProcStatusId.TrademarkNotProcessed);
    options.GetDefaultCtrlProcIdsAsync = async (sql, token) =>
    {
        return new HashSet<string>(await sql.Select<BasCtrlProc>()
            .Where(proc => proc.CaseTypeId == CaseType.Trade).Where(proc => proc.IsOutsourcing == true)
            .Where(proc => proc.IsEnabled == true).ToListAsync(proc => proc.CtrlProcId, token));
    };
    options.SpecialCtrlProcIds = ["6EE51434-9C6A-4DD7-B14A-21051F1E9B93"];
});

services.AddNotAssignedQueryAuthorization(AuthorizationType.TrademarkOutsourcingAll, options =>
{
    options.BuildDefaultQuery =
        query => query.Where(info => info.ProcStatusId != ProcStatusId.TrademarkHandled)
            .Where(info => info.ProcStatusId != ProcStatusId.TrademarkNotProcessed);
    options.GetDefaultCtrlProcIdsAsync = async (sql, token) =>
    {
        return new HashSet<string>(await sql.Select<BasCtrlProc>()
            .Where(proc => proc.CaseTypeId == CaseType.Trade).Where(proc => proc.IsOutsourcing == true)
            .Where(proc => proc.IsEnabled == true).ToListAsync(proc => proc.CtrlProcId, token));
    };
    options.SpecialCtrlProcIds = ["6EE51434-9C6A-4DD7-B14A-21051F1E9B93"];
});

services.AddNotAssignedQueryAuthorization(AuthorizationType.PatentOutsourcing, options =>
{
    options.BuildDefaultQuery = query => query.Where(info => info.FinishDate == null);
    options.GetDefaultCtrlProcIdsAsync = async (sql, token) =>
    {
        return new HashSet<string>(await sql.Select<BasCtrlProc>()
            .Where(proc => proc.CaseTypeId == CaseType.Patent).Where(proc => proc.IsOutsourcing == true)
            .Where(proc => proc.IsEnabled == true).ToListAsync(proc => proc.CtrlProcId, token));
    };
});

services.AddNotAssignedQueryAuthorization(AuthorizationType.PatentOutsourcingAll, options =>
{
    options.BuildDefaultQuery = query => query.Where(info => info.FinishDate == null);
    options.GetDefaultCtrlProcIdsAsync = async (sql, token) =>
    {
        return new HashSet<string>(await sql.Select<BasCtrlProc>()
            .Where(proc => proc.CaseTypeId == CaseType.Patent).Where(proc => proc.IsOutsourcing == true)
            .Where(proc => proc.IsEnabled == true).ToListAsync(proc => proc.CtrlProcId, token));
    };
});

services.AddTransient<IAuthorizationHandler, ProcOutsourcingAuthorizationHandler>();
services.AddTransient<IAuthorizationHandler, ApplyCaseOutsourcingAuthorizationHandler>();
services.AddTransient<IAuthorizationHandler, NotAssignedOutsourcingAuthorizationHandler>();
services.AddTransient<IAuthorizationHandler, BatchProcOutsourcingAuthorizationHandler>();

services.AddTransient<IAuthorizationMiddlewareResultHandler, DefaultAuthorizationResultHandler>();

services.AddAuthorizationBuilder().AddPolicy(AuthorizationPolicyNames.ProcOutsourcing, builder =>
{
    builder.AddAuthenticationSchemes(PlatformAuthOptions.SchemeName);
    builder.RequireAuthenticatedUser();
    builder.AddRequirements(
        new ProcOutsourcingAuthorizationRequirement()
            .Add(CaseType.Trade, "商标流程人员")
            .Add(CaseType.Patent, "专利流程人员")
            .Add(CaseType.Project, "专利流程人员"));
}).AddPolicy(AuthorizationPolicyNames.ApplyOutSourcing, builder =>
{
    builder.RequireAuthenticatedUser();
    builder.AddAuthenticationSchemes(PlatformAuthOptions.SchemeName);
}).AddPolicy(AuthorizationPolicyNames.NotAssignedOutsourcing, builder =>
{
    builder.RequireAuthenticatedUser();
    builder.AddAuthenticationSchemes(PlatformAuthOptions.SchemeName);
}).AddPolicy(AuthorizationPolicyNames.BatchProcOutsourcing, builder =>
{
    builder.RequireAuthenticatedUser();
    builder.AddAuthenticationSchemes(PlatformAuthOptions.SchemeName);
    builder.AddRequirements(new BatchProcOutsourcingAuthorizationRequirement()
        .Add(CaseType.Trade, "商标流程人员")
        .Add(CaseType.Patent, "专利流程人员")
        .Add(CaseType.Project, "专利流程人员"));
});

var kafkaOptions = configuration.GetSection("iplatform-outsourcing").Get<KafkaOptions>();
if (kafkaOptions is not null)
{
    services.AddOptions<KafkaOptions>().BindConfiguration("iplatform-outsourcing");
    services.AddKafkaFlowHostedService(builder => builder.UseMicrosoftLog().AddCluster(clusterBuilder =>
    {
        clusterBuilder.WithBrokers(kafkaOptions.Brokers);

        foreach (var consumerOptions in kafkaOptions.Consumers)
        {
            clusterBuilder.AddConsumer(consumerBuilder =>
            {
                consumerBuilder.DependencyConfigurator.AddSingleton<JsonSerializerOptions>(_ =>
                    new JsonSerializerOptions()
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                    });
                
                consumerBuilder.WithGroupId(consumerOptions.GroupId);
                if (!string.IsNullOrWhiteSpace(consumerOptions.ConsumerName))
                {
                    consumerBuilder.WithName(consumerOptions.ConsumerName);
                }
                
                foreach (var (topic, partitions) in consumerOptions.TopicInfos
                             .GroupBy(info => info.TopicName, (topicName, infos) => 
                                 KeyValuePair.Create(topicName, infos.Where(info => info.Partition.HasValue).Select(info => info.Partition))))
                {
                    if (partitions.Any())
                    {
                        consumerBuilder.ManualAssignPartitions(topic,
                            partitions.Select(partition => partition!.Value));
                    }
                    else
                    {
                        consumerBuilder.Topic(topic);
                    }
                }
                
                consumerBuilder.WithAutoOffsetReset(AutoOffsetReset.Latest);
                consumerBuilder.WithWorkersCount(consumerOptions.TopicInfos.Count());
                consumerBuilder.WithBufferSize(10);
                consumerBuilder.AddMiddlewares(middlewareBuilder =>
                {
                    middlewareBuilder.AddSingleTypeDeserializer<JsonCoreDeserializer>(
                        typeof(CdcMessageValue<CdcProcInfo>));
                    middlewareBuilder.AddTypedHandlers(
                        handlerBuilder => handlerBuilder.AddHandler<CdcCaseProcInfoHandler>());
                });
            });
        }
    }));
}

var app = applicationBuilder.Build();

app.UseExceptionHandler("/Error");

app.UseW3CTraceResponse();


if (!environment.IsProduction())
{
    app.MapOpenApi();
    app.MapScalarApiReference();
}

app.HandleUnsuccessfulResponse();

app.UseRequestEnableBuffering();

app.UseHttpLogging();

app.UseRouting();

// app.UseEndpointNotFound();

app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();

app.MapGrpcService<ForeignSupplierService>();

app.Run();

public partial class Program;
