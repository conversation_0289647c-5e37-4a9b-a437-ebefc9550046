﻿using iPlatformExtension.Finance.Applications.Queries.Fees;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeesResultQueryHandler(IMediator mediator) : IRequestHandler<FeesResultQuery>
{
    public async Task Handle(FeesResultQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return ;
        
        await mediator.Send(new FeeResultDictionaryQuery(request.FeeResults), cancellationToken);
        
        var caseIds = request.FeeResults.Select(dto => dto.CaseId).Distinct(StringComparer.CurrentCultureIgnoreCase).ToArray();
        await mediator.Send(new FeeResultApplicantsQuery(caseIds, request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultInventorQuery(caseIds, request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultPriorityQuery(caseIds, request.FeeResults), cancellationToken);
        
        await mediator.Send(new FeeResultApplyTypeQuery(request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultBusinessTypeQuery(request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultCustomerFollowQuery(request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultCaseStatusQuery(request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultCtrlProcQuery(request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultFeeTypeNameQuery(request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultUserInfoQuery(request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultCompanyQuery(request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultCountryQuery(request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultProcStatusQuery(request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultSubProcStatusQuery(request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultDepartmentQuery(request.FeeResults), cancellationToken);
        await mediator.Send(new FeeResultCustomerControlIdentifierQuery(request.FeeResults), cancellationToken);
    }
}