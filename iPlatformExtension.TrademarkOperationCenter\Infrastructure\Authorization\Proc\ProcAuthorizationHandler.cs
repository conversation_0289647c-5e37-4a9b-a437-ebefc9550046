﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Infrastructure;
using Microsoft.AspNetCore.Http.Features;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Authorization.Proc;

/// <summary>
/// 任务授权处理器
/// </summary>
internal sealed class ProcAuthorizationHandler : AuthorizationHandler<RolesAuthorizationRequirement, HttpContext>
{
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 构造器依赖注入
    /// </summary>
    /// <param name="freeSql"></param>
    public ProcAuthorizationHandler(IFreeSql freeSql)
    {
        _freeSql = freeSql;
    }
    
    
    private Task<bool> IsMainUndertakerAsync(object procId, string userId) => _freeSql.Select<CaseProcInfo>(procId)
        .AnyAsync(info => info.ProcUndertakeMainUserId == userId);

    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, RolesAuthorizationRequirement requirement,
        HttpContext resource)
    {
        if (requirement.AllowedRoles.Any(role => context.User.IsInRole(role)))
        {
            context.Succeed(requirement);
            return;
        }

        var userId = context.User.GetUserId();

        var routeValues = resource.Request.RouteValues;
        if (routeValues.TryGetValue("procId", out var procId) && !string.IsNullOrWhiteSpace(procId?.ToString()))
        {
            if (await IsMainUndertakerAsync(procId, userId))
            {
                context.Succeed(requirement);
                return;
            }
        }

        if (resource.Request.Query.TryGetValue("procId", out var procIdValue) && !string.IsNullOrWhiteSpace(procIdValue))
        {
            if (await IsMainUndertakerAsync(procIdValue, userId))
            {
                context.Succeed(requirement);
                return;
            }
        }
        
        context.Fail(new AuthorizationFailureReason(this, "你不是当前任务的主承办人或者你没有相应的角色权限"));
    }
}