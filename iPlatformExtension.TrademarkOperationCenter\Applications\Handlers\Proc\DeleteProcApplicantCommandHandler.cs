﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc
{
    /// <summary>
    /// 任务申请人删除
    /// </summary>
    internal sealed class DeleteProcApplicantCommandHandler(ICaseProcApplicantRepository caseProcApplicant) : 
        IRequestHandler<DeleteProcApplicantCommand>
    {
        /// <summary>
        /// 删除任务申请人
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task Handle(DeleteProcApplicantCommand request, CancellationToken cancellationToken)
        {
            return caseProcApplicant.DeleteAsync(applicant => applicant.Id == request.Id, cancellationToken);
        }
    }
}
