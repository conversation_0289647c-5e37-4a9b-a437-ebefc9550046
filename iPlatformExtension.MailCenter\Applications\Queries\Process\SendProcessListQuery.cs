﻿﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.MailCenter.Applications.Models;
using iPlatformExtension.MailCenter.Applications.Models.Process;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.Process;

/// <summary>
/// 获取发件待审清单列表
/// </summary>
/// <param name="Search">搜索</param>
/// <param name="CheckedPrivateList">标签</param>
public record SendProcessListQuery(string? Search, [Required] List<string>? CheckedPrivateList, string? Sort, string SortType = "Acs") : PageModel, IRequest<IEnumerable<SendProcessListDto>>;
