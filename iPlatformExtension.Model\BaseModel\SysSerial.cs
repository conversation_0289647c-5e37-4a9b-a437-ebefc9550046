using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_serial", DisableSyncStructure = true)]
	public partial class SysSerial {

		[ Column(Name = "serial_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string SerialId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "serial_name", StringLength = 50)]
		public string SerialName { get; set; }

		[ Column(Name = "serial_name_en_us", StringLength = 50)]
		public string SerialNameEnUs { get; set; }

		[ Column(Name = "serial_name_ja_jp", StringLength = 50)]
		public string SerialNameJaJp { get; set; }

		[ Column(Name = "serial_name_zh_cn", StringLength = 50)]
		public string SerialNameZhCn { get; set; }

	}

}
