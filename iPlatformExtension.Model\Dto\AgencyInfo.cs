﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 代理机构信息
/// </summary>
public class AgencyInfo : INameInfo
{
    /// <summary>
    /// 代理机构id
    /// </summary>
    public string Id { get; set; } = string.Empty;
    
    /// <summary>
    /// 代理机构中文名称
    /// </summary>
    public string? CnName { get; set; }
    
    /// <summary>
    /// 代理机构英文名称
    /// </summary>
    public string? EnName { get; set; }
}