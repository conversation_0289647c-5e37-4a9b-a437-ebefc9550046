﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 更新费项的实体
/// </summary>
public record FeeUpdateInfoDto()
{
    /// <summary>
    /// 费项id
    /// </summary>
    [Required]
    public string FeeId { get; set; } = default!;

    /// <summary>
    /// 到款日期。
    /// 划拨必传。
    /// </summary>
    public DateTime? ReceiveDate { get; set; }

    /// <summary>
    /// 应收款日期
    /// </summary>
    public DateTime? ReceiveDueDate { get; set; }

    /// <summary>
    /// 请款日期
    /// </summary>
    public DateTime? RequestDate { get; set; }

    /// <summary>
    /// 发票号。
    /// 发票号作废传空字符串<c>""</c>。
    /// </summary>
    public string? InvoiceNo { get; set; }

    /// <summary>
    /// 请款单号。
    /// 请款必传。
    /// </summary>
    public string? BillNo { get; set; }

    /// <summary>
    /// 客户id
    /// </summary>
    public string? CustomerId { get; set; }

    /// <summary>
    /// 销售人员工号
    /// </summary>
    public string? Sales { get; set; }

    /// <summary>
    /// 划拨人
    /// </summary>
    public string? Allocator { get; set; }

    /// <summary>
    /// 账款操作枚举
    /// </summary>
    [Required]
    public FinanceOperation Operation { get; init; }
}