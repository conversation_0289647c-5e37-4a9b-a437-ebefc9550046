﻿using System.Text.Json.Serialization;
using Microsoft.Extensions.Primitives;
using Yarp.ReverseProxy.Configuration;

namespace iPlatformExtension.Gateway.Nacos;

public class NacosNamingProxyConfig(List<RouteConfig> routes, List<ClusterConfig> clusters, IChangeToken changeToken) 
    : IProxyConfig
{
    /// <inheritdoc />
    public IReadOnlyList<RouteConfig> Routes { get; } = routes;

    /// <inheritdoc />
    public IReadOnlyList<ClusterConfig> Clusters { get; } = clusters;

    /// <inheritdoc />
    [JsonIgnore]
    public IChangeToken ChangeToken { get; } = changeToken;
}