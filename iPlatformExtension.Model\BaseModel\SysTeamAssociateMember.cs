﻿using System;
using System.Collections.Generic;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[Table(Name = "sys_team_associate_member", DisableSyncStructure = true)]
	public partial class SysTeamAssociateMember {

		/// <summary>
		/// 团队主键
		/// </summary>
		[Column(Name = "team_member_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string TeamMemberId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "create_time")]
		public DateTime CreateTime { get; set; }

		/// <summary>
		/// 创建用户id
		/// </summary>
		[Column(Name = "create_user_id", StringLength = 50, IsNullable = false)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 角色id
		/// </summary>
		[Column(Name = "role_id", StringLength = 50, IsNullable = false)]
		public string RoleId { get; set; }

		/// <summary>
		/// 团队id
		/// </summary>
		[Column(Name = "team_id", StringLength = 50, IsNullable = false)]
		public string TeamId { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户id
		/// </summary>
		[Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// 用户id
		/// </summary>
		[Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

		/// <summary>
		/// 用户姓名
		/// </summary>
		[Column(Name = "user_name", StringLength = 50, IsNullable = false)]
		public string UserName { get; set; }


        /// <summary>
        /// 团队成员信息导航
        /// </summary>
        [Navigate(nameof(TeamId))]
        public SysTeam SysTeam { get; set; }

    }

}
