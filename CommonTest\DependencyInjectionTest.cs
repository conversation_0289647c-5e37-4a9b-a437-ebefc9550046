using System.Reflection;
using CommonTest.Models;
using iPlatformExtension.Common.Extensions;
using Microsoft.Extensions.DependencyInjection;

namespace CommonTest;

public class DependencyInjectionTest
{
    private readonly IServiceCollection _serviceCollection = new ServiceCollection();

    private const string AssemblyFileName = "Microsoft.Extensions.ServiceDiscovery.dll";

    private const string InterfaceTypeName =
        "Microsoft.Extensions.ServiceDiscovery.LoadBalancing.IServiceEndpointSelector";
    
    internal static readonly Type _endpointInterfaceType = Assembly.LoadFrom(AssemblyFileName).GetType(InterfaceTypeName) 
                                                           ?? throw new TypeLoadException($"无法加载类型【{InterfaceTypeName}】");

    [Fact]
    public void NullableDependency_Test()
    {
        _serviceCollection.AddSingleton<ServiceA>();

        var provider = _serviceCollection.BuildServiceProvider();
        var serviceA = provider.GetRequiredService<ServiceA>();
        
        Assert.NotNull(serviceA);
        Assert.Null(serviceA.ServiceB);
    }

    [Fact]
    public void NotRequireService_Test()
    {
        _serviceCollection.AddSingleton<ServiceC>();

        var provider = _serviceCollection.BuildServiceProvider();
        var serviceC = provider.GetService<ServiceC>();
        
        Assert.Null(serviceC);
    }

    [Fact]
    public void ServiceEndpointSelectorInjection_Test()
    {
        _serviceCollection.AddServiceEndpointSelector<TestEndpointSelector>();
        
        var provider = _serviceCollection.BuildServiceProvider();
        var selector = provider.GetRequiredService(_endpointInterfaceType);
        
        Assert.True(selector.GetType().IsAssignableTo(_endpointInterfaceType));
    }
}