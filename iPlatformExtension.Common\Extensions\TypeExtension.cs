﻿namespace iPlatformExtension.Common.Extensions;

public static class TypeExtension
{
    public static bool IsSystemType(this Type type)
    {
        return type.IsPrimitive || type == typeof(string) || type.IsEnum;
    }

    public static bool IsNumericType(this Type type)
    {
        var typeCode = Type.GetTypeCode(type);
        return typeCode switch
        {
            TypeCode.UInt16 => true,
            TypeCode.UInt32 => true,
            TypeCode.UInt64 => true,
            TypeCode.Int16 => true,
            TypeCode.Int32 => true,
            TypeCode.Int64 => true,
            TypeCode.Decimal => true,
            TypeCode.Double => true,
            TypeCode.Single => true,
            TypeCode.Byte => true,
            TypeCode.SByte => true,
            _ => false
        };
    }
}