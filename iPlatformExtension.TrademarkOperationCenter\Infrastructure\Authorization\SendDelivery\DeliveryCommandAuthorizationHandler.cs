﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Authorization.SendDelivery;

internal sealed class DeliveryCommandAuthorizationHandler(ISender sender)
    : AuthorizationHandler<SendDeliveryAuthorizationRequirement, HttpContext>
{

    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, SendDeliveryAuthorizationRequirement requirement,
        HttpContext resource)
    {
        var queries = resource.Request.Query;
        if (queries.TryGetValue("button", out var buttonName) && Enum.TryParse<DeliveryButton>(buttonName, ignoreCase:true, out var deliveryButton))
        {
            if (!requirement.RequireToAuthorizeButtons.Contains(deliveryButton))
            {
                context.Succeed(requirement);
            }
            else
            {
                var routeValues = resource.Request.RouteValues;
                if (routeValues.TryGetValue("procId", out var procId))
                {
                    var flowInfo = await sender.Send(new FlowInfoQuery()
                    {
                        ObjId = procId?.ToString() ?? string.Empty,
                        FlowType = requirement.FlowType,
                        FlowSubType = requirement.FlowSubType
                    });
        
                    if (flowInfo?.CurAuditUserID == context.User.GetUserId())
                    {
                        context.Succeed(requirement);
                    }
                    else
                    {
                        context.Fail(new AuthorizationFailureReason(this, "你不是当前流程处理人员!"));
                    }
                }
            }
        }
    }
}