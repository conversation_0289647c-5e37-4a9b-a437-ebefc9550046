﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.Model.Dto;
using MediatR;
using System.Collections.Generic;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    public class GetUserInfoHandler(IFreeSql<PlatformFreeSql> msSql) : IRequestHandler<GetUserInfoQuery, PageResult<GetUserInfoDto>>
    {
        public async Task<PageResult<GetUserInfoDto>> Handle(GetUserInfoQuery request, CancellationToken cancellationToken)
        {
            var list = await msSql.Select<SysUserInfo, SysDeptInfo>().InnerJoin((ui, di) => ui.DeptId == di.DeptId)
                   .Where((ui, di) => ui.UserType == 1)
                   .WhereIf(!string.IsNullOrEmpty(request.SearchCode), (ui, di) => ui.IsEnabled == true)
                   .WhereIf(request.IsCharge.HasValue && request.IsCharge.Value, (ui, di) => di.IsCharge == true)
                   .WhereIf(!string.IsNullOrEmpty(request.SearchKey), (ui, di) => ui.UserName.Contains(request.SearchKey) ||
                                                                                ui.CnName.Contains(request.SearchKey) || ui.EnName.Contains(request.SearchKey) ||
                                                                                ui.Email.Contains(request.SearchKey) || di.FullName.Contains(request.SearchKey))
                   .WhereIf(!string.IsNullOrEmpty(request.RoleCode), (ui, di) => msSql.Select<SysUserRole, SysRoleInfo>()
                       .LeftJoin((ur, ri) => ur.RoleId == ri.RoleId)
                       .Where((ur, ri) => ri.RoleCode == request.RoleCode)
                       .Any(o => o.t1.UserId == ui.UserId))
                   .WhereIf(!string.IsNullOrEmpty(request.DeptId), (ui, di) => di.DeptId == request.DeptId)
                   .Count(out long totalCount)
                   .Take(request.PageSize).Skip(request.PageSize * (request.PageIndex - 1))
                   .ToListAsync<GetUserInfoDto>();
            return new PageResult<GetUserInfoDto>()
            {
                Data = list,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };
        }
    }
}
