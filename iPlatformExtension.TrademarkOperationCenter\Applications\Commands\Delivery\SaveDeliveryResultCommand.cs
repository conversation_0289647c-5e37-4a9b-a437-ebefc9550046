﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

internal record SaveDeliveryResultCommand(string ProcId, DeliveryResultDto Result, string OperatorId, bool IsAuto)
    : IFreeSqlUnitOfWorkCommand<PlatformFreeSql>, IRequest<bool>;