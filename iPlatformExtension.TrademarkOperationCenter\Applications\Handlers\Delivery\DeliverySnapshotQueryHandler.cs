﻿using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DeliverySnapshotQueryHandler(IDeliveryInfoRepository deliveryInfoRepository, IMediator mediator)
    : IRequestHandler<DeliverySnapshotQuery, TrademarkDeliveryDto?>
{
    public async Task<TrademarkDeliveryDto?> Handle(DeliverySnapshotQuery request, CancellationToken cancellationToken)
    {
        
        //快照数据
        var deliveryInfo = await deliveryInfoRepository.Where(info => info.ProcId == request.ProcId).WithLock()
            .IncludeMany(deliInfo => deliInfo.Applicants)
            .IncludeMany(deliInfo => deliInfo.Priorities)
            .IncludeMany(deliInfo => deliInfo.NiceCategories)
            .IncludeMany(deliInfo => deliInfo.Files)
            .ToOneAsync(cancellationToken);

      

        return deliveryInfo is not null
            ? await mediator.Send(new DeliverySnapshotResultQuery(deliveryInfo), cancellationToken)
            : null;
    }
}