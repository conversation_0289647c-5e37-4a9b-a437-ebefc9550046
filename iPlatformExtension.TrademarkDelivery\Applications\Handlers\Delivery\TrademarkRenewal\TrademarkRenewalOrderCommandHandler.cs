﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery.TrademarkRenewal;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Exceptions;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Extensions;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery.TrademarkRenewal;

internal sealed class TrademarkRenewalOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IDeliveryInfoRepository deliveryInfoRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    IBaseCountryRepository countryRepository,
    IApplicantTypeRepository applicantTypeRepository,
    UnitOfWorkManager unitOfWorkManager,
    IOptionsMonitor<PhoenixClientOptions> optionsMonitor,
    ILoggerFactory loggerFactory,
    PhoenixClientFactory phoenixClientFactory)
    :
        PhoenixDeliveryHandleBase<TrademarkRenewalOrderCommand, TrademarkRenewalOrder,
            PhoenixResponseParameters<OrderData>>(stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository,
            unitOfWorkManager, loggerFactory, phoenixClientFactory),
        IDeliveryHandler<TrademarkRenewalOrderCommand, TrademarkRenewalOrder, PhoenixResponseParameters<OrderData>>
{
    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.StartupDelivery;

    public override async Task InitializeAsync(TrademarkRenewalOrderCommand command, CancellationToken cancellationToken)
    {
        var procId = command.ProcId;
        _deliveryInfo = await DeliveryInfoRepository.Select
            .IncludeMany(deliInfo => deliInfo.Applicants, 
                select => select.Where(applicant => applicant.IsRepresent == true))
            .IncludeMany(deliInfo => deliInfo.Files)
            .Where(info => info.ProcId == procId).ToOneAsync(cancellationToken);
        if (_deliveryInfo is null)
        {
            throw new NotFoundException(procId, "递交任务");
        }

        if (command.Version != _deliveryInfo.Version)
        {
            throw new VersionException(command.Version, _deliveryInfo.Version);
        }

        _phoenixClient = _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
    }

    public override async Task<TrademarkRenewalOrder> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
         ArgumentNullException.ThrowIfNull(_deliveryInfo);
         ArgumentNullException.ThrowIfNull(_deliveryInfo.OtherInfo);

         var clientOptions = optionsMonitor.Get(_deliveryInfo.DeliveryKey);
         
         var grandNumbers = _deliveryInfo.OtherInfo.TrademarkNiceClasses?.Split(';') ?? [];
        
        var currentApplicant = _deliveryInfo.Applicants!.Single(applicant => applicant.IsRepresent ?? false);

        var countryId = currentApplicant.CountryId ??
                        throw new PropertyMissingException(currentApplicant.ApplicantId, currentApplicant.CountryId, "申请人");
        var applicantBookType = ApplicantBookType.GetApplicantBookType(countryId);
        
        string country;
        if (applicantBookType == ApplicantBookType.Overseas)
        {
            var countryInfo = await countryRepository.GetCacheValueAsync(countryId, cancellationToken: cancellationToken);
            if (countryInfo is null)
            {
                throw new NotFoundException(countryId, "国家");
            }

            if (string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCode) || string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCountryName))
            {
                throw new ApplicationException("商标局没有对应的国家编码和名称");
            }

            country = $"{countryInfo.TrademarkDeliveryCode}  {countryInfo.TrademarkDeliveryCountryName}";
        }
        else
        {
            country = applicantBookType.Description;
        }

        var applicantType = await applicantTypeRepository.GetCacheValueAsync(currentApplicant.TypeId.GetOrDefaultEmpty());
        if (applicantType is null)
        {
            throw new ApplicantTypeMissingException(currentApplicant, "当前申请人");
        }
        
        var attachments = _deliveryInfo.Files?
                              .Where(file => int.TryParse(file.FileCode, out _) && file.BaseFileType is "申请人文件" or "递交官方")
                              .OrderBy(file => file.FileDesc)
                              .Select(file => 
                                  new ApplicantAttachment(
                                      file.FileName, 
                                      file.Url ?? string.Empty, 
                                      ApplicantAttachmentType.OfficialAttachment, 
                                      new ApplicantAttachmentSubType(int.Parse(file.FileCode!), file.FileDesc ?? string.Empty))) 
                          ?? Array.Empty<ApplicantAttachment>();

        var order = new TrademarkRenewalOrder
        {
            OrderToken = _deliveryInfo.ProcId,
            DeliveryApplicantInfo = new DeliveryApplicantInfo
            {
                ApplicantAddress = currentApplicant.AddressCn,
                ApplicantEnglishAddress = currentApplicant.AddressEn,
                ApplicantEnglishName = currentApplicant.ApplicantNameEn,
                ApplicantName = currentApplicant.ApplicantNameCn,
                BookType = applicantBookType.Code.ToString(),
                CertificatesType = currentApplicant.CardType.GetApplicantCertificationType(),
                Code = currentApplicant.Postcode,
                Country = country,
                IdCard = currentApplicant.CardNo,
                OwnerType = applicantType.GetOwnerType(),
                SubjectType = currentApplicant.GetSubjectType(),
                UnifiedSocialCreditCode = currentApplicant.CardNo,
                DomesticReceiverAddress = _deliveryInfo.ContactAddress,
                DomesticReceiverCode = _deliveryInfo.ContactPostCode,
                DomesticReceiverName = _deliveryInfo.AgencyName,
                DomesticReceiverEmail = _deliveryInfo.ContactMailBox
            },
            Attachments = attachments,
            BrandInfos = grandNumbers.Select(grandNumber => new BrandInfo()
            {
                BrandRegisterNo = _deliveryInfo.AppNo,
                FirstCgNo = grandNumber.PadLeft(2, '0')
            }),
            OrderInfo = new OrderInfo()
            {
                PrincipalName = _deliveryInfo.ContactPerson,
                PrincipalTel = _deliveryInfo.ContactTel,
                AgentOrganConName = _deliveryInfo.AgentUser ?? throw new PropertyMissingException(_deliveryInfo, _deliveryInfo.AgentUser, "递交任务", "官方代理人"),
                ContactEmail = _deliveryInfo.ContactMailBox,
                AgentOrganId = clientOptions.OrganizationId,
                AgentOrganName = clientOptions.UserName,
                ContactName = _deliveryInfo.ContactPerson,
                ContactTel = _deliveryInfo.ContactTel,
                DomesticReceiverAddress = _deliveryInfo.ContactAddress,
                DomesticReceiverCode = _deliveryInfo.ContactPostCode,
                DomesticReceiverName = _deliveryInfo.AgencyName
            }
        };

        return order;
    }

    public override Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(TrademarkRenewalOrder request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        _phoenixClient ??= _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
        return _phoenixClient.CreateOrderAsync(PhoenixUri.TrademarkRenewal, request);
    }

    public override Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        var orderNo = response.Data?.OrderNo!;
        const int newStatus = (int) DeliveryStatus.Ordered;
        
        return Handler.UpdateDeliveryInfoInternalAsync(deliveryInfo =>
        {
            deliveryInfo.Status = newStatus;
            deliveryInfo.OrderNo = orderNo;
            deliveryInfo.OperationResult = true;
            return ValueTask.CompletedTask;
        }, cancellationToken);
    }
    
    public async Task<bool> HandleExceptionAsync(Exception ex, CancellationToken cancellationToken = default)
    {
        await Handler.HandleExceptionInternalAsync(ex, true, false, cancellationToken);
        return false;
    }
}