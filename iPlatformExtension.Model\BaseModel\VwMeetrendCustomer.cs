using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_meetrend_Customer", DisableSyncStructure = true)]
	public partial class VwMeetrendCustomer {

		[ Column(StringLength = 50)]
		public string FAREAID { get; set; }

		[ Column(StringLength = 50)]
		public string FCOMPANYID { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string FID { get; set; }

		[ Column(StringLength = 50)]
		public string FINDUSTRY { get; set; }

		[ Column(StringLength = 2000)]
		public string FNAME { get; set; }

		[ Column(StringLength = 2000)]
		public string FULLANME { get; set; }

		[ Column(StringLength = 50)]
		public string FUNITID { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string PARENTID { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

	}

}
