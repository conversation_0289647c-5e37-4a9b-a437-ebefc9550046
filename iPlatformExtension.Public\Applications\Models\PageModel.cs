﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.Public.Applications.Models
{
    /// <summary>
    /// 分页
    /// </summary>
    public record PageModel()
    {
        /// <summary>
        /// 分页大小
        /// </summary>
        public int? PageSize { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        [Range(1, 99999, ErrorMessage = "页码大小范围[1,99999]")]
        public int? PageIndex { get; set; }
    }
}
