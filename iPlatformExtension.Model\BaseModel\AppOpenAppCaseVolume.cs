using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_open_app_case_volume", DisableSyncStructure = true)]
	public partial class AppOpenAppCaseVolume {

		[ Column(Name = "app_obj_id", StringLength = 50)]
		public string AppObjId { get; set; }

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "item_id", StringLength = 50)]
		public string ItemId { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "real_case_id", StringLength = 50)]
		public string RealCaseId { get; set; }

		[ Column(Name = "volume", StringLength = 50, IsNullable = false)]
		public string Volume { get; set; }

	}

}
