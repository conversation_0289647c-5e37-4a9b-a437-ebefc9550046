﻿using System.IdentityModel.Tokens.Jwt;
using Microsoft.AspNetCore.Authentication;
using Microsoft.IdentityModel.Tokens;

namespace iPlatformExtension.Common.Authentication.BladeAuth;

public class BladeAuthOptions : AuthenticationSchemeOptions
{
    public const string SchemeName = "BladeAuth";
    
    private readonly JwtSecurityTokenHandler _defaultHandler = new ();

    public BladeAuthOptions()
    {
        SecurityTokenValidators = new List<ISecurityTokenValidator> { _defaultHandler };
    }

    public IList<ISecurityTokenValidator> SecurityTokenValidators { get; }

    public new BladeAuthEvents Events
    {
        get => (BladeAuthEvents) (base.Events ??= new BladeAuthEvents());
        set => base.Events = value;
    }
    
    public TokenValidationParameters TokenValidationParameters { get; set; } = new ();
    
    public bool SaveToken { get; set; } = true;

    /// <summary>
    /// Blade X token对应的头部名称
    /// </summary>
    public string TokenHeaderName { get; set; } = "blade-auth";
}