﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class WinningRewardRuleRepository(
    IFreeSql<PlatformFreeSql> freeSql, UnitOfWorkManage<PlatformFreeSql> uowManger)
    : DefaultRepository<WinningRewardRule, int>(freeSql, uowManger), IWinningRewardRuleRepository;