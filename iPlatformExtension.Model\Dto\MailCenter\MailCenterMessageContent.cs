﻿using iPlatformExtension.Model.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto.MailCenter
{
    /// <summary>
    /// 阅读人事件消息
    /// </summary>
    public class MailCenterMessageContent
    {


        /// <summary>
        /// 邮件ID
        /// </summary>
        public string MailId { get; set; }

        /// <summary>
        /// 收件编号
        /// </summary>
        public string MailNo { get; set; }
        /// <summary>
        /// 邮件主题
        /// </summary>
        public string MailTitle { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public string OperatorUser { get; set; }


        /// <summary>
        /// 时间
        /// </summary>
        public string DateTime { get; set; }

        /// <summary>
        /// 接收人
        /// </summary>
        public string RecipientBy { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public OperationTypeEnum OperationType { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 承办人
        /// </summary>
        public string Undertaker { get; set; }

        /// <summary>
        /// 发件人
        /// </summary>
        public string SentMail { get; set; }
    }
}
