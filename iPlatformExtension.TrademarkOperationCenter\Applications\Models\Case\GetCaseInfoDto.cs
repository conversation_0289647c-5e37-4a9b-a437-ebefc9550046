﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Case;


/// <summary>
/// 案件列表Dto
/// </summary>
/// <param name="Id">主键</param>
/// <param name="Volume">我方文号</param>
/// <param name="CaseName">商标名称</param>
/// <param name="CaseDirection">案件流向</param>
/// <param name="AppNo">申请号</param>
/// <param name="TrademarkClass">国际分类</param>
/// <param name="ApplicantRepresentative">申请人代表</param>
/// <param name="CustomerName">客户名称</param>
/// <param name="CreateTime">创建时间</param>
public record GetCaseInfoDto(string Id, string Volume, string CaseName, string CaseDirection, string AppNo, string TrademarkClass, string ApplicantRepresentative, string CustomerName,DateTime? CreateTime);

