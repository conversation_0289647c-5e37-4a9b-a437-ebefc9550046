using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "con_batch_list", DisableSyncStructure = true)]
	public partial class ConBatchList {

		[ Column(Name = "batch_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string BatchId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		[ Column(Name = "auto_mail")]
		public bool AutoMail { get; set; } = false;

		[ Column(Name = "batch_no", StringLength = 50)]
		public string BatchNo { get; set; }

		[ Column(Name = "batch_status", StringLength = 50)]
		public string BatchStatus { get; set; }

		[ Column(Name = "case_direction", StringLength = 50, IsNullable = false)]
		public string CaseDirection { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "mail_id", StringLength = 50)]
		public string MailId { get; set; }

		[ Column(Name = "notice_code", StringLength = 50)]
		public string NoticeCode { get; set; }

		[ Column(Name = "notice_name", StringLength = 50, IsNullable = false)]
		public string NoticeName { get; set; }

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		[ Column(Name = "setting_id", StringLength = 50)]
		public string SettingId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
