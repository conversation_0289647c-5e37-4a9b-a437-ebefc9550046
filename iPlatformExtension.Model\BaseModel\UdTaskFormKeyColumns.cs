using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ud_task_form_key_columns", DisableSyncStructure = true)]
	public partial class UdTaskFormKeyColumns {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_id", StringLength = 50)]
		public string ColumnId { get; set; }

		[ Column(Name = "key_type", StringLength = 50)]
		public string KeyType { get; set; }

		[ Column(Name = "task_form_code", StringLength = 50)]
		public string TaskFormCode { get; set; }

	}

}
