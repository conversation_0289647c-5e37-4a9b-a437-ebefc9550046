﻿FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
ENV TZ=Asia/Shanghai
ENV ASPNETCORE_URLS=http://+:5164;https://+:7295
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_Kestrel__Certificates__Default__Path=/https/cert.crt
ENV ASPNETCORE_Kestrel__Certificates__Default__KeyPath=/https/rsa_private.key
ENV ASPNETCORE_HTTP_PORTS=5164
ENV ASPNETCORE_HTTPS_PORTS=7295
ENV DOTNET_GCHeapHardLimit=80000000
ENV DOTNET_DbgEnableMiniDump=1
ENV DOTNET_DbgMiniDumpTyp=4
ENV DOTNET_DbgMiniDumpName=/tmp/iplatform-mail-%p-%t.dmp
ENV DOTNET_EnableCrashReport=1
ENV DOTNET_CreateDumpDiagnostics=1
WORKDIR /app
EXPOSE 5164
EXPOSE 7295

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["iPlatformExtension.Mail/iPlatformExtension.Mail.csproj", "iPlatformExtension.Mail/"]
COPY ["iPlatformExtension.Repository/iPlatformExtension.Repository.csproj", "iPlatformExtension.Repository/"]
COPY ["iPlatformExtension.Common/iPlatformExtension.Common.csproj", "iPlatformExtension.Common/"]
COPY ["iPlatformExtension.Model/iPlatformExtension.Model.csproj", "iPlatformExtension.Model/"]
RUN dotnet restore "iPlatformExtension.Mail/iPlatformExtension.Mail.csproj"
COPY . .
WORKDIR "/src/iPlatformExtension.Mail"
RUN dotnet build "iPlatformExtension.Mail.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "iPlatformExtension.Mail.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "iPlatformExtension.Mail.dll"]
