using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_further_type", DisableSyncStructure = true)]
	public partial class AppFurtherType {

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "further_information_id", StringLength = 50)]
		public string FurtherInformationId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "type_id", StringLength = 50)]
		public string TypeId { get; set; }

	}

}
