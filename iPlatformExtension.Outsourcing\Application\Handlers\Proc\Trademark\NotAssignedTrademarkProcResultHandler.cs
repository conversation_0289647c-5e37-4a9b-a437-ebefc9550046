﻿using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc.Trademark;

internal sealed class NotAssignedTrademarkProcResultHandler(
    IBaseCountryRepository countryRepository,
    IBaseCtrlProcRepository ctrlProcRepository,
    ICustomerRepository customerRepository,
    ISystemDictionaryRepository dictionaryRepository
    ) : INotificationHandler<NotAssignedTrademarkProcResultNotification>
{
    public async Task Handle(NotAssignedTrademarkProcResultNotification notification, CancellationToken cancellationToken)
    {
        var results = notification.NotAssignedProcResults;
        foreach (var notAssignedProcDto in results)
        {
            notAssignedProcDto.ProcName =
                await ctrlProcRepository.GetTextValueAsync(notAssignedProcDto.ProcName) ?? string.Empty;
            notAssignedProcDto.Country = await countryRepository.GetTextValueAsync(notAssignedProcDto.Country) ?? string.Empty;
            notAssignedProcDto.CustomerName = await customerRepository.GetTextValueAsync(notAssignedProcDto.CustomerId) ?? string.Empty;

            notAssignedProcDto.ProcMark =
                await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.CtrlProcMark,
                    notAssignedProcDto.ProcMark);
            notAssignedProcDto.ProcStatus =
                await dictionaryRepository.GetChineseValueAsync(SystemDictionaryName.ProcStatus,
                    notAssignedProcDto.ProcStatus);
        }
    }
}