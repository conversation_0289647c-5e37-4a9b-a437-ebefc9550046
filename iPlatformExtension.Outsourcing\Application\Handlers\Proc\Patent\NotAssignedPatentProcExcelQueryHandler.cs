﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc.Patent;

internal sealed class NotAssignedPatentProcExcelQueryHandler(
    IPublisher publisher,
    IFreeSql<PlatformFreeSql> freeSql,
    NotAssignedProcQueryContextFactory contextFactory) 
    : IRequestHandler<NotAssignedPatentProcExcelQuery, IEnumerable<NotAssignedPatentProcExportDto>>
{
    public async Task<IEnumerable<NotAssignedPatentProcExportDto>> Handle(NotAssignedPatentProcExcelQuery request, CancellationToken cancellationToken)
    {
        var (authorizationType, keyword) = request;

        var context = await contextFactory.CreateAsync(authorizationType, cancellationToken);

        if (context is null)
        {
            return [];
        }

        await publisher.Publish(context, cancellationToken);

        var caseIdSet = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        if (!string.IsNullOrWhiteSpace(keyword))
        {
            caseIdSet.UnionWith(await freeSql.Select<CaseInfo>().WithLock()
                .Where(info => info.Volume.StartsWith(keyword))
                .ToListAsync(info => info.Id, cancellationToken));
            caseIdSet.UnionWith(await freeSql.Select<CaseInfo>().WithLock()
                .Where(info => info.CaseName.StartsWith(keyword))
                .ToListAsync(info => info.Id, cancellationToken));
            caseIdSet.UnionWith(await freeSql.Select<CaseExtendInfo>().WithLock()
                .Where(info => info.PctAppNo!.StartsWith(keyword))
                .ToListAsync(info => info.CaseId, cancellationToken));
            caseIdSet.UnionWith(await freeSql.Select<CaseInfo>().WithLock()
                .Where(info => info.Customer.CustomerFullName.Contains(keyword))
                .ToListAsync(info => info.Id, cancellationToken));
            caseIdSet.UnionWith(await freeSql.Select<CaseInfo>().WithLock()
                .Where(info => info.Country.CountryZhCn.Contains(keyword))
                .ToListAsync(info => info.Id, cancellationToken));
        }
        
        var ctrlProcIdSet = context.CtrlProcIds;
        var ctrlProcIds = await freeSql.Select<BasCtrlProc>()
            .Where(basCtrlProc => basCtrlProc.CtrlProcZhCn.Contains(keyword!))
            .ToListAsync(basCtrlProc => basCtrlProc.CtrlProcId, cancellationToken);
        ctrlProcIdSet.IntersectWith(ctrlProcIds);
        
        var results = await context.Query
            .WhereIf(!string.IsNullOrWhiteSpace(keyword) && caseIdSet.Count <= 0, info => ctrlProcIdSet.Contains(info.CtrlProcId))
            .WhereIf(caseIdSet.Count > 0, info => caseIdSet.Contains(info.CaseId))
            .ToListAsync(info => new NotAssignedPatentProcExportDto
            {
                Volume = info.CaseInfo.Volume,
                CaseName = info.CaseInfo.CaseName,
                CustomerId = info.CaseInfo.CustomerId,
                Country = info.CaseInfo.CountryId ?? string.Empty,
                ProcName = info.CtrlProcId,
                ApplyType = info.CaseInfo.ApplyTypeId ?? string.Empty,
                OfficialDeadline = info.LegalDueDate,
                CaseDirection = info.CaseInfo.CaseDirection,
                PctAppNo = info.CaseInfo.ExtendInfo!.PctAppNo ?? string.Empty
            }, cancellationToken);
            
        await publisher.Publish(new NotAssignedPatentProcExportResultNotification(results), cancellationToken);

        return results;
    }
}
