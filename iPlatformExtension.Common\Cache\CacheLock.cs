﻿using System.Diagnostics;

namespace iPlatformExtension.Common.Cache;

public sealed class CacheLock<T>
{
    private uint _locked;
    
    public CacheLock()
    {
    }

    public bool Locked => _locked > 0;

    public bool TryLock()
    {
        var originValue = Interlocked.CompareExchange(ref _locked, 1, 0);
        Debug.Assert(_locked != originValue, "原值与更新后的值一样");
        return _locked != originValue;
    }

    public bool ReleaseLock()
    {
        var originValue = Interlocked.CompareExchange(ref _locked, 0, 1);
        Debug.Assert(_locked != originValue, "原值与更新后的值一样");
        return _locked != originValue;
    }
}