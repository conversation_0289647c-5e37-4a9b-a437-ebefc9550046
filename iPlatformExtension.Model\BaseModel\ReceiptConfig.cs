using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "receipt_config", DisableSyncStructure = true)]
	public partial class ReceiptConfig {

		[ Column(Name = "config_id", StringLength = 50, IsNullable = false)]
		public string ConfigId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "login_name", StringLength = 100, IsNullable = false)]
		public string LoginName { get; set; }

		[ Column(Name = "login_pwd", StringLength = 50, IsNullable = false)]
		public string LoginPwd { get; set; }

		[ Column(Name = "login_user", StringLength = 50, IsNullable = false)]
		public string LoginUser { get; set; }

	}

}
