﻿using System.Security.Claims;
using iPlatformExtension.Common.Exceptions;

namespace iPlatformExtension.Common.Extensions;

public static class HttpContextExtension
{
    public static string GetUserId(this ClaimsPrincipal? principal)
    {
        return principal?.FindFirstValue(ClaimTypes.NameIdentifier) ?? throw new NotAuthenticatedException();
    }

    public static IEnumerable<string?> GetRoleCodes(this ClaimsPrincipal? principal)
    {
        return principal?.FindAll("RoleCode").Select(x => x.Value) ?? [];
    }
}