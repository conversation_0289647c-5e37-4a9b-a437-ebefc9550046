using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_flow_private", DisableSyncStructure = true)]
	public partial class SysFlowPrivate {

		[ Column(Name = "private_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string PrivateId { get; set; }

		[ Column(Name = "flow_type", StringLength = 50)]
		public string FlowType { get; set; }

		[ Column(Name = "is_show")]
		public bool? IsShow { get; set; } = false;

		[ Column(Name = "private_name", StringLength = 50)]
		public string PrivateName { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

		[ Column(Name = "flow_sub_type", StringLength = 50)]
		public string FlowSubType { get; set; }

	}

}
