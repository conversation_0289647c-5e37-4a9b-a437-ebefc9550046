﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;

internal sealed record StartupDeliveryValidateByTCommand(IEnumerable<string> ProcIds,string FlowSubType="T") : IRequest<BatchDeliveryValidateResult>;