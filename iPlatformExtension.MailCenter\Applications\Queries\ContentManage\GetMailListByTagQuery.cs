﻿using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.ContentManage
{
    public class GetMailListByTagQuery : QueryBase, IRequest<PageResult<MailListByTagDto>>
    { 
        /// <summary>
        /// 标签ID
        /// </summary>
        public string? TagId { get; set; }

        /// <summary>
        /// 模糊查询:收件编号/收件主题/发件人
        /// </summary>
        public string? Search { get; set; }

        /// <summary>
        /// 邮件类型:Receive:收件标签,Send:发件标签.
        /// </summary>
        public string MailType { get; set; } = SysEnum.MailType.Receive.ToString();
    }
}
