﻿namespace iPlatformExtension.Public.Applications.Models.MyTrademarkCase;

/// <summary>
/// 我的商标列表
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="CtrlProcZhCn">任务名称</param>
/// <param name="ProcStatusId">任务状态</param>
/// <param name="CaseName">商标名称</param>
/// <param name="ApplicantNameCn">申请人</param>
/// <param name="AppNo">申请</param>
/// <param name="RegisterNo">注册号</param>
/// <param name="Volume">我方文号</param>
/// <param name="CountryZhCn">国家/地区</param>
/// <param name="TrademarkClass">国际分类</param>
/// <param name="OfficialStatusId">官方状态id</param>
/// <param name="EntrustDate">委案日期</param>
/// <param name="ProcAppDate">申请日</param>
/// <param name="LegalDueDate">官方期限</param>
/// <param name="DurationOutpost">指示外所期限</param>
/// <param name="DurationCustomer">反馈客户期限</param>
/// <param name="FeedbackDay">反馈日</param>
/// <param name="InstructDay">指示外所日</param>
/// <param name="VoucherDay">外所提供凭证日</param>
/// <param name="NotificationReceiptDate">通知书收文日</param>
/// <param name="CaseId">案件id</param>
/// <param name="ProcStatusName">任务状态名称</param>
/// <param name="SubProcStatusId">代理人状态</param>
/// <param name="FinishDate">完成日</param>
/// <param name="SubProcStatusCn">代理人处理状态中文</param>
/// <param name="DeadlineDocuments">后补文件期限</param>
/// <param name="CaseFollowDeadline">案件跟进期限</param>
/// <param name="Version">版本号</param>
/// <param name="ProcNo">任务编号</param>
/// <param name="CustomerName">客户名称</param>
/// <param name="CustomerId">客户id</param>
///
public record GetMyTrademarkCaseListDto(
    string ProcId,
    string CtrlProcZhCn,
    string ProcStatusId,
    string CaseName,
    string ApplicantNameCn,
    string AppNo,
    string RegisterNo,
    string Volume,
    string CountryZhCn,
    string TrademarkClass,
    string OfficialStatusId,
    DateTime? EntrustDate,
    DateTime? ProcAppDate,
    DateTime? LegalDueDate,
    DateTime? DurationOutpost,
    DateTime? DurationCustomer,
    DateTime? FeedbackDay,
    DateTime? InstructDay,
    DateTime? VoucherDay,
    DateTime? NotificationReceiptDate,
    string CaseId,
    string SubProcStatusId,
    DateTime? FinishDate,
    string? SubProcStatusCn,
    string? DeadlineDocuments,
    string? CaseFollowDeadline,
    string? ProcNo,
    string? CustomerName,
    string? CustomerId,
    int Version
    )
{
    public string? ProcStatusName { get; set; }
};
