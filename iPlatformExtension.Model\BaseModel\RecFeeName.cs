using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rec_fee_name", DisableSyncStructure = true)]
	public partial class RecFeeName {

		[ Column(Name = "name_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string NameId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "invoice_name", StringLength = 200, IsNullable = false)]
		public string InvoiceName { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "short_name", StringLength = 50, IsNullable = false)]
		public string ShortName { get; set; }

	}

}
