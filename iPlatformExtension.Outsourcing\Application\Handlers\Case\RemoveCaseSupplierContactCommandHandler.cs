﻿using iPlatformExtension.Outsourcing.Application.Commands.Case;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Case;

internal sealed class RemoveCaseSupplierContactCommandHandler(ICaseForeignContactRepository caseForeignContactRepository)
    : IRequestHandler<RemoveCaseSupplierContactCommand>
{
    public Task Handle(RemoveCaseSupplierContactCommand request, CancellationToken cancellationToken)
    {
        var (caseId, contactId) = request;
        return caseForeignContactRepository.DeleteAsync(list => list.CaseId == caseId && list.ContactId == contactId, cancellationToken);
    }
}