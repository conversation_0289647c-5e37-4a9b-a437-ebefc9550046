﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class SupplierProcCountQueryHandler(
    IFreeSql<PlatformFreeSql> freeSql,
    IBaseCountryRepository countryRepository,
    IBaseCtrlProcRepository ctrlProcRepository,
    IForeignSupplierRepository foreignSupplierRepository) : 
    IRequestHandler<SupplierProcCountQuery, IEnumerable<SupplierTrademarkProcCountDto>>
{
    
    private static readonly IEnumerable<string> caseDirections = [CaseDirection.IO, CaseDirection.OO];
    
    public async Task<IEnumerable<SupplierTrademarkProcCountDto>> Handle(SupplierProcCountQuery request, CancellationToken cancellationToken)
    {
        var (customerId, ctrlProcId, countries) = request;
        
        var counts = await freeSql.Select<CaseProcInfo>().WithLock()
            .Where(procInfo => procInfo.CtrlProcId == ctrlProcId)
            .Where(procInfo => procInfo.CaseInfo.CustomerId == customerId)
            .Where(procInfo => procInfo.CaseInfo.CaseTypeId == CaseType.Trade)
            .Where(procInfo => procInfo.CaseInfo.CountryId != null)
            .WhereIf(countries.Any(), procInfo => countries.Contains(procInfo.CaseInfo.CountryId!))
            .Where(procInfo => caseDirections.Contains(procInfo.CaseInfo.CaseDirection))
            .Where(procInfo => !string.IsNullOrEmpty(procInfo.ForeginAgencyId))
            .Where(procInfo => procInfo.EntrustDate != null)
            .GroupBy(procInfo => new SupplierTrademarkProcCountDto
            {
                SupplierId = procInfo.ForeginAgencyId!,
                CountryName = procInfo.CaseInfo.CountryId!,
                ProcName = procInfo.CtrlProcId
            }).ToListAsync(aggregate => new SupplierTrademarkProcCountDto
            {
                SupplierId = aggregate.Key.SupplierId,
                Count = aggregate.Count(),
                ProcName = aggregate.Key.ProcName,
                CountryName = aggregate.Key.CountryName,
                LatestUsedTime = aggregate.Max(aggregate.Value.EntrustDate!.Value)
            }, cancellationToken);
        
        foreach (var supplierTrademarkProcCountDto in counts)
        {
            supplierTrademarkProcCountDto.SupplierName = await foreignSupplierRepository.GetTextValueAsync(supplierTrademarkProcCountDto.SupplierId) ?? string.Empty;
            supplierTrademarkProcCountDto.ProcName = await ctrlProcRepository.GetTextValueAsync(supplierTrademarkProcCountDto.ProcName) ?? string.Empty;
            supplierTrademarkProcCountDto.CountryName = await countryRepository.GetTextValueAsync(supplierTrademarkProcCountDto.CountryName) ?? string.Empty;
        }
        
        return counts;
    }
}