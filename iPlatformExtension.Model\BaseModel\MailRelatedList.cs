using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_related_list", DisableSyncStructure = true)]
	public partial class MailRelatedList {

		[ Column(Name = "related_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string RelatedId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "mail_id", StringLength = 50)]
		public string MailId { get; set; }

		[ Column(Name = "related_form_id", StringLength = 50)]
		public string RelatedFormId { get; set; }

		[ Column(Name = "related_form_type", StringLength = 50)]
		public string RelatedFormType { get; set; }

	}

}
