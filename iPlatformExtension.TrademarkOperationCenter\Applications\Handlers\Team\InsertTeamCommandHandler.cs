﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Helper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;
using MediatR;
using Microsoft.AspNetCore.Http.HttpResults;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 创建团队
    /// </summary>
    public class InsertTeamCommandHandler(
        IFreeSql freeSql,
        IMediator mediator,
        IHttpContextAccessor content,
        IMapper mapper,
        ISysTeamRepository sysTeamRepository)
        : IRequestHandler<InsertTeamCommand>
    {
        public async Task Handle(InsertTeamCommand request, CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
            {
                return;
            }

            if (await freeSql.Select<SysTeam>()
                    .Where(it => it.TeamName == request.TeamName && it.Enable == SystemTeamStatus.Normal.GetHashCode())
                    .WithLock().AnyAsync(cancellationToken))
            {
                throw new ApplicationException("团队名字已有重复");
            }

            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userId);

            var sysTeam = mapper.Map<SysTeam>(request);
            sysTeam.TeamId = TimestampAndRandom.GenerateId();
            sysTeam.CreateTime = DateTime.Now;
            sysTeam.CreateUserId = userId;
            sysTeam.Enable = SystemTeamStatus.Normal.GetHashCode();
            sysTeam.AuthorizeUser = request.AuthorizeUser ?? "";
            sysTeam.Seq = request.Seq;
            await sysTeamRepository.InsertAsync(sysTeam, cancellationToken);
            //添加成员
            if (request.Members is not null && request.Members.Any())
            {
                await mediator.Send(new EditTeamMemberCommand(sysTeam.TeamId, request.Members));
            }

            //添加客户
            if (request.CustomerId is not null && request.CustomerId.Any() && request.IsEffect)
            {
                await mediator.Send(new EditCustomerCommand(request.CustomerId, sysTeam.TeamId));
            }
        }
    }
}