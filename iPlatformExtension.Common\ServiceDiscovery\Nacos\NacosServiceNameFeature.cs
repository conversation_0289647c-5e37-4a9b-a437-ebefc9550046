﻿using Microsoft.Extensions.ServiceDiscovery;

namespace iPlatformExtension.Common.ServiceDiscovery.Nacos;

public class NacosServiceNameFeature(string groupName, string serviceName, string ipAddress, string scheme) : IHostNameFeature
{
    public string HostName { get; } = new NacosClusterId(serviceName, groupName).GetServiceEndpointUri(scheme).Host;

    public string GroupName { get; } = groupName;

    public string ServiceName { get; } = serviceName;
    
    public string IpAddress { get; } = ipAddress;

    public string Scheme { get; } = scheme;
}