﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 申请人信息
/// </summary>
/// <param name="ApplicantId">申请人id</param>
/// <param name="ApplicantCnName">申请人中文名</param>
/// <param name="ApplicantEnName">申请人英文名</param>
public record struct ApplicantInfo(string ApplicantId, string ApplicantCnName, string ApplicantEnName)
{
    /// <summary>
    /// 是否代表
    /// </summary>
    public bool IsRepresent { get; set; } = false;
}