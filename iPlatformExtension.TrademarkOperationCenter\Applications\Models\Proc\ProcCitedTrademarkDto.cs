﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;

/// <summary>
/// 引证商标信息
/// </summary>
public class ProcCitedTrademarkDto
{
    /// <summary>
    /// 注册号
    /// </summary>
    [Required]
    public string RegisterNumber { get; set; } = string.Empty;

    /// <summary>
    /// 尼斯分类类别
    /// </summary>
    [Required]
    public string TrademarkClasses { get; set; } = string.Empty;

    /// <summary>
    /// 联系人名称
    /// </summary>
    public string Contactor { get; set; } = string.Empty;

    /// <summary>
    /// 联系人地址
    /// </summary>
    public string ContactAddress { get; set; } = string.Empty;

    /// <summary>
    /// 任务id
    /// </summary>
    [Required]
    public string ProcId { get; set; } = string.Empty;
}