﻿using System.Collections.Frozen;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core.Utils;
using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Commission.Grpc;
using iPlatformExtension.Commission.Infrastructure.Logging;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Messages;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class PushDomesticRewardCommandHandler(
    ILogger<PushDomesticRewardCommandHandler> logger,
    IWinningRewardProcRepository rewardProcRepository, 
    CommissionService.CommissionServiceClient client) 
    : IRequestHandler<PushDomesticRewardCommand>
{
    public async Task Handle(PushDomesticRewardCommand request, CancellationToken cancellationToken)
    {
        var (userId, deptIds, year, month) = request;
        var deptIdsSet = deptIds.ToFrozenSet(StringComparer.OrdinalIgnoreCase);

        var rewardRecords = await rewardProcRepository.Where(proc => proc.Pushed == false)
            .Where(proc => proc.Year == year && proc.Month == month)
            .ToListAsync(cancellationToken);

        var context = client.PushCommissionRewards(cancellationToken:cancellationToken);
        var requestStream = context.RequestStream;
        var responseStream = context.ResponseStream;

        var responseTask = Task.Run(() => responseStream.ForEachAsync(HandleResultAsync), cancellationToken);
        
        foreach (var proc in rewardRecords)
        {

            var commissionReward = new CommissionReward
            {
                Year = proc.Year,
                Month = proc.Month,
                Volume = proc.Volume,
                ProcNo = proc.ProcNo,
                CustomerName = proc.CustomerName,
                CaseDirection = proc.CaseDirection,
                ProcName = proc.ProcName,
                CommissionDate =
                    Timestamp.FromDateTime(TimeZoneInfo.ConvertTimeToUtc(proc.CommissionDate, TimeZoneInfo.Local)),
                ProcId = proc.ProcId,
                CustomerId = proc.CustomerId,
                CaseType = proc.CaseType,
            };
                
            commissionReward.Rewards.AddRange(proc.Beneficiaries.Values.Where(beneficiary => beneficiary.UserId == userId || deptIdsSet.Contains(beneficiary.DeptId)).Select(beneficiary => new UserReward
            {
                Username = beneficiary.UserName,
                CnName = beneficiary.CnName,
                Reward = (beneficiary.EditedReward ?? beneficiary.Reward).ToString("F2"),
                DeptName = beneficiary.DeptName,
                DeptId = beneficiary.DeptId,
                DistrictCode = beneficiary.DistrictCode,
                DistrictName = beneficiary.DistrictName
            }));

            if (commissionReward.Rewards.Count > 0)
            {
                await requestStream.WriteAsync(commissionReward, cancellationToken);
            }
        }

        await requestStream.CompleteAsync();
        await responseTask;
    }

    internal async Task HandleResultAsync(MessageResult messageResult)
    {
        logger.LogMessageResult(messageResult);
        var procId = messageResult.Data.UnpackToString();

        try
        {
            if (messageResult.Success)
            {
                var proc = await rewardProcRepository.Where(proc => proc.ProcId == procId).ToOneAsync();
                proc.Pushed = true;
                proc.UpdateTime = DateTime.Now;
                proc.Updater = UserIds.Administrator;
                
                await rewardProcRepository.UpdateAsync(proc);
            }
            else
            {
                logger.LogDomesticRewardsPushedFail(procId, messageResult.Message);
            }
            
        }
        catch (Exception e)
        {
            logger.LogDomesticRewardResponseError(procId, e);
        }
    }
}