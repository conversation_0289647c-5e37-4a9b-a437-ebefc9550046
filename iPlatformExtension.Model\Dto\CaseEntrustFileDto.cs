﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 委案接口文件DTO
/// </summary>
public sealed class CaseEntrustFileDto
{
    /// <summary>
    /// 文件id
    /// </summary>
    public string FileId { get; set; } = null!;

    /// <summary>
    /// 对应的案件号
    /// </summary>
    [JsonPropertyName("objId")]
    public string ObjectId { get; set; } = null!;

    /// <summary>
    /// 产品id
    /// </summary>
    public string ProductId { get; set; } = null!;

    /// <summary>
    /// 文件描述id
    /// </summary>
    public string FileDescId { get; set; } = null!;

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = null!;

    /// <summary>
    /// 文件下载路径
    /// </summary>
    public string FilePath { get; set; } = null!;

    /// <summary>
    /// 旧系统的案件id
    /// </summary>
    [JsonIgnore]
    public string? CaseId { get; set; }

}