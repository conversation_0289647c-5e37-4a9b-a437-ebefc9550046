using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_proc_examine_update", DisableSyncStructure = true)]
	public partial class CaseProcExamineUpdate {

		[ Column(StringLength = 50, IsNullable = false)]
		public string 承办人 { get; set; }

		[ Column(StringLength = 50)]
		public string 初核人 { get; set; }

		[ Column(Name = "初核人_ID", StringLength = 50)]
		public string 初核人ID { get; set; }

		[ Column(StringLength = 2000)]
		public string 错误描述 { get; set; }

		[ Column(StringLength = 500)]
		public string 核稿部门 { get; set; }

		
		public DateTime? 核稿完成时间 { get; set; }

		[ Column(StringLength = 50)]
		public string 任务标识 { get; set; }

		[ Column(StringLength = 50)]
		public string 任务名称 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 任务属性 { get; set; }

		[ Column(StringLength = 50)]
		public string 申请类型 { get; set; }

		[ Column(DbType = "varchar(1)", IsNullable = false)]
		public string 审核次数 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 审核人 { get; set; }

		[ Column(StringLength = 50)]
		public string 提成比例 { get; set; }

		[ Column(StringLength = 50)]
		public string 外派处理人 { get; set; }

		[ Column(StringLength = 50)]
		public string 我方文号 { get; set; }

		
		public int? 形式错误数 { get; set; }

		[ Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
		public string ProcId { get; set; }

	}

}
