﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Queries.Supplier;
using MediatR;
using Microsoft.Data.SqlClient;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Supplier;

internal sealed class SupplierInputCaseInfoCountQueryHandler(IFreeSql<PlatformFreeSql> freeSql) : IRequestHandler<SupplierInputCaseInfoCountQuery, long>
{
    public async Task<long> Handle(SupplierInputCaseInfoCountQuery request, CancellationToken cancellationToken)
    {
        
        var (manageCompanyId, caseDirections, ctrlProcIds, applicationTypeIds,(startDate, endDate), customerId) = request;

        var query = freeSql.Select<CaseProcInfo>().WithLock()
            .InnerJoin<CaseInfo>((info, caseInfo) => info.CaseId == caseInfo.Id)
            .Where(info => ctrlProcIds.Contains(info.CtrlProcId))
            .Where<CaseInfo>(caseInfo => caseInfo.CustomerId == customerId)
            .Where<CaseInfo>(caseInfo => caseDirections.Contains(caseInfo.CaseDirection))
            .Where<CaseInfo>(caseInfo => caseInfo.ManageCompany == manageCompanyId)
            .Where<CaseInfo>(caseInfo => applicationTypeIds.Contains(caseInfo.ApplyTypeId))
            .WhereIf<CaseProcInfo, CaseInfo>(startDate != null,
                (info, caseInfo) => caseInfo.CreateTime >= startDate!.Value.ToDateTime(TimeOnly.MinValue))
            .WhereIf<CaseProcInfo, CaseInfo>(endDate != null,
                (info, caseInfo) => info.CreateTime <= endDate!.Value.ToDateTime(TimeOnly.MaxValue))
            .GroupBy(info => info.CaseId);
        
        var count = await query.CountAsync(cancellationToken);

        // var count = list.Count;

        return count;
    }
}