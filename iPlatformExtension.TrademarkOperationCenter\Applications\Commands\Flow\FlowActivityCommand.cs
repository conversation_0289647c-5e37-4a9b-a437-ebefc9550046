﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow
{
    public record class FlowActivityCommand(FlowInfo FlowInfo, UserBaseInfo CurrentUser) 
        : IRequest<SysFlowActivity>, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;
}
