﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;

namespace iPlatformExtension.MailCenterRepository.Implement
{

    internal class MailReaderListRepository(
        IFreeSql<MailCenterFreeSql> fsql,
        UnitOfWorkManage<MailCenterFreeSql> manager)
        : DefaultRepository<MailReaderList, string>(fsql, manager), IMailReaderListRepository;

}
