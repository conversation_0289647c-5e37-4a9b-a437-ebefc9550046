﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Service.Interface;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Finance.Controllers;

/// <summary>
/// 账单控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public sealed class BillController : ControllerBase
{
    private readonly IBillQueryService _billQueryService;

    /// <summary>
    /// 构造函数注入
    /// </summary>
    /// <param name="billQueryService">账单查询服务</param>
    public BillController(IBillQueryService billQueryService)
    {
        _billQueryService = billQueryService;
    }

    /// <summary>
    /// 到款匹配
    /// 匹配到款处理人
    /// </summary>
    /// <param name="payerName">付款人姓名。由金蝶返回</param>
    /// <returns>匹配结果。匹配成功返回处理人，否则就进入待处理池</returns>
    [HttpGet("/api/bill/receivedHandlers")]
    public async ValueTask<ReceivedBillHandlerMatchResult> GetReceivedBillHandlerAsync([Required] string payerName)
    {
        return await _billQueryService.MatchReceivedBillHandlerAsync(payerName);
    }
}