using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_fee_subtotal", DisableSyncStructure = true)]
	public partial class BillFeeSubtotal {

		[ Column(Name = "bill_amount", DbType = "money")]
		public decimal? BillAmount { get; set; }

		[ Column(Name = "bill_currency_id", StringLength = 50)]
		public string BillCurrencyId { get; set; }

		[ Column(Name = "bill_exchange_rate", DbType = "decimal(21,6)")]
		public decimal? BillExchangeRate { get; set; }

		[ Column(Name = "bill_id", StringLength = 50, IsNullable = false)]
		public string BillId { get; set; }

		[ Column(Name = "fee_amount", DbType = "money")]
		public decimal? FeeAmount { get; set; }

		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		[ Column(Name = "fee_currency_id", StringLength = 50)]
		public string FeeCurrencyId { get; set; }

		[Navigate(nameof(BillId))]
		public BillInfo BillInfo { get; set; }

	}

}
