using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 
/// </summary>
public class AppFurtherInfoDto
{
    /// <summary>
    /// 待补充资料的主键
    /// </summary>
    [JsonIgnore]
    public string FurtherId { get; set; } = null!;

    /// <summary>
    /// 订单id
    /// </summary>
    [JsonPropertyName("orderId")]
    public string OrderId { get; set; } = null!;
    
    /// <summary>
    /// 案件名称
    /// </summary>
    public string CaseName { get; set; } = null!;

    /// <summary>
    /// 主题
    /// </summary>
    [JsonPropertyName("resourceTitle")]
    public string Subject { get; set; } = null!;

    /// <summary>
    /// 待补充类型id
    /// </summary>
    [JsonIgnore]
    public string FurtherTypeId { get; set; } = null!;

    /// <summary>
    /// 待补充来兴描述
    /// </summary>
    [JsonPropertyName("additionalType")]
    public string FurtherType { get; set; } = null!;

    /// <summary>
    /// 待补充来兴描述集合
    /// </summary>
    [JsonIgnore]
    public List<string>? FurtherTypes { get; set; }

    /// <summary>
    /// 开案id
    /// </summary>
    public string? ApplyId { get; set; }

    /// <summary>
    /// 我方文号
    /// </summary>
    public string? Volume { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [JsonPropertyName("remark")]
    public string Remark { get; set; }  = null!;

    /// <summary>
    /// 期望交付日期
    /// </summary>
    [JsonPropertyName("expectedCompletionDate")]
    public DateTime? ExpectedDate { get; set; }

    /// <summary>
    /// 案件id
    /// </summary>
    [JsonIgnore]
    public string CaseId { get; set; }

    /// <summary>
    /// 委案id
    /// </summary>
    [JsonIgnore]
    public string EntrustId { get; set; }
}