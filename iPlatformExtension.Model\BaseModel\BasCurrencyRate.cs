using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_currency_rate", DisableSyncStructure = true)]
	public partial class BasCurrencyRate {

		/// <summary>
		/// 汇率ID
		/// </summary>
		[ Column(Name = "history_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string HistoryId { get; set; }

		/// <summary>
		/// 基准币别
		/// </summary>
		[ Column(Name = "base_currency_id", StringLength = 50)]
		public string BaseCurrencyId { get; set; } = "CNY";

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 货币名称
		/// </summary>
		[ Column(Name = "currency_id", StringLength = 50, IsNullable = false)]
		public string CurrencyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 结束时间
		/// </summary>
		[ Column(Name = "end_date")]
		public DateTime? EndDate { get; set; }

		/// <summary>
		/// 汇率
		/// </summary>
		[ Column(Name = "rate", DbType = "money")]
		public decimal Rate { get; set; }

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		/// <summary>
		/// 生效时间
		/// </summary>
		[ Column(Name = "start_date")]
		public DateTime? StartDate { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
