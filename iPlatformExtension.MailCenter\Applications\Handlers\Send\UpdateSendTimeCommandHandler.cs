using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.MailMQ;
using iPlatformExtension.MailCenter.Applications.Commands.Send;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Dto.MailCenter;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send;

/// <summary>
/// 更新定时发送时间命令处理程序
/// </summary>
/// <param name="mailSendRepository">邮件发送仓储</param>
/// <param name="mailSendListRepository">邮件发送列表仓储</param>
/// <param name="httpContextAccessor">HTTP上下文访问器</param>
/// <param name="producer">Kaf<PERSON>生产者服务</param>
internal sealed class UpdateSendTimeCommandHandler(
    IMailSendRepository mailSendRepository,
    IMailSendListRepository mailSendListRepository,
    IHttpContextAccessor httpContextAccessor,
    KafkaProducerService<Confluent.Kafka.Null, MailCenterMessageContent> producer) : IRequestHandler<UpdateSendTimeCommand>
{
    /// <summary>
    /// 处理更新定时发送时间命令
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    public async Task Handle(UpdateSendTimeCommand request, CancellationToken cancellationToken)
    {
        var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
        var now = DateTime.Now;

        // 获取邮件信息
        var mailSend = await mailSendRepository
            .Where(m => m.MailId == request.MailId)
            .FirstAsync(cancellationToken)
            ?? throw new ApplicationException($"邮件ID {request.MailId} 不存在");

        // 检查邮件状态是否为定时发送
        if (mailSend.Status != SendStatusType.ScheduledSend.GetHashCode())
        {
            throw new ApplicationException("只有处于定时发送状态的邮件才能修改发送时间");
        }

        // 获取邮件发送列表信息
        var mailSendList = await mailSendListRepository
            .Where(m => m.MailId == request.MailId)
            .FirstAsync(cancellationToken)
            ?? throw new ApplicationException($"邮件ID {request.MailId} 的发送记录不存在");

        // 检查发送时间是否在2分钟内
        if (mailSendList.SendTime.HasValue && (mailSendList.SendTime.Value - now).TotalMinutes <= 2)
        {
            throw new ApplicationException("距离原定发送时间已不足2分钟，不能修改");
        }

        // 检查新的发送时间是否合理（不能是过去的时间）
        if (request.SendTime <= now)
        {
            throw new ApplicationException("新的发送时间不能早于当前时间");
        }

        // 更新邮件发送时间
        mailSend.SendTime = request.SendTime;
        await mailSendRepository.UpdateAsync(mailSend, cancellationToken);

        // 更新邮件发送列表中的发送时间
        mailSendList.SendTime = request.SendTime;
        mailSendList.UpdateBy = userId;
        mailSendList.UpdateTime = now;
        await mailSendListRepository.UpdateAsync(mailSendList, cancellationToken);

    }
}
