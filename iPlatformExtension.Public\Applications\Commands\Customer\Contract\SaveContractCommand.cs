﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Public.Applications.Commands.Customer.Contract;

internal sealed record SaveContractCommand(
    IEnumerable<CaseFile> Files,
    CusContract Contract, 
    IEnumerable<ContractDetail> Details) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;