﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using System.Collections.Generic;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    /// <summary>
    /// 发件阅读人
    /// </summary>
    /// <param name="mySql"></param>
    /// <param name="httpContextAccessor"></param>
    /// <param name="msSql"></param>
    public class GetMyReadHistoryListBySendHandler(IFreeSql<MailCenterFreeSql> mySql, IHttpContextAccessor httpContextAccessor,
        IFreeSql<PlatformFreeSql> msSql) : IRequestHandler<GetMyReadHistoryListBySendQuery, PageResult<ReadListDto>>
    {
        public async Task<PageResult<ReadListDto>> Handle(GetMyReadHistoryListBySendQuery request, CancellationToken cancellationToken)
        {
            var date = DateTime.Now.AddDays(-7).Date;
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var lst = await mySql.Select<MailReaderList, MailSend, MailSendFlow>()
                .LeftJoin(o => o.t1.MailId == o.t2.MailId)
                .LeftJoin(o => o.t2.MailId == o.t3.MailId)
                .Where(o => o.t1.MailType == SysEnum.MailType.Send.ToString() && o.t1.UserId == userId && o.t1.UpdateTime >= date
                && o.t1.Status == (int)ReaderStatusEnum.AlreadyRead && o.t2.Status >= SysEnum.ReceiveFileType.Handle.GetHashCode())
                .WhereIf(!string.IsNullOrEmpty(request.Search),
                    o => o.t2.MailNo.Contains(request.Search) || o.t2.MailSubject.Contains(request.Search) ||
                         o.t2.MailFrom.Contains(request.Search))
                .Count(out long totalCount).OrderByDescending(o => o.t1.UpdateTime)
                .Take(request.PageSize).Skip(request.PageSize * (request.PageIndex - 1))
            .ToListAsync<ReadListDto>(o =>
                new ReadListDto
                {
                    ReadId = o.t1.Id,
                    ReadTime = o.t1.UpdateTime,
                    MailId = o.t1.MailId,
                    MailDate = o.t2.MailDate,
                    MailFrom = o.t2.MailFrom,
                    MailNo = o.t2.MailNo,
                    MailTo = o.t2.MailTo,
                    MailSubject = o.t2.MailSubject,
                    //Uid = o.t2.Uid,
                    Status = o.t2.Status,
                    //SortBy = o.t3.SortBy,
                    //SortTime = o.t3.SortTime,
                    UndertakeUserId = o.t3.UndertakeUserId,
                    //IgnoreBy = o.t3.IgnoreBy,
                    //IgnoreTime = o.t3.IgnoreTime,
                }
            , cancellationToken);

            var userList = await msSql.Select<SysUserInfo>().Where(u => lst.Any(o => o.IgnoreBy == u.UserId || o.SortBy == u.UserId || o.UndertakeUserId == u.UserId)).ToListAsync((u => new { UserName = u.CnName, UserId = u.UserId }), cancellationToken).ConfigureAwait(false);
            lst.ForEach(o =>
            {
                o.IgnoreByUserName = userList.FirstOrDefault(u => u.UserId == o.IgnoreBy)?.UserName;
                o.SortByUserName = userList.FirstOrDefault(u => u.UserId == o.SortBy)?.UserName;
                o.UndertakeUserName = userList.FirstOrDefault(u => u.UserId == o.UndertakeUserId)?.UserName;
            });


            return new PageResult<ReadListDto>()
            {
                Data = lst,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };
        }
    }
}
