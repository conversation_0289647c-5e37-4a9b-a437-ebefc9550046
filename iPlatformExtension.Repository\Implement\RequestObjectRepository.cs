﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class RequestObjectRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    DefaultRedisCache redisCache,
    IMemoryCache memoryCache,
    CacheExpirationToken<CusRequestObject> expirationToken)
    : BaseRepository<CusRequestObject, string>(freeSql),
        IRequestObjectRepository
{
    IMemoryCache ICacheableRepository<string, CusRequestObject>.MemoryCache => memoryCache;

    CacheExpirationToken<CusRequestObject> ICacheableRepository<string, CusRequestObject>.ExpirationToken => expirationToken;
    
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}