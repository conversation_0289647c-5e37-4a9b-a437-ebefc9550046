﻿using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class Fe<PERSON><PERSON><PERSON><PERSON><PERSON>andler(IMediator mediator) : IRequestHandler<FeesQuery, IEnumerable<FeeListItemDto>>
{
    public async Task<IEnumerable<FeeListItemDto>> Handle(FeesQuery request, CancellationToken cancellationToken)
    {
        var dto = request.Dto;
        var feeQueryBuildCommand =
            new FeeQueryBuildCommand(dto);
        var feeQuery = await mediator.Send(feeQueryBuildCommand, cancellationToken);

        if (dto is {Page:not null, PageSize:not null})
        {
            return await mediator.Send(new FeePagingQuery(feeQuery, dto.SortCondition, dto.SortOrder, dto.Page.Value, dto.PageSize.Value, dto.Total),
                cancellationToken);
        }
        else
        {
            return await mediator.Send(new FeeIdsQuery(feeQuery, dto.SortCondition, dto.SortOrder), cancellationToken);
        }
    }
}