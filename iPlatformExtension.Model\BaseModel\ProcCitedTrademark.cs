﻿using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;
using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 引证商标无效宣告
/// </summary>
[Table(Name = "proc_cited_trademark", DisableSyncStructure = true)]
public sealed class ProcCitedTrademark 
{

	/// <summary>
	/// 主键
	/// </summary>
	[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
	[Display(Name = "引证商标id", Order = 0)]
	[Hide]
	public int Id { get; set; }

	/// <summary>
	/// 联系地址
	/// </summary>
	[Column(Name = "contact_address", StringLength = 500, IsNullable = false)]
	[Display(Name = "联系地址", Order = 4)]
	public string ContactAddress { get; set; } = "";

	/// <summary>
	/// 联系人
	/// </summary>
	[Column(Name = "contactor", StringLength = 50, IsNullable = false)]
	[Display(Name = "联系人", Order = 3)]
	public string Contactor { get; set; } = "";

	/// <summary>
	/// 创建时间
	/// </summary>
	[Column(Name = "creation_time", InsertValueSql = "getdate()")]
	public DateTime CreationTime { get; set; }

	/// <summary>
	/// 创建者
	/// </summary>
	[Column(Name = "creator", StringLength = 50, IsNullable = false)]
	public string Creator { get; set; } = "";

	/// <summary>
	/// 任务id
	/// </summary>
	[Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
	public string ProcId { get; set; } = "";

	/// <summary>
	/// 注册号
	/// </summary>
	[Column(Name = "register_number", StringLength = 50, IsNullable = false)]
	[Display(Name = "注册号", Order = 1)]
	public string RegisterNumber { get; set; } = "";

	/// <summary>
	/// 类别
	/// </summary>
	[Column(Name = "trademark_classes", StringLength = 100, IsNullable = false)]
	[Display(Name = "类别", Order = 2)]
	public string TrademarkClasses { get; set; } = "";

	/// <summary>
	/// 更新时间
	/// </summary>
	[Column(Name = "update_time", InsertValueSql = "getdate()")]
	public DateTime UpdateTime { get; set; }

	/// <summary>
	/// 更新者
	/// </summary>
	[Column(Name = "updater", StringLength = 50, IsNullable = false)]
	public string Updater { get; set; } = "";

}