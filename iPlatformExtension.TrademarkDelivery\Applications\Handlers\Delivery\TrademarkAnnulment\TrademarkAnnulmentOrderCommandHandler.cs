﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery.TrademarkAnnulment;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Exceptions;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Extensions;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery.TrademarkAnnulment;

internal sealed class TrademarkAnnulmentOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IBaseCountryRepository countryRepository,
    IApplicantTypeRepository applicantTypeRepository,
    IDeliveryInfoRepository deliveryInfoRepository,
    ISystemDictionaryRepository dictionaryRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    UnitOfWorkManager unitOfWorkManager,
    ILoggerFactory loggerFactory,
    PhoenixClientFactory phoenixClientFactory)
    : PhoenixDeliveryHandleBase<TrademarkAnnulmentOrderCommand, TrademarkAnnulmentOrder,
            PhoenixResponseParameters<OrderData>>(stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository,
            unitOfWorkManager, loggerFactory, phoenixClientFactory),
        IDeliveryHandler<TrademarkAnnulmentOrderCommand, TrademarkAnnulmentOrder, PhoenixResponseParameters<OrderData>>
{
    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.StartupDelivery;
    
    public override async Task<TrademarkAnnulmentOrder> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        ArgumentNullException.ThrowIfNull(_deliveryInfo.OtherInfo);

        var otherInfo = _deliveryInfo.OtherInfo;

        var lawProvisions = await otherInfo.LawProvisions.Split(';', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries)
            .ToAsyncEnumerable()
            .SelectAwait(async lawId => new LawProvision()
            {
                LawId = Convert.ToInt32(lawId),
                LawName = (await dictionaryRepository.GetChineseKeyValueAsync(
                    SystemDictionaryName.AnnulmentLawProvision, lawId)).Value
            }).ToListAsync(cancellationToken);
        
        var grandNumbers = _deliveryInfo.OtherInfo.TrademarkNiceClasses?.Split(';') ?? Array.Empty<string>();
        
        var currentApplicant = _deliveryInfo.Applicants!.Single(applicant => applicant.IsRepresent ?? false);

        var countryId = currentApplicant.CountryId ??
                        throw new PropertyMissingException(currentApplicant.ApplicantId, currentApplicant.CountryId, "申请人");
        var applicantBookType = ApplicantBookType.GetApplicantBookType(countryId);
        
        string country;
        if (applicantBookType == ApplicantBookType.Overseas)
        {
            var countryInfo = await countryRepository.GetCacheValueAsync(countryId);
            if (countryInfo is null)
            {
                throw new NotFoundException(countryId, "国家");
            }

            if (string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCode) || string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCountryName))
            {
                throw new ApplicationException("商标局没有对应的国家编码和名称");
            }

            country = $"{countryInfo.TrademarkDeliveryCode}  {countryInfo.TrademarkDeliveryCountryName}";
        }
        else
        {
            country = applicantBookType.Description;
        }

        var applicantType = await applicantTypeRepository.GetCacheValueAsync(currentApplicant.TypeId.GetOrDefaultEmpty());
        if (applicantType is null)
        {
            throw new ApplicantTypeMissingException(currentApplicant, "当前申请人");
        }
        
        var attachments = _deliveryInfo.Files?
                              .Where(file => int.TryParse(file.FileCode, out _) && file.BaseFileType is "申请人文件" or "递交官方")
                              .Select(file => 
                                  new ApplicantAttachment(
                                      file.FileName, 
                                      file.Url ?? string.Empty, 
                                      ApplicantAttachmentType.OfficialAttachment, 
                                      new ApplicantAttachmentSubType(int.Parse(file.FileCode!), file.FileDesc ?? string.Empty))) 
                          ?? Array.Empty<ApplicantAttachment>();

        var order = new TrademarkAnnulmentOrder()
        {
            BookType = applicantBookType.Code.ToString(),
            Country = country,
            OwnerType = applicantType.GetOwnerType(),
            ApplicantName = currentApplicant.ApplicantNameCn,
            IdCard = currentApplicant.CardNo,
            ApplicantAddress = currentApplicant.AddressCn,
            Code = currentApplicant.Postcode,
            PrincipalName = _deliveryInfo.ContactPerson,
            PrincipalTel = _deliveryInfo.ContactTel,
            AgentOrganConName = _deliveryInfo.AgentUser,
            AgentPerson = _deliveryInfo.AgentUser,
            SubjectType = currentApplicant.GetSubjectType(),
            CertificatesType = currentApplicant.CardType.GetApplicantCertificationType(),
            IsSuppleEvidence =  otherInfo.ReservationOfSupplementaryMaterial switch
            {
                false => 0,
                true => 1,
                _ => throw new ApplicationException("[是否保留补充材料权利]缺少对应的值")
            },
            IsAbsoluteReason = otherInfo.HasAbsoluteReason.HasValue ? otherInfo.HasAbsoluteReason.Value ? 1 : 0 : null,
            IsAgreeAddress = otherInfo.ExtendToSameAddress.HasValue ? otherInfo.ExtendToSameAddress.Value ? 1 : 0 : null,
            LawProvisionList = lawProvisions,
            ApplicantAttachments = attachments,
            QuoteBrandInfos = _deliveryInfo.CitedTrademarkAnnulments?.Select(trademark => new QuoteBrandInfo
            {
                RegisterNumber = trademark.RegisterNumber,
                ContactAddress = trademark.ContactAddress,
                ContactName = trademark.Contactor,
                FirstCategoryNumber = trademark.TrademarkClasses
            }),
            BrandInfos = grandNumbers.Select(grandNumber => new BrandInfo
            {
                BrandRegisterNo = _deliveryInfo.AppNo,
                FirstCgNo = grandNumber,
            }),
            OrderToken = _deliveryInfo.ProcId,
            ContactEmail = _deliveryInfo.ContactMailBox,
            ContactName = _deliveryInfo.ContactPerson,
            ContactTel = _deliveryInfo.ContactTel,
            AgentOrganTel = _deliveryInfo.ContactTel
        };

        return order;
    }

    public override Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(TrademarkAnnulmentOrder request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        _phoenixClient ??= _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
        return _phoenixClient.CreateOrderAsync(PhoenixUri.InvalidationOrder, request);
    }

    public override Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        var orderNo = response.Data?.OrderNo!;
        const int newStatus = (int) DeliveryStatus.Ordered;
        
        return Handler.UpdateDeliveryInfoInternalAsync(deliveryInfo =>
        {
            deliveryInfo.Status = newStatus;
            deliveryInfo.OrderNo = orderNo;
            deliveryInfo.OperationResult = true;
            return ValueTask.CompletedTask;
        }, cancellationToken);
    }

    public override async Task InitializeAsync(TrademarkAnnulmentOrderCommand command, CancellationToken cancellationToken)
    {
        var procId = command.ProcId;
        _deliveryInfo = await DeliveryInfoRepository.Where(info => info.ProcId == procId)
            .Include(info => info.OtherInfo)
            .IncludeMany(info => info.Applicants)
            .IncludeMany(info => info.Files)
            .IncludeMany(info => info.CitedTrademarkAnnulments)
            .ToOneAsync(cancellationToken);

        if (_deliveryInfo is null)
        {
            throw new NotFoundException(procId, "递交任务");
        }
        
        _deliveryInfo.ValidateVersion(command.Version);

        _phoenixClient = _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
    }
    
    public async Task<bool> HandleExceptionAsync(Exception ex, CancellationToken cancellationToken = default)
    {
        await Handler.HandleExceptionInternalAsync(ex, true, false, cancellationToken);
        return false;
    }
}