namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 
/// </summary>
public class FlowNodeDto
{
    /// <summary>
    /// 流程Id
    /// </summary>
    public string FlowId { get; set; } = null!;

    /// <summary>
    /// 排序
    /// </summary>
    public int Seq { get; set; }

    /// <summary>
    /// 下一节点的排序
    /// </summary>
    public int Next { get; set; }

    /// <summary>
    /// 节点名称
    /// </summary>
    public string NodeName { get; set; } = null!;

    /// <summary>
    /// 节点中文名称
    /// </summary>
    public string NodeZhCn { get; set; } = null!;

    /// <summary>
    /// 
    /// </summary>
    public string NodeCode { get; set; }

    public bool AllowEdit { get; set; }

    public string NodeId { get; set; }
}