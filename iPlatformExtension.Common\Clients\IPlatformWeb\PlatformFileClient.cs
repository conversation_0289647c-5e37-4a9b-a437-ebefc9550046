﻿using System.Text;
using iPlatformExtension.Common.Clients.IPlatformWeb.Options;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Clients.IPlatformWeb;

public sealed class PlatformFileClient
{
    private readonly HttpClient _httpClient;

    private readonly IOptionsMonitor<PlatformFileOptions> _options;

    private readonly ObjectPool<StringBuilder> _stringBuilderPool;

    public bool IsWebDav { get; }

    public PlatformFileClient(HttpClient httpClient, IOptionsMonitor<PlatformFileOptions> options, ObjectPool<StringBuilder> stringBuilderPool)
    {
        _httpClient = httpClient;
        _options = options;
        _stringBuilderPool = stringBuilderPool;

        var clientOptions = options.CurrentValue;
        _httpClient.BaseAddress = new Uri(clientOptions.HostAddress);
        IsWebDav = clientOptions.HostAddress.Contains("webdav", StringComparison.OrdinalIgnoreCase);
    }

    private string BuildFileUri(string serverPath, string fileName)
    {
        var uriBuilder = _stringBuilderPool.Get();
        if (IsWebDav)
        {
            uriBuilder.Append("/webdav");
        }

        uriBuilder.Append(serverPath.Replace('\\', '/'));
        uriBuilder.Append('/');
        uriBuilder.Append(fileName);

        var uri = uriBuilder.ToString();
        
        _stringBuilderPool.Return(uriBuilder);
        return uri;
    }

    public Task<HttpResponseMessage> GetFileAsync(string serverPath, string fileName)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, BuildFileUri(serverPath, fileName));
        return _httpClient.SendAsync(request);
    }
}