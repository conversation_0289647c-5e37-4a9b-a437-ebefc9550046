using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkDelivery.Applications.Queries;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkDelivery.Controllers;

[ApiController]
[Route("[controller]")]
public sealed class OrderController(IMediator mediator) : ControllerBase
{
    [HttpGet("{procId}")]
    public Task<PhoenixOrderInfo?> GetAsync(string procId)
    {
        return mediator.Send(new OrderInfoQuery(procId));
    }
}