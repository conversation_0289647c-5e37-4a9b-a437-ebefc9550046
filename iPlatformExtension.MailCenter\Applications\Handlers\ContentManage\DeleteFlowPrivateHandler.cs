﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class DeleteFlowPrivateHandler(IMailFlowPrivateRepository mailFlowPrivateRepository, IFlowPrivateListRepository flowPrivateListRepository, IHttpContextAccessor httpContextAccessor) : IRequestHandler<DeleteFlowPrivateCommand>
    {
        public async Task Handle(DeleteFlowPrivateCommand request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userId);
            var flowPrivate = await mailFlowPrivateRepository.Where(o => request.FlowPrivateId == o.Id && o.UserId == userId)
                .ToOneAsync(cancellationToken);
            if (flowPrivate != null)
            {
                flowPrivateListRepository.DeleteAsync(o => o.PrivateId == flowPrivate.Id);
                await mailFlowPrivateRepository.DeleteAsync(o => o.Id == flowPrivate.Id && o.UserId == userId);
            }
        }
    }
}
