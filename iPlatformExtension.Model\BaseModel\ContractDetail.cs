﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 合同明细
/// </summary>
[Table(Name = "contract_detail", DisableSyncStructure = true)]
public partial class ContractDetail 
{

	/// <summary>
	/// 主键
	/// </summary>
	[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
	public int Id { get; set; }

	/// <summary>
	/// 归档编号
	/// </summary>
	[Column(Name = "archive_number", StringLength = 50, IsNullable = false)]
	public string ArchiveNumber { get; set; } = "";

	/// <summary>
	/// 合同业务类型
	/// </summary>
	[Column(Name = "contract_business_type", StringLength = 50, IsNullable = false)]
	public string ContractBusinessType { get; set; } = "";

	/// <summary>
	/// 关联的合同id
	/// </summary>
	[Column(Name = "contract_id", StringLength = 50, IsNullable = false)]
	public string ContractId { get; set; } = "";

	/// <summary>
	/// 截至时间
	/// </summary>
	[Column(Name = "end_date")]
	public DateTime? EndDate { get; set; }

	// /// <summary>
	// /// 是否有售前支持
	// /// </summary>
	// [Column(Name = "has_pre_sales_support")]
	// public bool HasPreSalesSupport { get; set; } = false;

	/// <summary>
	/// 是否可用
	/// </summary>
	[Column(Name = "is_enable")]
	public bool IsEnable { get; set; } = false;

	/// <summary>
	/// 售前支持用户id
	/// </summary>
	[Column(Name = "pre_sales_support_id", StringLength = 50, IsNullable = false)]
	public string PreSalesSupportId { get; set; } = "";

	/// <summary>
	/// 售前支持员工编号
	/// </summary>
	[Column(Name = "pre_sales_username", StringLength = 50, IsNullable = false)]
	public string PreSalesUsername { get; set; } = "";

	/// <summary>
	/// 备注
	/// </summary>
	[Column(Name = "remark", StringLength = -2, IsNullable = false)]
	public string Remark { get; set; } = "";

	/// <summary>
	/// 开始时间
	/// </summary>
	[Column(Name = "start_date")]
	public DateTime StartDate { get; set; }

	/// <summary>
	/// 合同标题
	/// </summary>
	[Column(Name = "title", StringLength = -2, IsNullable = false)]
	public string Title { get; set; } = "";

	/// <summary>
	/// 更新时间
	/// </summary>
	[Column(Name = "update_time", InsertValueSql = "getdate()")]
	public DateTime UpdateTime { get; set; }

	/// <summary>
	/// 更新员工的工号
	/// </summary>
	[Column(Name = "updater", StringLength = 50)]
	public string Updater { get; set; } = string.Empty;

	/// <summary>
	/// 更新用户id
	/// </summary>
	[Column(Name = "updater_id", StringLength = 50)]
	public string UpdaterId { get; set; } = string.Empty;

	/// <summary>
	/// 合同基本信息
	/// </summary>
	[Navigate(nameof(ContractId))]
	public CusContract? Contract { get; set; }

}