﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class HandOverDeliveryValidateCommandHandler(IMediator mediator, IRedisCache<RedisCacheOptionsBase> redisCache) : IRequestHandler<HandOverDeliveryValidateCommand, BatchDeliveryValidateResult>
{
    public async Task<BatchDeliveryValidateResult> Handle(HandOverDeliveryValidateCommand request, CancellationToken cancellationToken)
    {
        var procIds = request.ProcIds;
        
        var deliveryList = await mediator.Send(new BatchDeliveryValidationQuery(procIds), cancellationToken);
        
        var validResult = await mediator.Send(new MultipartCtrlProcValidateCommand(deliveryList), cancellationToken);
        if (!validResult.Success)
        {
            return validResult;
        }
        
        validResult.Success = deliveryList.Where(item => item.CurrentNodeId is not null).GroupBy(item => item.CurrentNodeId).Count() == 1;
        if (!validResult.Success)
        {
            return validResult.MultipartNode();
        }

        validResult = await mediator.Send(new NoFirstFlowNodeValidateCommand(deliveryList, BatchDeliveriesValidateType.HandOver), cancellationToken);
        if (!validResult.Success)
        {
            return validResult;
        }
        
        validResult = await mediator.Send(new UndertakerValidateCommand(deliveryList), cancellationToken);
        if (!validResult.Success)
        {
            return validResult;
        }

        validResult = await deliveryList.ToAsyncEnumerable().WhereAwait(async dto =>
        {
            var locked =
                (await redisCache.GetCacheValueAsync<string, long?>(LockKey.DeliveringLockKey, dto.ProcId) ?? 0) >
                DateTimeOffset.Now.ToUnixTimeMilliseconds();

            var deliveryStatus = (DeliveryStatus) dto.DeliveryStatus;
            return deliveryStatus == DeliveryStatus.Ordered || deliveryStatus == DeliveryStatus.Delivering || locked;
        }).AggregateAsync(validResult, (result, dto) => result.CannotHandOver(dto.ProcNo), cancellationToken);

        return validResult;
    }
}