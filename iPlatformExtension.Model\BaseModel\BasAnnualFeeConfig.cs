using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_annual_fee_config", DisableSyncStructure = true)]
	public partial class BasAnnualFeeConfig {

		[ Column(Name = "config_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ConfigId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "annual_fee_id", StringLength = 50)]
		public string AnnualFeeId { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "date_day")]
		public int? DateDay { get; set; }

		[ Column(Name = "date_month")]
		public int? DateMonth { get; set; }

		[ Column(Name = "date_year")]
		public int? DateYear { get; set; }

		[ Column(Name = "fee_base", DbType = "money")]
		public decimal? FeeBase { get; set; }

		[ Column(Name = "fee_micro", DbType = "money")]
		public decimal? FeeMicro { get; set; }

		[ Column(Name = "fee_small", DbType = "money")]
		public decimal? FeeSmall { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "proc_property", StringLength = 50)]
		public string ProcProperty { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "year_end")]
		public int? YearEnd { get; set; }

		[ Column(Name = "year_start")]
		public int? YearStart { get; set; }

	}

}
