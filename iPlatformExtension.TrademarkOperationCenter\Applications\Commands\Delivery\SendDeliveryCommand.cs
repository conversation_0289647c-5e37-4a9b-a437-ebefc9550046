﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

internal record SendDeliveryCommand(string ProcId, DeliveryButton DeliveryButton, string OperatorId) 
    : IRequest<SendDeliveryInternalCommand?>, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;