using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_biology_preserve", DisableSyncStructure = true)]
	public partial class BasBiologyPreserve {

		[ Column(Name = "address", StringLength = 50)]
		public string Address { get; set; }

		/// <summary>
		/// 生物保藏编号
		/// </summary>
		[ Column(Name = "biology_code", StringLength = 50, IsNullable = false)]
		public string BiologyCode { get; set; }

		/// <summary>
		/// 生物保藏ID
		/// </summary>
		[ Column(Name = "biology_id", StringLength = 50, IsNullable = false)]
		public string BiologyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 保藏名称
		/// </summary>
		[ Column(Name = "code_name", StringLength = 500, IsNullable = false)]
		public string CodeName { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

	}

}
