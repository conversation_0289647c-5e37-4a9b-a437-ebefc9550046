using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_search_form_history", DisableSyncStructure = true)]
	public partial class SysSearchFormHistory {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "params", StringLength = 4000)]
		public string Params { get; set; }

		[ Column(Name = "search_form_code", StringLength = 50)]
		public string SearchFormCode { get; set; }

		[ Column(Name = "search_title", StringLength = 50)]
		public string SearchTitle { get; set; }

		[ Column(Name = "search_user_id", StringLength = 50)]
		public string SearchUserId { get; set; }

	}

}
