using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Common.Db;
using MediatR;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using RulesEngine.Models;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class SuggestionProcPointComputeCommandHandler(PlatformMongoDbContext mongoDbContext) 
    : IRequestHandler<SuggestionProcPointComputeCommand, decimal>
{
    private const string WorkFlowName = "国内商标提成建议类任务权值计算";
    
    public async Task<decimal> Handle(SuggestionProcPointComputeCommand request, CancellationToken cancellationToken)
    {
        var (procName, otherProcInfos) = request;

        var workflow = await mongoDbContext.Workflows.AsQueryable().Where(flow => flow.WorkflowName == WorkFlowName)
            .FirstOrDefaultAsync(cancellationToken);
        
        if (workflow == null)
            return 0m;
        
        var rulesEngine = new RulesEngine.RulesEngine([workflow]);
        
        var trees = await rulesEngine.ExecuteAllRulesAsync(workflow.WorkflowName, new RuleParameter("procInfos", otherProcInfos));
        var resultTree = trees.FirstOrDefault(tree => tree.Rule.RuleName == procName);

        if (resultTree is null) return default;
        
        if (resultTree.IsSuccess && resultTree.ActionResult.Output is decimal point)
        {
            return point;
        }

        var ex = resultTree.ActionResult.Exception;
        throw new ApplicationException(resultTree.ExceptionMessage, ex);

    }
}