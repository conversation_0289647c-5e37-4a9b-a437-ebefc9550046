<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SignalR 测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/@microsoft/signalr@7.0.12/dist/browser/signalr.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .connecting {
            background-color: #fff3cd;
            color: #856404;
        }
        #messageLog {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 4px;
        }
        .info {
            background-color: #e7f3fe;
            border-left: 3px solid #2196F3;
        }
        .error {
            background-color: #ffdddd;
            border-left: 3px solid #f44336;
        }
        .success {
            background-color: #ddffdd;
            border-left: 3px solid #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SignalR 测试页面</h1>

        <div class="card">
            <h2>连接设置</h2>
            <div class="form-group">
                <label for="hubUrl">SignalR Hub URL:</label>
                <input type="text" id="hubUrl" value="http://localhost:5000/flowHub" />
                <small style="display: block; margin-top: 5px; color: #666;">注意：如果连接到远程服务器，请使用wss://或ws://协议</small>
            </div>
            <div class="form-group">
                <label for="skipNegotiation">跳过协商:</label>
                <select id="skipNegotiation">
                    <option value="true">是 (直接使用WebSockets)</option>
                    <option value="false">否 (允许协商过程)</option>
                </select>
                <small style="display: block; margin-top: 5px; color: #666;">如果遇到CORS问题，请选择"是"</small>
            </div>
            <div class="form-group">
                <label for="logLevel">日志级别:</label>
                <select id="logLevel">
                    <option value="1">Error</option>
                    <option value="2">Warning</option>
                    <option value="3">Information</option>
                    <option value="4" selected>Debug</option>
                    <option value="5">Trace</option>
                </select>
            </div>
            <div class="form-group">
                <label for="authToken">认证Token:</label>
                <input type="text" id="authToken" value="bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDQ5NDUyMDIsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6ImMxNTk3YzUxLTc4MWItNDY4Mi04MWM3LWUwMWRjMjM0MzFjZSIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.b1HyYT7sS3g448_K81UKgxsgKYMv5vClAM0bfEb6DtY" style="font-size: 10px;" />
            </div>
            <div>
                <button id="connectButton">连接</button>
                <button id="disconnectButton" disabled>断开连接</button>
            </div>
            <div id="connectionStatus" class="status disconnected">
                状态: 未连接
            </div>
        </div>

        <div class="card">
            <h2>用户信息</h2>
            <div id="userInfo" class="status disconnected">
                未连接或未获取到用户信息
            </div>
            <div style="margin-top: 10px;">
                <button id="getUserInfoButton" disabled>获取用户信息</button>
                <button id="testMessageButton" disabled>测试发送消息</button>
            </div>
        </div>

        <div class="card">
            <h2>消息监听</h2>
            <div class="form-group">
                <label for="methodName">监听方法名:</label>
                <select id="methodName">
                    <option value="MailCenterCount">邮件中心计数(MailCenterCount)</option>
                    <option value="ToDoMessage">待办消息(ToDoMessage)</option>
                    <option value="NotificationFlowMessage">流程通知(NotificationFlowMessage)</option>
                    <option value="MailCountMessage">邮件计数消息(MailCountMessage)</option>
                </select>
                <button id="addListenerButton" disabled>添加监听</button>
            </div>
            <div id="activeListeners">
                <h3>活动监听器:</h3>
                <ul id="listenersList"></ul>
            </div>
        </div>

        <div class="card">
            <h2>消息日志</h2>
            <div id="messageLog"></div>
            <button id="clearLogButton">清除日志</button>
        </div>
    </div>

    <script>
        // DOM 元素
        const hubUrlInput = document.getElementById('hubUrl');
        const skipNegotiationSelect = document.getElementById('skipNegotiation');
        const connectButton = document.getElementById('connectButton');
        const disconnectButton = document.getElementById('disconnectButton');
        const connectionStatus = document.getElementById('connectionStatus');
        const userInfoDiv = document.getElementById('userInfo');
        const getUserInfoButton = document.getElementById('getUserInfoButton');
        const testMessageButton = document.getElementById('testMessageButton');
        const methodNameInput = document.getElementById('methodName');
        const addListenerButton = document.getElementById('addListenerButton');
        const listenersList = document.getElementById('listenersList');
        const messageLog = document.getElementById('messageLog');
        const clearLogButton = document.getElementById('clearLogButton');

        // SignalR 连接
        let connection = null;
        let activeListeners = new Set();

        // 更新连接状态UI
        function updateConnectionStatus(status, message) {
            connectionStatus.className = `status ${status}`;
            connectionStatus.textContent = `状态: ${message}`;

            if (status === 'connected') {
                connectButton.disabled = true;
                disconnectButton.disabled = false;
                addListenerButton.disabled = false;
                getUserInfoButton.disabled = false;
                testMessageButton.disabled = false;
            } else {
                connectButton.disabled = false;
                disconnectButton.disabled = true;
                addListenerButton.disabled = true;
                getUserInfoButton.disabled = true;
                testMessageButton.disabled = true;
            }
        }

        // 添加消息到日志
        function logMessage(message, type = 'info') {
            const messageElement = document.createElement('div');
            messageElement.className = `message ${type}`;
            messageElement.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            messageLog.appendChild(messageElement);
            messageLog.scrollTop = messageLog.scrollHeight;
        }

        // 连接到SignalR Hub
        async function connectToHub() {
            const hubUrl = hubUrlInput.value;
            const skipNegotiation = document.getElementById('skipNegotiation').value === 'true';

            if (!hubUrl) {
                logMessage('请输入有效的Hub URL', 'error');
                return;
            }

            try {
                updateConnectionStatus('connecting', '正在连接...');
                logMessage(`正在连接到 ${hubUrl} ${skipNegotiation ? '直接使用WebSockets' : '允许协商过程'}...`);

                // 获取认证Token
                const authToken = document.getElementById('authToken').value;

                // 创建连接
                connection = new signalR.HubConnectionBuilder()
                    .withUrl(hubUrl, {
                        accessTokenFactory: () => authToken.startsWith('bearer ') ? authToken.substring(7) : authToken,
                        headers: {
                            "Authorization": authToken
                        },
                        skipNegotiation: skipNegotiation,  // 是否跳过协商过程
                        transport: skipNegotiation ? signalR.HttpTransportType.WebSockets : undefined  // 如果跳过协商，则强制使用WebSockets
                    })
                    .withAutomaticReconnect([0, 2000, 10000, 30000]) // 自动重连策略
                    .configureLogging(parseInt(document.getElementById('logLevel').value))  // 使用选择的日志级别
                    .build();

                logMessage(`使用认证Token: ${authToken.substring(0, 20)}...`, 'info');

                // 连接事件处理
                connection.onreconnecting(error => {
                    updateConnectionStatus('connecting', '正在重新连接...');
                    logMessage(`连接丢失，正在尝试重新连接: ${error ? error.message : 'Unknown error'}`, 'error');
                });

                connection.onreconnected(connectionId => {
                    updateConnectionStatus('connected', '已连接');
                    logMessage(`重新连接成功，连接ID: ${connectionId}`, 'success');

                    // 重新添加所有监听器
                    activeListeners.forEach(method => {
                        addListener(method);
                    });
                });

                connection.onclose(error => {
                    updateConnectionStatus('disconnected', '已断开连接');
                    logMessage(`连接已关闭: ${error ? error.message : 'No error'}`, 'error');
                });

                // 启动连接
                await connection.start();
                updateConnectionStatus('connected', '已连接');
                logMessage(`连接成功，连接ID: ${connection.connectionId}`, 'success');

                // 获取并显示用户信息
                await getUserInfo();

                // 自动添加预设监听器
                addPresetListeners();
            } catch (error) {
                updateConnectionStatus('disconnected', '连接失败');
                logMessage(`连接失败: ${error.message}`, 'error');
                connection = null;
            }
        }

        // 断开连接
        async function disconnectFromHub() {
            if (connection) {
                try {
                    await connection.stop();
                    updateConnectionStatus('disconnected', '已断开连接');
                    logMessage('已断开连接', 'info');

                    // 清除监听器列表
                    listenersList.innerHTML = '';
                    activeListeners.clear();
                } catch (error) {
                    logMessage(`断开连接失败: ${error.message}`, 'error');
                }
            }
        }

        // 添加监听器
        function addListener(methodName) {
            if (!connection || !methodName) return;

            if (activeListeners.has(methodName)) {
                logMessage(`已经在监听方法 "${methodName}"`, 'info');
                return;
            }

            try {
                connection.on(methodName, (...args) => {
                    logMessage(`收到 "${methodName}" 消息: ${JSON.stringify(args)}`, 'success');
                });

                activeListeners.add(methodName);

                // 更新UI
                const listItem = document.createElement('li');
                listItem.textContent = methodName;
                listItem.dataset.method = methodName;

                const removeButton = document.createElement('button');
                removeButton.textContent = '移除';
                removeButton.style.marginLeft = '10px';
                removeButton.onclick = () => removeListener(methodName);

                listItem.appendChild(removeButton);
                listenersList.appendChild(listItem);

                logMessage(`开始监听方法 "${methodName}"`, 'info');
            } catch (error) {
                logMessage(`添加监听器失败: ${error.message}`, 'error');
            }
        }

        // 移除监听器
        function removeListener(methodName) {
            if (!connection || !activeListeners.has(methodName)) return;

            try {
                connection.off(methodName);
                activeListeners.delete(methodName);

                // 更新UI
                const listItem = Array.from(listenersList.children).find(
                    item => item.dataset.method === methodName
                );
                if (listItem) {
                    listenersList.removeChild(listItem);
                }

                logMessage(`停止监听方法 "${methodName}"`, 'info');
            } catch (error) {
                logMessage(`移除监听器失败: ${error.message}`, 'error');
            }
        }

        // 测试发送消息
        async function testSendMessage() {
            if (!connection) {
                logMessage('未连接到服务器，无法发送消息', 'error');
                return;
            }

            try {
                // 获取当前用户ID
                const userInfo = await connection.invoke('GetCurrentUserInfo');
                const userId = userInfo.userId || 'unknown';

                // 构造测试消息
                const testMessage = {
                    id: new Date().getTime(),
                    content: '这是一条测试消息',
                    timestamp: new Date().toISOString(),
                    sender: 'TestClient'
                };

                // 调用服务器方法发送消息
                logMessage(`正在发送测试消息到用户 ${userId}...`, 'info');
                await connection.invoke('MailCountMessageAsync', 'TestMessage', userId, testMessage);
                logMessage('测试消息发送成功', 'success');

                // 确保我们有一个监听器来接收这个消息
                if (!activeListeners.has('TestMessage')) {
                    addListener('TestMessage');
                }
            } catch (error) {
                logMessage(`发送测试消息失败: ${error.message}`, 'error');
            }
        }

        // 事件监听
        connectButton.addEventListener('click', connectToHub);
        disconnectButton.addEventListener('click', disconnectFromHub);

        getUserInfoButton.addEventListener('click', getUserInfo);
        testMessageButton.addEventListener('click', testSendMessage);

        addListenerButton.addEventListener('click', () => {
            const methodName = methodNameInput.value.trim();
            if (methodName) {
                addListener(methodName);
                methodNameInput.value = '';
            } else {
                logMessage('请输入有效的方法名', 'error');
            }
        });

        clearLogButton.addEventListener('click', () => {
            messageLog.innerHTML = '';
            logMessage('日志已清除', 'info');
        });

        // 获取用户信息
        async function getUserInfo() {
            if (!connection) {
                userInfoDiv.className = 'status disconnected';
                userInfoDiv.textContent = '未连接或未获取到用户信息';
                return;
            }

            try {
                userInfoDiv.className = 'status connecting';
                userInfoDiv.textContent = '正在获取用户信息...';

                // 从连接中获取用户信息
                const connectionId = connection.connectionId;
                const authToken = document.getElementById('authToken').value;

                // 从服务器获取用户信息
                logMessage('正在从服务器获取用户信息...', 'info');
                const serverUserInfo = await connection.invoke('GetCurrentUserInfo');
                logMessage('从服务器获取用户信息成功', 'success');

                // 解析JWT Token获取用户信息
                let tokenUserInfo = {};
                if (authToken && authToken.startsWith('bearer ')) {
                    const token = authToken.substring(7).split('.')[1];
                    try {
                        const base64 = token.replace(/-/g, '+').replace(/_/g, '/');
                        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                        }).join(''));

                        tokenUserInfo = JSON.parse(jsonPayload);
                        logMessage('JWT Token解析成功', 'success');
                    } catch (e) {
                        console.error('JWT解析失败', e);
                        logMessage(`JWT Token解析失败: ${e.message}`, 'error');
                    }
                }

                // 显示用户信息
                userInfoDiv.className = 'status connected';
                userInfoDiv.innerHTML = `
                    <h3>客户端信息</h3>
                    <strong>连接ID:</strong> ${connectionId}<br>
                    <strong>用户ID (Token):</strong> ${tokenUserInfo.sub || '未知'}<br>
                    <strong>发行者:</strong> ${tokenUserInfo.iss || '未知'}<br>
                    <strong>过期时间:</strong> ${tokenUserInfo.exp ? new Date(tokenUserInfo.exp * 1000).toLocaleString() : '未知'}<br>
                    <strong>受众:</strong> ${tokenUserInfo.aud || '未知'}<br>
                    <h3>服务器信息</h3>
                    <strong>连接ID:</strong> ${serverUserInfo.connectionId}<br>
                    <strong>用户ID:</strong> ${serverUserInfo.userId || '未知'}<br>
                    <strong>已认证:</strong> ${serverUserInfo.isAuthenticated ? '是' : '否'}<br>
                    <strong>Claims:</strong> <pre>${JSON.stringify(serverUserInfo.claims, null, 2)}</pre>
                `;

                logMessage('用户信息获取成功', 'success');
            } catch (error) {
                userInfoDiv.className = 'status disconnected';
                userInfoDiv.textContent = `获取用户信息失败: ${error.message}`;
                logMessage(`获取用户信息失败: ${error.message}`, 'error');
            }
        }

        // 添加预设监听器
        function addPresetListeners() {
            if (!connection) return;

            // 添加常用的监听器
            const presetMethods = [
                'MailCenterCount',
                'MailCountMessage'
            ];

            presetMethods.forEach(method => {
                addListener(method);
            });
        }

        // 初始化
        logMessage('页面已加载，准备连接到SignalR Hub', 'info');
        logMessage('提示: 连接成功后会自动添加常用监听器', 'info');
    </script>
</body>
</html>
