﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class CreateSuggestionProcWeightRuleCommandHandler(
    ISuggestionProcPointConfigRepository suggestionProcWeightConfigRepository) : 
    IRequestHandler<CreateSuggestionProcWeightMatchRuleCommand>
{
    public Task Handle(CreateSuggestionProcWeightMatchRuleCommand request, CancellationToken cancellationToken)
    {
        var (configId, dto) = request;

        var list = dto.Select(x => new SuggestionProcPointConfig
        {
            TargetCtrlProcId = x.TargetCtrlProcId,
            TargetProcMark = x.TargetProcMark,
            TrademarkBonusConfigId = configId,
            IsEnable = true
        }).ToList();
        
        return suggestionProcWeightConfigRepository.InsertAsync(list, cancellationToken);
    }
}