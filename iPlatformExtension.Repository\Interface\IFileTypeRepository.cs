﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface IFileTypeRepository : IBaseRepository<BasFileType, string>, IRedisCacheableRepository<string, BasFileType>, IScopeDependency
{
    Task<BasFileType?> ICacheableRepository<string, BasFileType>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Select.Where(type => type.FileTypeId == key).FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasFileType>> ICacheableRepository<string, BasFileType>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasFileType>.Generate<PERSON><PERSON>(BasFileType value)
    {
        return value.FileTypeId;
    }
}