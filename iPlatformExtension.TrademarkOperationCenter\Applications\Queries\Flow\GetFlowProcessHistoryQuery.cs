﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using MediatR;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow
{
    [Serializable]
    public class GetFlowProcessHistoryQuery : QueryBase, IRequest<PageResult<GetFlowProcessHistoryDto>>
    {

        /// <summary>
        /// 模糊查询
        /// </summary>
        public string? SearchKey { get; set; }

        /// <summary>
        ///  流程类型
        /// </summary>
        public string FFlowType { get; set; }


        /// <summary>
        ///  案件类型
        /// </summary>
        public string FSubType { get; set; }
    }
}
