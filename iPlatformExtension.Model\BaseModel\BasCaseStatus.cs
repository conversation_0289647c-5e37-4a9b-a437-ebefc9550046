using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_case_status", DisableSyncStructure = true)]
	public partial class BasCaseStatus {

		/// <summary>
		/// 案件状态ID
		/// </summary>
		[ Column(Name = "case_status_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CaseStatusId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 案件任务流程状态编码
		/// </summary>
		[ Column(Name = "case_code", StringLength = 50)]
		public string CaseCode { get; set; }

		/// <summary>
		/// 案件状态类型编码(被包含于案件状态类型)
		/// </summary>
		[ Column(Name = "case_status_code", StringLength = 50)]
		public string CaseStatusCode { get; set; }

		/// <summary>
		/// 案件状态英文名称
		/// </summary>
		[ Column(Name = "case_status_en_us", StringLength = 50)]
		public string CaseStatusEnUs { get; set; }

		/// <summary>
		/// 案件状态日文名称
		/// </summary>
		[ Column(Name = "case_status_ja_jp", StringLength = 50)]
		public string CaseStatusJaJp { get; set; }

		/// <summary>
		/// 案件状态类型
		/// </summary>
		[ Column(Name = "case_status_type", StringLength = 50)]
		public string CaseStatusType { get; set; }

		/// <summary>
		/// 案件状态中文名称
		/// </summary>
		[ Column(Name = "case_status_zh_cn", StringLength = 50)]
		public string CaseStatusZhCn { get; set; }

		/// <summary>
		/// 案件类型
		/// </summary>
		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		/// <summary>
		/// 分类编码
		/// </summary>
		[ Column(Name = "category_code", StringLength = 50)]
		public string CategoryCode { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
