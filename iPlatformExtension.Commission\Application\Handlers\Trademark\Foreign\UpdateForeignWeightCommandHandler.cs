﻿using AutoMapper;
using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;
using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class UpdateForeignWeightCommandHandler(
    IMapper mapper,
    IHttpContextAccessor httpContextAccessor,
    IForeignTrademarkBonusRepository foreignTrademarkBonusRepository) 
    : IRequestHandler<UpdateForeignWeightCommand>
{
    public async Task Handle(UpdateForeignWeightCommand request, CancellationToken cancellationToken)
    {
        var userId = httpContextAccessor.HttpContext?.User.GetUserId();
        var (procId, patch) = request;
        
        var bonus = await foreignTrademarkBonusRepository.GetAsync(procId, cancellationToken);
        if (bonus == null)
        {
            throw new NotFoundException(procId, "出口商标权值");
        }

        var dto = mapper.Map<ForeignWeightPatchDto>(bonus);
        patch.ApplyTo(dto);
        mapper.Map(dto, bonus);

        bonus.Updater = userId ?? string.Empty;
        bonus.UpdateTime = DateTime.Now;
        
        await foreignTrademarkBonusRepository.UpdateAsync(bonus, cancellationToken);
        
    }
}