﻿namespace iPlatformExtension.Public.Applications.Models.Customer;


/// <summary>
/// 联系类别Dto
/// </summary>
/// <param name="ContactTypeId">联系类别id</param>
/// <param name="ContactTypeZhCn">联系类别中文</param>
/// <param name="ContactTypeEnUs">联系类别英文</param>
/// <param name="ContactTypeJaJp">联系类别日文</param>
public record SearchContactTypeDto(string? ContactTypeId, string? ContactTypeZhCn, string? ContactTypeEnUs, string? ContactTypeJaJp);

