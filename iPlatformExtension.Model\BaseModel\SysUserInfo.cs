using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_user_info", DisableSyncStructure = true)]
	public partial class SysUserInfo {

		[ Column(Name = "user_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string UserId { get; set; }

		[ Column(Name = "address", StringLength = 500)]
		public string Address { get; set; }

		[ Column(Name = "age")]
		public int? Age { get; set; }

		[ Column(Name = "birthday", StringLength = 50)]
		public string Birthday { get; set; }

		[ Column(Name = "card_no", StringLength = 50)]
		public string CardNo { get; set; }

		[ Column(Name = "card_type", StringLength = 50)]
		public string CardType { get; set; }

		[ Column(Name = "cn_name", StringLength = 50, IsNullable = false)]
		public string CnName { get; set; }

		[ Column(Name = "contract_date")]
		public DateTime? ContractDate { get; set; }

		[ Column(Name = "contract_start")]
		public DateTime? ContractStart { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "crm_account_id", StringLength = 50)]
		public string CrmAccountId { get; set; }

		[ Column(Name = "crm_profile_id", StringLength = 50)]
		public string CrmProfileId { get; set; }

		/// <summary>
		/// 部门id
		/// </summary>
		[Column(Name = "dept_id", StringLength = 50)]
		public string DeptId { get; set; } = null!;

		[ Column(Name = "display_number", StringLength = 50)]
		public string DisplayNumber { get; set; }

		[ Column(Name = "driver_expire_date")]
		public DateTime? DriverExpireDate { get; set; }

		[ Column(Name = "driver_license_date")]
		public DateTime? DriverLicenseDate { get; set; }

		[ Column(Name = "driver_license_type", StringLength = 50)]
		public string DriverLicenseType { get; set; }

		[ Column(Name = "email", StringLength = 200)]
		public string Email { get; set; }

		[ Column(Name = "en_name", StringLength = 50)]
		public string EnName { get; set; }

		[ Column(Name = "entry_date")]
		public DateTime? EntryDate { get; set; }

		[ Column(Name = "expire_date")]
		public DateTime? ExpireDate { get; set; }

		[ Column(Name = "fa_push")]
		public bool FaPush { get; set; } = true;

		[ Column(Name = "fax", StringLength = 50)]
		public string Fax { get; set; }

		[ Column(Name = "gender", StringLength = 50)]
		public string Gender { get; set; }

		[ Column(Name = "high_education", StringLength = 500)]
		public string HighEducation { get; set; }

		[ Column(Name = "host_number", StringLength = 50)]
		public string HostNumber { get; set; }

		[ Column(Name = "is_agent")]
		public bool? IsAgent { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_free_trial")]
		public bool? IsFreeTrial { get; set; } = false;

		[ Column(Name = "is_login")]
		public bool IsLogin { get; set; } = true;

		[ Column(Name = "is_marry", StringLength = 50)]
		public string IsMarry { get; set; }

		[ Column(Name = "is_qc")]
		public bool? IsQc { get; set; } = true;

		[ Column(Name = "is_qc_teacher")]
		public bool? IsQcTeacher { get; set; } = false;

		[ Column(Name = "language", StringLength = 50)]
		public string Language { get; set; }

		[ Column(Name = "leave_date")]
		public DateTime? LeaveDate { get; set; }

		[ Column(Name = "manage_company", StringLength = 50)]
		public string? ManageCompany { get; set; }

		[ Column(Name = "mobile", StringLength = 50)]
		public string Mobile { get; set; }

		[ Column(Name = "nation", StringLength = 50)]
		public string Nation { get; set; }

		[ Column(Name = "native_place", StringLength = 500)]
		public string NativePlace { get; set; }

		[ Column(Name = "native_type", StringLength = 50)]
		public string NativeType { get; set; }

		[ Column(Name = "new_password", StringLength = 50)]
		public string NewPassword { get; set; }

		[ Column(Name = "new_user", StringLength = 50)]
		public string NewUser { get; set; }

		[ Column(Name = "pay_mode", StringLength = 50)]
		public string PayMode { get; set; }

		[ Column(Name = "political_status", StringLength = 50)]
		public string PoliticalStatus { get; set; }

		[ Column(Name = "private_email", StringLength = 100)]
		public string PrivateEmail { get; set; }

		[ Column(Name = "qq", StringLength = 50)]
		public string Qq { get; set; }

		[ Column(Name = "registered_permanent", StringLength = 500)]
		public string RegisteredPermanent { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "security_answer", StringLength = 50)]
		public string SecurityAnswer { get; set; }

		[ Column(Name = "security_question", StringLength = 50)]
		public string SecurityQuestion { get; set; }

		[ Column(Name = "socket_token", StringLength = 50)]
		public string SocketToken { get; set; }

		[ Column(Name = "specialty", StringLength = 300)]
		public string Specialty { get; set; }

		[ Column(Name = "sub_tel", StringLength = 50)]
		public string SubTel { get; set; }

		[ Column(Name = "sys_user_name", StringLength = 200)]
		public string SysUserName { get; set; }

		[ Column(Name = "tel", StringLength = 50)]
		public string Tel { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "urgent_way", StringLength = 1000)]
		public string UrgentWay { get; set; }

		[ Column(Name = "user_duty", StringLength = 50)]
		public string UserDuty { get; set; }

		[ Column(Name = "user_name", StringLength = 50)]
		public string UserName { get; set; }

		[ Column(Name = "user_pass", StringLength = 50)]
		public string UserPass { get; set; }

		[ Column(Name = "user_type")]
		public int UserType { get; set; } = 1;

		[ Column(Name = "wechart", StringLength = 50)]
		public string Wechart { get; set; }

		[ Column(Name = "work_no", StringLength = 50)]
		public string WorkNo { get; set; }

		[ Column(Name = "work_type", StringLength = 50)]
		public string WorkType { get; set; }

		[ Column(Name = "zkt_ssn", StringLength = 50)]
		public string ZktSsn { get; set; }

		/// <summary>
		/// 导师id
		/// </summary>
		[Column(Name = "supervisor_user_id")]
		public string? SupervisorUserId { get; set; }

		/// <summary>
		/// 导师
		/// </summary>
		[Navigate(nameof(SupervisorUserId))]
		public virtual SysUserInfo? Mentor { get; set; }

		/// <summary>
		/// 角色集合
		/// </summary>
		[Navigate(ManyToMany = typeof(SysUserRole))]
		public virtual ICollection<SysRoleInfo>? Roles { get; set; }
	}

}
