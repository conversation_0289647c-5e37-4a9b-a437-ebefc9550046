using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "qua_batch_list", DisableSyncStructure = true)]
	public partial class QuaBatchList {

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "batch_no", StringLength = 50)]
		public string BatchNo { get; set; }

		[ Column(Name = "batch_status", StringLength = 50)]
		public string BatchStatus { get; set; }

		[ Column(Name = "batch_title", StringLength = 50)]
		public string BatchTitle { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "remark", StringLength = 1000)]
		public string Remark { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
