using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Clients.Phoenix;

public class PhoenixClientFactory(IHttpClientFactory httpClientFactory, IServiceProvider services)
{
    private readonly ObjectFactory _activator = ActivatorUtilities.CreateFactory(typeof(PhoenixClient), 
    [typeof(HttpClient), typeof(string)]);

    public PhoenixClient CreateClient(string agencyKey = "Phoenix")
    {
        var httpClient = httpClientFactory.CreateClient(agencyKey);
        return (PhoenixClient) _activator(services, [httpClient, agencyKey]);
    }
}