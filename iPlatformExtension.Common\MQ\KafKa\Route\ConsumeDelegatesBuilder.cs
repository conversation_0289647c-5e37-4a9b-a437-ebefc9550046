﻿using System.Linq.Expressions;
using System.Reflection;
using iPlatformExtension.Common.MQ.KafKa.Consumer;
using iPlatformExtension.Common.MQ.KafKa.Models;

namespace iPlatformExtension.Common.MQ.KafKa.Route;

internal class ConsumeDelegatesBuilder<TMessage>
{
    private readonly Dictionary<MessageKey, ConsumeDelegateValue<TMessage>> _routeValues = new(new MessageKeyEqualityComparer());

    public ConsumeDelegatesBuilder<TMessage> AddConsumer<TConsumer>()
    {
        var consumerName = string.Empty;

        var consumerType = typeof(TConsumer);
        var consumerAttribute = consumerType.GetCustomAttribute<ConsumerAttribute>();
        if (consumerAttribute is not null)
        {
            consumerName = consumerAttribute.Consumer;
        }

        var consumeMethods = consumerType.GetMethods(BindingFlags.Instance | BindingFlags.Public)
            .Where(method => method.ReturnParameter.ParameterType.IsAssignableFrom(typeof(Task)))
            .Where(method =>
            {
                var parameters = method.GetParameters();
                return parameters.Length == 1 && parameters[0].ParameterType.IsAssignableFrom(typeof(TMessage));
            });

        foreach (var consumeMethod in consumeMethods)
        {
            var operationAttribute = consumeMethod.GetCustomAttribute<OperationAttribute>();
            var operationName = operationAttribute?.Operation ?? consumeMethod.Name;

            var messageParameterInfo = consumeMethod.GetParameters()[0];
            var consumerArg = Expression.Parameter(typeof(object), "consumer");
            var messageArg = Expression.Parameter(messageParameterInfo.ParameterType, "message");
            Expression instanceArg = consumerArg;
            if (consumeMethod.DeclaringType != null && consumeMethod.DeclaringType != typeof(object))
            {
                instanceArg = Expression.Convert(consumerArg, consumeMethod.DeclaringType);
            }

            var consumeCall = Expression.Call(instanceArg, consumeMethod, messageArg);
            var lambda = Expression.Lambda<ConsumeDelegate<TMessage>>(consumeCall, consumerArg,messageArg);
            var consumeDelegate = lambda.Compile();

            var addResult = _routeValues.TryAdd(new MessageKey(consumerName, operationName),
                new ConsumeDelegateValue<TMessage>(consumeDelegate, typeof(TConsumer)));
            if (!addResult)
            {
                throw new InvalidOperationException("消费者路由添加失败！可能存在相同路由");
            }
        }

        return this;
    }

    public ConsumeDelegates<TMessage> Build() => new (_routeValues);
}