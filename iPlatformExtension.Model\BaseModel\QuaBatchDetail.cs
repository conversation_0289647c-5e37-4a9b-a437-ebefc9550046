using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "qua_batch_detail", DisableSyncStructure = true)]
	public partial class QuaBatchDetail {

		[ Column(Name = "detail_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DetailId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "accident_level", StringLength = 50)]
		public string AccidentLevel { get; set; }

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "evaluation", StringLength = 1000)]
		public string Evaluation { get; set; }

		[ Column(Name = "examine_due_date")]
		public DateTime? ExamineDueDate { get; set; }

		[ Column(Name = "examine_user_id", StringLength = 50)]
		public string ExamineUserId { get; set; }

		[ Column(Name = "finish_date")]
		public DateTime? FinishDate { get; set; }

		[ Column(Name = "flow_cur_user_id", StringLength = 50)]
		public string FlowCurUserId { get; set; }

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		[ Column(Name = "flow_update_time")]
		public DateTime? FlowUpdateTime { get; set; }

		[ Column(Name = "is_recheck")]
		public bool? IsRecheck { get; set; } = false;

		[ Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
		public string ProcId { get; set; }

		[ Column(Name = "quality_type", StringLength = 50, IsNullable = false)]
		public string QualityType { get; set; }

		[ Column(Name = "recheck_detail_id", StringLength = 50)]
		public string RecheckDetailId { get; set; }

		[ Column(Name = "reject_reason", StringLength = 500)]
		public string RejectReason { get; set; }

		[ Column(Name = "remark", StringLength = 1000)]
		public string Remark { get; set; }

		[ Column(Name = "remark_num")]
		public int? RemarkNum { get; set; }

		[ Column(Name = "score")]
		public int? Score { get; set; }

		[ Column(Name = "status", StringLength = 50)]
		public string Status { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
