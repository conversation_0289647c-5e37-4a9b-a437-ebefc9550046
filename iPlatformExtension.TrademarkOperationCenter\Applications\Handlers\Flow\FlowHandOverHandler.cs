﻿using Grpc.Core;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;
using System.Xml.Linq;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public sealed class FlowHandOverHandler : IRequestHandler<FlowHandOverCommand, bool>
    {
        private readonly IFreeSql _freeSql;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ICaseAllotRepository _caseAllotRepository;

        public FlowHandOverHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor, ICaseAllotRepository caseAllotRepository)
        {
            _freeSql = freeSql;
            _httpContextAccessor = httpContextAccessor;
            _caseAllotRepository = caseAllotRepository;
        }

        public async Task<bool> Handle(FlowHandOverCommand request, CancellationToken cancellationToken)
        {
            var userid = _httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var CnName = _httpContextAccessor.HttpContext?.User.GetGivenName() ?? string.Empty;
            var flowActivity = await _freeSql.Select<SysFlowActivity>().Where(fa => fa.ObjId == request.ObjId && fa.FlowType == request.FFlowType).FirstAsync(cancellationToken);
            if (flowActivity != null)
            {
                flowActivity.CurUserId = request.FAuditUserID;
                flowActivity.UpdateUserId = userid;
                flowActivity.UpdateTime = DateTime.Now;
                flowActivity.PrevAuditTypeId = SubmitTypeEnum.Handover;
                flowActivity.PrivateId = string.Empty;
                await _freeSql.Update<SysFlowActivity>().SetSource(flowActivity).ExecuteAffrowsAsync(cancellationToken);

                var allotInfo = await _caseAllotRepository.Where(o => o.ProcId == request.ObjId).ToOneAsync(cancellationToken);
                if (allotInfo != null)
                {
                    allotInfo.FlowCurUserId = request.FAuditUserID;
                    allotInfo.FlowUpdateTime = DateTime.Now;
                    allotInfo.UpdateUserId = userid;
                    allotInfo.UndertakeUserId = string.Empty;
                    await _caseAllotRepository.UpdateAsync(allotInfo);
                }

                var history = new SysFlowHistory()
                {
                    HistoryId = Guid.NewGuid().ToString(),
                    ObjId = flowActivity.ObjId,
                    FlowType = flowActivity.FlowType,
                    FlowSubType = flowActivity.FlowSubType,
                    NodeId = flowActivity.CurNodeId,
                    AuditUserId = userid,
                    AuditCnName = CnName,
                    AuditTime = DateTime.Now,
                    Remark = request.FRemark,
                    AuditTypeId = SubmitTypeEnum.Handover
                };
                await _freeSql.Insert<SysFlowHistory>(history).ExecuteAffrowsAsync(cancellationToken);
                return true;
            }
            return false;


        }

    }
}
