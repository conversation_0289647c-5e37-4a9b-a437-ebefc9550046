using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "con_batch_detail", DisableSyncStructure = true)]
	public partial class ConBatchDetail {

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; }

		[ Column(Name = "detail_id", StringLength = 50, IsNullable = false)]
		public string DetailId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "notice_id", StringLength = 50, IsNullable = false)]
		public string NoticeId { get; set; }

	}

}
