using iPlatformExtension.Common.ServiceDiscovery.LoadBalance;
using Microsoft.Extensions.ServiceDiscovery;

namespace CommonTest.Models;

public class TestEndpointSelector : IServiceEndpointSelector
{
    /// <inheritdoc />
    public void SetEndpoints(ServiceEndpointSource endpoints)
    {
        Console.WriteLine("Set Endpoints");
    }

    /// <inheritdoc />
    public ServiceEndpoint GetEndpoint(object? context)
    {
        Console.WriteLine("Get Endpoints");
        return null;
    }
}