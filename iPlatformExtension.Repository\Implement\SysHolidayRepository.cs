﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement
{
    public class SysHolidayRepository : DefaultRepository<SysHoliday, string>, ISysHolidayRepository
    {
        /// <inheritdoc />
        public SysHolidayRepository(IFreeSql fsql, UnitOfWorkManager uowManger) : base(fsql, uowManger)
        {
        }
    }
}
