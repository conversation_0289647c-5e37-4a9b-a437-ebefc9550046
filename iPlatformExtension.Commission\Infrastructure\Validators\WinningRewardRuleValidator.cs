﻿using FluentValidation;
using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Common.Validation.Validators;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Commission.Infrastructure.Validators;

internal sealed class WinningRewardRuleValidator : AbstractValidator<RuleDto>
{

    public WinningRewardRuleValidator(ISystemDictionaryRepository dictionaryRepository, IBaseCtrlProcRepository baseCtrlProcRepository)
    {
        var rulingResultValidator =
            new SystemDictionaryValueValidator<RuleDto>(dictionaryRepository, SystemDictionaryName.RulingResult);
        var caseDirectionValidator = new SystemDictionaryValueValidator<RuleDto>(dictionaryRepository, SystemDictionaryName.CaseDirection);
        var dateTypeValidator = new SystemDictionaryValueValidator<RuleDto>(dictionaryRepository, SystemDictionaryName.WinningRewardDateType);
        var ctrlProcIdValidator = new CtrlProcIdValidator<RuleDto>(baseCtrlProcRepository);
        
        RuleFor(dto => dto.RulingResult).SetAsyncValidator(rulingResultValidator);
        RuleFor(dto => dto.CaseDirection).SetAsyncValidator(caseDirectionValidator);
        RuleFor(dto => dto.DateType).SetAsyncValidator(dateTypeValidator);
        RuleFor(dto => dto.CtrlProcId).SetAsyncValidator(ctrlProcIdValidator);
    }
}