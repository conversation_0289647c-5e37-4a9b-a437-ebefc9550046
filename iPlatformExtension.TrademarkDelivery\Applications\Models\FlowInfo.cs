﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.TrademarkDelivery.Applications.Models;

public class FlowInfo
{
    [JsonPropertyName("curNodeId")]
    public string CurrentNodeId { get; set; } = default!;

    public string FlowId { get; set; } = default!;

    public string FlowType { get; set; } = default!;

    public string FlowSubType { get; set; } = default!;

    [JsonPropertyName("objId")]
    public string ObjectId { get; set; } = default!;

    [JsonPropertyName("rejectNodeId")]
    public string? PreviousNodeId { get; set; }

    [JsonPropertyName("rejectUserId")]
    public string? PreviousUserId { get; set; }
}