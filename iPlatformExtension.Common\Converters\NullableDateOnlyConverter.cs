﻿using System.Text.Json;
using System.Text.Json.Serialization;
using FreeSql.DataAnnotations;
using FreeSql.Internal.Model.Interface;

namespace iPlatformExtension.Common.Converters;

public class NullableDateOnlyConverter : JsonConverter<DateOnly?>, ITypeHandler
{
    public override DateOnly? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.Null)
            return null;
        return DateOnly.FromDateTime(reader.GetDateTime());
    }

    public override void Write(Utf8JsonWriter writer, DateOnly? value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value?.ToString("yyyy-MM-dd"));
    }

    public object Deserialize(object value)
    {
        if (value is DateTime dateTime)
        {
            return DateOnly.FromDateTime(dateTime);
        }

        return null!;
    }

    public object Serialize(object value)
    {
        if (value is DateOnly date)
        {
            return date.ToDateTime(TimeOnly.MinValue, DateTimeKind.Local);
        }

        return null!;
    }

    public void FluentApi(ColumnFluent col)
    {
        col.MapType(typeof(DateTime)).DbType("datetime");
    }
}