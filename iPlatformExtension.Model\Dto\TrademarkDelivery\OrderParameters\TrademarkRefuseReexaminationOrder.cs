﻿using System.Text.Json.Serialization;
using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;

/// <summary>
/// 商标驳回复审申请
/// </summary>
public sealed class TrademarkRefuseReexaminationOrder : PhoenixOrderRequestParameters
{

    /// <summary>
    /// 申请人附件信息
    /// </summary>
    public IEnumerable<ApplicantAttachment> ApplicantAttachments { get; set; } = Array.Empty<ApplicantAttachment>();

    /// <summary>
    /// 序列化后附件信息
    /// </summary>
    [JsonSerializationSource(nameof(ApplicantAttachments))]
    public string? Attachments { get; private set; }

    /// <summary>
    /// 阐述事实与理由
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? FactReason { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否补充材料
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? IsReplenish { get; set; }

    /// <summary>
    /// 递交类型： 0 纸质递交，1 电子递交
    /// </summary>
    public int SubmitType { get; set; } = 1;

    /// <summary>
    /// 纸质递交时，国家知识产权局发文号
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? SubmitTypeNo { get; set; }

    /// <summary>
    /// 驳回复审申请人信息
    /// </summary>
    public RefuseReexaminationApplicationInfo ApplicationInfo { get; set; } = default!;

    /// <summary>
    /// 序列化后的申请人信息
    /// </summary>
    [JsonSerializationSource(nameof(ApplicationInfo))]
    [JsonPropertyName("transferParam")]
    public string? Applicant { get; private set; }

    /// <summary>
    /// 商标商品信息
    /// </summary>
    public IEnumerable<BrandInfo> BrandInfos { get; set; } = Array.Empty<BrandInfo>();

    /// <summary>
    /// 序列化后的商品信息
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonSerializationSource(nameof(BrandInfos))]
    public string? BrandTransferParam { get; private set; }

    /// <summary>
    /// 驳回通知书
    /// </summary>
    public Attachments? RefuseNotification { get; set; }

    /// <summary>
    /// 序列化后的驳回通知书
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonSerializationSource(nameof(RefuseNotification))]
    public string? BrandOfficial { get; private set; }

    /// <summary>
    /// 代理机构签字
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? AgentPerson { get; set; }

    /// <summary>
    /// 代理人
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? AgentOrganConName { get; set; }
}