﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery;
using MediatR;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery;

internal sealed class ResetOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IDeliveryInfoRepository deliveryInfoRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    UnitOfWorkManager unitOfWorkManager,
    ILoggerFactory loggerFactory,
    PhoenixClientFactory phoenixClientFactory)
    :
        PhoenixDeliveryHandleBase<ResetOrderCommand, CancelOrderParameters, PhoenixResponseParameters<OrderData>>(
            stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository, unitOfWorkManager, loggerFactory,
            phoenixClientFactory),
        IDeliveryHandler<ResetOrderCommand, CancelOrderParameters, PhoenixResponseParameters<OrderData>>

{
    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.StartupDelivery;

    DeliInfo? IDeliveryHandler<ResetOrderCommand, CancelOrderParameters, PhoenixResponseParameters<OrderData>>.DeliveryInfo
    {
        get => _deliveryInfo;
        set => _deliveryInfo = value;
    }

    async Task<DeliInfo?> IRequestHandler<ResetOrderCommand, DeliInfo?>.Handle(ResetOrderCommand command, CancellationToken cancellationToken)
    {
        await Handler.InitializeAsync(command, cancellationToken);

        if (string.IsNullOrWhiteSpace(_deliveryInfo?.OrderNo))
        {
            Logger.LogInformation("当前订单号{OrderNo}", _deliveryInfo?.OrderNo);
            return DeliveryInfo;
        }

        var deliveryStatus = (DeliveryStatus) (_deliveryInfo.Status ?? 0);
        if (deliveryStatus >= DeliveryStatus.Delivering)
            return DeliveryInfo;

        try
        {
            var request = await CreateParameterAsync(cancellationToken);
            var response = await HandleRemoteDeliveryAsync(request, cancellationToken);
            if (response is null)
                throw new InvalidDataException("can not get response from remote delivery host!");
            await HandleDeliveryInfoAsync(response, cancellationToken);
        }
        catch (Exception e)
        {
            if (!await Handler.HandleExceptionInternalAsync(e, false, true, cancellationToken))
            {
                throw;
            }
        }

        return DeliveryInfo;
    }

    public override Task<CancelOrderParameters> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        return Task.FromResult(new CancelOrderParameters()
        {
            OrderNo = _deliveryInfo.OrderNo
        });
    }

    public override Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(CancelOrderParameters request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_phoenixClient);
        return _phoenixClient.CancelOrderAsync(request);
    }

    public override async Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);

        const int deliveryStatus = (int) DeliveryStatus.Ready;

        await Handler.UpdateDeliveryInfoInternalAsync(deliveryInfo =>
        {
            deliveryInfo.OrderNo = string.Empty;
            deliveryInfo.Status = deliveryStatus;
            deliveryInfo.OperationResult = true;
            return ValueTask.CompletedTask;
        }, cancellationToken: cancellationToken);
    }
}