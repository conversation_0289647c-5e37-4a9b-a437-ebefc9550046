﻿using System.Collections.Concurrent;
using System.Runtime.CompilerServices;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Common.Cache;

/// <summary>
/// 可缓存仓储。
/// 针对字典表、费项类型名称等基础信息表提供缓存接口。
/// 可以根据自定义的键类型获取缓存值，避免每次都做过多的连表（特别是字典表）影响查询效率
/// </summary>
/// <typeparam name="TKey">缓存的键类型。可以是主键的类型，或者是自定义类型</typeparam>
/// <typeparam name="TValue">缓存的值类型。一般直接取表对应的实体类</typeparam>
public interface ICacheableRepository<TKey, TValue> where TKey : notnull
{
    /// <summary>
    /// 在初始化缓存时候的一个排他锁
    /// </summary>
    /// <seealso cref="CacheLock{TValue}"/>
    private static readonly CacheLock<ICacheableRepository<TKey, TValue>> cacheLock = new ();

    /// <summary>
    /// 缓存组件
    /// </summary>
    protected IMemoryCache MemoryCache { get; }
    
    protected CacheExpirationToken<TValue> ExpirationToken { get; }

    /// <summary>
    /// <see cref="TKey"/>类型的比较接口。
    /// 对于一般基础类型的键不需要实现。
    /// 对于自定义键类型的缓存可以重新实现该接口。
    /// </summary>
    protected IEqualityComparer<TKey>? KeyEqualityComparer => null;

    /// <summary>
    /// 缓存的键名。
    /// 取<see cref="TValue"/>的类型全名
    /// </summary>
    protected string CacheKey => typeof(TValue).FullName ?? typeof(TValue).Name;

    /// <summary>
    /// 根据<see cref="TKey"/>获取<see cref="TValue"/>。
    /// 如果没有缓存则会调用<see cref="CreateCacheAsync"/>初始化缓存，再从缓存中尝试拿值。
    /// 如果初始化的缓存中还是拿不到值，那么再从数据库中根据键精确匹配去拿值，然后把拿到的值放到缓存中。
    /// </summary>
    /// <remarks>
    /// 在调用<see cref="CreateCacheAsync"/>初始化缓存中可以不加载所有缓存，这个视表的数据量而定。
    /// 所以在初始化缓存后也不一定能从缓存中拿到值
    /// </remarks>
    /// <param name="key">键</param>
    /// <param name="mustExist">指定是否必须存在。如果值不存在且<see cref="mustExist"/>为<c>true</c>则会抛出<see cref="KeyNotFoundException"/></param>
    /// <param name="keyArgumentName">运行时传入键的参数名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>缓存值</returns>
    /// <exception cref="KeyNotFoundException"><see cref="mustExist"/>为<c>true</c>且对应的缓存值不存在</exception>
    /// <exception cref="ArgumentNullException">键为空时抛出异常</exception>
    public async ValueTask<TValue?> GetCacheValueAsync(TKey key, bool mustExist = false, [CallerArgumentExpression("key")]string? keyArgumentName = null, CancellationToken cancellationToken = default)
    {
        if (key is null)
        {
            throw new ArgumentNullException(keyArgumentName);
        }
        
        if (!MemoryCache.TryGetValue<IDictionary<TKey, TValue?>>(CacheKey, out var cacheValues))
        {
            cacheValues = await CreateCacheAsync(cancellationToken);
        }

        TValue? cacheValue;
        if (cacheValues is null)
        {
            cacheValue = await GetValueFromDbAsync(key, cancellationToken);
        }
        else
        {
            if (cacheValues.TryGetValue(key, out cacheValue))
            {
                return cacheValue;
            }
            
            cacheValue = await GetValueFromDbAsync(key, cancellationToken);

            if (cacheValue is null && mustExist)
            {
                throw new KeyNotFoundException($"无法找到对应的值。 key: {key}");
            }

            if (cacheValues is ConcurrentDictionary<TKey, TValue?> values)
            {
                values.TryAdd(key, cacheValue);
            }
        }

        return cacheValue;
    }

    public virtual async ValueTask<bool> SetCacheValuesAsync(IEnumerable<TValue> values, CancellationToken cancellationToken)
    {
        var cacheData = values.ToDictionary(GenerateKey);
        if (!MemoryCache.TryGetValue<IDictionary<TKey, TValue?>>(CacheKey, out var cacheValues))
        {
            cacheValues = await CreateCacheAsync(cancellationToken);

            if (cacheValues is null)
            {
                return false;
            }
        }
        else
        {
            foreach (var (supplerId, supplier) in cacheData)
            {
                cacheValues![supplerId] = supplier;
            }
        }

        return true;
    }

    public virtual async ValueTask<bool> SetCacheValueAsync(TKey key, TValue? value, CancellationToken cancellationToken)
    {
        if (!MemoryCache.TryGetValue<IDictionary<TKey, TValue?>>(CacheKey, out var cacheValues))
        {
            cacheValues = await CreateCacheAsync(cancellationToken);
            if (cacheValues is null)
            {
                return false;
            }
        }
        
        cacheValues![key] = value;
        return true;
    }

    /// <summary>
    /// 异步移除缓存中的指定键值对。
    /// </summary>
    /// <param name="key">要移除的缓存项的键。</param>
    /// <param name="cancellationToken">用于取消操作的令牌。</param>
    /// <returns>返回一个值任务，表示操作是否成功完成。如果缓存中存在指定的键，并且成功移除，则返回 true；否则返回 false。</returns>
    public virtual ValueTask<bool> RemoveCacheValueAsync(TKey key, CancellationToken cancellationToken = default)
    {
        // 尝试从内存缓存中获取缓存值的字典，如果成功获取且字典不为空
        if (MemoryCache.TryGetValue<IDictionary<TKey, TValue?>>(CacheKey, out var cacheValues) && cacheValues is not null)
        {
            // 移除指定键的缓存值，并返回操作是否成功的任务
            return ValueTask.FromResult(cacheValues.Remove(key));
        }
        
        // 如果缓存中没有找到指定的键，返回表示操作未成功完成的任务
        return ValueTask.FromResult(false);
    }
    
    /// <summary>
    /// 创建缓存。
    /// 通过调用<see cref="GetValuesFromDbAsync"/>从数据库中加载缓存。
    /// 利用<see cref="cacheLock"/>做排斥锁，避免并发调用<see cref="GetValuesFromDbAsync"/>
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化的缓存值</returns>
    protected async Task<IDictionary<TKey, TValue?>?> CreateCacheAsync(CancellationToken cancellationToken = default)
    {
        if (!cacheLock.TryLock()) return null;
        var valueCollection = await GetValuesFromDbAsync(cancellationToken);
        var dictionaryCache = new ConcurrentDictionary<TKey, TValue?>(KeyEqualityComparer);
        foreach (var value in valueCollection)
        {
            
            dictionaryCache.TryAdd(GenerateKey(value), value);
        }
        
        var cacheValues = MemoryCache.Set(CacheKey, dictionaryCache, new MemoryCacheEntryOptions()
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(5),
            ExpirationTokens = { ExpirationToken.Refresh() }
        });
        cacheLock.ReleaseLock();
        return cacheValues;
    }

    /// <summary>
    /// 根据<see cref="TKey"/>键从数据库中查询<see cref="TValue"/>缓存值
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>缓存值</returns>
    protected Task<TValue?> GetValueFromDbAsync(TKey key, CancellationToken cancellationToken = default);

    /// <summary>
    /// 从数据库中加载全部或部分缓存值
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>缓存值集合</returns>
    protected Task<IEnumerable<TValue>> GetValuesFromDbAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据<see cref="TValue"/>缓存值构建对应的<see cref="TKey"/>的缓存键。
    /// 后续根据这个键才能找到对应的缓存值
    /// </summary>
    /// <param name="value">缓存值</param>
    /// <returns>缓存键</returns>
    protected TKey GenerateKey(TValue value);

}