﻿using System.Text.Json;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using MediatR;
using Microsoft.AspNetCore.JsonPatch.Operations;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc.Patent;

internal sealed class PatentProcCreateNotificationHandler(
    ISender sender,
    IFreeSql<PlatformFreeSql> freeSql) 
    : IMatchNotificationHandler<CdcProcNotification>
{
    public async ValueTask<bool> MatchAsync(CdcProcNotification notification, CancellationToken cancellationToken)
    {
        var payload = notification.ProcChangedInfo.Payload;
        return payload is {Op: "c", After: not null} 
               && string.IsNullOrWhiteSpace(payload.After?.ForeignAgencyId) 
               && await freeSql.Select<CaseProcInfo>(payload.After!.ProcId)
                   .InnerJoin<BasCtrlProc>((procInfo, basCtrlProc) => procInfo.CtrlProcId == basCtrlProc.CtrlProcId)
                   .InnerJoin<CaseInfo>((info, caseInfo) => info.CaseId == caseInfo.Id)
                   .Where<BasCtrlProc>(basCtrlProc => basCtrlProc.IsOutsourcing == true)
                   .Where<CaseInfo>(caseInfo => caseInfo.CaseTypeId == CaseType.Patent)
                   .Where<CaseInfo>(caseInfo => caseInfo.CaseDirection == CaseDirection.IO || caseInfo.CaseDirection == CaseDirection.OO)
                   .AnyAsync(cancellationToken);
    }

    public async Task HandleAsync(CdcProcNotification notification, CancellationToken cancellationToken)
    {
        var createdProcInfo = notification.ProcChangedInfo.Payload.After!;
        var caseId = createdProcInfo.CaseId;
        var procId = createdProcInfo.ProcId;

        var procInfo = await freeSql.Select<CaseProcInfo>(procId).ToOneAsync(info => new CaseProcInfo()
        {
            ProcId = info.ProcId,
            Version = info.Version,
            CaseInfo = new CaseInfo()
            {
                Id = info.CaseInfo.Id,
                ForeginAgencyId = info.CaseInfo.ForeginAgencyId,
                ForeginCaseNo = info.CaseInfo.ForeginCaseNo,
                ForeignSupplierRemark = info.CaseInfo.ForeignSupplierRemark
            }
        }, cancellationToken);

        var caseForeignContactorIds = await freeSql.Select<CaseForeignContactList>()
            .Where(c => c.CaseId == caseId)
            .ToListAsync(c => c.ContactId, cancellationToken);

        var namePolicy = JsonNamingPolicy.CamelCase;
        List<Operation<ProcPatchDto>> operations =
        [
            new(nameof(OperationType.Replace).ToLower(),
                $"/{namePolicy.ConvertName(nameof(ProcPatchDto.SupplierId))}",
                null, procInfo.CaseInfo.ForeginAgencyId),
            new(nameof(OperationType.Replace).ToLower(),
                $"/{namePolicy.ConvertName(nameof(ProcPatchDto.ForeignNumber))}",
                null, procInfo.CaseInfo.ForeginCaseNo ?? string.Empty),
            new(nameof(OperationType.Replace).ToLower(),
                $"/{namePolicy.ConvertName(nameof(ProcPatchDto.ForeignSupplierRemark))}",
                null, procInfo.CaseInfo.ForeignSupplierRemark)
        ];

        if (caseForeignContactorIds.Count > 0)
        {
            operations.Add(new Operation<ProcPatchDto>(nameof(OperationType.Replace).ToLower(), 
                $"/{namePolicy.ConvertName(nameof(ProcPatchDto.ContactorIds))}", 
                null, caseForeignContactorIds));
        }
        
        await sender.Send(new UpdateProcCommand(procId, procInfo.Version, new ProcJsonPatchDocument(operations)
        {
            ProcId = procId,
            Version = procInfo.Version
        }), cancellationToken);
    }
}