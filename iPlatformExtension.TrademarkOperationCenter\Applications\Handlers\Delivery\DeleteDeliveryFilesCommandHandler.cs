﻿using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DeleteDeliveryFilesCommandHandler(IDeliveryFilesRepository deliveryFilesRepository)
    : IRequestHandler<DeleteDeliveryFilesCommand>
{
    public async Task Handle(DeleteDeliveryFilesCommand request, CancellationToken cancellationToken)
    {
        await deliveryFilesRepository.DeleteAsync(file => request.FileIds.Contains(file.FileId),
            cancellationToken);
    }
}