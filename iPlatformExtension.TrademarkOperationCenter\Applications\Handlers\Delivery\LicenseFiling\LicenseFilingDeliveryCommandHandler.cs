﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.LicenseFiling;

internal sealed class LicenseFilingDeliveryCommandHandler(
    IMediator mediator, 
    IRedisCache<RedisCacheOptionsBase> redisCache, 
    IDeliveryInfoRepository deliveryInfoRepository) 
    : DeliveryCommandHandlerBase(mediator, redisCache, deliveryInfoRepository)
{
    public override Task<SendDeliveryInternalCommand?> HandleSendStartupCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        var deliveryInfo = context.DeliveryInfo;
        
        var otherInfo = deliveryInfo.OtherInfo;
        if (otherInfo?.TrademarkNiceClasses?.Split(';').Length > 1)
        {
            throw new ApplicationException("仅支持当前任务在“一个类别”的场景下自动递交。");
        }
        
        if (deliveryInfo.Applicants?.Any(applicant => applicant.IsOtherApplicant) ?? false)
        {
            throw new ApplicationException("暂不支持当前任务在“共同申请/共有商标”的场景下自动递交");
        }
        
        return base.HandleSendStartupCommandAsync(context, cancellationToken);
    }
}