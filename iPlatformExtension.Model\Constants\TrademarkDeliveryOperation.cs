﻿using System.ComponentModel;

namespace iPlatformExtension.Model.Constants;

/// <summary>
/// 商标递交操作
/// </summary>
public enum TrademarkDeliveryOperation
{
    /// <summary>
    /// 发起递交
    /// </summary>
    [Description("发起自动递交")]
    StartupDelivery,

    /// <summary>
    /// 中止递交
    /// </summary>
    [Description("中止自动递交")]
    StopDelivery,

    /// <summary>
    /// 从官方撤销递交
    /// </summary>
    [Description("撤回已递交")]
    WithdrawDelivery,

    /// <summary>
    /// 递交官方
    /// </summary>
    [Description("递交")]
    SubmitOfficial,
    
    /// <summary>
    /// 取消自动递交，转为手动递交
    /// </summary>
    [Description("转为手动递交")]
    CancelDelivery,
    
    /// <summary>
    /// 二审递交中
    /// </summary>
    [Description("重试递交中")]
    Submitting
}