﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 递交预览json
	/// </summary>
	[Table(Name = "deli_display_json", DisableSyncStructure = true)]
	public class DeliveryDisplayJson {

		/// <summary>
		/// 主键
		/// </summary>
		[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
		public int Id { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "creation_time", InsertValueSql = "getdate()")]
		public DateTime CreationTime { get; set; }

		/// <summary>
		/// 创建用户id
		/// </summary>
		[Column(Name = "creator", StringLength = 50, IsNullable = false)]
		public string Creator { get; set; } = default!;

		/// <summary>
		/// 预览json
		/// </summary>
		[Column(Name = "display_json", DbType = "ntext", IsNullable = false)]
		public string DisplayJson { get; set; } = "[]";

		/// <summary>
		/// 任务id
		/// </summary>
		[Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
		public string ProcId { get; set; } = default!;

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 更新用户id
		/// </summary>
		[Column(Name = "updater", StringLength = 50, IsNullable = false)]
		public string Updater { get; set; } = default!;

	}

}
