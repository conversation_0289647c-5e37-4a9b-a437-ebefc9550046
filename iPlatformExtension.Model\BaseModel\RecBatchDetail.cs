using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rec_batch_detail", DisableSyncStructure = true)]
	public partial class RecBatchDetail {

		[ Column(Name = "detail_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DetailId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "batch_id", StringLength = 50)]
		public string BatchId { get; set; }

		[ Column(Name = "detail_status")]
		public int? DetailStatus { get; set; } = 0;

		[ Column(Name = "file_id", StringLength = 50)]
		public string FileId { get; set; }

		[ Column(Name = "page_no")]
		public int? PageNo { get; set; }

	}

}
