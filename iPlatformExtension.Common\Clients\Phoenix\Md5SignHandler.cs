﻿using System.Net;
using System.Net.Http.Json;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using iPlatformExtension.Common.Clients.HttpMessageHandlers;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Clients.Phoenix;

public sealed class Md5SignHandler(
    IOptionsMonitor<PhoenixClientOptions> optionsMonitor,
    ObjectPool<StringBuilder> stringBuilderPool,
    ILogger<Md5SignHandler> logger)
    : NamedDelegatingHandler
{
    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var clientOptions = optionsMonitor.Get(OptionsName);
        var jsonSerializerOptions = clientOptions.SerializerOptions;
        
        if (request.Content is not JsonContent jsonContent)
            return Task.FromResult(new HttpResponseMessage(HttpStatusCode.UnsupportedMediaType));

        var signBuilder = stringBuilderPool.Get();
        signBuilder.Append(clientOptions.SecretKey);
        
        var jsonDocument =
            JsonSerializer.SerializeToDocument(jsonContent.Value, jsonContent.ObjectType, jsonSerializerOptions);
        foreach (var jsonProperty in jsonDocument.RootElement.EnumerateObject().OrderBy(property => property.Name))
        {
            if (jsonProperty.NameEquals("sign"u8) || jsonProperty.Value.GetRawText() == """
                    ""
                    """)
            {
                continue;
            }
            
            signBuilder.Append(jsonProperty.Name).Append(jsonProperty.Value);
        }

        signBuilder.Append(clientOptions.SecretKey);
        var originSign = signBuilder.ToString();

        logger.LogDebug("原签名：{OriginSign}", originSign);
        
        var signData = MD5.HashData(originSign.GetBytes(Encoding.UTF8));
        var sign = BitConverter.ToString(signData).Replace("-", string.Empty);
        
        logger.LogDebug("BitConverter权大师签名{Sign}", sign);

        if (jsonContent.Value is PhoenixRequestParameters requestParameters)
        {
            requestParameters.Sign = sign;
        }

        return base.SendAsync(request, cancellationToken);

    }
}