﻿using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.AnalysisRule;

/// <summary>
/// 邮件动作
/// </summary>
/// <param name="Action">动作：Reject/退回,Transfer/移交,Submit/提交</param>
/// <param name="MailId">邮件id</param>
/// <param name="DisplayName">办理意见</param>
/// <param name="UndertakeUserId">移交人</param>
/// <param name="MailType">动作类型：Receive/收件,Send/发件</param>
public record MailActionCommand(string Action, List<string> MailId, string? DisplayName,string? UndertakeUserId,string? MailType = "Receive") : IRequest;

