using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_fee_type_name", DisableSyncStructure = true)]
	public partial class BasFeeTypeName {

		[ Column(Name = "name_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string NameId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "apply_type_id", StringLength = 500)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "attached_type", StringLength = 50)]
		public string AttachedType { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_type", StringLength = 50)]
		public string CaseType { get; set; }

		[ Column(Name = "country", StringLength = 500)]
		public string Country { get; set; }

		[ Column(Name = "fa_classify", StringLength = 50)]
		public string FaClassify { get; set; }

		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		[ Column(Name = "invoice_name", StringLength = 50)]
		public string InvoiceName { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_setting")]
		public bool IsSetting { get; set; } = true;

		[ Column(Name = "name_brief", StringLength = 50)]
		public string NameBrief { get; set; }

		[ Column(Name = "name_code", StringLength = 50)]
		public string NameCode { get; set; }

		[ Column(Name = "name_en_us", StringLength = 1000)]
		public string NameEnUs { get; set; }

		[ Column(Name = "name_ja_jp", StringLength = 1000)]
		public string NameJaJp { get; set; }

		[ Column(Name = "name_zh_cn", StringLength = 1000)]
		public string NameZhCn { get; set; }

		[ Column(Name = "p_short_name", StringLength = 50)]
		public string PShortName { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
