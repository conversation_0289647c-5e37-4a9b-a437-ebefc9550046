﻿using System;
using System.ComponentModel;

namespace iPlatformExtension.Outsourcing.Application.Models.Proc
{
    public class MyProcInfoDto
    {
        /// <summary>
        /// 任务编号
        /// </summary>
        [Description("任务编号")]
        public string ProcNo { get; set; } = string.Empty;

        /// <summary>
        /// 任务名称
        /// </summary>
        [Description("任务名称")]
        public string ProcName { get; set; } = string.Empty;

        /// <summary>
        /// 案件名称
        /// </summary>
        [Description("案件名称")]
        public string CaseName { get; set; } = string.Empty;

        /// <summary>
        /// 国际分类
        /// </summary>
        [Description("国际分类")]
        public string TrademarkClasses { get; set; } = string.Empty;

        /// <summary>
        /// 委案日期
        /// </summary>
        [Description("委案日期")]
        public DateTime? EntrustDate { get; set; }

        /// <summary>
        /// 官方期限
        /// </summary>
        [Description("官方期限")]
        public DateTime? OfficialDeadline { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        [Description("国家")]
        public string Country { get; set; } = string.Empty;

        /// <summary>
        /// 客户id
        /// </summary>
        [Description("客户id")]
        public string CustomerId { get; set; } = string.Empty;

        /// <summary>
        /// 客户
        /// </summary>
        [Description("客户名称")]
        public string Customer { get; set; } = string.Empty;

        /// <summary>
        /// 管理分所
        /// </summary>
        [Description("管理分所")]
        public string ManageCompany { get; set; } = string.Empty;

        /// <summary>
        /// 版本号
        /// </summary>
        [Description("版本号")]
        public int Version { get; set; }
    }
}
