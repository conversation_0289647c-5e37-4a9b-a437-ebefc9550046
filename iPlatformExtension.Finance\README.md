# iPlatformExtension.Finance

## 环境变量

| 名称  |      值      |              说明               |
|:---:|:-----------:|:-----------------------------:|
| 开发  | Development | 本地调试：在launchSettings.json中设置。 |
| 预发布 |   Staging   | 101测试环境/95开发环境：Web.config中配置  |
| 生产  |   Product   |      线上环境：Dockerfile中配置       |

## 服务发现与注册
开发环境不启用nacos服务注册。预发布（测试）环境，程序注册到相应的namespace中。
对应的nacos配置在***appsettings.{Enviroment}.json***中的`nacos`节点。
预发布环境的配置如下：
```json
{
  "nacos": {
    "ServerAddresses": [ "patas-dev.aciplaw.com/" ],
    "DefaultTimeOut": 15000,
    "Namespace": "dev",
    "ListenInterval": 1000,
    "ServiceName": "iPlatformExtension.Finance",
    "Listeners": [
      {
        "Optional": true,
        "DataId": "appsettings.json",
        "Group": "iPlatformExtension.Finance"
      },
      {
        "Optional": true,
        "DataId": "clients.json",
        "Group": "iPlatformExtension.Finance"
      }
    ]
  }
}
```

注意`ServerAddresses`节点和`Namespace`节点赋值对应的开发环境的值。
在***Program.cs***文件中添加如下代码，启用nacos服务发现：
````csharp
if (!builder.Environment.IsDevelopment())
{
    builder.Services.AddNacosAspNet(configuration);
}
````

## 配置中心
本地开发环境不启用nacos配置中心。配置中心的配置在***appsettings.{Enviroment}.json***中的`nacos`节点下的`Listeners`的节点。
配置文件示例如下：
```json
{
  "nacos": {
    "Listeners": [
      {
        "Optional": true,
        "DataId": "appsettings.json",
        "Group": "iPlatformExtension.Finance"
      },
      {
        "Optional": true,
        "DataId": "clients.json",
        "Group": "iPlatformExtension.Finance"
      }
    ]
  }
}
```
**nacos**配置中心允许一应用多配置项（文件）。`Listeners`下的每一项即为一个配置项/配置文件。
`DataId`对应的是每个文件名，`Group`作为分组名可以设置为对应的服务名称。 在***Program.cs***文件中添加如下代码，启用**nacos**配置中心：
````csharp
if (!builder.Environment.IsDevelopment())
{
    configuration.AddNacosV2Configuration(configuration.GetSection("nacos"));
}
````

