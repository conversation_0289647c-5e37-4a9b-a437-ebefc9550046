﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface
{
    public interface ISysFlowNodeRepository : IBaseRepository<SysFlowNode, string>, IScopeDependency, IRedisCacheableRepository<Tuple<string, string?>, SysFlowNode>
    {
        Task<SysFlowNode?> ICacheableRepository<Tuple<string, string?>, SysFlowNode>.GetValueFromDbAsync(Tuple<string, string?> key, CancellationToken cancellationToken)
        {
            return Select.Where(flowNode => flowNode.FlowType == key.Item1)
                .WhereIf(key.Item2 is not null, flowNode => flowNode.NodeCode == key.Item2).WithLock().ToOneAsync(cancellationToken)!;
        }

        async Task<IEnumerable<SysFlowNode>> ICacheableRepository<Tuple<string, string?>, SysFlowNode>.GetValuesFromDbAsync(CancellationToken cancellationToken)
        {
            return await Select.WithLock().ToListAsync(cancellationToken);
        }

        Tuple<string, string?> ICacheableRepository<Tuple<string, string?>, SysFlowNode>.GenerateKey(SysFlowNode value)
        {
            return new(value.FlowType, value.NodeCode);
        }
    }
}
