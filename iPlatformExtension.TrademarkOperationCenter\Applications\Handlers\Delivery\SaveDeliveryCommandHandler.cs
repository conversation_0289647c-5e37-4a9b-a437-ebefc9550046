﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class SaveDeliveryCommandHandler(IMediator mediator, IDeliveryInfoRepository deliveryInfoRepository)
    : IRequestHandler<SaveDeliveryCommand>
{
    public async Task Handle(SaveDeliveryCommand request, CancellationToken cancellationToken)
    {
        var (procId, operatorId, version, refresh) = request;
        var procInfo = await deliveryInfoRepository.Orm.Select<CaseProcInfo>()
            .Include(procInfo => procInfo.CaseInfo.ExtendInfo)
            .IncludeMany(procInfo => procInfo.Applicants)
            .IncludeMany(procInfo => procInfo.NiceCategories)
            .IncludeMany(procInfo => procInfo.LawBasis)
            .IncludeMany(procInfo => procInfo.CitedTrademarks)
            .Where(info => info.ProcId == procId).FirstAsync(info => new CaseProcInfo()
            {
                ProcId = info.ProcId,
                ProcNo = info.ProcNo,
                CtrlProcId = info.CtrlProcId,
                FilingType = info.FilingType,
                LegalDueDate = info.LegalDueDate,
                UndertakeUserId = info.UndertakeUserId,
                ProcUndertakeMainUserId = info.ProcUndertakeMainUserId,
                AgentUser = info.AgentUser,
                DeliveryKey = info.DeliveryKey,
                TrademarkDeliveryAgencyId = info.TrademarkDeliveryAgencyId,
                TrademarkDeliveryContactor = info.TrademarkDeliveryContactor,
                TrademarkNiceClasses = info.TrademarkNiceClasses,
                TrademarkDeliveryContactMailbox = info.TrademarkDeliveryContactMailbox,
                TrademarkDeliveryContactTel = info.TrademarkDeliveryContactTel,
                TrademarkDeliveryContactAddressCn = info.TrademarkDeliveryContactAddressCn,
                TrademarkDeliveryContactPostCode = info.TrademarkDeliveryContactPostCode,
                CaseId = info.CaseId,
                IsSplit = info.IsSplit,
                NominalChangesType = info.NominalChangesType,
                CtrlProcMark = info.CtrlProcMark,
                CtrlProcProperty = info.CtrlProcProperty,
                ContractEffectiveDate = info.ContractEffectiveDate,
                ContractTerminationDate = info.ContractTerminationDate,
                LicenseType = info.LicenseType,
                HasAbsoluteReason = info.HasAbsoluteReason,
                ExtendToSameAddress = info.ExtendToSameAddress,
                LawProvisions = info.LawProvisions,
                CitedRegisterNumbers = info.CitedRegisterNumbers,
                ReservationOfSupplementaryMaterial = info.ReservationOfSupplementaryMaterial,
                OfficialName = info.OfficialName,
                CaseInfo = new CaseInfo
                {
                    Id = info.CaseInfo.Id,
                    CaseName = info.CaseInfo.CaseName,
                    CaseNameEn = info.CaseInfo.CaseNameEn,
                    CaseDirection = info.CaseInfo.CaseDirection,
                    CaseTypeId = info.CaseInfo.CaseTypeId,
                    Volume = info.CaseInfo.Volume,
                    ApplyTypeId = info.CaseInfo.ApplyTypeId,
                    ShowMode = info.CaseInfo.ShowMode,
                    AppNo = info.CaseInfo.AppNo,
                    MultiType = info.CaseInfo.MultiType,
                    UndertakeMainUserId = info.CaseInfo.UndertakeMainUserId,
                    CustomerId = info.CaseInfo.CustomerId,
                    TrademarkClass = info.CaseInfo.TrademarkClass,
                    ExtendInfo = new CaseExtendInfo()
                    {
                        CaseId = info.CaseId,
                        CaseDescriptions = info.CaseInfo.ExtendInfo!.CaseDescriptions
                    }
                }
            }, cancellationToken);

        if (procInfo is null)
        {
            throw new NotFoundException(procId, "商标任务");
        }

        var elderDeliveryInfo = await mediator.Send(new DeleteDeliveryCommand(procId, refresh, version), cancellationToken);
        var isFirstTime = !refresh || elderDeliveryInfo is null;

        var deliveryInfo = new DeliInfo()
        {
            CreateUserId = operatorId,
            UpdateUserId = operatorId,
            CreateTime = DateTime.Now,
            UpdateTime = DateTime.Now,
            ProcId = procInfo.ProcId,
            CaseId = procInfo.CaseId,
            Status = elderDeliveryInfo?.Status ?? 0,
            OperationResult = elderDeliveryInfo?.OperationResult ?? true,
            OrderNo = elderDeliveryInfo?.OrderNo ?? string.Empty,
            DeliveryKey = procInfo.DeliveryKey ?? string.Empty,
            FlowType = FlowType.Delivery,
            FlowSubType = "TII"
        };

        await mediator.Send(new BuildTrademarkInfoCommand(deliveryInfo, procInfo), cancellationToken);
        await mediator.Send(new BuildContactCommand(deliveryInfo, procInfo), cancellationToken);
        await mediator.Send(new BuildApplicantInfoCommand(procInfo, isFirstTime), cancellationToken);
        await mediator.Send(new BuildPriorityInfoCommand(procInfo.CaseId, procInfo.ProcId), cancellationToken);
        await mediator.Publish(new BuildNiceInfoCommand(deliveryInfo, procInfo), cancellationToken);
        await mediator.Publish(new BuildOtherInfoCommand(procInfo, procInfo.CaseInfo.CaseDirection), cancellationToken);

        await deliveryInfoRepository.InsertAsync(deliveryInfo, cancellationToken);
    }
}