using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Queries.Send;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send;

/// <summary>
/// 获取邮件列表查询处理器
/// </summary>
internal sealed class GetMailListQueryHandler(
    IFreeSql<MailCenterFreeSql> freeSql,
    IHttpContextAccessor httpContextAccessor,
    IUserInfoRepository userInfoRepository) : IRequestHandler<GetMailListQuery, PageResult<GetMailListDto>>
{
    public async Task<PageResult<GetMailListDto>> Handle(GetMailListQuery request, CancellationToken cancellationToken)
    {
        // 获取当前用户ID
        var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;

        // 查询收件人、抄送人和密送人的邮件ID
        var receiverMailIds = new List<string>();
        var ccMailIds = new List<string>();
        var bccMailIds = new List<string>();
        if (!string.IsNullOrEmpty(request.MailTo))
        {
            receiverMailIds = await freeSql.Select<MailUser>()
                .Where(u => u.AddressType == "to" &&
                    (u.DisplayName != null && u.DisplayName.Contains(request.MailTo!) ||
                     u.MailAddress != null && u.MailAddress.Contains(request.MailTo!)))
                .ToListAsync(u => u.MailId, cancellationToken);
        }

        if (!string.IsNullOrEmpty(request.MailCc))
        {
            ccMailIds = await freeSql.Select<MailUser>()
                .Where(u => u.AddressType == "cc" &&
                    (u.DisplayName != null && u.DisplayName.Contains(request.MailCc!) ||
                     u.MailAddress != null && u.MailAddress.Contains(request.MailCc!)))
                .ToListAsync(u => u.MailId, cancellationToken);
        }

        if (!string.IsNullOrEmpty(request.MailBcc))
        {
            bccMailIds = await freeSql.Select<MailUser>()
                .Where(u => u.AddressType == "se" &&
                    (u.DisplayName != null && u.DisplayName.Contains(request.MailBcc!) ||
                     u.MailAddress != null && u.MailAddress.Contains(request.MailBcc!)))
                .ToListAsync(u => u.MailId, cancellationToken);
        }

        // 查询阅读人的邮件ID
        var readerMailIds = new List<string>();
        if(request.Reader is not null && request.Reader.Any()){
            // 查询符合阅读人条件的邮件ID
            readerMailIds = await freeSql.Select<MailReaderList>()
                .Where(r => request.Reader.Contains(r.UserId))
                .ToListAsync(r => r.MailId, cancellationToken);
        }

        // 直接使用四表连接查询，并使用 WhereIf 拼接条件
        var query = freeSql.Select<MailSend, MailUser, MailSendList, MailSendFlow>()
            .LeftJoin((m, u, sl, sf) => m.MailId == u.MailId && u.AddressType == "from")
            .LeftJoin((m, u, sl, sf) => m.MailId == sl.MailId)
            .LeftJoin((m, u, sl, sf) => m.MailId == sf.MailId)
            .Where((m, u, sl, sf) => m.Status == request.Status)
            .WhereIf(!string.IsNullOrEmpty(request.MailNo), (m, u, sl, sf) => m.MailNo != null && m.MailNo.Contains(request.MailNo!))
            .WhereIf(!string.IsNullOrEmpty(request.MailSubject), (m, u, sl, sf) => m.MailSubject != null && m.MailSubject.Contains(request.MailSubject!))
            .WhereIf(!string.IsNullOrEmpty(request.MailFrom), (m, u, sl, sf) =>
                u.DisplayName != null && u.DisplayName.Contains(request.MailFrom!) ||
                u.MailAddress != null && u.MailAddress.Contains(request.MailFrom!))
            .WhereIf(request.StartDate.HasValue, (m, u, sl, sf) => m.MailDate >= request.StartDate!.Value)
            .WhereIf(request.EndDate.HasValue, (m, u, sl, sf) => m.MailDate <= request.EndDate!.Value.Date.AddDays(1).AddSeconds(-1)) // 设置结束日期为当天的23:59:59
            .WhereIf(request.CreateTimeStartDate.HasValue, (m, u, sl, sf) => m.CreateTime >= request.CreateTimeStartDate!.Value)
            .WhereIf(request.CreateTimeEndDate.HasValue, (m, u, sl, sf) => m.CreateTime <= request.CreateTimeEndDate!.Value.Date.AddDays(1).AddSeconds(-1))
            .WhereIf(request.DiscardStartDate.HasValue, (m, u, sl, sf) => sf.DiscardTime >= request.DiscardStartDate!.Value)
            .WhereIf(request.DiscardEndDate.HasValue, (m, u, sl, sf) => sf.DiscardTime <= request.DiscardEndDate!.Value.Date.AddDays(1).AddSeconds(-1))
            .WhereIf(receiverMailIds.Count > 0, (m, u, sl, sf) => receiverMailIds.Contains(m.MailId))
            .WhereIf(ccMailIds.Count > 0, (m, u, sl, sf) => ccMailIds.Contains(m.MailId))
            .WhereIf(bccMailIds.Count > 0, (m, u, sl, sf) => bccMailIds.Contains(m.MailId))
            .WhereIf(request.UndertakeUser is not null && request.UndertakeUser.Any(),(m, u, sl, sf) => request.UndertakeUser.Contains(sf.UndertakeUserId))
            .WhereIf(request.AuditUser is not null && request.AuditUser.Any(),(m, u, sl, sf) => request.AuditUser.Contains(sf.AuditUser))
            .WhereIf(readerMailIds.Any(),(m, u, sl, sf) => readerMailIds.Contains(m.MailId));

        // 获取总记录数
        var totalCount = await query.CountAsync(cancellationToken);

        // 分页查询并直接映射到 GetMailListDto
        var result = await query
            .OrderByDescending((m, u, sl, sf) => m.CreateTime)
            .Page(request.PageIndex, request.PageSize)
            .ToListAsync((m, u, sl, sf) => new GetMailListDto
            {
                MailId = m.MailId,
                FileNumber = m.MailNo ?? string.Empty,
                MailSubject = m.MailSubject ?? string.Empty,
                MailFrom = m.MailFrom ?? string.Empty,
                MailFromEmail = u.MailAddress ?? string.Empty,
                RequiredProcessTime = sl.SendTime, // 使用MailSendList的SendTime作为要求发送时间
                MailDate = m.MailDate,
                Status = m.Status ?? 0,
                IsRequiredProcessTime = m.IsRequiredProcessTime ?? false,
                UndertakeUserId = sf.UndertakeUserId ?? string.Empty,
                CreateTime = m.CreateTime,
                AuditUserId = sf.AuditUser ?? string.Empty
            }, cancellationToken);

        var lastResult = await result.ToAsyncEnumerable()
            .SelectAwait(async it => {
                it.StatusName = ((SendStatusType)it.Status).GetDescription();

                // 添加承办人信息
                if (!string.IsNullOrEmpty(it.UndertakeUserId))
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    it.UndertakeUser = new
                    {
                        CnName = (await userBaseInfoRepository.GetCacheValueAsync(it.UndertakeUserId))?.CnName ?? "",
                        UserId = it.UndertakeUserId
                    };
                }

                // 添加审核人信息
                if (!string.IsNullOrEmpty(it.AuditUserId))
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    it.AuditUser = new
                    {
                        CnName = (await userBaseInfoRepository.GetCacheValueAsync(it.AuditUserId))?.CnName ?? "",
                        UserId = it.AuditUserId
                    };
                }

                return it;
            }).ToListAsync(cancellationToken);
        return new PageResult<GetMailListDto>
        {
            Data = lastResult,
            Page = request.PageIndex,
            PageSize = request.PageSize,
            Total = totalCount
        };
    }


}
