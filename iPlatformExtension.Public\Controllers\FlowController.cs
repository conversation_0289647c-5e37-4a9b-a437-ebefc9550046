﻿using iPlatformExtension.Public.Applications.Models.Flow;
using iPlatformExtension.Public.Applications.Queries.Flow;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers
{
    /// <summary>
    /// 流程控制器
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public sealed class FlowController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// 构造函数
        /// </summary>
        public FlowController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 获取流程信息
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetProcessInformation")]
        public async Task<IEnumerable<ProcessInformationDto>> GetProcessInformation([FromQuery] ProcessInformationQuery query)
        {
            return await _mediator.Send(query);
        }

        /// <summary>
        /// 获取被催稿人列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetUrgentDraftUserList")]
        public async Task<IEnumerable<GetUrgentDraftUserListDto>> GetUrgentDraftUserList([FromQuery] GetUrgentDraftUserListQuery query)
        {
            return await _mediator.Send(query);
        }

        /// <summary>
        /// 催稿历史
        /// </summary>
        /// <returns></returns>
        [HttpGet("UrgentDraftHistory")]
        public async Task<IEnumerable<UrgentDraftHistoryDto>> UrgentDraftHistory([FromQuery] UrgentDraftHistoryQuery query)
        {
            return await _mediator.Send(query);
        }
    }
}
