﻿using iPlatformExtension.Service.Interface;

namespace iPlatformExtension.Service.Implement;

internal sealed class CustomerService : ICustomerQueryService
{

    public CustomerService(IFreeSql dbQuery)
    {
        DbQuery = dbQuery;
    }

    // public async Task<IEnumerable<CustomerFollowReDto>> GetCustomerCustomerFollowListAsync(string customerId, string customerUserType)
    // {
    //     var customerFollowList = await _customerRepository.GetCustomerFollowListAsync(customerId, customerUserType);
    //     var result = new List<CustomerFollowReDto>();
    //     foreach (var customerFollow in customerFollowList)
    //     {
    //         if (!result.Any(p => p.UserId.Contains(customerFollow.UserId)))
    //         {
    //             var dto = new CustomerFollowReDto
    //             {
    //                 UserId = customerFollow.UserId,
    //                 UserName = customerFollow.UserName,
    //                 UserNum = customerFollow.UserNum
    //             };
    //             result.Add(dto);
    //         }
    //     }
    //     return result;
    // }

    public IFreeSql DbQuery { get; }
}