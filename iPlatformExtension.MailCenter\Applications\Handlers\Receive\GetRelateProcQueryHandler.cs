﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 任务关联处理者
    /// </summary>
    internal sealed class GetRelateProcQueryHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IFreeSql<PlatformFreeSql> platformFreeSql,
        IUserInfoRepository userInfoRepository,
        IBaseCtrlProcRepository baseCtrlProcRepository,
        IBasCaseProcStatusRepository basCaseProcStatusRepository,
        ISystemDictionaryRepository systemDictionaryRepository,
        IBaseCaseStatusRepository iBaseCaseStatusRepository
    ) : IRequestHandler<GetRelateProcQuery, IEnumerable<GetRelateProcDto>>
    {
        public async Task<IEnumerable<GetRelateProcDto>> Handle(
            GetRelateProcQuery request,
            CancellationToken cancellationToken
        )
        {
            var mailCorrelatives = await freeSql
                .Select<MailCorrelative>()
                .WithLock()
                .Where(it =>
                    it.MailId == request.MailId
                    && it.CorrelateType == SysEnum.CorrelateType.Proc.ToString()
                )
                .ToListAsync(
                    it => new
                    {
                        it.Id,
                        it.ObjId,
                        it.CreateBy,
                        it.CreateTime,
                    },
                    cancellationToken
                );
            var procList = mailCorrelatives.Select(it => it.ObjId);
            var getRelateProcDtos = await platformFreeSql
                .Select<CaseProcInfo>()
                .WithLock()
                .Where(it => procList.Contains(it.ProcId))
                .OrderByDescending(it => it.CreateTime)
                .Page(request.PageIndex.Value, request.PageSize.Value)
                .Count(out var count)
                .ToListAsync(
                    it => new GetRelateProcDto(
                        it.ProcId,
                        it.CaseId,
                        it.CtrlProcId,
                        it.ProcNo,
                        it.UndertakeUserId,
                        it.CaseInfo.Volume,
                        it.CaseInfo.CaseTypeId,
                        it.CaseInfo.CaseDirection,
                        it.CaseInfo.CaseName,
                        it.ProcStatusId,
                        it.CtrlProcMark,
                        it.CtrlProcProperty
                    ),
                    cancellationToken
                );
            var data = await getRelateProcDtos
                .ToAsyncEnumerable()
                .SelectAwait(async it =>
                {
                    if (it.UndertakeUserId is not null)
                    {
                        it.UndertakeUser = new
                        {
                            CnName = await userInfoRepository.GetTextValueAsync(it.UndertakeUserId),
                            UserId = it.UndertakeUserId,
                        };
                    }

                    var relate = mailCorrelatives.FirstOrDefault(x => x.ObjId == it.ProcId);
                    if (relate is not null)
                    {
                        it.CreateBy = new
                        {
                            CnName = await userInfoRepository.GetTextValueAsync(relate.CreateBy),
                            UserId = relate.CreateBy,
                        };
                        it.CreateTime = relate.CreateTime;
                        it.RelateId = relate.Id;
                    }

                    if (it.CtrlProcId is not null)
                    {
                        it.CtrlProc = new
                        {
                            CtrlProcId = it.CtrlProcId,
                            CtrlProcName = await baseCtrlProcRepository.GetTextValueAsync(
                                it.CtrlProcId
                            ),
                        };
                    }
                    if (it.ProcStatusId is not null)
                    {
                        var chineseKeyValueAsync =
                            await basCaseProcStatusRepository.GetCacheValueAsync(it.ProcStatusId);
                        it.ProcStatus = new
                        {
                            cnName = chineseKeyValueAsync!.TextZhCn,
                            id = it.ProcStatusId,
                        };
                    }
                    if (it.CtrlProcMarkId is not null)
                    {
                        it.CtrlProcMark = new
                        {
                            id = it.CtrlProcMarkId,
                            cnName = (
                                await systemDictionaryRepository.GetChineseValueAsync(
                                    SystemDictionaryName.CtrlProcMark,
                                    it.CtrlProcMarkId
                                )
                            ) ?? "",
                        };
                    }

                    if (!string.IsNullOrWhiteSpace(it.CtrlProcPropertyId))
                    {
                        var baseBasCaseStatus = await iBaseCaseStatusRepository.GetCacheValueAsync(
                            it.CtrlProcPropertyId
                        );
                        it.CtrlProcProperty = new
                        {
                            id = it.CtrlProcPropertyId,
                            cnName = baseBasCaseStatus?.CaseStatusZhCn,
                        };
                    }
                    return it;
                })
                .ToListAsync(cancellationToken: cancellationToken);
            return new PageResult<GetRelateProcDto>()
            {
                Data = data,
                Page = request.PageIndex.Value,
                PageSize = request.PageSize.Value,
                Total = count,
            };
        }
    }
}
