﻿using System.Text;
using Microsoft.Extensions.Http.Logging;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Common.Logging.HttpClient;

public class RequestHeaderLogger(Func<string, bool> redactHeader, ILogger logger, ObjectPool<StringBuilder> stringBuilderPool) : IHttpClientLogger
{
    public object? LogRequestStart(HttpRequestMessage request)
    {
        var stringBuilder = request.Headers.Aggregate(stringBuilderPool.Get(),
                (builder, header) => builder.Append(header.Key).Append(": ")
                    .Append(redactHeader(header.Key) ? "[Redacted]" : string.Join(", ", header.Value))
                    .AppendLine());
        
        var headerInfo =  request.Content?.Headers.Aggregate(stringBuilder, 
            (builder, header) => builder.Append(header.Key).Append(": ")
            .Append(redactHeader(header.Key) ? "[Redacted]" : string.Join(", ", header.Value))
            .AppendLine()).ToString();
        
        stringBuilderPool.Return(stringBuilder);
        
        logger.LogInformation("Request Headers: \n{HeaderInfo}", headerInfo);
        
        return null;
    }

    public void LogRequestStop(object? context, HttpRequestMessage request, HttpResponseMessage response, TimeSpan elapsed)
    {
        
    }

    public void LogRequestFailed(object? context, HttpRequestMessage request, HttpResponseMessage? response, Exception exception,
        TimeSpan elapsed)
    {
        
    }
}