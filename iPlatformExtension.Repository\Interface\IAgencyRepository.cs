using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface IAgencyRepository : 
    IBaseRepository<BasAgency, string>, 
    IRedisCacheableRepository<string, BasAgency>, 
    IScopeDependency,
    IStringKeyCacheableRepository<BasAgency>
{
    Task<BasAgency?> ICacheableRepository<string, BasAgency>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Orm.Select<BasAgency>().Where(agency => agency.AgencyId == key).FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasAgency>> ICacheableRepository<string, BasAgency>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Orm.Select<BasAgency>().ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasAgency>.GenerateKey(BasAgency value)
    {
        return value.AgencyId;
    }

    string? IStringKeyCacheableRepository<BasAgency>.GetCacheTextValue(BasAgency value)
    {
        return value.AgencyNameCn;
    }
}