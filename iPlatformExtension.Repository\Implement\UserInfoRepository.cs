﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement
{

    internal sealed class UserInfoRepository(
        IFreeSql<PlatformFreeSql> freeSql,
        DefaultRedisCache redisCache,
        IMemoryCache memoryCache,
        CacheExpirationToken<UserInfoDto> userNameExpirationToken,
        CacheExpirationToken<UserBaseInfo> userIdExpirationToken,
        CacheExpirationToken<SysUserInfo> userInfoExpirationToken) 
        : BaseRepository<SysUserInfo, string>(freeSql), IUserInfoRepository
    {
        
        CacheExpirationToken<SysUserInfo> ICacheableRepository<string, SysUserInfo>.ExpirationToken =>
            userInfoExpirationToken;

        public IMemoryCache MemoryCache { get; } = memoryCache;

        CacheExpirationToken<UserBaseInfo> ICacheableRepository<string, UserBaseInfo>.ExpirationToken => userIdExpirationToken;

        CacheExpirationToken<UserInfoDto> ICacheableRepository<string, UserInfoDto>.ExpirationToken => userNameExpirationToken;
        
        public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
    }
}
