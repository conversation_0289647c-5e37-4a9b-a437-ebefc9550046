﻿using Grpc.Core;
using Grpc.Core.Interceptors;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Grpc.Stream;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.Grpc.Interceptors;

public abstract class GrpcExceptionHandler<TRequest, TResponse>(ILogger logger) 
    : global::Grpc.Core.Interceptors.Interceptor where TRequest : class where TResponse : class
{
    protected ILogger Logger { get; } = logger;
    
    public sealed override async Task<TResponse1> UnaryServerHandler<TRequest1, TResponse1>(TRequest1 request, ServerCallContext context,
        UnaryServerMethod<TRequest1, TResponse1> continuation)
    {
        Logger.LogGrpcRequestParameter(request);
        
        if (request is not TRequest request1 || continuation is not UnaryServerMethod<TRequest, TResponse> next)
            return await continuation(request, context);
        
        try
        {
            var response = await next(request1, context);
            return (response as TResponse1)!;
        }
        catch (Exception e)
        {
            Logger.LogGrpcException(e);
            var response = await UnaryServerHandleAsync(request1, e, context);
            return (response as TResponse1)!;
        }
    }

    protected abstract ValueTask<TResponse> UnaryServerHandleAsync(TRequest request, Exception exception,
        ServerCallContext context);

    public sealed override async Task<TResponse1> ClientStreamingServerHandler<TRequest1, TResponse1>(IAsyncStreamReader<TRequest1> requestStream, ServerCallContext context,
        ClientStreamingServerMethod<TRequest1, TResponse1> continuation)
    {
        var loggingRequestStream = new LoggingStreamReader<TRequest1>(Logger, requestStream);
        
        if (loggingRequestStream is not IAsyncStreamReader<TRequest> requestStreamReader
            || continuation is not ClientStreamingServerMethod<TRequest, TResponse> next)
            return await continuation(loggingRequestStream, context);
        
        try
        {
            var response = await next(requestStreamReader, context);
            return (response as TResponse1)!;
        }
        catch (Exception e)
        {
            var response = await ClientStreamingServerHandleAsync(requestStreamReader, e, context);
            return (response as TResponse1)!;
        }
    }
    
    protected abstract ValueTask<TResponse> ClientStreamingServerHandleAsync(IAsyncStreamReader<TRequest> requestStreamReader, Exception exception, ServerCallContext context);

    public sealed override async Task ServerStreamingServerHandler<TRequest1, TResponse1>(TRequest1 request, IServerStreamWriter<TResponse1> responseStream,
        ServerCallContext context, ServerStreamingServerMethod<TRequest1, TResponse1> continuation)
    {
        if (request is TRequest request1 && responseStream is IServerStreamWriter<TResponse> streamWriter && continuation is ServerStreamingServerMethod<TRequest, TResponse> next)
        {
            try
            {
                await next(request1, streamWriter, context);
            }
            catch (Exception e)
            {
                Logger.LogGrpcException(e);
                await ServerStreamingServerHandleAsync(request1, streamWriter, e, context);
            }
        }
        
        await continuation(request, responseStream, context);
    }
    
    protected abstract Task ServerStreamingServerHandleAsync(TRequest request, IServerStreamWriter<TResponse> responseStream, Exception exception, ServerCallContext context);

    public sealed override async Task DuplexStreamingServerHandler<TRequest1, TResponse1>(IAsyncStreamReader<TRequest1> requestStream,
        IServerStreamWriter<TResponse1> responseStream, ServerCallContext context, DuplexStreamingServerMethod<TRequest1, TResponse1> continuation)
    {
        if (requestStream is IAsyncStreamReader<TRequest> streamReader 
            && responseStream is IServerStreamWriter<TResponse> streamWriter 
            && continuation is DuplexStreamingServerMethod<TRequest, TResponse> next)
        {
            try
            {
                await next(streamReader, streamWriter, context);
            }
            catch (Exception e)
            {
               Logger.LogGrpcException(e);
               await DuplexStreamingServerHandleAsync(streamReader, streamWriter, e, context);
            }
        }
    }
    
    protected abstract Task DuplexStreamingServerHandleAsync(IAsyncStreamReader<TRequest> reader, IServerStreamWriter<TResponse> writer, Exception exception, ServerCallContext context);

    public sealed override AsyncUnaryCall<TResponse1> AsyncUnaryCall<TRequest1, TResponse1>(TRequest1 request, ClientInterceptorContext<TRequest1, TResponse1> context,
        AsyncUnaryCallContinuation<TRequest1, TResponse1> continuation)
    {
        
        if (context is ClientInterceptorContext<TRequest, TResponse> context1 
            && continuation is AsyncUnaryCallContinuation<TRequest, TResponse> next 
            && request is TRequest request1)
        {
            var call = next(request1, context1);
            var asyncCall = new AsyncUnaryCall<TResponse>(
                CatchClientExceptionAsync(request1, call.ResponseAsync, context1), 
                call.ResponseHeadersAsync,
                call.GetStatus,
                call.GetTrailers,
                call.Dispose);
            
            return (asyncCall as AsyncUnaryCall<TResponse1>)!;
        }
        else
        {
            return continuation(request, context);
        }
    }

    private async Task<TResponse> CatchClientExceptionAsync(TRequest request, Task<TResponse> responseCallback, ClientInterceptorContext<TRequest, TResponse> context)
    {
        try
        {
            return await responseCallback;
        }
        catch (Exception e)
        {
            Logger.LogGrpcException(e);
            return await UnaryClientHandleAsync(request, e, context);
        }
    }

    protected abstract ValueTask<TResponse> UnaryClientHandleAsync(TRequest request, Exception exception,
        ClientInterceptorContext<TRequest, TResponse> context);
}