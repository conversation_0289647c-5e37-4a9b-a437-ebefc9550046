using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "oa_flow_list", DisableSyncStructure = true)]
	public partial class OaFlowList {

		/// <summary>
		/// 审核人员角色
		/// </summary>
		[ Column(Name = "auditor_type", StringLength = 500)]
		public string AuditorType { get; set; }

		/// <summary>
		/// 流程id
		/// </summary>
		[ Column(Name = "flow_id", StringLength = 50)]
		public string FlowId { get; set; }

		/// <summary>
		/// 流程节点id
		/// </summary>
		[ Column(Name = "list_id", StringLength = 50)]
		public string ListId { get; set; }

		/// <summary>
		/// 下一步
		/// </summary>
		[ Column(Name = "next", StringLength = 50)]
		public string Next { get; set; }

		/// <summary>
		/// 节点类型
		/// </summary>
		[ Column(Name = "node_type", StringLength = 50)]
		public string NodeType { get; set; }

		/// <summary>
		/// 流程限制
		/// </summary>
		[ Column(Name = "process_limit", StringLength = 50)]
		public string ProcessLimit { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
