﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Outsourcing.Application.Models.Case;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;

namespace iPlatformExtension.Outsourcing.Application.Commands.Case;

internal sealed record UpdateCaseCommand(string CaseId, JsonPatchDocument<CasePatchDto> PatchDocument) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;