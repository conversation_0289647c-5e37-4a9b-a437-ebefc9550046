﻿using System.Text.Json.Serialization;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;

/// <summary>
/// 任务预览信息
/// </summary>
public sealed class ProcItemDto
{
    /// <summary>
    /// 任务id
    /// </summary>
    public string ProcId { get; set; } = default!;

    /// <summary>
    /// 案件id
    /// </summary>
    public string CaseId { get; set; } = default!;

    /// <summary>
    /// 任务编号
    /// </summary>
    public string ProcNo { get; set; } = string.Empty;
    
    /// <summary>
    /// 任务类型id
    /// </summary>
    [JsonIgnore]
    public string CtrlProcId { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    public string ProcName { get; set; } = string.Empty;

    /// <summary>
    /// 商标名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 国际分类
    /// </summary>
    public string TrademarkClass { get; set; } = string.Empty;

    /// <summary>
    /// 代表申请人id
    /// </summary>
    [JsonIgnore]
    public string ApplicantId { get; set; } = string.Empty;

    /// <summary>
    /// 申请号
    /// </summary>
    public string AppNo { get; set; } = string.Empty;

    /// <summary>
    /// 代表申请人
    /// </summary>
    public ApplicantInfo ApplicantInfo { get; set; }

    /// <summary>
    /// 承办人id
    /// </summary>
    [JsonIgnore]
    public string? UndertakerId { get; set; }
    
    /// <summary>
    /// 承办人
    /// </summary>
    public UserBaseInfo? UndertakerInfo { get; set; }

    /// <summary>
    /// 代理机构id
    /// </summary>
    [JsonIgnore]
    public string? AgencyId { get; set; }

    /// <summary>
    /// 代理名义
    /// </summary>
    public string? AgencyName { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public int Version { get; set; }
}