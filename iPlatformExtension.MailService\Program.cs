


using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Extensions;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.OpenApi.Models;
using Nacos.AspNetCore.V2;
using NLog.Web;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Mediator.PipelineBehaviors;
using System.Reflection;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Db.FreeSQL;
using FreeSql;
using iPlatformExtension.MailService.HostedService;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.AspNetCore.Builder;
using iPlatformExtension.MailService.Extensions;
using iPlatformExtension.MailService.Infrastructure.SignalR;
using iPlatformExtension.MailService.Infrastructure;
using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.MailService.Infrastructure.MQ;

[assembly: ApplicationPart("iPlatformExtension.Common")]
var builder = WebApplication.CreateBuilder(args);


var configuration = builder.Configuration;
var environment = builder.Environment;
var logging = builder.Logging;
logging.ClearProviders();
logging.AddSimpleConsole(options => options.IncludeScopes = true);
logging.AddNLogWeb(new NLogAspNetCoreOptions
{
    RemoveLoggerFactoryFilter = false,
    LoggingConfigurationSectionName = "NLog"
});

if (!environment.IsLocal())
{
    configuration.AddNacosV2Configuration(configuration.GetSection("nacos"));
    builder.Services.AddNacosAspNet(configuration);
    builder.Services.AddNacosServiceDiscovery(options => options.AllowedSchemes = [Uri.UriSchemeHttp, Uri.UriSchemeHttps]);
    //builder.Services.AddNacosServiceClient("iPlatformExtension.Public");
}


if (!environment.IsProduction())
{
    configuration.AddJsonFile($"clients.{environment.EnvironmentName}.json", true, true);
    configuration.AddJsonFile($"mqs.{environment.EnvironmentName}.json", true, true);
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = builder.Environment.ApplicationName,
            Version = "2.8.2.RELEASE-v2.7.11",
            Description = "邮件中心后台服务"
        });

        c.OrderActionsBy(o => o.RelativePath);
    });
}

builder.Services.AddObjectPools();
builder.Services.FreeSqlInit(configuration);

builder.Services.AddDataService();
 
builder
    .Services.AddCache()
    .ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
    {
        options.ConnectionString = configuration.GetConnectionString("Redis");
        options.Converters.Add(new ClaimsIdentityConverter());
    });

builder.Services.AddMediatR(serviceConfiguration =>
{
    serviceConfiguration.Lifetime = ServiceLifetime.Scoped;
    serviceConfiguration.AddOpenBehavior(typeof(DefaultFreeSqlUnitOfWorkPipelineBehavior<,>));
    serviceConfiguration.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
});

builder.Services.AddHttpLogging(options =>
{
    options.LoggingFields |= HttpLoggingFields.RequestHeaders;
    options.LoggingFields |= HttpLoggingFields.RequestPath;
    options.LoggingFields |= HttpLoggingFields.RequestQuery;
    options.LoggingFields |= HttpLoggingFields.RequestBody;
    options.LoggingFields |= HttpLoggingFields.ResponsePropertiesAndHeaders;
    options.RequestBodyLogLimit = 327680;
});
builder.Services.AddHttpContextAccessor();
builder.Services.AddHuaweiObsClient();
builder.Services.AddControllers();
builder.Services.AddMailExtension();
builder.Services.InitSignalRService(configuration);
builder.Services.AddWxConfiguration(configuration);
builder.Services.InitKafaService(configuration);

var app = builder.Build();

if (!app.Environment.IsProduction())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseW3CTraceResponse();
// Configure the HTTP request pipeline.

app.UseAuthorization();

app.MapControllers();

app.Run();
