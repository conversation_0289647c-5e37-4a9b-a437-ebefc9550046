﻿

namespace iPlatformExtension.Model.Constants
{
    /// <summary>
    /// 返回Code
    /// </summary>
    public readonly struct ResultCode : IEquatable<ResultCode>
    {
        /// <summary>
        /// 检查当前实例与另一个指定的ResultCode实例是否相等。
        /// </summary>
        /// <param name="other">要比较的ResultCode实例。</param>
        /// <returns>如果当前实例与other相等，则返回true；否则返回false。</returns>
        public bool Equals(ResultCode other)
        {
            return _code == other._code;
        }
        
        /// <summary>
        /// 重写Object类的Equals方法，以支持与任何对象的比较。
        /// </summary>
        /// <param name="obj">要比较的对象。</param>
        /// <returns>如果当前实例与obj相等，则返回true；否则返回false。</returns>
        public override bool Equals(object? obj)
        {
            // 检查obj是否为ResultCode类型或int类型，并调用相应的Equals方法进行比较
            return obj is ResultCode other && Equals(other) || obj is int otherCode && Equals(otherCode);
        }
        
        /// <summary>
        /// 重写Object类的GetHashCode方法，提供当前实例的哈希码。
        /// </summary>
        /// <returns>当前实例的哈希码。</returns>
        public override int GetHashCode()
        {
            // 返回私有字段_code的值作为哈希码
            return _code;
        }

        /// <summary>
        /// 成功
        /// </summary>
        public static readonly ResultCode Success = 200;

        /// <summary>
        /// 业务异常
        /// </summary>
        public static readonly ResultCode Failure = 400;

        /// <summary>
        /// 编码
        /// </summary>
        private readonly int _code;

        private ResultCode(int code) => _code = code;

        /// <summary>
        /// 隐式转换
        /// 整型转换<see cref="ResultCode"/>
        /// </summary>
        /// <param name="code">整型值</param>
        /// <returns><see cref="ResultCode"/></returns>
        public static implicit operator ResultCode(int code) => new(code);

        /// <summary>
        /// 隐式转换
        /// <see cref="ResultCode"/>转整型
        /// </summary>
        /// <param name="resultCode"><see cref="ResultCode"/></param>
        /// <returns>整型值</returns>
        public static implicit operator int(ResultCode resultCode) => resultCode._code;
        
        /// <summary>
        /// 相等运算符重载
        /// </summary>
        /// <param name="x"><see cref="ResultCode"/></param>
        /// <param name="y">整型编码值</param>
        /// <returns>整型编码值与<see cref="ResultCode"/>.<see cref="_code"/>是否相等</returns>
        public static bool operator ==(ResultCode x, int y)
        {
            return x._code == y;
        }

        /// <summary>
        /// 相等运算符重载
        /// </summary>
        /// <param name="x"><see cref="ResultCode"/></param>
        /// <param name="y">整型编码值</param>
        /// <returns>整型编码值与<see cref="ResultCode"/>.<see cref="_code"/>是否相等</returns>
        public static bool operator !=(ResultCode x, int y)
        {
            return x._code != y;
        }
        
        /// <summary>
        /// 相等运算符重载
        /// </summary>
        /// <param name="x"><see cref="ResultCode"/></param>
        /// <param name="y">整型编码值</param>
        /// <returns>整型编码值与<see cref="ResultCode"/>.<see cref="_code"/>是否相等</returns>
        public static bool operator ==(int x, ResultCode y)
        {
            return x == y._code;
        }

        /// <summary>
        /// 相等运算符重载
        /// </summary>
        /// <param name="y"><see cref="ResultCode"/></param>
        /// <param name="x">整型编码值</param>
        /// <returns>整型编码值与<see cref="ResultCode"/>.<see cref="_code"/>是否相等</returns>
        public static bool operator !=(int x, ResultCode y)
        {
            return x != y._code;
        }

        /// <summary>
        /// 比较两个<see cref="ResultCode"/>是否不相等
        /// </summary>
        /// <param name="x"><see cref="ResultCode"/>1</param>
        /// <param name="y"><see cref="ResultCode"/>2</param>
        /// <returns></returns>
        public static bool operator !=(ResultCode x, ResultCode y)
        {
            return x._code != y._code;
        }

        /// <summary>
        /// 比较两个<see cref="ResultCode"/>是否相等
        /// </summary>
        /// <param name="x"><see cref="ResultCode"/>1</param>
        /// <param name="y"><see cref="ResultCode"/>2</param>
        /// <returns></returns>
        public static bool operator ==(ResultCode x, ResultCode y)
        {
            return x._code == y._code;
        }
    }
}
