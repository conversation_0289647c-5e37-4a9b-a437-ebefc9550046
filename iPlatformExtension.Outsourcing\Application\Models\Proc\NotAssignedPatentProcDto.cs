﻿using System.ComponentModel;
using System.Text.Json.Serialization;
using iPlatformExtension.Common.Converters;

namespace iPlatformExtension.Outsourcing.Application.Models.Proc;

public class NotAssignedPatentProcDto
{
        /// <summary>
    /// 任务id
    /// </summary>
    [Description("任务id")]
    public string ProcId { get; set; } = string.Empty;

    /// <summary>
    /// 我方文号
    /// </summary>
    [Description("我方文号")]
    public string Volume { get; set; }=string.Empty;
    

    /// <summary>
    /// 案件名称
    /// </summary>
    [Description("案件名称")]
    public string CaseName { get; set; } = string.Empty;

    /// <summary>
    /// 案件流向
    /// </summary>
    public string CaseDirection { get; set; } = string.Empty;

    /// <summary>
    /// 客户id
    /// </summary>
    [Description("客户id")]
    public string CustomerId { get; set; } = string.Empty;

    /// <summary>
    /// 客户名称
    /// </summary>
    [Description("客户名称")]
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// 国家
    /// </summary>
    [Description("国家/地区")]
    public string Country { get; set; } = string.Empty;

    /// <summary>
    /// 国家编码
    /// </summary>
    public string CountryId { get; set; } = string.Empty;

    /// <summary>
    /// 申请类型
    /// </summary>
    [Description("申请类型")]
    public string ApplyType { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    [Description("任务名称")]
    public string ProcName { get; set; } = string.Empty;

    /// <summary>
    /// PCT申请号
    /// </summary>
    [Description("PCT申请号")]
    public string PctAppNo { get; set; } = string.Empty;

    /// <summary>
    /// 内部初稿期限
    /// </summary>
    [JsonConverter(typeof(DateTimeToDateOnlyJsonConverter))]
    [Description("初稿期限（内）")]
    public DateTime? InternalInitialDraftDeadline { get; set; }

    /// <summary>
    /// 官方期限
    /// </summary>
    [JsonConverter(typeof(DateTimeToDateOnlyJsonConverter))]
    [Description("官方期限")]
    public DateTime? OfficialDeadline { get; set; }

    /// <summary>
    /// 剩余天数
    /// 当天加5天于内部初稿期限的时间差
    /// </summary>
    [Description("剩余天数")]
    public int RemainingDays { get; set; }

    /// <summary>
    /// 案件id
    /// </summary>
    [Description("案件id")]
    public string CaseId { get; set; } = string.Empty;

    /// <summary>
    /// 版本号
    /// </summary>
    [Description("版本号")]
    public int Version { get; set; }
}