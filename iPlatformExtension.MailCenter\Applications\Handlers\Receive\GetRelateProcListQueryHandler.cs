﻿using System.Collections.Generic;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 获取关联任务列表
    /// </summary>
    internal sealed class GetRelateProcListQueryHandler(
        IFreeSql<PlatformFreeSql> freeSql,
        IFreeSql<MailCenterFreeSql> mailFreeSql,
        IBaseCtrlProcRepository baseCtrlProcRepository,
        ISystemDictionaryRepository systemDictionaryRepository,
        IBasCaseProcStatusRepository basCaseProcStatusRepository,
        IBaseCaseStatusRepository iBaseCaseStatusRepository
    ) : IRequestHandler<GetRelateProcListQuery, IEnumerable<GetRelateProcListDto>>
    {
        public async Task<IEnumerable<GetRelateProcListDto>> Handle(
            GetRelateProcListQuery request,
            CancellationToken cancellationToken
        )
        {
            var select = freeSql
                .Select<CaseProcInfo, CaseInfo, CaseApplicantList>()
                .LeftJoin(it => it.t1.CaseId == it.t2.Id)
                .WithLock()
                .LeftJoin(it => it.t2.Id == it.t3.CaseId && it.t3.IsRepresent == true);

            var splitAndFilter = (string? input) => input?.Replace(" ", ";").Split(";");

            var volume = splitAndFilter(request.Volume);
            var procNo = splitAndFilter(request.ProcNo);
            var applyNo = splitAndFilter(request.ApplyNo);
            var appNo = splitAndFilter(request.AppNo);
            var ApplicantId = splitAndFilter(request.ApplicantId);
            var CustomerId = splitAndFilter(request.CustomerId);
            var CaseNo = splitAndFilter(request.CaseNo);
            var registerNo = splitAndFilter(request.RegisterNo);
            List<string>? caseIdList = null;
            if (
                !string.IsNullOrEmpty(request.CustomerId)
                || !string.IsNullOrEmpty(request.CaseName)
                || !string.IsNullOrEmpty(request.Volume)
                || !string.IsNullOrEmpty(request.ApplyNo)
                || !string.IsNullOrEmpty(request.AppNo)
                || !string.IsNullOrEmpty(request.RegisterNo) || !string.IsNullOrEmpty(request.CaseNo)
            )
            {
                select = select.RightJoin(it => it.t1.CaseId == it.t2.Id);
                caseIdList = await freeSql
                    .Select<CaseInfo>()
                    .WithLock()
                    .WhereIf(volume is not null, it => volume.Contains(it.Volume))
                    .WhereIf(applyNo is not null, it => applyNo.Contains(it.ApplyNo))
                    .WhereIf(CustomerId is not null, it => CustomerId.Contains(it.CustomerId))
                    .WhereIf(
                        request.CaseName is not null,
                        it => it.CaseName.Contains(request.CaseName)
                    )
                    .WhereIf(CaseNo is not null, it => CaseNo.Contains(it.CustomerCaseNo))
                    .WhereIf(registerNo is not null, it => registerNo.Contains(it.RegisterNo))
                    .WhereIf(appNo is not null, it => appNo.Contains(it.AppNo))
                    .ToListAsync(it => it.Id);
            }

            select = select
                .WhereIf(procNo is not null, it => procNo.Contains(it.t1.ProcNo))
                .WhereIf(caseIdList is not null, it => caseIdList.Contains(it.t1.CaseId))
                .WhereIf(ApplicantId is not null, it => ApplicantId.Contains(it.t3.ApplicantId));
            List<string>? mailCorrelatives = new List<string>();
            if (!string.IsNullOrWhiteSpace(request.MailId))
            {
                mailCorrelatives = await mailFreeSql
                    .Select<MailCorrelative>()
                    .WithLock()
                    .Where(it =>
                        it.MailId == request.MailId
                        && it.CorrelateType == SysEnum.CorrelateType.Proc.ToString()
                    )
                    .ToListAsync(it => it.ObjId, cancellationToken);
            }

            var getRelateProcListDtos = await select
                .WhereIf(
                    request.CtrlProcId is not null,
                    it => it.t1.CtrlProcId == request.CtrlProcId
                )
                .WhereIf(
                    request.CountryId is not null,
                    it => request.CountryId.Contains(it.t2.CountryId)
                )
                .WhereIf(mailCorrelatives.Count > 0, it => !mailCorrelatives.Contains(it.t1.ProcId))
                .Page(request.PageIndex!.Value, request.PageSize!.Value)
                .OrderByDescending(it => it.t1.CreateTime)
                .Count(out var totalCount)
                .ToListAsync(
                    it => new GetRelateProcListDto(
                        it.t1.ProcId,
                        it.t1.ProcNo,
                        it.t2.CaseTypeId,
                        it.t1.CtrlProcId,
                        it.t2.Volume,
                        it.t2.CaseName,
                        it.t1.CreateTime,
                        it.t1.CtrlProcMark,
                        it.t1.ProcStatusId,
                        it.t1.CtrlProcProperty
                    ),
                    cancellationToken
                );

            var relateProcListDtos = await getRelateProcListDtos
                .ToAsyncEnumerable()
                .SelectAwait(async it =>
                {
                    if (it.CtrlProcId is not null)
                    {
                        it.CtrlProcName =
                            await baseCtrlProcRepository.GetTextValueAsync(it.CtrlProcId) ?? "";
                    }
                    if (it.CtrlProcMarkId is not null)
                    {
                        it.CtrlProcMark = new
                        {
                            id = it.CtrlProcMarkId,
                            cnName = (
                                await systemDictionaryRepository.GetChineseValueAsync(
                                    SystemDictionaryName.CtrlProcMark,
                                    it.CtrlProcMarkId
                                )
                            ) ?? "",
                        };
                    }
                    if (it.ProcStatusId is not null)
                    {
                        var chineseKeyValueAsync =
                            await basCaseProcStatusRepository.GetCacheValueAsync(it.ProcStatusId);
                        it.ProcStatus = new
                        {
                            id = it.ProcStatusId,
                            cnName = chineseKeyValueAsync?.TextZhCn,
                        };
                    }
                    if (!string.IsNullOrWhiteSpace(it.CtrlProcPropertyId))
                    {
                        var baseBasCaseStatus = await iBaseCaseStatusRepository.GetCacheValueAsync(
                            it.CtrlProcPropertyId
                        );
                        it.CtrlProcProperty = new
                        {
                            id = it.CtrlProcPropertyId,
                            cnName = baseBasCaseStatus?.CaseStatusZhCn,
                        };
                    }
                    return it;
                })
                .ToListAsync(cancellationToken: cancellationToken);

            return new PageResult<GetRelateProcListDto>()
            {
                Data = relateProcListDtos,
                Page = request.PageIndex.Value,
                PageSize = request.PageSize.Value,
                Total = totalCount,
            };
        }
    }
}
