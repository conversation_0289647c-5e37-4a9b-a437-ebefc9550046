﻿using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class AllocateCommandHandler(
    ICaseFeeRepository caseFeeRepository,
    IMediator mediator,
    IHttpContextAccessor httpContextAccessor)
    : FeesUpdateCommandHandler(caseFeeRepository, httpContextAccessor)
{

    protected override FinanceOperation UpdateOperation => FinanceOperation.Allocate;
    
    protected override async ValueTask<FeesUpdateResult> UpdateFeeAsync(CaseFeeList caseFeeList, FeeUpdateInfoDto feeUpdateInfoDto, string operatorId)
    {
        var result = caseFeeList.Allocate(feeUpdateInfoDto.ReceiveDate, feeUpdateInfoDto.CustomerId, operatorId);
        await mediator.Send(
            new AllocateCommissionUserInfoQuery(result, caseFeeList.SalesUserId, caseFeeList.TrackUserId,
                result.BusinessUser?.UserId, result.ClueUser?.UserId));

        return result;
    }
}