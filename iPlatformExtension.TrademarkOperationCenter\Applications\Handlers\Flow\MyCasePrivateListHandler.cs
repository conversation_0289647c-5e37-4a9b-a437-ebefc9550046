﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    /// <summary>
    /// 获取自定义标签
    /// </summary>
    public sealed class MyCasePrivateCountHandler : IRequestHandler<MyCasePrivateCountQuery, IEnumerable<MyCasePrivateCountDto>>
    {
        private readonly IFreeSql _freeSql;
        private readonly IHttpContextAccessor _httpContextAccessor;

        /// <summary>
        /// 获取自定义标签构造函数
        /// </summary>
        /// <param name="freeSql"></param>
        /// <param name="httpContextAccessor"></param>
        public MyCasePrivateCountHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor)
        {
            _freeSql = freeSql;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<IEnumerable<MyCasePrivateCountDto>> Handle(MyCasePrivateCountQuery request, CancellationToken cancellationToken)
        {
            var userid = _httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userid);

            var list = await _freeSql.Select<SysFlowPrivate>()
                .Where(o => o.FlowType == request.flowType && o.UserId == userid)
                .WhereIf(request.flowSubType is not null, it => it.FlowSubType == request.flowSubType).WithLock()
                .ToListAsync(flow => new MyCasePrivateCountDto(flow.PrivateId, flow.UserId, flow.FlowType, flow.FlowSubType, flow.PrivateName, flow.IsShow, flow.Seq), cancellationToken);
            return list.OrderBy(o => o.flowType).ThenBy(o => o.seq);
        }
    }
}
