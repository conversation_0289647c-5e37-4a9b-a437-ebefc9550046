﻿using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow
{
    public class GetFlowConfigDto
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        public string ListId { get; set; }
        /// <summary>
        /// 流程ID
        /// </summary>
        public string FlowId { get; set; }
        /// <summary>
        /// 顺序
        /// </summary>
        public int? Seq { get; set; }
        /// <summary>
        /// 下一个顺序
        /// </summary>
        public string Next { get; set; }
        /// <summary>
        /// 节点ID
        /// </summary>
        public string NodeId { get; set; }
        /// <summary>
        /// 允许编辑
        /// </summary>
        public bool AllowEdit { get; set; }
        /// <summary>
        /// 审核类型
        /// </summary>
        public string NodeType { get; set; }
        /// <summary>
        /// 用户ID列表
        /// </summary>
        public string UserList { get; set; }
        /// <summary>
        /// 节点名称
        /// </summary>
        public string NodeName { get; set; }
        /// <summary>
        /// 节点编码
        /// </summary>
        public string NodeCode { get; set; }
        /// <summary>
        /// 节点编码
        /// </summary>
        public bool? IsSkip { get; set; }

        /// <summary>
        /// 流程类型
        /// </summary>
        public string FlowSubType { get; set; }

        /// <summary>
        /// 审核人列表
        /// </summary>
        public List<FlowUserDto> AuditUsers { get; set; }
    }
}
