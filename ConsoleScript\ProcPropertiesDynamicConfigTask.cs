﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MiniExcelLibs;

namespace ConsoleScript;

public class ProcPropertiesDynamicConfigTask
{
    public async Task Work()
    {
        const string fileName = "ImportDeliveryKeyField.xlsx";

        var freeSql = new FreeSqlBuilder().UseConnectionString(DataType.SqlServer,
                "data source=***************,7433;initial catalog=acip_iplatform_1009;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true")
            .Build();

        var rows = await MiniExcel.QueryAsync<CtrlProcInfo>(fileName);
        var procNames = rows.Select(info => info.Name).ToList();
        var basCtrlProcInfo = await freeSql.Select<BasCtrlProc>().WithLock()
            .Where(proc => procNames.Contains(proc.CtrlProcZhCn)).ToListAsync(proc => new BasCtrlProc()
            {
                CtrlProcZhCn = proc.CtrlProcZhCn,
                CtrlProcId = proc.CtrlProcId
            });
        var ctrlProcDictionary = basCtrlProcInfo.ToDictionary(proc => proc.CtrlProcZhCn, proc => proc.CtrlProcId);

        foreach (var ctrlProcInfo in rows)
        {
            if (ctrlProcDictionary.TryGetValue(ctrlProcInfo.Name, out var ctrlProcId))
            {
                var caseDirections = ctrlProcInfo.CaseDirections.Split(',');
                // var deliveryKeyInfos = caseDirections.Select(caseDirection => new ProcPropertiesDynamicConfig()
                // {
                //     BelongClassName = nameof(CaseProcInfo),
                //     TableName = "case_proc_info",
                //     CtrlProcId = ctrlProcId,
                //     DisplayName = "国内网交用Key",
                //     PropertyName = nameof(CaseProcInfo.DeliveryKey),
                //     FieldName = "delivery_key",
                //     Key = "deliveryKey",
                //     Include = NavigationType.None,
                //     Order = 8,
                //     CaseDirection = caseDirection,
                //     IsShow = true,
                //     IsDictionary = true,
                //     DictionaryName = SystemDictionaryName.DeliveryKey,
                //     DisplayValueSourceType = string.Empty,
                //     IsReadonly = false,
                //     IsKeyValue = true
                // }).ToList();
                //
                // await freeSql.Insert(deliveryKeyInfos).ExecuteAffrowsAsync();
                //
                // var agencyInfos = caseDirections.Select(caseDirection => new ProcPropertiesDynamicConfig()
                // {
                //     BelongClassName = nameof(CaseProcInfo),
                //     TableName = "case_proc_info",
                //     CtrlProcId = ctrlProcId,
                //     DisplayName = "代理名义",
                //     PropertyName = nameof(CaseProcInfo.TrademarkDeliveryAgencyId),
                //     FieldName = "trademark_delivery_agency_id",
                //     Key = "trademarkDeliveryAgencyId",
                //     Include = NavigationType.None,
                //     Order = 1,
                //     CaseDirection = caseDirection,
                //     IsShow = true,
                //     IsDictionary = false,
                //     DictionaryName = string.Empty,
                //     DisplayValueSourceType = nameof(BasAgency),
                //     IsReadonly = false,
                //     IsKeyValue = true
                // }).ToList();
                //
                // await freeSql.Insert(agencyInfos).ExecuteAffrowsAsync();
                
                var versionInfos = caseDirections.Select(caseDirection => new ProcPropertiesDynamicConfig()
                {
                    BelongClassName = nameof(CaseProcInfo),
                    TableName = "case_proc_info",
                    CtrlProcId = ctrlProcId,
                    DisplayName = "版本号",
                    PropertyName = nameof(CaseProcInfo.Version),
                    FieldName = "version",
                    Key = "version",
                    Include = NavigationType.None,
                    Order = 0,
                    CaseDirection = caseDirection,
                    IsShow = false,
                    IsDictionary = false,
                    DictionaryName = string.Empty,
                    DisplayValueSourceType = string.Empty,
                    IsReadonly = true,
                    IsKeyValue = false
                }).ToList();

                await freeSql.Insert(versionInfos).ExecuteAffrowsAsync();
            }
        }
    }
}