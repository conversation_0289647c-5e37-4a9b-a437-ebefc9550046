using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.Mail.Applications.Models;

public class NotificationMailMessage
{
    [Required]
    public string Subject { get; set; } = string.Empty;

    [Required]
    public string BodyText { get; set; } = string.Empty;

    [EmailAddress]
    public string? Sender { get; set; }

    [EmailAddress]
    [Required]
    public string Receiver { get; set; } = string.Empty;

    public FormCollection Files { get; set; } = FormCollection.Empty;
}