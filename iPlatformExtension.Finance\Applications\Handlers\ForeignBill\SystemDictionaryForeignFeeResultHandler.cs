﻿using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal sealed class SystemDictionaryForeignFeeResultHandler(ISystemDictionaryRepository dictionaryRepository) 
    : ForeignFeeResultHandlerBase
{
    protected override async ValueTask GetResultAsync(ForeignBillFeeExportDto dto)
    {
        dto.FeeClass = (await dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.FeeType, dto.FeeClass))
            .Value;
        dto.CaseType = (await dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.CaseType, dto.CaseType))
            .Value;
        dto.RemittanceFeeSharingMethod =
            (await dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.RemittanceFeeSharingMethod,
                dto.RemittanceFeeSharingMethod)).Value;
        dto.PaymentStatus = (await dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.ForeignBillPaymentStatus,
            dto.PaymentStatus)).Value;
    }
}