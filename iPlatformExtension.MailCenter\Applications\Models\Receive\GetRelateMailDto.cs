﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 关联邮件dto
/// </summary>
/// <param name="RelateId">关联id</param>
/// <param name="MailId">邮件id</param>
/// <param name="ReceiveType">邮件类型</param>
/// <param name="CreateTime">关联时间</param>
/// <param name="MailNo">邮件编号</param>
/// <param name="MailFrom">发件人</param>
/// <param name="MailSubject">邮件主题</param>
/// <param name="MailDate">发送时间</param>
/// <param name="RelateUser">关联用户</param>
/// <param name="Status">邮件状态</param>
/// <param name="ObjId">被关联id</param>
public record GetRelateMailDto(
    string RelateId,
    string MailId,
    string ReceiveType,
    DateTime? CreateTime,
    string MailNo,
    string MailFrom,
    string MailSubject,
    string RelateUserId,
    DateTime? MailDate,
    int? Status,
    string ObjId)
{
    public object RelateUser { get; set; }
};

