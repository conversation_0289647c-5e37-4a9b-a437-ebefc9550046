﻿using Confluent.Kafka;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Common.MQ.KafKa.Models;
using MediatR;

namespace iPlatformExtension.Common.Mediator.Handlers;

public class DefaultSendKafkaMessageCommandHandler<TCommand, TMessage>(IProducer<MessageKey, TMessage> producer)
    : IRequestHandler<TCommand>
    where TCommand : ISendKafkaMessageCommand<TMessage>
{
    public virtual async Task Handle(TCommand request, CancellationToken cancellationToken)
    {
        var result = await producer.ProduceMessageAsync(request.Topic, request.MessageKey, request.Message, cancellationToken);
        if (result.Status < request.PersistenceStatus)
        {
            throw new Exception("消息持久化失败");
        }
    }
}