﻿using iPlatformExtension.Public.Applications.Commands.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers
{

    /// <summary>
    /// 我的商标控制器
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    [Authorize]
    public class MyTrademarkCaseController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// 我的商标列表
        /// </summary>
        [HttpGet("GetMyTrademarkCaseList")]
        public async Task<IEnumerable<GetMyTrademarkCaseListDto>> GetMyTrademarkCaseListQuery([FromQuery] GetMyTrademarkCaseListQuery query)
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 流程归入分类
        /// </summary>
        /// <returns></returns>
        [HttpPost("MoveProcess")]
        public async Task MoveProcess([FromBody] MoveProcessCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 获取标签
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetMyTrademarkCasePrivate")]
        public async Task<IEnumerable<GetMyTrademarkCasePrivateDto>> GetMyTrademarkCasePrivate([FromQuery] GetMyTrademarkCasePrivateQuery query)
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取公认证规则列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetPublicAuthenticationRulesList")]
        public async Task<IEnumerable<GetPublicAuthenticationRulesListDto>> GetPublicAuthenticationRulesList([FromQuery] GetPublicAuthenticationRulesListQuery query)
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 商标页签计数
        /// </summary>
        /// <returns></returns>
        [HttpGet("MyTrademarkCaseCount")]
        public async Task<MyTrademarkCaseCountDto> MyTrademarkCaseCount([FromQuery] MyTrademarkCaseCountQuery query)
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 修改公认证规则
        /// </summary>
        /// <returns></returns>
        [HttpPost("SaveAuthenticationRule")]
        public async Task SaveAuthenticationRules([FromBody] SaveAuthenticationRulesCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 删除公认证规则
        /// </summary>
        /// <returns></returns>
        [HttpDelete("DeleteAuthenticationRules")]
        public async Task DeleteAuthenticationRules([FromBody] DeleteAuthenticationRulesCommand command)
        {
            await mediator.Send(command);
        }


        /// <summary>
        /// 根据任务流向获取商标页签
        /// </summary>
        /// <returns></returns>
        [HttpPost("ProcTrademarkTab")]
        public async Task<ProcTrademarkTabDto> ProcTrademarkTab([FromBody] ProcTrademarkTabQuery query)
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 批量更新代理人状态
        /// </summary>
        /// <returns></returns>
        [HttpPost("BatchUpdateAgentStatus")]
        public async Task ProcTrademarkTab([FromBody] BatchUpdateAgentStatusCommand query)
        {
            await mediator.Send(query, HttpContext.RequestAborted);
        }


        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetCaseStatus")]
        public async Task GetCaseStatus([FromQuery] GetCaseStatusQuery query)
        {
            await mediator.Send(query, HttpContext.RequestAborted);
        }


    }
}
