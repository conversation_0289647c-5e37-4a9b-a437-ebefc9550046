﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <DocumentationFile>iPlatformExtension.Finance.XML</DocumentationFile>
    <RootNamespace>iPlatformExtension.Finance</RootNamespace>
    <ApplicationIcon>favicon.ico</ApplicationIcon>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <UserSecretsId>************************************</UserSecretsId>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>13</LangVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <Optimize>False</Optimize>
    <DocumentationFile>bin/Debug/iPlatformExtension.Finance.XML</DocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <Optimize>True</Optimize>
    <DocumentationFile>bin/Release/iPlatformExtension.Finance.XML</DocumentationFile>
  </PropertyGroup>

<!--  <PropertyGroup>-->
<!--    <ContainerBaseImage>mcr.microsoft.com/dotnet/aspnet:8.0</ContainerBaseImage>-->
<!--    <ContainerRuntimeIdentifier>linux-x64</ContainerRuntimeIdentifier>-->
<!--    <ContainerRegistry>harbor.aciplaw.com</ContainerRegistry>-->
<!--    <ContainerRepository>test/iplatform.finance</ContainerRepository>-->
<!--    <ContainerImageTag>latest</ContainerImageTag>-->
<!--    <ContainerUser>root</ContainerUser>-->
<!--    <PublishProfile>DefaultContainer</PublishProfile>-->
<!--  </PropertyGroup>-->

<!--  <ItemGroup>-->
<!--    <ContainerPort Include="8055" Type="tcp" />-->
<!--    <ContainerEnvironmentVariable Include="ASPNETCORE_HTTP_PORTS" Value="8055" />-->
<!--    <ContainerEnvironmentVariable Include="ASPNETCORE_URLS" Value="http://+:8055" />-->
<!--    <ContainerEnvironmentVariable Include="ASPNETCORE_ENVIRONMENT" Value="Staging" />-->
<!--    <ContainerEnvironmentVariable Include="DOTNET_GCHeapHardLimit" Value="80000000" />-->
<!--    <ContainerEnvironmentVariable Include="TZ" Value="Asia/Shanghai" />-->
<!--  </ItemGroup>-->

  <ItemGroup>
    <Content Include="..\.dockerignore">
      <Link>.dockerignore</Link>
    </Content>
    <None Update="Exclude.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="favicon.ico">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <Content Remove="secrets.json" />
    <Content Remove="temp\**" />
    <None Remove="temp\**" />
    <Content Update="skyapm.Staging.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.StackExchangeRedis" Version="9.0.0" />
    <PackageReference Include="nacos-sdk-csharp.AspNetCore" Version="1.3.9" />
    <PackageReference Include="nacos-sdk-csharp.Extensions.Configuration" Version="1.3.9" />
    <PackageReference Include="NLog.DiagnosticSource" Version="5.2.0" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.10.0" />
    <PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.4.0-rc.4" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.10.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.9.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Process" Version="0.5.0-beta.2" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.9.0" />
    <PackageReference Include="SkyAPM.Agent.AspNetCore" Version="2.2.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\iPlatformExtension.Common\iPlatformExtension.Common.csproj" />
    <ProjectReference Include="..\iPlatformExtension.Repository\iPlatformExtension.Repository.csproj" />
    <ProjectReference Include="..\iPlatformExtension.Service\iPlatformExtension.Service.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="temp\**" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Remove="temp\**" />
  </ItemGroup>
  
  
  
</Project>
