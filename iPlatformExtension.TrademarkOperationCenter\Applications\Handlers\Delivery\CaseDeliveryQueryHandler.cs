﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class CaseDeliveryQueryHandler(IFreeSql freeSql, IMediator mediator)
    : IRequestHandler<CaseDeliveryQuery, TrademarkDeliveryDto>
{
    private readonly IMediator _mediator = mediator;


    public async Task<TrademarkDeliveryDto> Handle(CaseDeliveryQuery request, CancellationToken cancellationToken)
    {
        var procInfo = await freeSql.Select<CaseProcInfo>().Where(info => info.ProcId == request.ProcId)
            .FirstAsync(cancellationToken);
        
        if (procInfo is null)
        {
            throw new NotFoundException(request.ProcId, "商标任务");
        }

        var dto = new TrademarkDeliveryDto();

        return dto;
    }
}