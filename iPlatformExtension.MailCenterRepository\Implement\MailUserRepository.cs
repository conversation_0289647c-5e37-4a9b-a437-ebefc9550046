﻿﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;

namespace iPlatformExtension.MailCenterRepository.Implement;

/// <summary>
/// 邮件用户仓储实现
/// </summary>
internal class MailUserRepository(
    IFreeSql<MailCenterFreeSql> fsql,
    UnitOfWorkManage<MailCenterFreeSql> manager)
    : DefaultRepository<MailUser, string>(fsql, manager), IMailUserRepository;
