﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel 
{
	/// <summary>
	/// 商标递交共同申请人
	/// </summary>
	[Table(Name = "trademark_other_applicant", DisableSyncStructure = true)]
	public  class TrademarkOtherApplicant 
	{

		/// <summary>
		/// 主键
		/// </summary>
		[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
		public int Id { get; set; }

		/// <summary>
		/// 递交信息id
		/// </summary>
		[Column(Name = "delivery_info_id")]
		public int DeliverInfoId { get; set; }

		/// <summary>
		/// 申请人id
		/// </summary>
		[Column(Name = "applicant_id", StringLength = 50, IsNullable = false)]
		public string ApplicantId { get; set; } = default!;

		/// <summary>
		/// 证件编号
		/// </summary>
		[Column(Name = "certification_number", StringLength = 50)]
		public string? CertificationNumber { get; set; }

		/// <summary>
		/// 证件类型
		/// </summary>
		[Column(Name = "certification_type", StringLength = 5)]
		public string? CertificationType { get; set; }

		/// <summary>
		/// 中文名称
		/// </summary>
		[Column(Name = "cn_name", StringLength = 100)]
		public string? CnName { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "creation_time", InsertValueSql = "getdate()")]
		public DateTime CreationTime { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[Column(Name = "creator", StringLength = 50, IsNullable = false)]
		public string Creator { get; set; } = default!;

		/// <summary>
		/// 英文名称
		/// </summary>
		[Column(Name = "en_name", StringLength = 50)]
		public string? EnName { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[Column(Name = "updater", StringLength = 50, IsNullable = false)]
		public string Updater { get; set; } = default!;

	}

}
