﻿using iPlatformExtension.Common.Clients.HttpMessageHandlers;
using iPlatformExtension.Common.ServiceDiscovery.Nacos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Nacos.V2;

namespace iPlatformExtension.Common.Extensions;

public static class NacosServiceClientExtension
{
    public static IServiceCollection AddNacosServiceClient(this IServiceCollection services, string serviceName, string groupName = "DEFAULT_GROUP")
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(serviceName);

        services.AddScoped<NacosServiceTokenHandler>();
        services.AddScoped<W3CTraceContextHandler>();
        services.AddHttpClient(serviceName).AddServiceDiscovery().ConfigureHttpClient((provider, client) =>
        {
            var loggerFactory = provider.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger(serviceName);
            
            // var remoteService = nacos.SelectOneHealthyInstance(serviceName, groupName).Result;
            // ArgumentNullException.ThrowIfNull(remoteService);

            var host = new NacosClusterId(serviceName, groupName);
            client.BaseAddress = host.GetServiceEndpointUri("http");
            client.Timeout = TimeSpan.FromSeconds(10);
            logger.LogInformation("{ServiceName}'s HostAddress is {BaseAddress}", serviceName, client.BaseAddress);
        }).AddHttpMessageHandler<NacosServiceTokenHandler>().AddHttpMessageHandler<W3CTraceContextHandler>();

        return services;
    }

    /// <summary>
    /// 本地HttpClient调试扩展
    /// </summary>
    /// <param name="services"></param>
    /// <param name="serviceName"></param>
    /// <param name="groupName"></param>
    /// <returns></returns>
    public static IServiceCollection AddLocalServiceClient(this IServiceCollection services, string serviceName, string groupName = "DEFAULT_GROUP")
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(serviceName);

        services.AddScoped<NacosServiceTokenHandler>();
        services.AddScoped<W3CTraceContextHandler>();
        services.AddHttpClient(serviceName).ConfigureHttpClient((provider, client) =>
        {
            var nacos = provider.GetRequiredService<INacosNamingService>();
            var loggerFactory = provider.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger(serviceName);

            var remoteService = nacos.SelectOneHealthyInstance(serviceName, groupName).Result;
            ArgumentNullException.ThrowIfNull(remoteService);

            client.BaseAddress = new Uri($"http://127.0.0.1:{remoteService.Port}");
            client.Timeout = TimeSpan.FromSeconds(10);
            logger.LogInformation("{ServiceName}'s HostAddress is {BaseAddress}", serviceName, client.BaseAddress);
        }).AddHttpMessageHandler<NacosServiceTokenHandler>().AddHttpMessageHandler<W3CTraceContextHandler>();

        return services;
    }
}