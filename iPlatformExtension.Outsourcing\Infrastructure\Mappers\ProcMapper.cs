﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Proc;

namespace iPlatformExtension.Outsourcing.Infrastructure.Mappers;

internal sealed class ProcMapper : Profile
{
    public ProcMapper()
    {
        CreateMap<CaseProcInfo, ProcPatchDto>()
            .ForMember(dto => dto.SupplierId, expression => expression.MapFrom(info => info.ForeginAgencyId));

        CreateMap<ProcPatchDto, CaseProcInfo>()
            .ForMember(info => info.ForeginAgencyId, expression => expression.MapFrom(src => src.SupplierId));

        CreateMap<CaseProcInfo, ProcOutsourcingDto>()
            .ForMember(dto => dto.SupplierId, expression => expression.MapFrom(info => info.ForeginAgencyId))
            .ForMember(dto => dto.ContactIds, expression => expression.MapFrom((info, _) =>
                info.ForeignSupplierContacts?.Select(contact => contact.ContactId).ToList() ??
                Enumerable.Empty<string>()))
            .ForMember(dto => dto.CaseType, expression => expression.MapFrom(info => info.CaseInfo.CaseTypeId));

        CreateMap<ProcForeignSupplierContact, ProcContactPatchDto>();
        CreateMap<ProcContactPatchDto, ProcForeignSupplierContact>();
    }
}