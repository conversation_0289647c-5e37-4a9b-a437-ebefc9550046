using Microsoft.Extensions.Options;
using MongoDB.Driver;

namespace iPlatformExtension.Common.Db.Mongo;

public abstract class MongoDbContext<T>
{
    protected readonly MongoDbContextOptions<T> _contextOptions;
    
    protected readonly IMongoClient _mongoClient;
    
    public IMongoDatabase Database { get; protected set; }

    protected MongoDbContext(IOptions<MongoDbContextOptions<T>> options)
    {
        var contextOptions = options.Value;
        _contextOptions = contextOptions;
        _mongoClient = contextOptions.MongoClient;
        Database = _mongoClient.GetDatabase(contextOptions.DatabaseName);
    }
}