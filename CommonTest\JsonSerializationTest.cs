﻿using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization.Metadata;
using iPlatformExtension.Common.Modifiers;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using Xunit.Abstractions;

namespace CommonTest;

public class JsonSerializationTest
{
    private readonly ITestOutputHelper _testOutputHelper;

    public JsonSerializationTest(ITestOutputHelper testOutputHelper)
    {
        _testOutputHelper = testOutputHelper;
    }

    [Fact]
    public void TestJsonModifier()
    {
        var options = new JsonSerializerOptions()
        {
            Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            TypeInfoResolver = new DefaultJsonTypeInfoResolver()
            {
                Modifiers = {new PropertySerializationSourceModifier().ModifyTypeInfo}
            }
        };

        var requestOrder = new TrademarkRegistrationOrder()
        {
            Attachments = new[]
            {
                new ApplicantAttachment("文件名", "路径", ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseIdentification),
                new ApplicantAttachment("文件名2", "华为云OBS", ApplicantAttachmentType.OfficialAttachment,
                    ApplicantAttachmentSubType.ChineseSubjectQualification)
            }
        };

        var json = JsonSerializer.Serialize(requestOrder, options);
        _testOutputHelper.WriteLine(json);

        Assert.Contains("""applicantAttachments":"[""", json, StringComparison.OrdinalIgnoreCase);
    }
}