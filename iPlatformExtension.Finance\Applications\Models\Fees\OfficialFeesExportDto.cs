﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Finance.Applications.Models.Fees;

internal class OfficialFeesExportDto
{
    [ExcelColumn(Name = "任务编号")]
    public string ProcNo { get; set; } = string.Empty;

    [ExcelColumn(Name = "我方文号")]
    public string Volume { get; set; } = string.Empty;

    [ExcelColumn(Name = "申请人名称")]
    public string ApplicantNames { get; set; } = string.Empty;

    [ExcelColumn(Name = "客户名称")]
    public string CustomerName { get; set; } = string.Empty;

    [ExcelColumn(Name = "商标名称")]
    public string CaseName { get; set; } = string.Empty;

    [ExcelColumn(Name = "国际分类")]
    public string TrademarkNiceClasses { get; set; } = string.Empty;

    [ExcelColumn(Name = "送官方日", Format = "yyyy-MM-dd")]
    public DateTime? SendOfficeDate { get; set; }

    [ExcelColumn(Name = "任务名称")]
    public string ProcName { get; set; } = string.Empty;

    [ExcelColumn(Name = "缴费key")]
    public string DeliveryKey { get; set; } = string.Empty;

    [ExcelColumn(Name = "案件流向")]
    public string CaseDirection { get; set; } = string.Empty;

    [ExcelColumn(Name = "申请号/注册号")]
    public string ApplicationNumber { get; set; } = string.Empty;

    [ExcelColumn(Name = "递交方式")]
    public string DeliveryType { get; set; } = string.Empty;

    [ExcelColumn(Name = "标识说明")]
    public string OfficialFeeMarkDescription { get; set; } = string.Empty;

    [ExcelColumn(Name = "官费")]
    public decimal Amount { get; set; }

    [ExcelColumn(Name = "缴费发文日", Format = "yyyy-MM-dd")]
    public DateTime? OfficialPaymentPublicationDate { get; set; }

    [ExcelColumn(Name = "管控标识")]
    public string CustomerControlIdentifiers { get; set; } = string.Empty;
}