using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_read_list", DisableSyncStructure = true)]
	public partial class MailReadList {

		[ Column(Name = "allot_time")]
		public DateTime? AllotTime { get; set; }

		[ Column(Name = "first_read_time", InsertValueSql = "getdate()")]
		public DateTime FirstReadTime { get; set; }

		/// <summary>
		/// 最后一次阅读时间
		/// </summary>
		[ Column(Name = "last_read_time", InsertValueSql = "getdate()")]
		public DateTime LastReadTime { get; set; }

		[ Column(Name = "mail_id", StringLength = 50)]
		public string MailId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "status")]
		public int Status { get; set; } = 0;

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
