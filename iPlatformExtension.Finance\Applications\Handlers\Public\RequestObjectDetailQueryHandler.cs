using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Queries.Public;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Public;

/// <summary>
/// 请款对象明细查询处理者
/// </summary>
public sealed class RequestObjectDetailQueryHandler : IRequestHandler<RequestObjectDetailQuery, RequestObjectInfoDto?>
{
    private readonly IFreeSql _freeSql;

    private readonly ISystemDictionaryRepository _systemDictionaryRepository;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="freeSql">数据库组件</param>
    /// <param name="systemDictionaryRepository">字段仓储</param>
    public RequestObjectDetailQueryHandler(IFreeSql freeSql, ISystemDictionaryRepository systemDictionaryRepository)
    {
        _freeSql = freeSql;
        _systemDictionaryRepository = systemDictionaryRepository;
    }
    
    /// <summary>
    /// 请款对象明细查询处理
    /// </summary>
    /// <param name="request">请款对象查询参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>请款对象明细数据</returns>
    /// <exception cref="NotFoundException"></exception>
    public async Task<RequestObjectInfoDto?> Handle(RequestObjectDetailQuery request, CancellationToken cancellationToken)
    {
        var requestObjectId = request.RequestObjectId;
        var requestObjectInfo = await _freeSql.Select<CusRequestObject>().WithLock()
            .Where(requestObject => requestObject.RequestObjectId == requestObjectId)
            .FirstAsync(o => new RequestObjectInfoDto()
            {
                RequestObjectId = o.RequestObjectId,
                Address = o.AddressCn,
                BankName = o.BankName,
                IdentifierNumber = o.IdentifyNumber,
                InvoiceTitle = o.InvoicesTitle,
                TaxpayerQualificationType = new KeyValuePair<string, string>(SystemDictionaryName.TaxpayerType.ToSqlStringConstant(), o.IsGeneralTaxpayer),
                Name = o.RequestObjectName,
                Telephone = o.Tel,
                AccountNo = o.AccountNo,
                IsOutbound = o.IsOutbound
            }, cancellationToken);

        if (requestObjectInfo is not null)
        {
            requestObjectInfo.TaxpayerQualificationType =
                await _systemDictionaryRepository.GetChineseKeyValueAsync(requestObjectInfo.TaxpayerQualificationType);
        }
        else
        {
            throw new NotFoundException(requestObjectId, "请款对象");
        }

        return requestObjectInfo;
    }
}