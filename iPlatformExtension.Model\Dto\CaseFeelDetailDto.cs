﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 费项明细dto
/// </summary>
public record CaseFeeDetailDto
{
    /// <summary>
    /// 费用标识
    /// </summary>
    public string FeeId { get; init; } = default!;
    
    /// <summary>
    /// 任务名称
    /// </summary>
    public string ProcName { get; set; } = default!;
    
    /// <summary>
    /// 生成方式：
    /// <c>true</c>自动生成
    /// <c>false</c>用户生成
    /// </summary>
    public bool IsAuto { get; init; }
    
    /// <summary>
    /// 字典表"fee_type"
    /// </summary>
    public KeyValuePair<string, string> FeeClass { get; set; }
    
    /// <summary>
    /// 币种
    /// </summary>
    public string Currency { get; init; } = default!;
    
    /// <summary>
    /// 是否编辑
    /// </summary>
    public bool IsEdit { get; init; }

    /// <summary>
    /// 费用类型名称id
    /// </summary>
    [JsonIgnore]
    public string FeeTypeNameId { get; init; } = default!;

    /// <summary>
    /// 基础表"bas_fee_type_name"
    /// 费用类型名称
    /// </summary>
    public KeyValuePair<string, string> FeeTypeName { get; set; }
    
    /// <summary>
    /// 是否可用
    /// </summary>
    public bool IsEnable { get; init; }
    
    /// <summary>
    /// 缴费名称
    /// </summary>
    public string OfficeName { get; init; } = default!;
    
    /// <summary>
    /// 首缴年度
    /// </summary>
    public bool IsFirstPayAnnual { get; init; }
    
    /// <summary>
    /// 标准金额
    /// </summary>
    public decimal StandardAmount { get; init; }
    
    /// <summary>
    /// 折扣
    /// </summary>
    public string? Discount { get; init; }
    
    /// <summary>
    /// 实际金额
    /// </summary>
    public decimal Amount { get; init; }
    
    /// <summary>
    /// 字典表"balance_way"
    /// 结算方式
    /// </summary>
    public KeyValuePair<string, string>? BalanceWay { get; set; }
    
    /// <summary>
    /// 字典表"charge_rule"
    /// 收费规则
    /// </summary>
    public KeyValuePair<string, string>? ReceiveRule { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public int Seq { get; init; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; init; }
    
    /// <summary>
    /// 是否向官方缴费
    /// </summary>
    public bool IsOfficer { get; init; }
    
    /// <summary>
    /// 字典表"pay_way"
    /// 官费支付规则
    /// 支付方式
    /// </summary>
    public KeyValuePair<string, string>? PayWay { get; set; }

    /// <summary>
    /// 字典表"officer_status"
    /// 缴官费状态
    /// </summary>
    public KeyValuePair<string, string> OfficerStatus { get; set; }
    
    /// <summary>
    /// 缴费期限
    /// </summary>
    public DateTime? PayOfficerLegalDate { get; init; }
    
    /// <summary>
    /// 缴费编号
    /// </summary>
    public string? PayOfficerSerial { get; init; }
    
    /// <summary>
    /// 缴费日期
    /// </summary>
    public DateTime? PayOfficerDate { get; init; }
    
    /// <summary>
    /// 字典表"payment_name"
    /// 缴费名义
    /// </summary>
    public KeyValuePair<string, string>? PaymentName { get; set; }
    
    /// <summary>
    /// 缴费人名称
    /// </summary>
    public string? PaymentAgency { get; init; }
    
    /// <summary>
    /// 是否向客户缴费
    /// </summary>
    public bool IsRequest { get; init; }
    
    /// <summary>
    /// 预请款日期
    /// pre_request_date
    /// </summary>
    public DateTime? PreRequestDate { get; init; }

    /// <summary>
    /// 请款日期
    /// </summary>
    public DateTime? RequestDate { get; set; }

    /// <summary>
    /// 到款状态
    /// 字典表"receive_status"
    /// </summary>
    public KeyValuePair<string, string> ReceiveStatus { get; set; }
    
    /// <summary>
    /// 账单号码
    /// </summary>
    public string BillNo { get; init; } = default!;
    
    /// <summary>
    /// 应收日期
    /// </summary>
    public DateTime? ReceiveDueDate { get; init; }

    /// <summary>
    /// 到款日期
    /// </summary>
    public DateTime? ReceiveDate { get; init; }
    
    /// <summary>
    /// 税率
    /// </summary>
    public string? TaxRate { get; init; }
    
    /// <summary>
    /// 发票编号
    /// </summary>
    public string? InvoiceNo { get; init; }
    
    /// <summary>
    /// 付款单号
    /// </summary>
    public string? PayBillNo { get; init; }
    
    /// <summary>
    /// 实付日期
    /// </summary>
    public DateTime? PayCooperationDate { get; init; }

    /// <summary>
    /// 付款状态
    /// 字典表"pay_status"
    /// </summary>
    public KeyValuePair<string, string> PayStatus { get; set; }
    
    /// <summary>
    /// 应付期限
    /// </summary>
    public DateTime? PayDueDate { get; init; }

    /// <summary>
    /// 创建用户
    /// 用户表"sys_user_info"
    /// </summary>
    public KeyValuePair<string, string> Creator { get; init; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreationTime { get; init; }

    /// <summary>
    /// 更新用户
    /// 用户表"sys_user_info"
    /// </summary>
    public KeyValuePair<string, string> Updater { get; init; }
    
    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime UpdateTime { get; init; }
}

