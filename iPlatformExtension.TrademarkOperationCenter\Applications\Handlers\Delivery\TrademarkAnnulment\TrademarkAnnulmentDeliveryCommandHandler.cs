﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkAnnulment;

internal sealed class TrademarkAnnulmentDeliveryCommandHandler(IMediator mediator, IRedisCache<RedisCacheOptionsBase> redisCache, IDeliveryInfoRepository deliveryInfoRepository) : DeliveryCommandHandlerBase(mediator, redisCache, deliveryInfoRepository)
{
    public override Task<SendDeliveryInternalCommand?> HandleSendStartupCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        var deliveryInfo = context.DeliveryInfo;
        
        var otherInfo = deliveryInfo.OtherInfo;
        if (otherInfo?.TrademarkNiceClasses?.Split(';').Length > 1)
        {
            throw new ApplicationException("仅支持当前任务在“一个类别”的场景下自动递交。");
        }
        
        if (!string.IsNullOrWhiteSpace(otherInfo?.CtrlProcMark) && otherInfo.CtrlProcMark is not (CtrlProcMark.BasicMaterial or CtrlProcMark.DisposableMaterial))
        {
            throw new ApplicationException("仅支持当前任务在[任务标识]为“基础材料”或“一次性任务”的场景下自动递交");
        }
        
        return base.HandleSendStartupCommandAsync(context, cancellationToken);
    }
}