using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "req_batch_case_list", DisableSyncStructure = true)]
	public partial class ReqBatchCaseList {

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; }

		[ Column(Name = "credit_code", StringLength = 50)]
		public string CreditCode { get; set; }

		[ Column(Name = "fee_id", StringLength = 50, IsNullable = false)]
		public string FeeId { get; set; }

		[ Column(Name = "message_info", StringLength = 4000)]
		public string MessageInfo { get; set; }

		[ Column(Name = "payment_agency", StringLength = 50)]
		public string PaymentAgency { get; set; }

		[ Column(Name = "payment_name", StringLength = 50)]
		public string PaymentName { get; set; }

		[ Column(Name = "req_status", StringLength = 50)]
		public string ReqStatus { get; set; }

		[ Column(Name = "run_status")]
		public bool RunStatus { get; set; } = false;

	}

}
