﻿using FluentValidation;
using FluentValidation.Validators;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Common.Validation.Validators;

public class CtrlProcIdValidator<T>(IStringKeyCacheableRepository<BasCtrlProc> cacheableRepository) 
    : AsyncPropertyValidator<T, string>
{
    public override async Task<bool> IsValidAsync(ValidationContext<T> context, string value, CancellationToken cancellation)
    {
        if (await cacheableRepository.GetTextValueAsync(value) is not null)
        {
            return true;
        }
        
        context.AddFailure(context.PropertyPath, $"任务名称id[{value}]不存在!");
        return false;
    }

    public override string Name => nameof(CtrlProcIdValidator<T>);
}