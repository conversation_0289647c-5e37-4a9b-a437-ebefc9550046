﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.MapManage;
using iPlatformExtension.MailCenter.Applications.Queries.MapManage;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.MapManage
{
    /// <summary>
    /// 获取映射列表信息
    /// </summary>
    internal sealed class GetMapListQueryHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IUserInfoRepository userInfoRepository,
        IBaseCtrlProcRepository baseCtrlProcRepository
    ) : IRequestHandler<GetMapListQuery, IEnumerable<GetMapListDto>>
    {
        public async Task<IEnumerable<GetMapListDto>> Handle(
            GetMapListQuery request,
            CancellationToken cancellationToken
        )
        {
            List<string>? matchedCtrlProcIds = null;
            if (!string.IsNullOrEmpty(request.Search))
            {
                matchedCtrlProcIds = await baseCtrlProcRepository.Select.WithLock()
                    .Where(x => x.CtrlProcZhCn.Contains(request.Search))
                    .ToListAsync(x => x.CtrlProcId, cancellationToken);
            }
            var getMapListDtos = await freeSql
                .Select<MapConfig>()
                .WithLock()
                .OrderByDescending(it=>it.UpdateTime)
                .Page(request.PageIndex!.Value, request.PageSize!.Value)
                .Count(out var totalCount)
                .WhereIf(
                    request.Search is not null,
                    it =>
                        it.MapNo.Contains(request.Search)
                        || it.MapValue.Contains(request.Search)
                        || (
                            matchedCtrlProcIds.Contains(it.CtrlProcId)
                        )
                )
                .ToListAsync(
                    it => new GetMapListDto(
                        it.MapId,
                        it.CreateTime,
                        it.CreateUser,
                        it.CtrlProcId,
                        it.IsEnabled,
                        it.MapNo,
                        it.MapValue,
                        it.Scope,
                        it.UpdateTime,
                        it.UpdateUser
                    ),
                    cancellationToken
                );
            var mapListDtos = await getMapListDtos
                .ToAsyncEnumerable()
                .SelectAwait(async it =>
                {
                    if (it.CreateUserTemp is not null)
                    {
                        ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                            userInfoRepository;
                        it.CreateUser = new
                        {
                            CnName = (
                                await userBaseInfoRepository.GetCacheValueAsync(
                                    it.CreateUserTemp,
                                    cancellationToken: cancellationToken
                                )
                            )?.CnName ?? "",
                            UserId = it.CreateUserTemp,
                        };
                    }
                    if (it.UpdateUserTemp is not null)
                    {
                        ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                            userInfoRepository;
                        it.UpdateUser = new
                        {
                            CnName = (
                                await userBaseInfoRepository.GetCacheValueAsync(
                                    it.UpdateUserTemp,
                                    cancellationToken: cancellationToken
                                )
                            )?.CnName ?? "",
                            UserId = it.UpdateUserTemp,
                        };
                    }
                    if (it.CtrlProcId != null)
                    {
                        var ctrlProc = await baseCtrlProcRepository.GetCacheValueAsync(
                            it.CtrlProcId,
                            cancellationToken: cancellationToken
                        );
                        it.CtrlProc = new
                        {
                            CtrlProcId = it.CtrlProcId,
                            CnName = ctrlProc?.CtrlProcZhCn,
                        };
                    }

                    if (!string.IsNullOrWhiteSpace(it.ScopeTemp))
                    {
                        var scopeList = it.ScopeTemp.Split(",");
                        it.Scope = new
                        {
                            CnName = scopeList.Select(x => ((SysEnum.ParseScope)Enum.Parse(typeof(SysEnum.ParseScope), x)).GetDescription())
                                .Aggregate((current, next) => current + ";" + next),
                            Scope = it.ScopeTemp,
                        };
                    }

                    return it;
                })
                .ToListAsync(cancellationToken: cancellationToken);
            return new PageResult<GetMapListDto>()
            {
                Data = mapListDtos,
                Page = request.PageIndex.Value,
                PageSize = request.PageSize.Value,
                Total = totalCount,
            };
        }
    }
}
