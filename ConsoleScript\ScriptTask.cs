﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;

namespace ConsoleScript;

public abstract class ScriptTask
{
    protected IFreeSql _freeSql = null!;

    protected ILogger _logger = null!;

    private readonly IServiceCollection _services = new ServiceCollection();
    
    public abstract string ConnectionString { get; }

    protected ScriptTask()
    {
        var services = _services;
        services.AddFreeSql<PlatformFreeSql>(options =>
        {
            options.ConnectionString = ConnectionString;
            options.DbType = DataType.SqlServer;
            options.CommandTimeout = TimeSpan.FromSeconds(10);
        }).ConfigureGlobal((freeSql, provider) =>
        {
            var logger = provider.GetRequiredService<ILogger<SyncPatentForeignSupplierTask>>();
            freeSql.Aop.CurdAfter += (_, args) =>
            {
                logger.LogInformation(args.Sql);
            };
        });

        services.AddLogging(builder =>
            builder.AddSimpleConsole(options => options.ColorBehavior = LoggerColorBehavior.Enabled));
        
        services.AddStringBuilderPool();
        
        services.AddDataService();
    }

    protected abstract ValueTask ConfigureServicesAsync(IServiceCollection services);

    protected abstract Task RunAsync(IServiceProvider serviceProvider);

    public async Task RunAsync()
    {
        await ConfigureServicesAsync(_services);
        var serviceProvider = _services.BuildServiceProvider();
        
        var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
        _freeSql = serviceProvider.GetRequiredService<IFreeSql<PlatformFreeSql>>();
        _logger = loggerFactory.CreateLogger(GetType());
        
        await RunAsync(serviceProvider);
    }
}