using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_sz_yesterday_shenqin", DisableSyncStructure = true)]
	public partial class VwSzYesterdayShenqin {

		[ Column(StringLength = 4000, IsNullable = false)]
		public string 案件备注 { get; set; }

		[ Column(StringLength = 500)]
		public string 案件名称 { get; set; }

		[ Column(StringLength = 50)]
		public string 案源分所 { get; set; }

		[ Column(Name = "案源人(邮箱)", StringLength = -2, IsNullable = false)]
		public string 案源人邮箱 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 承办人 { get; set; }

		[ Column(StringLength = 200, IsNullable = false)]
		public string 承办人邮箱 { get; set; }

		[ Column(StringLength = 4000, IsNullable = false)]
		public string 个案要求 { get; set; }

		[ Column(Name = "跟案人(邮箱)", StringLength = -2, IsNullable = false)]
		public string 跟案人邮箱 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 公司电话 { get; set; }

		[ Column(StringLength = 1000)]
		public string 客户名称 { get; set; }

		[ Column(StringLength = 100)]
		public string 客户文号 { get; set; }

		[ Column(StringLength = -2, IsNullable = false)]
		public string 联系人 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 名义承办人 { get; set; }

		[ Column(StringLength = 10, IsNullable = false)]
		public string 配案日期 { get; set; }

		[ Column(StringLength = 4000, IsNullable = false)]
		public string 任务备注 { get; set; }

		[ Column(StringLength = 50)]
		public string 任务名称 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 任务属性 { get; set; }

		[ Column(StringLength = 50)]
		public string 申请类型 { get; set; }

		[ Column(StringLength = -2)]
		public string 申请人 { get; set; }

		[ Column(StringLength = 50)]
		public string 我方文号 { get; set; }

	}

}
