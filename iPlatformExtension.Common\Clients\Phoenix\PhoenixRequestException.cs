﻿using iPlatformExtension.Model.Dto.TrademarkDelivery;

namespace iPlatformExtension.Common.Clients.Phoenix;

public sealed class PhoenixRequestException(PhoenixResponseParameters response) : ApplicationException
{
    public PhoenixResponseParameters Response { get; } = response;

    public override string Message
    {
        get
        {
            if (Response.Code is 9091)
            {
                return Response.SubMessage!;
            }
            return Response.Code switch
            {
                9000 => "权大师调用错误：请传入正确的公共参数",
                9001 => "权大师调用错误：请传入appKey参数",
                9002 => "权大师调用错误：请传入timestamp参数",
                9003 => "权大师调用错误：请传入v参数",
                9004 => "权大师调用错误：请传入sign参数",
                9005 => "权大师调用错误：请传入signMethod参数",
                9006 => "权大师调用错误：signMethod参数错误",
                9007 => "权大师调用错误：方法名称或api协议版本错误",
                9008 => "权大师调用错误：请求超时",
                9031 => "权大师调用错误：签名错误",
                9092 => "权大师调用错误：接口请求失败",
                _ => "权大师调用错误：超出响应枚举"
            };
        }
    }
}