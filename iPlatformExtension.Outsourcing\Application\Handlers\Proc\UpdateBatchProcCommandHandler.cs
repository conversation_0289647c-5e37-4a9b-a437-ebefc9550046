﻿using System.Text;
using iPlatformExtension.Outsourcing.Application.Commands;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class UpdateBatchProcCommandHandler(
    ISender sender, 
    ObjectPool<StringBuilder> stringBuilderPool,
    IForeignSupplierFeeRepository foreignSupplierFeeRepository) 
    : IRequestHandler<UpdateBatchProcCommand>
{
    public async Task Handle(UpdateBatchProcCommand request, CancellationToken cancellationToken)
    {
        var documents = request.Documents;
        var feesIds = documents.Select(document => document.Operations
            .Where(operation => operation.OperationType == OperationType.Replace && operation.path == "/feesId")
            .Select(operation => operation.value.ToString()).SingleOrDefault());
        
        var resultBuilder = await documents.ToAsyncEnumerable()
            .SelectAwaitWithCancellation(async (document, token) => await sender.Send(document, token))
            .Where(result => !result.Success).AggregateAsync(stringBuilderPool.Get(),
                (builder, result) => builder.Append(result.ItemId).Append(':').AppendLine(result.Message),
                cancellationToken);
        
        foreach (var feesId in feesIds.Where(id => !string.IsNullOrWhiteSpace(id)))
        {
            await foreignSupplierFeeRepository.DeleteAsync(feesId!);
        }

        var errorMessage = resultBuilder.ToString();
        stringBuilderPool.Return(resultBuilder);

        if (errorMessage.Length > 0)
        {
            throw new ApplicationException(errorMessage);
        }
    }
}