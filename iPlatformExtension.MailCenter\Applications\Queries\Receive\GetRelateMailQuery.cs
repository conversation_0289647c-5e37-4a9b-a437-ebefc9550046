﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.MailCenter.Applications.Models;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.Receive;

/// <summary>
/// 获取关联邮件列表
/// </summary>
/// <param name="MailId"></param>
public record GetRelateMailQuery([Required] string MailId) : PageModel, IRequest<IEnumerable<GetRelateMailDto>>;

