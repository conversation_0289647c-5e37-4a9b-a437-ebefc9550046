﻿using System.Linq.Expressions;
using FreeSql;

namespace iPlatformExtension.Repository.Extensions;

public static class BaseRepositoryExtension
{
    public static Task<TEntity?> GetAsync<TEntity>(this IBaseRepository<TEntity> repository, object key,
        Expression<Func<TEntity, TEntity>> selectExpression, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        return repository.Select.WhereDynamic(key).ToOneAsync(selectExpression, cancellationToken)!;
    }
}