﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 移交团队列表处理者
    /// </summary>
    /// <remarks>
    /// 移交团队处理者构造函数
    /// </remarks>
    public class HandoverTeamListQueryHandler(IFreeSql freeSql, IMediator mediator, IMemoryCache memoryCache, ISystemRoleInfoRepository systemRoleInfoRepository) : IRequestHandler<HandoverTeamListQuery, IEnumerable<HandoverTeamListDto>>
    {

        /// <summary>
        /// 移交团队处理者方法
        /// </summary>
        public async Task<IEnumerable<HandoverTeamListDto>> Handle(HandoverTeamListQuery request, CancellationToken cancellationToken)
        {
            var roleId = systemRoleInfoRepository.GetRole(SysEnum.RoleCode.TeamLeader, SysEnum.RoleType.TrademarkOperation);
            var handoverTeamListDto = freeSql.Select<SysTeam, SysTeamAssociateMember>().LeftJoin(it => it.t1.TeamId == it.t2.TeamId).Distinct()
                .Where(it => it.t2.RoleId == roleId && it.t1.IsEffect).ToList(it => new HandoverTeamListDto(it.t1.TeamId, it.t1.TeamName, it.t2.UserId, it.t1.IsExclusive));
            //团队成员数统计
            var teamMemberCountDtos = await mediator.Send(new TeamMemberCountQuery(), cancellationToken);
            //团队任务数统计
            var teamTaskCountDtos = await mediator.Send(new TeamCountQuery(FlowTypeEnum.AT, CaseType.Trade, SysEnum.FlowStatus.Execute.GetHashCode(), handoverTeamListDto.Select(it => it.UserId).ToArray()), cancellationToken);
            foreach (var handoverTeam in handoverTeamListDto)
            {
                var teamMemberCountDto = teamMemberCountDtos.FirstOrDefault(it => it.TeamId == handoverTeam.TeamId);
                if (teamMemberCountDto != null)
                {
                    handoverTeam.TeamMemberCount = teamMemberCountDto.TeamMemberCount;
                }
                var taskCount = teamTaskCountDtos.FirstOrDefault(it => it.UserId == handoverTeam.UserId);
                if (taskCount != null)
                {
                    handoverTeam.TeamTaskCount = taskCount.TeamTaskCount;
                }
            }

            return handoverTeamListDto;
        }
    }
}