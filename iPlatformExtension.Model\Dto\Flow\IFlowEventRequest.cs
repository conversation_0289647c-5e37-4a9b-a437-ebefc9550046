﻿namespace iPlatformExtension.Model.Dto.Flow;

/// <summary>
/// 流程事件请求接口
/// </summary>
public interface IFlowEventRequest
{
    /// <summary>
    /// 事件消息
    /// </summary>
    FlowEventMessage EventMessage { get; }
    
    /// <summary>
    /// 需要做版本号检查为false。
    /// 跳过检查为true。
    /// </summary>
    bool Checked { get; }
    
    /// <summary>
    /// 流程上下文所关联的业务实体ID
    /// </summary>
    string? ObjectId { get; set; }

    /// <summary>
    /// 关联的实体id是否必须有值
    /// </summary>
    bool RequiredObjectId => true;
}