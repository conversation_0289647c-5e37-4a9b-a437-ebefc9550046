using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_join_type", DisableSyncStructure = true)]
	public partial class CusJoinType {

		[ Column(Name = "join_type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string JoinTypeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "form_type", StringLength = 50)]
		public string FormType { get; set; }

		[ Column(Name = "join_type", StringLength = 50)]
		public string JoinType { get; set; }

	}

}
