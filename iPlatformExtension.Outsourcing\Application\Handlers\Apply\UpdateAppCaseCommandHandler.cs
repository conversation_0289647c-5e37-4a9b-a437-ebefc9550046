﻿using AutoMapper;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Outsourcing.Application.Commands.Apply;
using iPlatformExtension.Outsourcing.Application.Models.Apply;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Apply;

internal sealed class UpdateAppCaseCommandHandler(
    IMapper mapper,
    ICaseFeeRepository caseFeeRepository,
    IHttpContextAccessor httpContextAccessor,
    IAppCaseListRepository appCaseListRepository,
    IForeignSupplierFeeRepository foreignSupplierFeeRepository) : IRequestHandler<UpdateAppCaseCommand>
{
    public async Task Handle(UpdateAppCaseCommand request, CancellationToken cancellationToken)
    {
        var (appCaseId, document) = request;
        var appCaseList = await appCaseListRepository.GetAsync(appCaseId, cancellationToken);

        if (appCaseList == null)
        {
            throw new NotFoundException(appCaseId, "开案案件信息");
        }

        var dto = mapper.Map<ApplyCasePatchDto>(appCaseList);
        document.ApplyTo(dto);
        
        mapper.Map(dto, appCaseList);
        appCaseList.UpdateUserId =
            httpContextAccessor.HttpContext?.User.GetUserId() ?? throw new NotAuthenticatedException();
        appCaseList.UpdateTime = DateTime.Now;
        
        await appCaseListRepository.UpdateAsync(appCaseList, cancellationToken);

        if (dto.FeesId != null)
        {
            var fees = await foreignSupplierFeeRepository.GetAsync(dto.FeesId);
            await caseFeeRepository.InsertAsync(fees.Where(fee => fee is not null)
                .Select(fee =>
                {
                    fee!.FeeId = Guid.CreateVersion7(DateTimeOffset.Now).ToString();
                    fee.ProcId = appCaseId;
                    return fee;
                }).ToList(), cancellationToken);
        }
    }
}