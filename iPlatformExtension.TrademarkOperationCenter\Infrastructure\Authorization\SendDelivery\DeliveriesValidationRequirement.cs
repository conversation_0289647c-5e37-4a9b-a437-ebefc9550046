﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Authorization.SendDelivery;

/// <summary>
/// 定义一个用于批处理交付验证的需求类，实现了IAuthorizationRequirement接口。
/// </summary>
/// <param name="validateType">指定批处理交付的验证类型。</param>
public class DeliveriesValidationRequirement(BatchDeliveriesValidateType validateType) : IAuthorizationRequirement
{
    /// <summary>
    /// 获取批处理交付的验证类型。
    /// </summary>
    public BatchDeliveriesValidateType ValidateType { get; } = validateType;

    
    /// <summary>
    /// 根据不同的验证类型创建相应的验证命令对象。
    /// </summary>
    /// <param name="procIds">一组流程ID，用于指定需要进行验证的交付物。</param>
    /// <param name="deliveryButton">可选参数，指定交付按钮类型，影响命令对象的创建。</param>
    /// <returns>返回一个请求对象，用于执行批量交付验证；如果验证类型不匹配任何已知情况，则可能返回null。</returns>
    public IRequest<BatchDeliveryValidateResult>? CreateValidationCommand(IEnumerable<string> procIds, DeliveryButton? deliveryButton = null) =>
        ValidateType switch
        {
            // 根据启动验证类型创建启动交付验证命令
            BatchDeliveriesValidateType.Startup => new StartupDeliveryValidateCommand(procIds),
            // 根据更新验证类型创建更新交付验证命令
            BatchDeliveriesValidateType.Update => new UpdateDeliveryValidateCommand(procIds),
            // 根据流程提交验证类型创建提交交付验证命令
            BatchDeliveriesValidateType.FlowSubmit => new SubmitDeliveryValidateCommand(procIds),
            // 根据交付验证类型和特定的交付按钮创建启动交付验证命令；如果不匹配特定按钮则返回null
            BatchDeliveriesValidateType.Deliver => deliveryButton is DeliveryButton.StartUpDelivery ? new LaunchDeliveryValidateCommand(procIds) : null,
            // 根据交接验证类型创建交接交付验证命令
            BatchDeliveriesValidateType.HandOver => new HandOverDeliveryValidateCommand(procIds),
            // 根据拒绝验证类型创建拒绝交付验证命令
            BatchDeliveriesValidateType.Reject => new RejectDeliveryValidateCommand(procIds),
            // 根据通过T启动验证类型创建启动交付验证命令
            BatchDeliveriesValidateType.StartupByT => new StartupDeliveryValidateByTCommand(procIds),
            // 默认情况下返回null，表示不支持的验证类型
            _ => null
        };
}
