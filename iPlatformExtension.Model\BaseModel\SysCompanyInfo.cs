using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_company_info", DisableSyncStructure = true)]
	public partial class SysCompanyInfo {

		[ Column(Name = "company_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CompanyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "agency_id", StringLength = 50, IsNullable = false)]
		public string AgencyId { get; set; }

		[ Column(Name = "case_volume_stage", StringLength = 50)]
		public string CaseVolumeStage { get; set; }

		[ Column(Name = "city_id", StringLength = 50)]
		public string CityId { get; set; }

		[ Column(Name = "company_name_cn", StringLength = 100)]
		public string CompanyNameCn { get; set; }

		[ Column(Name = "company_name_en", StringLength = 200)]
		public string CompanyNameEn { get; set; }

		[ Column(Name = "contact_address_cn", StringLength = 100)]
		public string ContactAddressCn { get; set; }

		[ Column(Name = "contact_address_en", StringLength = 200)]
		public string ContactAddressEn { get; set; }

		[ Column(Name = "contact_name_cn", StringLength = 50)]
		public string ContactNameCn { get; set; }

		[ Column(Name = "contact_name_en", StringLength = 100)]
		public string ContactNameEn { get; set; }

		[ Column(Name = "corporation", StringLength = 100)]
		public string Corporation { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "email", StringLength = 500)]
		public string Email { get; set; }

		[ Column(Name = "email_name", StringLength = 50)]
		public string EmailName { get; set; }

		[ Column(Name = "email_user_name", StringLength = 50)]
		public string EmailUserName { get; set; }

		[ Column(Name = "email_user_pass", StringLength = 50)]
		public string EmailUserPass { get; set; }

		[ Column(Name = "fax", StringLength = 50)]
		public string Fax { get; set; }

		[ Column(Name = "found_day")]
		public DateTime? FoundDay { get; set; }

		[ Column(Name = "is_allocate_flow")]
		public bool? IsAllocateFlow { get; set; }

		[ Column(Name = "mobil", StringLength = 50)]
		public string Mobil { get; set; }

		[ Column(Name = "pop3_port")]
		public short? Pop3Port { get; set; }

		[ Column(Name = "pop3_server", StringLength = 50)]
		public string Pop3Server { get; set; }

		[ Column(Name = "postcode", StringLength = 50)]
		public string Postcode { get; set; }

		[ Column(Name = "province_id", StringLength = 50)]
		public string ProvinceId { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "smtp_port")]
		public short? SmtpPort { get; set; }

		[ Column(Name = "smtp_server", StringLength = 50)]
		public string SmtpServer { get; set; }

		[ Column(Name = "tel", StringLength = 50)]
		public string Tel { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
