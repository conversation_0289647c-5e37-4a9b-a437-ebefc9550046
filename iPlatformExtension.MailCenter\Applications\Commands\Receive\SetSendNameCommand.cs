﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.Receive;

/// <summary>
/// 设置发件名称命令
/// </summary>
/// <param name="MailId">邮件id</param>
/// <param name="SendName">发件名称</param>
/// <param name="MailActionType">邮件动作类型,Send:发件,Receive:收件</param>
public record SetSendNameCommand([Required(ErrorMessage = "MailId是必须的")] string MailId, string? SendName,string? MailActionType = "Receive") : IRequest, IUnitOfWorkCommandMysql;

