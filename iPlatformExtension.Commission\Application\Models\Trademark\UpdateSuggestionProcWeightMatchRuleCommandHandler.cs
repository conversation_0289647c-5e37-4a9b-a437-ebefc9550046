﻿using AutoMapper;
using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Models.Trademark;

internal sealed class UpdateSuggestionProcWeightMatchRuleCommandHandler(
    IMapper mapper,
    ISuggestionProcPointConfigRepository configRepository) 
    : IRequestHandler<UpdateSuggestionProcWeightMatchRuleCommand>
{
    public async Task Handle(UpdateSuggestionProcWeightMatchRuleCommand request, CancellationToken cancellationToken)
    {
        var (ruleId, document) = request;
        var config = await configRepository.GetAsync(ruleId, cancellationToken);

        if (config is null)
        {
            throw new NotFoundException(ruleId, "建议流程权值匹配规则");
        }
        
        var dto = mapper.Map<SuggestionProcWeightMatchRulePatchDto>(config);
        document.ApplyTo(dto);
        mapper.Map(dto, config);
        
        await configRepository.UpdateAsync(config, cancellationToken);
    }
}