﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class WinningRewardProcRepository(IFreeSql<PlatformFreeSql> freeSql, UnitOfWorkManage<PlatformFreeSql> uowManager)
    : AggregateRootRepository<WinningRewardProc>(freeSql, uowManager), IWinningRewardProcRepository;