﻿using System.Collections.Concurrent;
using System.Runtime.CompilerServices;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Validation;

public class AsyncValidationParameterBinder(
    IServiceProvider serviceProvider,
    IModelMetadataProvider modelMetadataProvider,
    IModelBinderFactory modelBinderFactory,
    IObjectModelValidator validator,
    IOptions<MvcOptions> mvcOptions,
    ILoggerFactory loggerFactory)
    : ParameterBinder(modelMetadataProvider, modelBinderFactory, validator, mvcOptions, loggerFactory)
{
    private static readonly ConcurrentDictionary<Type, Lazy<ValidationTypeEntry>> validationTypes = new(Environment.ProcessorCount, 111);
    
    [UnsafeAccessor(UnsafeAccessorKind.Field, Name = "_objectModelValidator")]
    private static extern ref IObjectModelValidator GetObjectModelGetValidator(ParameterBinder parameterBinder);
    
    [UnsafeAccessor(UnsafeAccessorKind.Method, Name = "EnforceBindRequiredAndValidate")]
    private static extern void EnforceBindRequiredValidate(
        ParameterBinder parameterBinder,
        ObjectModelValidator baseObjectValidator,
        ActionContext actionContext,
        ParameterDescriptor parameter,
        ModelMetadata metadata,
        ModelBindingContext modelBindingContext,
        ModelBindingResult modelBindingResult,
        object? container);
    
    public override async ValueTask<ModelBindingResult> BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider,
        ParameterDescriptor parameter, ModelMetadata metadata, object? value, object? container)
    {
        ArgumentNullException.ThrowIfNull(actionContext);
        ArgumentNullException.ThrowIfNull(modelBinder);
        ArgumentNullException.ThrowIfNull(valueProvider);
        ArgumentNullException.ThrowIfNull(parameter);
        ArgumentNullException.ThrowIfNull(metadata);
 
        if (parameter.BindingInfo?.RequestPredicate?.Invoke(actionContext) == false)
        {
            return ModelBindingResult.Failed();
        }
 
        var modelBindingContext = DefaultModelBindingContext.CreateBindingContext(
            actionContext,
            valueProvider,
            metadata,
            parameter.BindingInfo,
            parameter.Name);
        modelBindingContext.Model = value;
 
        var parameterModelName = parameter.BindingInfo?.BinderModelName ?? metadata.BinderModelName;
        if (parameterModelName != null)
        {
            modelBindingContext.ModelName = parameterModelName;
        }
        else if (modelBindingContext.ValueProvider.ContainsPrefix(parameter.Name))
        {
            modelBindingContext.ModelName = parameter.Name;
        }
        else
        {
            modelBindingContext.ModelName = string.Empty;
        }
 
        await modelBinder.BindModelAsync(modelBindingContext);
        
        var modelBindingResult = modelBindingContext.Result;

        if (modelBindingResult is {IsModelSet: true, Model: not null})
        {
            var modelType = modelBindingContext.ModelType;
            var (validatorType, validationContextType) = validationTypes.GetOrAdd(modelType,
                type => new Lazy<ValidationTypeEntry>(() => 
                    new ValidationTypeEntry(
                        typeof(IValidator<>).MakeGenericType(type), 
                        typeof(ValidationContext<>).MakeGenericType(type))))
                .Value;
            
            var validator = serviceProvider.GetService(validatorType);
            if (validator is IValidator validatorInstance)
            {
                var validationContext =
                    ActivatorUtilities.CreateInstance(serviceProvider, validationContextType, modelBindingResult.Model)
                        as IValidationContext;
                
                var validationResult = await validatorInstance.ValidateAsync(validationContext);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                return modelBindingResult;
            }
        }

        ref var modelValidator = ref GetObjectModelGetValidator(this);
        if (modelValidator is ObjectModelValidator objectModelValidator)
        {
            EnforceBindRequiredValidate(this, objectModelValidator, actionContext, parameter, metadata, modelBindingContext, modelBindingResult, container);
        }
        else if (modelBindingResult.IsModelSet)
            modelValidator.Validate(actionContext, modelBindingContext.ValidationState, modelBindingContext.ModelName, modelBindingResult.Model);
        return modelBindingResult;
    }

    
    private readonly record struct ValidationTypeEntry(Type ValidatorType, Type ValidationContextType);
}