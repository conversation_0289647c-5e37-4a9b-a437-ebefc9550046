﻿using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.Finance.Applications.Commands.CurrencyRate;
using iPlatformExtension.Finance.Applications.Models.CurrencyRate;
using MediatR;

namespace iPlatformExtension.Finance.Consumers;

[Consumer("currencyRate")]
internal sealed class CurrencyRateConsumer(IMediator mediator)
{
   [Operation("synchorize")]
   public Task SynchronousAsync(KingdeeCurrencyRate rate)
   {
      return mediator.Send(new UpdateCurrencyRateCommand(rate));
   }
}