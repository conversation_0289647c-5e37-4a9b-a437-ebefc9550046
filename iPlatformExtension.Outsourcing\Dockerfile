﻿FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
ENV ASPNETCORE_URLS=https://+:7162;http://+:5123
ENV ASPNETCORE_HTTP_PORTS=5123
ENV ASPNETCORE_HTTPS_PORTS=7162
ENV ASPNETCORE_ENVIRONMENT=Production
ENV TZ=Asia/Shanghai
ENV ASPNETCORE_Kestrel__Certificates__Default__Path=/https/cert.crt
ENV ASPNETCORE_Kestrel__Certificates__Default__KeyPath=/https/rsa_private.key
ENV DOTNET_GCHeapHardLimit=80000000
ENV DOTNET_DbgEnableMiniDump=1
ENV DOTNET_DbgMiniDumpTyp=4
ENV DOTNET_DbgMiniDumpName=/tmp/iplatform-outsourcing-%p-%t.dmp
ENV DOTNET_EnableCrashReport=1
ENV DOTNET_CreateDumpDiagnostics=1
WORKDIR /app
EXPOSE 5123
EXPOSE 7162

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["iPlatformExtension.Outsourcing/iPlatformExtension.Outsourcing.csproj", "iPlatformExtension.Outsourcing/"]
COPY ["iPlatformExtension.Common/iPlatformExtension.Common.csproj", "iPlatformExtension.Common/"]
COPY ["iPlatformExtension.Model/iPlatformExtension.Model.csproj", "iPlatformExtension.Model/"]
COPY ["iPlatformExtension.Repository/iPlatformExtension.Repository.csproj", "iPlatformExtension.Repository/"]
RUN dotnet restore "iPlatformExtension.Outsourcing/iPlatformExtension.Outsourcing.csproj"
COPY . .
WORKDIR "/src/iPlatformExtension.Outsourcing"
RUN dotnet build "iPlatformExtension.Outsourcing.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "iPlatformExtension.Outsourcing.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "iPlatformExtension.Outsourcing.dll"]
