﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface ICompanyRepository : 
    IBaseRepository<BasCompany, string>, 
    IScopeDependency, 
    IRedisCacheableRepository<string, BasCompany>,
    IStringKeyCacheableRepository<BasCompany>
{
    Task<BasCompany?> ICacheableRepository<string, BasCompany>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Select.WithLock().Where(company => company.CompanyId == key).FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasCompany>> ICacheableRepository<string, BasCompany>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().Take(100).ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasCompany>.GenerateKey(BasCompany value)
    {
        return value.CompanyId;
    }

    string? IStringKeyCacheableRepository<BasCompany>.GetCacheTextValue(BasCompany value)
    {
        return value.CompanyNameCn;
    }
}