using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_merge_join_list", DisableSyncStructure = true)]
	public partial class CusMergeJoinList {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 关联栏位ID
		/// </summary>
		[ Column(Name = "datebase_id", StringLength = 100)]
		public string DatebaseId { get; set; }

		[ Column(Name = "init_id", StringLength = 50)]
		public string InitId { get; set; }

		[ Column(Name = "is_seperate")]
		public bool? IsSeperate { get; set; } = false;

		[ Column(Name = "merge_id", StringLength = 50)]
		public string MergeId { get; set; }

		/// <summary>
		/// 更新后客户ID
		/// </summary>
		[ Column(Name = "new_id", StringLength = 50)]
		public string NewId { get; set; }

		/// <summary>
		/// 关联表主键ID
		/// </summary>
		[ Column(Name = "obj_id", StringLength = -2)]
		public string ObjId { get; set; }

		/// <summary>
		/// 更新前客户ID
		/// </summary>
		[ Column(Name = "old_id", StringLength = 50)]
		public string OldId { get; set; }

		/// <summary>
		/// 关联表名称
		/// </summary>
		[ Column(Name = "table_name", StringLength = 100)]
		public string TableName { get; set; }

	}

}
