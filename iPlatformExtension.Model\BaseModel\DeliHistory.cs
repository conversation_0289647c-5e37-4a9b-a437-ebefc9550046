using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_history", DisableSyncStructure = true)]
	public partial class DeliHistory {

		[ Column(Name = "history_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string HistoryId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "error_message", StringLength = 1000)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "finish_time")]
		public DateTime? FinishTime { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "start_time")]
		public DateTime StartTime { get; set; }

		[ Column(Name = "status", StringLength = 50)]
		public string Status { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

		/// <summary>
		/// 操作名称
		/// </summary>
		[Column(Name = "operation", StringLength = 50)]
		public string Operation { get; set; } = string.Empty;

		/// <summary>
		/// 打印预览的jsonId
		/// </summary>
		[Column(Name = "display_json_id", StringLength = 50)]
		public int? DisplayJsonId { get; set; }

		/// <summary>
		/// 是否显示
		/// </summary>
		[Column(Name = "display")]
		public bool Display { get; set; } = true;

		/// <summary>
		/// 打印预览json导航属性
		/// </summary>
		[Navigate(nameof(DisplayJsonId))]
		public DeliveryDisplayJson? DisplayJson { get; set; }

	}

}
