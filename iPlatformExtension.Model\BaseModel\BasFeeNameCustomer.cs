using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_fee_name_customer", DisableSyncStructure = true)]
	public partial class BasFeeNameCustomer {

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "fee_name_en_us", StringLength = 200)]
		public string FeeNameEnUs { get; set; }

		[ Column(Name = "fee_name_id", StringLength = 50, IsNullable = false)]
		public string FeeNameId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "fee_name_zh_cn", StringLength = 200)]
		public string FeeNameZhCn { get; set; }

		[ Column(Name = "fee_type_id", StringLength = 50, IsNullable = false)]
		public string FeeTypeId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

	}

}
