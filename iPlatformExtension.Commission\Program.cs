using System.Reflection;
using System.Security.Claims;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Unicode;
using System.Threading.Channels;
using iPlatformExtension.Commission.Application.Commands.Patent;
using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;
using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Commission.Application.Models.Patent;
using iPlatformExtension.Commission.Clients;
using iPlatformExtension.Commission.Grpc;
using iPlatformExtension.Commission.Infrastructure.Authorization;
using iPlatformExtension.Commission.Infrastructure.BackgroundServices;
using iPlatformExtension.Commission.Infrastructure.Interceptors;
using iPlatformExtension.Commission.Infrastructure.OpenApi;
using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Common.Authorization;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Db;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Filters;
using iPlatformExtension.Common.Formatters.Input;
using iPlatformExtension.Common.Mediator.PipelineBehaviors;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Common.Validation;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Nacos.AspNetCore.V2;
using NLog.Web;
using SkyApm.AspNetCore.Diagnostics;

[assembly:ApplicationPart("iPlatformExtension.Common")]

var builder = WebApplication.CreateBuilder(args);
var services = builder.Services;
var configuration = builder.Configuration;
var logging = builder.Logging;
var environment = builder.Environment;

logging.ClearProviders();
logging.AddSimpleConsole(options => options.IncludeScopes = true);
logging.AddNLogWeb(new NLogAspNetCoreOptions
{
    RemoveLoggerFactoryFilter = false,
    LoggingConfigurationSectionName = "NLog"
});

if (!environment.IsLocal())
{
    services.AddNacosAspNet(configuration);
    configuration.AddNacosV2Configuration(configuration.GetSection("nacos"));
    services.AddGrpcClientFromNacos<PeriodSettingService.PeriodSettingServiceClient>("commission-service");
    services.AddGrpcClientFromNacos<CommissionService.CommissionServiceClient>("commission-service");
    services.AddNacosServiceDiscovery(options =>
    {
        options.Groups = ["DEFAULT_GROUP"];
    });
}



services.AddDataService().AddCache().ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
{
    options.ConnectionString = configuration.GetConnectionString("Redis");
    options.Converters.Add(new ClaimsIdentityConverter());
});

services.AddPlatformFreeSql(configuration.GetConnectionString("Default") ??
                            throw new NullReferenceException("Default connection string"))
    .ConfigureGlobalCurdLogging("Logging:FreeSql:Curd");

services.AddAutoMapper(typeof(Program));
services.AddObjectPools();
services.AddHttpLogging(options =>
{
    options.LoggingFields |= HttpLoggingFields.RequestHeaders;
    options.LoggingFields |= HttpLoggingFields.RequestPath;
    options.LoggingFields |= HttpLoggingFields.RequestQuery;
    options.LoggingFields |= HttpLoggingFields.RequestBody;
    options.LoggingFields |= HttpLoggingFields.ResponsePropertiesAndHeaders;
    options.LoggingFields |= HttpLoggingFields.ResponseBody;
    options.RequestBodyLogLimit = 327680;
});
services.AddHttpContextAccessor();

services.TryAddSingleton<EntityTypeInfoProvider>();

services.AddMediatR(options =>
{
    options.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
    options.AddOpenBehavior(typeof(BackgroundTracingCommandPipelineBehavior<,>), ServiceLifetime.Scoped);
    options.AddOpenBehavior(typeof(ScopeLoggerPipelineBehavior<,>));
    options.AddOpenBehavior(typeof(DefaultFreeSqlUnitOfWorkPipelineBehavior<,>), ServiceLifetime.Scoped);
});

if (!environment.IsProduction())
{
    services.AddSwaggerGen(c => 
    { 
        c.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = builder.Environment.ApplicationName, 
            Version = "v1",
            Description = "提成扩展"
        });
        var documentPath = Path.Combine(AppContext.BaseDirectory, $"{builder.Environment.ApplicationName}.xml");
        c.IncludeXmlComments(documentPath, true);

        var modelPath = Path.Combine(AppContext.BaseDirectory, "iPlatformExtension.Model.xml");
        c.IncludeXmlComments(modelPath, true);
        c.OrderActionsBy(o => o.RelativePath);
        
        c.OperationFilter<OpenApiHeaderFilter>();
    });
    
}
else
{
    services.AddEntityChangeLogs();
    services.AddMongoDbContext<PlatformMongoDbContext>(configuration.GetConnectionString("Log") ?? throw new NullReferenceException("Log connection string"));
}

services.AddAuthentication().AddScheme<BladeAuthOptions, BladeAuthenticationHandler>(BladeAuthOptions.SchemeName, options =>
{
    options.TokenValidationParameters = new TokenValidationParameters()
    {
        ValidateIssuer = false,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidateAudience = false,
        IssuerSigningKey = new SymmetricSecurityKey(configuration["BladeAuth:SecurityKey"]?.GetBytes(Encoding.UTF8))
    };
    options.Events.OnTokenValidated = AuthenticationExtension.ValidateUserAsync;
}).AddJwtBearer(PlatformAuthOptions.SchemeName, options =>
{
    var platformAuthOptions = configuration.GetSection("IPlatformAuth").Get<PlatformAuthOptions>();
    ArgumentNullException.ThrowIfNull(platformAuthOptions);
    options.TokenValidationParameters = new TokenValidationParameters()
    {
        ValidAlgorithms = [SecurityAlgorithms.HmacSha256],
        ValidateLifetime = true,
        ValidIssuers = platformAuthOptions.Issuers,
        ValidateIssuerSigningKey = true,
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidAudiences = platformAuthOptions.Audiences,
        IssuerSigningKey = new SymmetricSecurityKey(platformAuthOptions.SecurityKey.GetBytes(Encoding.UTF8))
    };

    options.Events = new JwtBearerEvents()
    {
        OnTokenValidated = AuthenticationExtension.ValidateUserAsync,
        OnChallenge = AuthenticationExtension.OnChallengeAsync,
    };
    options.ForwardChallenge = BladeAuthOptions.SchemeName;
    options.ForwardForbid = BladeAuthOptions.SchemeName;
});

services.AddTransient<IAuthorizationMiddlewareResultHandler, DefaultAuthorizationResultHandler>();
services.AddScoped<IAuthorizationHandler, CommissionUserDetailAuthorizationHandler>();
services.AddAuthorizationBuilder().AddPolicy("国内商标权值用户明细", policy =>
{
    policy.RequireClaim(ClaimTypes.GroupSid);
    policy.RequireClaim(ClaimTypes.NameIdentifier);
    policy.RequireAuthenticatedUser();
    policy.AddRequirements(new CommissionUserDetailRequirement("国内商标权值"));
}).AddPolicy("出口商标权值用户明细", policy =>
{
    policy.RequireClaim(ClaimTypes.GroupSid);
    policy.RequireClaim(ClaimTypes.NameIdentifier);
    policy.RequireAuthenticatedUser();
    policy.AddRequirements(new CommissionUserDetailRequirement("出口商标权值"));
}).AddPolicy("国内商标胜诉奖励用户明细", policy =>
{
    policy.RequireClaim(ClaimTypes.GroupSid);
    policy.RequireClaim(ClaimTypes.NameIdentifier);
    policy.RequireAuthenticatedUser();
    policy.AddRequirements(new CommissionUserDetailRequirement("国内商标胜诉奖励"));
});


services.AddControllers(options =>
{
    options.Filters.Add<ActionExceptionFilter<ResultData>>();
    options.Filters.Add<ModelValidationExceptionFilter>();
    options.Filters.Add<ActionResultFilter<ResultData>>();
    options.InputFormatters.Insert(0, JsonPatchInputFormatterExtension.GetJsonPatchInputFormatter());
}).AddJsonOptions(options =>
{
    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.DictionaryKeyPolicy = JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
    options.JsonSerializerOptions.Converters.Add(new DateTimeJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new DateOnlyJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new NullableDateTimeJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new JsonPatchOperationConverterFactory());
    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter(JsonNamingPolicy.CamelCase));

    if (!environment.IsProduction())
    {
        options.JsonSerializerOptions.WriteIndented = true;
    }
});

services.AddValidation(new ModelValidationOptions()
{
    Lifetime = ServiceLifetime.Scoped,
    ParameterBinder = typeof(AsyncValidationParameterBinder)
});

if (!environment.IsLocal())
{
    services.AddSkyAPM(extensions => extensions.AddAspNetCoreHosting());
}

services.AddSingleton(Channel.CreateBounded<CreateDomesticCommissionWeightCommand>(new BoundedChannelOptions(1)
{
    SingleReader = true,
    FullMode = BoundedChannelFullMode.DropWrite,
}));

services.AddSingleton(Channel.CreateBounded<PushDomesticCommissionWeightCommand>(new BoundedChannelOptions(5)
{
    SingleReader = true,
    FullMode = BoundedChannelFullMode.Wait
}));

services.AddSingleton(Channel.CreateBounded<CreateForeignCommissionWeightCommand>(new BoundedChannelOptions(1)
{
    SingleReader = true,
    FullMode = BoundedChannelFullMode.DropWrite
}));

services.AddSingleton(Channel.CreateBounded<PushForeignCommissionWeightCommand>(new BoundedChannelOptions(5)
{
    SingleReader = true,
    FullMode = BoundedChannelFullMode.Wait
}));

services.AddSingleton(Channel.CreateBounded<CreateRewardsCommand>(new BoundedChannelOptions(1)
{
    SingleReader = true,
    FullMode = BoundedChannelFullMode.DropWrite
}));
services.AddSingleton(Channel.CreateBounded<PushDomesticRewardCommand>(new BoundedChannelOptions(5)
{
    SingleReader = true,
    FullMode = BoundedChannelFullMode.Wait
}));
services.AddSingleton(Channel.CreateBounded<RefreshProcRewardDateCommand>(new BoundedChannelOptions(10)
{
    SingleReader = true,
    FullMode = BoundedChannelFullMode.Wait
}));
services.AddSingleton(Channel.CreateBounded<PushPatentCommissionWeightCommand>(new BoundedChannelOptions(5)
{
    SingleReader = true,
    FullMode = BoundedChannelFullMode.Wait
}));

services.AddHostedService<DomesticTrademarkCommissionWeightBackgroundService>();
services.AddHostedService<DomesticTrademarkCommissionWeightPushingService>();
services.AddHostedService<ForeignTrademarkCommissionWeightBackgroundService>();
services.AddHostedService<ForeignTrademarkCommissionWeightPushingService>();
services.AddHostedService<DomesticTrademarkRewardBackgroundService>();
services.AddHostedService<DomesticTrademarkRewardPushingService>();
services.AddHostedService<RewardRuleChangedNotificationService>();
services.AddHostedService<PatentCommissionWeightPushingService>();

services.AddOptions<PatentPushingOptions>().BindConfiguration("PatentPushingOptions");

var app = builder.Build();

app.UseExceptionHandler("/Error");

app.UseW3CTraceResponse();

if (!app.Environment.IsProduction())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.HandleUnsuccessfulResponse();

app.UseStaticFiles();

app.UseRequestEnableBuffering();

app.UseHttpLogging();

app.UseRouting();

app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();

app.Run();