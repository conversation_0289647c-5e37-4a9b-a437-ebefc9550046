using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_payment_list", DisableSyncStructure = true)]
	public partial class BasPaymentList {

		[ Column(Name = "cur_id", StringLength = 50, IsNullable = false)]
		public string CurId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "fee_name_id", StringLength = 50)]
		public string FeeNameId { get; set; }

		[ Column(Name = "item_end", StringLength = 50)]
		public string ItemEnd { get; set; }

		[ Column(Name = "item_start", StringLength = 50)]
		public string ItemStart { get; set; }

		[ Column(Name = "item_type", StringLength = 50)]
		public string ItemType { get; set; }

		[ Column(Name = "setting_id", StringLength = 50)]
		public string SettingId { get; set; }

	}

}
