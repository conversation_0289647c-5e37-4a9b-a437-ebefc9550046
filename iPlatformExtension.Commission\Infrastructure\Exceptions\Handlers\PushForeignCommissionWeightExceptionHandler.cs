﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class PushForeignCommissionWeightExceptionHandler(ILogger<PushForeignCommissionWeightCommand> logger)
    : IRequestExceptionHandler<PushForeignCommissionWeightCommand, Unit, Exception>
{
    public Task Handle(PushForeignCommissionWeightCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        logger.LogPushingForeignTrademarkWeightError(exception);
        state.SetHandled(Unit.Value);
        
        return Task.CompletedTask;
    }
}