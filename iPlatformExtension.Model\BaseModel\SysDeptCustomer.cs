using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_dept_customer", DisableSyncStructure = true)]
	public partial class SysDeptCustomer {

		[ Column(Name = "key_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string KeyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "dept_id", StringLength = 50)]
		public string DeptId { get; set; }

	}

}
