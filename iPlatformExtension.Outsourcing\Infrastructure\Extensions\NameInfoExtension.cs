﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;

namespace iPlatformExtension.Outsourcing.Infrastructure.Extensions;

internal static class NameInfoExtension
{
    public static ForeignAgencyInfo CreateForeignAgencyInfo(this ForeignSupplier foreignSupplier)
    {
        return new ForeignAgencyInfo(foreignSupplier.SupplierId, foreignSupplier.CnName, foreignSupplier.EnName);
    }
}