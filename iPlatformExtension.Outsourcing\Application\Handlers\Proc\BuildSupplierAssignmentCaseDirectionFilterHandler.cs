﻿using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using iPlatformExtension.Outsourcing.Infrastructure.Authorization.SupplierAssignment;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

// internal sealed class BuildSupplierAssignmentCaseDirectionFilterHandler : IBuildSupplierAssignmentFilterHandler
// {
//     public Task HandleAsync(SupplierAssignmentAuthorizationContext context, CancellationToken cancellationToken)
//     {
//         var (_, caseDirections, _, filters, query) = context;
//
//         if (filters.TryGetValue("case_direction", out var filterEntry) && filterEntry.Values.Length > 0)
//         {
//             caseDirections.IntersectWith(filterEntry.Values.ToArray());
//         }
//
//         query.Where(info => caseDirections.Contains(info.CaseInfo.CaseDirection));
//         return Task.CompletedTask;
//     }
// }

internal sealed class BuildSupplierAssignmentCaseDirectionFilterHandler : IMatchNotificationHandler<NotAssignedProcQueryContext>
{
    private string[] _caseDirections = [];
    
    public ValueTask<bool> MatchAsync(NotAssignedProcQueryContext notification, CancellationToken cancellationToken)
    {
        var filters = notification.Filters;
        if (filters.TryGetValue("case_direction", out var filter) 
            && "custom".Equals(filter.FilterType, StringComparison.OrdinalIgnoreCase))
        {
            _caseDirections = filter.FilterValue.Split(';');
            return ValueTask.FromResult(true);
        }

        return ValueTask.FromResult(false);
    }

    public Task HandleAsync(NotAssignedProcQueryContext notification, CancellationToken cancellationToken)
    {
        IEnumerable<string> caseDirections = _caseDirections;
        var query = notification.Query;
        query.WhereIf(_caseDirections.Length > 0, info => caseDirections.Contains(info.CaseInfo.CaseDirection));
        notification.CountQueryBuilder.WhereIf(_caseDirections.Length > 0,
            info => caseDirections.Contains(info.CaseInfo.CaseDirection));
        return Task.CompletedTask;
    }
}