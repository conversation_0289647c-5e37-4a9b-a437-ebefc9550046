using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_notice_office", DisableSyncStructure = true)]
	public partial class CaseNoticeOffice {

		[ Column(Name = "notice_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string NoticeId { get; set; }

		/// <summary>
		/// 申请日
		/// </summary>
		[ Column(Name = "app_date")]
		public DateTime? AppDate { get; set; }

		/// <summary>
		/// 申请号
		/// </summary>
		[ Column(Name = "app_no", StringLength = 50)]
		public string AppNo { get; set; }

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "cert_no", StringLength = 50)]
		public string CertNo { get; set; }

		[ Column(Name = "certificate_no", StringLength = 50)]
		public string CertificateNo { get; set; }

		[ Column(Name = "convey_date")]
		public DateTime? ConveyDate { get; set; }

		[ Column(Name = "convey_id", StringLength = 50)]
		public string ConveyId { get; set; }

		[ Column(Name = "convey_status")]
		public int ConveyStatus { get; set; } = 0;

		[ Column(Name = "convey_type", StringLength = 50, IsNullable = false)]
		public string ConveyType { get; set; } = "0";

		[ Column(Name = "ctrl_proc_property", StringLength = 50)]
		public string CtrlProcProperty { get; set; }

		/// <summary>
		/// 下载日期
		/// </summary>
		[ Column(Name = "download_time")]
		public DateTime? DownloadTime { get; set; }

		/// <summary>
		/// 发明名称
		/// </summary>
		[ Column(Name = "famingmc")]
		public string Famingmc { get; set; }

		/// <summary>
		/// 发文序列号
		/// </summary>
		[ Column(Name = "fawenxlh", StringLength = 50)]
		public string Fawenxlh { get; set; }

		[ Column(Name = "fee_status")]
		public int? FeeStatus { get; set; } = 0;

		[ Column(Name = "fee_update_time")]
		public DateTime? FeeUpdateTime { get; set; }

		[ Column(Name = "fee_update_user_id", StringLength = 50)]
		public string FeeUpdateUserId { get; set; }

		/// <summary>
		/// 表格代码
		/// </summary>
		[ Column(Name = "file_code", StringLength = 50)]
		public string FileCode { get; set; }

		[ Column(Name = "file_no", StringLength = 50)]
		public string FileNo { get; set; }

		[ Column(Name = "file_no_zip", StringLength = 50)]
		public string FileNoZip { get; set; }

		/// <summary>
		/// 公布号
		/// </summary>
		[ Column(Name = "gongbuh", StringLength = 50)]
		public string Gongbuh { get; set; }

		/// <summary>
		/// 公布日期
		/// </summary>
		[ Column(Name = "gongbur")]
		public DateTime? Gongbur { get; set; }

		[ Column(Name = "input_time")]
		public DateTime? InputTime { get; set; }

		[ Column(Name = "input_user_id", StringLength = 50)]
		public string InputUserId { get; set; }

		[ Column(Name = "is_cpc")]
		public bool IsCpc { get; set; } = true;

		[ Column(Name = "is_pagery")]
		public bool? IsPagery { get; set; } = false;

		[ Column(Name = "issue_date")]
		public DateTime? IssueDate { get; set; }

		[ Column(Name = "issue_no", StringLength = 50)]
		public string IssueNo { get; set; }

		[ Column(Name = "jinrussr")]
		public DateTime? Jinrussr { get; set; }

		/// <summary>
		/// 官方期限
		/// </summary>
		[ Column(Name = "legal_due_date")]
		public DateTime? LegalDueDate { get; set; }

		/// <summary>
		/// 发文日期
		/// </summary>
		[ Column(Name = "notice_date")]
		public DateTime? NoticeDate { get; set; }

		/// <summary>
		/// 国际申请号
		/// </summary>
		[ Column(Name = "pct_app_no", StringLength = 50)]
		public string PctAppNo { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "qianzhangbj")]
		public bool? Qianzhangbj { get; set; } = false;

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "shenqingbh", StringLength = 50)]
		public string Shenqingbh { get; set; }

		[ Column(Name = "shenqinglx", StringLength = 50)]
		public string Shenqinglx { get; set; }

		[ Column(Name = "status", StringLength = 20)]
		public string Status { get; set; }

		/// <summary>
		/// 通知书编号
		/// </summary>
		[ Column(Name = "tongzhisbh", StringLength = 50)]
		public string Tongzhisbh { get; set; }

		/// <summary>
		/// 通知书名称
		/// </summary>
		[ Column(Name = "tongzhismc")]
		public string Tongzhismc { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// 我方文号
		/// </summary>
		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

	}

}
