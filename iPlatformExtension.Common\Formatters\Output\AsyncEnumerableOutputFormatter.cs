﻿using iPlatformExtension.Common.Attributes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.Formatters;

namespace iPlatformExtension.Common.Formatters.Output;

public abstract class AsyncEnumerableOutputFormatter : OutputFormatter
{
    protected override bool CanWriteType(Type? type)
    {
        return type?.IsGenericType == true && type.GetGenericTypeDefinition() == typeof(IAsyncEnumerable<>);
    }

    public override bool CanWriteResult(OutputFormatterCanWriteContext context)
    {
        var httpContext = context.HttpContext;
        var endpoint = httpContext.GetEndpoint();
        return endpoint?.Metadata.GetMetadata<AsyncStreamAttribute>() is not null && base.CanWriteResult(context);
    }

    public override async Task WriteResponseBodyAsync(OutputFormatterWriteContext context)
    {
        var asyncEnumerable = (IAsyncEnumerable<object>)context.Object!;
        var httpContext = context.HttpContext;
        var endpoint = httpContext.GetEndpoint();
        var responseOptions = endpoint?.Metadata.GetMetadata<AsyncStreamAttribute>();
        var stopOnExceptions = responseOptions?.StopOnException ?? true;
        var delay = responseOptions?.Delay ?? 0;
        var enumerator = asyncEnumerable.GetAsyncEnumerator(httpContext.RequestAborted);
        
        var bufferingFeature = httpContext.Features.GetRequiredFeature<IHttpResponseBodyFeature>();
        bufferingFeature.DisableBuffering();
        
        moveNext:
        try
        {
            while (await enumerator.MoveNextAsync())
            {
                var item = enumerator.Current;
                await WriteAsync(context, item, httpContext.RequestAborted);
                
                if (delay > 0)
                {
                    await Task.Delay(delay);
                }
            }
        }
        catch (Exception e)
        {
            await WriteErrorAsync(context, e, httpContext.RequestAborted);
            
            if (delay > 0)
            {
                await Task.Delay(delay);
            }
            
            if (!stopOnExceptions)
            {
                goto moveNext;
            }
            
        }
        
        await httpContext.Response.BodyWriter.CompleteAsync();
    }
    
    protected abstract Task WriteAsync(OutputFormatterWriteContext context, object item,
        CancellationToken cancellationToken);

    protected abstract Task WriteErrorAsync(OutputFormatterWriteContext context, Exception exception,
        CancellationToken cancellationToken);
}