﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Nice;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Nice;

internal sealed class InsertProcNiceCommandHandler(
    IHttpContextAccessor httpContextAccessor,
    ICaseProcInfoRepository caseProcInfoRepository,
    IProcNiceCategoryRepository procNiceCategoryRepository) : 
    IRequestHandler<InsertProcNiceCommand>
{
    public async Task Handle(InsertProcNiceCommand request, CancellationToken cancellationToken)
    {
        
        var procCategories = request.CustomCategories
            .Select(dto => new ProcNiceCategory()
            {
                Id = 0,
                ProcId = request.ProcId,
                CategoryName = dto.Description,
                GrandNumber = dto.CategoryId,
                CreationTime = DateTime.Now,
                UpdateTime = DateTime.Now,
                IsStandard = false,
                Order = dto.Order
            }).OrderBy(category => category.GrandNumber).ToList();

        if (procCategories.Count > 0)
        {
            await procNiceCategoryRepository.InsertAsync(procCategories, cancellationToken);
        }

        var grandNumbers = new HashSet<int>(request.GrandNumbers.Select(grandNumber => Convert.ToInt32(grandNumber)));
        if (procCategories.Count > 0)
        {
            grandNumbers.UnionWith(request.CustomCategories.Select(dto => Convert.ToInt32(dto.CategoryId)));
        }
        
        var procInfo = await caseProcInfoRepository.GetAsync(request.ProcId, procInfo => new CaseProcInfo
        {
            ProcId = procInfo.ProcId,
            TrademarkNiceClasses = procInfo.TrademarkNiceClasses,
            Version = procInfo.Version,
            UpdateTime = procInfo.UpdateTime,
            UpdateUserId = procInfo.UpdateUserId
        }, cancellationToken);

        if (procInfo is null)
        {
            throw new NotFoundException(request, "案件任务");
        }
        
        procInfo.TrademarkNiceClasses = string.Join(';', grandNumbers.Order());
        procInfo.UpdateTime = DateTime.Now;
        procInfo.UpdateUserId = (httpContextAccessor.HttpContext?.User).GetUserId();

        await caseProcInfoRepository.UpdateAsync(procInfo, cancellationToken);
    }
}