﻿using System.Reflection;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;
using FreeSql;
using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Common.Authentication.Providers;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Filters;
using iPlatformExtension.Common.Mediator.PipelineBehaviors;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.MailCenter.HostedService;
using iPlatformExtension.MailCenter.Infrastructure.Extension;
using iPlatformExtension.MailCenter.Infrastructure.MQ;
using iPlatformExtension.MailCenter.Infrastructure.Provide;
using iPlatformExtension.Model.Dto;
using KafkaProvider;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Nacos.AspNetCore.V2;
using Nacos.V2;
using NLog.Web;

[assembly: ApplicationPart("iPlatformExtension.Common")]

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

var configuration = builder.Configuration;
var environment = builder.Environment;
var logging = builder.Logging;
logging.ClearProviders();
logging.AddSimpleConsole(options => options.IncludeScopes = true);
logging.AddNLogWeb(
    new NLogAspNetCoreOptions
    {
        RemoveLoggerFactoryFilter = false,
        LoggingConfigurationSectionName = "NLog",
    }
);

if (!environment.IsLocal())
{
    configuration.AddNacosV2Configuration(configuration.GetSection("nacos"));
    builder.Services.AddNacosAspNet(configuration);
    builder.Services.AddNacosServiceDiscovery(options =>
        options.AllowedSchemes = [Uri.UriSchemeHttp]
    );
}
else
{
    configuration.AddJsonFile($"clients.{environment.EnvironmentName}.json", true, true);
    configuration.AddJsonFile($"mqs.{environment.EnvironmentName}.json", true, true);
}

if (!environment.IsProduction())
{
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc(
            "v1",
            new OpenApiInfo
            {
                Title = builder.Environment.ApplicationName,
                Version = "2.8.2.RELEASE-v2.7.11",
                Description = "邮件中心服务",
            }
        );
        var documentPath = Path.Combine(
            AppContext.BaseDirectory,
            $"{environment.ApplicationName}.xml"
        );
        c.IncludeXmlComments(documentPath, true);

        var modelPath = Path.Combine(AppContext.BaseDirectory, "iPlatformExtension.Model.xml");
        c.IncludeXmlComments(modelPath, true);
        c.OrderActionsBy(o => o.RelativePath);
    });
}

builder.Services.AddHttpLogging(options =>
{
    options.LoggingFields |= HttpLoggingFields.RequestHeaders;
    options.LoggingFields |= HttpLoggingFields.RequestPath;
    options.LoggingFields |= HttpLoggingFields.RequestQuery;
    options.LoggingFields |= HttpLoggingFields.RequestBody;
    options.LoggingFields |= HttpLoggingFields.ResponsePropertiesAndHeaders;
    options.RequestBodyLogLimit = 327680;
});
builder
    .Services.AddFreeSql<MailCenterFreeSql>(options =>
    {
        options.CommandTimeout = TimeSpan.FromSeconds(300);
        options.Lifetime = ServiceLifetime.Singleton;
        options.ConnectionString = configuration.GetConnectionString("mysql") ?? string.Empty;
        options.DbType = DataType.MySql;
        options.LazyLoading = true;
        options.CommandParameterWithLambda = true;
        options.UseAdoConnectionPool = true;
        options.TypeHandlers.TryAdd(typeof(DateOnly), new NullableDateOnlyConverter());
      
    })
    .ConfigureGlobalCurdLogging("Logging:FreeSql:Curd");
builder
    .Services.AddPlatformFreeSql(configuration.GetConnectionString("Default") ?? string.Empty, serviceLifetime: ServiceLifetime.Singleton)
    .ConfigureGlobalCurdLogging("Logging:FreeSql:Curd");
builder.Services.AddDataService();

builder.Services.AddAutoMapper(typeof(Program));
builder.Services.AddObjectPools();
builder
    .Services.AddCache()
    .ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
    {
        options.ConnectionString = configuration.GetConnectionString("Redis");
        options.Converters.Add(new ClaimsIdentityConverter());
    });

builder
    .Services.AddAuthentication(options =>
    {
        options.DefaultScheme = PlatformAuthOptions.SchemeName;
        options.DefaultChallengeScheme = BladeAuthOptions.SchemeName;
        options.DefaultForbidScheme = BladeAuthOptions.SchemeName;
    })
    .AddJwtBearer(
        PlatformAuthOptions.SchemeName,
        options =>
        {
            var platformAuthenticationOptions = configuration.Get<PlatformAuthOptions>(
                sectionKey: "IPlatformAuth"
            );
            options.TokenValidationParameters = new TokenValidationParameters
            {
                IssuerSigningKey = new SymmetricSecurityKey(
                    platformAuthenticationOptions?.SecurityKey.GetBytes(Encoding.UTF8)
                        ?? throw new ArgumentNullException(nameof(configuration))
                ),
                ValidateIssuerSigningKey = true,
                ValidAudiences = platformAuthenticationOptions.Audiences,
                ValidIssuers = platformAuthenticationOptions.Issuers,
            };
            options.Events = new JwtBearerEvents
            {
                OnTokenValidated = AuthenticationExtension.ValidateUserAsync,
                OnMessageReceived = AuthenticationExtension.GetTokenAsync,
                OnAuthenticationFailed = AuthenticationExtension.AuthenticateFailedAsync,
            };
            options.SaveToken = true;
            options.ForwardChallenge = BladeAuthOptions.SchemeName;
            options.ForwardForbid = BladeAuthOptions.SchemeName;
            options.ForwardDefaultSelector = context =>
                context.Request.Headers.Authorization.Count > 0
                    ? PlatformAuthOptions.SchemeName
                    : null;
        }
    )
    .AddScheme<BladeAuthOptions, BladeAuthenticationHandler>(
        BladeAuthOptions.SchemeName,
        "bladeX token验证",
        options =>
        {
            options.Events.OnTokenValidated = AuthenticationExtension.ValidateUserAsync;
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                ValidateIssuer = false,
                ValidateAudience = false,
                IssuerSigningKey = new SymmetricSecurityKey(
                    configuration["BladeAuth:SecurityKey"]?.GetBytes(Encoding.UTF8)
                        ?? throw new ArgumentNullException(
                            "BladeAuth:SecurityKey",
                            "缺少blade-auth秘钥"
                        )
                ),
            };
            options.ForwardDefaultSelector = context =>
                context.Request.Headers.ContainsKey(options.TokenHeaderName)
                    ? BladeAuthOptions.SchemeName
                    : null;
        }
    )
    .ReplaceAuthenticationSchemeProvider<ForwardAuthenticationSchemeProvider>();
builder.Services.AddAuthorizationBuilder();
builder.Services.AddMediatR(serviceConfiguration =>
{
    serviceConfiguration.Lifetime = ServiceLifetime.Scoped;
    serviceConfiguration.AddOpenBehavior(typeof(MysqlUnitOfWorkPipelineBehavior<,>));
    serviceConfiguration.AddOpenBehavior(typeof(MssqlUnitOfWorkPipelineBehavior<,>));
    serviceConfiguration.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
});
builder.Services.AddHuaweiObsClient();

builder.Services.TryAddSingleton<EntityTypeInfoProvider>();
builder.Services.AddSingleton<ISemaphoreProvider, SemaphoreProvider>();
builder.Services.AddWxConfiguration(configuration);
builder.Services.InitKafaService(configuration);
builder.Services.AddMailExtension();
if (!environment.IsLocal())
{
    builder.Services.AddHostedService<MailCenterConsumerHostedService>();
    builder.Services.AddHostedService<ToDoListCountHostedService>();
}
builder
    .Services.AddControllers(options =>
    {
        options.Filters.Add<ActionExceptionFilter<ResultData>>();
        options.Filters.Add<ActionResultFilter<ResultData>>();
    })
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        options.JsonSerializerOptions.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
        options.JsonSerializerOptions.Converters.Add(new DateTimeJsonConverter());
        options.JsonSerializerOptions.Converters.Add(new NullableDateTimeJsonConverter());
        if (environment.IsLocal())
        {
            options.JsonSerializerOptions.WriteIndented = true;
        }
    });
builder.Services.AddValidation();

if (!environment.IsLocal())
{
    builder.Services.AddSingleton(
       (serviceProvider) =>
       {
           var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

           try
           {
               // 从nacos获取iPlatformExtension.Public服务的地址和端口
               var nacosNamingService = serviceProvider.GetRequiredService<INacosNamingService>();

               // 获取服务实例
               var instance = nacosNamingService.SelectOneHealthyInstance("iPlatformExtension.Public", "DEFAULT_GROUP").GetAwaiter().GetResult();

               // 检查实例是否为空
               if (instance == null)
               {
                   logger.LogError("无法从Nacos获取iPlatformExtension.Public服务实例，分组名称: {GroupName}", "DEFAULT_GROUP");
                   throw new InvalidOperationException("无法从nacos获取iPlatformExtension.Public服务实例");
               }

               logger.LogInformation("成功从Nacos获取iPlatformExtension.Public服务实例: {InstanceIp}:{InstancePort}", instance.Ip, instance.Port);

               // 构建SignalR连接字符串
               var signalRUrl = $"http://{instance.Ip}:{instance.Port}/flowHub";
               logger.LogInformation("从nacos获取的SignalR连接地址: {SignalRUrl}", signalRUrl);

               var connection = new HubConnectionBuilder()
                   .WithUrl(new Uri(signalRUrl))
                   .WithAutomaticReconnect(
                       [
                           TimeSpan.Zero,
                            TimeSpan.FromSeconds(2),
                            TimeSpan.FromSeconds(10),
                            TimeSpan.FromSeconds(30),
                            TimeSpan.FromSeconds(50),
                            TimeSpan.FromSeconds(70),
                            TimeSpan.FromSeconds(100),
                            TimeSpan.FromMinutes(3),
                       ]
                   )
                   .Build();
               connection.StartAsync();
               return connection;
           }
           catch (Exception ex)
           {
               logger.LogError(ex, "从nacos获取iPlatformExtension.Public服务实例失败: {ErrorMessage}，尝试使用配置文件中的连接字符串", ex.Message);

               // 如果从nacos获取失败，则使用配置文件中的连接字符串
               var signalRUrl = configuration["ConnectionStrings:SignalR"];
               logger.LogInformation("尝试从配置文件获取SignalR连接字符串: {SignalRUrl}", signalRUrl);

               if (string.IsNullOrEmpty(signalRUrl))
               {
                   logger.LogError("配置文件中缺少SignalR连接配置，请检查appsettings.json或环境变量");
                   throw new InvalidOperationException("缺少SignalR连接配置");
               }

               var connection = new HubConnectionBuilder()
                   .WithUrl(new Uri(signalRUrl))
                   .WithAutomaticReconnect(
                       [
                           TimeSpan.Zero,
                            TimeSpan.FromSeconds(2),
                            TimeSpan.FromSeconds(10),
                            TimeSpan.FromSeconds(30),
                            TimeSpan.FromSeconds(50),
                            TimeSpan.FromSeconds(70),
                            TimeSpan.FromSeconds(100),
                            TimeSpan.FromMinutes(3),
                       ]
                   )
                   .Build();
               connection.StartAsync();
               return connection;
           }
       }
   );
    builder.Services.AddHostedService<InitMailCountService>();
    builder.Services.AddTransient<IKafkaService, KafkaService>();
    builder.Services.AddHostedService<KafkaFlowRecordCountService>();
    builder.Services.AddHostedService<KafkaMailReceiveCountService>();
    builder.Services.AddHostedService<KafkaMailReaderListCountService>();

}
if (environment.IsLocal())
{
    builder.Services
        .AddMcpServer()
        .WithHttpTransport()
        //.WithStdioServerTransport()
        .WithToolsFromAssembly();
}
var app = builder.Build();

if (!app.Environment.IsProduction())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.HandleUnsuccessfulResponse();

app.UseRequestEnableBuffering();

app.UseW3CTraceResponse();

app.UseAuthentication();

app.MapControllers();

if (environment.IsLocal())
{
    app.MapMcp();
}

app.Run();
