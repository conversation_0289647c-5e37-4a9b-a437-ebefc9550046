﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery.RefuseReexamination;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Exceptions;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery.RefuseReexamination;

internal sealed class RefuseReexaminationOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IDeliveryInfoRepository deliveryInfoRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    IBaseCountryRepository countryRepository,
    IApplicantTypeRepository applicantTypeRepository,
    UnitOfWorkManager unitOfWorkManager,
    ILoggerFactory loggerFactory,
    PhoenixClientFactory phoenixClientFactory)
    :
        PhoenixDeliveryHandleBase<RefuseReexaminationOrderCommand, TrademarkRefuseReexaminationOrder,
            PhoenixResponseParameters<OrderData>>(stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository,
            unitOfWorkManager, loggerFactory, phoenixClientFactory),
        IDeliveryHandler<RefuseReexaminationOrderCommand, TrademarkRefuseReexaminationOrder,
            PhoenixResponseParameters<OrderData>>
{
    
    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.StartupDelivery;
    
    public override async Task<TrademarkRefuseReexaminationOrder> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);

        var procId = _deliveryInfo.ProcId;

        var otherInfo = _deliveryInfo.OtherInfo;
        PropertyMissingException.ThrowIfNull(procId, otherInfo, "递交任务", "其他信息");
        
        var applicant = _deliveryInfo.Applicants!.Single(applicant => applicant.IsRepresent ?? false);
        var countryId = applicant.CountryId ??
                        throw new PropertyMissingException(applicant.ApplicantId, applicant.ApplicantId, "申请人");
        var applicantBookType = ApplicantBookType.GetApplicantBookType(countryId);
        
        string country;
        if (applicantBookType == ApplicantBookType.Overseas)
        {
            var countryInfo = await countryRepository.GetCacheValueAsync(countryId, cancellationToken: cancellationToken);
            if (countryInfo is null)
            {
                throw new NotFoundException(countryId, "国家");
            }

            if (string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCode) || string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCountryName))
            {
                throw new ApplicationException("商标局没有对应的国家编码和名称");
            }

            country = $"{countryInfo.TrademarkDeliveryCode}  {countryInfo.TrademarkDeliveryCountryName}";
        }
        else
        {
            country = applicantBookType.Description;
        }

        var applicantType = await applicantTypeRepository.GetCacheValueAsync(applicant.TypeId.GetOrDefaultEmpty(), cancellationToken: cancellationToken);
        if (applicantType is null)
        {
            throw new ApplicantTypeMissingException(applicant);
        }

        var attachments = _deliveryInfo.Files?
                              .Where(file => int.TryParse(file.FileCode, out _) && file.BaseFileType is "申请人文件" or "递交官方")
                              .Select(file =>
                              new ApplicantAttachment(
                                  file.FileName, file.Url ?? string.Empty, ApplicantAttachmentType.OfficialAttachment,
                                  new ApplicantAttachmentSubType(int.Parse(file.FileCode!), file.FileDesc ?? string.Empty))) 
                          ?? Array.Empty<ApplicantAttachment>();

        var grandNumbers = otherInfo!.TrademarkNiceClasses?.Split(';') ?? [];
        // var grandBrandInfos = await freeSql.Select<CaseTrademarkNiceCategory>().WithLock()
        //     .WhereIf(grandNumbers.Length > 0,
        //         category => grandNumbers.Contains(category.GrandNumber) && category.CaseId == _deliveryInfo.CaseId)
        //     .GroupBy(category => new CaseTrademarkNiceCategory()
        //     {
        //         GrandNumber = category.GrandNumber,
        //         GrandName = category.GrandName
        //     })
        //     .ToListAsync(group => group.Value, cancellationToken);
        
        var order = new TrademarkRefuseReexaminationOrder()
        {
            SubmitType = 1,
            IsReplenish = otherInfo.ReservationOfSupplementaryMaterial switch
            {
                false => 0,
                true => 1,
                _ => throw new ApplicationException("[是否保留补充材料权利]缺少对应的值")
            },
            AgentOrganTel = _deliveryInfo.ContactTel ?? throw new PropertyMissingException(procId, _deliveryInfo.ContactTel, "递交任务"),
            AgentPerson = _deliveryInfo.AgentUser ?? throw new PropertyMissingException(procId, _deliveryInfo.AgentUser, "递交任务"),
            AgentOrganConName = _deliveryInfo.AgentUser ?? throw new PropertyMissingException(procId, _deliveryInfo.AgentUser, "递交任务"),
            OrderToken = _deliveryInfo.ProcId,
            ApplicationInfo = new RefuseReexaminationApplicationInfo()
            {
                BookType = applicantBookType.Code.ToString(),
                CertificatesType = "0",
                ApplicantAddress = applicant.AddressCn ?? throw new PropertyMissingException(applicant.ApplicantId, applicant.AddressCn, "申请人"),
                IdCard = applicant.CardNo,
                ContactName = _deliveryInfo.ContactPerson ?? throw new PropertyMissingException(procId, _deliveryInfo.ContactPerson, "递交任务"),
                ContactTel = _deliveryInfo.ContactTel ?? throw new PropertyMissingException(procId, _deliveryInfo.ContactTel, "递交任务"),
                ContactEmail = _deliveryInfo.ContactMailBox,
                Code = applicant.Postcode ?? throw new PropertyMissingException(applicant.ApplicantId, applicant.Postcode, "递交任务"),
                OwnerType = applicantType.ApplicantTypeCode == "5" ? "0" : "1",
                Country = country,
                IsChangeName = otherInfo.NominalChangesType ?? throw new PropertyMissingException(procId, otherInfo.NominalChangesType, "递交任务其他信息", "名义变更类型"),
                ChangeName = applicant.ApplicantNameCn,
                ApplicantName = applicant.ApplicantNameCn
            },
            BrandInfos = grandNumbers.Select(grandNumber => new BrandInfo()
            {
                BrandRegisterNo = _deliveryInfo.AppNo,
                FirstCgNo = grandNumber.PadLeft(2, '0')
                // FirstCgName = grandInfo.GrandName
            }),
            ApplicantAttachments = attachments
        };

        return order;
    }

    public override Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(TrademarkRefuseReexaminationOrder request,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        var client = _phoenixClient ?? _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
        return client.CreateOrderAsync(PhoenixUri.TrademarkRefuseReexamination, request);
    }

    public override async Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        var orderNo = response.Data?.OrderNo!;
        const int newStatus = (int) DeliveryStatus.Ordered;
        
        await Handler.UpdateDeliveryInfoInternalAsync(deliveryInfo =>
        {
            deliveryInfo.Status = newStatus;
            deliveryInfo.OrderNo = orderNo;
            deliveryInfo.OperationResult = true;
            return ValueTask.CompletedTask;
        }, cancellationToken);
    }
    
    public async Task<bool> HandleExceptionAsync(Exception ex, CancellationToken cancellationToken = default)
    {
        await Handler.HandleExceptionInternalAsync(ex, true, false, cancellationToken);
        return false;
    }
}