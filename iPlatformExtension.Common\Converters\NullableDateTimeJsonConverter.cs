using System.Text.Json;
using System.Text.Json.Serialization;

namespace iPlatformExtension.Common.Converters;

/// <summary>
/// DateTime?格式转换器。统一时间时间的格式转换
/// </summary>
public class NullableDateTimeJsonConverter : JsonConverter<DateTime?>
{
    /// <summary>
    /// DateTime?类型的读取转换
    /// </summary>
    /// <param name="reader">读取器</param>
    /// <param name="typeToConvert"><c>typeof(DateTime?)</c></param>
    /// <param name="options">序列化选项</param>
    /// <returns>DateTime? 值</returns>
    public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.Null) return null;
        return DateTime.TryParse(reader.GetString(), out var dateTime) ? dateTime : default(DateTime?);
    }

    /// <summary>
    /// DateTime?类型的写入转换
    /// </summary>
    /// <param name="writer">写入器</param>
    /// <param name="value">DateTime? 值</param>
    /// <param name="options">序列化选项</param>
    public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value?.ToString("yyyy-MM-dd HH:mm:ss"));
    }
}