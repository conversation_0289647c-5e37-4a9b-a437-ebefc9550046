﻿using AutoMapper;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class ProcOutsourcingQueryHandler(
    IMapper mapper,
    IFreeSql<PlatformFreeSql> freeSql, 
    IPublisher publisher) 
    : IRequestHandler<ProcOutsourcingQuery, ProcOutsourcingDto>
{
    public async Task<ProcOutsourcingDto> Handle(ProcOutsourcingQuery request, CancellationToken cancellationToken)
    {
        var procId = request.ProcId;
        var procInfo = await freeSql.Select<CaseProcInfo>(procId).WithLock()
            .IncludeMany(procInfo => procInfo.ForeignSupplierContacts)
            .ToOneAsync(info => new CaseProcInfo()
            {
                ProcId = info.ProcId,
                CaseInfo = new CaseInfo()
                {
                    CaseTypeId = info.CaseInfo.CaseTypeId,
                    Id = info.CaseInfo.Id,
                },
                ForeginAgencyId = info.ForeginAgencyId,
                ForeignNumber = info.ForeignNumber,
                ForeignSupplierRemark = info.ForeignSupplierRemark,
                Version = info.Version,
            }, cancellationToken);

        var dto = mapper.Map<ProcOutsourcingDto>(procInfo);
        
        if (dto == null)
            throw new NotFoundException(procId, "案件任务");
        
        await publisher.Publish(new ProcOutsourcingResultNotification(dto), cancellationToken);
        
        return dto;
    }
}