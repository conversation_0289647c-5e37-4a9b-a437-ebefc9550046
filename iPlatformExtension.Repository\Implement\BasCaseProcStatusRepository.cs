﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;
using iPlatformExtension.Common.Db.FreeSQL;

namespace iPlatformExtension.Repository.Implement
{
    internal class BasCaseProcStatusRepository(
        IFreeSql<PlatformFreeSql> freeSql,
        UnitOfWorkManage<PlatformFreeSql> uowManger,
        IMemoryCache memoryCache,
        DefaultRedisCache redisCache,
        CacheExpirationToken<BasCaseProcStatus> expirationToken)
        : DefaultRepository<BasCaseProcStatus, string>(freeSql, uowManger), IBasCaseProcStatusRepository
    {
        IMemoryCache ICacheableRepository<string, BasCaseProcStatus>.MemoryCache => memoryCache;

        CacheExpirationToken<BasCaseProcStatus> ICacheableRepository<string, BasCaseProcStatus>.ExpirationToken => expirationToken;

        public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
    }
}
