﻿using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class CancelRequisitionCommandHandler(
    ICaseFeeRepository caseFeeRepository,
    IHttpContextAccessor httpContextAccessor)
    : FeesUpdateCommandHandler(caseFeeRepository, httpContextAccessor)
{
    protected override FinanceOperation UpdateOperation => FinanceOperation.CancelRequisition;
    
    protected override ValueTask<FeesUpdateResult> UpdateFeeAsync(CaseFeeList caseFeeList, FeeUpdateInfoDto feeUpdateInfoDto, string operatorId)
    {
        return new ValueTask<FeesUpdateResult>(caseFeeList.UnlockFee(operatorId));
    }
}