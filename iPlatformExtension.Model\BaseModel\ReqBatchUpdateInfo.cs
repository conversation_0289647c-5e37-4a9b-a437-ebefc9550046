using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "req_batch_update_info", DisableSyncStructure = true)]
	public partial class ReqBatchUpdateInfo {

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "cur_fee_id", StringLength = 50)]
		public string CurFeeId { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "update_detail", StringLength = -2)]
		public string UpdateDetail { get; set; }

		[ Column(Name = "update_type", StringLength = 50)]
		public string UpdateType { get; set; }

	}

}
