﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.AnalysisRule;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AnalysisRule
{
    /// <summary>
    /// 分拣处理者
    /// </summary>
    internal sealed class SortingCommandHandler(
        IMailReceiveRepository mailReceiveRepository,
        IMailReceiveFlowRepository mailReceiveFlowRepository,
        IMailSendRepository mailSendRepository,
        IMailSendFlowRepository mailSendFlowRepository,
        IHttpContextAccessor context,
        IFlowRecordRepository flowRecordRepository,
        IMediator mediator,
        IMailReaderListRepository mailReaderListRepository,
        IMailSendListRepository mailSendListRepository
    ) : IRequestHandler<SortingCommand>
    {
        public async Task Handle(SortingCommand request, CancellationToken cancellationToken)
        {
            var userId = context.HttpContext?.User.GetUserID() ?? string.Empty;
            var flowRecordList = new List<FlowRecord>();

            // 根据动作类型选择处理收件或发件
            if (request.MailType == SysEnum.MailType.Receive.ToString())
            {
                // 处理收件
                var mailReceives = await mailReceiveRepository
                    .Where(it => request.MailId.Contains(it.MailId))
                    .ToListAsync(cancellationToken);
                var receiveFlows = await mailReceiveFlowRepository
                    .Where(it => request.MailId.Contains(it.MailId))
                    .ToListAsync(cancellationToken);
                // 添加状态校验
                if (mailReceives.Any(x => x.Status != ReceiveFileType.Sort.GetHashCode()))
                {
                    throw new ApplicationException("存在非待分拣状态的邮件，无法进行分拣操作");
                }
                if (mailReceives.Count + receiveFlows.Count != request.MailId.Count * 2)
                {
                    throw new ApplicationException("流程参数状态有误");
                }

                mailReceives.ForEach(it =>
                {
                    it.Status = ReceiveFileType.Handle.GetHashCode();
                    var allotRecord = new FlowRecord
                    {
                        Id = Guid.NewGuid().ToString(),
                        AuditTime = DateTime.Now,
                        AuditType = MailAction.Submit.ToString(),
                        AuditUser = userId,
                        CurNodeId = MailFlowAction.Allot.ToString(),
                        IsCurrent = MailFlowActionStatus.DisEnable.GetHashCode(),
                        MailId = it.MailId,
                        Version = "1",
                        AuditRemark = request.DisplayName ?? "",
                    };
                    flowRecordList.Add(allotRecord);
                    flowRecordList.Add(
                        new FlowRecord
                        {
                            Id = Guid.NewGuid().ToString(),
                            AuditTime = null,
                            AuditUser = !string.IsNullOrEmpty(request.UndertakeUserId)
                                ? request.UndertakeUserId
                                : receiveFlows
                                    .FirstOrDefault(x => x.MailId == it.MailId)
                                    ?.UndertakeUserId
                                    ?? throw new ApplicationException("当前承办人为空"),
                            CurNodeId = MailFlowAction.Handle.ToString(),
                            IsCurrent = MailFlowActionStatus.Enable.GetHashCode(),
                            PreRecordId = allotRecord.Id,
                            MailId = it.MailId,
                            Version = "1",
                        }
                    );
                });
                receiveFlows.ForEach(it =>
                {
                    it.UpdateBy = userId;
                    it.UpdateTime = DateTime.Now;
                    it.SortBy = userId;
                    it.SortTime = DateTime.Now;
                    if (!string.IsNullOrEmpty(request.UndertakeUserId))
                    {
                        it.UndertakeUserId = request.UndertakeUserId;
                    }
                });

                await flowRecordRepository.InsertAsync(flowRecordList, cancellationToken);
                await mailReceiveFlowRepository.UpdateAsync(receiveFlows, cancellationToken);
                await mailReceiveRepository.UpdateAsync(mailReceives, cancellationToken);

                var readerList = await mailReaderListRepository
                                  .Where(o => request.MailId.Contains(o.MailId))
                                  .ToListAsync(cancellationToken);
                //添加阅读人企业微信通知
                await mediator.Send(new MailCenterMessageQuery(readerList, OperationTypeEnum.Sort), cancellationToken);
                //分拣流程微信通知
                await mediator.Send(
                    new MailCenterFlowMessageQuery(flowRecordList, OperationTypeEnum.Submit), cancellationToken
                );
            }
            else if (request.MailType == SysEnum.MailType.Send.ToString())
            {
                // 处理发件
                var mailSends = await mailSendRepository
                    .Where(it => request.MailId.Contains(it.MailId))
                    .ToListAsync(cancellationToken);
                var sendFlows = await mailSendFlowRepository
                    .Where(it => request.MailId.Contains(it.MailId))
                    .ToListAsync(cancellationToken);

                // 添加状态校验
                if (mailSends.Any(x => x.Status != SendStatusType.Draft.GetHashCode()))
                {
                    throw new ApplicationException("存在非草稿状态的邮件，无法进行分拣操作");
                }
                if (mailSends.Count + sendFlows.Count != request.MailId.Count * 2)
                {
                    throw new ApplicationException("沒有找到流程，請重新保存");
                }
                // 处理发件流程记录
                foreach (var mailSend in mailSends)
                {
                    mailSend.Status = request.IsReviewing ? SendStatusType.Reviewing.GetHashCode() : SendStatusType.PendingSend.GetHashCode();
                    if (request.IsReviewing)
                    {
                        var allotRecord = new FlowRecord
                        {
                            Id = Guid.NewGuid().ToString(),
                            AuditTime = DateTime.Now,
                            AuditType = MailAction.Submit.ToString(),
                            AuditUser = userId,
                            CurNodeId = MailFlowAudit.SendHandle.ToString(),
                            IsCurrent = MailFlowActionStatus.DisEnable.GetHashCode(),
                            MailId = mailSend.MailId,
                            Version = "1",
                            AuditRemark = request.DisplayName ?? "",
                        };
                        flowRecordList.Add(allotRecord);
                        flowRecordList.Add(
                            new FlowRecord
                            {
                                Id = Guid.NewGuid().ToString(),
                                AuditTime = null,
                                AuditUser = !string.IsNullOrEmpty(request.UndertakeUserId)
                                    ? request.UndertakeUserId
                                    : sendFlows
                                        .FirstOrDefault(x => x.MailId == mailSend.MailId)
                                        ?.UndertakeUserId
                                        ?? throw new ApplicationException("当前承办人为空"),
                                CurNodeId = MailFlowAudit.Audit.ToString(),
                                IsCurrent = MailFlowActionStatus.Enable.GetHashCode(),
                                PreRecordId = allotRecord.Id,
                                MailId = mailSend.MailId,
                                Version = "1",
                            }
                        );
                    }
                    else
                    {
                        await mailSendListRepository.InsertAsync(new MailSendList
                        {
                            Id = Guid.NewGuid().ToString(),
                            CreateBy = userId,
                            CreateTime = DateTime.Now,
                            MailId = mailSend.MailId,
                            Status = SendStatusType.PendingSend.GetHashCode(),
                            SendTime = mailSend.SendTime,
                        }, cancellationToken);
                    }
                }

                // 更新发件流程
                sendFlows.ForEach(it =>
                {
                    it.UpdateBy = userId;
                    it.UpdateTime = DateTime.Now;
                    if (!string.IsNullOrEmpty(request.UndertakeUserId))
                    {
                        it.UndertakeUserId = request.UndertakeUserId;
                    }
                });

                await mailSendRepository.UpdateAsync(mailSends, cancellationToken);
                await flowRecordRepository.InsertAsync(flowRecordList, cancellationToken);
                await mailSendFlowRepository.UpdateAsync(sendFlows, cancellationToken);


                var readerList = await mailReaderListRepository
                                .Where(o => request.MailId.Contains(o.MailId))
                                .ToListAsync(cancellationToken);
                if (readerList.Count > 0)
                {
                    //添加阅读人企业微信通知
                    await mediator.Send(new MailCenterMessageQuery(readerList, OperationTypeEnum.SentAudit), cancellationToken);
                }//分拣流程微信通知
                await mediator.Send(
                    new MailCenterFlowMessageQuery(flowRecordList, OperationTypeEnum.SentAudit), cancellationToken
                );
            }
            else
            {
                throw new ApplicationException($"不支持的邮件类型：{request.MailType}");
            }
        }
    }
}
