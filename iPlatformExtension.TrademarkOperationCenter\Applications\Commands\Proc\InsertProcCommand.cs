﻿
using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;

/// <summary>
/// 递交任务Id
/// </summary>
/// <param name="ProcId"></param>
internal sealed record InsertProcCommand(string ProcId) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>
{
    public Propagation TransactionPropagation { get; set; } = Propagation.Required;
}

