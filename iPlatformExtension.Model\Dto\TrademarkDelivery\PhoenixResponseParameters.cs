﻿namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 权大师响应参数
/// </summary>
/// <typeparam name="T">数据类型参数</typeparam>
public sealed class PhoenixResponseParameters<T> : PhoenixResponseParameters
{
    /// <summary>
    /// 业务数据
    /// </summary>
    public T? Data { get; set; }
}

/// <summary>
/// 权大师响应参数抽象类
/// </summary>
public abstract class PhoenixResponseParameters
{
    /// <summary>
    /// 系统级code
    /// </summary>
    public int Code { get; set; }

    /// <summary>
    /// 系统级接口请求说明文字
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 业务级code
    /// </summary>
    public int SubCode { get; set; }

    /// <summary>
    /// 业务级message
    /// </summary>
    public string? SubMessage { get; set; }

    /// <summary>
    /// 请求方法名
    /// </summary>
    public string? MethodName { get; set; }
}