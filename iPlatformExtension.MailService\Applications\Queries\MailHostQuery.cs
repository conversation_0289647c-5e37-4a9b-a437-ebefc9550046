﻿using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailService.Applications.Queries
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="IsPrivate">是否个人邮箱</param>
    /// <param name="HostId">邮箱ID</param>
    /// <param name="IsEnabled">是否有效</param>
    /// <param name="IsReceive">是否开启接收邮件</param>
    public record MailHostQuery(bool IsPrivate,string HostId = "", bool IsEnabled = true, bool IsReceive = true) : IRequest<IEnumerable<MailHost>>;

}
