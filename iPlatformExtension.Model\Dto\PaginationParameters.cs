namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 分页参数
/// </summary>
public class PaginationParameters : IPaginationParameters
{
    /// <summary>
    /// 页码
    /// </summary>
    public int? PageIndex { get; set; }

    /// <summary>
    /// 页面大小
    /// </summary>
    public int? PageSize { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页面大小</param>
    public PaginationParameters(int? pageIndex, int? pageSize)
    {
        PageIndex = pageIndex;
        PageSize = pageSize;
    }
}