﻿using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Commission.Application.Queries.WinningReward;

internal record WinningRewardDetailQuery(
    DateRange DateRange,
    string? DistrictCode,
    IEnumerable<string> DeptIds,
    string? Keyword) : IRequest<IEnumerable<RewardUserDetail>>, IPageQuery
{
    public int PageIndex { get; set; }
    
    public int PageSize { get; set; }
    
    public long? Total { get; set; }
    
    public void Deconstruct(out int pageIndex, out int pageSize, out long? total)
    {
        pageIndex = PageIndex;
        pageSize = PageSize;
        total = Total;
    }
}