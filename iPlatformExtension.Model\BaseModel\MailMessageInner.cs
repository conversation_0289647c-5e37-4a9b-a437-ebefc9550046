using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_message_inner", DisableSyncStructure = true)]
	public partial class MailMessageInner {

		/// <summary>
		/// 允许发送邮件
		/// </summary>
		[ Column(Name = "allow_send")]
		public bool? AllowSend { get; set; } = false;

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "display_name", StringLength = 500, IsNullable = false)]
		public string DisplayName { get; set; }

		[ Column(Name = "finish_time")]
		public DateTime? FinishTime { get; set; }

		[ Column(Name = "fixed_send_time")]
		public DateTime? FixedSendTime { get; set; }

		[ Column(Name = "host_id", StringLength = 50)]
		public string HostId { get; set; }

		[ Column(Name = "html_body", DbType = "ntext", IsNullable = false)]
		public string HtmlBody { get; set; }

		[ Column(Name = "is_attachment")]
		public bool? IsAttachment { get; set; } = false;

		[ Column(Name = "is_error")]
		public bool IsError { get; set; } = false;

		/// <summary>
		/// 外部或内部邮件
		/// </summary>
		[ Column(Name = "is_inside")]
		public bool? IsInside { get; set; } = false;

		/// <summary>
		/// 是否新邮件-未被任何人点击打开过的邮件
		/// </summary>
		[ Column(Name = "is_new")]
		public bool? IsNew { get; set; } = true;

		/// <summary>
		/// 是否个人邮箱
		/// </summary>
		[ Column(Name = "is_private")]
		public bool? IsPrivate { get; set; } = false;

		/// <summary>
		/// 接收或发送的邮件
		/// </summary>
		[ Column(Name = "is_send")]
		public bool? IsSend { get; set; } = false;

		[ Column(Name = "mail_date")]
		public DateTime MailDate { get; set; }

		[ Column(Name = "mail_from", StringLength = 200)]
		public string MailFrom { get; set; }

		[ Column(Name = "mail_guid", StringLength = 200, IsNullable = false)]
		public string MailGuid { get; set; }

		[ Column(Name = "mail_id", StringLength = 50, IsNullable = false)]
		public string MailId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "mail_size", StringLength = 50)]
		public string MailSize { get; set; }

		[ Column(Name = "mail_subject", StringLength = 500, IsNullable = false)]
		public string MailSubject { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "replay_to", StringLength = 200)]
		public string ReplayTo { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

	}

}
