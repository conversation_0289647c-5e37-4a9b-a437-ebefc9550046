﻿using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Common.Converters;

public class PageResultJsonConverterFactory : JsonConverterFactory
{
    public override bool CanConvert(Type typeToConvert)
    {
        if (!typeToConvert.IsGenericType)
        {
            return false;
        }

        return typeToConvert.GetGenericTypeDefinition() == typeof(PageResult<>);
    }

    public override JsonConverter CreateConverter(Type typeToConvert, JsonSerializerOptions options)
    {
        var dataType = typeToConvert.GenericTypeArguments[0];
        var converter = (JsonConverter)Activator.CreateInstance(
            typeof(PageResultJsonConverter<>).MakeGenericType(dataType),
            BindingFlags.Instance | BindingFlags.Public,
            binder: null,
            args: [options],
            culture: null)!;

        return converter;
    }

    private class PageResultJsonConverter<T>(JsonSerializerOptions serializerOptions) : JsonConverter<PageResult<T>>
    {
        private readonly JsonConverter<IEnumerable<T>> _dataConverter = (JsonConverter<IEnumerable<T>>)serializerOptions.GetConverter(typeof(IEnumerable<T>));

        private readonly byte[] _pageName = Encoding.UTF8.GetBytes(nameof(PageResult<T>.Page).JsonPropertyName(serializerOptions));
        
        private readonly byte[] _pageSizeName = Encoding.UTF8.GetBytes(nameof(PageResult<T>.PageSize).JsonPropertyName(serializerOptions));
        
        private readonly byte[] _totalName = Encoding.UTF8.GetBytes(nameof(PageResult<T>.Total).JsonPropertyName(serializerOptions));

        private readonly byte[] _totalPageName = Encoding.UTF8.GetBytes(nameof(PageResult<T>.TotalPage).JsonPropertyName(serializerOptions));
        
        private readonly byte[] _dataName = Encoding.UTF8.GetBytes(nameof(PageResult<T>.Data).JsonPropertyName(serializerOptions));

        public override PageResult<T> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var readTimes = 0;
            var page = 0;
            var pageSize = 0;
            IEnumerable<T> data = Array.Empty<T>();
            var total = 0;
            do
            {
                
                if (reader.TokenType != JsonTokenType.PropertyName) continue;
                if (reader.ValueTextEquals(_pageName))
                {
                    reader.Read();
                    page = reader.GetInt32();
                    readTimes++;
                }
                else if (reader.ValueTextEquals(_pageSizeName))
                {
                    reader.Read();
                    pageSize = reader.GetInt32();
                    readTimes++;
                }
                else if (reader.ValueTextEquals(_totalName))
                {
                    reader.Read();
                    total = reader.GetInt32();
                    readTimes++;
                }
                else if (reader.ValueTextEquals(_totalPageName))
                {
                    reader.Read();
                    reader.GetInt32();
                    readTimes++;
                }
                else if (reader.ValueTextEquals(_dataName))
                {
                    reader.Read();
                    data = _dataConverter.Read(ref reader, typeof(IEnumerable<T>), options) ?? Array.Empty<T>();
                    readTimes++;
                }
            } while (reader.Read() && readTimes < 5);

            var result = new PageResult<T>
            {
                Page = page,
                PageSize = pageSize,
                Data = data,
                Total = total
            };
            return result;
        }

        public override void Write(Utf8JsonWriter writer, PageResult<T> value, JsonSerializerOptions options)
        {
            writer.WriteStartObject();
            writer.WriteNumber(nameof(PageResult<T>.Page).JsonPropertyName(options), value.Page);
            writer.WriteNumber(nameof(PageResult<T>.PageSize).JsonPropertyName(options), value.PageSize);
            writer.WriteNumber(nameof(PageResult<T>.Total).JsonPropertyName(options), value.Total);
            writer.WriteNumber(nameof(PageResult<T>.TotalPage).JsonPropertyName(options), value.TotalPage);
            writer.WritePropertyName(nameof(PageResult<T>.Data).JsonPropertyName(options));
            if (_dataConverter.CanConvert(typeof(IEnumerable<T>)))
            {
                _dataConverter.Write(writer, value.Data, options);
            }
            else
            {
                JsonSerializer.Serialize(writer, value.Data, options);
            }
            writer.WriteEndObject();
        }
    }
}