﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class DefaultDeliveryAgencyInfoQueryHandler(IFreeSql freeSql, IUserInfoRepository userInfoRepository) : 
    IRequestHandler<DefaultDeliveryAgencyInfoQuery, DefaultAgencyDto>
{
    public async Task<DefaultAgencyDto> Handle(DefaultDeliveryAgencyInfoQuery request, CancellationToken cancellationToken)
    {
        var dto = await freeSql.Select<CaseProcInfo>(request.ProcId).WithLock()
            .Include(info => info.CaseInfo)
            .ToOneAsync(info => new DefaultAgencyDto()
            {
                AgencyUser = info.ProcUndertakeMainUserId,
                AgencyId = info.CaseInfo.AgencyId
            }, cancellationToken);

        dto.AgencyUser = await userInfoRepository.GetChineseValueAsync(dto.AgencyUser);

        return dto;
    }
}