﻿using FreeSql.DataAnnotations;


namespace iPlatformExtension.Model.Dto.CaseEntrust
{
    public class FeeSettingDto
    {
        [ Column(Name = "fee_class", StringLength = 50)]
        public string feeClass { get; set; }
        [ Column(Name = "balance_way", StringLength = 50)]
        public string balanceWay { get; set; }
        [ Column(Name = "charge_rule", StringLength = 50)]
        public string chargeRule { get; set; }
        [ Column(Name = "pay_way", StringLength = 50)]
        public string payWay { get; set; }
        [ Column(Name = "payment_name", StringLength = 50)]
        public string paymentName { get; set; }
        [ Column(Name = "case_direction", StringLength = 50)]
        public string caseDirection { get; set; }
        [ Column(Name = "customer_id", StringLength = 50)]
        public string customerId { get; set; }
        [ Column(Name = "is_request", StringLength = 50)]
        public bool isRequest { get; set; }
        [ Column(Name = "is_officer", StringLength = 50)]
        public bool isOfficer { get; set; }
        [ Column(Name = "fee_name_id", StringLength = 50)]
        public string feeNameId { get; set; }
        [ Column(Name = "item_type", StringLength = 50)]
        public string itemType { get; set; }
        [ Column(Name = "item_start", StringLength = 50)]
        public string itemStart { get; set; }
        [ Column(Name = "item_end", StringLength = 50)]
        public string itemEnd { get; set; }
    }
}
