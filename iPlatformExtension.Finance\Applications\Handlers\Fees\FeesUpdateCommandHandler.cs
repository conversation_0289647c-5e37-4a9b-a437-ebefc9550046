﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal abstract class FeesUpdateCommandHandler(ICaseFeeRepository caseFeeRepository, IHttpContextAccessor httpContextAccessor) 
    : INotificationHandler<CaseFeeUpdateContext>
{
    protected abstract FinanceOperation UpdateOperation { get; }
    
    public async Task Handle(CaseFeeUpdateContext notification, CancellationToken cancellationToken)
    {
        var (feeId, updateOperation, feeUpdateInfoDto) = notification;
        
        if (updateOperation != UpdateOperation)
        {
            return;
        }
        
        var operatorId = (httpContextAccessor.HttpContext?.User).GetUserId();
        
        var caseFeeList = await caseFeeRepository.GetAsync(feeId, cancellationToken);
        if (caseFeeList is null) 
            throw FeesUpdateException.NotExist(feeId);

        var updateResult = await UpdateFeeAsync(caseFeeList, feeUpdateInfoDto, operatorId);
        if (!updateResult.Success || await caseFeeRepository.UpdateAsync(caseFeeList, cancellationToken) <= 0)
        {
            throw new FeesUpdateException(updateResult);
        }

        notification.FeesUpdateResult = updateResult;
    }
    
    protected abstract ValueTask<FeesUpdateResult> UpdateFeeAsync(CaseFeeList caseFeeList, FeeUpdateInfoDto feeUpdateInfoDto, string operatorId);
}