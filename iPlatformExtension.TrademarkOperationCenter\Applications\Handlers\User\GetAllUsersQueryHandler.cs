﻿﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.User;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.User;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.User
{
    /// <summary>
    /// 获取所有用户信息查询处理器
    /// </summary>
    public class GetAllUsersQueryHandler : IRequestHandler<GetAllUsersQuery, IEnumerable<AllUserInfoDto>>
    {
        private readonly IUserInfoRepository _userInfoRepository;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="userInfoRepository">用户信息仓储</param>
        public GetAllUsersQueryHandler(IUserInfoRepository userInfoRepository)
        {
            _userInfoRepository = userInfoRepository;
        }

        /// <summary>
        /// 处理查询请求
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>用户信息列表</returns>
        public async Task<IEnumerable<AllUserInfoDto>> Handle(GetAllUsersQuery request, CancellationToken cancellationToken)
        {
            // 使用仓储查询所有用户信息
            var users = await _userInfoRepository.Select.WithLock()
                .ToListAsync(cancellationToken);

            // 转换为DTO
            return users.Select(u => new AllUserInfoDto
            {
                UserId = u.UserId,
                UserName = u.UserName,
                CnName = u.CnName,
                EnName = u.EnName,
                DeptId = u.DeptId,
                Email = u.Email,
                Tel = u.Tel,
                Mobile = u.Mobile,
                IsEnabled = u.IsEnabled,
                LeaveDate = u.LeaveDate,
                WorkNo = u.WorkNo,
                UserType = u.UserType
            });
        }
    }
}
