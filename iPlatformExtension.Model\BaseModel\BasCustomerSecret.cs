using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_customer_secret", DisableSyncStructure = true)]
	public partial class BasCustomerSecret {

		/// <summary>
		/// 保密等级
		/// </summary>
		[ Column(Name = "secret_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string SecretId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 英文名称
		/// </summary>
		[ Column(Name = "secret_en_us", StringLength = 100)]
		public string SecretEnUs { get; set; }

		/// <summary>
		/// 日文名称
		/// </summary>
		[ Column(Name = "secret_ja_jp", StringLength = 100)]
		public string SecretJaJp { get; set; }

		/// <summary>
		/// 中文名称
		/// </summary>
		[ Column(Name = "secret_zh_cn", StringLength = 50)]
		public string SecretZhCn { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
