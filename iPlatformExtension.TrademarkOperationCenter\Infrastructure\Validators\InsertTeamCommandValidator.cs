﻿using FluentValidation;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Validators
{
    public class InsertTeamCommandValidator : AbstractValidator<InsertTeamCommand>
    {
        public InsertTeamCommandValidator()
        {
            RuleFor(dto => dto.TeamName).NotNull().NotEmpty().WithMessage("请补充团队名称");
            RuleFor(dto => dto).Must((x) =>
            {
                if (x.IsEffect && (x.Members is null || !x.Members.Any()))
                {
                    return false;
                }

                return true;
            }).WithMessage("请补充团队成员/关联客户");
            RuleFor(dto => dto).Must(x => !(x is { IsEffect: true, IsExclusive: true, CustomerId: null })).WithMessage("请补充关联客户");
            //RuleFor(dto => dto).Must((x) => (x is { IsEffect: false, IsExclusive: true, CustomerId: null })).WithMessage("失效状态，不可关联客户");
            RuleFor(dto => dto.TeamDescription).Length(0, 500).WithMessage("团队描述为500字以下");
        }
    }
}
