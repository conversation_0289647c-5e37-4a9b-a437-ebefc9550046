﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;

namespace iPlatformExtension.MailCenterRepository.Implement
{
    internal class MailConfigHistoryRepository(
IFreeSql<MailCenterFreeSql> fsql,
        UnitOfWorkManage<MailCenterFreeSql> manager)
        : DefaultRepository<MailConfigHistory, string>(fsql, manager), IMailConfigHistoryRepository;
}
