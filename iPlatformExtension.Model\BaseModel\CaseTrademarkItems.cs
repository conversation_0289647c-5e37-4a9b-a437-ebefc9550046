using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_trademark_items", DisableSyncStructure = true)]
	public partial class CaseTrademarkItems {

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "cur_code", StringLength = 50)]
		public string CurCode { get; set; }

		[ Column(Name = "cur_id", StringLength = 50, IsNullable = false)]
		public string CurId { get; set; }

		[ Column(Name = "text_en_us", StringLength = 500)]
		public string TextEnUs { get; set; }

		[ Column(Name = "text_zh_cn", StringLength = 500)]
		public string TextZhCn { get; set; }

		[ Column(Name = "user_items", StringLength = 1000)]
		public string UserItems { get; set; }

	}

}
