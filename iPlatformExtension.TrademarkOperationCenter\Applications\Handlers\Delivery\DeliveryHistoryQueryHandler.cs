﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DeliveryHistoryQueryHandler(IFreeSql freeSql, IUserInfoRepository infoRepository)
    : IRequestHandler<DeliveryHistoryQuery, IEnumerable<DeliveryHistoryDto>>
{
    public async Task<IEnumerable<DeliveryHistoryDto>> Handle(DeliveryHistoryQuery request, CancellationToken cancellationToken)
    {
        ICacheableRepository<string, UserBaseInfo> userInfoRepository = infoRepository;
        var histories = await freeSql.Select<DeliHistory>().NoTracking().WithLock()
            .From<DeliveryDisplayJson>()
            .LeftJoin((history, json) => history.DisplayJsonId == json.Id)
            .Where((history, json) => history.ProcId == request.ProcId && history.Display == true)
            .OrderBy((history, json) => history.StartTime)
            .ToListAsync((history, json) => new DeliveryHistoryDto
            {
                HistoryId = history.HistoryId,
                Operation = history.Operation,
                Operator = history.UserId,
                OperatingTime = history.StartTime,
                Remark = history.ErrorMessage,
                ResultId = json.Id
            }, cancellationToken);

        foreach (var dto in histories)
        {
            dto.Operator = (await userInfoRepository.GetCacheValueAsync(dto.Operator))?.CnName ?? string.Empty;
        }

        return histories;
    }
}