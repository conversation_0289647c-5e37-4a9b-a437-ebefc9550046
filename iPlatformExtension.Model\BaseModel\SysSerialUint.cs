using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_serial_uint", DisableSyncStructure = true)]
	public partial class SysSerialUint {

		[ Column(Name = "default_value", StringLength = 50)]
		public string DefaultValue { get; set; }

		[ Column(Name = "remark", StringLength = 200)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "serial_id", StringLength = 50)]
		public string SerialId { get; set; }

		[ Column(Name = "unit_id", StringLength = 50)]
		public string UnitId { get; set; }

		[ Column(Name = "unit_name_en_us", StringLength = 50)]
		public string UnitNameEnUs { get; set; }

		[ Column(Name = "unit_name_ja_jp", StringLength = 50)]
		public string UnitNameJaJp { get; set; }

		[ Column(Name = "unit_name_zh_cn", StringLength = 50)]
		public string UnitNameZhCn { get; set; }

	}

}
