using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_files_200509", DisableSyncStructure = true)]
	public partial class DeliFiles200509 {

		[ Column(Name = "file_code", StringLength = 50)]
		public string FileCode { get; set; }

		[ Column(Name = "file_count")]
		public int? FileCount { get; set; }

		[ Column(Name = "file_desc", StringLength = 200)]
		public string FileDesc { get; set; }

		[ Column(Name = "file_ex", StringLength = 50)]
		public string FileEx { get; set; }

		[ Column(Name = "file_id", StringLength = 50, IsNullable = false)]
		public string FileId { get; set; }

		[ Column(Name = "file_name", StringLength = 200)]
		public string FileName { get; set; }

		[ Column(Name = "file_no", StringLength = 50)]
		public string FileNo { get; set; }

		[ Column(Name = "file_page")]
		public int? FilePage { get; set; }

		[ Column(Name = "file_type")]
		public int? FileType { get; set; }

		[ Column(Name = "is_list")]
		public bool? IsList { get; set; }

		[ Column(Name = "is_show")]
		public bool? IsShow { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
