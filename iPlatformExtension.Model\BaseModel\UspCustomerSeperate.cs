using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "usp_customer_seperate", DisableSyncStructure = true)]
	public partial class UspCustomerSeperate {

		[ Column(Name = "@merge_id", StringLength = 50, IsNullable = false)]
		public string MergeId { get; set; }

		[ Column(Name = "@new_id", StringLength = 50, IsNullable = false)]
		public string NewId { get; set; }

		[ Column(Name = "@old_id", StringLength = 50, IsNullable = false)]
		public string OldId { get; set; }

	}

}
