using Confluent.Kafka;
using iPlatformExtension.Common.MQ;
using iPlatformExtension.Common.MQ.KafKa.Consumer;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using iPlatformExtension.Common.MQ.KafKa.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace KafkaProvider
{
    /// <summary>
    /// 重构后的Kafka服务实现
    /// </summary>
    public class KafkaServiceRefactored : IKafkaService
    {
        private readonly ILoggerFactory? _loggerFactory;
        private readonly KafkaConsumerFactory _consumerFactory;
        private readonly KafkaConsumerManager _consumerManager;
        private readonly KafkaErrorHandler _errorHandler;
        private ProducerConfig? _producerConfig;
        private ConsumerConfig? _consumerConfig;

        /// <summary>
        /// 初始化Kafka服务
        /// </summary>
        /// <param name="loggerFactory">日志工厂</param>
        public KafkaServiceRefactored(ILoggerFactory? loggerFactory = null)
        {
            _loggerFactory = loggerFactory;
            
            // 创建各个组件
            var consumerFactoryLogger = _loggerFactory?.CreateLogger<KafkaConsumerFactory>();
            _consumerFactory = new KafkaConsumerFactory(consumerFactoryLogger);
            
            var consumerManagerLogger = _loggerFactory?.CreateLogger<KafkaConsumerManager>();
            _consumerManager = new KafkaConsumerManager(_consumerFactory, consumerManagerLogger);
            
            var errorHandlerLogger = _loggerFactory?.CreateLogger<KafkaErrorHandler>();
            _errorHandler = new KafkaErrorHandler(errorHandlerLogger);
        }

        /// <summary>
        /// 获取生产者配置
        /// </summary>
        /// <param name="bootstrapServers">服务器地址</param>
        /// <returns>生产者配置</returns>
        public ProducerConfig GetProducerConfig(string bootstrapServers)
        {
            _producerConfig = new ProducerConfig()
            {
                BootstrapServers = bootstrapServers,
                ClientId = Dns.GetHostName(),
                //Partitioner = Partitioner.Murmur2Random
            };
            return _producerConfig;
        }

        /// <summary>
        /// 获取消费者配置
        /// </summary>
        /// <param name="bootstrapServers">服务器地址</param>
        /// <param name="groupId">消费者组ID</param>
        /// <returns>消费者配置</returns>
        public ConsumerConfig GetConsumerConfig(string bootstrapServers, string groupId = "Group1")
        {
            _consumerConfig = new ConsumerConfig
            {
                GroupId = groupId,
                BootstrapServers = bootstrapServers,
                AutoOffsetReset = AutoOffsetReset.Latest,
                //EnableAutoCommit = false,
                EnableAutoCommit = true, // (the default)
                EnableAutoOffsetStore = false
            };
            return _consumerConfig;
        }

        /// <summary>
        /// 发布消息
        /// </summary>
        /// <typeparam name="TMessage">消息类型</typeparam>
        /// <param name="topicName">主题名称</param>
        /// <param name="key">消息键</param>
        /// <param name="message">消息内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task PublishAsync<TMessage>(string topicName, MessageKey key, TMessage message, CancellationToken cancellationToken) where TMessage : class
        {
            if (_producerConfig == null) throw new ArgumentNullException("ProducerConfig is Required");
            
            try
            {
                using var producer = new ProducerBuilder<MessageKey, TMessage>(_producerConfig)
                    .SetKeySerializer(new JsonMessageKeyConverter(JsonSerializerOptions.Default))
                    .Build();
                
                await producer.ProduceAsync(topicName, new Message<MessageKey, TMessage>
                {
                    Key = key,
                    Value = message
                }, cancellationToken);
            }
            catch (Exception e)
            {
                var logger = _loggerFactory?.CreateLogger<KafkaServiceRefactored>();
                logger?.LogError(e, $"Error publishing message to topic {topicName}: {e.Message}");
                Console.WriteLine(e);
                throw;
            }
        }

        /// <summary>
        /// 订阅消息
        /// </summary>
        /// <typeparam name="TMessage">消息类型</typeparam>
        /// <param name="topics">主题列表</param>
        /// <param name="messageFunc">消息处理函数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task SubscribeAsync<TMessage>(IEnumerable<string> topics, Action<TMessage> messageFunc, CancellationToken cancellationToken) where TMessage : class
        {
            if (_consumerConfig == null) throw new ArgumentNullException("ServerOptions is Required");
            
            // 使用消费者管理器订阅消息
            await _consumerManager.SubscribeAsync(_consumerConfig, topics, messageFunc, cancellationToken);
        }

        /// <summary>
        /// 批量订阅消息
        /// </summary>
        /// <typeparam name="TMessage">消息类型</typeparam>
        /// <param name="topics">主题列表</param>
        /// <param name="batchAction">批量处理函数</param>
        /// <param name="batchSize">批量大小</param>
        /// <param name="maxBatchTimeMilliseconds">最大批处理时间（毫秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task SubscribeBatchAsync<TMessage>(
            IEnumerable<string> topics, 
            Action<IReadOnlyList<TMessage>> batchAction, 
            int batchSize = 100,
            int maxBatchTimeMilliseconds = 1000,
            CancellationToken cancellationToken = default) where TMessage : class
        {
            if (_consumerConfig == null) throw new ArgumentNullException("ServerOptions is Required");
            if (topics == null) throw new ArgumentNullException(nameof(topics));
            if (batchAction == null) throw new ArgumentNullException(nameof(batchAction));

            // 创建批量消息处理器
            var batchProcessorLogger = _loggerFactory?.CreateLogger<KafkaBatchMessageProcessor<TMessage>>();
            var batchProcessor = new KafkaBatchMessageProcessor<TMessage>(
                batchProcessorLogger,
                batchSize,
                maxBatchTimeMilliseconds);

            // 创建消费者
            using var consumer = _consumerFactory.CreateStringConsumer(_consumerConfig);
            
            // 订阅主题
            consumer.Subscribe(topics);

            try
            {
                // 批量处理消息
                await batchProcessor.ProcessBatchAsync(consumer, batchAction, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                var logger = _loggerFactory?.CreateLogger<KafkaServiceRefactored>();
                logger?.LogInformation("Closing batch consumer.");
                Console.WriteLine("Closing batch consumer.");
                consumer.Close();
            }
        }
    }
}