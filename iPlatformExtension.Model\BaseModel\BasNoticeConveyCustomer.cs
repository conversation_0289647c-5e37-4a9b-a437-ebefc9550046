using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_notice_convey_customer", DisableSyncStructure = true)]
	public partial class BasNoticeConveyCustomer {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		[ Column(Name = "convey_setting_id", StringLength = 50)]
		public string ConveySettingId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "last_time")]
		public DateTime? LastTime { get; set; }

		[ Column(Name = "next_ready_time")]
		public DateTime? NextReadyTime { get; set; }

		[ Column(Name = "next_time")]
		public DateTime? NextTime { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

	}

}
