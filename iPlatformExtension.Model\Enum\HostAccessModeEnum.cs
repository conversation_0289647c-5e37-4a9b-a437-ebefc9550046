﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Enum
{
    /// <summary>
    /// 邮件查看权限模型
    /// </summary>
    public class HostAccessModeEnum
    {
        /// <summary>
        /// 全部
        /// </summary>
        public const string All = "all";

        /// <summary>
        /// 查看
        /// </summary>
        public const string Read = "read";

        /// <summary>
        /// 发件
        /// </summary>
        public const string Write = "write";

        /// <summary>
        /// 分拣
        /// </summary>
        public const string Sorter = "sorter";

        /// <summary>
        /// 管理
        /// </summary>
        public const string Manager = "manager";

        /// <summary>
        /// 仅公共邮件
        /// </summary>
        public const string OnlyPublic = "only_public";
    }
}
