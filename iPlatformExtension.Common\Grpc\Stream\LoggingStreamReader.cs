﻿using Grpc.Core;
using iPlatformExtension.Common.Extensions;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.Grpc.Stream;

public class LoggingStreamReader<T>(ILogger logger, IAsyncStreamReader<T> innerReader) : IAsyncStreamReader<T>
{
    public Task<bool> MoveNext(CancellationToken cancellationToken)
    {
        return innerReader.MoveNext(cancellationToken);
    }

    public T Current
    {
        get
        {
            var current = innerReader.Current;
            logger.LogGrpcRequestParameter(current);
            return current;
        }
    }
}