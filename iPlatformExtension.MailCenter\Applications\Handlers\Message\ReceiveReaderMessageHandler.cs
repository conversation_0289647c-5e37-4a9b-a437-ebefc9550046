﻿using Confluent.Kafka;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.MailMQ;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.Model.Dto.MailCenter;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;
using MongoDB.Driver;
using static Confluent.Kafka.ConfigPropertyNames;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Message
{
    /// <summary>
    /// 收件阅读人消息
    /// </summary>
    /// <param name="producer"></param>
    /// <param name="httpContextAccessor"></param>
    /// <param name="sql"></param>
    public class ReceiveReaderMessageHandler(
          KafkaProducerService<Null, MailCenterMessageContent> producer,
        IHttpContextAccessor httpContextAccessor,
        IFreeSql<MailCenterFreeSql> sql
        ) : IRequestHandler<ReceiveReaderMessageQuery>
    {
        public async Task Handle(ReceiveReaderMessageQuery request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            //判断邮件状态是否处于处理中.
            List<MailReceive> mailList = null;
            List<MailReaderList> ReaderList = null;
            if (string.IsNullOrEmpty(request.MailId))
            {
                mailList = await sql.Select<MailReceive>().WithLock()
                    .WhereIf(request.OperationType == OperationTypeEnum.Sort, o => o.Status == (int)SysEnum.ReceiveFileType.Sort)
                    .WhereIf(request.OperationType != OperationTypeEnum.Sort, o => o.Status == (int)SysEnum.ReceiveFileType.Handle || o.Status == (int)SysEnum.ReceiveFileType.Processed)
                  .Where(o => request.ReaderList.Any(r => o.MailId == r.MailId))
                  .ToListAsync(cancellationToken);
                ReaderList = request.ReaderList;
            }
            else
            {
                mailList = await sql.Select<MailReceive>().WithLock()
                    .WhereIf(request.OperationType == OperationTypeEnum.Sort, o => o.Status == (int)SysEnum.ReceiveFileType.Sort)
                    .WhereIf(request.OperationType != OperationTypeEnum.Sort, o => o.Status == (int)SysEnum.ReceiveFileType.Handle || o.Status == (int)SysEnum.ReceiveFileType.Processed)
                    .Where(o => o.MailId == request.MailId)
                    .ToListAsync(cancellationToken);
                ReaderList = await sql.Select<MailReaderList>().Where(o => o.MailId == request.MailId).ToListAsync(cancellationToken);
            }


            ReaderList.ForEach(async r =>
            {
                var mailInfo = mailList.FirstOrDefault(o => o.MailId == r.MailId);
                if (mailInfo != null && r.Status == (int)ReaderStatusEnum.ToRead)
                {
                    MailCenterMessageContent content = new MailCenterMessageContent()
                    {
                        MailId = mailInfo.MailId,
                        MailTitle = mailInfo.MailSubject,
                        MailNo = mailInfo.MailNo,
                        OperatorUser = userId,
                        DateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        RecipientBy = r.UserId,
                        OperationType = request.OperationType
                    };
                    var res = await producer.SendMessageAsync(content, "MailCenterProducer");
                }
            });
        }
    }
}
