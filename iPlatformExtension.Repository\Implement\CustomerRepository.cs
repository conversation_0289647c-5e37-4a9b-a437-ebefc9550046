﻿using FreeSql;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class CustomerRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    DefaultRedisCache redisCache,
    CacheExpirationToken<CusCustomer> expirationToken)
    : BaseRepository<CusCustomer, string>(freeSql), ICustomerRepository
{
    IMemoryCache ICacheableRepository<string, CusCustomer>.MemoryCache => memoryCache;

    CacheExpirationToken<CusCustomer> ICacheableRepository<string, CusCustomer>.ExpirationToken => expirationToken;
    
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}