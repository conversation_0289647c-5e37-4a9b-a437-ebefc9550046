using Confluent.Kafka;
using Microsoft.Extensions.Logging;
using System;
using System.Net;

namespace iPlatformExtension.Common.MQ.KafKa.Consumer
{
    /// <summary>
    /// Kafka消费者工厂，负责创建消费者实例
    /// </summary>
    public class KafkaConsumerFactory
    {
        private readonly ILogger<KafkaConsumerFactory>? _logger;

        public KafkaConsumerFactory(ILogger<KafkaConsumerFactory>? logger = null)
        {
            _logger = logger;
        }

        /// <summary>
        /// 创建字符串消费者
        /// </summary>
        /// <param name="config">消费者配置</param>
        /// <returns>消费者实例</returns>
        public IConsumer<Ignore, string> CreateStringConsumer(ConsumerConfig config)
        {
            if (config == null) throw new ArgumentNullException(nameof(config));

            var consumerBuilder = new ConsumerBuilder<Ignore, string>(config);
            
            // 设置错误处理器
            consumerBuilder.SetErrorHandler((_, e) =>
            {
                var errorMessage = $"Error: {e.Reason}";
                _logger?.LogError(errorMessage);
                Console.WriteLine(errorMessage);
            });

            // 设置统计信息处理器
            consumerBuilder.SetStatisticsHandler((_, json) =>
            {
                var message = $" - {DateTime.Now:yyyy-MM-dd HH:mm:ss} > 消息监听中..";
                _logger?.LogInformation(message);
                Console.WriteLine(message);
            });

            // 设置分区分配处理器
            consumerBuilder.SetPartitionsAssignedHandler((c, partitions) =>
            {
                string partitionsStr = string.Join(", ", partitions);
                var message = $" - 分配的 kafka 分区: {partitionsStr}";
                _logger?.LogInformation(message);
                Console.WriteLine(message);
            });

            // 设置分区回收处理器
            consumerBuilder.SetPartitionsRevokedHandler((c, partitions) =>
            {
                string partitionsStr = string.Join(", ", partitions);
                var message = $" - 回收了 kafka 的分区: {partitionsStr}";
                _logger?.LogInformation(message);
                Console.WriteLine(message);
            });

            return consumerBuilder.Build();
        }
    }
}