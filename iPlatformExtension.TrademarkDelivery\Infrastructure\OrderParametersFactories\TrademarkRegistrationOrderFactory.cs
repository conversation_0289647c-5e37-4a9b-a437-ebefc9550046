﻿using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.TrademarkDelivery.Infrastructure.OrderParametersFactories;

public sealed class TrademarkRegistrationOrderFactory : IOrderParametersFactory, ITransientDependency
{
    private readonly IFreeSql _freeSql;

    private readonly ISystemDictionaryRepository _dictionaryRepository;

    public TrademarkRegistrationOrderFactory(IFreeSql freeSql, ISystemDictionaryRepository dictionaryRepository)
    {
        _freeSql = freeSql;
        _dictionaryRepository = dictionaryRepository;
    }

    public async Task<PhoenixOrderRequestParameters> CreateAsync(CaseProcInfo procInfo, CancellationToken cancellationToken)
    {
        var applicant = await _freeSql.Select<CusApplicant>().WithLock()
            .InnerJoin<CaseApplicantList>((cusApplicant, caseApplicant) =>
                cusApplicant.ApplicantId == caseApplicant.ApplicantId)
            .Where<CaseApplicantList>((cusApplicant, caseApplicant) =>
                caseApplicant.IsRepresent == true && cusApplicant.IsEnabled == true)
            .FirstAsync(cancellationToken);
        var applicantType = await _freeSql.Select<BasApplicantType>().WithLock()
            .Where(type => type.ApplicantTypeId == applicant.TypeId).FirstAsync(cancellationToken);
        var undertaker = await _freeSql.Select<SysUserInfo>().WithLock()
            .Where(info => info.UserId == procInfo.UndertakeUserId).FirstAsync(cancellationToken);
        
        var orderParameters = new TrademarkRegistrationOrder()
        {
            OwnerType = applicantType.ApplicantTypeCode == "5" ? "0" : "1",
            BookType = ApplicantBookType.GetApplicantBookType(applicant.CountryId).Code.ToString(),
            OrderToken = procInfo.ProcId,
            ApplicantName = applicant.ApplicantNameCn,
            EnglishApplicantName = applicant.ApplicantNameEn,
            IdCard = applicant.CardNo
        };

        return orderParameters;
    }
}