﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto.Flow
{
    /// <summary>
    /// SignalR消息体
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class SignalRMessage<T>
    {
        /// <summary>
        /// 消息方法
        /// </summary>
        public string Method { get; set; }

        public T MessageData { get; set; }
    }
}
