﻿using iPlatformExtension.Finance.Applications.Queries;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultFeeTypeNameQueryHandler(IBaseFeeTypeNameRepository baseFeeTypeNameRepository)
    : IRequestHandler<FeeResultFeeTypeNameQuery>
{
    private static readonly IEnumerable<string> annualFeeCodes = new[] {"AN", "ANZNJ"};

    private static readonly IEnumerable<string> licenseFeeCodes = new[] {"SQ1", "SQ2", "SQ3", "SQ4"};

    public async Task Handle(FeeResultFeeTypeNameQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
           return;

        foreach (var feeItem in request.FeeResults)
        {
            var feeNameInfo = await baseFeeTypeNameRepository.GetCacheValueAsync(feeItem.FeeNameId);;
            feeItem.FeeName = feeNameInfo;

            if (annualFeeCodes.Contains(feeNameInfo.NameCode) && !feeItem.IsFirstPayAnnual)
            {
                feeItem.AnnualFeeType = "年费";
            }
            else if (annualFeeCodes.Contains(feeNameInfo.NameCode) && feeItem.IsFirstPayAnnual || (licenseFeeCodes.Contains(feeNameInfo.NameCode) && !feeItem.IsFirstPayAnnual))
            {
                feeItem.AnnualFeeType = "授权费";
            }
            else if (!annualFeeCodes.Contains(feeNameInfo.NameCode) && !licenseFeeCodes.Contains(feeNameInfo.NameCode))
            {
                feeItem.AnnualFeeType = "非年费（不含授权费）";
            }
            else if (!feeNameInfo.NameCode.Equals("ABCDE", StringComparison.OrdinalIgnoreCase))
            {
                if (annualFeeCodes.Contains(feeNameInfo.NameCode) && feeItem.IsFirstPayAnnual || !annualFeeCodes.Contains(feeNameInfo.NameCode))
                {
                    feeItem.AnnualFeeType = "非年费（含授权费）";
                }
            }
        }
    }
}