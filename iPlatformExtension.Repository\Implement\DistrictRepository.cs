﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class DistrictRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    DefaultRedisCache redisCache,
    CacheExpirationToken<BasDistrict> expirationToken)
    : BaseRepository<BasDistrict, string>(freeSql), IDistrictRepository
{
    IMemoryCache ICacheableRepository<string, BasDistrict>.MemoryCache => memoryCache;

    CacheExpirationToken<BasDistrict> ICacheableRepository<string, BasDistrict>.ExpirationToken => expirationToken;
    
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}