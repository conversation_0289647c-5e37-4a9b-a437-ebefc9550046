using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "tm_case_notice_restore", DisableSyncStructure = true)]
	public partial class TmCaseNoticeRestore {

		[ Column(Name = "app_no", StringLength = 50)]
		public string AppNo { get; set; }

		[ Column(Name = "batch_id", StringLength = 50, IsNullable = false)]
		public string BatchId { get; set; }

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "file_name", StringLength = 500)]
		public string FileName { get; set; }

		[ Column(Name = "file_no", StringLength = 50)]
		public string FileNo { get; set; }

		[ Column(Name = "hash_no", StringLength = 100)]
		public string HashNo { get; set; }

		[ Column(Name = "server_path", StringLength = 1000)]
		public string ServerPath { get; set; }

		[ Column(Name = "server_path_temp", StringLength = 1000)]
		public string ServerPathTemp { get; set; }

		[ Column(Name = "sn_id")]
		public long? SnId { get; set; }

		[ Column(Name = "sn_no", StringLength = 4000)]
		public string SnNo { get; set; }

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

	}

}
