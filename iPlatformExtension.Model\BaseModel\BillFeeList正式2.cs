using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_fee_list_正式2", DisableSyncStructure = true)]
	public partial class BillFeeList正式2 {

		[ Column(Name = "allot_id", StringLength = 50)]
		public string AllotId { get; set; }

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "amount_in_bill", DbType = "money")]
		public decimal? AmountInBill { get; set; }

		[ Column(Name = "amount_poundage", DbType = "money")]
		public decimal? AmountPoundage { get; set; }

		[ Column(Name = "amount_rate", DbType = "money")]
		public decimal? AmountRate { get; set; }

		[ Column(Name = "amount_tax", DbType = "money")]
		public decimal? AmountTax { get; set; }

		[ Column(Name = "bill_id", StringLength = 50)]
		public string BillId { get; set; }

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "fee_add_time")]
		public DateTime? FeeAddTime { get; set; }

		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		[ Column(Name = "fee_create_time")]
		public DateTime? FeeCreateTime { get; set; }

		[ Column(Name = "fee_id", StringLength = 50)]
		public string FeeId { get; set; }

		[ Column(Name = "fee_type_name_id", StringLength = 50)]
		public string FeeTypeNameId { get; set; }

		[ Column(Name = "has_tax")]
		public bool? HasTax { get; set; }

		[ Column(Name = "invoice_type", StringLength = 50)]
		public string InvoiceType { get; set; }

		[ Column(Name = "office_name", StringLength = 1000)]
		public string OfficeName { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "status")]
		public int? Status { get; set; }

	}

}
