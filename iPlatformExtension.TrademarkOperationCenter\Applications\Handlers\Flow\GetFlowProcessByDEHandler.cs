﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;
using System.Linq.Expressions;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public class GetFlowProcessByDEHandler(IFreeSql freeSql) : IRequestHandler<FlowProcessByDEQuery, PageResult<GetFlowProcessDto>>
    {
        public async Task<PageResult<GetFlowProcessDto>> Handle(FlowProcessByDEQuery request, CancellationToken cancellationToken)
        {
            //Expression<Func<SysFlowActivity, bool>> whereCommon = (fa) => fa.FlowType == request.FFlowType && fa.CurUserId == userid;
            //Expression<Func<SysFlowActivity, bool>> whereTodo = null;
            var totalCount = 0L;
            var list = new List<GetFlowProcessDto>();
            var sql = freeSql.Select<SysFlowActivity>().WithLock();


            list = await sql.Where(request.whereCommon).Where(request.whereTodo)
                   .Count(out totalCount)
                   .ToListAsync(fa => new GetFlowProcessDto()
                   {
                       procNo = fa.CaseProcInfo.ProcNo,
                       caseName = fa.CaseProcInfo.CaseInfo.CaseName,
                       UndertakeUserName = fa.CaseProcInfo.UndertakeUserInfo.CnName,
                       ctrlProcName = fa.CaseProcInfo.BasCtrlProc.CtrlProcZhCn,
                       appNo = fa.CaseProcInfo.CaseInfo.AppNo,
                       TrademarkClass = fa.CaseProcInfo.DeliInfo.OtherInfo.TrademarkNiceClasses,
                       LegalDueDate = fa.CaseProcInfo.LegalDueDate,
                       Status = fa.CaseProcInfo.DeliInfo.Status,
                       OperationResult = fa.CaseProcInfo.DeliInfo.OperationResult,
                       CurNode = fa.CurFlowNode.NameZhCn,
                       CurNodeId = fa.CurNodeId,
                       PreNode = fa.PreFlowNode.NameZhCn,
                       SubmitType = freeSql.Select<SysDictionary>().Where(d => d.Value == fa.PrevAuditTypeId && d.DictionaryName == "audit_type").ToOne(o => o.TextZhCn),
                       UpdateTime = fa.UpdateTime,
                       CurHandler = fa.CurNodeUserInfo.CnName,
                       ObjectId = fa.ObjId,
                       IsAuto = fa.CaseProcInfo.DeliInfo.IsAuto,
                       IsStandardNice = (!fa.CaseProcInfo.DeliInfo.IsStandardNice.HasValue || fa.CaseProcInfo.DeliInfo.IsStandardNice == true) ? false : true,
                       IsCheck = fa.CaseProcInfo.DeliInfo.Status == (int)DeliveryStatus.Confirmed ? true : false,
                       Country = fa.CaseProcInfo.CaseInfo.Country.CountryZhCn,
                       ApplyTypeId = fa.CaseProcInfo.CaseInfo.ApplyType.ApplyTypeZhCn,
                       RequirementSubmitDate = fa.CaseProcInfo.RequirementSubmitDate,
                       SubProcStatus = fa.CaseProcInfo.SubProcStatus.TextZhCn,
                       AgencyName = fa.CaseProcInfo.Agency.AgencyNameCn,
                       TeamId = fa.CaseProcInfo.TeamId,
                       Version = fa.CaseProcInfo.DeliInfo.Version,
                       ReturnDocDate = fa.CaseProcInfo.ReturnDocDate,
                       ApplicantName = freeSql.Select<DeliApplicant>().Any(o => o.ProcId == fa.CaseProcInfo.ProcId) ? freeSql.Select<DeliApplicant>().Where(o => o.ProcId == fa.CaseProcInfo.ProcId).OrderByDescending(o => o.IsRepresent).ToOne(o => o.ApplicantNameCn) : string.Empty,
                       Volume = fa.CaseProcInfo.CaseInfo.Volume,
                       RemainingDays = (fa.CaseProcInfo.BasCtrlProc.TrademarkTab == "0" || fa.CaseProcInfo.BasCtrlProc.TrademarkTab == "3") ? (!fa.CaseProcInfo.ReturnDocDate.HasValue ? null : (fa.CaseProcInfo.ReturnDocDate.Value - DateTime.Now.Date).Days + 1) :
                       (fa.CaseProcInfo.CtrlProcId == "DD886CBE-D8D4-4BE0-97F7-FD521674269A" ? (!fa.CaseProcInfo.RequirementSubmitDate.HasValue ? null : (fa.CaseProcInfo.RequirementSubmitDate.Value - DateTime.Now.Date).Days + 1) :
                       (!fa.CaseProcInfo.LegalDueDate.HasValue ? null : (fa.CaseProcInfo.LegalDueDate.Value - DateTime.Now.Date).Days + 1)
                       ),
                       CustomerName = fa.CaseProcInfo.CaseInfo.Customer.CustomerName,
                       ProcID = fa.CaseProcInfo.ProcId,
                       CtrlProcMark = freeSql.Select<SysDictionary>().Where(o => o.DictionaryName == "ctrl_proc_mark" && o.Value == fa.CaseProcInfo.DeliInfo.OtherInfo.CtrlProcMark).ToOne(o => o.TextZhCn),
                       DeliveryKey = freeSql.Select<SysDictionary>().Where(o => o.DictionaryName == "delivery_key" && o.Value == fa.CaseProcInfo.DeliveryKey).ToOne(o=>o.TextZhCn),
                       ImportantTrademark = fa.CaseProcInfo.CaseInfo.ImportantTrademark,
                       Prioritization = fa.CaseProcInfo.CaseInfo.Prioritization,
                       HighCosts = fa.CaseProcInfo.CaseInfo.HighCosts,
                       RegisterNo = fa.CaseProcInfo.CaseInfo.RegisterNo
                   }, cancellationToken);

            var res = list.CustomSort<GetFlowProcessDto>(request.Sort, request.IsAscending)
              .Skip(request.PageSize * (request.PageIndex - 1)).Take(request.PageSize).ToList();

            return new PageResult<GetFlowProcessDto>()
            {
                Data = res,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };
        }
    }
}
