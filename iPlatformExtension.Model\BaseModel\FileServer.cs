using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "file_server", DisableSyncStructure = true)]
	public partial class FileServer {

		[ Column(Name = "server_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ServerId { get; set; }

		[ Column(Name = "abs_path", StringLength = 300)]
		public string AbsPath { get; set; }

		[ Column(Name = "server_port", StringLength = 10)]
		public string ServerPort { get; set; }

		[ Column(Name = "server_type", StringLength = 10, IsNullable = false)]
		public string ServerType { get; set; }

		[ Column(Name = "server_url", StringLength = 200)]
		public string ServerUrl { get; set; }

		[ Column(Name = "server_url_o", StringLength = 50)]
		public string ServerUrlO { get; set; }

		[ Column(Name = "user_name", StringLength = 20)]
		public string UserName { get; set; }

		[ Column(Name = "user_pass", StringLength = 20)]
		public string UserPass { get; set; }

	}

}
