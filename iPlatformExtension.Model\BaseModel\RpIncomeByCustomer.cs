using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_income_by_customer", DisableSyncStructure = true)]
	public partial class RpIncomeByCustomer {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "amount_asc", DbType = "money")]
		public decimal? AmountAsc { get; set; }

		[ Column(Name = "amount_asc_hb", DbType = "money")]
		public decimal? AmountAscHb { get; set; }

		[ Column(Name = "case_type", StringLength = 50)]
		public string CaseType { get; set; }

		[ Column(Name = "customer_name", StringLength = 200)]
		public string CustomerName { get; set; }

		[ Column(Name = "dept_id", StringLength = 50)]
		public string DeptId { get; set; }

		[ Column(Name = "dept_name", StringLength = 500)]
		public string DeptName { get; set; }

		[ Column(Name = "month", StringLength = 5)]
		public string Month { get; set; }

		[ Column(Name = "quarter", StringLength = 5)]
		public string Quarter { get; set; }

		[ Column(Name = "user_area", StringLength = 50)]
		public string UserArea { get; set; }

		[ Column(Name = "user_district", StringLength = 50)]
		public string UserDistrict { get; set; }

		[ Column(Name = "year", StringLength = 5)]
		public string Year { get; set; }

	}

}
