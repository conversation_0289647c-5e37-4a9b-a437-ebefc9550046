using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_table_custom", DisableSyncStructure = true)]
	public partial class SysTableCustom {

		[ Column(Name = "custom_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CustomId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "custom_name", StringLength = 50)]
		public string CustomName { get; set; }

		[ Column(Name = "fixed_column")]
		public int? FixedColumn { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
