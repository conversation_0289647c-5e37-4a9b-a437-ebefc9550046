﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[Table(Name = "rp_foreign_trademark_bonus", DisableSyncStructure = true)]
	public partial class RpForeignTrademarkBonus {

		[Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ProcId { get; set; }

		[Column(Name = "agency_fee", StringLength = -2)]
		public string AgencyFee { get; set; }

		[Column(Name = "app_no", StringLength = 50)]
		public string AppNo { get; set; }

		[Column(Name = "bigclient", DbType = "varchar(2)", IsNullable = false)]
		public string Bigclient { get; set; }

		[Column(Name = "case_name", StringLength = 500)]
		public string CaseName { get; set; }

		[Column(Name = "cn_name", StringLength = 50, IsNullable = false)]
		public string CnName { get; set; }

		[Column(Name = "ctrl_proc_mark", StringLength = 50)]
		public string CtrlProcMark { get; set; }

		[Column(Name = "ctrl_proc_mark_cn", StringLength = 100)]
		public string CtrlProcMarkCn { get; set; }

		[Column(Name = "ctrl_proc_zh_cn", StringLength = 50)]
		public string CtrlProcZhCn { get; set; }

		[Column(Name = "customer_name", StringLength = 1000)]
		public string CustomerName { get; set; }

		[Column(Name = "dept_id", StringLength = 50, IsNullable = false)]
		public string DeptId { get; set; }

		[Column(Name = "dept_name", StringLength = 100)]
		public string DeptName { get; set; }

		[Column(Name = "district_code", StringLength = 50)]
		public string DistrictCode { get; set; }

		[Column(Name = "district_name", StringLength = 50)]
		public string DistrictName { get; set; }

		[Column(Name = "month")]
		public int Month { get; set; }

		[Column(Name = "point_date")]
		public DateTime PointDate { get; set; }

		[Column(Name = "proc_no", StringLength = 50)]
		public string ProcNo { get; set; }

		[Column(Name = "proc_point", DbType = "money")]
		public decimal ProcPoint { get; set; }

		[Column(Name = "receive_date")]
		public DateTime ReceiveDate { get; set; }

		[Column(Name = "register_no", StringLength = 50)]
		public string? RegisterNo { get; set; }

		[Column(Name = "status", StringLength = 50)]
		public string Status { get; set; }

		[Column(Name = "trademark_items", StringLength = -2)]
		public string TrademarkItems { get; set; }

		[Column(Name = "trademark_items_num")]
		public int? TrademarkItemsNum { get; set; }

		[Column(Name = "undertake_user_id", StringLength = 50)]
		public string UndertakeUserId { get; set; }

		[Column(Name = "user_name", StringLength = 50)]
		public string UserName { get; set; }

		[Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

		[Column(Name = "year")]
		public int Year { get; set; }

		/// <summary>
		/// 任务名称id
		/// </summary>
		[Column(Name = "ctrl_proc_id", StringLength = 50, IsNullable = false)]
		public string CtrlProcId { get; set; } = string.Empty;

		/// <summary>
		/// 任务状态id
		/// </summary>
		[Column(Name = "proc_status_id", StringLength = 50, IsNullable = false)]
		public string ProcStatusId { get; set; } = string.Empty;

		/// <summary>
		/// 国际分类
		/// </summary>
		[Column(Name = "trademark_classes", IsNullable = false)]
		public string TrademarkClasses { get; set; } = string.Empty;

		/// <summary>
		/// 案件id
		/// </summary>
		[Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; } = string.Empty;

		/// <summary>
		/// 客户id
		/// </summary>
		[Column(Name = "customer_id", StringLength = 50, IsNullable = false)]
		public string CustomerId { get; set; } = string.Empty;

		/// <summary>
		/// 案件流向
		/// </summary>
		[Column(Name = "case_direction", IsNullable = false)]
		public string CaseDirection { get; set; } = string.Empty;

		/// <summary>
		/// 推送装填
		/// </summary>
		[Column(Name = "pushed", IsNullable = false)]
		public bool Pushed { get; set; }

		/// <summary>
		/// 创建者
		/// </summary>
		[Column(Name = "creator")]
		public string Creator { get; set; } = string.Empty;

		/// <summary>
		/// 更新者
		/// </summary>
		[Column(Name = "updater")]
		public string Updater { get; set; } = string.Empty;

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time")]
		public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "creation_time")]
		public DateTime CreationTime { get; set; }
		
		/// <summary>
		/// 编辑后的任务权值
		/// </summary>
		[Column(Name = "edited_proc_point")]
		public decimal? EditedProcPoint { get; set; }
	}

}
