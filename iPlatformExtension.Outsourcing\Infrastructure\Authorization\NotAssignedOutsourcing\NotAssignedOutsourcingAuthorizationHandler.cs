﻿using iPlatformExtension.Outsourcing.Application.Handlers.Proc;
using Microsoft.AspNetCore.Authorization;

namespace iPlatformExtension.Outsourcing.Infrastructure.Authorization.NotAssignedOutsourcing;

internal sealed class NotAssignedOutsourcingAuthorizationHandler(
    NotAssignedProcQueryContextFactory contextFactory)
    : AuthorizationHandler<NotAssignedOutsourcingAuthorizationRequirement, HttpContext>
{
    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context,
        NotAssignedOutsourcingAuthorizationRequirement requirement, HttpContext resource)
    {

        if  (resource.Request.Query.TryGetValue("authorizationType", out var authorizationType) 
            && !string.IsNullOrWhiteSpace(authorizationType))
        {
            var authorizationTypes = requirement.AuthorizationTypes;
            if (!authorizationTypes.Contains<string?>(authorizationType))
            {
                context.Fail(new AuthorizationFailureReason(this, "你没有相应的权限"));
                return;
            }
            
            var notAssignedProcQueryContext =
                await contextFactory.CreateAsync(authorizationType!, resource.RequestAborted);
           
            if (notAssignedProcQueryContext is not null)
            {
                context.Succeed(requirement);
            }
            else
            {
                context.Fail(new AuthorizationFailureReason(this, "你没有相应的权限"));
            }
        }
    }
}