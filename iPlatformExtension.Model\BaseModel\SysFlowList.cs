using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_flow_list", DisableSyncStructure = true)]
	public partial class SysFlowList {

		[ Column(Name = "list_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ListId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "allow_edit")]
		public bool AllowEdit { get; set; } = true;

		[ Column(Name = "copy_list", StringLength = 4000)]
		public string CopyList { get; set; }

		[ Column(Name = "flow_id", StringLength = 50, IsNullable = false)]
		public string FlowId { get; set; }

		[ Column(Name = "next", StringLength = 50)]
		public string Next { get; set; }

		[ Column(Name = "node_id", StringLength = 50)]
		public string NodeId { get; set; }

		[ Column(Name = "node_type", StringLength = 50)]
		public string NodeType { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "user_list", StringLength = 2000)]
		public string UserList { get; set; }

	}

}
