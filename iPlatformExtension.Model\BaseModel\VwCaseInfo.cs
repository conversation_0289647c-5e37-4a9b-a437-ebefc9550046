using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_case_info", DisableSyncStructure = true)]
	public partial class VwCaseInfo {

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "case_name", StringLength = 500)]
		public string CaseName { get; set; }

		[ Column(Name = "country", StringLength = 50)]
		public string Country { get; set; }

		[ Column(Name = "customer_name", StringLength = 1000)]
		public string CustomerName { get; set; }

		[ Column(Name = "direction_name", StringLength = 100)]
		public string DirectionName { get; set; }

		[ Column(Name = "sales_user", StringLength = 50, IsNullable = false)]
		public string SalesUser { get; set; }

		[ Column(Name = "undertake_dept", StringLength = 500)]
		public string UndertakeDept { get; set; }

		[ Column(Name = "undertake_dept_id", StringLength = 50)]
		public string UndertakeDeptId { get; set; }

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

	}

}
