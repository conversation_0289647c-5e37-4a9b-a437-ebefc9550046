﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.Applicant;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 申请人控制器
/// </summary>
/// <param name="mediator"></param>
[ApiController]
[Route("[controller]")]
public sealed class ApplicantController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// 查询申请人
    /// </summary>
    /// <param name="keyword">关键字</param>
    /// <returns></returns>
    [HttpGet]
    public Task<IEnumerable<ApplicantInfo>> GetByKeywordAsync([FromQuery]string? keyword)
    {
        return mediator.Send(new ApplicantKeywordQuery(keyword));
    }

    /// <summary>
    /// 获取案件代表申请人详情信息
    /// </summary>
    /// <param name="caseId">案件id</param>
    /// <returns>申请人详情</returns>
    [HttpGet("/case/{caseId}/applicant/represent")]
    public Task<ApplicantDetailDto> GetRepresentAsync(string caseId)
    {
        return mediator.Send(new CaseRepresentApplicantQuery(caseId));
    }
    
    /// <summary>
    /// 获取申请人详情信息
    /// </summary>
    /// <param name="applicantId">申请人id</param>
    /// <returns>申请人详情</returns>
    [HttpGet("{applicantId}")]
    public Task<ApplicantDetailDto> GetAsync(string applicantId)
    {
        return mediator.Send(new ApplicantDetailQuery(applicantId));
    }

    /// <summary>
    /// 获取申请人下的地址信息
    /// </summary>
    /// <param name="applicantId">申请人id</param>
    /// <returns>地址信息</returns>
    [HttpGet("{applicantId}/address")]
    public Task<IEnumerable<AddressInfoDto>> GetApplicantAddressAsync(string applicantId)
    {
        return mediator.Send(new ApplicantAddressQuery(applicantId));
    }
}