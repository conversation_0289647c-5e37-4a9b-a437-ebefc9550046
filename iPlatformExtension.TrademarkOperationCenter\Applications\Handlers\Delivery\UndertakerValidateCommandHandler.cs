﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class UndertakerValidateCommandHandler(IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<UndertakerValidateCommand, BatchDeliveryValidateResult>
{
    public Task<BatchDeliveryValidateResult> Handle(UndertakerValidateCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = (httpContextAccessor.HttpContext?.User).GetUserId();

        return Task.FromResult(request.ValidationItems.Where(dto => dto.CurrentUserId != currentUserId).Aggregate(
            new BatchDeliveryValidateResult(request.ValidationItems.Count()),
            (result, dto) => result.InvalidUndertaker(dto.ProcNo)));
    }
}