﻿using iPlatformExtension.Model.Extensions;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 金蝶物料配置键
/// </summary>
/// <param name="FeeTypeNameId">费用名称id</param>
/// <param name="CaseType">案件类型</param>
/// <param name="CaseDirection">案件流向</param>
/// <param name="FeeClass">费用类型</param>
public record struct KingdeeMaterialTaxationKey(
    string FeeTypeNameId, 
    string CaseType,
    string CaseDirection,
    string FeeClass);

/// <summary>
/// <see cref="KingdeeMaterialTaxationKey"/>相等比较器。
/// 用于获取<see cref="KingdeeMaterialTaxationKey"/>的哈希值以及比较两个<see cref="KingdeeMaterialTaxationKey"/>对象的值相等
/// </summary>
public class KingdeeMaterialTaxationKeyEqualityComparer : IEqualityComparer<KingdeeMaterialTaxationKey>
{
    /// <summary>
    /// 比较两个<see cref="KingdeeMaterialTaxationKey"/>对象是否值相等
    /// </summary>
    /// <param name="x">第一个<see cref="KingdeeMaterialTaxationKey"/>对象</param>
    /// <param name="y">第二个<see cref="KingdeeMaterialTaxationKey"/>对象</param>
    /// <returns>如果属性值都相等返回<c>true</c>否则返回<c>false</c></returns>
    public bool Equals(KingdeeMaterialTaxationKey x, KingdeeMaterialTaxationKey y)
    {
        return x.Equals(y);
    }

    /// <summary>
    /// 获取<see cref="KingdeeMaterialTaxationKey"/>的哈希值
    /// </summary>
    /// <param name="obj"><see cref="KingdeeMaterialTaxationKey"/>对象</param>
    /// <returns>哈希值</returns>
    public int GetHashCode(KingdeeMaterialTaxationKey obj)
    {
        return HashCode.Combine(obj.FeeTypeNameId, obj.CaseType, obj.CaseDirection, obj.FeeClass);
    }
}
