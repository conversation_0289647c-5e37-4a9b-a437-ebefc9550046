﻿using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Cache;

public sealed class CacheExpirationTokenProvider
{
    private readonly Dictionary<string, CacheExpirationToken?> _expirationTokens;

    public CacheExpirationTokenProvider(IServiceProvider serviceProvider)
    {
        var registerTokens = serviceProvider.GetRequiredService<IEnumerable<CacheExpirationToken>>();
        _expirationTokens = new Dictionary<string, CacheExpirationToken?>(registerTokens.Count());
        foreach (var cacheExpirationToken in registerTokens)
        {
            _expirationTokens[cacheExpirationToken.Name] = 
                serviceProvider.GetRequiredService(cacheExpirationToken.GetType()) as CacheExpirationToken;
        }
    }

    public CacheExpirationToken? GetCacheExpirationToken(string name)
    {
        ArgumentNullException.ThrowIfNull(name);
        return _expirationTokens.TryGetValue(name, out var expirationToken) ? expirationToken : default;
    }

    public IEnumerable<CacheExpirationToken?> GetCacheExpirationTokens() => _expirationTokens.Values;
}