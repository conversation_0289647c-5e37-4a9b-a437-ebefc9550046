﻿/*----------------------------------------------------------------------------------
// Copyright 2019 Huawei Technologies Co.,Ltd.
// Licensed under the Apache License, Version 2.0 (the "License"); you may not use
// this file except in compliance with the License.  You may obtain a copy of the
// License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations under the License.
//----------------------------------------------------------------------------------*/
using System;
using System.Collections.Generic;
using System.Text;

namespace OBS.Model
{
    /// <summary>
    /// SSE-KMS encryption headers
    /// </summary>
    public class SseKmsHeader : SseHeader
    {
        /// <summary>
        /// SSE-KMS encryption algorithm
        /// </summary>
        public SseKmsAlgorithmEnum Algorithm
        {
            get;
            set;
        }

        /// <summary>
        /// Master key used in the SSE-KMS encryption mode
        /// </summary>
        /// <remarks>
        /// <para>
        /// Optional parameter
        /// </para>
        /// </remarks>
        public string Key
        {
            get;
            set;
        }
    }
}


