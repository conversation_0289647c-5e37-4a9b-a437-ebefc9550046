﻿using iPlatformExtension.Finance.Applications.Queries.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Proc;

internal sealed class UnpaidOfficialProcResultCtrlProcHandler(IBaseCtrlProcRepository ctrlProcRepository) 
    : INotificationHandler<UnpaidOfficialProcResultNotification>
{
    public async Task Handle(UnpaidOfficialProcResultNotification notification, CancellationToken cancellationToken)
    {
        var results = notification.Results;
        foreach (var dto in results)
        {
            dto.ProcName = await ctrlProcRepository.GetTextValueAsync(dto.CtrlProcId) ?? string.Empty;
        }
    }
}