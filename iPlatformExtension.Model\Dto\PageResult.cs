﻿using System.Collections;
using System.Diagnostics;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 分页结果基类
/// </summary>
public abstract class PageResultBase : IEnumerable
{
    /// <summary>
    /// 当前页码
    /// </summary>
    public int Page { get; init; }

    /// <summary>
    /// 页面大小
    /// </summary>
    public int PageSize { get; init; }
    
    /// <summary>
    /// 数据总数
    /// </summary>
    public long Total { get; set; }

    private long? _totalPage;
    
    /// <summary>
    /// 总页数
    /// </summary>
    public long TotalPage
    {
        get
        {
            if (!_totalPage.HasValue)
            {
                if (PageSize <= 0)
                {
                    _totalPage = 0;
                    return _totalPage.Value;
                }
            
                _totalPage = Total / PageSize;
                if (Total % PageSize != 0)
                    ++_totalPage;
                
                Debug.Assert(_totalPage != null, nameof(_totalPage) + " != null");
                return _totalPage.Value;
            }
            else
            {
                return _totalPage.Value;
            }
        }
    }

    /// <summary>
    /// 是否有下一页
    /// </summary>
    public bool HasNextPage => Page < TotalPage;

    /// <summary>
    /// 是否有上一页
    /// </summary>
    public bool HasPreviousPage => Page > 1 && Page <= TotalPage;

    /// <summary>
    /// 枚举器接口的抽象实现
    /// </summary>
    /// <returns>数据结果集</returns>
    public abstract IEnumerator GetEnumerator();

    /// <summary>
    /// 结构函数
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">页面大小</param>
    /// <param name="totalPage">总页</param>
    /// <param name="total">总数</param>
    /// <param name="hasNextPage">是否有上一页</param>
    /// <param name="hasPreviousPage">是否有下一页</param>
    public void Deconstruct(out int page, out int pageSize, out long totalPage, out long total, out bool hasNextPage, out bool hasPreviousPage)
    {
        page = Page;
        pageSize = PageSize;
        totalPage = TotalPage;
        total = Total;
        hasNextPage = HasNextPage;
        hasPreviousPage = HasPreviousPage;
    }
}

/// <summary>
/// 分页查询结果
/// </summary>
/// <typeparam name="T">结果集的类型参数</typeparam>
public  class PageResult<T> : PageResultBase, IEnumerable<T>, IDisposable
{
    /// <summary>
    /// 查询结果集
    /// </summary>
    public IEnumerable<T> Data { get; set; } = [];

    /// <summary>
    /// 实现迭代器接口
    /// </summary>
    /// <returns>数据结果集</returns>
    public override IEnumerator<T> GetEnumerator()
    {
        return Data.GetEnumerator();
    }

    /// <summary>
    /// 实现迭代器接口
    /// </summary>
    /// <returns>数据结果集</returns>
    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (Data is ICollection<T> {IsReadOnly: false} collection)
        {
            collection.Clear();
        }

        Data = null!;
    }
}
