﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using System.Text.RegularExpressions;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 获取附件
    /// </summary>
    internal sealed class GetMailAttachmentQueryHandler(IFreeSql<MailCenterFreeSql> freeSql, IConfiguration configuration) : IRequestHandler<GetMailAttachmentQuery, IEnumerable<GetMailAttachmentDto>>
    {
        public async Task<IEnumerable<GetMailAttachmentDto>> Handle(GetMailAttachmentQuery request, CancellationToken cancellationToken)
        {
            return await freeSql.Select<MailAttachments>().WithLock().Where(it => it.MailId == request.MailId)
                .ToListAsync(it => new GetMailAttachmentDto(it.AttachmentId, it.Bucket, it.Extension, it.FileName, it.InputTime, it.MailId, it.RealName, it.ServerPath, it.FileSize
                    , $"https://{it.Bucket}.{Regex.Replace(configuration["HuaweiObs:Host"]!, @"^https?://", "")}/{it.ServerPath}/{it.FileName}"), cancellationToken);
        }
    }
}

