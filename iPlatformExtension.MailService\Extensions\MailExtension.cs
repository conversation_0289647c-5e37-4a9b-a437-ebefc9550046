﻿using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.MailService.Infrastructure;
using MimeKit;
using MimeKit.IO;
using System.Collections.Concurrent;

namespace iPlatformExtension.MailService.Extensions
{
    public static class MailExtension
    {
        public static void AddMailExtension(this IServiceCollection services)
        {
            services.AddSingleton<MailTool>();
        }
    }
}
