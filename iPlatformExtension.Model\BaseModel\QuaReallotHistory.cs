using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "qua_reallot_history", DisableSyncStructure = true)]
	public partial class QuaReallotHistory {

		[ Column(Name = "create_time")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "detail_id", StringLength = 50)]
		public string DetailId { get; set; }

		[ Column(Name = "examine_user_id", StringLength = 50)]
		public string ExamineUserId { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; }

	}

}
