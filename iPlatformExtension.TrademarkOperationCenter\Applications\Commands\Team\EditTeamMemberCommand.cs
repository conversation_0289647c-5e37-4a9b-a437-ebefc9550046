﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;

/// <summary>
/// 添加团队成员命令
/// </summary>
/// <param name="TeamId">团队id</param>
/// <param name="Members">成员</param>
public record EditTeamMemberCommand([Required(ErrorMessage = "团队id不能为空")] string TeamId,
    IEnumerable<MemberInput> Members) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

