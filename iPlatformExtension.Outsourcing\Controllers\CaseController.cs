using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Outsourcing.Application.Commands.Case;
using iPlatformExtension.Outsourcing.Application.Models.Case;
using iPlatformExtension.Outsourcing.Application.Queries.Case;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Outsourcing.Controllers;

[Tags("案件委外信息控制器")]
[ApiController]
[Route("case")]
[Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
public sealed class CaseController(ISender sender) : ControllerBase
{
    [EndpointName(nameof(GetCaseOutsourcingAsync))]
    [EndpointSummary("获取案件委外信息")]
    [EndpointDescription("获取案件委外信息，包含境外代理信息和境外代理备注信息")]
    [ProducesResponseType<ResultData<CaseOutsourcingDto>>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    [HttpGet("{caseId}/outsourcing")]
    public Task<CaseOutsourcingDto> GetCaseOutsourcingAsync([FromRoute, Description("案件id"), Required] string caseId)
    {
        return sender.Send(new CaseOutsourcingQuery(caseId), HttpContext.RequestAborted);
    }

    [EndpointName(nameof(UpdateCaseOutsourcingAsync))]
    [EndpointSummary("修改案件委外信息")]
    [EndpointDescription("修改案件委外信息，外所文号、境外代理信息、境外代理联系人信息和境外代理备注信息。")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    [ProducesResponseType<ResultData>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    [HttpPatch("{caseId}/outsourcing")]
    [Authorize(Roles = "专利流程人员")]
    public Task UpdateCaseOutsourcingAsync(
        [FromRoute, Description("案件id"), Required] string caseId,
        [FromBody, Description("""
                               案件委外信息jsonPath请求体。
                               path说明：
                               /foreignAgencyId：境外代理id
                               /foreignCaseNo：外所文号
                               /foreignSupplierRemark：选所备注
                               /contactorIds：联系人id集合
                               """), Required] JsonPatchDocument<CasePatchDto> patchDocument)
    {
        return sender.Send(new UpdateCaseCommand(caseId, patchDocument), HttpContext.RequestAborted);
    }
}