﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.MailCenter.Applications.Models;
using iPlatformExtension.MailCenter.Applications.Models.Process;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.Process;


/// <summary>
/// 获取待办清单列表
/// </summary>
/// <param name="Search">搜索</param>
/// <param name="CheckedPrivateList">标签</param>
public record CollectionProcessListQuery(string? Search, [Required] List<string>? CheckedPrivateList, string? Sort, string SortType = "Acs") : PageModel, IRequest<IEnumerable<CollectionProcessListDto>>;

