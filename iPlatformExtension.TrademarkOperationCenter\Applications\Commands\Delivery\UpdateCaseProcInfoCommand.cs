﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery
{
    public record UpdateCaseProcInfoCommand(UpdateCaseProcInfoDto InfoDto) 
        : IRequest<bool>,IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;
}
