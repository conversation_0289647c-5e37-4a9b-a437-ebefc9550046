﻿using iPlatformExtension.Public.Applications.Commands.MyTrademarkCase;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.MyTrademarkCase
{
    /// <summary>
    /// 批量更新代理人状态处理者
    /// </summary>
    internal sealed class BatchUpdateAgentStatusCommandHandler(
        IFreeSql freeSql,
        IBaseSubProcStatusRepository baseSubProcStatusRepository,
        ICaseProcInfoRepository caseProcInfoRepository
    ) : IRequestHandler<BatchUpdateAgentStatusCommand>
    {
        public async Task Handle(
            BatchUpdateAgentStatusCommand request,
            CancellationToken cancellationToken
        )
        {
            var basSubProcStatus = await baseSubProcStatusRepository.GetCacheValueAsync(
                request.Status
            );
            var caseProcInfos = await caseProcInfoRepository
                .Where(it => request.ProcList.Contains(it.ProcId))
                .ToListAsync(cancellationToken);
            if (basSubProcStatus == null)
                throw new ArgumentException("Status Error");
            foreach (var it in caseProcInfos)
            {
                it.SubProcStatusId = basSubProcStatus.Code;
                if (basSubProcStatus.Code == "t_wait_sure")
                {
                    it.FeedbackDay = DateTime.Today;
                }
                if (basSubProcStatus.Code == "t_directed_office")
                {
                    it.InstructDay = DateTime.Today;
                }
                if (request.Status == "t_delay")
                {
                    it.ProcStatusId = "YQZ";
                }
                else
                {
                    it.ProcStatusId = "TCLZ";
                }
            }

            await caseProcInfoRepository.UpdateAsync(caseProcInfos, cancellationToken);
        }
    }
}
