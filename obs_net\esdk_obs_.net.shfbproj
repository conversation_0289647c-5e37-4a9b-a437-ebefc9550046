﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <!-- The configuration and platform will be used to determine which assemblies to include from solution and
				 project documentation sources -->
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{29b25de8-690e-4f48-92dc-f47b45f79f68}</ProjectGuid>
    <SHFBSchemaVersion>2017.9.26.0</SHFBSchemaVersion>
    <!-- AssemblyName, Name, and RootNamespace are not used by SHFB but Visual Studio adds them anyway -->
    <AssemblyName>Documentation</AssemblyName>
    <RootNamespace>Documentation</RootNamespace>
    <Name>Documentation</Name>
    <!-- SHFB properties -->
    <FrameworkVersion>.NET Framework 2.0</FrameworkVersion>
    <OutputPath>doc\</OutputPath>
    <HtmlHelpName>Documentation</HtmlHelpName>
    <Language>en-US</Language>
    <HelpFileFormat>Website</HelpFileFormat>
    <SyntaxFilters>C#</SyntaxFilters>
    <PresentationStyle>VS2013</PresentationStyle>
    <CleanIntermediates>True</CleanIntermediates>
    <KeepLogFile>True</KeepLogFile>
    <DisableCodeBlockComponent>False</DisableCodeBlockComponent>
    <IndentHtml>False</IndentHtml>
    <BuildAssemblerVerbosity>OnlyWarningsAndErrors</BuildAssemblerVerbosity>
    <VisibleItems>InheritedMembers, ProtectedInternalAsProtected, EditorBrowsableNever, NonBrowsable</VisibleItems>
    <DocumentationSources>
      <DocumentationSource sourceFile="bin\Release\esdk_obs_.net.dll" />
      <DocumentationSource sourceFile="bin\Release\esdk_obs_.net.xml" />
    </DocumentationSources>
    <HelpTitle>OBS SDK for .NET 3.19.7.1 API</HelpTitle>
    <HelpFileVersion>3.19.7.1</HelpFileVersion>
    <RootNamespaceContainer>False</RootNamespaceContainer>
    <NamespaceGrouping>True</NamespaceGrouping>
    <Preliminary>False</Preliminary>
    <SdkLinkTarget>Blank</SdkLinkTarget>
    <NamingMethod>MemberName</NamingMethod>
    <ContentPlacement>AboveNamespaces</ContentPlacement>
    <MissingTags>AutoDocumentCtors, AutoDocumentDispose</MissingTags>
    <RootNamespaceTitle>OBS</RootNamespaceTitle>
    <WarnOnMissingSourceContext>False</WarnOnMissingSourceContext>
    <ApiFilter>
      <Filter entryType="Namespace" fullName="OBS.Internal" isExposed="False" xmlns="" />
    </ApiFilter>
    <MaximumGroupParts>2</MaximumGroupParts>
  </PropertyGroup>
  <!-- There are no properties for these groups.  AnyCPU needs to appear in order for Visual Studio to perform
			 the build.  The others are optional common platform types that may appear. -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|Win32' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|Win32' ">
  </PropertyGroup>
  <!-- Import the SHFB build targets -->
  <Import Project="$(SHFBROOT)\SandcastleHelpFileBuilder.targets" />
  <!-- The pre-build and post-build event properties must appear *after* the targets file import in order to be
			 evaluated correctly. -->
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
    <PostBuildEvent>
    </PostBuildEvent>
    <RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
  </PropertyGroup>
</Project>