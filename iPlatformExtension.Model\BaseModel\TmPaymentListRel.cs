using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "tm_payment_list_rel", DisableSyncStructure = true)]
	public partial class TmPaymentListRel {

		[ Column(Name = "fee_id", StringLength = 50)]
		public string FeeId { get; set; }

		[ Column(Name = "list_id", StringLength = 50)]
		public string ListId { get; set; }

		[ Column(Name = "rel_id", StringLength = 50, IsNullable = false)]
		public string RelId { get; set; } = Guid.NewGuid().ToString().ToUpper();

	}

}
