﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_tag", DisableSyncStructure = true)]
	public partial class MailTag {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 标签名称
		/// </summary>
		[Column(Name = "label_name", StringLength = 50)]
		public string LabelName { get; set; }

		[Column(Name = "mail_type", StringLength = 10)]
		public string MailType { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[Column(Name = "seq", DbType = "int")]
		public int? Seq { get; set; }

		/// <summary>
		/// 用户id
		/// </summary>
		[Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
