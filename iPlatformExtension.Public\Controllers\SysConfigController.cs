﻿using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.SysConfig;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers
{
    /// <summary>
    /// 更新节假日
    /// </summary>
    /// <param name="mediator"></param>
    [Route("[controller]")]
    [ApiController]
    public class SysConfigController(IMediator mediator) : ControllerBase
    {

        /// <summary>
        /// 获取工作日
        /// </summary>
        [HttpGet("GetHoliday")]
        public Task GetHoliday([FromQuery] GetHolidayQuery query)
        {
            return mediator.Send(query);
        }

        /// <summary>
        /// 计算日期
        /// </summary>
        /// <returns></returns>
        [HttpPost("CalDate")]
        public async Task<CalDateDto> CalDate([FromBody] CalDateQuery query)
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }
    }
}
