using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ud_task_form_columns", DisableSyncStructure = true)]
	public partial class UdTaskFormColumns {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_id", StringLength = 50)]
		public string ColumnId { get; set; }

		[ Column(Name = "form_data_code", StringLength = 50)]
		public string FormDataCode { get; set; }

		[ Column(Name = "is_key_id")]
		public bool? IsKeyId { get; set; }

		[ Column(Name = "is_not_null")]
		public bool? IsNotNull { get; set; }

		[ Column(Name = "is_use")]
		public bool? IsUse { get; set; } = true;

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

		[ Column(Name = "task_form_code", StringLength = 50)]
		public string TaskFormCode { get; set; }

	}

}
