using System;
using System.Collections.Generic;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_proc_flow", DisableSyncStructure = true)]
	public partial class CaseProcFlow {

		[ Column(Name = "proc_flow_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ProcFlowId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "examine_finish_date")]
		public DateTime? ExamineFinishDate { get; set; }

		[ Column(Name = "finish_doc_ids", StringLength = 2000)]
		public string FinishDocIds { get; set; }

		[ Column(Name = "flow_cur_node_id", StringLength = 50)]
		public string FlowCurNodeId { get; set; }

		[ Column(Name = "flow_cur_user_id", StringLength = 50)]
		public string FlowCurUserId { get; set; }

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		/// <summary>
		/// 管制事项对应的流程名称
		/// </summary>
		[ Column(Name = "flow_type", StringLength = 50)]
		public string FlowType { get; set; }

		[ Column(Name = "flow_update_time")]
		public DateTime? FlowUpdateTime { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

        /// <summary>
        ///  任务信息
        /// </summary>
        [Navigate(nameof(ProcId))]
        public virtual CaseProcInfo CaseProcInfo { get; set; } = default!;

    }

}
