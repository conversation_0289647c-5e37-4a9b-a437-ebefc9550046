﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Case;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Case;

/// <summary>
/// 搜索案件信息
/// </summary>
/// <param name="Search">搜索</param>
/// <param name="CaseTypeId">案件类型</param>
/// <param name="CaseDirection">案件流向</param>
/// <param name="CustomerId">客户id</param>
/// <param name="ExcludeCaseId">排除案件id</param>
public record GetCaseInfoQuery(string? Search, string? CaseTypeId, string? CaseDirection, string? CustomerId,string? ExcludeCaseId) : PageModel, IRequest<IEnumerable<GetCaseInfoDto>>;

