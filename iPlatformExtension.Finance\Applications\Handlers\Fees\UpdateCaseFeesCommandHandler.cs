﻿using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class UpdateCaseFeesCommandHandler(ICaseFeeRepository caseFeeRepository, IMediator mediator) : IRequestHandler<UpdateCaseFeesCommand>
{
    public async Task Handle(UpdateCaseFeesCommand request, CancellationToken cancellationToken)
    {
        var documents = request.Documents;
        var feeIds = documents.Select(document => document.FeeId).ToList();

        var caseFeeLists = await caseFeeRepository.Where(list => feeIds.Contains(list.FeeId))
            .ToDictionaryAsync(list => list.FeeId, cancellationToken);

        foreach (var jsonPatchDocument in documents)
        {
            var feeId = jsonPatchDocument.FeeId;
            if (caseFeeLists.TryGetValue(feeId, out var caseFeeList))
            {
                await mediator.Send(new UpdateCaseFeeCommand(jsonPatchDocument, caseFeeList), cancellationToken);
            }
        }
    }
}