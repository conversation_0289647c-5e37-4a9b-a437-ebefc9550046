using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;

internal sealed record CreateDomesticCommissionCommand(string ProcId, CommissionWeightDto? WeightDto = null) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;