﻿{

    "Logging": {
        "LogLevel": {
            "Default": "Debug",
            "Microsoft.AspNetCore": "Warning",
            "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information",
            "Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware": "Debug",
            "IFreeSql": "Information"
        },
        "Console": {
            "FormatterOptions": {
                "TimestampFormat": "[yyyy-MM-dd HH:mm:ss]"
            }
        },
        "FreeSql": {
            "Curd": {
                "IncludeCurdTypes": [ "Select", "Delete", "Update", "Insert", "InsertOrUpdate" ],
                "Environments": [ "Local", "Development" ]
            }
        }
    },
    "NLog": {
        "autoReload": true,
        "throwConfigExceptions": true,
        "internalLogLevel": "Debug",
        "targets": {
            "async": true,
            "infoLog": {
                "type": "File",
                "encoding": "utf-8",
                "maxArchiveFiles": 7,
                "fileName": "${basedir}/logs/${shortdate}.log",
                "layout": "[${longdate}] [${uppercase:${level}}] [${logger}] [${activity:property=TraceId}] [${aspnet-request-url}] [${aspnet-mvc-action}] : ${newline}${message} ${newline}\n                ${exception:format=ToString} "
            },
            "errorLog": {
                "type": "File",
                "encoding": "utf-8",
                "maxArchiveFiles": 7,
                "fileName": "${basedir}/logs/${shortdate}-error.log",
                "layout": "[${longdate}] [${uppercase:${level}}] [${logger}] [${activity:property=TraceId}] [${aspnet-request-url}] [${aspnet-mvc-action}] : ${newline}${message} ${newline}\n                ${exception:format=ToString} "
            }
        },
        "rules": [
            {
                "logger": "*",
                "minLevel": "Info",
                "writeTo": "InfoLog",
                "filterDefaultAction": "Log",
                "filters": [
                    {
                        "action": "Ignore",
                        "type": "when",
                        "condition": "logger == 'IFreeSql' and contains(message, 'SELECT')"
                    }
                ]
            },
            {
                "logger": "*",
                "minLevel": "Error",
                "writeTo": "errorLog"
            }
        ]
    },
    "IPlatformAuth": {
        "Issuers": [
            "ipr.aciplaw.com"
        ],
        "Audiences": [
            "patas.aciplaw.com",
            "patas-test.aciplaw.com",
            "patas-dev.aciplaw.com"
        ],
        "SecurityKey": "MDBkMDVkMmVlZGI1MTQ4NDE0ZTI3ZmNiODI4MDdhMDA="
    },
    "BladeAuth": {
        "Issuer": "",
        "Audience": "iPlatformExtend",
        "SecurityKey": "bladexisapowerfulmicroservicearchitectureupgradedandoptimizedfromacommercialproject"
    },
  "ConnectionStrings": {
    "Default": "data source=***************,7433;initial catalog=acip_iplatform_1009;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true;Min Pool Size=5;Max Pool Size=100;Connection Lifetime=300",
    "mysql": "Server=***************;Port=6033;User ID=root;Password=**********;Database=mail_center_test;Connect Timeout=30;CharSet=utf8;Max pool size=200;",
    "Redis": "*************:6379,password=Acip_cc@54,defaultDatabase=4",
    "Log": "************************************************************",
    "SignalR": "http://localhost:8089/flowHub"
  }
}
