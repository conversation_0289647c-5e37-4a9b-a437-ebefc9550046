﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class InsertHistoryCommandHandler(IDeliveryHistoryRepository deliveryHistoryRepository)
    : IRequestHandler<InsertHistoryCommand, DeliHistory>
{
    public Task<DeliHistory> Handle(InsertHistoryCommand request, CancellationToken cancellationToken)
    {
        var procId = request.ProcId;
        var currentUserId = request.OperatorId;

        var history = new DeliHistory()
        {
            ProcId = procId,
            ErrorMessage = request.Message ?? string.Empty,
            Operation = request.Operation,
            StartTime = DateTime.Now,
            FinishTime = DateTime.Now,
            UserId = currentUserId,
            DisplayJsonId = request.DisplayJsonId,
            Display = request.Display
        };

        return deliveryHistoryRepository.InsertAsync(history, cancellationToken);
    }
}