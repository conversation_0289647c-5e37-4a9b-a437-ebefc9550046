﻿namespace iPlatformExtension.Public.Applications.Models.Company;


/// <summary>
/// 代理机构
/// </summary>
/// <param name="AgencyId">代理机构id</param>
/// <param name="AgencyNameCn">代理机构中文名</param>
/// <param name="AgencyNameEn">代理机构英文名</param>
/// <param name="Tel">联系电话</param>
/// <param name="Contacter">联系人</param>
/// <param name="AddressCn">中文地址</param>
/// <param name="AddressEn">英文地址</param>
public record SearchAgencyDto(string AgencyId, string AgencyNameCn, string AgencyNameEn, string Tel, string Contacter, string? AddressCn, string? AddressEn);

