﻿namespace iPlatformExtension.Common.Exceptions;

public class NotFoundException(object objectId, string objectType = "对象") : ApplicationException
{
    public override string Message => $"{objectType}[{objectId}]不存在";

    public static NotFoundException FeesNotFound(string feeId)
    {
        ArgumentNullException.ThrowIfNull(feeId);
        return new NotFoundException(feeId, "费项");
    }
    
    
}