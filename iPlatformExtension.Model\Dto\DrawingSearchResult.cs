﻿namespace iPlatformExtension.Model.Dto;

public class DrawingSearchResult
{
    public string Volume { get; set; }

    public string DrawingType { get; set; }

    public int DeliveryQuantity { get; set; }

    public DateTime ExpectedDeliveryDate { get; set; }

    public DateTime? ActualDeliveryDate { get; set; }

    public string AgentName { get; set; }

    public string AgentDepartment { get; set; }

    public DateTime CreationTime { get; set; }

    public DateTime? FinishTime { get; set; }

    public string UndertakeUserName { get; set; }
}