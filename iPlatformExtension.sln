﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31919.166
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iPlatformExtension.Finance", "iPlatformExtension.Finance\iPlatformExtension.Finance.csproj", "{09099385-B0AA-4982-ADB1-B6DA6E7C08A8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iPlatformExtension.Common", "iPlatformExtension.Common\iPlatformExtension.Common.csproj", "{F937DE7F-E43F-4B5C-8554-0E52F133D4D8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iPlatformExtension.Repository", "iPlatformExtension.Repository\iPlatformExtension.Repository.csproj", "{45BD6418-F2AF-45D0-8462-E06374994BF0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iPlatformExtension.Model", "iPlatformExtension.Model\iPlatformExtension.Model.csproj", "{FE6F0842-089C-4451-AE14-E54A15400CC9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iPlatformExtension.Service", "iPlatformExtension.Service\iPlatformExtension.Service.csproj", "{189D9FF8-D1FC-4858-8FD4-DAFE19F5DC6F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{AF6BC2A3-78B1-4105-BD58-CB0C763ADBE0}"
	ProjectSection(SolutionItems) = preProject
		docker-compose.yaml = docker-compose.yaml
		NuGet.config = NuGet.config
		show-dependency-tree.sh = show-dependency-tree.sh
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CommonTest", "CommonTest\CommonTest.csproj", "{F9A86B24-4050-4080-885D-9D698086EE30}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iPlatformExtension.TrademarkDelivery", "iPlatformExtension.TrademarkDelivery\iPlatformExtension.TrademarkDelivery.csproj", "{4EB55F7F-16D2-4B13-84EF-B229F83583FC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iPlatformExtension.TrademarkOperationCenter", "iPlatformExtension.TrademarkOperationCenter\iPlatformExtension.TrademarkOperationCenter.csproj", "{C58B3D00-4288-4E1E-81C5-F3FDDB317191}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iPlatformExtension.Public", "iPlatformExtension.Public\iPlatformExtension.Public.csproj", "{943551DB-352C-4E23-ACBE-524E32412FFB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "esdk_obs_.net_core", "obs_net\esdk_obs_.net_core.csproj", "{F51287C5-1B0B-45EA-A6F2-24A016905936}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iPlatformExtension.Gateway", "iPlatformExtension.Gateway\iPlatformExtension.Gateway.csproj", "{865EB913-64DD-4E8F-8FB6-C71219E238DA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iPlatformExtension.Mail", "iPlatformExtension.Mail\iPlatformExtension.Mail.csproj", "{ADF1FA95-CDAA-4E31-83C9-6BB89E6193E7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ConsoleScript", "ConsoleScript\ConsoleScript.csproj", "{53F297B4-EF6F-4F61-B195-1127904DAD26}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iPlatformExtension.MailCenter", "iPlatformExtension.MailCenter\iPlatformExtension.MailCenter.csproj", "{EE1A0240-6C68-47A2-BA55-84060BD8A042}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iPlatformExtension.MailService", "iPlatformExtension.MailService\iPlatformExtension.MailService.csproj", "{30ECDD8E-FA2C-4AE7-B391-760ECE273616}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iPlatformExtension.Commission", "iPlatformExtension.Commission\iPlatformExtension.Commission.csproj", "{353F7E27-08C9-4A91-A530-DA48D27A9308}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iPlatformExtension.MailCenterRepository", "iPlatformExtension.MailCenterRepository\iPlatformExtension.MailCenterRepository.csproj", "{CC0128DA-F535-4E3B-B3CD-E335BD9651BA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iPlatformExtension.Outsourcing", "iPlatformExtension.Outsourcing\iPlatformExtension.Outsourcing.csproj", "{0023B5F9-FACE-4A09-86AA-839CB01836F6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{719BCD92-99B7-4CED-89AA-31C339645D8C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{58DC8BFC-D2B3-4DE4-BF97-2F9D4A79A930}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iPlatform.Extension.Public.Test", "iPlatform.Extension.Public.Test\iPlatform.Extension.Public.Test.csproj", "{3018237B-129B-43C4-BDDB-1DF203F0D2D3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iPlatformExtension.TrademarkOperationCenter.Test", "iPlatformExtension.TrademarkOperationCenter.Test\iPlatformExtension.TrademarkOperationCenter.Test.csproj", "{AA5DB016-0B43-49BB-913B-08B19D97355E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iPlatformExtension.Outsourcing.Test", "iPlatformExtension.Outsourcing.Test\iPlatformExtension.Outsourcing.Test.csproj", "{52E1ADB2-29C6-41E2-AB07-939E24A5FF01}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iPlatformExtension.KafKa", "iPlatformExtension.KafKa\iPlatformExtension.KafKa.csproj", "{C4C292C4-0F76-4459-BE36-721258E85944}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
		test|Any CPU = test|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{09099385-B0AA-4982-ADB1-B6DA6E7C08A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{09099385-B0AA-4982-ADB1-B6DA6E7C08A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{09099385-B0AA-4982-ADB1-B6DA6E7C08A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{09099385-B0AA-4982-ADB1-B6DA6E7C08A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{09099385-B0AA-4982-ADB1-B6DA6E7C08A8}.test|Any CPU.ActiveCfg = Release|Any CPU
		{09099385-B0AA-4982-ADB1-B6DA6E7C08A8}.test|Any CPU.Build.0 = Release|Any CPU
		{F937DE7F-E43F-4B5C-8554-0E52F133D4D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F937DE7F-E43F-4B5C-8554-0E52F133D4D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F937DE7F-E43F-4B5C-8554-0E52F133D4D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F937DE7F-E43F-4B5C-8554-0E52F133D4D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{F937DE7F-E43F-4B5C-8554-0E52F133D4D8}.test|Any CPU.ActiveCfg = Release|Any CPU
		{F937DE7F-E43F-4B5C-8554-0E52F133D4D8}.test|Any CPU.Build.0 = Release|Any CPU
		{45BD6418-F2AF-45D0-8462-E06374994BF0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{45BD6418-F2AF-45D0-8462-E06374994BF0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{45BD6418-F2AF-45D0-8462-E06374994BF0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{45BD6418-F2AF-45D0-8462-E06374994BF0}.Release|Any CPU.Build.0 = Release|Any CPU
		{45BD6418-F2AF-45D0-8462-E06374994BF0}.test|Any CPU.ActiveCfg = Release|Any CPU
		{45BD6418-F2AF-45D0-8462-E06374994BF0}.test|Any CPU.Build.0 = Release|Any CPU
		{FE6F0842-089C-4451-AE14-E54A15400CC9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE6F0842-089C-4451-AE14-E54A15400CC9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE6F0842-089C-4451-AE14-E54A15400CC9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE6F0842-089C-4451-AE14-E54A15400CC9}.Release|Any CPU.Build.0 = Release|Any CPU
		{FE6F0842-089C-4451-AE14-E54A15400CC9}.test|Any CPU.ActiveCfg = Release|Any CPU
		{FE6F0842-089C-4451-AE14-E54A15400CC9}.test|Any CPU.Build.0 = Release|Any CPU
		{189D9FF8-D1FC-4858-8FD4-DAFE19F5DC6F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{189D9FF8-D1FC-4858-8FD4-DAFE19F5DC6F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{189D9FF8-D1FC-4858-8FD4-DAFE19F5DC6F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{189D9FF8-D1FC-4858-8FD4-DAFE19F5DC6F}.Release|Any CPU.Build.0 = Release|Any CPU
		{189D9FF8-D1FC-4858-8FD4-DAFE19F5DC6F}.test|Any CPU.ActiveCfg = Release|Any CPU
		{189D9FF8-D1FC-4858-8FD4-DAFE19F5DC6F}.test|Any CPU.Build.0 = Release|Any CPU
		{F9A86B24-4050-4080-885D-9D698086EE30}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F9A86B24-4050-4080-885D-9D698086EE30}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F9A86B24-4050-4080-885D-9D698086EE30}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F9A86B24-4050-4080-885D-9D698086EE30}.Release|Any CPU.Build.0 = Release|Any CPU
		{F9A86B24-4050-4080-885D-9D698086EE30}.test|Any CPU.ActiveCfg = Release|Any CPU
		{F9A86B24-4050-4080-885D-9D698086EE30}.test|Any CPU.Build.0 = Release|Any CPU
		{4EB55F7F-16D2-4B13-84EF-B229F83583FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4EB55F7F-16D2-4B13-84EF-B229F83583FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4EB55F7F-16D2-4B13-84EF-B229F83583FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4EB55F7F-16D2-4B13-84EF-B229F83583FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{4EB55F7F-16D2-4B13-84EF-B229F83583FC}.test|Any CPU.ActiveCfg = Release|Any CPU
		{4EB55F7F-16D2-4B13-84EF-B229F83583FC}.test|Any CPU.Build.0 = Release|Any CPU
		{C58B3D00-4288-4E1E-81C5-F3FDDB317191}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C58B3D00-4288-4E1E-81C5-F3FDDB317191}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C58B3D00-4288-4E1E-81C5-F3FDDB317191}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C58B3D00-4288-4E1E-81C5-F3FDDB317191}.Release|Any CPU.Build.0 = Release|Any CPU
		{C58B3D00-4288-4E1E-81C5-F3FDDB317191}.test|Any CPU.ActiveCfg = Release|Any CPU
		{C58B3D00-4288-4E1E-81C5-F3FDDB317191}.test|Any CPU.Build.0 = Release|Any CPU
		{943551DB-352C-4E23-ACBE-524E32412FFB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{943551DB-352C-4E23-ACBE-524E32412FFB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{943551DB-352C-4E23-ACBE-524E32412FFB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{943551DB-352C-4E23-ACBE-524E32412FFB}.Release|Any CPU.Build.0 = Release|Any CPU
		{943551DB-352C-4E23-ACBE-524E32412FFB}.test|Any CPU.ActiveCfg = Release|Any CPU
		{943551DB-352C-4E23-ACBE-524E32412FFB}.test|Any CPU.Build.0 = Release|Any CPU
		{F51287C5-1B0B-45EA-A6F2-24A016905936}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F51287C5-1B0B-45EA-A6F2-24A016905936}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F51287C5-1B0B-45EA-A6F2-24A016905936}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F51287C5-1B0B-45EA-A6F2-24A016905936}.Release|Any CPU.Build.0 = Release|Any CPU
		{F51287C5-1B0B-45EA-A6F2-24A016905936}.test|Any CPU.ActiveCfg = test|Any CPU
		{F51287C5-1B0B-45EA-A6F2-24A016905936}.test|Any CPU.Build.0 = test|Any CPU
		{865EB913-64DD-4E8F-8FB6-C71219E238DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{865EB913-64DD-4E8F-8FB6-C71219E238DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{865EB913-64DD-4E8F-8FB6-C71219E238DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{865EB913-64DD-4E8F-8FB6-C71219E238DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{865EB913-64DD-4E8F-8FB6-C71219E238DA}.test|Any CPU.ActiveCfg = Debug|Any CPU
		{865EB913-64DD-4E8F-8FB6-C71219E238DA}.test|Any CPU.Build.0 = Debug|Any CPU
		{ADF1FA95-CDAA-4E31-83C9-6BB89E6193E7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ADF1FA95-CDAA-4E31-83C9-6BB89E6193E7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ADF1FA95-CDAA-4E31-83C9-6BB89E6193E7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ADF1FA95-CDAA-4E31-83C9-6BB89E6193E7}.Release|Any CPU.Build.0 = Release|Any CPU
		{ADF1FA95-CDAA-4E31-83C9-6BB89E6193E7}.test|Any CPU.ActiveCfg = Debug|Any CPU
		{ADF1FA95-CDAA-4E31-83C9-6BB89E6193E7}.test|Any CPU.Build.0 = Debug|Any CPU
		{53F297B4-EF6F-4F61-B195-1127904DAD26}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{53F297B4-EF6F-4F61-B195-1127904DAD26}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{53F297B4-EF6F-4F61-B195-1127904DAD26}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{53F297B4-EF6F-4F61-B195-1127904DAD26}.Release|Any CPU.Build.0 = Release|Any CPU
		{53F297B4-EF6F-4F61-B195-1127904DAD26}.test|Any CPU.ActiveCfg = Debug|Any CPU
		{53F297B4-EF6F-4F61-B195-1127904DAD26}.test|Any CPU.Build.0 = Debug|Any CPU
		{EE1A0240-6C68-47A2-BA55-84060BD8A042}.test|Any CPU.ActiveCfg = Debug|Any CPU
		{EE1A0240-6C68-47A2-BA55-84060BD8A042}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE1A0240-6C68-47A2-BA55-84060BD8A042}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE1A0240-6C68-47A2-BA55-84060BD8A042}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE1A0240-6C68-47A2-BA55-84060BD8A042}.Release|Any CPU.Build.0 = Release|Any CPU
		{30ECDD8E-FA2C-4AE7-B391-760ECE273616}.test|Any CPU.ActiveCfg = Debug|Any CPU
		{30ECDD8E-FA2C-4AE7-B391-760ECE273616}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{30ECDD8E-FA2C-4AE7-B391-760ECE273616}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{30ECDD8E-FA2C-4AE7-B391-760ECE273616}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{30ECDD8E-FA2C-4AE7-B391-760ECE273616}.Release|Any CPU.Build.0 = Release|Any CPU
		{353F7E27-08C9-4A91-A530-DA48D27A9308}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{353F7E27-08C9-4A91-A530-DA48D27A9308}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{353F7E27-08C9-4A91-A530-DA48D27A9308}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{353F7E27-08C9-4A91-A530-DA48D27A9308}.Release|Any CPU.Build.0 = Release|Any CPU
		{353F7E27-08C9-4A91-A530-DA48D27A9308}.test|Any CPU.ActiveCfg = Debug|Any CPU
		{353F7E27-08C9-4A91-A530-DA48D27A9308}.test|Any CPU.Build.0 = Debug|Any CPU
		{0023B5F9-FACE-4A09-86AA-839CB01836F6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0023B5F9-FACE-4A09-86AA-839CB01836F6}.test|Any CPU.ActiveCfg = Debug|Any CPU
		{0023B5F9-FACE-4A09-86AA-839CB01836F6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0023B5F9-FACE-4A09-86AA-839CB01836F6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0023B5F9-FACE-4A09-86AA-839CB01836F6}.Release|Any CPU.Build.0 = Release|Any CPU
		{CC0128DA-F535-4E3B-B3CD-E335BD9651BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CC0128DA-F535-4E3B-B3CD-E335BD9651BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CC0128DA-F535-4E3B-B3CD-E335BD9651BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CC0128DA-F535-4E3B-B3CD-E335BD9651BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{CC0128DA-F535-4E3B-B3CD-E335BD9651BA}.test|Any CPU.ActiveCfg = Debug|Any CPU
		{CC0128DA-F535-4E3B-B3CD-E335BD9651BA}.test|Any CPU.Build.0 = Debug|Any CPU
		{3018237B-129B-43C4-BDDB-1DF203F0D2D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3018237B-129B-43C4-BDDB-1DF203F0D2D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3018237B-129B-43C4-BDDB-1DF203F0D2D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3018237B-129B-43C4-BDDB-1DF203F0D2D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{3018237B-129B-43C4-BDDB-1DF203F0D2D3}.test|Any CPU.ActiveCfg = Debug|Any CPU
		{3018237B-129B-43C4-BDDB-1DF203F0D2D3}.test|Any CPU.Build.0 = Debug|Any CPU
		{AA5DB016-0B43-49BB-913B-08B19D97355E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA5DB016-0B43-49BB-913B-08B19D97355E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA5DB016-0B43-49BB-913B-08B19D97355E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA5DB016-0B43-49BB-913B-08B19D97355E}.Release|Any CPU.Build.0 = Release|Any CPU
		{AA5DB016-0B43-49BB-913B-08B19D97355E}.test|Any CPU.ActiveCfg = Debug|Any CPU
		{AA5DB016-0B43-49BB-913B-08B19D97355E}.test|Any CPU.Build.0 = Debug|Any CPU
		{52E1ADB2-29C6-41E2-AB07-939E24A5FF01}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{52E1ADB2-29C6-41E2-AB07-939E24A5FF01}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{52E1ADB2-29C6-41E2-AB07-939E24A5FF01}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{52E1ADB2-29C6-41E2-AB07-939E24A5FF01}.Release|Any CPU.Build.0 = Release|Any CPU
		{52E1ADB2-29C6-41E2-AB07-939E24A5FF01}.test|Any CPU.ActiveCfg = Debug|Any CPU
		{52E1ADB2-29C6-41E2-AB07-939E24A5FF01}.test|Any CPU.Build.0 = Debug|Any CPU
		{C4C292C4-0F76-4459-BE36-721258E85944}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4C292C4-0F76-4459-BE36-721258E85944}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4C292C4-0F76-4459-BE36-721258E85944}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4C292C4-0F76-4459-BE36-721258E85944}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4C292C4-0F76-4459-BE36-721258E85944}.test|Any CPU.ActiveCfg = Debug|Any CPU
		{C4C292C4-0F76-4459-BE36-721258E85944}.test|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {741EC3FA-446B-40F3-9DD7-195F8B2AA8D3}
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F51287C5-1B0B-45EA-A6F2-24A016905936} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{353F7E27-08C9-4A91-A530-DA48D27A9308} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{F937DE7F-E43F-4B5C-8554-0E52F133D4D8} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{09099385-B0AA-4982-ADB1-B6DA6E7C08A8} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{865EB913-64DD-4E8F-8FB6-C71219E238DA} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{EE1A0240-6C68-47A2-BA55-84060BD8A042} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{ADF1FA95-CDAA-4E31-83C9-6BB89E6193E7} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{CC0128DA-F535-4E3B-B3CD-E335BD9651BA} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{30ECDD8E-FA2C-4AE7-B391-760ECE273616} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{FE6F0842-089C-4451-AE14-E54A15400CC9} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{0023B5F9-FACE-4A09-86AA-839CB01836F6} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{943551DB-352C-4E23-ACBE-524E32412FFB} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{45BD6418-F2AF-45D0-8462-E06374994BF0} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{189D9FF8-D1FC-4858-8FD4-DAFE19F5DC6F} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{4EB55F7F-16D2-4B13-84EF-B229F83583FC} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{C58B3D00-4288-4E1E-81C5-F3FDDB317191} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
		{53F297B4-EF6F-4F61-B195-1127904DAD26} = {58DC8BFC-D2B3-4DE4-BF97-2F9D4A79A930}
		{F9A86B24-4050-4080-885D-9D698086EE30} = {58DC8BFC-D2B3-4DE4-BF97-2F9D4A79A930}
		{3018237B-129B-43C4-BDDB-1DF203F0D2D3} = {58DC8BFC-D2B3-4DE4-BF97-2F9D4A79A930}
		{AA5DB016-0B43-49BB-913B-08B19D97355E} = {58DC8BFC-D2B3-4DE4-BF97-2F9D4A79A930}
		{52E1ADB2-29C6-41E2-AB07-939E24A5FF01} = {58DC8BFC-D2B3-4DE4-BF97-2F9D4A79A930}
		{C4C292C4-0F76-4459-BE36-721258E85944} = {719BCD92-99B7-4CED-89AA-31C339645D8C}
	EndGlobalSection
EndGlobal
