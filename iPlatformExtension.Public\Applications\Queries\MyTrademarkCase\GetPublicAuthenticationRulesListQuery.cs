﻿using iPlatformExtension.Public.Applications.Models;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using MediatR;

namespace iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;

/// <summary>
/// 获取公认证列表
/// </summary>
/// <param name="SearchKey">搜索词/国家、任务名称</param>
public record GetPublicAuthenticationRulesListQuery(string? SearchKey) : PageModel, IRequest<IEnumerable<GetPublicAuthenticationRulesListDto>>;

