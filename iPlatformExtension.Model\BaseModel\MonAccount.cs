using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mon_account", DisableSyncStructure = true)]
	public partial class MonAccount {

		[ Column(Name = "account_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string AccountId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "client_id", StringLength = 50)]
		public string ClientId { get; set; }

		[ Column(Name = "client_id_text", StringLength = 500)]
		public string ClientIdText { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "manage_company", StringLength = 50)]
		public string ManageCompany { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

	}

}
