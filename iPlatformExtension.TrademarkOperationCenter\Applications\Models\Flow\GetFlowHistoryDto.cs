﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow
{
    /// <summary>
    /// 任务递交流程历史
    /// </summary>
    public class GetFlowHistoryDto
    {
        public List<GetFlowHistoryList> FlowHistoryList { get; set; }

        public GetFlowActivity FlowActivity { get; set; }
    }

    public class GetFlowHistoryList
    {
        /// <summary>
        /// 历史记录ID
        /// </summary>
        public string HistoryId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public string ObjId { get; set; }

        /// <summary>
        /// 办理人员姓名
        /// </summary>
        public string AuditCnName { get; set; }

        /// <summary>
        /// 办理人员id
        /// </summary>
        public string AuditCnUserId { get; set; }

        /// <summary>
        /// 办理时间
        /// </summary>
        public DateTime? AuditTime { get; set; }

        /// <summary>
        /// 办理结果编码
        /// </summary>
        public string AuditTypeId { get; set; }

        /// <summary>
        /// 办理结果
        /// </summary>
        public string AuditTypeZhCn { get; set; }

        /// <summary>
        /// 流程阶段
        /// </summary>
        public string NameZhCn { get; set; }

        /// <summary>
        /// 流程阶段编码
        /// </summary>
        public string NodeCode { get; set; }

        /// <summary>
        /// 节点ID
        /// </summary>
        public string NodeId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }


        /// <summary>
        /// 任务ID
        /// </summary>
        public string ProcId { get; set; }

        /// <summary>
        /// 核稿任务流程id
        /// </summary>
        public string ProcFlowId { get; set; }
    }

    public class GetFlowActivity
    {
        /// <summary>
        /// 办理人员
        /// </summary>
        public string AuditCnName { get; set; }

        /// <summary>
        /// 当前节点ID
        /// </summary>
        public string CurNodeId { get; set; }

        /// <summary>
        /// 当前节点用户ID
        /// </summary>
        public string CurUserId { get; set; }

        //public string AllowEdit { get; set; }

        //public string FAllowEdit { get; set; }

        //public string FMaxSeq { get; set; }

        //public string FNext { get; set; }

        /// <summary>
        /// 流程类型
        /// </summary>
        public string FlowType { get; set; }

        /// <summary>
        /// 流程阶段
        /// </summary>
        public string NameZhCn { get; set; }

        /// <summary>
        /// 流程阶段编码
        /// </summary>
        public string NodeCode { get; set; }

        /// <summary>
        /// 上一节点提交类型
        /// </summary>
        public string PrevAuditTypeId { get; set; }

        /// <summary>
        /// 流程状态
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 更新用户
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 关联的对象id
        /// </summary>
        public string ObjectId { get; set; } = default!;

        /// <summary>
        /// 历史记录ID
        /// </summary>
        public string HistoryId { get; set; }

        /// <summary>
        /// 业务id
        /// </summary>
        public string ObjId { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? AuditTime { get; set; }

        /// <summary>
        /// 审核类型
        /// </summary>
        public string AuditTypeId { get; set; }

        /// <summary>
        /// 审核
        /// </summary>
        public string AuditTypeZhCn { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
