﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.Clients.WxWork
{
    public class WxResponseMessage
    {
        public int errcode { get; set; }
        public string errmsg { get; set; }
        public string access_token { get; set; }
        public int expires_in { get; set; }
        public DateTime OperationTime { get; set; } = DateTime.Now;
        public DateTime ExpiresTime
        {
            get
            {
                return OperationTime.AddSeconds(expires_in);
            }
        }
        public string ticket { get; set; }
        public string userid { get; set; }
    }

  
}
