﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;


    /// <summary>
    /// BillInfo对象_1，请款单(主表)
    /// </summary>
    public record BillInfoConvertDto
    {
        /// <summary>
        /// 创建人账号
        /// </summary>
        [JsonPropertyName("account")]
        public string Account { get; set; } = default!;

        /// <summary>
        /// 微调金额
        /// </summary>
        [JsonPropertyName("adjustmentAmount")]
        public decimal? AdjustmentAmount { get; set; }

        /// <summary>
        /// crmID
        /// </summary>
        public string? CrmId { get; set; }

        /// <summary>
        /// 开票备注
        /// </summary>
        public string? InvoiceRemark { get; set; }

        /// <summary>
        /// 开票抬头
        /// </summary>
        public string? InvoiceTitle { get; set; }
        
        /// <summary>
        /// 开票要求
        /// </summary>
        [JsonPropertyName("invoicingRemark")]
        public string? InvoicingRequest { get; set; }

        /// <summary>
        /// 划拨状态
        /// </summary>
        [JsonPropertyName("allotStatus")]
        public string AllotStatus { get; set; }

        /// <summary>
        /// 请款日期
        /// </summary>
        [JsonPropertyName("applyDate")]
        public DateTimeOffset? ApplyDate { get; set; }

        /// <summary>
        /// 请款审批状态
        /// </summary>
        [JsonPropertyName("approvalStatus")]
        public string ApprovalStatus { get; set; }

        /// <summary>
        /// 账单金额本位币(CNY)(选中费项)
        /// </summary>
        [JsonPropertyName("baseCurrency")]
        public string BaseCurrency { get; set; } = default!;

        /// <summary>
        /// 账单地址
        /// </summary>
        [JsonPropertyName("billAddress")]
        public string? BillAddress { get; set; }

        /// <summary>
        /// 请款费项总额
        /// </summary>
        [JsonPropertyName("billAmount")]
        public decimal? BillAmount { get; set; }

        /// <summary>
        /// 账单联系人
        /// </summary>
        [JsonPropertyName("billContactUser")]
        public string BillContactUser { get; set; } = default!;

        /// <summary>
        /// 账单联系人电话
        /// </summary>
        public string? BillContactTel { get; set; }

        /// <summary>
        /// 账单联系人邮件
        /// </summary>
        public string? BillContactEmail { get; set; }

        /// <summary>
        /// 账单联系人id
        /// </summary>
        [JsonPropertyName("billContactUserId")]
        public string BillContactUserId { get; set; } = default!;

        /// <summary>
        /// 账单币种
        /// </summary>
        [JsonPropertyName("billCurrencyType")]
        public string BillCurrencyType { get; set; } = default!;

        /// <summary>
        /// 费项记录
        /// </summary>
        [JsonPropertyName("billFeeList")]
        public IEnumerable<BillFee> BillFeeList { get; set; } = Array.Empty<BillFee>();

        /// <summary>
        /// 业务系统id
        /// </summary>
        [JsonPropertyName("billId")]
        public string BillId { get; set; }

        /// <summary>
        /// 请款单附件记录
        /// </summary>
        [JsonPropertyName("billInfoAttachList")]
        public IEnumerable<BillInfoAttach> BillInfoAttachList { get; set; } = Array.Empty<BillInfoAttach>();

        /// <summary>
        /// 账单语言
        /// </summary>
        [JsonPropertyName("billLanguage")]
        public string BillLanguage { get; set; }

        /// <summary>
        /// 请款单号
        /// </summary>
        [JsonPropertyName("billNo")]
        public string BillNo { get; set; }

        /// <summary>
        /// 账单备注
        /// </summary>
        [JsonPropertyName("billRemark")]
        public string BillRemark { get; set; }

        /// <summary>
        /// 账单发送状态
        /// </summary>
        [JsonPropertyName("billSendStatus")]
        public string BillSendStatus { get; set; }

        /// <summary>
        /// 请款单状态（草单，审核中，待发送账单，未到款，部分到款，已到款，部分坏账，整单坏账）
        /// </summary>
        [JsonPropertyName("billStatus")]
        public string BillStatus { get; set; }

        /// <summary>
        /// 账单主题
        /// </summary>
        [JsonPropertyName("billTitle")]
        public string BillTitle { get; set; }

        /// <summary>
        /// (0：正常 1：废单 2：红单)
        /// </summary>
        [JsonPropertyName("billType")]
        public string BillType { get; set; }

        /// <summary>
        /// 业务税率
        /// </summary>
        [JsonPropertyName("businessTaxRate")]
        public decimal? BusinessTaxRate { get; set; }

        /// <summary>
        /// 请款单冲销id
        /// </summary>
        [JsonPropertyName("cancelRefId")]
        public long? CancelRefId { get; set; }

        /// <summary>
        /// 请款类型(案件类型)
        /// </summary>
        [JsonPropertyName("caseType")]
        public string CaseType { get; set; }

        /// <summary>
        /// 请款类型（案件类型）Id
        /// </summary>
        [JsonPropertyName("caseTypeId")]
        public string CaseTypeId { get; set; }

        /// <summary>
        /// 应收款日期
        /// </summary>
        [JsonPropertyName("collectDate")]
        public DateTimeOffset? CollectDate { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [JsonPropertyName("createBy")]
        public string? CreateBy { get; set; }

        /// <summary>
        /// 创建人工号
        /// </summary>
        [JsonPropertyName("create_by_name")]
        public string CreateByName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonPropertyName("createTime")]
        public DateTimeOffset? CreateTime { get; set; }

        /// <summary>
        /// 业务汇率
        /// </summary>
        [JsonPropertyName("currencyRate")]
        public double? CurrencyRate { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        [JsonPropertyName("customerId")]
        public string CustomerId { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [JsonPropertyName("customerName")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户英文名
        /// </summary>
        public string CustomerNameEn { get; set; }

        /// <summary>
        /// 客户编码
        /// </summary>
        public string CustomerCode { get; set; }
        
        /// <summary>
        /// 客户传真号
        /// </summary>
        public string? Fax { get; set; }

        /// <summary>
        /// 客户企业邮箱
        /// </summary>
        [JsonPropertyName("qiyeEmail")]
        public string? Email { get; set; }

        /// <summary>
        /// 是否境外客户
        /// </summary>
        [JsonPropertyName("isCooperation")]
        public bool CustomerIsOutbound { get; set; }

        /// <summary>
        /// 部门code
        /// </summary>
        [JsonPropertyName("deptCode")]
        public string DeptCode { get; set; }

        /// <summary>
        /// 账单Id
        /// </summary>
        [JsonPropertyName("id")]
        public long? Id { get; set; }

        /// <summary>
        /// 开票方式(NEW：新开发票、LINK:关联已有发票)
        /// </summary>
        [JsonPropertyName("invoiceGenType")]
        public string InvoiceGenType { get; set; }

        /// <summary>
        /// 开票状态(待开票、开票中、已开票、不开票)
        /// </summary>
        [JsonPropertyName("invoiceStatus")]
        public string? InvoiceStatus { get; set; }

        /// <summary>
        /// 逻辑删除
        /// </summary>
        [JsonPropertyName("isDeleted")]
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// 是否即时开票(1:是， 0:否)
        /// </summary>
        [JsonPropertyName("isInstantInvoice")]
        public bool? IsInstantInvoice { get; set; }

        /// <summary>
        /// 是否需要开票（0:开票，1:不开票）
        /// </summary>
        [JsonPropertyName("isNeedInvoice")]
        public bool IsNeedInvoice { get; set; }

        /// <summary>
        /// 是否需要发送账单
        /// </summary>
        [JsonPropertyName("isNeedSend")]
        public bool? IsNeedSend { get; set; }

        /// <summary>
        /// 最新到款日期（划拨日期）
        /// </summary>
        [JsonPropertyName("lastAllotDate")]
        public DateTimeOffset? LastAllotDate { get; set; }

        /// <summary>
        /// 未到款原因
        /// </summary>
        [JsonPropertyName("notReceiveReason")]
        public string NotReceiveReason { get; set; }

        /// <summary>
        /// 未到款说明
        /// </summary>
        [JsonPropertyName("notReceiveDesc")]
        public string NotReceiveDes { get; set; }

        /// <summary>
        /// 官费总额
        /// </summary>
        [JsonPropertyName("officalfeeAmount")]
        public decimal? OfficalFeeAmount { get; set; }

        /// <summary>
        /// 请款对象id
        /// </summary>
        [JsonPropertyName("payerId")]
        public string PayerId { get; set; }

        /// <summary>
        /// 请款对象
        /// </summary>
        [JsonPropertyName("payerName")]
        public string PayerName { get; set; }

        /// <summary>
        /// 请款对象编码
        /// </summary>
        [JsonPropertyName("payerCode")]
        public string? PayerCode { get; set; }

        /// <summary>
        /// 请款对象是否境外
        /// </summary>
        [JsonPropertyName("isRequestCooperation")]
        public bool PayerIsOutbound { get; set; }

        /// <summary>
        /// 付款周期
        /// </summary>
        [JsonPropertyName("paymentTerm")]
        public string PaymentTerm { get; set; }

        /// <summary>
        /// 收款账户
        /// </summary>
        [JsonPropertyName("receivedAccount")]
        public string ReceivedAccount { get; set; }

        /// <summary>
        /// 开户行英文名
        /// </summary>
        public string? ReceiveAccountEn { get; set; }

        /// <summary>
        /// 账户中文地址
        /// </summary>
        public string? AccountAddressCn { get; set; }

        /// <summary>
        /// 帐户英文地址
        /// </summary>
        public string? AccountAddressEn { get; set; }

        /// <summary>
        /// 帐户英文名
        /// </summary>
        public string? AccountNameEn { get; set; }

        /// <summary>
        /// 到款日期
        /// </summary>
        [JsonPropertyName("receiveDate")]
        public DateTimeOffset? ReceiveDate { get; set; }

        /// <summary>
        /// 收款账户id
        /// </summary>
        [JsonPropertyName("receivedAccountId")]
        public string ReceivedAccountId { get; set; }

        /// <summary>
        /// 银行swift code
        /// </summary>
        public string? SwiftCode { get; set; }

        /// <summary>
        /// 回款异常等级
        /// </summary>
        [JsonPropertyName("receiveExceptionLevel")]
        public string ReceiveExceptionLevel { get; set; }

        /// <summary>
        /// 服务费总额
        /// </summary>
        [JsonPropertyName("servicefeeAmount")]
        public decimal? ServiceFeeAmount { get; set; }

        /// <summary>
        /// 收款主体
        /// </summary>
        [JsonPropertyName("subCompany")]
        public string SubCompany { get; set; }

        /// <summary>
        /// 收款主体英文名
        /// </summary>
        public string? SubCompanyEn { get; set; }

        /// <summary>
        /// 请款地区
        /// </summary>
        [JsonPropertyName("subCompanyArea")]
        public string SubCompanyArea { get; set; }

        /// <summary>
        /// 请款地区id
        /// </summary>
        [JsonPropertyName("subCompanyAreaId")]
        public string SubCompanyAreaId { get; set; }

        /// <summary>
        /// 收款主体id
        /// </summary>
        [JsonPropertyName("subCompanyId")]
        public string SubCompanyId { get; set; }

        /// <summary>
        /// 收款主体编码
        /// </summary>
        [JsonPropertyName("subCompanyCode")]
        public string SubCompanyCode { get; set; }

        /// <summary>
        /// 计税金额
        /// </summary>
        [JsonPropertyName("taxableAmount")]
        public decimal? TaxableAmount { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        [JsonPropertyName("taxAmount")]
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [JsonPropertyName("taxRate")]
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 第三方费总额
        /// </summary>
        [JsonPropertyName("thirdPartyfeeAmount")]
        public decimal? ThirdPartyFeeAmount { get; set; }

        /// <summary>
        /// 账单金额(选中费项)
        /// </summary>
        [JsonPropertyName("totalAmount")]
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        [JsonPropertyName("updateBy")]
        public string UpdateBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [JsonPropertyName("updateTime")]
        public DateTimeOffset? UpdateTime { get; set; }
    }

    /// <summary>
    /// BillFee对象，请款费项明细表
    /// </summary>
    public record BillFee
    {
        /// <summary>
        /// 请款单号
        /// </summary>
        [JsonIgnore]
        public string BillNo { get; set; } = default!;
        
        /// <summary>
        /// 需划拨金额
        /// </summary>
        [JsonPropertyName("allotAmount")]
        public decimal? AllotAmount { get; set; }

        /// <summary>
        /// 划拨币别
        /// </summary>
        [JsonPropertyName("allotCurrencyType")]
        public string AllotCurrencyType { get; set; }

        /// <summary>
        /// 划拨状态
        /// </summary>
        [JsonPropertyName("allotStatus")]
        public string AllotStatus { get; set; }

        /// <summary>
        /// 含税金额
        /// </summary>
        [JsonPropertyName("amountContainTax")]
        public double? AmountContainTax { get; set; }

        /// <summary>
        /// 不含税金额
        /// </summary>
        [JsonPropertyName("amountNotContainTax")]
        public double? AmountNotContainTax { get; set; }

        /// <summary>
        /// 到款日期
        /// </summary>
        [JsonPropertyName("applyDate")]
        public DateTimeOffset? ApplyDate { get; set; }

        /// <summary>
        /// 申请人中文名
        /// </summary>
        public string ApplicantCnName { get; set; } = string.Empty;

        /// <summary>
        /// 申请人英文名
        /// </summary>
        public string ApplicantEnName { get; set; } = string.Empty;

        /// <summary>
        /// 到款状态（不到款，待请款，请款中）
        /// </summary>
        [JsonPropertyName("applyStatus")]
        public string ApplyStatus { get; set; }

        /// <summary>
        /// 开票单号
        /// </summary>
        [JsonPropertyName("appNo")]
        public string AppNo { get; set; }

        /// <summary>
        /// 审核人
        /// </summary>
        [JsonPropertyName("auditBy")]
        public string AuditBy { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        [JsonPropertyName("auditTime")]
        public DateTimeOffset? AuditTime { get; set; }

        /// <summary>
        /// 请款单id
        /// </summary>
        [JsonPropertyName("billId")]
        public long? BillId { get; set; }

        /// <summary>
        /// 业务汇率
        /// </summary>
        [JsonPropertyName("businessExchangeRate")]
        public long? BusinessExchangeRate { get; set; }

        /// <summary>
        /// 案件流向
        /// </summary>
        [JsonPropertyName("caseDirection")]
        public string CaseDirection { get; set; }

        /// <summary>
        /// 案件名称
        /// </summary>
        public string CaseName { get; set; } = default!;

        /// <summary>
        /// 案件英文名
        /// </summary>
        public string? CaseNameEn { get; set; }

        /// <summary>
        /// 案件文号
        /// </summary>
        [JsonPropertyName("caseNo")]
        public string CaseNo { get; set; }

        /// <summary>
        /// 客户文号
        /// </summary>
        public string CustomerCaseNo { get; set; } = default!;

        /// <summary>
        /// 我方文号
        /// </summary>
        [JsonPropertyName("caseVolume")]
        public string CaseVolume { get; set; }

        /// <summary>
        /// 案源类型id
        /// </summary>
        public string? CaseSourceTypeId { get; set; }

        /// <summary>
        /// 案源类型名称
        /// </summary>
        public string? CaseSourceType { get; set; }

        /// <summary>
        /// 申请日
        /// </summary>
        public DateTime ApplicationDate { get; set; }

        /// <summary>
        /// 申请号
        /// </summary>
        [JsonPropertyName("applicationNo")]
        public string ApplicationNo { get; set; } = default!;

        /// <summary>
        /// 申请类型
        /// </summary>
        public string ApplyType { get; set; } = default!;

        /// <summary>
        /// 商标分类
        /// </summary>
        public string? TrademarkClass { get; set; }

        /// <summary>
        /// 商标注册号
        /// </summary>
        public string? TrademarkRegisterNo { get; set; }

        /// <summary>
        /// pct申请号
        /// </summary>
        public string? PctApplyNo { get; set; }

        /// <summary>
        /// pct国际申请日
        /// </summary>
        public DateTime? PctApplyDate { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        public string? BusinessType { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [JsonPropertyName("createBy")]
        public string CreateBy { get; set; }

        /// <summary>
        /// 优先权号集合
        /// </summary>
        [JsonPropertyName("priorityNo")]
        public string PriorityNos { get; set; } = string.Empty;

        /// <summary>
        /// 优先权申请日集合
        /// </summary>
        [JsonPropertyName("priorityDate")]
        public string PriorityApplyDates { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonPropertyName("feeCreateTime")]
        public DateTimeOffset? CreateTime { get; set; }

        /// <summary>
        /// 业务汇率
        /// </summary>
        [JsonPropertyName("currencyRate")]
        public string CurrencyRate { get; set; }

        /// <summary>
        /// 币别
        /// </summary>
        [JsonPropertyName("currencyType")]
        public string CurrencyType { get; set; }

        /// <summary>
        /// 汇率
        /// </summary>
        [JsonPropertyName("exchangeRate")]
        public decimal? ExchangeRate { get; set; }

        /// <summary>
        /// 费项金额
        /// </summary>
        [JsonPropertyName("feeAmount")]
        public decimal? FeeAmount { get; set; }
        
        /// <summary>
        /// 付款状态
        /// </summary>
        public string PayStatus { get; set; } = default!;

        /// <summary>
        /// 付款状态
        /// </summary>
        public string PaidStatusValue { get; set; } = default!;

        /// <summary>
        /// 官方缴费期限
        /// </summary>
        public DateTime? PayOfficerLegalDate { get; set; }

        /// <summary>
        /// 发明人中文名
        /// </summary>
        public string? InventorCnName { get; set; }

        /// <summary>
        /// 费项id
        /// </summary>
        [JsonPropertyName("feeId")]
        public string FeeId { get; set; }

        /// <summary>
        /// 费项名称
        /// </summary>
        [JsonPropertyName("feeName")]
        public string FeeName { get; set; }

        /// <summary>
        /// 费项名称英文
        /// </summary>
        public string? FeeNameEn { get; set; }

        /// <summary>
        /// 费项编码
        /// </summary>
        public string FeeNameCode { get; set; }

        /// <summary>
        /// 费项类型(官费，代理费，第三方费用)
        /// </summary>
        [JsonPropertyName("feeType")]
        public string FeeType { get; set; }

        [JsonPropertyName("id")]
        public long? Id { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary>
        [JsonPropertyName("invoiceAmount")]
        public decimal? InvoiceAmount { get; set; }

        /// <summary>
        /// 开票状态
        /// </summary>
        [JsonPropertyName("invoiceStatus")]
        public string InvoiceStatus { get; set; }


        /// <summary>
        /// 逻辑删除
        /// </summary>
        [JsonPropertyName("isDeleted")]
        public bool IsDeleted { get; set; }

        /// <summary>
        /// 是否开票
        /// </summary>
        [JsonPropertyName("isInvoicing")]
        public bool? IsInvoicing { get; set; }

        /// <summary>
        /// 是否计税
        /// </summary>
        [JsonPropertyName("isNeedTax")]
        public bool? IsNeedTax { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [JsonPropertyName("materialCode")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料名称(开票名称)
        /// </summary>
        [JsonPropertyName("materialName")]
        public string MaterialName { get; set; }

        /// <summary>
        /// 创收分类
        /// </summary>
        [JsonPropertyName("monDept")]
        public string MonDept { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        [JsonPropertyName("orderNo")]
        public string OrderNo { get; set; }

        /// <summary>
        /// 缴费状态
        /// </summary>
        [JsonPropertyName("paidStatus")]
        public string PaidStatus { get; set; }

        /// <summary>
        /// 缴费名义
        /// </summary>
        [JsonPropertyName("payName")]
        public string? PayName { get; set; }

        /// <summary>
        /// 划拨单id(最后划拨单)
        /// </summary>
        [JsonPropertyName("receivedAllotId")]
        public string ReceivedAllotId { get; set; }

        /// <summary>
        /// 到款日期
        /// </summary>
        public DateTimeOffset? ReceiveDate { get; set; }

        /// <summary>
        /// 任务编号
        /// </summary>
        public string? ProcNo { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string ProcName { get; set; } = default!;

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remark")]
        public string? Remark { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        [JsonPropertyName("taxAmount")]
        public double? TaxAmount { get; set; }

        /// <summary>
        /// 税项名称
        /// </summary>
        [JsonPropertyName("taxCode")]
        public string TaxCode { get; set; }

        /// <summary>
        /// 税项编码
        /// </summary>
        [JsonPropertyName("taxName")]
        public string TaxName { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [JsonPropertyName("taxRate")]
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 承办人
        /// </summary>
        public string? Undertaker { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        [JsonPropertyName("updateBy")]
        public string UpdateBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [JsonPropertyName("updateTime")]
        public DateTimeOffset? UpdateTime { get; set; }
    }

    /// <summary>
    /// BillInfoAttach对象，请款单附件表
    /// </summary>
    public partial class BillInfoAttach
    {
        /// <summary>
        /// 附件名称
        /// </summary>
        [JsonPropertyName("attachName")]
        public string AttachName { get; set; }

        /// <summary>
        /// 附件路径
        /// </summary>
        [JsonPropertyName("attachPath")]
        public string AttachPath { get; set; }

        /// <summary>
        /// 附件大小
        /// </summary>
        [JsonPropertyName("attachSize")]
        public long? AttachSize { get; set; }

        /// <summary>
        /// 附件类型
        /// </summary>
        [JsonPropertyName("attachType")]
        public string AttachType { get; set; }

        /// <summary>
        /// 请款单单号
        /// </summary>
        [JsonPropertyName("billNo")]
        public string BillNo { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [JsonPropertyName("createBy")]
        public string CreateBy { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonPropertyName("createTime")]
        public DateTimeOffset? CreateTime { get; set; }

        /// <summary>
        /// 请款单附件id
        /// </summary>
        [JsonPropertyName("id")]
        public long? Id { get; set; }

        /// <summary>
        /// oss资源编号
        /// </summary>
        [JsonPropertyName("ossCode")]
        public string OssCode { get; set; }

        /// <summary>
        /// 账单文件发送时间
        /// </summary>
        [JsonPropertyName("sendTime")]
        public DateTimeOffset? SendTime { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        [JsonPropertyName("updateBy")]
        public string? UpdateBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [JsonPropertyName("updateTime")]
        public DateTimeOffset? UpdateTime { get; set; }

        /// <summary>
        /// 账单文件生成/上传时间
        /// </summary>
        [JsonPropertyName("uploadTime")]
        public DateTimeOffset? UploadTime { get; set; }
    }

