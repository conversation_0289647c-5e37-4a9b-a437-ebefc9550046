using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;
using Confluent.Kafka;
using iPlatformExtension.Common.Extensions;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 获取流程用户
    /// </summary>
    internal sealed class GetActionUserListQueryHandler(IFreeSql<MailCenterFreeSql> freeSql, IFreeSql<PlatformFreeSql> platformFreeSql) : IRequestHandler<GetActionUserListQuery, GetActionUserListDto>
    {
        public async Task<GetActionUserListDto> Handle(GetActionUserListQuery request, CancellationToken cancellationToken)
        {
            if (request.Action == SysEnum.MailAction.Submit.ToString() || request.Action == SysEnum.MailAction.Transfer.ToString())
            {
                string? underTakeUser = request.Flow switch
                {
                    SysEnum.MailType.Receive => await freeSql.Select<MailReceiveFlow>()
                        .WithLock()
                        .Where(it => it.MailId == request.MailId)
                        .FirstAsync(it => it.UndertakeUserId, cancellationToken),
                    SysEnum.MailType.Send => await freeSql.Select<MailSendFlow>()
                        .WithLock()
                        .Where(it => it.MailId == request.MailId)
                        .FirstAsync(it => it.UndertakeUserId, cancellationToken),
                    _ => string.Empty
                };

                var node = await freeSql.Select<FlowRecord>().WithLock().Where(it =>
                        it.MailId == request.MailId && it.IsCurrent == SysEnum.MailFlowActionStatus.Enable.GetHashCode())
                    .OrderByDescending(it => it.AuditTime).FirstAsync(it => it.CurNodeId, cancellationToken) ?? MailFlowAction.Allot.ToString();
                if (underTakeUser is null)
                {
                    throw new ArgumentNullException("承办人为空");
                }
                var sysUserInfo = await platformFreeSql.Select<SysUserInfo, SysDeptInfo>().InnerJoin(it => it.t1.DeptId == it.t2.DeptId)
                    .Where(it => it.t1.UserId == underTakeUser).FirstAsync(it => new GetActionUserListDto(new { it.t1.UserId, it.t1.CnName },
                        new { it.t2.DeptId, CnName = it.t2.FullName }), cancellationToken);
                sysUserInfo.Node = request.Action == SysEnum.MailAction.Transfer.ToString()
                    ? ((MailFlowAction)Enum.Parse(typeof(MailFlowAction), node)).GetEnumIndex() ?? ""
                    : ((MailFlowAction)Enum.Parse(typeof(MailFlowAction), node)).GetEnumIndex(1) ?? "";
                return sysUserInfo;
            }
            else
            {
                var flowUser = await freeSql.Select<FlowRecord>().WithLock().Where(it => request.MailId == it.MailId && it.AuditType != SysEnum.MailAction.Reject.ToString())
                    .OrderByDescending(it => it.AuditTime)
                .FirstAsync(it => it.AuditUser, cancellationToken);
                var node = await freeSql.Select<FlowRecord>().WithLock().Where(it =>
                        it.MailId == request.MailId && it.IsCurrent == SysEnum.MailFlowActionStatus.Enable.GetHashCode())
                    .OrderByDescending(it => it.AuditTime).FirstAsync(it => it.CurNodeId, cancellationToken);
                var sysUserInfo = await platformFreeSql.Select<SysUserInfo, SysDeptInfo>()
                    .InnerJoin(it => it.t1.DeptId == it.t2.DeptId)
                    .Where(it => it.t1.UserId == flowUser).FirstAsync(
                        it => new GetActionUserListDto(new { it.t1.UserId, it.t1.CnName },
                            new { it.t2.DeptId, CnName = it.t2.FullName }), cancellationToken);
                sysUserInfo.Node = node == null ? "没有流程" : ((MailFlowAction)Enum.Parse(typeof(MailFlowAction), node)).GetEnumIndex(-1) ?? "";

                return sysUserInfo;
            }
        }
    }
}

