using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_case_user_type", DisableSyncStructure = true)]
	public partial class SysCaseUserType {

		[ Column(Name = "user_type", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string UserType { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "case_type", StringLength = 20)]
		public string CaseType { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "type_en_us", StringLength = 50)]
		public string TypeEnUs { get; set; }

		[ Column(Name = "type_ja_jp", StringLength = 50)]
		public string TypeJaJp { get; set; }

		[ Column(Name = "type_zh_cn", StringLength = 50)]
		public string TypeZhCn { get; set; }

	}

}
