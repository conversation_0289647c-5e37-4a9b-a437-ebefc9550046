﻿namespace iPlatformExtension.Model.Dto.Customer;

/// <summary>
/// 联系人id键
/// </summary>
/// <param name="ContactId">联系人id</param>
public record struct ContactTypeIdKey(string ContactId)
{
    /// <summary>
    /// 隐式转换为字符串
    /// </summary>
    /// <param name="key">联系人id键</param>
    /// <returns>联系人id值</returns>
    public static implicit operator string(ContactTypeIdKey key) => key.ContactId;
}