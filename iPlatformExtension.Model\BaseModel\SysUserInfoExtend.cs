using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_user_info_extend", DisableSyncStructure = true)]
	public partial class SysUserInfoExtend {

		[ Column(Name = "user_extend_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string UserExtendId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "bachelor_academy", StringLength = 100)]
		public string BachelorAcademy { get; set; }

		[ Column(Name = "bachelor_majors", StringLength = 100)]
		public string BachelorMajors { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "cn_grade", StringLength = 50)]
		public string CnGrade { get; set; }

		[ Column(Name = "cn_to_en_grade", StringLength = 50)]
		public string CnToEnGrade { get; set; }

		[ Column(Name = "cn_to_ja_grade", StringLength = 50)]
		public string CnToJaGrade { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "doctor_academy", StringLength = 100)]
		public string DoctorAcademy { get; set; }

		[ Column(Name = "doctor_majors", StringLength = 100)]
		public string DoctorMajors { get; set; }

		[ Column(Name = "en_grade", StringLength = 50)]
		public string EnGrade { get; set; }

		[ Column(Name = "en_oa_grade", StringLength = 50)]
		public string EnOaGrade { get; set; }

		[ Column(Name = "en_to_cn_grade", StringLength = 50)]
		public string EnToCnGrade { get; set; }

		[ Column(Name = "en_translate_grade", StringLength = 50)]
		public string EnTranslateGrade { get; set; }

		[ Column(Name = "english_grade", StringLength = 50)]
		public string EnglishGrade { get; set; }

		[ Column(Name = "has_agent_qualification")]
		public bool? HasAgentQualification { get; set; }

		[ Column(Name = "has_lawyer_competence")]
		public bool? HasLawyerCompetence { get; set; }

		[ Column(Name = "io_draft_grade", StringLength = 50)]
		public string IoDraftGrade { get; set; }

		[ Column(Name = "io_oa_grade", StringLength = 50)]
		public string IoOaGrade { get; set; }

		[ Column(Name = "io_translate_grade", StringLength = 50)]
		public string IoTranslateGrade { get; set; }

		[ Column(Name = "is_lawyer")]
		public bool? IsLawyer { get; set; }

		[ Column(Name = "is_patent_agent")]
		public bool? IsPatentAgent { get; set; }

		[ Column(Name = "ja_to_cn_grade", StringLength = 50)]
		public string JaToCnGrade { get; set; }

		[ Column(Name = "lawyer_no", StringLength = 50)]
		public string LawyerNo { get; set; }

		[ Column(Name = "main_tech_field", StringLength = 50)]
		public string MainTechField { get; set; }

		[ Column(Name = "master_academy", StringLength = 100)]
		public string MasterAcademy { get; set; }

		[ Column(Name = "master_majors", StringLength = 100)]
		public string MasterMajors { get; set; }

		[ Column(Name = "oi_draft_grade", StringLength = 50)]
		public string OiDraftGrade { get; set; }

		[ Column(Name = "oi_oa_grade", StringLength = 50)]
		public string OiOaGrade { get; set; }

		[ Column(Name = "oi_translate_grade", StringLength = 50)]
		public string OiTranslateGrade { get; set; }

		[ Column(Name = "patent_agent_no", StringLength = 50)]
		public string PatentAgentNo { get; set; }

		[ Column(Name = "patent_start_date")]
		public DateTime? PatentStartDate { get; set; }

		[ Column(Name = "plan_customers", StringLength = 500)]
		public string PlanCustomers { get; set; }

		[ Column(Name = "previous_customers", StringLength = 500)]
		public string PreviousCustomers { get; set; }

		[ Column(Name = "resume", StringLength = 4000)]
		public string Resume { get; set; }

		[ Column(Name = "specialty", StringLength = 500)]
		public string Specialty { get; set; }

		[ Column(Name = "tech_work_years")]
		public int? TechWorkYears { get; set; }

		[ Column(Name = "tm_grade", StringLength = 50)]
		public string TmGrade { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

	}

}
