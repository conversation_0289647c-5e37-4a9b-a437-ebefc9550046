using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_city", DisableSyncStructure = true)]
	public partial class BasCity {

		[ Column(Name = "code")]
		public string Code { get; set; }

		[ Column(Name = "level")]
		public string Level { get; set; }

		[ Column(Name = "name")]
		public string Name { get; set; }

		[ Column(Name = "parent_code")]
		public string ParentCode { get; set; }

		[ Column(Name = "pinyin")]
		public string Pinyin { get; set; }

		[ Column(Name = "postcode")]
		public string Postcode { get; set; }

		[ Column(Name = "short_name")]
		public string ShortName { get; set; }

		[ Column(Name = "tel")]
		public string Tel { get; set; }

	}

}
