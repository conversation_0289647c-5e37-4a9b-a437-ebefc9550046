using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "express_info", DisableSyncStructure = true)]
	public partial class ExpressInfo {

		[ Column(Name = "express_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ExpressId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "alert_date")]
		public DateTime? AlertDate { get; set; }

		[ Column(Name = "confirm_date")]
		public DateTime? ConfirmDate { get; set; }

		[ Column(Name = "courier_company", StringLength = 50)]
		public string CourierCompany { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "express_fee", DbType = "money")]
		public decimal? ExpressFee { get; set; }

		[ Column(Name = "express_no", StringLength = 50)]
		public string ExpressNo { get; set; }

		[ Column(Name = "express_status", StringLength = 50)]
		public string ExpressStatus { get; set; }

		[ Column(Name = "flow_cur_user_id", StringLength = 50)]
		public string FlowCurUserId { get; set; }

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; } = "0";

		[ Column(Name = "flow_update_time")]
		public DateTime? FlowUpdateTime { get; set; }

		[ Column(Name = "is_auto_send")]
		public bool? IsAutoSend { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_monitor_logistics")]
		public bool IsMonitorLogistics { get; set; } = false;

		[ Column(Name = "is_tailing")]
		public bool? IsTailing { get; set; }

		[ Column(Name = "item_information", StringLength = 50)]
		public string ItemInformation { get; set; }

		[ Column(Name = "mail_status", StringLength = 50)]
		public string MailStatus { get; set; }

		[ Column(Name = "monitor_logistics_time")]
		public DateTime? MonitorLogisticsTime { get; set; }

		[ Column(Name = "pay_way", StringLength = 50)]
		public string PayWay { get; set; }

		[ Column(Name = "print_template", StringLength = -2)]
		public string PrintTemplate { get; set; }

		[ Column(Name = "receive_time")]
		public DateTime? ReceiveTime { get; set; }

		[ Column(Name = "recipients_address", StringLength = 50)]
		public string RecipientsAddress { get; set; }

		[ Column(Name = "recipients_address_detail", StringLength = 400)]
		public string RecipientsAddressDetail { get; set; }

		[ Column(Name = "recipients_area", StringLength = 50)]
		public string RecipientsArea { get; set; }

		[ Column(Name = "recipients_city", StringLength = 50)]
		public string RecipientsCity { get; set; }

		[ Column(Name = "recipients_company", StringLength = 100)]
		public string RecipientsCompany { get; set; }

		[ Column(Name = "recipients_country_id", StringLength = 50)]
		public string RecipientsCountryId { get; set; }

		[ Column(Name = "recipients_full_address", StringLength = 500)]
		public string RecipientsFullAddress { get; set; }

		[ Column(Name = "recipients_mobile", StringLength = 50)]
		public string RecipientsMobile { get; set; }

		[ Column(Name = "recipients_name", StringLength = 50)]
		public string RecipientsName { get; set; }

		[ Column(Name = "recipients_postcode", StringLength = 50)]
		public string RecipientsPostcode { get; set; }

		[ Column(Name = "recipients_province", StringLength = 50)]
		public string RecipientsProvince { get; set; }

		[ Column(Name = "recipients_tel", StringLength = 50)]
		public string RecipientsTel { get; set; }

		[ Column(Name = "recipients_type", StringLength = 50)]
		public string RecipientsType { get; set; }

		[ Column(Name = "recipients_user_id", StringLength = 50)]
		public string RecipientsUserId { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "scan_user", StringLength = 50)]
		public string ScanUser { get; set; }

		[ Column(Name = "send_address", StringLength = 50)]
		public string SendAddress { get; set; }

		[ Column(Name = "send_address_detail", StringLength = 400)]
		public string SendAddressDetail { get; set; }

		[ Column(Name = "send_area", StringLength = 50)]
		public string SendArea { get; set; }

		[ Column(Name = "send_city", StringLength = 50)]
		public string SendCity { get; set; }

		[ Column(Name = "send_company", StringLength = 50)]
		public string SendCompany { get; set; }

		[ Column(Name = "send_country_id", StringLength = 50)]
		public string SendCountryId { get; set; }

		[ Column(Name = "send_full_address", StringLength = 500)]
		public string SendFullAddress { get; set; }

		[ Column(Name = "send_mobile", StringLength = 50)]
		public string SendMobile { get; set; }

		[ Column(Name = "send_name", StringLength = 50)]
		public string SendName { get; set; }

		[ Column(Name = "send_postcode", StringLength = 50)]
		public string SendPostcode { get; set; }

		[ Column(Name = "send_province", StringLength = 50)]
		public string SendProvince { get; set; }

		[ Column(Name = "send_tel", StringLength = 50)]
		public string SendTel { get; set; }

		[ Column(Name = "send_user_id", StringLength = 50)]
		public string SendUserId { get; set; }

		[ Column(Name = "trail_user_id", StringLength = 50)]
		public string TrailUserId { get; set; }

		[ Column(Name = "trailsman", StringLength = 50)]
		public string Trailsman { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "waybill_no", StringLength = 50)]
		public string WaybillNo { get; set; }

		[ Column(Name = "weight", DbType = "money")]
		public decimal? Weight { get; set; }

	}

}
