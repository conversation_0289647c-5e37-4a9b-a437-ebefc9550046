﻿using System.Linq.Expressions;

namespace iPlatformExtension.Public.Applications.Models.DateCalculation;

/// <summary>
/// 表达式拼接
/// </summary>
public static class DateSettingExtend
{
    /// <summary>
    /// 拼接对比表达式
    /// </summary>
    /// <param name="dateSettings"></param>
    /// <param name="calibrationDate"></param>
    /// <returns></returns>
    public static List<DateSetting> ExpressionCompile(this IEnumerable<DateSetting> dateSettings, int calibrationDate)
    {
        List<DateSetting> settings = [];
        foreach (var dateSetting in dateSettings)
        {
            var max = true;
            if (dateSetting.MaxValue.HasValue)
            {
                max = dateSetting.MaxValue >= calibrationDate;
            }

            var min = true;
            if (dateSetting.MinValue.HasValue)
            {
                min = dateSetting.MinValue < calibrationDate;
            }

            if (min && max)
            {
                settings.Add(dateSetting);
            }
        }

        return settings;
    }
}