﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;

namespace ConsoleScript;

public class SyncPatentForeignSupplierTask
{
    private readonly IFreeSql _freeSql;

    private readonly ILogger _logger;

    public SyncPatentForeignSupplierTask()
    {
        var services = new ServiceCollection();
        services.AddFreeSql<PlatformFreeSql>(options =>
        {
            options.ConnectionString = "data source=***************,7433;initial catalog=acip_iplatform_1009;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true";
            options.DbType = DataType.SqlServer;
            options.CommandTimeout = TimeSpan.FromSeconds(10);
        }).ConfigureGlobal((freeSql, provider) =>
        {
            var logger = provider.GetRequiredService<ILogger<SyncPatentForeignSupplierTask>>();
            freeSql.Aop.CurdAfter += (_, args) =>
            {
                logger.LogInformation(args.Sql);
            };
        });

        services.AddLogging(builder =>
            builder.AddSimpleConsole(options => options.ColorBehavior = LoggerColorBehavior.Enabled));
        
        services.AddStringBuilderPool();
        
        services.AddDataService();
        
        var serviceProvider = services.BuildServiceProvider();
        
        
        _freeSql = serviceProvider.GetRequiredService<IFreeSql<PlatformFreeSql>>();
        _logger = serviceProvider.GetRequiredService<ILogger<SyncPatentForeignSupplierTask>>();
    }
    
    public async Task RunAsync()
    {
        
        
        try
        {
            _logger.LogInformation("开始执行同步专利任务境外代理信息脚本");

            // 1. 查询所有流向为内外或外外并且任务名称是否委外为是的所有境外代理的专利任务
            var tasks = await QueryEligibleTasksAsync(_freeSql);
            _logger.LogInformation($"找到符合条件的任务数量: {tasks.Count}");

            // 2. 处理每个任务
            int successCount = 0;
            int skipCount = 0;
            int errorCount = 0;
            int contactSyncCount = 0;

            foreach (var task in tasks)
            {
                try
                {
                    // 获取任务对应的案件信息
                    var caseInfo = await _freeSql.Select<CaseInfo>()
                        .Where(c => c.Id == task.CaseId)
                        .ToOneAsync();

                    if (caseInfo == null)
                    {
                        _logger.LogWarning($"任务ID: {task.ProcId}, 找不到对应的案件信息");
                        skipCount++;
                        continue;
                    }

                    // 检查案件上的境外代理是否为空
                    if (string.IsNullOrWhiteSpace(caseInfo.ForeginAgencyId))
                    {
                        _logger.LogInformation($"任务ID: {task.ProcId}, 案件ID: {caseInfo.Id}, 案件上的境外代理为空，跳过");
                        skipCount++;
                        continue;
                    }

                    // 更新任务的境外代理信息
                    bool updated = false;
                    var procUpdate = _freeSql.Update<CaseProcInfo>(task.ProcId);
                    
                    // 只有当任务上的境外代理为空时才更新
                    if (string.IsNullOrWhiteSpace(task.ForeginAgencyId))
                    {
                        task.ForeginAgencyId = caseInfo.ForeginAgencyId;
                        procUpdate.Set(procInfo => procInfo.ForeginAgencyId, caseInfo.ForeginAgencyId);
                        updated = true;
                    }
                    
                    // 只有当任务上的外所文号为空时才更新
                    if (string.IsNullOrWhiteSpace(task.ForeignNumber))
                    {
                        task.ForeignNumber = caseInfo.ForeginCaseNo ?? string.Empty;
                        procUpdate.Set(procInfo => procInfo.ForeignNumber, task.ForeignNumber);
                        updated = true;
                    }
                    
                    // 只有当任务上的选所备注为空时才更新
                    if (string.IsNullOrWhiteSpace(task.ForeignSupplierRemark))
                    {
                        task.ForeignSupplierRemark = caseInfo.ForeignSupplierRemark;
                        procUpdate.Set(procInfo => procInfo.ForeignSupplierRemark, task.ForeignSupplierRemark);
                        updated = true;
                    }

                    if (updated)
                    {


                        // 更新任务信息
                        procUpdate.Set(procInfo => procInfo.UpdateTime, DateTime.Now);

                        
                        // 保存到数据库
                        await procUpdate.Where(p => string.IsNullOrEmpty(p.ForeginAgencyId))
                            // .Set(c => c.ForeginAgencyId, task.ForeginAgencyId)
                            // .Set(c => c.ForeignNumber, task.ForeignNumber)
                            // .Set(c => c.ForeignSupplierRemark, task.ForeignSupplierRemark)
                            // .Set(c => c.UpdateTime, task.UpdateTime)
                            // .Where(c => c.ProcId == task.ProcId)
                            .ExecuteAffrowsAsync();
                        
                        _logger.LogInformation($"成功更新任务ID: {task.ProcId}, 案件ID: {caseInfo.Id}, 境外代理ID: {task.ForeginAgencyId}, 外所文号: {task.ForeignNumber}");
                        successCount++;
                    }
                    else
                    {
                        _logger.LogInformation($"任务ID: {task.ProcId}, 案件ID: {caseInfo.Id}, 任务已有境外代理信息，无需更新");
                        skipCount++;
                    }
                    
                    // 同步案件境外代理联系人到任务境外代理联系人
                    if (!string.IsNullOrWhiteSpace(task.ForeginAgencyId))
                    {
                        bool contactUpdated = await SyncCaseContactsToTaskAsync(task.ProcId, caseInfo.Id);
                        if (contactUpdated)
                        {
                            contactSyncCount++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"处理任务ID: {task.ProcId} 时发生错误");
                    errorCount++;
                }
            }

            _logger.LogInformation($"脚本执行完成。成功更新: {successCount}, 联系人同步: {contactSyncCount}, 跳过: {skipCount}, 错误: {errorCount}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行同步专利任务境外代理信息脚本时发生错误");
            throw;
        }
    }
    
    
    /// <summary>
    /// 同步案件境外代理联系人到任务境外代理联系人（全部覆盖）
    /// </summary>
    private async Task<bool> SyncCaseContactsToTaskAsync(string procId, string caseId)
    {
        try
        {
            // 获取案件的境外代理联系人
            var caseContacts = await _freeSql.Select<CaseForeignContactList>()
                .Where(c => c.CaseId == caseId)
                .ToListAsync();

            // 获取任务已有的境外代理联系人
            var existingContacts = await _freeSql.Select<ProcForeignSupplierContact>()
                .Where(c => c.ProcId == procId)
                .ToListAsync();

            // 删除任务上所有现有的联系人
            if (existingContacts.Count > 0)
            {
                await _freeSql.Delete<ProcForeignSupplierContact>()
                    .Where(c => c.ProcId == procId)
                    .ExecuteAffrowsAsync();
                
                _logger.LogInformation($"已删除任务ID: {procId} 上的 {existingContacts.Count} 个现有联系人");
            }

            // 如果案件没有联系人，则结束
            if (caseContacts == null || caseContacts.Count == 0)
            {
                _logger.LogInformation($"案件ID: {caseId} 没有境外代理联系人，任务联系人已清空");
                return true;
            }

            // 添加案件上的所有联系人到任务
            var newContacts = caseContacts.Select(caseContact => new ProcForeignSupplierContact
            {
                ContactId = caseContact.ContactId,
                ProcId = procId,
                CreationTime = DateTime.Now,
                UpdateTime = DateTime.Now,
                Updater = UserIds.Administrator,
                Representative = false
            }).ToList();

            if (newContacts.Count > 0)
            {
                await _freeSql.Insert<ProcForeignSupplierContact>()
                    .AppendData(newContacts)
                    .ExecuteAffrowsAsync();
                
                _logger.LogInformation($"成功为任务ID: {procId} 添加 {newContacts.Count} 个境外代理联系人");
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"同步案件联系人到任务时发生错误，任务ID: {procId}, 案件ID: {caseId}");
            return false;
        }
    }

    /// <summary>
    /// 查询符合条件的专利任务
    /// </summary>
    private async Task<List<CaseProcInfo>> QueryEligibleTasksAsync(IFreeSql freeSql)
    {
        // 查询所有流向为内外或外外并且任务名称是否委外为是的所有境外代理的专利任务
        var tasks = await freeSql.Select<CaseProcInfo, BasCtrlProc, CaseInfo>().WithLock()
            .LeftJoin((p, c, d) => p.CtrlProcId == c.CtrlProcId)
            .LeftJoin((p, c, d) => p.CaseId == d.Id)
            .Where((p, c, d) => 
                !string.IsNullOrEmpty(d.ForeginAgencyId) &&
                
                string.IsNullOrEmpty(p.ForeginAgencyId) &&
                // 任务有效
                p.IsEnabled == true &&
                // 案件类型为专利
                d.CaseTypeId == "P" &&
                // 是否委外为是
                c.IsOutsourcing == true &&
                // 流向为内外或外外
                (d.CaseDirection == "IO" || d.CaseDirection == "OO"))
            .ToListAsync((p, c, d) => new CaseProcInfo()
            {
                ProcId = p.ProcId,
                CaseId = p.CaseId,
                ForeginAgencyId = p.ForeginAgencyId,
                ForeignNumber = p.ForeignNumber,
                ForeignSupplierRemark = p.ForeignSupplierRemark
            });

        return tasks;
    }

        // /// <summary>
        // /// 脚本入口方法
        // /// </summary>
        // public static async Task Main(IServiceProvider serviceProvider)
        // {
        //     var script = new SyncPatentForeignSupplierTask(serviceProvider);
        //     await script.ExecuteAsync();
        // }
    
}