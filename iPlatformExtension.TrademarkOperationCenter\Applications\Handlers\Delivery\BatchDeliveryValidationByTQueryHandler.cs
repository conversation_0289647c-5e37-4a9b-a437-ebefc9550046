﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class BatchDeliveryValidationByTQueryHandler(IFreeSql freeSql)
    : IRequestHandler<BatchDeliveryValidationByTQuery, IEnumerable<DeliveryValidationDto>>
{
    public async Task<IEnumerable<DeliveryValidationDto>> Handle(BatchDeliveryValidationByTQuery request, CancellationToken cancellationToken)
    {
        var procIds = request.ProcIds;

        var deliveryList = await freeSql.Select<DeliInfo, CaseProcInfo, SysFlowActivity, SysFlowNode>().WithLock()
            .LeftJoin((info, procInfo, activity, node) => info.ProcId == procInfo.ProcId)
            .LeftJoin((info, procInfo, activity, node) => info.ProcId == activity.ObjId && activity.FlowType == FlowType.Delivery)
            .LeftJoin((info, procInfo, activity, node) => activity.CurNodeId == node.NodeId)
            .Where((info, procInfo, activity, node) => procIds.Contains(info.ProcId))
            .WhereIf(request.Started, (info, procInfo, activity, node) => activity.FlowType == FlowType.Delivery &&
                                                                          activity.FlowSubType == request.FlowSubType)
            .ToListAsync((info, procInfo, activity, node) =>
                new DeliveryValidationDto
                {
                    FlowId = activity.FlowId,
                    ProcId = info.ProcId,
                    ProcNo = string.IsNullOrEmpty(info.ProcNo) ? procInfo.ProcNo : info.ProcNo,
                    FlowStatus = activity.Status,
                    CurrentNodeId = activity.CurNodeId,
                    CurrentUserId = activity.CurUserId,
                    DeliveryStatus = info.Status!.Value,
                    OperationResult = info.OperationResult,
                    Version = info.Version,
                    CtrlProcId = info.CtrlProcId,
                    CurrentNodeCode = node.NodeCode,
                    FlowType = activity.FlowType,
                    FlowSubType = activity.FlowSubType,
                    DeliveryFlowSubType = info.FlowSubType,
                    IsAuto = info.IsAuto
                }, cancellationToken);

        if (request.Started && deliveryList.Count == 0)
        {
            throw new ApplicationException("选中的数据没有启动提交到流程部流程");
        }

        return deliveryList;
    }
}