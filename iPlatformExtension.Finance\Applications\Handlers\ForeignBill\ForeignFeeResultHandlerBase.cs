﻿using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Finance.Applications.Queries.ForeignBill;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal abstract class ForeignFeeResultHandlerBase : INotificationHandler<ForeignBillFeeResultNotification>
{
    public async Task Handle(ForeignBillFeeResultNotification notification, CancellationToken cancellationToken)
    {
        var results = notification.Results;
        foreach (var dto in results)
        {
            await GetResultAsync(dto);
        }
    }

    protected abstract ValueTask GetResultAsync(ForeignBillFeeExportDto dto);
}