﻿
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;

/// <summary>
/// 新增任务申请人
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="ChangeType">递交业务类型</param>
/// <param name="ChangeTypeName">递交业务类型名称</param>
/// <param name="TypeId">申请人类型id</param>
/// <param name="ApplicantId">原来的申请人id</param>
/// <param name="ApplicantNameCn">申请人中文名</param>
/// <param name="ApplicantNameEn">申请人英文名</param>
/// <param name="AddressCn">申请人中文地址</param>
/// <param name="AddressEn">申请人英文地址</param>
/// <param name="CountryId">国家编码</param>
/// <param name="IsChineseIdentity">身份证明文件是否为中文</param>
/// <param name="PostCode">邮编</param>
/// <param name="CertificationType">证件类型</param>
/// <param name="CertificationNumber">证件号码</param>
/// <param name="FileList">附件</param>
public record InsertProcApplicantCommand(string ProcId, string ChangeType, string? ChangeTypeName, string? TypeId, string? ApplicantId, string? ApplicantNameCn, string? ApplicantNameEn, string? AddressCn, string? AddressEn, string? CountryId , bool IsChineseIdentity, string? PostCode, string? CertificationType, string? CertificationNumber, IList<ApplicantFileDto> FileList) :IRequest;


