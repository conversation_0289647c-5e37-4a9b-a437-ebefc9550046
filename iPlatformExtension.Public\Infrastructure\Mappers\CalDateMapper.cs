﻿using AutoMapper;
using iPlatformExtension.Public.Applications.Models.DateCalculation;

namespace iPlatformExtension.Public.Infrastructure.Mappers
{
    /// <summary>
    /// 日期计算mapper
    /// </summary>
    public sealed class CalDateMapper : Profile
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public CalDateMapper()
        {
            CreateMap<DateCalDto, DateCalDto>().ForMember(info => info.ValidDate, options =>
            {
                options.Condition(dto => dto.ValidDate is not null);
                options.MapFrom(dto => dto.ValidDate);
            }).ForMember(info => info.LegalDueDate, options =>
            {
                options.Condition(dto => dto.LegalDueDate is not null);
                options.MapFrom(dto => dto.LegalDueDate);
            }).ForMember(info => info.CtrlProcId, options =>
            {
                options.Condition(dto => dto.CtrlProcId is not null);
                options.MapFrom(dto => dto.CtrlProcId);
            }).ForMember(info => info.CountryId, options =>
            {
                options.Condition(dto => dto.CountryId is not null);
                options.MapFrom(dto => dto.CountryId);
            }).ForMember(info => info.ExtensionPeriod, options =>
            {
                options.Condition(dto => dto.ExtensionPeriod is not null);
                options.MapFrom(dto => dto.ExtensionPeriod);
            }).ForMember(info => info.EntrustDate, options =>
            {
                options.Condition(dto => dto.EntrustDate is not null);
                options.MapFrom(dto => dto.EntrustDate);
            }).ForMember(info => info.ReceiveDate, options =>
            {
                options.Condition(dto => dto.ReceiveDate is not null);
                options.MapFrom(dto => dto.ReceiveDate);
            });
        }
    }
}
