﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto
{
    /// <summary>
    /// 客户数据返回实体类
    /// </summary>
    public class CustomerReDto
    {
        /// <summary>
        /// 客户id
        /// </summary>
        public string CustomerId { get; set; } = null!;

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; } = string.Empty;

        /// <summary>
        /// 客户名称（英文）
        /// </summary>
        public string? CustomerNameEn { get; set; }

        /// <summary>
        /// CRM客户编码
        /// </summary>
        public string? CrmCustomerCode { get; set; }

        /// <summary>
        /// CRM客户id
        /// </summary>
        public string? CrmCustomerId { get; set; }

        /// <summary>
        /// 案源地区
        /// </summary>
        public string? District { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>
        public bool? IsEnabled { get; set; }

        /// <summary>
        /// 商务
        /// </summary>
        public string? BusiUserId { get; set; }

        /// <summary>
        /// 商务工号
        /// </summary>
        public string? BusiUserNum { get; set; }

        /// <summary>
        /// 商务姓名
        /// </summary>
        public string? BusiUserName { get; set; }

        /// <summary>
        /// 是否境外代理
        /// </summary>
        public bool? IsCooperation { get; set; }

        /// <summary>
        /// 客户企业邮箱
        /// </summary>
        public string? Email { get; set; }
    }
}
