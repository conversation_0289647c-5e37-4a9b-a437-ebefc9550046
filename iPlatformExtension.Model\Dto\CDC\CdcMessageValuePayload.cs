﻿using System.Text.Json;

namespace iPlatformExtension.Model.Dto.CDC;

/// <summary>
/// cdc消息负载
/// </summary>
/// <typeparam name="T">负载类型</typeparam>
public class CdcMessageValuePayload<T>
{
    /// <summary>
    /// 更新前内容
    /// </summary>
    public T? Before { get; set; }

    /// <summary>
    /// 更新后的内容
    /// </summary>
    public T? After { get; set; }
    
    /// <summary>
    /// 操作标识
    /// </summary>
    public string Op { get; set; } = string.Empty;

    /// <summary>
    /// 触发时间
    /// </summary>
    public long TsMs { get; set; }

    /// <summary>
    /// 事件源信息
    /// </summary>
    public CdcMessagePayloadSource Source { get; set; } = null!;
}

/// <summary>
/// 通用消息负载
/// </summary>
public class CdcMessageValuePayload : CdcMessageValuePayload<JsonDocument>
{
}