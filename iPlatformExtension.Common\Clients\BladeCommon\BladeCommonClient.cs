﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.ServiceDiscovery.Nacos;
using iPlatformExtension.Model.Dto;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Clients.BladeCommon;

public sealed class BladeCommonClient
{
    private readonly HttpClient _httpClient;

    private readonly IOptionsMonitor<BladeCommonClientOptions> _clientOptions;

    private readonly IHostEnvironment _hostEnvironment;

    public BladeCommonClient(
        HttpClient httpClient, 
        IOptionsMonitor<BladeCommonClientOptions> optionsMonitor,
        IHostEnvironment hostEnvironment)
    {
        _httpClient = httpClient;
        _clientOptions = optionsMonitor;
        _hostEnvironment = hostEnvironment;

        var clientOptions = optionsMonitor.CurrentValue;
        if (hostEnvironment.IsLocal())
        {
            _httpClient.BaseAddress = new Uri(clientOptions.HostAddress);
        }
        else
        {
            // var namingService = serviceProvider.GetRequiredService<INacosNamingService>();
            // var instanceInfo = namingService
            //     .SelectOneHealthyInstance(clientOptions.ServiceName, clientOptions.GroupName).ConfigureAwait(false)
            //     .GetAwaiter().GetResult();
            //
            // ArgumentNullException.ThrowIfNull(instanceInfo);
            //
            // string uri;
            // var ipAddress = instanceInfo.ToInetAddr();
            // if (instanceInfo.Metadata.TryGetValue("secure", out var secure) && Convert.ToBoolean(secure))
            // {
            //     uri = $"https://{ipAddress}";
            // }
            // else
            // {
            //     uri = $"http://{ipAddress}";
            // }

            var nacosClusterId = new NacosClusterId(clientOptions.ServiceName, clientOptions.GroupName);

            _httpClient.BaseAddress = nacosClusterId.GetServiceEndpointUri("http");
        }
    }

    public Task<BladeXResult<EnterpriseWechatNotificationResult>?> SendEnterpriseWechatNotificationAsync(string uri,
        EnterpriseWechatNotification notification)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(notification.Content);
        
        if (_hostEnvironment.IsLocal())
        {
            uri = $"/api/common-service{uri}";
        }
        
        var clientOptions = _clientOptions.CurrentValue;
        return _httpClient
            .PostJsonAsync<EnterpriseWechatNotification, BladeXResult<EnterpriseWechatNotificationResult>>(uri,
                notification, clientOptions.SerializerOptions);
    }

    public Task<BladeXResult?> WithdrawEnterpriseWechatNotificationAsync(string messageId)
    {
        var uri = "/qiyewechat/recallMsg";
        if (_hostEnvironment.IsLocal())
        {
            uri = $"/api/common-service{uri}";
        }
        
        var clientOptions = _clientOptions.CurrentValue;
        return _httpClient.PostJsonAsync<EnterpriseWechatNotificationWithdraw, BladeXResult>(uri,
            new EnterpriseWechatNotificationWithdraw()
            {
                MessageId = messageId
            }, clientOptions.SerializerOptions);
    }
}