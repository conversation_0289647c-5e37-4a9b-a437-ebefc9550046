﻿using System.Runtime.CompilerServices;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace iPlatformExtension.Common.Filters;

public abstract class AsyncStreamResultFilter<T>(bool stopOnException) : ResultFilterAttribute where T : ApiResult<object>, new()
{
    public override async Task OnResultExecutionAsync(ResultExecutingContext context, ResultExecutionDelegate next)
    {
        var httpContext = context.HttpContext;
        var result = context.Result;

        if (result is ObjectResult objectResult && objectResult.DeclaredType?.GetGenericTypeDefinition() == typeof(IAsyncEnumerable<>))
        {
            var asyncStream = new AsyncEnumerableWrapper(objectResult.Value!);
            await foreach (var item in asyncStream)
            {
                try
                {
                    await WriteAsync(context, item, httpContext.RequestAborted);
                }
                catch (Exception e)
                {
                    await WriteErrorAsync(context, e, httpContext.RequestAborted);
                    if (stopOnException)
                    {
                        break;
                    }
                }
            }

            await next();
        }
        else
        {
            await base.OnResultExecutionAsync(context, next);
        }
    }

    protected abstract Task WriteAsync(ResultExecutingContext context, object item,
        CancellationToken cancellationToken);

    protected abstract Task WriteErrorAsync(ResultExecutingContext context, Exception exception,
        CancellationToken cancellationToken);

    /// <summary>
    /// 
    /// </summary>
    private class AsyncEnumerableWrapper(object enumerable) : IAsyncEnumerable<object>
    {
        [UnsafeAccessor(UnsafeAccessorKind.Method, Name = nameof(GetAsyncEnumerator))]
        private static extern IAsyncEnumerator<object> GetIAsyncEnumerator(object enumerable, CancellationToken cancellationToken);
        
        public IAsyncEnumerator<object> GetAsyncEnumerator(CancellationToken cancellationToken = new ())
        {
            return GetIAsyncEnumerator(enumerable, cancellationToken);
        }
    }
}