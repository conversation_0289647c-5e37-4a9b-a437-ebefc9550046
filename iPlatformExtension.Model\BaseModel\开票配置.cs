using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(DisableSyncStructure = true)]
	public partial class 开票配置 {

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_direction_2", StringLength = 50)]
		public string CaseDirection2 { get; set; }

		[ Column(Name = "case_type", StringLength = 50)]
		public string CaseType { get; set; }

		[ Column(Name = "case_type_2", StringLength = 50)]
		public string CaseType2 { get; set; }

		[ Column(Name = "config_id", StringLength = 50)]
		public string ConfigId { get; set; }

		[ Column(Name = "district_name", StringLength = 50)]
		public string DistrictName { get; set; }

		[ Column(Name = "district_name_2", StringLength = 50)]
		public string DistrictName2 { get; set; }

		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		[ Column(Name = "fee_class_2", StringLength = 50)]
		public string FeeClass2 { get; set; }

		[ Column(Name = "fee_name_alias", StringLength = 50)]
		public string FeeNameAlias { get; set; }

		[ Column(Name = "fee_type_name", StringLength = 100)]
		public string FeeTypeName { get; set; }

		[ Column(Name = "fee_type_name_2", StringLength = 50)]
		public string FeeTypeName2 { get; set; }

	}

}
