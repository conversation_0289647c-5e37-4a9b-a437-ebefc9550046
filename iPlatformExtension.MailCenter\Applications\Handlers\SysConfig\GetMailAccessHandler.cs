﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    public class GetMailAccessHandler(IMailAccessRepository mailAccessRepository, IFreeSql<PlatformFreeSql> msSql) : IRequestHandler<GetMailAccessQuery, IEnumerable<GetMailAccessDto>>
    {
        public async Task<IEnumerable<GetMailAccessDto>> Handle(GetMailAccessQuery request, CancellationToken cancellationToken)
        {
            //var users = await msSql.Select<SysUserInfo>()
            //    .Where(o => o.UserId == mailInfo.OperatorUser || o.UserId == mailInfo.UpdateBy)
            //    .ToListAsync(o => new { UserId = o.UserId, CnName = o.CnName });
            //mailInfo.OperatorUser = users.FirstOrDefault(o => o.UserId == mailInfo.OperatorUser)?.CnName;
            //mailInfo.UpdateBy = users.FirstOrDefault(o => o.UserId == mailInfo.UpdateBy)?.CnName;

            var mailAccessDtos = await mailAccessRepository.Where(o => o.HostId == request.HostId).ToListAsync<GetMailAccessDto>();

            var userAccessList = mailAccessDtos.Where(o => o.UseType == UseTypeEnum.User).ToList();
            var users = await msSql.Select<SysUserInfo>()
                .Where(o => userAccessList.Any(u => u.UseId == o.UserId))
                .ToListAsync(o => new { UserId = o.UserId, CnName = o.CnName });


            var deptAccessList = mailAccessDtos.Where(o => o.UseType == UseTypeEnum.Dept).ToList();
            var deptsList = await msSql.Select<SysDeptInfo>()
                .Where(o => deptAccessList.Any(u => u.UseId == o.DeptId))
                .ToListAsync(o => new { DeptId = o.DeptId, FullName = o.FullName });


            mailAccessDtos.ForEach(o =>
            {
                if (o.UseType == UseTypeEnum.User)
                {
                    o.DisplayName = users.Find(u => u.UserId == o.UseId).CnName;
                }
            });

            mailAccessDtos.ForEach(o =>
            {
                if (o.UseType == UseTypeEnum.Dept)
                {
                    o.DisplayName = deptsList.Find(u => u.DeptId == o.UseId).FullName;
                }
            });

            return mailAccessDtos;
        }
    }
}
