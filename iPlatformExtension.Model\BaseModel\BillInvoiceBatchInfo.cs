using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_invoice_batch_info", DisableSyncStructure = true)]
	public partial class BillInvoiceBatchInfo {

		[ Column(Name = "batch_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string BatchId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "batch_name", StringLength = 100)]
		public string BatchName { get; set; }

		[ Column(Name = "batch_type", StringLength = 50)]
		public string BatchType { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "submit_is_enabled")]
		public bool? SubmitIsEnabled { get; set; } = true;

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
