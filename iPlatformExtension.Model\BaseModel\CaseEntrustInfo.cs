using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{

    [ Table(Name = "case_entrust_info", DisableSyncStructure = true)]
    public partial class CaseEntrustInfo
    {

        /// <summary>
        /// 委案ID
        /// 主键
        /// </summary>
        [ Column(StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string ID { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 开案案件号
        /// </summary>
        [ Column(StringLength = 50)]
        public string ApplyCaseID { get; set; }
        
        /// <summary>
        /// 开案ID(新案使用)
        /// </summary>
        [ Column(StringLength = 50)]
        public string? ApplyID { get; set; }

        /// <summary>
        /// 合同主题
        /// </summary>
        
        public string ContractTopic { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人(工号)
        /// </summary>
        [ Column(Name = "CreateUserID", StringLength = 50)]
        public string CreateUser { get; set; }



        /// <summary>
        /// 订单ID
        /// </summary>
        [ Column(StringLength = 50)]
        public string OrderID { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        [ Column(StringLength = 50)]
        public string OrderNo { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        
        public string? Remark { get; set; }

        /// <summary>
        /// 受理状态
        /// 0:未受理
        /// 1:已受理
        /// 2:已完成
        /// -1:已退回
        /// </summary>
        
        public int? Status { get; set; } = 0;

        /// <summary>
        /// 我方文号
        /// </summary>
        [ Column(StringLength = 50)]
        public string? Volume { get; set; }

        /// <summary>
        /// 订单案件ID
        /// </summary>
        public string OrderCaseID { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 更新操作的用户ID
        /// </summary>
        public string? UpdateUserID { get; set; }

        /// <summary>
        /// 业务案件ID
        /// 对应旧系统case_id
        /// </summary>
        public string? CaseID { get; set; }

        /// <summary>
        /// 委案日期
        /// </summary>
        public DateTime? EntrustDate { get; set; }

    }

}
