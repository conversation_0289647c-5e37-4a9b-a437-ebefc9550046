﻿using Confluent.Kafka;
using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using static Org.BouncyCastle.Math.EC.ECCurve;

namespace iPlatformExtension.Common.MQ.MailMQ
{
    public class KafkaProducerFactory<TKey, TValue>
    {
        private readonly IOptionsMonitor<KafkaProducerSettings> _config;
        private readonly ILogger<KafkaProducerFactory<TKey, TValue>> _logger;

        public KafkaProducerFactory(IOptionsMonitor<KafkaProducerSettings> config, ILogger<KafkaProducerFactory<TKey, TValue>> logger)
        {
            _config = config;
            _logger = logger;
        }

        public IProducer<TKey, TValue> CreateProducer(string configName)
        {
            var setting = _config.Get(configName);
            var producerConfig = new ProducerConfig
            {
                BootstrapServers = setting.BootstrapServers,
                //ClientId = _config.ClientId
                EnableIdempotence = setting.EnableIdempotence,
            };
            var jsonSerializerOptions = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var builder = new ProducerBuilder<TKey, TValue>(producerConfig)
                 // .SetKeySerializer(new CustomStringSerializer<TKey>(jsonSerializerOptions))
                 .SetValueSerializer(new ValueByUtf8Converter<TValue>());

            builder.SetLogHandler((_, m) =>
            {
                _logger.Log((LogLevel)m.LevelAs(LogLevelType.MicrosoftExtensionsLogging), $"KafkaProducerMessage: {m?.Message}");
            });
            return builder.Build();
        }
    }
}
