using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_address_list", DisableSyncStructure = true)]
	public partial class CusAddressList {

		[ Column(Name = "address_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string AddressId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "address_cn", StringLength = 500)]
		public string? AddressCn { get; set; }

		[ Column(Name = "address_detail", StringLength = 200)]
		public string? AddressDetail { get; set; }

		[ Column(Name = "address_en", StringLength = 500)]
		public string? AddressEn { get; set; }

		[ Column(Name = "address_type", StringLength = 50)]
		public string AddressType { get; set; }

		[ Column(Name = "address_use_type", StringLength = 50)]
		public string AddressUseType { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "city_id", StringLength = 50)]
		public string CityId { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "district_id", StringLength = 50)]
		public string DistrictId { get; set; }

		[ Column(Name = "form_type", StringLength = 50)]
		public string FormType { get; set; }

		[ Column(Name = "is_default")]
		public bool? IsDefault { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

		[ Column(Name = "postcode", StringLength = 50)]
		public string? Postcode { get; set; }

		[ Column(Name = "province_id", StringLength = 50)]
		public string ProvinceId { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
