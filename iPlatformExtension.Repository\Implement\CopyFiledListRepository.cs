﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Repository.Implement
{
    internal class CopyFiledListRepository : DefaultRepository<CopyFiledList, string>, ICopyFiledListRepository
    {
        /// <inheritdoc />
        public CopyFiledListRepository(IFreeSql fsql, UnitOfWorkManager uowManger) : base(fsql, uowManger)
        {
        }
    }
}
