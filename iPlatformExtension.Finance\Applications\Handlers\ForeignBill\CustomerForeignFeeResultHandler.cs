﻿using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal sealed class CustomerForeignFeeResultHandler(ICustomerRepository customerRepository) : ForeignFeeResultHandlerBase
{
    protected override async ValueTask GetResultAsync(ForeignBillFeeExportDto dto)
    {
        dto.CaseCustomer = (await customerRepository.GetCacheValueAsync(dto.CaseCustomer ?? string.Empty))?.CustomerName ??
                           string.Empty;
        dto.ForeignBillCustomer = (await customerRepository.GetCacheValueAsync(dto.ForeignBillCustomer ?? string.Empty))?.CustomerName ??
                                  string.Empty;
    }
}