﻿using Microsoft.AspNetCore.OpenApi;
using Microsoft.OpenApi.Models;

namespace iPlatformExtension.Public.Infrastructure.OpenApi.Document;

/// <summary>
/// CRM open-api文档对接参数
/// </summary>
public class CrmDocumentTransformer : IOpenApiDocumentTransformer
{
    /// <inheritdoc />
    public Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context,
        CancellationToken cancellationToken)
    {
        document.Info = new OpenApiInfo
        {
            Title = "CRM API",
            Version = "v1",
            Description = "CRM相关接口文档",
        };

        document.Servers =
        [
            new OpenApiServer
            {
                Url = "https://ipr-test.aciplaw.com/iPlatformExtension.Public",
                Description = "CRM接口测试环境对接"
            }
        ];

        return Task.CompletedTask;
    }
}