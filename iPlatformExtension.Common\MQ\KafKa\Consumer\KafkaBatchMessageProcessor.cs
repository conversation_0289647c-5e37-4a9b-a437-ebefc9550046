using Confluent.Kafka;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.MQ.KafKa.Consumer
{
    /// <summary>
    /// Kafka批量消息处理器，支持批量处理消息
    /// </summary>
    /// <typeparam name="TMessage">消息类型</typeparam>
    public class KafkaBatchMessageProcessor<TMessage> where TMessage : class
    {
        private readonly ILogger<KafkaBatchMessageProcessor<TMessage>>? _logger;
        private readonly int _batchSize;
        private readonly TimeSpan _maxBatchTime;
        private JsonSerializerOptions _jsonSerializerOptions = new JsonSerializerOptions();

        /// <summary>
        /// 初始化批量消息处理器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="batchSize">批量大小</param>
        /// <param name="maxBatchTimeMilliseconds">最大批处理时间（毫秒）</param>
        /// <param name="jsonSerializerOptions">JSON序列化选项</param>
        public KafkaBatchMessageProcessor(
            ILogger<KafkaBatchMessageProcessor<TMessage>>? logger = null,
            int batchSize = 100,
            int maxBatchTimeMilliseconds = 1000,
            JsonSerializerOptions? jsonSerializerOptions = null)
        {
            _logger = logger;
            _batchSize = batchSize;
            _maxBatchTime = TimeSpan.FromMilliseconds(maxBatchTimeMilliseconds);
            if (jsonSerializerOptions != null)
            {
                _jsonSerializerOptions = jsonSerializerOptions;
            }
        }
        
        /// <summary>
        /// 设置JSON序列化选项
        /// </summary>
        /// <param name="options">JSON序列化选项</param>
        public void SetJsonSerializerOptions(JsonSerializerOptions options)
        {
            _jsonSerializerOptions = options ?? throw new ArgumentNullException(nameof(options));
        }

        /// <summary>
        /// 批量处理消息
        /// </summary>
        /// <param name="consumer">消费者</param>
        /// <param name="batchAction">批量处理函数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task ProcessBatchAsync(
            IConsumer<Ignore, string> consumer,
            Action<IReadOnlyList<TMessage>> batchAction,
            CancellationToken cancellationToken)
        {
            if (consumer == null) throw new ArgumentNullException(nameof(consumer));
            if (batchAction == null) throw new ArgumentNullException(nameof(batchAction));

            var messages = new List<TMessage>();
            var consumeResults = new List<ConsumeResult<Ignore, string>>();
            var startTime = DateTime.UtcNow;

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // 尝试消费消息，设置超时时间
                    var consumeResult = consumer.Consume(TimeSpan.FromMilliseconds(100));
                    if (consumeResult != null)
                    {
                        // 记录消费信息
                        LogConsumeInfo(consumeResult);

                        // 检查是否到达分区末尾
                        if (consumeResult.IsPartitionEOF)
                        {
                            LogPartitionEOF(consumeResult);
                            continue;
                        }

                        // 反序列化消息
                        TMessage? messageResult = DeserializeMessage(consumeResult.Message.Value);
                        if (messageResult != null)
                        {
                            messages.Add(messageResult);
                            consumeResults.Add(consumeResult);
                        }
                    }

                    // 检查是否达到批处理条件
                    bool batchSizeReached = messages.Count >= _batchSize;
                    bool batchTimeReached = (DateTime.UtcNow - startTime) >= _maxBatchTime;

                    if ((batchSizeReached || batchTimeReached) && messages.Count > 0)
                    {
                        // 处理批量消息
                        await ProcessMessageBatchAsync(messages, consumeResults, batchAction, consumer);

                        // 重置批处理状态
                        messages.Clear();
                        consumeResults.Clear();
                        startTime = DateTime.UtcNow;
                    }
                }
                catch (ConsumeException e)
                {
                    _logger?.LogError(e, $"Consume error: {e.Error.Reason}");
                    Console.WriteLine($"Consume error: {e.Error.Reason}");
                }
                catch (OperationCanceledException)
                {
                    // 处理剩余的消息
                    if (messages.Count > 0)
                    {
                        await ProcessMessageBatchAsync(messages, consumeResults, batchAction, consumer);
                    }
                    throw;
                }

                // 添加一个短暂的延迟，避免CPU使用率过高
                await Task.Delay(10, cancellationToken);
            }

            // 处理剩余的消息
            if (messages.Count > 0)
            {
                await ProcessMessageBatchAsync(messages, consumeResults, batchAction, consumer);
            }
        }

        /// <summary>
        /// 处理消息批次
        /// </summary>
        /// <param name="messages">消息列表</param>
        /// <param name="consumeResults">消费结果列表</param>
        /// <param name="batchAction">批量处理函数</param>
        /// <param name="consumer">消费者</param>
        /// <returns>异步任务</returns>
        private async Task ProcessMessageBatchAsync(
            List<TMessage> messages,
            List<ConsumeResult<Ignore, string>> consumeResults,
            Action<IReadOnlyList<TMessage>> batchAction,
            IConsumer<Ignore, string> consumer)
        {
            try
            {
                _logger?.LogInformation($"Processing batch of {messages.Count} messages");
                
                // 执行批量处理函数
                batchAction(messages.AsReadOnly());

                // 存储偏移量
                foreach (var consumeResult in consumeResults)
                {
                    try
                    {
                        consumer.StoreOffset(consumeResult);
                    }
                    catch (KafkaException e)
                    {
                        _logger?.LogError(e, $"Failed to store offset: {e.Message}");
                        Console.WriteLine(e.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"Error processing message batch: {ex.Message}");
                Console.WriteLine($"Error processing message batch: {ex.Message}");
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 反序列化消息
        /// </summary>
        /// <param name="messageValue">消息值</param>
        /// <returns>反序列化后的消息对象</returns>
        private TMessage? DeserializeMessage(string messageValue)
        {
            try
            {
                return JsonSerializer.Deserialize<TMessage>(messageValue, _jsonSerializerOptions);
            }
            catch (Exception ex)
            {
                var errorMessage = $" - {DateTime.Now:yyyy-MM-dd HH:mm:ss}【Exception 消息反序列化失败，Value：{messageValue}】 ：{ex.StackTrace?.ToString()}";
                _logger?.LogError(ex, errorMessage);
                Console.WriteLine(errorMessage);
                return null;
            }
        }

        /// <summary>
        /// 记录消费信息
        /// </summary>
        /// <param name="consumeResult">消费结果</param>
        private void LogConsumeInfo(ConsumeResult<Ignore, string> consumeResult)
        {
            var message = $"Consumed message at: '{consumeResult?.TopicPartitionOffset}'.";
            _logger?.LogDebug(message);
        }

        /// <summary>
        /// 记录分区末尾信息
        /// </summary>
        /// <param name="consumeResult">消费结果</param>
        private void LogPartitionEOF(ConsumeResult<Ignore, string> consumeResult)
        {
            var message = $" - {DateTime.Now:yyyy-MM-dd HH:mm:ss} 已经到底了：{consumeResult.Topic}, partition {consumeResult.Partition}, offset {consumeResult.Offset}.";
            _logger?.LogInformation(message);
            Console.WriteLine(message);
        }
    }
}