﻿using AutoMapper;
using Google.Protobuf;
using iPlatformExtension.Messages.Mails;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Public.Applications.Models.Statistics;
using iPlatformExtension.Public.Applications.Queries.Statistics;
using MediatR;
using MiniExcelLibs;
using MongoDB.Bson;
using static iPlatformExtension.Common.GrpcServices.Notification;

namespace iPlatformExtension.Public.Applications.Handlers.Statistics
{
    /// <summary>
    /// 2.1代理人（承办人本人）端邮件模板-（周一）
    /// </summary>
    internal sealed class Send15DeadlineMsgQueryHandler(
        IFreeSql freeSql,
        IMediator mediator,
        NotificationClient notificationClient,
        IMapper mapper,
        ILogger<Send15DeadlineMsgQuery> logger,
        IHostEnvironment hostEnvironment
    ) : IRequestHandler<Send15DeadlineMsgQuery, IEnumerable<SendMsgDto>>
    {
        public async Task<IEnumerable<SendMsgDto>> Handle(
            Send15DeadlineMsgQuery request,
            CancellationToken cancellationToken
        )
        {
            var intFirstDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.IntFirstDate, 14),
                cancellationToken
            );
            var cusFirstDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.CusFirstDate, 14),
                cancellationToken
            );
            var intFinishDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.IntFinishDate, 14),
                cancellationToken
            );
            var cusFinishDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.CusFinishDate, 14),
                cancellationToken
            );
            var legalDueDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.LegalDueDate, 14),
                cancellationToken
            );
            var intFirstDateWarning = mapper.Map<List<IntFirstDateWarning>>(
                intFirstDateWarningDtos
            );
            var cusFirstDateWarning = mapper.Map<List<CusFirstDateWarning>>(
                cusFirstDateWarningDtos
            );
            var intFinishDateWarning = mapper.Map<List<IntFinishDateWarning>>(
                intFinishDateWarningDtos
            );
            var cusFinishDateWarning = mapper.Map<List<CusFinishDateWarning>>(
                cusFinishDateWarningDtos
            );
            var legalDueDateWarning = mapper.Map<List<LegalDueDateWarning>>(
                legalDueDateWarningDtos
            );

            var underTakeUserList = intFirstDateWarningDtos
                .Select(it => new { it.UndertakeUserId, it.UndertakeUserName })
                .ToList();
            underTakeUserList.AddRange(
                cusFirstDateWarningDtos.Select(it => new
                {
                    it.UndertakeUserId,
                    it.UndertakeUserName,
                })
            );
            underTakeUserList.AddRange(
                intFinishDateWarningDtos.Select(it => new
                {
                    it.UndertakeUserId,
                    it.UndertakeUserName,
                })
            );
            underTakeUserList.AddRange(
                cusFinishDateWarningDtos.Select(it => new
                {
                    it.UndertakeUserId,
                    it.UndertakeUserName,
                })
            );
            underTakeUserList.AddRange(
                legalDueDateWarningDtos.Select(it => new
                {
                    it.UndertakeUserId,
                    it.UndertakeUserName,
                })
            );
            var asyncDuplexStreamingCall = notificationClient.NotifyAsync();

            underTakeUserList = hostEnvironment.IsProduction()
               ? underTakeUserList.Distinct().ToList()
               : underTakeUserList.Distinct().Where(it => new[]
               {
                   "ea937da8-85fe-498d-9843-17e7d616c692",
                   "c1597c51-781b-4682-81c7-e01dc23431ce",
               }.Contains(it.UndertakeUserId)).ToList();

            foreach (var underTakeUser in underTakeUserList)
            {
                var dictionary = new Dictionary<string, object>()
                {
                    ["官方期限"] = legalDueDateWarning.Where(it =>
                        it.UndertakeUserId == underTakeUser.UndertakeUserId
                    ),
                    ["初稿期限(外)"] = cusFirstDateWarning.Where(it =>
                        it.UndertakeUserId == underTakeUser.UndertakeUserId
                    ),
                    ["初稿期限(内)"] = intFirstDateWarning.Where(it =>
                        it.UndertakeUserId == underTakeUser.UndertakeUserId
                    ),
                    ["定稿期限(外)"] = cusFinishDateWarning.Where(it =>
                        it.UndertakeUserId == underTakeUser.UndertakeUserId
                    ),
                    ["定稿期限(内)"] = intFinishDateWarning.Where(it =>
                        it.UndertakeUserId == underTakeUser.UndertakeUserId
                    ),
                };
                var notificationMail = new NotificationMail
                {
                    Sender = "【正式版本】系统邮箱",
                    MessageId = Guid.NewGuid().ToString(),
                    BodyTemplate = BodyTemplate.MineExpired15Days,
                };
                using (var stream = new MemoryStream())
                {
                    await stream.SaveAsAsync(
                        dictionary,
                        excelType: ExcelType.XLSX,
                        cancellationToken: cancellationToken
                    );
                    stream.Seek(0, SeekOrigin.Begin);
                    notificationMail.Attachments.Add(
                        new MailAttachment
                        {
                            FileName = "我的任务到期提醒(15天内，含当天).xlsx",
                            Data = await ByteString.FromStreamAsync(stream, cancellationToken),
                        }
                    );

                    var receiverIds = hostEnvironment.IsProduction()
                        ? new List<string> { underTakeUser.UndertakeUserId }
                        : new List<string>
                        {
                            "ea937da8-85fe-498d-9843-17e7d616c692",
                            "c1597c51-781b-4682-81c7-e01dc23431ce",
                        };

                    notificationMail.Receivers.AddRange(receiverIds);
                    logger.LogInformation(
                        notificationMail.ToJson() + notificationMail.Receivers.ToJson()
                    );

                    await asyncDuplexStreamingCall.RequestStream.WriteAsync(
                        notificationMail,
                        cancellationToken
                    );
                }
            }
            await asyncDuplexStreamingCall.RequestStream.CompleteAsync();

            return new List<SendMsgDto>();
        }
    }
}
