﻿using Grpc.Core;
using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Commission.Infrastructure.Logging;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class DomesticCommissionPushedResultCommandHandler(
    ILogger<DomesticCommissionPushedResultCommandHandler> logger,
    IDomesticTrademarkCommissionRepository domesticTrademarkCommissionRepository) 
    : IRequestHandler<DomesticCommissionPushedResultCommand>
{
    public async Task Handle(DomesticCommissionPushedResultCommand request, CancellationToken cancellationToken)
    {
        var messageResult = request.MessageResult;
        logger.LogMessageResult(messageResult);
        
        var procId = messageResult.Data.UnpackToString();
            
        if (!messageResult.Success)
        {
            logger.LogDomesticTrademarkWeightPushedFail(procId, messageResult.Message);
            return;
        }
            
        var commission = await domesticTrademarkCommissionRepository.GetAsync(procId, cancellationToken);
        if (commission == null)
        {
            logger.LogError(new NotFoundException(procId, "国内商标权值"), "");
            return;
        }
            
        commission.Pushed = true;
        commission.UpdateTime = DateTime.Now;
        commission.Updater = UserIds.Administrator;
            
        await domesticTrademarkCommissionRepository.UpdateAsync(commission, cancellationToken);
            
        logger.LogDomesticTrademarkWeightPushedSuccess(procId);
    }
}