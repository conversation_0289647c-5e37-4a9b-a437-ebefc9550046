﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public class FlowSubmitByBatchProcessHandler : IRequestHandler<FlowSubmitByBatchProcessCommand, List<FlowSubmitByBatchDto>>
    {
        private readonly IFreeSql _freeSql;
        private readonly IMediator _mediator;

        public FlowSubmitByBatchProcessHandler(IFreeSql freeSql, IMediator mediator)
        {
            _freeSql = freeSql;
            _mediator = mediator;
        }

        public async Task< List<FlowSubmitByBatchDto>> Handle(FlowSubmitByBatchProcessCommand request, CancellationToken cancellationToken)
        {
            var userInfo = request.CurrentUser;

            var cnName = userInfo.CnName;
            List<FlowSubmitByBatchDto> dto = new List<FlowSubmitByBatchDto>();

            foreach (var info in request.infos)
            {
                bool success = true;
               
                try
                {
                    await _mediator.Send(new SubmitFlowProcessCommand(info, userInfo), cancellationToken);

                }
                catch (Exception)
                {
                    success = false;
                }
                dto.Add(new FlowSubmitByBatchDto() { ProcID = info.ProcID, Successfully = success });
            }
            return dto;
        }
    }
}
