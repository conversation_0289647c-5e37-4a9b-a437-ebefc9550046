﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 获取已关联案件DTo
/// </summary>
/// <param name="CaseId">案件id</param>
/// <param name="CaseTypeId">案件类型</param>
/// <param name="Volume">我方文号</param>
/// <param name="CaseName">案件名称</param>
/// <param name="CaseDirection">案件流向</param>
/// <param name="ApplyType">申请类型</param>
/// <param name="Agency">境内代理</param>
/// <param name="CustomerName">客户名称</param>
/// <param name="CreateBy">关联人</param>
/// <param name="CreateTime">关联时间</param>
/// <param name="RelateId">关联id</param>
public record GetRelateCaseDto(
    string CaseId,
    string CaseTypeId,
    string Volume,
    string CaseName,
    string CaseDirection,
    object ApplyType,
    object Agency,
    string CustomerName)
{
    public object CreateBy { get; set; }
    public DateTime? CreateTime { get; set; }
    public string RelateId { get; set; }

};

