﻿using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Clients.Mail;

public sealed class ScopedSmtpClient(string serviceKey, IOptionsMonitor<MailAccount> accountOptions) : SmtpClient
{
    public async Task EnsureInitAsync(CancellationToken cancellationToken)
    {
        var options = accountOptions.Get(serviceKey);
        if (!IsConnected)
        {
            await ConnectAsync(options.Host, options.Port, SecureSocketOptions.None, cancellationToken);
        }

        if (!IsAuthenticated)
        {
            await AuthenticateAsync(options.Username, options.Password, cancellationToken);
        }
    }
    
    protected override void Dispose(bool disposing)
    {
        Disconnect(true);
        base.Dispose(true);
    }
}