﻿using Google.Protobuf.WellKnownTypes;
using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Commission.Clients;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class CreateRewardsCommandHandler(
    ISender sender,
    IFreeSql<PlatformFreeSql> freeSql
#if !DEBUG
    , PeriodSettingService.PeriodSettingServiceClient client
#endif
    ) 
    : IRequestHandler<CreateRewardsCommand>
{
    public async Task Handle(CreateRewardsCommand request, CancellationToken cancellationToken)
    {
        var endDate = DateTime.Today;
#if DEBUG
        
        var yesterday = endDate.AddDays(-1);
        var startDate = new DateTime(yesterday.Year, yesterday.Month, 1);
#endif

#if !DEBUG
        
        var settings = (await client.getPeriodSettingListAsync(new Empty(), cancellationToken:cancellationToken)).PeriodSettings;
        
        var todaySetting = settings.FirstOrDefault(setting => setting.Month == endDate.Month);
        if (todaySetting == null)
        {
            return;
        }
        
        var startDateMonth = endDate.Day <= todaySetting.DeadlinePushingPermissionData
            ? endDate.AddMonths(-1)
            : endDate;
        
        var startDate = new DateTime(startDateMonth.Year, startDateMonth.Month, 1);
#endif

        var procIds = await freeSql.Select<CaseProcInfo>().WithLock()
            .Where(info => info.IsEnabled == true)
            .Where(info => info.RewardEffectiveDate >= startDate && info.RewardEffectiveDate < endDate)
            .Where(info => !freeSql.Select<WinningRewardProc>().Where(rewardProc => rewardProc.ProcNo == info.ProcNo).Any())
            .Where(info => info.CaseInfo.CaseTypeId == CaseType.Trade)
            .Where(info => info.CaseInfo.CaseDirection == CaseDirection.II)
            .ToListAsync(info => info.ProcId, cancellationToken);
        
        foreach (var procId in procIds)
        {
            await sender.Send(new CreateRewardCommand(procId), cancellationToken);
        }
    }
}