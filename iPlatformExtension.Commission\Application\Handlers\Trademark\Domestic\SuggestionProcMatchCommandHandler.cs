﻿using System.Linq.Expressions;
using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class SuggestionProcMatchCommandHandler(IFreeSql<PlatformFreeSql> freeSql) : IRequestHandler<SuggestionProcMatchCommand, bool>
{
    public Task<bool> Handle(SuggestionProcMatchCommand request, CancellationToken cancellationToken)
    {
        var (commission, configs) = request;

        var suggestionProcMatchExpression =
            configs.Aggregate<SuggestionProcPointConfig, Expression<Func<CaseProcInfo, bool>>>(info => 1 == 2,
                (expression, config) => expression.Or(info =>
                    info.CtrlProcId == config.TargetCtrlProcId && info.CtrlProcMark == config.TargetProcMark &&
                    info.IsEnabled == true));

        return freeSql.Select<CaseProcInfo>().WithLock().Where(info => info.CaseId == commission.CaseId)
            .Where(suggestionProcMatchExpression).AnyAsync(cancellationToken);
    }
}