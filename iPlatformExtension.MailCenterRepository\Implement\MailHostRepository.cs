﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.MailCenterRepository.Implement;
internal class MailHostRepository(
    IFreeSql<MailCenterFreeSql> fsql, IMemoryCache memoryCache,
    UnitOfWorkManage<MailCenterFreeSql> manager, DefaultRedisCache redisCache, CacheExpirationToken<MailHost> expirationToken)
    : DefaultRepository<MailHost, string>(fsql, manager), IMailHostRepository
{
    /// <inheritdoc />
    public IMemoryCache MemoryCache { get; } = memoryCache;

    /// <inheritdoc />
    public CacheExpirationToken<MailHost> ExpirationToken { get; } = expirationToken;

    /// <inheritdoc />
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}
