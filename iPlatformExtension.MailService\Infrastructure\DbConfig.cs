﻿using FreeSql;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;

namespace iPlatformExtension.MailService.Infrastructure
{
    public static class DbConfig
    {
        public static void FreeSqlInit(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddPlatformFreeSql(configuration.GetConnectionString("Default") ?? string.Empty)
                .ConfigureGlobalCurdLogging("Logging:FreeSql:Curd");
            services.AddFreeSql<MailCenterFreeSql>(options =>
                {
                    options.CommandTimeout = TimeSpan.FromSeconds(300);
                    options.Lifetime = ServiceLifetime.Singleton;
                    options.ConnectionString = configuration.GetConnectionString("mysql") ?? string.Empty;
                    options.DbType = DataType.MySql;
                    options.LazyLoading = true;
                    options.CommandParameterWithLambda = true;
                    options.TypeHandlers.TryAdd(typeof(DateOnly), new NullableDateOnlyConverter());
                })
                .ConfigureGlobalCurdLogging("Logging:FreeSql:Curd");
            services.AddFreeSql<LogsFreeSql>(options =>
                {
                    options.CommandTimeout = TimeSpan.FromSeconds(300);
                    options.Lifetime = ServiceLifetime.Singleton;
                    options.ConnectionString = configuration.GetConnectionString("Logdb") ?? string.Empty;
                    options.DbType = DataType.SqlServer;
                    options.LazyLoading = true;
                    options.CommandParameterWithLambda = true;
                    options.TypeHandlers.TryAdd(typeof(DateOnly), new NullableDateOnlyConverter());
                })
                .ConfigureGlobalCurdLogging("Logging:FreeSql:Curd");
        }
    }
}
