﻿using iPlatformExtension.Commission.Application.Commands.Patent;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class PushPatentCommissionWeightExceptionHandler(ILogger<PushPatentCommissionWeightCommand> logger) 
    : IRequestExceptionHandler<PushPatentCommissionWeightCommand, Unit, Exception>
{
    public Task Handle(PushPatentCommissionWeightCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        logger.LogPushingPatentWeightsDataError(exception);
        state.SetHandled(Unit.Value);
        return Task.CompletedTask;
    }
}