using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_foreign_info", DisableSyncStructure = true)]
	public partial class SysForeignInfo {

		[ Column(Name = "base_id", StringLength = 50)]
		public string BaseId { get; set; }

		[ Column(Name = "base_table", StringLength = 50)]
		public string BaseTable { get; set; }

		[ Column(Name = "foreign_id", StringLength = 50)]
		public string ForeignId { get; set; }

		[ Column(Name = "foreign_table", StringLength = 50)]
		public string ForeignTable { get; set; }

		[ Column(Name = "foreign_text", StringLength = 500)]
		public string ForeignText { get; set; }

		[ Column(Name = "foreign_type", StringLength = 20)]
		public string ForeignType { get; set; }

		[ Column(Name = "multi_lingual")]
		public bool? MultiLingual { get; set; } = true;

		[ Column(Name = "rename")]
		public bool? Rename { get; set; } = true;

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
