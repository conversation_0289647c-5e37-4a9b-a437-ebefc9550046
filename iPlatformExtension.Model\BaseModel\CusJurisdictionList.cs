using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_jurisdiction_list", DisableSyncStructure = true)]
	public partial class CusJurisdictionList {

		[ Column(Name = "jurisdiction_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string JurisdictionId { get; set; }

		[ Column(Name = "case_type", StringLength = 50)]
		public string CaseType { get; set; }

		[ Column(Name = "customer_id", StringLength = 50, IsNullable = false)]
		public string CustomerId { get; set; }

		[ Column(Name = "handle_manage_company", StringLength = 50)]
		public string HandleManageCompany { get; set; }

		[ Column(Name = "handle_type", StringLength = 50)]
		public string HandleType { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
