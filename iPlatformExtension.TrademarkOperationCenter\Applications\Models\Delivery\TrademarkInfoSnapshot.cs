﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 商标快照信息
/// </summary>
public class TrademarkInfoSnapshot
{
    /// <summary>
    /// 商标说明
    /// </summary>
    public string TrademarkDescription { get; set; } = "";

    /// <summary>
    /// 商标名称
    /// </summary>
    public string TrademarkName { get; set; } = "";

    /// <summary>
    /// 商标类型
    /// </summary>
    public string TrademarkType { get; set; } = "";
    
    /// <summary>
    /// 是否颜色组合
    /// </summary>
    public bool IsMultipartColor { get; set; }

    /// <summary>
    /// 是否三维
    /// </summary>
    public bool IsThreeD { get; set; }

    /// <summary>
    /// 是否声音
    /// </summary>
    public bool IsVoice { get; set; }
    
    /// <summary>
    /// 我方文号
    /// </summary>
    public string Volume { get; set; } = "";
    
    /// <summary>
    /// 任务编号
    /// </summary>
    public string ProcNo { get; set; } = "";

    /// <summary>
    /// 标识种类
    /// </summary>
    public string MarkingType { get; set; } = string.Empty;

    /// <summary>
    /// 官方期限
    /// </summary>
    public DateTime? OfficialDeadline { get; set; }

    /// <summary>
    /// 官方代理人
    /// </summary>
    public string? AgentUser { get; set; }

    /// <summary>
    /// 申请号
    /// </summary>
    public string? AppNo { get; set; }

    /// <summary>
    /// 客户
    /// </summary>
    public string Customer { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    public string CtrlProcName { get; set; } = string.Empty;

    /// <summary>
    /// 递交方式
    /// </summary>
    public string? DeliveryType { get; set; }

    /// <summary>
    /// 申請方式
    /// </summary>
    public string ApplicationType { get; set; } = string.Empty;
    
}