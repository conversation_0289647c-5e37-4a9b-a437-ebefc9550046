using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_user_private", DisableSyncStructure = true)]
	public partial class MailUserPrivate {

		[ Column(Name = "mail_id", StringLength = 50)]
		public string MailId { get; set; }

		[ Column(Name = "menu_id", StringLength = 50)]
		public string MenuId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
