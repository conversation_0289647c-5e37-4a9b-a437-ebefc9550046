﻿using AutoMapper;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    /// <summary>
    /// 保存规则详情
    /// </summary>
    internal sealed class SaveAnalysisRuleDetailCommandHandler(IMailConfigFilterRepository mailConfigFilterRepository, IMapper mapper) : IRequestHandler<SaveAnalysisRuleDetailCommand>
    {
        public async Task Handle(SaveAnalysisRuleDetailCommand request, CancellationToken cancellationToken)
        {
            if (request.ConfigId is null) throw new NotImplementedException();
            var mailConfigs = await mailConfigFilterRepository.Where(it => it.ConfigId == request.ConfigId).ToListAsync(cancellationToken);

            if (mailConfigs.Count == 0)
            {
                var mailConfigFilters = mapper.Map<List<MailConfigFilter>>(request.AnalysisRuleDetails);
                mailConfigFilters.ForEach(it =>
                {
                    it.ConfigId = request.ConfigId;
                    it.FilterId = Guid.NewGuid().ToString();
                });
                await mailConfigFilterRepository.InsertAsync(mailConfigFilters, cancellationToken);
                return;
            }
            //删除
            var configIdList = request.AnalysisRuleDetails.Where(it => it.FilterId != null).Select(it => it.FilterId);
            var deleteListConfigs = mailConfigs.Where(it => !configIdList.Contains(it.ConfigId));
            await mailConfigFilterRepository.DeleteAsync(deleteListConfigs, cancellationToken);

            //更新
            var updateList = await mailConfigFilterRepository.Where(it => configIdList.Contains(it.FilterId)).ToListAsync(cancellationToken);
            updateList = mapper.Map<List<MailConfigFilter>>(
                request.AnalysisRuleDetails.Where(it => updateList.Select(x => x.FilterId).Contains(it.FilterId)));
            await mailConfigFilterRepository.UpdateAsync(updateList, cancellationToken);

            //新增
            var newConfigFilters = mapper.Map<List<MailConfigFilter>>(request.AnalysisRuleDetails.Where(it => it.FilterId == null));

            await mailConfigFilterRepository.InsertAsync(newConfigFilters, cancellationToken);

        }
    }
}

