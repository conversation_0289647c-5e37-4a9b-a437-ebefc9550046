﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Case;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Case;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers
{
    /// <summary>
    /// 案件信息控制器
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    //[Authorize]
    public class CaseController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mediator"></param>
        public CaseController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 获取案件信息列表（分页）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public Task<IEnumerable<GetCaseInfoDto>> GetTeamListAsync([FromQuery] GetCaseInfoQuery teamQuery)
        {
            return _mediator.Send(teamQuery);
        }

    }
}
