using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "oa_attendance_list", DisableSyncStructure = true)]
	public partial class OaAttendanceList {

		[ Column(Name = "cur_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CurId { get; set; }

		[ Column(Name = "application_no", StringLength = 50)]
		public string ApplicationNo { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "dept_id", StringLength = 50, IsNullable = false)]
		public string DeptId { get; set; }

		[ Column(Name = "patch_time")]
		public DateTime? PatchTime { get; set; }

		[ Column(Name = "patch_type", StringLength = 200)]
		public string PatchType { get; set; }

		[ Column(Name = "reason", DbType = "varchar(500)")]
		public string Reason { get; set; }

		[ Column(Name = "status", StringLength = 50)]
		public string Status { get; set; } = "0";

		[ Column(Name = "type", StringLength = 50)]
		public string Type { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

		[ Column(Name = "work_agent", StringLength = 200)]
		public string WorkAgent { get; set; }

	}

}
