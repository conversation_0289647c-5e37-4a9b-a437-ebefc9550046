using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;
using iPlatformExtension.Model.Dto.TrademarkDelivery;

namespace iPlatformExtension.Model.BaseModel
{
	/// <summary>
	/// 递交信息
	/// </summary>
    [Table(Name = "deli_info", DisableSyncStructure = true)]
    public partial class DeliInfo : IVersionEntity
    {

        [Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string ProcId { get; set; }

        [Column(Name = "app_pages")]
        public int? AppPages { get; set; }

        [Column(Name = "apply_type_id", StringLength = 50)]
        public string ApplyTypeId { get; set; }

        /// <summary>
        /// 申請類型
        /// </summary>
        [Column(Name = "application_type")] 
        public string ApplicationType { get; set; } = string.Empty;

        [Column(Name = "case_id", StringLength = 50)]
        public string CaseId { get; set; }

        [Column(Name = "case_name", StringLength = 500)]
        public string CaseName { get; set; }

        [Column(Name = "case_name_en", StringLength = 500)]
        public string CaseNameEn { get; set; }

        [Column(Name = "case_type_id", StringLength = 50)]
        public string CaseTypeId { get; set; }

        [Column(Name = "cert_code", StringLength = 50)]
        public string CertCode { get; set; }

        [Column(Name = "claims")]
        public int? Claims { get; set; }

        [Column(Name = "country_id", StringLength = 50)]
        public string CountryId { get; set; }

        [Column(Name = "cpc_file_no", StringLength = 50)]
        public string CpcFileNo { get; set; }

        [Column(Name = "create_time")]
        public DateTime? CreateTime { get; set; }

        [Column(Name = "create_user_id", StringLength = 50)]
        public string CreateUserId { get; set; }

        [Column(Name = "ctrl_proc_id", StringLength = 50)]
        public string CtrlProcId { get; set; }

        [Column(Name = "customer_id", StringLength = 50)]
        public string CustomerId { get; set; }

        [Column(Name = "delay_review", StringLength = 50)]
        public string DelayReview { get; set; }

        [Column(Name = "dependent_claims")]
        public int? DependentClaims { get; set; }

        [Column(Name = "essence_exam_fr", StringLength = 10)]
        public string EssenceExamFr { get; set; }

        [Column(Name = "essence_exam_fro", StringLength = 200)]
        public string EssenceExamFro { get; set; }

        [Column(Name = "figure", StringLength = 50)]
        public string Figure { get; set; }

        [Column(Name = "first_priority_date")]
        public DateTime? FirstPriorityDate { get; set; }

        [Column(Name = "flow_cur_user_id", StringLength = 50)]
        public string FlowCurUserId { get; set; }

        /// <summary>
        /// 流程类型
        /// </summary>
        [Column(Name = "flow_type")]
        public string FlowType { get; set; } = string.Empty;
        
        /// <summary>
        /// 流程子类型
        /// </summary>
        [Column(Name = "flow_sub_type")]
        public string FlowSubType { get; set; } = string.Empty;

        [Column(Name = "flow_update_time")]
        public DateTime? FlowUpdateTime { get; set; }

        [Column(Name = "flow_update_user", StringLength = 50)]
        public string FlowUpdateUser { get; set; }

        [Column(Name = "independent_claims")]
        public int? IndependentClaims { get; set; }

        [Column(Name = "initial_app_date")]
        public DateTime? InitialAppDate { get; set; }

        [Column(Name = "initial_app_no", StringLength = 50)]
        public string InitialAppNo { get; set; }

        [Column(Name = "invalidate_code", StringLength = 50)]
        public string InvalidateCode { get; set; }

        [Column(Name = "is_ahead_pub")]
        public bool? IsAheadPub { get; set; }

        [Column(Name = "is_auto")]
        public bool? IsAuto { get; set; } = false;

        [Column(Name = "is_en_name")]
        public bool? IsEnName { get; set; } = false;

        [Column(Name = "is_essence_exam")]
        public bool? IsEssenceExam { get; set; }

        [Column(Name = "is_fee_reduce")]
        public bool? IsFeeReduce { get; set; }

        [Column(Name = "is_heredity")]
        public bool? IsHeredity { get; set; }

        [Column(Name = "is_local_design")]
        public bool? IsLocalDesign { get; set; } = false;

        [Column(Name = "is_priority_review")]
        public bool? IsPriorityReview { get; set; }

        [Column(Name = "is_product")]
        public bool? IsProduct { get; set; } = false;

        [Column(Name = "is_request_das")]
        public bool? IsRequestDas { get; set; } = false;

        [Column(Name = "is_same_day")]
        public bool? IsSameDay { get; set; }

        [Column(Name = "is_secrecy_request")]
        public bool? IsSecrecyRequest { get; set; }

        [Column(Name = "is_seq")]
        public bool? IsSeq { get; set; }

        [Column(Name = "is_similar")]
        public bool? IsSimilar { get; set; } = false;

        [Column(Name = "novelity1")]
        public bool? Novelity1 { get; set; }

        [Column(Name = "novelity2")]
        public bool? Novelity2 { get; set; }

        [Column(Name = "novelity3")]
        public bool? Novelity3 { get; set; }

        [Column(Name = "office_id", StringLength = 50)]
        public string OfficeId { get; set; }

        /// <summary>
        /// 官方期限
        /// </summary>
        [Column(Name = "official_deadline")]
        public DateTime? OfficialDeadline { get; set; }

        [Column(Name = "pct_ahead0")]
        public bool? PctAhead0 { get; set; } = true;

        [Column(Name = "pct_ahead1")]
        public bool? PctAhead1 { get; set; }

        [Column(Name = "pct_app_date")]
        public DateTime? PctAppDate { get; set; }

        [Column(Name = "pct_app_no", StringLength = 50)]
        public string PctAppNo { get; set; }

        [Column(Name = "pct_declare", StringLength = 10)]
        public string PctDeclare { get; set; }

        [Column(Name = "pct_declare10", StringLength = 50)]
        public string PctDeclare10 { get; set; }

        [Column(Name = "pct_declare11", StringLength = 50)]
        public string PctDeclare11 { get; set; }

        [Column(Name = "pct_declare12", StringLength = 50)]
        public string PctDeclare12 { get; set; }

        [Column(Name = "pct_declare20", StringLength = 50)]
        public string PctDeclare20 { get; set; }

        [Column(Name = "pct_declare21", StringLength = 50)]
        public string PctDeclare21 { get; set; }

        [Column(Name = "pct_declare22", StringLength = 50)]
        public string PctDeclare22 { get; set; }

        [Column(Name = "pct_declare23", StringLength = 50)]
        public string PctDeclare23 { get; set; }

        [Column(Name = "pct_declare30", StringLength = 50)]
        public string PctDeclare30 { get; set; }

        [Column(Name = "pct_declare31", StringLength = 50)]
        public string PctDeclare31 { get; set; }

        [Column(Name = "pct_declare32", StringLength = 50)]
        public string PctDeclare32 { get; set; }

        [Column(Name = "pct_declare40", StringLength = 50)]
        public string PctDeclare40 { get; set; }

        [Column(Name = "pct_declare41", StringLength = 50)]
        public string PctDeclare41 { get; set; }

        [Column(Name = "pct_declare42", StringLength = 50)]
        public string PctDeclare42 { get; set; }

        [Column(Name = "pct_language", StringLength = 50)]
        public string PctLanguage { get; set; }

        [Column(Name = "pct_pub_date")]
        public DateTime? PctPubDate { get; set; }

        [Column(Name = "pct_pub_no", StringLength = 50)]
        public string PctPubNo { get; set; }

        [Column(Name = "picture_count")]
        public int? PictureCount { get; set; }

        [Column(Name = "product")]
        public int? Product { get; set; }

        [Column(Name = "request_das", StringLength = 50)]
        public string RequestDas { get; set; }

        [Column(Name = "similar")]
        public int? Similar { get; set; }

        [Column(Name = "specification_pages")]
        public int? SpecificationPages { get; set; }

        [Column(Name = "status")]
        public int? Status { get; set; }

        [Column(Name = "update_time")]
        public DateTime? UpdateTime { get; set; }

        [Column(Name = "update_user_id", StringLength = 50)]
        public string UpdateUserId { get; set; }

        [Column(Name = "upload_time")]
        public DateTime? UploadTime { get; set; }

        [Column(Name = "upload_user_id", StringLength = 50)]
        public string UploadUserId { get; set; }

        /// <summary>
        /// 代理名义
        /// </summary>
        [Column(Name = "agency_name")]
        public string? AgencyName { get; set; }

        /// <summary>
        /// 代理机构ID
        /// </summary>
        [Column(Name = "trademark_agency_id")]
        public string? AgencyId { get; set; }


        /// <summary>
        /// 代理人
        /// </summary>
        [Column(Name = "agent_user")]
        public string? AgentUser { get; set; }

        /// <summary>
        /// 申请号
        /// </summary>
        [Column(Name = "application_no")]
        public string? AppNo { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [Column(Name = "contact_person")]
        public string? ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [Column(Name = "contact_tel")]
        public string? ContactTel { get; set; }

        /// <summary>
        /// 联系邮箱
        /// </summary>
        [Column(Name = "contact_mailbox")]
        public string? ContactMailBox { get; set; }

        /// <summary>
        /// 联系邮编
        /// </summary>
        [Column(Name = "contact_postcode")]
        public string? ContactPostCode { get; set; }

        /// <summary>
        /// 联系人地址
        /// </summary>
        [Column(Name = "contact_address")]
        public string? ContactAddress { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [Column(Name = "description")]
        public string? Description { get; set; }

        /// <summary>
        /// 是否颜色组合
        /// </summary>
        [Column(Name = "is_multipart_color", IsNullable = false)]
        public bool IsMultipartColor { get; set; }

        /// <summary>
        /// 是否三维
        /// </summary>
        [Column(Name = "is_three_d", IsNullable = false)]
        public bool IsThreeD { get; set; }

        /// <summary>
        /// 是否肖像
        /// </summary>
        [Column(Name = "is_portraits", IsNullable = false)]
        public bool IsPortraits { get; set; }

        /// <summary>
        /// 是否声音
        /// </summary>
        [Column(Name = "is_voice", IsNullable = false)]
        public bool IsVoice { get; set; }

        /// <summary>
        /// 代理文号
        /// </summary>
        [Column(Name = "agent_symbol", StringLength = 50, IsNullable = false)]
        public string Volume { get; set; } = "";

        /// <summary>
        /// 任务编号
        /// </summary>
        [Column(Name = "task_no", StringLength = 20, IsNullable = false)]
        public string ProcNo { get; set; } = "";

        /// <summary>
        /// 承办人Id
        /// </summary>
        [Column(Name = "undertaker_id", StringLength = 50, IsNullable = false)]
        public string UndertakerId { get; set; } = "";

        /// <summary>
        /// 标识种类
        /// </summary>
        [Column(Name = "marking_type")]
        public string? MarkingType { get; set; }

        /// <summary>
        /// 权大师订单号
        /// </summary>
        [Column(Name = "order_no")]
        public string OrderNo { get; set; } = string.Empty;

        /// <summary>
        /// 版本号
        /// </summary>
        [Column(Name = "version", IsVersion = true)]
        public int Version { get; set; } = 0;

        /// <summary>
        /// 递交方式
        /// </summary>
        [Column(Name = "delivery_type")]
        public string? DeliveryType { get; set; }

        /// <summary>
        /// 商标递交结果
        /// </summary>
        [Column(Name = "display_json")]
        public string DisplayJson { get; set; } = "[]";

        /// <summary>
        /// 操作结果(错误:false,成功:true)
        /// </summary>
        [Column(Name = "operation_result")]
        public bool OperationResult { get; set; } = true;

        /// <summary>
        /// 尼斯分类是否是标准项
        /// </summary>
        [Column(Name = "is_standard_nice")]
        public bool? IsStandardNice { get; set; }

		/// <summary>
		/// 递交key
		/// </summary>
		[Column(Name = "delivery_key", InsertValueSql = "")]
		public string DeliveryKey { get; set; } = string.Empty;

		/// <summary>
		/// 递交日期
		/// </summary>
		[Column(Name = "delivery_date")]
		public DateTime? DeliveryDate { get; set; }


        /// <summary>
        /// 事项备注
        /// </summary>
        [Column(Name = "proc_note")]
        public string? ProcNote { get; set; }

        /// <summary>
        /// 商标递交其他信息
        /// </summary>
        [Navigate(nameof(ProcId))]
		public virtual DeliOtherInfo? OtherInfo { get; set; }

        /// <summary>
        /// 递交申请人信息
        /// </summary>
        [Navigate(nameof(DeliApplicant.ProcId))]
        public virtual ICollection<DeliApplicant>? Applicants { get; set; }

        /// <summary>
        /// 递交优先权信息
        /// </summary>
        [Navigate(nameof(DeliPriority.ProcId))]
        public virtual ICollection<DeliPriority>? Priorities { get; set; }

        // /// <summary>
        // /// 递交的案件关联信息
        // /// </summary>
        // [Navigate(nameof(CaseId))]
        // public virtual DeliCase? DeliveryCase { get; set; }

        /// <summary>
        /// 尼斯分类集合导航属性
        /// </summary>
        [Navigate(nameof(DeliveryNiceCategory.ProcId))]
        public virtual ICollection<DeliveryNiceCategory>? NiceCategories { get; set; }

        /// <summary>
        /// 递交文件
        /// </summary>
        [Navigate(nameof(DeliFiles.ProcId))]
        public virtual ICollection<DeliFiles>? Files { get; set; }

        /// <summary>
        /// 商标异议的法律条款信息
        /// </summary>
        [Navigate(nameof(DeliveryLawBasis.ProcId))]
        public virtual ICollection<DeliveryLawBasis>? LawBasis { get; set; }

        /// <summary>
        /// 无效宣告申请
        /// </summary>
        [Navigate(nameof(DeliveryCitedTrademark.ProcId))]
        public virtual ICollection<DeliveryCitedTrademark>? CitedTrademarkAnnulments { get; set; }

        /// <summary>
        /// 获取递交状态描述
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ArgumentOutOfRangeException">当前状态不在定义的枚举范围内</exception>
        public string GetDeliveryStatus()
        {
            var deliveryStatus = (DeliveryStatus)(Status ?? 0);
            if (!OperationResult)
            {
                return "递交失败";
            }

            var statusDescription = deliveryStatus switch
            {
                DeliveryStatus.Ready => "未发起",
                DeliveryStatus.Ordered => "递交中",
                DeliveryStatus.Delivering => "递交中",
                DeliveryStatus.Complete => "递交成功",
                DeliveryStatus.Confirmed => "递交成功",
                DeliveryStatus.Stopped => "未发起",
                _ => throw new ArgumentOutOfRangeException()
            };

            if (IsAuto ?? false)
            {
	            return statusDescription;
            }

            return "未发起";
        }

    }

}
