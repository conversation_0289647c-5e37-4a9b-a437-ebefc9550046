﻿using System.Reflection;
using iPlatformExtension.Common.Mediator.PipelineBehaviors;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.ExceptionHandlers;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.PipelineBehaviors;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;

/// <summary>
/// mediat services 依赖注入
/// </summary>
public static class DependencyInjectionExtension
{
    /// <summary>
    /// 添加应用服务
    /// </summary>
    /// <param name="configuration"></param>
    public static void AddApplicationServices(MediatRServiceConfiguration configuration)
    {
        configuration.Lifetime = ServiceLifetime.Transient;
        configuration.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
        configuration.AddOpenBehavior(typeof(DefaultFreeSqlUnitOfWorkPipelineBehavior<,>), ServiceLifetime.Scoped);
        configuration.AddOpenBehavior(typeof(FlowEventVersionValidateHandler<,>), ServiceLifetime.Singleton);
        configuration.AddOpenBehavior(typeof(FlowObjectIdQueryHandler<,>), ServiceLifetime.Singleton);
        configuration.AddOpenBehavior(typeof(DeliveryValidationExceptionHandler<,>));
        // configuration.RequestExceptionActionProcessorStrategy =
        //     RequestExceptionActionProcessorStrategy.ApplyForAllExceptions;
    }
}