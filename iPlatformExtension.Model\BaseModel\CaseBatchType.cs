using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_batch_type", DisableSyncStructure = true)]
	public partial class CaseBatchType {

		[ Column(Name = "batch_type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string BatchTypeId { get; set; }

		[ Column(Name = "batch_type", StringLength = 50)]
		public string BatchType { get; set; }

		[ Column(Name = "batch_type_en_us", StringLength = 50)]
		public string BatchTypeEnUs { get; set; }

		[ Column(Name = "batch_type_ja_jp", StringLength = 50)]
		public string BatchTypeJaJp { get; set; }

		[ Column(Name = "batch_type_zh_cn", StringLength = 50)]
		public string BatchTypeZhCn { get; set; }

		[ Column(Name = "extended_column", StringLength = 50)]
		public string ExtendedColumn { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "update_table", StringLength = 50)]
		public string UpdateTable { get; set; }

	}

}
