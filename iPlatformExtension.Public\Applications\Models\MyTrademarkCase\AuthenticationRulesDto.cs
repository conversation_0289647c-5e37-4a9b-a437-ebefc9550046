﻿namespace iPlatformExtension.Public.Applications.Models.MyTrademarkCase
{
    /// <summary>
    /// 国家认证dto
    /// </summary>
    /// <param name="Id">主键，不传为新增</param>
    /// <param name="RecognizedCertificateId">公认证字典id</param>
    /// <param name="CtrlProcId">任务id</param>
    /// <param name="CountryIds">国家id</param>
    /// <param name="IsEnable">是否生效</param>
    /// <param name="Mark">备注</param>
    public record AuthenticationRulesDto(string? Id,
        string RecognizedCertificateId,
        string CtrlProcId,
        List<string> CountryIds,
        bool IsEnable,
        string Mark)
    {
    }
}
