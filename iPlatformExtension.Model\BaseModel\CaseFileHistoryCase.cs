using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_file_history_case", DisableSyncStructure = true)]
	public partial class CaseFileHistoryCase {

		[ Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

		[ Column(Name = "volume_short", StringLength = 50)]
		public string VolumeShort { get; set; }

	}

}
