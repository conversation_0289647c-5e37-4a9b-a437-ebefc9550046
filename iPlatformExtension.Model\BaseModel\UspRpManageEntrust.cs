using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "usp_rp_manage_entrust", DisableSyncStructure = true)]
	public partial class UspRpManageEntrust {

		[ Column(Name = "@date", StringLength = 50, IsNullable = false)]
		public string Date { get; set; }

		[ Column(Name = "@report_form_code", StringLength = 100, IsNullable = false)]
		public string ReportFormCode { get; set; }

	}

}
