namespace iPlatformExtension.MailCenter.Applications.Models.AuditUser
{
    /// <summary>
    /// 审核必选人DTO
    /// </summary>
    public class GetAuditRequiredDto
    {
        /// <summary>
        /// 是否必选人
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// 指定审核人列表
        /// </summary>
        public List<UserInfoDto> DesignatedAuditUsers { get; set; } = new List<UserInfoDto>();
    }

    /// <summary>
    /// 用户信息DTO
    /// </summary>
    public class UserInfoDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public string Department { get; set; }
    }
}
