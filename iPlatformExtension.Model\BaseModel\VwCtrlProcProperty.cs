using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_ctrl_proc_property", DisableSyncStructure = true)]
	public partial class VwCtrlProcProperty {

		[ Column(Name = "name_id", StringLength = 50, IsNullable = false)]
		public string NameId { get; set; }

		[ Column(Name = "name_zh_cn", StringLength = 1000)]
		public string NameZhCn { get; set; }

	}

}
