using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_meetrend_UnitTree", DisableSyncStructure = true)]
	public partial class VwMeetrendUnitTree {

		[ Column(StringLength = 500)]
		public string FFULLNAME { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string FID { get; set; }

		[ Column(StringLength = 100)]
		public string FNAME { get; set; }

		[ Column(StringLength = 50)]
		public string FPARENTID { get; set; }

		
		public bool FSTATUS { get; set; }

		[ Column(Name = "update_time")]
		public DateTime UpdateTime { get; set; }

	}

}
