﻿using System.ComponentModel;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Applications.Models.Customer.Contract;

/// <summary>
/// crm合同创建参数
/// </summary>
[Description("crm合同创建参数")]
public class CrmContractCreateDto
{
    /// <summary>
    /// 申请类型
    /// </summary>
    [Description("申请类型")]
    [BindProperty(Name = "applicationType")]
    public required string ApplicationType { get; set; }

    /// <summary>
    /// 合同类型
    /// </summary>
    [Description("合同类型")]
    [BindProperty(Name = "contractType")]
    public required string ContractType { get; set; }

    /// <summary>
    /// 合同标题
    /// </summary>
    [Description("合同标题")]
    [BindProperty(Name = "contractTitle")]
    public required string ContractTitle { get; set; }

    /// <summary>
    /// 合同编号
    /// </summary>
    [Description("合同编号")]
    [BindProperty(Name = "contractNo")]
    public required string ContractNo { get; set; }

    /// <summary>
    /// 签订日期
    /// </summary>
    [Description("签订日期")]
    [BindProperty(Name = "signingDate")]
    public required DateOnly SigningDate { get; set; }
    
    /// <summary>
    /// 生效日期
    /// </summary>
    [Description("生效日期")]
    [BindProperty(Name = "effectiveDate")]
    public required DateOnly EffectiveDate { get; set; }
    
    /// <summary>
    /// 截至日期
    /// </summary>
    [Description("截至日期")]
    [BindProperty(Name = "endDate")]
    public required DateOnly EndDate { get; set; }
    
    /// <summary>
    /// CRM合同id
    /// </summary>
    [Description("CRM合同id")]
    [BindProperty(Name = "crmContractId")]
    public required string CrmContractId { get; set; }
    
    /// <summary>
    /// 归档状态
    /// </summary>
    [Description("归档状态")]
    [BindProperty(Name = "archiveStatus")]
    public required string ArchiveStatus { get; set; }
    
    /// <summary>
    /// 签订者用户
    /// </summary>
    [Description("签订者用户工号")]
    [BindProperty(Name = "signatoryUser")]
    public required string SignatoryUser { get; set; }
    
    /// <summary>
    /// 我方签约主体
    /// </summary>
    [Description("我方签约主体")]
    [BindProperty(Name = "myContractingEntity")]
    public required string MyContractingEntity { get; set; }
    
    /// <summary>
    /// 对方签约主体
    /// </summary>
    [Description("对方签约主体")]
    [BindProperty(Name = "customerContractingEntity")]
    public required string CustomerContractingEntity { get; set; }
    
    /// <summary>
    /// 操作用户编号
    /// </summary>
    [Description("操作用户工号")]
    [BindProperty(Name = "operator")]
    public required string Operator { get; set; }
    
    /// <summary>
    /// 文件信息
    /// </summary>
    [Description("文件信息")]
    [BindProperty(Name = "files")]
    public IFormFileCollection Files { get; set; } = new FormFileCollection();

    /// <summary>
    /// 明细信息
    /// </summary>
    [Description("明细信息")]
    [BindProperty(Name = "details")]
    public IList<ContractDetailDto> Details { get; set; } = [];
}
