﻿using CSScriptLib;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Helper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 添加团队成员
    /// </summary>
    public class EditTeamMemberCommandHandler(IFreeSql freeSql, IHttpContextAccessor content, ISysTeamAssociateMemberRepository sysTeamAssociateMemberRepository, 
        ISystemRoleInfoRepository systemRoleInfoRepository) : IRequestHandler<EditTeamMemberCommand>
    {
        public async Task Handle(EditTeamMemberCommand request, CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
            {
                return;
            }
            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userId);

            //获取团队负责人角色id

            var teamLeaderRoleId = systemRoleInfoRepository.GetRole(RoleCode.TeamLeader, RoleType.TrademarkOperation);
            var team = await freeSql.Select<SysTeam>().WithLock().Where(it => it.TeamId == request.TeamId).FirstAsync(cancellationToken);
            if (team == null)
            {
                throw new ApplicationException("团队id为空");
            }
            if (request.Members.Count(it => it.RoleId == teamLeaderRoleId) != 1 && team.IsEffect)
            {
                throw new ApplicationException("请补充团队成员");
            }

            var sysTeamAssociateMembers = await sysTeamAssociateMemberRepository.Where(it => it.TeamId == request.TeamId).ToListAsync(cancellationToken);

            //不存在删除
            sysTeamAssociateMembers.ForEach((sysTeamAssociateMember) =>
            {
                if (request.Members.All(it => it.UserId != sysTeamAssociateMember.UserId))
                    sysTeamAssociateMemberRepository.Delete(sysTeamAssociateMember);
            });
            //更新与插入
            request.Members.ForEach((member) =>
            {
                var teamMember = sysTeamAssociateMembers.FirstOrDefault(it => it.UserId == member.UserId);
                if (teamMember != null)
                {
                    if (teamMember.RoleId != member.RoleId)
                    {
                        teamMember.UpdateTime = DateTime.Now;
                        teamMember.UpdateUserId = userId;
                        teamMember.RoleId = member.RoleId;
                        sysTeamAssociateMemberRepository.Update(teamMember);
                    }
                }
                else
                {
                    sysTeamAssociateMemberRepository.Insert(new SysTeamAssociateMember
                    {
                        TeamMemberId = TimestampAndRandom.GenerateId(),
                        CreateTime = DateTime.Now,
                        CreateUserId = userId,
                        RoleId = member.RoleId,
                        TeamId = request.TeamId,
                        UserId = member.UserId,
                        UserName = member.UserName,
                    });
                }
            });
        }
    }
}

