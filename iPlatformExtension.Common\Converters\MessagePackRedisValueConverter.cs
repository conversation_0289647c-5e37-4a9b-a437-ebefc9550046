﻿using iPlatformExtension.Common.Converters.Abstraction;
using MessagePack;
using StackExchange.Redis;

namespace iPlatformExtension.Common.Converters;

public sealed class MessagePackRedisValueConverter(MessagePackSerializerOptions serializerOptions)
    : IRedisValueConverter
{
    public MessagePackRedisValueConverter() : this(MessagePackSerializerOptions.Standard)
    {
        
    }

    public bool CanConvert(Type type)
    {
        return serializerOptions.Resolver.GetFormatter<object>() is not null;
    }

    public RedisValue ConvertToRedisValue(object? obj)
    {
        return obj is null ? ReadOnlyMemory<byte>.Empty : MessagePackSerializer.Serialize(obj.GetType(), obj, serializerOptions);
    }

    public object? ConvertFromRedisValue(RedisValue redisValue, Type objectType)
    {
        return MessagePackSerializer.Deserialize(objectType, redisValue, serializerOptions);
    }
}