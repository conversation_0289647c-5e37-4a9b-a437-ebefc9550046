using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_cus_gz_update", DisableSyncStructure = true)]
	public partial class ACusGzUpdate {

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "id", StringLength = 50)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "is_do")]
		public bool? IsDo { get; set; } = false;

		[ Column(Name = "old_id", StringLength = 50)]
		public string OldId { get; set; }

		[ Column(Name = "old_name")]
		public string OldName { get; set; }

		[ Column(Name = "parent_id", StringLength = 50)]
		public string ParentId { get; set; }

		[ Column(Name = "parent_name")]
		public string ParentName { get; set; }

	}

}
