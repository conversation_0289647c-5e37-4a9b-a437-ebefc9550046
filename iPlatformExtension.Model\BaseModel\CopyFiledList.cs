﻿using System;
using System.Collections.Generic;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{

    [Table(Name = "copy_filed_list", DisableSyncStructure = true)]
    public partial class CopyFiledList
    {

        /// <summary>
        /// 主键
        /// </summary>
        [Column(StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string Id { get; set; }

        /// <summary>
        /// 父级id
        /// </summary>
        [Column(Name = "father_id", StringLength = 50)]
        public string FatherId { get; set; }

        /// <summary>
        /// 显示名称
        /// </summary>
        [Column(Name = "name", StringLength = 50)]
        public string Name { get; set; }

        /// <summary>
        /// 字段
        /// </summary>
        [Column(Name = "property", StringLength = 50)]
        public string Property { get; set; }

        /// <summary>
        /// 表名
        /// </summary>
        [Column(Name = "table", StringLength = 50)]
        public string Table { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        [Column(Name = "type", StringLength = 50)]
        public string Type { get; set; }

        /// <summary>
        /// 外键
        /// </summary>
        [Column(Name = "key_type", StringLength = 30)]
        public string KeyType { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [Column(Name = "mark", StringLength = 500)]
        public string Mark { get; set; }

        [Navigate(nameof(FatherId))]
        public CopyFiledList Parent { get; set; }

        [Navigate(nameof(FatherId))]
        public List<CopyFiledList> Childs { get; set; }

    }

}
