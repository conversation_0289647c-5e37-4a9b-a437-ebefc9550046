﻿using System.Text;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Common.Mediator.PipelineBehaviors;

public sealed class DefaultFreeSqlUnitOfWorkPipelineBehavior<TRequest, TResult>(
    IMediator mediator,
    ObjectPool<StringBuilder> stringBuilderPool,
    UnitOfWorkManagerProvider unitOfWorkManagerProvider,
    ILoggerFactory loggerFactory)
    : IFreeSqlUnitOfWorkPipelineBehavior<TRequest, TResult>
    where TRequest : IFreeSqlUnitOfWorkCommand
{
    public UnitOfWorkManagerProvider UnitOfWorkManagerProvider { get; } = unitOfWorkManagerProvider;
    public ILogger Logger { get; } = loggerFactory.CreateLogger<DefaultFreeSqlUnitOfWorkPipelineBehavior<TRequest, TResult>>();

    public ObjectPool<StringBuilder> StringBuilderPool { get; } = stringBuilderPool;

    public IMediator Mediator { get; } = mediator;
}