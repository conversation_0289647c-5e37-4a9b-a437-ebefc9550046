using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_applicant_list", DisableSyncStructure = true)]
	public partial class CusApplicantList {

		[ Column(Name = "applicant_id", StringLength = 50, IsNullable = false)]
		public string ApplicantId { get; set; }

		[ Column(Name = "init_id", StringLength = 50)]
		public string InitId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

		[ Column(Name = "proxy_no", StringLength = 50)]
		public string ProxyNo { get; set; }

		[ Column(Name = "proxy_no_pct", StringLength = 50)]
		public string ProxyNoPct { get; set; }

	}

}
