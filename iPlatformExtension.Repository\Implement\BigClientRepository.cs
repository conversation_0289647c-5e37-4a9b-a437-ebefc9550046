﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class BigClientRepository(
    IFreeSql<PlatformFreeSql> freeSql, 
    IMemoryCache memoryCache, 
    CacheExpirationToken<SysCustomerBigClient> expirationToken, 
    IRedisCache<RedisCacheOptionsBase> redisCache) 
    : BaseRepository<SysCustomerBigClient, string>(freeSql), IBigClientRepository
{
    public IMemoryCache MemoryCache { get; } = memoryCache;

    public CacheExpirationToken<SysCustomerBigClient> ExpirationToken { get; } = expirationToken;

    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}