﻿using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class RefreshProcRewardDateCommandHandler(
    EntityTypeInfoProvider typeInfoProvider,
    ICaseProcInfoRepository caseProcInfoRepository) 
    : IRequestHandler<RefreshProcRewardDateCommand>
{
    public async Task Handle(RefreshProcRewardDateCommand request, CancellationToken cancellationToken)
    {
        var (oldRule, currentRule) = request.RuleChangedInfo;
        var freeSql = caseProcInfoRepository.Orm;

        if (oldRule.IsEnabled)
        {
            var revertProcList = await caseProcInfoRepository
                .Where(info => info.CtrlProcId == oldRule.CtrlProcId)
                .Where(info => info.CaseInfo.CaseDirection == oldRule.CaseDirection)
                .Where(info => info.SituationChanged == oldRule.SituationChanged)
                .Where(info => info.ResultRemark == oldRule.RulingResult)
                .Where(info => info.RewardEffectiveDate != null)
                .Where(info => !freeSql.Select<WinningRewardProc>().Where(rewardProc => rewardProc.ProcId == info.ProcId).Any())
                .ToListAsync(cancellationToken);

            await caseProcInfoRepository.UpdateAsync(revertProcList.Select(info =>
            {
                info.UpdateTime = DateTime.Now;
                info.UpdateUserId = UserIds.Administrator;
                info.RewardEffectiveDate = null;

                return info;
            }).ToList(), cancellationToken);
        }

        if (currentRule.IsEnabled)
        {
            var refreshProcList = await caseProcInfoRepository
                .Where(info => info.CtrlProcId == currentRule.CtrlProcId)
                .Where(info => info.CaseInfo.CaseDirection == currentRule.CaseDirection)
                .Where(info => info.SituationChanged == currentRule.SituationChanged)
                .Where(info => info.ResultRemark == currentRule.RulingResult)
                .Where(info => info.RewardEffectiveDate == null)
                .Where(info => info.IsEnabled == true)
                .Where(info => !freeSql.Select<WinningRewardProc>().Where(rewardProc => rewardProc.ProcId == info.ProcId).Any())
                .ToListAsync(cancellationToken);
            
            var dateType = currentRule.DateType;
            var typeInfo = typeInfoProvider.Get(typeof(CaseProcInfo));
            var rewardEffectiveDateProperty = typeInfo.EntityPropertyInfos.Values.SingleOrDefault(prop => prop.ColumnName == dateType);

            if (rewardEffectiveDateProperty is not null)
            {
                await caseProcInfoRepository.UpdateAsync(refreshProcList.Where(info =>
                {
                    if (rewardEffectiveDateProperty.Get?.Invoke(info) is not DateTime rewardEffectiveDate) 
                        return false;
                    
                    info.RewardEffectiveDate = rewardEffectiveDate;
                    info.UpdateTime = DateTime.Now;
                    info.UpdateUserId = UserIds.Administrator;
                    return true;

                }).ToList(), cancellationToken);
            }
        }
    }
}