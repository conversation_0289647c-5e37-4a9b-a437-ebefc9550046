﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;

/// <summary>
/// 保存自定义标签
/// </summary>
/// <param name="FlowType">流程类型</param>
/// <param name="FlowSubType">递交类型</param>
/// <param name="PrivateList">标签列表</param>
public record SaveFlowPrivateCommand(string FlowType, string? FlowSubType,List<PrivateListDto> PrivateList) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

