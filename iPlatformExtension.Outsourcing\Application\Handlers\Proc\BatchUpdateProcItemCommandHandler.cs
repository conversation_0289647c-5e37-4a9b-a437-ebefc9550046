﻿using iPlatformExtension.Outsourcing.Application.Commands;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class BatchUpdateProcItemCommandHandler(ISender sender) : IRequestHandler<ProcJsonPatchDocument, BatchUpdateProcItemResult>
{
    public async Task<BatchUpdateProcItemResult> Handle(ProcJsonPatchDocument request, CancellationToken cancellationToken)
    {
        var (procId, version) = request;
        
        await sender.Send(new UpdateProcCommand(procId, version, request, true), cancellationToken);
        
        return new BatchUpdateProcItemResult()
        {
            Success = true,
            ItemId = request.ProcId,
            Message = "操作成功"
        };
    }
}