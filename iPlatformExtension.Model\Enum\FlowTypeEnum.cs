﻿

namespace iPlatformExtension.Model.Enum
{
    /// <summary>
    /// 流程类型
    /// </summary>
    public class FlowTypeEnum
    {
        /// <summary>
        /// 提成流程
        /// </summary>
        public const string BONUS = "BONUS";
        /// <summary>
        /// 批量开案
        /// </summary>
        public const string BAP = "BAP";
        /// <summary>
        /// 商标官文转寄
        /// </summary>
        public const string TMFN = "TMFN";
        /// <summary>
        /// 快递流程
        /// </summary>
        public const string EXPRESS = "EXPRESS";
        /// <summary>
        /// 待补充资料
        /// </summary>
        public const string FI = "FI";
        /// <summary>
        /// 开案流程
        /// </summary>
        public const string AP = "AP";
        /// <summary>
        /// 核稿流程
        /// </summary>
        public const string EX = "EX";
        /// <summary>
        /// 到款流程
        /// </summary>
        public const string RB = "RB";
        /// <summary>
        /// 配案流程
        /// </summary>
        public const string AT = "AT";
        /// <summary>
        /// 邮件处理
        /// </summary>
        public const string EM = "EM";
        /// <summary>
        /// 递交流程
        /// </summary>
        public const string DE = "DE";
        /// <summary>
        /// 请款流程
        /// </summary>
        public const string BL = "BL";
        /// <summary>
        /// 结案流程
        /// </summary>
        public const string CL = "CL";
        /// <summary>
        /// 委案流程
        /// </summary>
        public const string Entrust = "Entrust";
        /// <summary>
        /// 开票管理
        /// </summary>
        public const string ABI = "ABI";
        /// <summary>
        /// 质检流程
        /// </summary>
        public const string Quality = "Quality";
        /// <summary>
        /// 制图流程
        /// </summary>
        public const string DrawingProcess = "DrawingProcess";
    }
}
