﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.Encryption
{
    public class DESHelper
    {
        /// <summary>
        /// 进行DES加密。
        /// </summary>
        /// <param name="_context">要加密的字符串。</param>
        /// <param name="_key">密钥，且必须为8位。</param>
        /// <returns>以Base64格式返回的加密字符串。</returns>
        public static string DESEncrypt(string _context, string _key = "")
        {
            using (DESCryptoServiceProvider des = new DESCryptoServiceProvider())
            {
                _key = string.IsNullOrEmpty(_key) ? "platform" : _key;

                byte[] inputByteArray = Encoding.UTF8.GetBytes(_context);
                des.Key = ASCIIEncoding.ASCII.GetBytes(_key);
                des.IV = ASCIIEncoding.ASCII.GetBytes(_key);
                System.IO.MemoryStream ms = new System.IO.MemoryStream();
                using (CryptoStream cs = new CryptoStream(ms, des.CreateEncryptor(), CryptoStreamMode.Write))
                {
                    cs.Write(inputByteArray, 0, inputByteArray.Length);
                    cs.FlushFinalBlock();
                    cs.Close();
                }
                string str = Convert.ToBase64String(ms.ToArray());
                ms.Close();
                return str;
            }
        }

        /// <summary>
        /// 进行DES解密。
        /// </summary>
        /// <param name="context">要解密的以Base64</param>
        /// <param name="sKey">密钥，且必须为8位。</param>
        /// <returns>已解密的字符串。</returns>
        public static string DESDecrypt(string _context, string _key = "")
        {
            byte[] inputByteArray = Convert.FromBase64String(_context);
            using (DESCryptoServiceProvider des = new DESCryptoServiceProvider())
            {
                _key = string.IsNullOrEmpty(_key) ? "platform" : _key;

                des.Key = ASCIIEncoding.ASCII.GetBytes(_key);
                des.IV = ASCIIEncoding.ASCII.GetBytes(_key);
                System.IO.MemoryStream ms = new System.IO.MemoryStream();
                using (CryptoStream cs = new CryptoStream(ms, des.CreateDecryptor(), CryptoStreamMode.Write))
                {
                    cs.Write(inputByteArray, 0, inputByteArray.Length);
                    cs.FlushFinalBlock();
                    cs.Close();
                }
                string str = Encoding.UTF8.GetString(ms.ToArray());
                ms.Close();
                return str;
            }
        }
    }
}
