﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>13</LangVersion>
    </PropertyGroup>

    <ItemGroup>
      <None Update="ImportDeliveryKeyField.xlsx">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\iPlatformExtension.Common\iPlatformExtension.Common.csproj" />
      <ProjectReference Include="..\iPlatformExtension.Repository\iPlatformExtension.Repository.csproj" />
    </ItemGroup>

</Project>
