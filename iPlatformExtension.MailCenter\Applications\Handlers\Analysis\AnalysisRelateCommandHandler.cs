﻿using System.Text.RegularExpressions;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.MailCenter.Applications.Commands.Analysis;
using iPlatformExtension.MailCenter.Applications.Models.Analysis;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Attributes;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Analysis
{
    /// <summary>
    /// 解析关联
    /// </summary>
    internal sealed class AnalysisRelateCommandHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IFreeSql<PlatformFreeSql> platformFreeSql,
        EntityTypeInfoProvider entityTypeInfoProvider,
        IMailCorrelativeRepository mailCorrelativeRepository
    ) : IRequestHandler<AnalysisRelateCommand>
    {
        public async Task Handle(AnalysisRelateCommand request, CancellationToken cancellationToken)
        {
            // 定义正则表达式模式：2位大写字母 + 8位数字 + 2位大写字母
            var pattern = @"[A-Z]{2}\d{8}[A-Z]{2}";
            var regex = new Regex(pattern);

            // 从邮件主题和正文中提取匹配的字符串
            var matches = new HashSet<string>();

            if (!string.IsNullOrEmpty(request.MailAnalysis.MailSubject))
            {
                matches.UnionWith(
                    regex.Matches(request.MailAnalysis.MailSubject).Select(m => m.Value)
                );
            }

            if (!string.IsNullOrEmpty(request.MailAnalysis.MailHtmlBody))
            {
                matches.UnionWith(
                    regex.Matches(request.MailAnalysis.MailHtmlBody).Select(m => m.Value)
                );
            }

            if (request.MailAnalysis.MailAttachments?.Count > 0)
            {
                matches.UnionWith(
                    request.MailAnalysis.MailAttachments.SelectMany(m =>
                        regex.Matches(m.FileName).Select(match => match.Value)
                    )
                );
            }

            if (matches.Count == 0)
                return;
            //获取案件信息
            var caseInfos = await platformFreeSql
                .Select<CaseInfo>()
                .Where(it => matches.Contains(it.Volume))
                .ToListAsync(it => it.Id, cancellationToken);
            if (caseInfos is null || caseInfos.Count == 0)
                return;

            //获取映射配置
            var mapConfigs = await freeSql
                .Select<MapConfig>()
                .WithLock()
                .Where(it => it.IsEnabled == SysEnum.Status.Enable.GetHashCode())
                .ToListAsync(cancellationToken);
            var ctrlProcId = new List<string>();
            foreach (var mapConfig in mapConfigs)
            {
                //获取映射字段
                var scopeList = mapConfig
                    .Scope.Split(',')
                    .Select(x => Enum.Parse<SysEnum.ParseScope>(x))
                    .ToArray();

                //获取映射值和实体类型信息
                var mapValue = mapConfig.MapValue;
                var entityTypeInfo = entityTypeInfoProvider.Get(typeof(MailAnalysis));

                foreach (var scope in scopeList)
                {
                    var tableName = scope.GetTableName<TableNameAttribute>();
                    if (ctrlProcId.Contains(mapConfig.CtrlProcId))
                        continue;

                    if (tableName is null)
                    {
                        // 处理普通属性
                        if (
                            CheckPropertyMatch(
                                scope.ToString(),
                                request.MailAnalysis,
                                entityTypeInfo,
                                mapValue
                            )
                        )
                        {
                            ctrlProcId.Add(mapConfig.CtrlProcId);
                        }
                    }
                    else
                    {
                        // 处理表属性
                        if (
                            CheckTablePropertyMatch(
                                scope.ToString(),
                                request.MailAnalysis,
                                entityTypeInfo,
                                mapValue
                            )
                        )
                        {
                            ctrlProcId.Add(mapConfig.CtrlProcId);
                        }
                    }
                }
            }

            // 检查普通属性是否匹配
            bool CheckPropertyMatch(
                string scopeName,
                MailAnalysis mailAnalysis,
                EntityTypeInfo entityTypeInfo,
                string mapValue
            )
            {
                foreach (var entityPropertyInfo in entityTypeInfo.EntityPropertyInfos)
                {
                    if (scopeName != entityPropertyInfo.PropertyName)
                        continue;
                    var value = entityPropertyInfo.Get?.Invoke(mailAnalysis)?.ToString();
                    return value != null && value.Contains(mapValue);
                }
                return false;
            }

            // 检查表属性是否匹配
            bool CheckTablePropertyMatch(
                string scopeName,
                MailAnalysis mailAnalysis,
                EntityTypeInfo entityTypeInfo,
                string mapValue
            )
            {
                foreach (var entityPropertyInfo in entityTypeInfo.EntityPropertyInfos)
                {
                    var invoke = entityPropertyInfo.Get?.Invoke(mailAnalysis);
                    if (invoke is null)
                        continue;

                    if (invoke is IEnumerable<object> list)
                    {
                        if (list.Any(item => CheckObjectPropertyValue(item, scopeName, mapValue)))
                        {
                            return true;
                        }
                    }
                    else if (CheckObjectPropertyValue(invoke, scopeName, mapValue))
                    {
                        return true;
                    }
                }
                return false;
            }

            // 检查对象属性值是否匹配
            bool CheckObjectPropertyValue(object obj, string propertyName, string expectedValue)
            {
                var properties = obj.GetType().GetProperties();
                foreach (var property in properties)
                {
                    if (propertyName != property.Name)
                        continue;
                    var value = property.GetValue(obj)?.ToString();
                    return value != null && value.Contains(expectedValue);
                }
                return false;
            }
            var caseProcInfos = await platformFreeSql
                .Select<CaseProcInfo>()
                .WithLock()
                .Where(it => caseInfos.Contains(it.CaseId) && ctrlProcId.Contains(it.CtrlProcId))
                .ToListAsync(cancellationToken);
            if (caseProcInfos is null || caseProcInfos.Count == 0)
                return;

            // 按CaseId分组,找出只有一个CtrlProcId的组
            var groupedProcs = caseProcInfos
                .GroupBy(x => x.CaseId)
                .Where(g => g.Count() == 1)
                .SelectMany(g => g)
                .ToList();

            if (groupedProcs.Count > 0)
            {
                var mailCorrelatives = groupedProcs.Select(p => new MailCorrelative
                {
                    Id = Guid.NewGuid().ToString(),
                    CorrelateType = SysEnum.CorrelateType.Proc.ToString(),
                    CreateBy = "52c742fa-76d4-4e5d-b3ad-c51841d576dd",
                    CreateTime = DateTime.Now,
                    MailId = request.MailAnalysis.MailId,
                    ObjId = p.ProcId,
                });
                await mailCorrelativeRepository.InsertAsync(mailCorrelatives, cancellationToken);
            }
        }
    }
}
