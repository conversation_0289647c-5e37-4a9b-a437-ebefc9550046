using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_delegation_letter_config", DisableSyncStructure = true)]
	public partial class BasDelegationLetterConfig {

		[ Column(Name = "delegation_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DelegationId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "apply_type_id", StringLength = 1500)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "belong_company", StringLength = 1500)]
		public string BelongCompany { get; set; }

		[ Column(Name = "case_direction", StringLength = 1500)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_type_id", StringLength = 10)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "country_id", StringLength = 1500)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 1500)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "ctrl_proc_property", StringLength = 1500)]
		public string CtrlProcProperty { get; set; }

		[ Column(Name = "is_provide_template")]
		public bool? IsProvideTemplate { get; set; } = true;

		[ Column(Name = "manage_company", StringLength = 1500)]
		public string ManageCompany { get; set; }

		[ Column(Name = "pct_enter")]
		public bool PctEnter { get; set; } = false;

		[ Column(Name = "remark", StringLength = 150)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
