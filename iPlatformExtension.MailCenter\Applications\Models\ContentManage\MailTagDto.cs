﻿using iPlatformExtension.Model.Enum;

namespace iPlatformExtension.MailCenter.Applications.Models.ContentManage
{
    public class MailTagDto
    {
        /// <summary>
        /// 标签Id(留空为新增,否则为更新)
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 标签名称
        /// </summary>
        public string LabelName { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Seq { get; set; }

        /// <summary>
        /// 邮件类型:Receive:收件标签,Send:发件标签
        /// </summary>
        public string MailType { get; set; } = SysEnum.MailType.Receive.ToString();
    }
}
