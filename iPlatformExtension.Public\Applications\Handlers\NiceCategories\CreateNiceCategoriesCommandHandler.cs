﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Commands.NiceCategories;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.NiceCategories;

internal sealed class CreateNiceCategoriesCommandHandler(IFreeSql freeSql, IMediator mediator)
    : IRequestHandler<CreateNiceCategoriesCommand>
{
    public async Task Handle(CreateNiceCategoriesCommand request, CancellationToken cancellationToken)
    {
        var versionId = Guid.NewGuid().ToString();
        var itemsVersion = new BasTrademarkItemsVersion()
        {
            CreateUserId = request.OperatorId,
            VersionId = versionId,
            CreateTime = DateTime.Now,
            IsDefault = true,
            IsEnabled = true,
            StartDate = DateTime.Now,
            UpdateTime = DateTime.Now,
            UpdateUserId = request.OperatorId,
            Seq = 0,
            VersionName = "权大师尼斯分类"
        };

        await freeSql.Insert(itemsVersion).ExecuteAffrowsAsync(cancellationToken);

        await mediator.Send(new InsertNewCategoriesCommand(versionId, request.NiceCategoryInfos, request.OperatorId),
            cancellationToken);
    }
    
    
}