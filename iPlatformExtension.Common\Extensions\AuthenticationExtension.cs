﻿using System.Security.Claims;
using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Extensions;

public static class AuthenticationExtension
{
    public static string? GetUserName(this ClaimsPrincipal principal) => principal.FindFirstValue(ClaimTypes.Name);
    public static string? GetUserID(this ClaimsPrincipal principal) => principal.FindFirstValue(ClaimTypes.NameIdentifier);
    public static string? GetGivenName(this ClaimsPrincipal principal) => principal.FindFirstValue(ClaimTypes.GivenName);
    
    public static IEnumerable<string?> GetRoleIds(this ClaimsPrincipal principal) => principal.FindAll("RoleId").Select(claim => claim.Value);

    public static bool IsInRole(this ClaimsPrincipal principal, string roleId) =>
        principal.HasClaim(claim => claim.Type == "RoleId" && claim.Value == roleId);

    public static async ValueTask<AuthenticateResult?> ValidateUserAsync(this BladeAuthenticatedContext context)
    {
        var username = context.Principal?.FindFirstValue("user_name");
        if (username is null)
            context.Fail("token 中缺少有效用户信息[user_name]");
        else
        {
            var userCache = context.HttpContext.RequestServices
                .GetRequiredService<ICacheableRepository<string, UserInfoDto>>();
            var userInfo = await userCache.GetCacheValueAsync(username);
            if (userInfo is not null)
            {
                var principal = context.Principal!;
                
                var identity = new ClaimsIdentity([
                    new Claim(ClaimTypes.NameIdentifier, userInfo.UserId),
                    new Claim(ClaimTypes.Name, userInfo.UserName),
                    new Claim(ClaimTypes.GivenName, userInfo.CnName),
                    new Claim(ClaimTypes.GroupSid, userInfo.DeptId),
                    new Claim(ClaimTypes.Expiration, principal.FindFirstValue("exp") ?? principal.FindFirstValue(ClaimTypes.Expiration) ?? DateTimeOffset.Now.Add(TimeSpan.FromMinutes(5)).ToUnixTimeSeconds().ToString())
                ], BladeAuthOptions.SchemeName);
                identity.AddClaims(principal.FindAll(claim => "roleTypes".Equals(claim.Type, StringComparison.OrdinalIgnoreCase)).Select(claim => new Claim(ClaimTypes.Role, claim.Value)));
                context.Principal!.AddIdentity(identity);
                
                var cache = context.HttpContext.RequestServices.GetService<IRedisCache<RedisCacheOptionsBase>>();
                if (cache is not null && context.Options.SaveToken)
                {
                    await cache.SetCacheValueAsync(BladeAuthOptions.SchemeName,
                        context.Properties.GetTokenValue(BladeAuthOptions.SchemeName), identity, true);
                }
                context.Success();
            }
            else
            {
                context.Fail(new ApplicationException($"找不到对应的用户[{username}]"));
            }
        }
    
        return context.Result;
    }
    
    public static async Task ValidateUserAsync(this TokenValidatedContext context)
    {
        var userId = context.Principal?.FindFirstValue(ClaimTypes.NameIdentifier);
        if (userId is null)
            context.Fail("token 中缺少有效用户信息[sub]");
        else
        {
            var userCache = context.HttpContext.RequestServices
                .GetRequiredService<ICacheableRepository<string, SysUserInfo>>();
            var userInfo = await userCache.GetCacheValueAsync(userId);
            if (userInfo is not null)
            {
                var platformIdentity = new ClaimsIdentity([
                    new Claim(ClaimTypes.NameIdentifier, userInfo.UserId),
                    new Claim(ClaimTypes.Name, userInfo.UserName),
                    new Claim(ClaimTypes.GivenName, userInfo.CnName),
                    new Claim(ClaimTypes.GroupSid, userInfo.DeptId)
                ], PlatformAuthOptions.SchemeName);
                platformIdentity.AddClaims(userInfo.Roles!.Select(roleInfo => new Claim(ClaimTypes.Role, roleInfo.RoleName)));
                platformIdentity.AddClaims(userInfo.Roles!.Select(roleInfo => new Claim(nameof(roleInfo.RoleId), roleInfo.RoleId)));
                platformIdentity.AddClaims(userInfo.Roles!.Select(roleInfo => new Claim(nameof(roleInfo.RoleCode), roleInfo.RoleCode)));

                var cache = context.HttpContext.RequestServices.GetService<IRedisCache<RedisCacheOptionsBase>>();
                if (cache is not null && context.Options.SaveToken)
                {
                    await cache.SetCacheValueAsync(PlatformAuthOptions.SchemeName, context.SecurityToken.UnsafeToString(), platformIdentity, true);
                    
                }
                
                context.Principal!.AddIdentity(platformIdentity);
                context.Success();
            }
            else
            {
                context.Fail(new NotFoundException(userId, "用户"));
            }
        }
        
    }

    public static Task OnChallengeAsync(this JwtBearerChallengeContext context)
    {
        if (context.Response.HasStarted)
        {
            context.HandleResponse();
        }
        
        return Task.CompletedTask;
    }

    public static Task AuthenticateFailedAsync(this AuthenticationFailedContext context)
    {
        context.Fail(context.Exception);
        context.HttpContext.Features.Set<IAuthenticateResultFeature>(new BladeAuthenticationResultFeature(context.Result));
        return Task.CompletedTask;
    }

    public static async Task GetTokenAsync(this MessageReceivedContext context)
    {
        const string accessToken = "accessToken";
        const string accessToken1 = "access_token";
        var request = context.HttpContext.Request;

        if (!string.IsNullOrEmpty(request.Headers.Authorization))
        {
            context.Token = request.Headers.Authorization;
        }
        else if (!string.IsNullOrEmpty(request.Query[accessToken1]))
        {
            context.Token = request.Query[accessToken1];
        }
        else if (!string.IsNullOrEmpty(request.Query[accessToken]))
        {
            context.Token = request.Query[accessToken];
        }

        if (context.Token?.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase) ?? false)
        {
            context.Token = context.Token["Bearer ".Length..].Trim();
        }

        if (string.IsNullOrEmpty(context.Token))
        {
            context.NoResult();
            return;
        }

        var cache = context.HttpContext.RequestServices.GetService<IRedisCache<RedisCacheOptionsBase>>();
        if (cache is not null)
        {
            var identity = await cache.GetCacheValueAsync<string, ClaimsIdentity>(PlatformAuthOptions.SchemeName, context.Token);
            var expires = identity?.FindFirst(ClaimTypes.Expiration)?.Value;

            if (long.TryParse(expires, out var expiration) && DateTimeOffset.UtcNow.ToUnixTimeSeconds() < expiration)
            {
                context.Success();
            }
            else
            {
                await cache.RemoveCacheValueAsync(PlatformAuthOptions.SchemeName, context.Token);
            }
        }
    }

    public static string? GetTokenValue(this HttpContext httpContext, string tokenName) =>
        httpContext.Features.Get<IAuthenticateResultFeature>()?.AuthenticateResult?.Properties
            ?.GetTokenValue(tokenName);

    public static string? GetBladeAuthToken(this HttpContext httpContext)
    {
        return GetTokenValue(httpContext, BladeAuthOptions.SchemeName);
    }

    public static string? GetPlatformAuthToken(this HttpContext httpContext)
    {
        return GetTokenValue(httpContext, "access_token");
    }

    /// <summary>
    /// 替换默认的认证方案提供者
    /// </summary>
    /// <param name="builder">身份认证构建者</param>
    /// <typeparam name="TAuthenticationSchemeProvider">新的认证方案提供者类型</typeparam>
    /// <returns>身份认证构建者</returns>
    public static AuthenticationBuilder ReplaceAuthenticationSchemeProvider<TAuthenticationSchemeProvider>(
        this AuthenticationBuilder builder)
        where TAuthenticationSchemeProvider : class, IAuthenticationSchemeProvider
    {
        builder.Services.AddSingleton<IAuthenticationSchemeProvider, TAuthenticationSchemeProvider>();
        return builder;
    }
}