﻿using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.MailCenter.Applications.Queries.File;
using iPlatformExtension.Model.Dto;
using MediatR;
using SharpCompress.Common;

namespace iPlatformExtension.MailCenter.Applications.Handlers.File
{
    public class FileHandler : IRequestHandler<GetFileBytesQuery, byte[]?>
    {

        public async Task<byte[]?> Handle(GetFileBytesQuery request, CancellationToken cancellationToken)
        {
            string url = request.fileName; //"http://example.com/file.zip"; // 你想要下载的文件URL

            using (HttpClient client = new HttpClient())
            {
                try
                {
                    // 发起请求并直接获取响应的流
                    using (HttpResponseMessage response = await client.GetAsync(url, HttpCompletionOption.ResponseHeadersRead))
                    using (Stream streamToReadFrom = await response.Content.ReadAsStreamAsync())
                    {
                        if (response.IsSuccessStatusCode)
                        {
                            // 将流复制到内存流中
                            using (MemoryStream memoryStream = new MemoryStream())
                            {
                                await streamToReadFrom.CopyToAsync(memoryStream);
                                byte[] fileBytes = memoryStream.ToArray();

                                // 此时fileBytes包含了文件的数据，你可以根据需要进一步处理这些数据
                                Console.WriteLine($"Downloaded file with {fileBytes.Length} bytes.");
                                // 设置 Content-Disposition 头部信息，以附件形式下载，并指定下载的文件名为参数传递的 fileName
                                //return System.IO.File(fileBytes, "application/octet-stream", fileName);
                                return fileBytes;
                            }
                        }
                    }
                }
                catch (HttpRequestException e)
                {
                    Console.WriteLine($"Request error: {e.Message}");
                }
            }
            return null;
        }
    }
}
