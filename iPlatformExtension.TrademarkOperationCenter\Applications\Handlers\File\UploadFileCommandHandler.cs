﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;
using MediatR;
using Microsoft.AspNetCore.Http.Json;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File;

internal sealed class UploadFileCommandHandler(
    IHttpClientFactory httpClientFactory,
    IOptionsMonitor<JsonOptions> options)
    : IRequestHandler<UploadFileCommand, int>
{
    public async Task<int> Handle(UploadFileCommand request, CancellationToken cancellationToken)
    {
        var httpClient = httpClientFactory.CreateClient("iPlatformExtension.Public");
        var jsonOptions = options.CurrentValue;
        var content = new MultipartFormDataContent();
        content.Add(new StringContent(request.ServerPath), nameof(request.ServerPath));
        content.Add(new StreamContent(System.IO.File.OpenRead(request.FileName)), "file", Path.GetFileName(request.FileName));
        content.Add(new StringContent(Path.GetFileName(request.FileName)), nameof(request.FileName));

        var response = await httpClient.PostAsync("/file", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadFromJsonAsync<ResultData<int>>(jsonOptions.SerializerOptions, cancellationToken);
        return result?.Data ?? throw new ApplicationException(result?.Message ?? "上传文件失败");
    }
}