﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.File
{
    /// <summary>
    /// 任务申请人文件列表Dto
    /// </summary>
    public class ApplicantFileListDto
    {
        /// <summary>
        /// caseFile文件id
        /// </summary>
        public string CaseFileId { get; set; } = default!;

        /// <summary>
        /// 文件id
        /// </summary>
        public int FileNo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FileId { get; set; } = default!;

        /// <summary>
        /// 关联的对象id
        /// </summary>
        public string ObjectId { get; set; } = default!;

        /// <summary>
        /// 文件类型
        /// </summary>
        public string? FileType { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; } = default!;

        /// <summary>
        /// 文件描述
        /// </summary>
        public string? FileDescription { get; set; }

        /// <summary>
        /// 文件描述id
        /// </summary>
        public string? FileDescriptionId { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        public DateTime UploadTime { get; set; }

        /// <summary>
        /// 上传者
        /// </summary>
        public string Uploader { get; set; } = default!;
        /// <summary>
        /// 文件Url
        /// </summary>
        public string Url { get; set; } = default!;
    }
}
