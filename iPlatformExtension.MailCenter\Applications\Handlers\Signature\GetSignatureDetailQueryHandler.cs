﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.Signature;
using iPlatformExtension.MailCenter.Applications.Queries.Signature;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Signature
{
    /// <summary>
    /// 获取签名详情查询处理器
    /// </summary>
    internal sealed class GetSignatureDetailQueryHandler(
        IMailSignatureRepository mailSignatureRepository,
        IHttpContextAccessor httpContextAccessor
    ) : IRequestHandler<GetSignatureDetailQuery, GetSignatureDetailDto>
    {
        public async Task<GetSignatureDetailDto> Handle(
            GetSignatureDetailQuery request,
            CancellationToken cancellationToken
        )
        {
            // 获取当前用户ID
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;

            // 查询签名详情
            var signature = await mailSignatureRepository
                .Where(it => it.Id == request.Id && it.UserId == userId)
                .FirstAsync(cancellationToken);

            if (signature == null)
            {
                return null;
            }

            // 返回签名详情
            return new GetSignatureDetailDto
            {
                Id = signature.Id,
                Name = signature.Name ?? string.Empty,
                Content = signature.Content ?? string.Empty,
                Language = signature.Language ?? string.Empty,
                CreateTime = signature.CreateTime,
                UpdateTime = signature.UpdateTime
            };
        }
    }
}
