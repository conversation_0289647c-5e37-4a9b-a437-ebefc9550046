﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    /// <summary>
    /// 获取解析规则详情处理者
    /// </summary>
    internal sealed class GetAnalysisRuleDetailQueryHandler(IFreeSql<MailCenterFreeSql> freeSql)
        : IRequestHandler<GetAnalysisRuleDetailQuery, IEnumerable<GetAnalysisRuleDetailDto>>
    {
        public async Task<IEnumerable<GetAnalysisRuleDetailDto>> Handle(
            GetAnalysisRuleDetailQuery request,
            CancellationToken cancellationToken
        )
        {
            return await freeSql
                .Select<MailConfigFilter>()
                .WithLock()
                .Where(it => it.ConfigId == request.ConfigId)
                .OrderBy(it => it.FilterHead)
                .ToListAsync(
                    it => new GetAnalysisRuleDetailDto(
                        it.FilterId,
                        it.FilterHead,
                        it.FilterType,
                        it.FilterValue,
                        it.Seq
                    ),
                    cancellationToken
                );
        }
    }
}
