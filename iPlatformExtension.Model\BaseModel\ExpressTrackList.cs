using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "express_track_list", DisableSyncStructure = true)]
	public partial class ExpressTrackList {

		[ Column(Name = "list_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ListId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_uesr_id", StringLength = 50)]
		public string CreateUesrId { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; }

		[ Column(Name = "track_no", StringLength = 50)]
		public string TrackNo { get; set; }

		[ Column(Name = "track_title", StringLength = 500)]
		public string TrackTitle { get; set; }

		[ Column(Name = "track_type", StringLength = 50)]
		public string TrackType { get; set; }

	}

}
