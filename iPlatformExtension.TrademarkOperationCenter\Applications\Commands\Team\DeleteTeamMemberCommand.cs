﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;

/// <summary>
/// 删除团队成员命令
/// </summary>
/// <param name="TeamMemberId">团队成员主键</param>
public record DeleteTeamMemberCommand([FromQuery] params string[] TeamMemberId) : IRequest,IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

