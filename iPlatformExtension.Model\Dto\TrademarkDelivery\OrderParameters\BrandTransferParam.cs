﻿namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;

/// <summary>
/// 商标信息
/// </summary>
public class BrandTransferParam
{
    /// <summary>
    /// 注册号
    /// </summary>
    public string? brandRegisterNo { get; set; }

    /// <summary>
    /// 大类编码
    /// </summary>
    public int? firstCgNo { get; set; }

    /// <summary>
    /// 商标名称
    /// </summary>
    public string? brandName { get; set; }

    /// <summary>
    /// 商标申请人名称
    /// </summary>
    public string applicantName { get; set; }

    /// <summary>
    /// 商标流程状态
    /// </summary>
    public string categoryFlowStateName { get; set; }

    /// <summary>
    /// 大类
    /// </summary>
    public int? intCls { get; set; }

    /// <summary>
    /// 大类编码名称
    /// </summary>
    public string? firstCgName { get; set; }

    public Brandteam[]? brandTeam { get; set; }

    public string? imageUrl { get; set; }

    /// <summary>
    /// 小项个数
    /// </summary>
    public int? number { get; set; }
}