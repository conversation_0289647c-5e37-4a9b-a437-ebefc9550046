using System.Reflection;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Mediator.PipelineBehaviors;
using iPlatformExtension.Mail.GrpcServices;
using Microsoft.OpenApi.Models;
using Nacos.AspNetCore.V2;
using NLog.Web;

var builder = WebApplication.CreateBuilder(args);
var logging = builder.Logging;
var environment = builder.Environment;
var configuration = builder.Configuration;

logging.ClearProviders();
logging.AddSimpleConsole(options => options.IncludeScopes = true);
logging.AddNLogWeb(new NLogAspNetCoreOptions
{
    RemoveLoggerFactoryFilter = false,
    LoggingConfigurationSectionName = "NLog"
});

if (!environment.IsLocal())
{
    configuration.AddNacosV2Configuration(configuration.GetSection("nacos"));
    builder.Services.AddNacosAspNet(configuration);
    builder.Services.AddNacosServiceDiscovery(options => options.AllowedSchemes = ["http", "https"]);
}
else
{
    configuration.AddJsonFile($"clients.{environment.EnvironmentName}.json", true, true);
}

builder.AddSmtpClients();

builder.Services.AddEndpointsApiExplorer();
if (!environment.IsProduction())
{
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = builder.Environment.ApplicationName,
            Version = "2.8.2.RELEASE-v2.7.11",
            Description = "邮件服务"
        });
        var documentPath = Path.Combine(AppContext.BaseDirectory, $"{environment.ApplicationName}.xml");
        c.IncludeXmlComments(documentPath, true);

        var modelPath = Path.Combine(AppContext.BaseDirectory, "iPlatformExtension.Model.xml");
        c.IncludeXmlComments(modelPath, true);
        c.OrderActionsBy(o => o.RelativePath);
    });
}

builder.Services.AddGrpc(option =>
{
    option.MaxReceiveMessageSize = 50 * 1024 * 1024;
    option.MaxSendMessageSize = 50 * 1024 * 1024;
});
builder.Services.AddObjectPools();
builder.Services.AddPlatformFreeSql(configuration.GetConnectionString("Default") ?? string.Empty)
    .ConfigureGlobalCurdLogging("Logging:FreeSql:Curd");
builder.Services.AddDataService();
builder.Services.AddCache().ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
{
    options.ConnectionString = configuration.GetConnectionString("Redis");
    options.Converters.Add(new ClaimsIdentityConverter());
});
builder.Services.AddMediatR(serviceConfiguration =>
{
    serviceConfiguration.Lifetime = ServiceLifetime.Scoped;
    serviceConfiguration.AddOpenBehavior(typeof(DefaultFreeSqlUnitOfWorkPipelineBehavior<,>));
    serviceConfiguration.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
});

var app = builder.Build();

app.UseExceptionHandler("/Error");

app.UseW3CTraceResponse();

if (!environment.IsProduction())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.HandleUnsuccessfulResponse();

app.MapGet("/test", () => "test success!");

app.MapGrpcService<NotificationService>();

app.Run();
