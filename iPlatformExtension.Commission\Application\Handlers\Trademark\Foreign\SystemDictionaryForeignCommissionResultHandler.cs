﻿using iPlatformExtension.Commission.Application.Notifications.Trademark.Foreign;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class SystemDictionaryForeignCommissionResultHandler(ISystemDictionaryRepository systemDictionaryRepository) 
    : INotificationHandler<ForeignCommissionResultNotification>
{
    public async Task Handle(ForeignCommissionResultNotification notification, CancellationToken cancellationToken)
    {
        var commission = notification.ForeignTrademarkBonus;
        commission.CtrlProcMarkCn = await systemDictionaryRepository.GetChineseValueAsync(SystemDictionaryName.CtrlProcMark, commission.CtrlProcMark);
        commission.Status = await systemDictionaryRepository.GetChineseValueAsync(SystemDictionaryName.ProcStatus, commission.ProcStatusId);
    }
}