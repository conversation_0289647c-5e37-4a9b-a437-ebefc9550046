﻿using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using iPlatformExtension.Common.Extensions;
using Microsoft.AspNetCore.Http;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Common.Helper;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class SaveMailTagHandler(IMailTagRepository mailTagRepository, IHttpContextAccessor httpContextAccessor) : IRequestHandler<SaveMailTagCommand>
    {
        public async Task Handle(SaveMailTagCommand request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            ArgumentNullException.ThrowIfNull(userId);

            List<MailTag> tagsAdd = new List<MailTag>();
            List<MailTag> tagsUpdate = new List<MailTag>();
            foreach (var item in request.Tags)
            {
                if (!string.IsNullOrEmpty(item.Id))
                {
                    var tag = await mailTagRepository.GetAsync(item.Id);
                    tag.Seq = item.Seq;
                    tag.LabelName = item.LabelName;
                    tagsUpdate.Add(tag);
                }
                else
                {
                    tagsAdd.Add(new MailTag
                    {
                        Id = Guid.NewGuid().ToString(),
                        LabelName = item.LabelName,
                        MailType = item.MailType,
                        Seq = item.Seq,
                        UserId = userId
                    });
                }

            }
            if (tagsAdd.Any())
            {
                await mailTagRepository.InsertAsync(tagsAdd);
            }
            if (tagsUpdate.Any())
            {
                await mailTagRepository.UpdateAsync(tagsUpdate);
            }
        }
    }
}
