﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Nice;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Nice;

internal sealed class CaseNiceClassesQueryHandler(IFreeSql freeSql) : IRequestHandler<CaseNiceClassesQuery, IEnumerable<string>>
{
    public async Task<IEnumerable<string>> <PERSON>le(CaseNiceClassesQuery request, CancellationToken cancellationToken)
    {
        var niceClassesValue = await freeSql.Select<CaseInfo>(request.CaseId)
            .ToOneAsync(info => info.TrademarkClass, cancellationToken);

        return niceClassesValue.Split(';', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries);
    }
}