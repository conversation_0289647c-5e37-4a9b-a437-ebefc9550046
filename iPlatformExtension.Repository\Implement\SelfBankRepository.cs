﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class SelfBankRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    DefaultRedisCache redisCache,
    CacheExpirationToken<BasSelfBank> expirationToken)
    : BaseRepository<BasSelfBank, string>(freeSql), ISelfBankRepository
{
    IMemoryCache ICacheableRepository<string, BasSelfBank>.MemoryCache => memoryCache;

    CacheExpirationToken<BasSelfBank> ICacheableRepository<string, BasSelfBank>.ExpirationToken => expirationToken;
    
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}