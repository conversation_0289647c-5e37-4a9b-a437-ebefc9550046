﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;

/// <summary>
/// 团队列表搜索
/// </summary>
/// <param Name="Name">名称/团队名称</param>
/// <param IsExclusive="IsExclusive">是否专属</param>
/// <param IsEffect="IsEffect">是否有效</param>
public record TeamQuery(string? Name, bool? IsExclusive, bool? IsEffect) : PageModel, IRequest<IEnumerable<TeamDto>>;
