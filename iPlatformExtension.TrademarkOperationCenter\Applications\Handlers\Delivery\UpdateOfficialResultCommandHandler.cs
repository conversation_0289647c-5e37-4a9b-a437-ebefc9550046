﻿using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class UpdateOfficialResultCommandHandler(
    IDeliveryInfoRepository deliveryInfoRepository,
    IMediator mediator)
    : IRequestHandler<UpdateOfficialResultCommand, bool>
{
    public async Task<bool> Handle(UpdateOfficialResultCommand request, CancellationToken cancellationToken)
    {
        var deliveryInfo = request.DeliveryInfo;
        var result = request.Result;
        var operatorId = request.OperatorId;
        var originalStatus = (DeliveryStatus)(deliveryInfo.Status ?? 0);
        int? displayId = null;

        if (originalStatus >= DeliveryStatus.Complete)
        {
            return false;
        }

        if (result.Success)
        {
            deliveryInfo.IsAuto = true;
            deliveryInfo.Status = (int?) DeliveryStatus.Complete;
            deliveryInfo.DisplayJson = result.DisplayJson;
            deliveryInfo.DeliveryDate = result.DeliveryDate;
            deliveryInfo.OperationResult = true;
            displayId = (await mediator.Send(new InsertDisplayCommand(deliveryInfo.ProcId, result.DisplayJson, operatorId),
                cancellationToken)).Id;
        }
        else
        {
            deliveryInfo.Status = (int?) DeliveryStatus.Ready;
            deliveryInfo.OperationResult = false;
            deliveryInfo.IsAuto = false;
        }

        deliveryInfo.UpdateTime = DateTime.Now;
        deliveryInfo.UpdateUserId = operatorId;

        var updated = await deliveryInfoRepository.UpdateAsync(deliveryInfo, cancellationToken);
        if (updated <= 0)
        {
            throw new ApplicationException("更新失败");
        }

        await mediator.Send(
            new InsertHistoryCommand(
                deliveryInfo.ProcId,
                result.GetOperationDescriptionWithResult(),
                operatorId,
                result.Message,
                displayId), cancellationToken);

        return result.Success;
    }
}