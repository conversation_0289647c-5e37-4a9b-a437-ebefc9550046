﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 账款操作枚举。
/// 0：保存账单
/// 1：更新请款日期
/// 2：撤销请款
/// 3：划拨
/// 4：撤销划拨
/// 5：保存发票号
/// 6：撤销开票
/// 7：坏账
/// 8：不到款
/// 9：撤销不到款
/// 10：标记为坏账处理中
/// 11：取消坏账
/// 12：标记为不到款处理中
/// </summary>
public enum FinanceOperation : ushort
{
    /// <summary>
    /// 保存账单
    /// </summary>
    SaveBill,
    
    /// <summary>
    /// 提交或者审核结束后。
    /// 更新请款日期。
    /// </summary>
    RequestOrConfirmBill,
    
    /// <summary>
    /// 撤销请款
    /// </summary>
    CancelRequisition,
    
    /// <summary>
    /// 划拨
    /// </summary>
    Allocate,
    
    /// <summary>
    /// 撤销划拨
    /// </summary>
    CancelAllocation,
    
    /// <summary>
    /// 保存发票号
    /// </summary>
    Invoicing,
    
    /// <summary>
    /// 撤销开票
    /// </summary>
    CancelInvoicing,
    
    /// <summary>
    /// 坏账
    /// </summary>
    BadDept,
    
    /// <summary>
    /// 不到款
    /// </summary>
    NotPaid,
    
    /// <summary>
    /// 撤销不到款
    /// </summary>
    CancelNotPaid,
    
    /// <summary>
    /// 坏账处理中
    /// </summary>
    LockBadDept,
    
    /// <summary>
    /// 取消坏账
    /// </summary>
    CancelBadDept,
    
    /// <summary>
    /// 锁定为坏账处理中
    /// </summary>
    LockToNotPaid
}