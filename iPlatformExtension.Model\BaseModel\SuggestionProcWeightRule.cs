﻿
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 建议类任务权值规则
	/// </summary>
	[Table(Name = "suggestion_proc_weight_rule", DisableSyncStructure = true)]
	public partial class SuggestionProcWeightRule {

		/// <summary>
		/// 主键
		/// </summary>
		[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
		public int Id { get; set; }

		/// <summary>
		/// 逻辑运算
		/// </summary>
		[Column(Name = "logic", StringLength = 10, IsNullable = false, MapType = typeof(string))]
		public SuggestionProcWeightLogic Logic { get; set; }

		/// <summary>
		/// 任务名称id
		/// </summary>
		[Column(Name = "source_ctrl_proc_id", StringLength = 50, IsNullable = false)]
		public string SourceCtrlProcId { get; set; } = null!;

		/// <summary>
		/// 权重
		/// </summary>
		[Column(Name = "proc_point", DbType = "decimal(5,2)")]
		public decimal ProcPoint { get; set; }

		/// <summary>
		/// 关联的配置
		/// </summary>
		[Navigate(nameof(SuggestionProcPointConfig.RuleId))]
		public virtual ICollection<SuggestionProcPointConfig>? Configs { get; set; }

	}

}
