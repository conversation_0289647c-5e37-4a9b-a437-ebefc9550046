﻿using System.Threading.Channels;
using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;

namespace iPlatformExtension.Commission.Infrastructure.BackgroundServices;

internal sealed class ForeignTrademarkCommissionWeightBackgroundService(
    IServiceScopeFactory serviceScopeFactory, 
    Channel<CreateForeignCommissionWeightCommand> channel,
    ILogger<ForeignTrademarkCommissionWeightBackgroundService> logger) 
    : BackgroundConsumeService<CreateForeignCommissionWeightCommand>(channel, logger, serviceScopeFactory);