﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 商标递交优先权信息
	/// </summary>
	[Table(Name = "trademark_priority_info", DisableSyncStructure = true)]
	public class TrademarkPriorityInfo 
	{

		/// <summary>
		/// 主键
		/// </summary>
		[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
		public int Id { get; set; }

		/// <summary>
		/// 关联的案件id
		/// </summary>
		[Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; } = default!;

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "creation_time")]
		public DateTime CreationTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[Column(Name = "creator", StringLength = 50, IsNullable = false)]
		public string Creator { get; set; } = default!;

		/// <summary>
		/// 接入码
		/// </summary>
		[Column(Name = "das_code", StringLength = 50)]
		public string? DasCode { get; set; }

		/// <summary>
		/// 关联的递交信息id
		/// </summary>
		[Column(Name = "delivery_id")]
		public int DeliveryId { get; set; }

		/// <summary>
		/// 优先权地区
		/// </summary>
		[Column(Name = "priority_area", StringLength = 1)]
		public string? PriorityArea { get; set; }

		/// <summary>
		/// 优先权日
		/// </summary>
		[Column(Name = "priority_date")]
		public DateTime? PriorityDate { get; set; }

		/// <summary>
		/// 案件优先权id
		/// </summary>
		[Column(Name = "priority_id", StringLength = 50, IsNullable = false)]
		public string PriorityId { get; set; } = default!;

		/// <summary>
		/// 优先权号
		/// </summary>
		[Column(Name = "priority_no", StringLength = 50)]
		public string? PriorityNo { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time")]
		public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[Column(Name = "updater", StringLength = 50, IsNullable = false)]
		public string Updater { get; set; } = default!;

		/// <summary>
		/// 我方文号
		/// </summary>
		[Column(Name = "volume", StringLength = 50)]
		public string? Volume { get; set; }

	}

}
