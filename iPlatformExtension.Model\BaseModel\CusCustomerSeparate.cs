using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_customer_separate", DisableSyncStructure = true)]
	public partial class CusCustomerSeparate {

		[ Column(Name = "customer_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CustomerId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "balance_way", StringLength = 50)]
		public string BalanceWay { get; set; }

		[ Column(Name = "corporation", StringLength = 50)]
		public string Corporation { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50, IsNullable = false)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_code", StringLength = 100)]
		public string CustomerCode { get; set; }

		[ Column(Name = "customer_credit", StringLength = 50)]
		public string CustomerCredit { get; set; }

		[ Column(Name = "customer_from", StringLength = 50)]
		public string CustomerFrom { get; set; }

		[ Column(Name = "customer_full_name", StringLength = 2000)]
		public string CustomerFullName { get; set; }

		[ Column(Name = "customer_name", StringLength = 1000)]
		public string CustomerName { get; set; }

		[ Column(Name = "customer_name_en", StringLength = 2000)]
		public string CustomerNameEn { get; set; }

		[ Column(Name = "customer_name_other", StringLength = 300)]
		public string CustomerNameOther { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "email", StringLength = 200)]
		public string Email { get; set; }

		[ Column(Name = "fax", StringLength = 100)]
		public string Fax { get; set; }

		[ Column(Name = "head_user_id1", StringLength = 50)]
		public string HeadUserId1 { get; set; }

		[ Column(Name = "head_user_type", StringLength = 50)]
		public string HeadUserType { get; set; }

		[ Column(Name = "industry", StringLength = 50)]
		public string Industry { get; set; }

		[ Column(Name = "introducer", StringLength = 50)]
		public string Introducer { get; set; }

		[ Column(Name = "introducer_tel", StringLength = 50)]
		public string IntroducerTel { get; set; }

		[ Column(Name = "is_applicant")]
		public bool? IsApplicant { get; set; } = false;

		[ Column(Name = "is_conflict")]
		public bool? IsConflict { get; set; } = false;

		[ Column(Name = "is_cooperation")]
		public bool? IsCooperation { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "is_import")]
		public bool? IsImport { get; set; } = false;

		[ Column(Name = "manage_company", StringLength = 50)]
		public string ManageCompany { get; set; }

		[ Column(Name = "parent_id", StringLength = 50)]
		public string ParentId { get; set; }

		[ Column(Name = "pay_way", StringLength = 50)]
		public string PayWay { get; set; }

		[ Column(Name = "payment_name", StringLength = 50)]
		public string PaymentName { get; set; }

		[ Column(Name = "receive_rule", StringLength = 50)]
		public string ReceiveRule { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "secret_id", StringLength = 50)]
		public string SecretId { get; set; }

		[ Column(Name = "tel", StringLength = 100)]
		public string Tel { get; set; }

		[ Column(Name = "type_id", StringLength = 50)]
		public string TypeId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "website", StringLength = 1000)]
		public string Website { get; set; }

	}

}
