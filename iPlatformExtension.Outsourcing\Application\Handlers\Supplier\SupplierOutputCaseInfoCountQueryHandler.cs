﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Queries.Supplier;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Supplier;

internal sealed class SupplierOutputCaseInfoCountQueryHandler(IFreeSql<PlatformFreeSql> freeSql) 
    : IRequestHandler<SupplierOutputCaseInfoCountQuery, long>
{
    public Task<long> Handle(SupplierOutputCaseInfoCountQuery request, CancellationToken cancellationToken)
    {
        var (manageCompanyId, caseDirection, ctrlProcId, applicationTypeId, (startDate, endDate), foreignAgencyId) = request;
        return freeSql.Select<CaseProcInfo>().WithLock()
            .InnerJoin<CaseInfo>((info, caseInfo) => info.CaseId == caseInfo.Id)
            .Where(info => info.CtrlProcId == ctrlProcId)
            .Where<CaseInfo>(caseInfo => caseInfo.ForeginAgencyId == foreignAgencyId)
            .Where<CaseInfo>(caseInfo => caseInfo.CaseDirection == caseDirection)
            .Where<CaseInfo>(caseInfo => caseInfo.ManageCompany == manageCompanyId)
            .Where<CaseInfo>(caseInfo => caseInfo.ApplyTypeId == applicationTypeId)
            .WhereIf(startDate != null, info => info.SendPartnerDate >= startDate!.Value.ToDateTime(TimeOnly.MinValue))
            .WhereIf(endDate != null, info => info.SendPartnerDate <= endDate!.Value.ToDateTime(TimeOnly.MaxValue))
            .GroupBy(info => info.CaseId)
            .CountAsync(cancellationToken);
    }
}