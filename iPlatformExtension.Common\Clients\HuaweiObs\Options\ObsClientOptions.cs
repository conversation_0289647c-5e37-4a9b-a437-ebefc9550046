﻿namespace iPlatformExtension.Common.Clients.HuaweiObs.Options;

public class ObsClientOptions
{
    public string AccessKey { get; set; } = default!;

    public string SecretKey { get; set; } = default!;

    public string Host { get; set; } = default!;

    public string SecurityToken { get; set; } = string.Empty;

    public string DefaultBucketName { get; set; } = default!;

    public TimeSpan DefaultTemporaryUriExpirationTime { get; set; } = TimeSpan.FromDays(1);

    public void Deconstruct(out string accessKey, out string secretKey, out string securityToken, out string endpoint)
    {
        accessKey = AccessKey;
        secretKey = SecretKey;
        endpoint = Host;
        securityToken = SecurityToken;
    }
    
    public void Deconstruct(out string accessKey, out string secretKey, out string securityToken)
    {
        accessKey = AccessKey;
        secretKey = SecretKey;
        securityToken = SecurityToken;
    }
}