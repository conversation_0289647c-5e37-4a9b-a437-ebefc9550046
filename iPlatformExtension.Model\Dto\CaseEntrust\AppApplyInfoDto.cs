﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using FreeSql.DataAnnotations;


namespace iPlatformExtension.Model.Dto.CaseEntrust
{
    /// <summary>
    /// 开案信息
    /// </summary>
    public class AppApplyInfoDto
    {
        /// <summary>
        /// 我方文号(用于旧案开案)
        /// </summary>
        public string? volume { get; set; }

        /// <summary>
        /// 订单ID
        /// </summary>
        [ Column(StringLength = 50)]
        public string OrderID { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string orderNo { get; set; }

        [Column(Name = "applyCases")]
        [JsonIgnore]
        public List<AppCaseDto> applyCases { get; set; }

        /// <summary>
        /// 开案ID
        /// </summary>
        public string? ApplyId { get; set; }

        /// <summary>
        /// 开案名称
        /// </summary>
        public string? ApplyName { get; set; }

        /// <summary>
        /// 开案号
        /// </summary>
        public string? ApplyNo { get; set; }

        /// <summary>
        /// 类型说明
        /// </summary>
        [ Column(Name = "aspx_code", StringLength = 50)]
        public string AspxCode { get; set; }

        [ Column(Name = "assign_user", StringLength = 50)]
        public string? AssignUser { get; set; }

        /// <summary>
        /// 案源分所
        /// </summary>
        // [Required(ErrorMessage = "案源分所不能为空")]
        public string? BelongCompany { get; set; }

        /// <summary>
        /// 案源地区
        /// </summary>
        // [Required(ErrorMessage = "案源地区不能为空")]
        public string? BelongDistrict { get; set; }

        /// <summary>
        /// 案件类型
        /// </summary>
        [Required(ErrorMessage = "案件类型不能为空")]
        public string CaseTypeId { get; set; } = null!;

        /// <summary>
        /// 创建时间
        /// </summary>
        [ Column(Name = "create_time")]
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        [Required(ErrorMessage = "创建用户不能为空")]
        public string CreateUser { get; set; } = null!;

        /// <summary>
        /// 客户联系人
        /// </summary>
        public string? CustContactId { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        [Required(ErrorMessage = "客户ID不能为空")]
        public string CustomerId { get; set; }

        /// <summary>
        /// （审核人）
        /// </summary>
        [ Column(Name = "disclosure_user", StringLength = 200)]
        public string DisclosureUser { get; set; } = null!;

        /// <summary>
        /// 委案时间
        /// </summary>
        [ Column(Name = "entrust_date")]
        public DateTime? EntrustDate { get; set; }

        /// <summary>
        /// 家族案ID
        /// </summary>
        [ Column(Name = "family_case_id", StringLength = 50)]
        public string? FamilyCaseId { get; set; }

        /// <summary>
        /// 其他家族ID
        /// </summary>
        [ Column(Name = "family_other_id", StringLength = 500)]
        public string? FamilyOtherId { get; set; }

        /// <summary>
        /// 其他家族ID
        /// </summary>
        public RelatedCasesIds? FamilyOtherIds { get; set; }

        /// <summary>
        /// 流程状态代码
        /// </summary>
        [ Column(Name = "flow_status")]
        public int? FlowStatus { get; set; } = 0;

        [ Column(Name = "is_crm")]
        public bool? Crm { get; set; } = false;

        /// <summary>
        /// 是否有效
        /// </summary>
        [ Column(Name = "is_enabled")]
        public bool? Enabled { get; set; } = true;

        public bool? Ep { get; set; } = false;

        /// <summary>
        /// 承办人
        /// </summary>
        public string? TrackUserId { get; set; }


        /// <summary>
        /// 附件ID
        /// </summary>
        public string? PicFileNo { get; set; }

        /// <summary>
        /// 设计ID（未使用）
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 备注（未使用）
        /// </summary>

        public string? Remark { get; set; }

        /// <summary>
        /// 案源人
        /// </summary>
        [Required(ErrorMessage = "案源人不能为空")]
        public string SalesUserId { get; set; }

        /// <summary>
        /// 同日ID（？）
        /// </summary>
        public string? SameDayId { get; set; }

        /// <summary>
        /// 同日ID（？）
        /// </summary>
        public RelatedCasesIds? SameDayIds { get; set; }

        /// <summary>
        /// 同递交ID
        /// </summary>
        public string? SameSubmitId { get; set; }

        /// <summary>
        /// 同递交ID
        /// </summary>
        public RelatedCasesIds? SameSubmitIds { get; set; }

        /// <summary>
        /// 新案重提ID
        /// </summary>
        public string? CaseResubmitId { get; set; }

        /// <summary>
        /// 新案重提ID
        /// </summary>
        public RelatedCasesIds? CaseResubmitIds { get; set; }

        /// <summary>
        /// 再次提交（未使用）
        /// </summary>
        public bool? SubmitAgain { get; set; } = false;

        /// <summary>
        /// 技术见证人（未使用）
        /// </summary>
        [ Column(Name = "tech_witness", StringLength = 100)]
        public string? TechWitness { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [ Column(Name = "update_time")]
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 更新用户
        /// </summary>
        public string? UpdateUserId { get; set; }

        /// <summary>
        /// 委案ID
        /// </summary>
        public string? caseEntrustID { get; set; }

        /// <summary>
        /// 合同id
        /// </summary>
        [JsonPropertyName("contractId")]
        public string ContractId { get; set; } = null!;
    }
}
