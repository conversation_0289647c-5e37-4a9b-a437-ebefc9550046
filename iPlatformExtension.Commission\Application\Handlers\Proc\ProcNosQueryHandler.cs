﻿using iPlatformExtension.Commission.Application.Models.Proc;
using iPlatformExtension.Commission.Application.Queries.Proc;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Proc;

internal sealed class ProcNosQueryHandler(IFreeSql freeSql) : IRequestHandler<ProcNosQuery, IEnumerable<KeyValuePair<string,string>>>
{
    public async Task<IEnumerable<KeyValuePair<string, string>>> Handle(ProcNosQuery request, CancellationToken cancellationToken)
    {
        var (volume, queryType) = request;
        return await freeSql.Select<CaseProcInfo, SysDictionary>().WithLock()
            .LeftJoin((info, dictionary) => info.CtrlProcMark == dictionary.Value &&
                                            dictionary.DictionaryName == SystemDictionaryName.CtrlProcMark)
            .Where((info, dictionary) => info.CaseInfo.Volume == volume)
            .Where((info, dictionary) => info.IsEnabled == true)
            .WhereIf(queryType == ProcQueryType.DomesticTrademarkReward, 
                (info, dictionary) => !freeSql.Select<WinningRewardProc>().Where(rewardProc => rewardProc.ProcNo == info.ProcNo).Any())
            .WhereIf(queryType == ProcQueryType.ForeignTrademarkWeight, 
                (info, dictionary) => !freeSql.Select<RpForeignTrademarkBonus>().Any(bonus => bonus.ProcId == info.ProcId))
            .WhereIf(queryType == ProcQueryType.DomesticTrademarkWeight, 
                (info, dictionary) => !freeSql.Select<DomesticTrademarkCommission>().Any(commission => commission.ProcId == info.ProcId))
            .OrderBy((info, dictionary) => info.ProcNo)
            .ToListAsync(
                (info, dictionary) => new KeyValuePair<string, string>(info.ProcId,
                    // ReSharper disable once ConditionIsAlwaysTrueOrFalseAccordingToNullableAPIContract
                    dictionary.TextZhCn == null
                        ? info.ProcNo
                        : string.Concat(info.ProcNo, "（", dictionary.TextZhCn, "）")),
                cancellationToken);
    }
}