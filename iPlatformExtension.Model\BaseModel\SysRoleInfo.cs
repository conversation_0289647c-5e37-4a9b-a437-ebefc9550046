using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_role_info", DisableSyncStructure = true)]
	public partial class SysRoleInfo {

		[ Column(Name = "role_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string RoleId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "is_default")]
		public bool IsDefault { get; set; } = true;

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "role_code", StringLength = 50)]
		public string RoleCode { get; set; }

		[ Column(Name = "role_name", StringLength = 50)]
		public string RoleName { get; set; }

		[ Column(Name = "role_type")]
		public int? RoleType { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "useforoa")]
		public bool? Useforoa { get; set; }

		/// <summary>
		/// 角色对应的用户集合
		/// </summary>
		[Navigate(ManyToMany = typeof(SysUserRole))]
		public virtual ICollection<SysUserInfo>? Users { get; set; }

	}

}
