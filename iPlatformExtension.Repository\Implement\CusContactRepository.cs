﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal class CusContactRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    DefaultRedisCache redisCache,
    CacheExpirationToken<CusContact> expirationToken)
    : BaseRepository<CusContact, string>(freeSql), ICusContactRepository
{
    IMemoryCache ICacheableRepository<string, CusContact>.MemoryCache => memoryCache;

    CacheExpirationToken<CusContact> ICacheableRepository<string, CusContact>.ExpirationToken => expirationToken;
    
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}