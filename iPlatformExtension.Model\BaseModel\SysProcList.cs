using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_proc_list", DisableSyncStructure = true)]
	public partial class SysProcList {

		[ Column(Name = "list_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ListId { get; set; }

		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "ctrl_proc_property", StringLength = 50)]
		public string CtrlProcProperty { get; set; }

		[ Column(Name = "ctrl_proc_property_add", StringLength = 50)]
		public string CtrlProcPropertyAdd { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "remark", StringLength = 200)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
