﻿using iPlatformExtension.Public.Applications.Commands.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Proc;

internal sealed class DeleteFeeNameCtrlProcCommandHandler(IFeeNameCtrlProcRepository feeNameCtrlProcRepository) 
    : IRequestHandler<DeleteFeeNameCtrlProcCommand>
{
    public async Task Handle(DeleteFeeNameCtrlProcCommand request, CancellationToken cancellationToken)
    {
        var (feeNameId, ctrlProcId) = request;
        var feeNameCtrlProc = await feeNameCtrlProcRepository
            .Where(feeNameCtrlProc => feeNameCtrlProc.FeeNameId == feeNameId && feeNameCtrlProc.CtrlProcId == ctrlProcId)
            .ToOneAsync(cancellationToken);
        
        if (feeNameCtrlProc is not null)
        {
            await feeNameCtrlProcRepository.DeleteAsync(feeNameCtrlProc, cancellationToken);
        }
    }
}