using FluentValidation;

namespace iPlatformExtension.Common.Clients.Phoenix;

internal sealed class PhoenixClientOptionsValidator : AbstractValidator<PhoenixClientOptions>
{
    public PhoenixClientOptionsValidator()
    {
        RuleFor(options => options.HostAddress).NotEmpty().WithMessage("HostAddress不能为空");
        RuleFor(options => options.AppKey).NotEmpty().WithMessage("AppKey不能为空");
        RuleFor(options => options.SecretKey).NotEmpty().WithMessage("SecretKey不能为空");
        RuleFor(options => options.UserId).NotEmpty().WithMessage("UserId不能为空");
        RuleFor(options => options.UserName).NotEmpty().WithMessage("UserName不能为空");
        RuleFor(options => options.Executor).NotEmpty().WithMessage("Executor不能为空");
    }
}