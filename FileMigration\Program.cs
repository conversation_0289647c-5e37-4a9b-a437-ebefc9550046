using System.IO;
using System.Threading.Channels;
using FileMigration;
using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Clients.HttpMessageHandlers;
using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Clients.HuaweiObs.Options;
using iPlatformExtension.Common.Clients.IPlatformWeb;
using iPlatformExtension.Common.Clients.IPlatformWeb.HttpMessageHandlers;
using iPlatformExtension.Common.Clients.IPlatformWeb.Options;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Extensions;
using NLog.Extensions.Logging;

var host = Host.CreateDefaultBuilder(args)
    .ConfigureLogging((context, logging) =>
    {
        logging.ClearProviders();
        logging.AddSimpleConsole();
        logging.AddNLog(new NLogLoggingConfiguration(context.Configuration.GetSection("NLog")));
    })
    .ConfigureServices((context, services) =>
    {
        services.AddSingleton(Channel.CreateUnbounded<int>());
        services.AddSingleton<Worker>();
        services.AddSingleton<BackupWorker>();
        services.AddHostedService(provider => provider.GetRequiredService<Worker>());
        services.AddHostedService(provider => provider.GetRequiredService<BackupWorker>());
        services.AddObjectPools();
        services.AddStackExchangeRedisCache(options =>
            options.Configuration = context.Configuration.GetConnectionString("Redis"));
        services.AddPlatformFreeSql(context.Configuration.GetConnectionString("Default") ?? throw new ArgumentNullException(nameof(context.Configuration)));
        services.AddOptions<WorkOptions>().BindConfiguration("WorkOptions");
        services.AddOptions<ObsClientOptions>().BindConfiguration("HuaweiObs");
        services.AddOptions<PlatformFileOptions>().BindConfiguration("PlatformFile");
        services.AddSingleton<HuaweiObsClient>();
        services.AddTransient<PlatformFileAuthorizationHandler>();
        services.AddHttpClient<PlatformFileClient>()
            .AddHttpMessageHandler(provider =>
                new HttpRequestExceptionHandler<PlatformFileClient>(provider.GetRequiredService<ILoggerFactory>(),
                    false))
            .AddHttpMessageHandler<PlatformFileAuthorizationHandler>();
        services.AddCache().ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
        {
            options.ConnectionString = context.Configuration.GetConnectionString("Redis");
            options.Converters.Add(new ClaimPrincipalRedisValueConverter());
        });
    })
.Build();


host.Run();