﻿using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Repository.Implement;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery;

internal sealed class CancelOrderCommandHandler(
    DeliveryInfoRepository deliveryInfoRepository,
    PhoenixClientFactory phoenixClientFactory)
    : IRequestHandler<CancelOrderCommand, DeliInfo?>
{
    public async Task<DeliInfo?> Handle(CancelOrderCommand request, CancellationToken cancellationToken)
    {
        var deliveryInfo = await deliveryInfoRepository.GetAsync(request.ProcId, cancellationToken);
        var orderNo = request.OrderNo ?? deliveryInfo.OrderNo;
        var deliveryKey = request.DeliveryKey ?? deliveryInfo.DeliveryKey;
        
        if (string.IsNullOrWhiteSpace(orderNo)) 
            return deliveryInfo;
        
        var client = phoenixClientFactory.CreateClient(deliveryKey);
        var parameters = new CancelOrderParameters()
        {
            OrderNo = orderNo
        };
        await client.CancelOrderAsync(parameters);

        deliveryInfo.OrderNo = string.Empty;
        deliveryInfo.UpdateTime = DateTime.Now;
        deliveryInfo.UpdateUserId = IDeliveryCommand.AdminUserId;

        await deliveryInfoRepository.UpdateAsync(deliveryInfo, cancellationToken);

        return deliveryInfo;
    }
}