using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_meetrend_CustomerRelation", DisableSyncStructure = true)]
	public partial class VwMeetrendCustomerRelation {

		[ Column(Name = "customer_id", StringLength = 50, IsNullable = false)]
		public string CustomerId { get; set; }

		[ Column(Name = "customer_user_type", StringLength = 50, IsNullable = false)]
		public string CustomerUserType { get; set; }

		[ Column(StringLength = -2, IsNullable = false)]
		public string FID { get; set; }

		[ Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

	}

}
