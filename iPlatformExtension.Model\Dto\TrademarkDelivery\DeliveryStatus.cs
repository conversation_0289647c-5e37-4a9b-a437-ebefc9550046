namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 递交状态
/// </summary>
public enum DeliveryStatus
{
    /// <summary>
    /// 取消递交
    /// </summary>
    Stopped = -1,
    
    /// <summary>
    /// 待递交
    /// </summary>
    Ready = 0,

    /// <summary>
    /// 已下单
    /// </summary>
    Ordered = 1000,

    /// <summary>
    /// 递交中
    /// </summary>
    Delivering = 3000,
    
    /// <summary>
    /// 已递交
    /// </summary>
    Complete = 5000,

    /// <summary>
    /// 已确认
    /// </summary>
    Confirmed = 6000,

    /// <summary>
    /// 错误
    /// </summary>
    Error =500
}