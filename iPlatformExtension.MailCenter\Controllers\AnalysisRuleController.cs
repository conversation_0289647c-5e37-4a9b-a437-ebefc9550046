﻿using iPlatformExtension.MailCenter.Applications.Commands.AnalysisRule;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.MailCenter.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class AnalysisRuleController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// 解析规则
        /// </summary>
        /// <param name="command"></param>
        [HttpPost("AnalysisRule")]
        public async Task AnalysisRule([FromBody] AnalysisMailCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 重新解析、忽略按钮
        /// </summary>
        /// <param name="command"></param>
        [HttpPost("SetReAnalysis")]
        public async Task SetReAnalysis([FromBody] SetReAnalysisCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 分拣
        /// </summary>
        /// <param name="command"></param>
        [HttpPost("Sorting")]
        public async Task Sorting([FromBody] SortingCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 设置承办人
        /// </summary>
        /// <param name="command"></param>
        [HttpPost("SetUndertakeUser")]
        public async Task SetUndertakeUser([FromBody] SetUndertakeUserCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 邮件动作（提交、移交、退回）
        /// </summary>
        /// <param name="command"></param>
        [HttpPost("MailAction")]
        public async Task MailAction([FromBody] MailActionCommand command)
        {
            await mediator.Send(command);
        }


    }
}
