﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Commands.Customer.Contract;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Customer.Contract;

internal sealed class AddContractDetailsCommandHandler(ISender sender, IFreeSql<PlatformFreeSql> freeSql) : IRequestHandler<AddContractDetailsCommand>
{
    public async Task Handle(AddContractDetailsCommand request, CancellationToken cancellationToken)
    {
        var (crmContractId, details) = request;
        var contractId = await freeSql.Select<CusContract>().Where(contract => contract.CrmContractId == crmContractId)
            .ToOneAsync(contract => contract.ContractId, cancellationToken);

        if (string.IsNullOrWhiteSpace(contractId))
        {
            throw new NotFoundException(contractId, "CRM合同数据");
        }
        
        foreach (var group in details.GroupBy(detail => detail.BusinessType))
        {
            var detail = group.Single();
            await sender.Send(new SaveContractDetailCommand(contractId, detail), cancellationToken);
        }
    }
}