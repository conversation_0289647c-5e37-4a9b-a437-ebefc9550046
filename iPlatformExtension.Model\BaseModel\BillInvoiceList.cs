using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_invoice_list", DisableSyncStructure = true)]
	public partial class BillInvoiceList {

		[ Column(Name = "bill_id", StringLength = 50)]
		public string BillId { get; set; }

		[ Column(Name = "invoice_id", StringLength = 50, IsNullable = false)]
		public string InvoiceId { get; set; }

		/// <summary>
		/// 账单发票的导航属性
		/// </summary>
		[Navigate(nameof(BillInvoice.InvoiceId))]
		public virtual BillInvoice InvoiceInfo { get; set; } = default!;
	}

}
