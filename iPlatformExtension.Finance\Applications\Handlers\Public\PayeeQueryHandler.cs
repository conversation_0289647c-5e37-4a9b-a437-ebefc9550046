using iPlatformExtension.Finance.Applications.Commands.Public;
using iPlatformExtension.Finance.Applications.Queries.Public;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Public;

/// <summary>
/// 收款主体详情查询处理
/// </summary>
public sealed class PayeeQueryHandler : IRequestHandler<PayeeQuery, PayeeDetailInfoDto?>
{
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="freeSql">数据库查询组件</param>
    public PayeeQueryHandler(IFreeSql freeSql)
    {
        _freeSql = freeSql;
    }

    /// <summary>
    /// 查询收款主体信息
    /// </summary>
    /// <param name="request">包含公司ID的查询参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>收款主体详情</returns>
    public async Task<PayeeDetailInfoDto?> Handle(PayeeQuery request, CancellationToken cancellationToken)
    {
        ArgumentException.ThrowIfNullOrEmpty(request.CompanyId);
        
        var payeeInfo = await _freeSql.Select<BasCompany>().WithLock()
            .Where(company => company.CompanyId == request.CompanyId && company.IsEnabled == true)
            .FirstAsync(company => new PayeeDetailInfoDto()
            {
                CompanyCode = company.CompanyCode,
                Id = company.CompanyId,
                CnName = company.CompanyNameCn,
                EnName = company.CompanyNameEn,
                Payee = company.Payee,
                Invoicer = company.Invoicer,
                Reviewer = company.Reviewer,
                DistrictId = company.DistrictId,
                TaxRate = company.TaxRate ?? decimal.Zero,
                AbbreviatedName = company.ShortNameCn,
                Fax = company.Fax,
                Email = company.Email,
                Tel = company.Tel,
                CnAddress = company.AddressCn,
                EnAddress = company.AddressEn,
                PostCode = company.PostCode
            }, cancellationToken);
        
        if (payeeInfo is not null)
        {
            payeeInfo.BankAccountInfos = await _freeSql.Select<BasSelfBank>().WithLock().From<BasSelfBankCompany>()
                .InnerJoin((bank, company) => bank.BankId == company.BankId)
                .Where((bank, company) => company.CompanyId == request.CompanyId && bank.IsEnabled == true)
                .OrderBy((bank, company) => bank.Seq)
                .ToListAsync((bank, company) =>
                    new PayeeBankAccountInfo()
                    {
                        AddressCn = bank.BankAddressZhCn,
                        AddressEn = bank.BankAddressEnUs,
                        BankAccount = bank.Account,
                        BankId = bank.BankId,
                        CnName = bank.BankNameZhCn,
                        EnName = bank.BankNameEnUs,
                        Currency = bank.Currency,
                        SwiftCode = bank.BankCode,
                        AccountName = bank.UserNameZhCn,
                        AccountNameEn = bank.UserNameEnUs,
                    }, cancellationToken);
        }

        return payeeInfo;
    }
}