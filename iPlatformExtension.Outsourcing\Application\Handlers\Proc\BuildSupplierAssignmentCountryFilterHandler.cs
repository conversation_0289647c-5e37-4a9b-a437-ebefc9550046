﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

// internal sealed class BuildSupplierAssignmentCountryFilterHandler(IFreeSql<PlatformFreeSql> freeSql) 
//     : IBuildSupplierAssignmentFilterHandler
// {
//     public async Task HandleAsync(SupplierAssignmentAuthorizationContext context, CancellationToken cancellationToken)
//     {
//         var (_, _, _, filters, query) = context;
//         var includeCountries = filters.TryGetValue("country", out var countryFilter) ? new HashSet<string>(countryFilter.Values.ToArray()) : [];
//         var excludeCountries = filters.TryGetValue("country_not", out var excludeCountryFilter) ? excludeCountryFilter.Values.ToArray() : [];
//
//         if (excludeCountries.Length > 0 && includeCountries.Count == 0)
//         {
//             includeCountries =
//             [
//                 ..await freeSql.Select<BasCountry>().WithLock()
//                     .ToListAsync(country => country.CountryId, cancellationToken)
//             ];
//             includeCountries.ExceptWith(excludeCountries);
//         }
//         
//         query.WhereIf(includeCountries.Count > 0, info => includeCountries.Contains(info.CaseInfo.CountryId!));
//     }
// }

internal sealed class BuildSupplierAssignmentCountryFilterHandler(IFreeSql<PlatformFreeSql> freeSql) : IMatchNotificationHandler<NotAssignedProcQueryContext>
{
    private string[] _includeCountries = [];

    private string[] _excludeCountries = [];
    
    public ValueTask<bool> MatchAsync(NotAssignedProcQueryContext notification, CancellationToken cancellationToken)
    {
        var filters = notification.Filters;
        if (filters.TryGetValue("country", out var countryFilter) 
            && "custom".Equals(countryFilter.FilterType, StringComparison.OrdinalIgnoreCase))
        {
            _includeCountries = countryFilter.FilterValue.Split(';');
        }
        
        if (filters.TryGetValue("country_not", out var excludeCountryFilter) 
            && "custom".Equals(excludeCountryFilter.FilterType, StringComparison.OrdinalIgnoreCase))
        {
            _excludeCountries = excludeCountryFilter.FilterValue.Split(';');
        }
        
        return ValueTask.FromResult(_includeCountries.Length > 0 || _excludeCountries.Length > 0);
    }

    public async Task HandleAsync(NotAssignedProcQueryContext notification, CancellationToken cancellationToken)
    {
        var allCountries = new HashSet<string>(await freeSql.Select<BasCountry>()
            .ToListAsync(country => country.CountryId, cancellationToken));
        
        if (_includeCountries.Length == 0)
        {
            allCountries.ExceptWith(_excludeCountries);

            if (allCountries.Count < _excludeCountries.Length)
            {
                notification.Query.Where(info => allCountries.Contains(info.CaseInfo.CountryId!));
                notification.CountQueryBuilder.Where(info => allCountries.Contains(info.CaseInfo.CountryId!));
            }
            else
            {
                IEnumerable<string> excludeCountries = _excludeCountries;
                notification.Query.Where(info => !excludeCountries.Contains(info.CaseInfo.CountryId!));
                notification.CountQueryBuilder.Where(info => !excludeCountries.Contains(info.CaseInfo.CountryId!));
            }
        }
        else
        {
            var countries = new HashSet<string>(_includeCountries);
            countries.ExceptWith(_excludeCountries);

            allCountries.ExceptWith(countries);
            var excludeCountries = allCountries;

            if (0 < excludeCountries.Count && excludeCountries.Count < countries.Count)
            {
                notification.Query.Where(info => !excludeCountries.Contains(info.CaseInfo.CountryId!));
                notification.CountQueryBuilder.Where(info => !excludeCountries.Contains(info.CaseInfo.CountryId!));
            }
            else
            {
                notification.Query.Where(info => countries.Contains(info.CaseInfo.CountryId!));
                notification.CountQueryBuilder.Where(info => countries.Contains(info.CaseInfo.CountryId!));
            }
            
        }
        
    }
}