using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_message_user_inner", DisableSyncStructure = true)]
	public partial class MailMessageUserInner {

		[ Column(Name = "address_type", StringLength = 50, IsNullable = false)]
		public string AddressType { get; set; }

		[ Column(Name = "display_name", StringLength = 500)]
		public string DisplayName { get; set; }

		[ Column(Name = "mail_address", StringLength = 200, IsNullable = false)]
		public string MailAddress { get; set; }

		[ Column(Name = "mail_id", StringLength = 50, IsNullable = false)]
		public string MailId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
