﻿using System.Net;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Proc;

namespace iPlatform.Extension.Public.Test;

public class FeeNameControllerTest(PublicWebApplicationFactory factory) : IClassFixture<PublicWebApplicationFactory>
{
    private readonly HttpClient _client = factory.CreateClient();


    [Theory]
    [InlineData("15c8d06d-5dde-4873-a6cc-e461455e343c", "")]
    [InlineData("3aa250fd-4141-40c7-84a7-6e9d9633b9a6", "")]
    [InlineData("3b51ff9c-7e3d-46e9-b9eb-f67eda49e165", "")]
    [InlineData("468b88bb-66df-4165-82ff-16c0351c01f4", "")]
    public async Task GetFeeNameCtrlProcInfoAsync_ReturnsSuccess(string feeNameId, string keyword)
    {
        // Arrange
        

        // Act
        var response = await _client.GetAsync($"fee-name/{feeNameId}/ctrl-proc-info?keyword={keyword}");
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadFromJsonAsync<ResultData<IEnumerable<FeeNameCtrlProcInfo>>>();
        
        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.Data);
        Assert.NotEmpty(result.Data!);
        Assert.True(result.Data!.All(info => !string.IsNullOrWhiteSpace(info.IsEnabled)));
    }

    [InlineData("15c8d06d-5dde-4873-a6cc-e461455e343c", "cce6e06d-e77f-4d54-80f9-7e8553a08f78")]
    [InlineData("3d7e7c19-42f8-44e5-9c99-8d8b7a965f77", "")]
    [InlineData("79c2c543-d738-4003-91d3-d220d4588eaf", "")]
    [Theory]
    public async Task AddFeeNameCtrlProcInfoAsync_ReturnsSuccess(string feeNameId, params string[] ctrlProcIds)
    {
        // Arrange
        
        var content = JsonContent.Create(ctrlProcIds);

        // Act
        var response = await _client.PostAsync($"fee-name/{feeNameId}/ctrl-proc-info", content);

        // Assert
        Assert.Equal(ctrlProcIds.Length == 0 ? HttpStatusCode.BadRequest : HttpStatusCode.OK, response.StatusCode);
    }
    
    [InlineData("15c8d06d-5dde-4873-a6cc-e461455e343c", 
        "cce6e06d-e77f-4d54-80f9-7e8553a08f78", "0460f001-db78-401d-8ded-0edc7aa15832", "aa7483a0-25a3-4ad7-aae4-c247a2c83c86")]
    [InlineData("3d7e7c19-42f8-44e5-9c99-8d8b7a965f77", 
        "0460f001-db78-401d-8ded-0edc7aa15832", "be986cbd-f82b-47d2-ac4c-b28a2505cb7d", "aeb4456c-de1d-461c-9d70-89345d4c15f5")]
    [InlineData("79c2c543-d738-4003-91d3-d220d4588eaf", "")]
    [Theory]
    public async Task AddNotEnabledCtrlProcInfoToFeeNameAsync_ReturnsFailed(string feeNameId, params string[] ctrlProcIds)
    {
        // Arrange
        
        var content = JsonContent.Create(ctrlProcIds);

        // Act
        var response = await _client.PostAsync($"fee-name/{feeNameId}/ctrl-proc-info", content);
        var result = await response.Content.ReadFromJsonAsync<ResultData>();

        // Assert
        Assert.False(response.IsSuccessStatusCode);
        Assert.NotNull(result);
        Assert.False(result.Success);
    }

    [Fact]
    public async Task DeleteFeeNameCtrlProcInfoAsync_ReturnsSuccess()
    {
        // Arrange
        var feeNameId = "testFeeNameId";
        var ctrlProcId = "testCtrlProcId";

        // Act
        var response = await _client.DeleteAsync($"fee-name/{feeNameId}/ctrl-proc-info/{ctrlProcId}");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }
}