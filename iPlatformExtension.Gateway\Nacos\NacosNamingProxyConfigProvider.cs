﻿using System.Threading.Channels;
using iPlatformExtension.Gateway.Infrastructure.Extensions;
using Microsoft.Extensions.Options;
using Nacos.V2;
using Nacos.V2.Naming.Dtos;
using Nacos.V2.Naming.Event;
using Yarp.ReverseProxy.Configuration;

namespace iPlatformExtension.Gateway.Nacos;

internal sealed class NacosNamingProxyConfigProvider(
    IOptionsMonitor<NacosYarpOptions> configOptions,
    ILogger<NacosNamingProxyConfigProvider> logger) 
    : IProxyConfigProvider, IEventListener
{
    private int _updateLock;

    private readonly Dictionary<string, ClusterConfig> _clusterConfigs = new(StringComparer.CurrentCultureIgnoreCase);

    private readonly Dictionary<string, RouteConfig> _routeConfigs = new(StringComparer.CurrentCultureIgnoreCase);

    private readonly Channel<InstancesChangeEvent> _instancesChangeChannel =
        Channel.CreateUnbounded<InstancesChangeEvent>();

    private readonly NacosNamingProxyReloadToken _reloadToken = new();

    private NacosYarpOptions _optionsSnapshot = configOptions.CurrentValue;
    
    public IProxyConfig GetConfig()
    {
        return new NacosNamingProxyConfig(
            _routeConfigs.Select(pair => pair.Value).GroupBy(config => config.RouteId)
                .Select(configs => configs.OrderBy(config => config.Order ?? int.MaxValue)
                    .First()).ToList(),
            _clusterConfigs.Select(pair => pair.Value).ToList(),
            _reloadToken.Refresh());
    }

    public async Task OnEvent(IEvent @event)
    {
        if (@event is InstancesChangeEvent changeEvent)
        {
            logger.LogInformation("服务实例变更事件触发。组名：{GroupName}, 服务名：{ServiceName}", changeEvent.GroupName, changeEvent.ServiceName);
            
            await _instancesChangeChannel.Writer.WriteAsync(changeEvent);
            logger.LogInformation("更新信息插入队列");
            
            if (Interlocked.CompareExchange(ref _updateLock, 1, 0) != _updateLock)
            {
                logger.LogInformation("锁定服务信息更新");
                
                while (_instancesChangeChannel.Reader.TryRead(out var instancesChangeEvent))
                {
                    logger.LogInformation("读取更新队列信息");
                    RefreshServiceConfigs(instancesChangeEvent.ServiceName, instancesChangeEvent.GroupName, instancesChangeEvent.Hosts);
                }

                Interlocked.Exchange(ref _updateLock, 0);
                
                logger.LogInformation("解锁服务信息更新");
            }
            else
            {
                await Task.Delay(TimeSpan.FromSeconds(1));
            }
            _reloadToken.OnReload();
        }
    }

    internal void RefreshOptions(NacosYarpOptions currentOptions)
    {
        logger.LogRefreshOptions(currentOptions);
        
        foreach (var (optionsRouteId, optionsRouteConfig) in currentOptions.Routes)
        {
            if (_routeConfigs.TryGetValue(optionsRouteId, out _) || !currentOptions.GroupNames.Contains(optionsRouteId))
            {
                _routeConfigs[optionsRouteId] = optionsRouteConfig.GenerateRouteConfig(optionsRouteId);
            }
            else if (currentOptions.GroupNames.Contains(optionsRouteId))
            {
                foreach (var (routeId, _) in _routeConfigs)
                {
                    NacosClusterId clusterId = routeId;
                    if (currentOptions.Routes.TryGetValue(clusterId.GroupName, out var currentRouteConfig))
                    {
                        _routeConfigs[routeId] = currentRouteConfig.GenerateRouteConfig(clusterId);
                    }
                }
            }
            
        }

        foreach (var (optionsClusterId, optionsClusterConfig) in currentOptions.Clusters)
        {
            if (_clusterConfigs.TryGetValue(optionsClusterId, out var clusterConfig))
            {
                var destinationConfigs = clusterConfig.Destinations;
                _clusterConfigs[clusterConfig.ClusterId] = new ClusterConfig
                {
                    Destinations = optionsClusterConfig.Destinations ?? destinationConfigs,
                    ClusterId = optionsClusterId,
                    LoadBalancingPolicy = optionsClusterConfig.LoadBalancingPolicy,
                    SessionAffinity = optionsClusterConfig.SessionAffinity,
                    HealthCheck = optionsClusterConfig.HealthCheck,
                    HttpClient = optionsClusterConfig.HttpClient,
                    HttpRequest = optionsClusterConfig.HttpRequest,
                    Metadata = optionsClusterConfig.Metadata
                };
            }
            else
            {
                if (currentOptions.GroupNames.Contains(optionsClusterId))
                {
                    foreach (var (clusterId, cluster) in _clusterConfigs)
                    {
                        NacosClusterId nacosClusterId = clusterId;
                        if (currentOptions.Clusters.TryGetValue(nacosClusterId.GroupName, out var currentClusterConfig))
                        {
                            _clusterConfigs[clusterId] = new ClusterConfig
                            {
                                Destinations = cluster.Destinations,
                                ClusterId = clusterId,
                                LoadBalancingPolicy = currentClusterConfig.LoadBalancingPolicy,
                                SessionAffinity = currentClusterConfig.SessionAffinity,
                                HealthCheck = currentClusterConfig.HealthCheck,
                                HttpClient = currentOptions.HttpClients.TryGetValue(clusterId, out var httpClientConfig) 
                                    ? httpClientConfig 
                                    : currentClusterConfig.HttpClient,
                                HttpRequest = currentClusterConfig.HttpRequest,
                                Metadata = cluster.Metadata
                            };
                        }
                    }
                }
                else
                {
                    _clusterConfigs[optionsClusterId] = new ClusterConfig
                    {
                        Destinations = optionsClusterConfig.Destinations,
                        ClusterId = optionsClusterId,
                        LoadBalancingPolicy = optionsClusterConfig.LoadBalancingPolicy,
                        SessionAffinity = optionsClusterConfig.SessionAffinity,
                        HealthCheck = optionsClusterConfig.HealthCheck,
                        HttpClient = optionsClusterConfig.HttpClient,
                        HttpRequest = optionsClusterConfig.HttpRequest,
                        Metadata = optionsClusterConfig.Metadata
                    };
                }
            }
        }

        var snapShotRouteIds = new HashSet<string>(_optionsSnapshot.Routes.Keys);
        var currentRouteIds = currentOptions.Routes.Keys;
        snapShotRouteIds.ExceptWith(currentRouteIds);
        foreach (var snapShotRouteId in snapShotRouteIds)
        {
            _routeConfigs.Remove(snapShotRouteId);
        }

        var snapShotClusterIds = new HashSet<string>(_optionsSnapshot.Clusters.Keys);
        var currentClusterIds = currentOptions.Clusters.Keys;
        snapShotClusterIds.ExceptWith(currentClusterIds);
        foreach (var snapShotClusterId in snapShotClusterIds)
        {
            _clusterConfigs.Remove(snapShotClusterId);
        }

        _optionsSnapshot = currentOptions;
        
        _reloadToken.OnReload();
    }

    internal void RefreshServiceConfigs(string serviceName, string groupName, IEnumerable<Instance> instances)
    {
        var currentConfigs = configOptions.CurrentValue;
        var clusterId = new NacosClusterId(serviceName, groupName);

        if (!instances.Any())
        {
            _clusterConfigs.Remove(clusterId);
            return;
        }
        
        var destinationConfigs = instances
            .ToDictionary(instance => instance.ToInetAddr(), instance =>
            {
                var metaData = instance.Metadata;
                var protocol = "http";
                if (metaData.TryGetValue("secure", out var secure) && StringComparer.OrdinalIgnoreCase.Equals("true", secure))
                {
                    protocol = "https";
                }
                
                logger.LogInformation("下游服务信息：{Instance}", instance);
                            
                return new DestinationConfig()
                {
                    Address = $"{protocol}://{instance.ToInetAddr()}",
                    Metadata = instance.Metadata,
                };
            });
                    
        // var loadBalancingPolicy = instancesChangeEvent.
        var currentClusterConfigs = currentConfigs.Clusters;
        var baseClusterConfig = currentClusterConfigs.TryGetValue(clusterId, out var currentClusterConfig)
            ? currentClusterConfig
            : currentClusterConfigs.TryGetValue(groupName, out currentClusterConfig)
                ? currentClusterConfig
                : default;
        
        var isCustomCluster = baseClusterConfig?.ClusterId == clusterId;

        var clusterConfig = new ClusterConfig
        {
            Destinations = destinationConfigs,
            ClusterId = clusterId,
            LoadBalancingPolicy = baseClusterConfig?.LoadBalancingPolicy,
            SessionAffinity = baseClusterConfig?.SessionAffinity,
            HealthCheck = baseClusterConfig?.HealthCheck,
            HttpClient = isCustomCluster 
                ? baseClusterConfig?.HttpClient 
                : currentConfigs.HttpClients.TryGetValue(clusterId, out var httpClientConfig) 
                    ? httpClientConfig 
                    : baseClusterConfig?.HttpClient,
            HttpRequest = baseClusterConfig?.HttpRequest,
            Metadata = baseClusterConfig?.Metadata
        };
        _clusterConfigs[clusterConfig.ClusterId] = clusterConfig;
        
        var currentRouteConfigs = currentConfigs.Routes;
        var baseRouteConfig = currentRouteConfigs.TryGetValue(clusterId, out var currentRouteConfig)
            ? currentRouteConfig
            : currentRouteConfigs.TryGetValue(groupName, out currentRouteConfig)
                ? currentRouteConfig
                : new RouteConfig()
                {
                    RouteId = clusterId,
                    ClusterId = clusterId,
                    Match = new RouteMatch()
                    {
                        Hosts = ["patas.aciplaw.com", "patas-test.aciplaw.com", "patas-dev.aciplaw.com", "ipr-test.aciplaw.com"],
                        Path = $"/{serviceName}/{{**catch-all}}"
                    }
                };
        
        var routeConfig = baseRouteConfig.GenerateRouteConfig(clusterId);
        _routeConfigs[routeConfig.RouteId] = routeConfig;
    }
}