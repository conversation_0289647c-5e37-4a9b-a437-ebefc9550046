﻿﻿using iPlatformExtension.MailCenter.Applications.Models.Signature;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.Signature
{
    /// <summary>
    /// 获取签名列表查询
    /// </summary>
    public class GetSignatureListQuery : QueryBase, IRequest<PageResult<GetSignatureListDto>>
    {
        /// <summary>
        /// 签名名称（模糊查询）
        /// </summary>
        public string? Name { get; set; }
        
        /// <summary>
        /// 排序
        /// </summary>
        public string? Sort { get; set; }
        
        /// <summary>
        /// 升序降序（asc,desc）小写
        /// </summary>
        public string? SortType { get; set; }
    }
}
