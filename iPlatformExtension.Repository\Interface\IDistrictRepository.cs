﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface IDistrictRepository : 
    IBaseRepository<BasDistrict, string>, 
    IScopeDependency, 
    IStringKeyCacheableRepository<BasDistrict>,
    IRedisCacheableRepository<string, BasDistrict>
{
    Task<BasDistrict?> ICacheableRepository<string, BasDistrict>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Select.WithLock().Where(district => district.DistrictCode == key)
            .FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasDistrict>> ICacheableRepository<string, BasDistrict>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasDistrict>.GenerateKey(BasDistrict value)
    {
        return value.DistrictCode;
    }

    string IStringKeyCacheableRepository<BasDistrict>.GetCacheTextValue(BasDistrict value)
    {
        return value.TextZhCn;
    }
}