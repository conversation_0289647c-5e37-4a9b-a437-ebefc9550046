﻿using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Service.Interface;

public interface IBillQueryService : IFreeSqlQueryService, ITransientDependency
{
    async Task<ReceivedBillHandlerMatchResult> MatchReceivedBillHandlerAsync(string payerName)
    {
        var receiveBillHandlerInfo = await DbQuery.Select<CusRequestObject>().WithLock()
            .From<CusCustomer, SysUserInfo>((requestObject, customer, userInfo) =>
                requestObject.InnerJoin(o => o.CustomerId == customer.CustomerId)
                    .LeftJoin(o => customer.BusiUserId == userInfo.UserId && userInfo.IsEnabled ==true))
            .Where(tuple => tuple.t1.RequestObjectName == payerName && tuple.t1.IsEnabled == true)
            .ToListAsync((requestObject, customer, userInfo) => new ReceiveBillHandlerInfo()
            {
                CustomerDto = new ReceiveBillHandlerInfo.MatchedCustomerInfo()
                {
                    Id = customer.CustomerId,
                    CrmCode = customer.CrmCustomerCode,
                    Name = customer.CustomerFullName,
                    IsOutbound = customer.IsCooperation!.Value
                },
                BusinessPersonnelDto = new UserInfoDto()
                {
                    UserName = userInfo.UserName,
                    CnName = userInfo.CnName,
                    UserId = userInfo.UserId,
                    IsEnabled = userInfo.IsEnabled
                },
                RequestObjectDto = new ReceiveBillHandlerInfo.RequestObjectInfo()
                {
                    Id = requestObject.RequestObjectId,
                    RequestObjectCode = requestObject.RequestObjectCode,
                    Name = requestObject.RequestObjectName
                }
            });

        var context = new ReceiveBillMatchHandlerContext(receiveBillHandlerInfo);
        var matchService = new ReceivedBillHandlerMatchServiceBuilder()
            .AddMatch(handlerContext => handlerContext.HandlerInfos.Count == 1 ? handlerContext.Success() : handlerContext.Fail("匹配结果不唯一"))
            .AddMatch(handlerContext => handlerContext.HandlerInfos.First().BusinessPersonnelDto is not null ? handlerContext.Success() : handlerContext.Fail("没有商务人员"))
            .AddMatch(handlerContext => handlerContext.HandlerInfos.First().BusinessPersonnelDto!.IsEnabled ? handlerContext.Success() : handlerContext.Fail("商务人员无效"))
            .AddMatch(handlerContext => handlerContext.HandlerInfos.First().BusinessPersonnelDto!.UserName != "admin" ? handlerContext.Success() : handlerContext.Fail("商务是超级管理员"))
            .AddMatch(handlerContext => handlerContext.HandlerInfos.First().CustomerDto.IsOutbound.HasValue ? handlerContext.Success() : handlerContext.Fail("境外代理标识不明确"))
            .AddMatch(handlerContext => handlerContext.HandlerInfos.First().BusinessPersonnelDto!.UserName != "H99999" ? handlerContext.Success() : handlerContext.Fail("商务是虚拟账号"))
            .AddMatch(handlerContext => !string.IsNullOrWhiteSpace(handlerContext.HandlerInfos.First().CustomerDto.CrmCode) ? handlerContext.Success() : handlerContext.Fail("没有CRM客户编码"))
            .AddMatch(handlerContext => !string.IsNullOrWhiteSpace(handlerContext.HandlerInfos.First().RequestObjectDto.RequestObjectCode) ? handlerContext.Success() : handlerContext.Fail("没有请款对象编码"))
            .AddMatch(handlerContext => !handlerContext.HandlerInfos.First().CustomerDto.IsOutbound!.Value ? handlerContext.Success() : handlerContext.Fail("客户是境外代理"))
            .Build();
        var matchResult = matchService.Match(context);

        if (!matchResult.MatchSuccess) return matchResult;
        
        var salesmen = await DbQuery.Select<CusFollowList>().WithLock()
            .From<CusCustomer, SysUserInfo>((followList, customer, info) => 
                followList.LeftJoin(list => list.CustomerId == customer.CustomerId)
                    .LeftJoin(list => list.TrackUser == info.UserId))
            .Where(tuple => tuple.t2.CrmCustomerCode == matchResult.Customer.CrmCode && tuple.t1.IsEnabled == true &&
                            tuple.t1.CustomerUserType == CustomerUserType.CaseSource)
            .GroupBy(tuple => new UserInfoDto()
            {
                CnName = tuple.t3.CnName,
                UserId = tuple.t3.UserId,
                UserName = tuple.t3.UserName
            })
            .ToListAsync(aggregate => aggregate.Key);
        if (salesmen.Count == 1)
        {
            matchResult.Salesman = salesmen[0];
        }
        

        return matchResult;
    }
    
    internal class ReceivedBillHandlerMatchService(
        Func<ReceiveBillMatchHandlerContext, ReceivedBillHandlerMatchResult> match)
    {
        public ReceivedBillHandlerMatchService? NextMatchService { get; set; }

        public ReceivedBillHandlerMatchResult Match(ReceiveBillMatchHandlerContext context)
        {
            var result = match(context);
            if (result.MatchSuccess && NextMatchService is not null)
            {
                result = NextMatchService.Match(context);
            }

            return result;
        }
    }
    
    internal readonly struct ReceivedBillHandlerMatchServiceBuilder()
    {
        private readonly IList<ReceivedBillHandlerMatchService> _services = new List<ReceivedBillHandlerMatchService>();

        public ReceivedBillHandlerMatchServiceBuilder AddMatch(Func<ReceiveBillMatchHandlerContext, ReceivedBillHandlerMatchResult> match)
        {
            ArgumentNullException.ThrowIfNull(match);
            _services.Add(new ReceivedBillHandlerMatchService(match));
            return this;
        }

        public ReceivedBillHandlerMatchService Build()
        {
            if (!_services.Any())
            {
                AddMatch(context => context.Fail());
            }

            var service = _services.Last();
            for (var i = _services.Count - 1; i >= 1; i--)
            {
                service = _services[i - 1];
                service.NextMatchService = _services[i];
            }

            return service;
        }
    }
}