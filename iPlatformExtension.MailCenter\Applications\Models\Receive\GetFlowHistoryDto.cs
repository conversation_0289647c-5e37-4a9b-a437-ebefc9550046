﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 流程历史dto
/// </summary>
/// <param name="Id">主键id</param>
/// <param name="Action">动作/办理结果（Submit/提交,Reject/退回,Transfer/移交）</param>
/// <param name="MailId">邮件id</param>
/// <param name="DisplayName">办理意见</param>
/// <param name="UndertakeUser">承办人</param>
/// <param name="Seq">流程排序</param>
/// <param name="Current">是否当前流程</param>
/// <param name="CreateTime">创建时间</param>
/// <param name="FinishDate">完成日期，判断流程是否结束标志</param>
public record GetFlowHistoryDto(
    string Id,
    string Action,
    string MailId,
    string DisplayName,
    string UndertakeUserId,
    string? Seq,
    int? Current,
    DateTime? CreateTime)
{
    /// <summary>
    /// 流程节点
    /// </summary>
    public string ProcessNodes { get; set; }
    public object UndertakeUser { get; set; }
    public DateTime? FinishDate { get; set; }
};

