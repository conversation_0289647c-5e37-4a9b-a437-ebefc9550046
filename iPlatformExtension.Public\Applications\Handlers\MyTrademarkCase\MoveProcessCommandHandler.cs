﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Public.Applications.Commands.MyTrademarkCase;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.MyTrademarkCase
{
    /// <summary>
    /// 案件任务移入标签
    /// </summary>
    internal sealed class MoveProcessCommandHandler(ICaseProcInfoRepository caseProcInfoRepository,IHttpContextAccessor contextAccessor) : IRequestHandler<MoveProcessCommand>
    {
        public async Task Handle(MoveProcessCommand request, CancellationToken cancellationToken)
        {
            var userId = contextAccessor.HttpContext?.User.GetUserId();
            var caseProcInfos = await caseProcInfoRepository.Where(it=>request.ProcId.Contains(it.ProcId) && it.UndertakeUserId == userId).ToListAsync(cancellationToken);
            caseProcInfos.ForEach(it=>it.PrivateId = request.PrivateId);
            await caseProcInfoRepository.UpdateAsync(caseProcInfos, cancellationToken);
        }
    }
}

