﻿using Confluent.Kafka;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class StartupDeliveryCommandHandler(
    IProducer<MessageKey, DeliveryMessage> producer,
    ILogger<StartupDeliveryCommandHandler> logger)
    : DefaultSendKafkaMessageCommandHandler<StartupDeliveryCommand, DeliveryMessage>(producer)
{
    public override async Task Handle(StartupDeliveryCommand request, CancellationToken cancellationToken)
    {
        await base.Handle(request, cancellationToken);
        logger.LogInformation("发送递交信息成功！Topic:{Topic}, MessageKey:{MessageKey}, Value:{Message}", request.Topic,
            request.MessageKey, request.Message);
    }
}