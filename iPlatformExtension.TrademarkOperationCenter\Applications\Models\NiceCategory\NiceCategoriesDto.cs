﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;

/// <summary>
/// 用户编辑尼斯分类
/// </summary>
public class NiceCategoriesDto
{
    /// <summary>
    /// 版本号
    /// </summary>
    [Required]
    public string VersionId { get; set; } = default!;

    /// <summary>
    /// 尼斯分类大类编号集合
    /// </summary>
    public IEnumerable<string> GrandNumbers { get; set; } = [];

    /// <summary>
    /// 标准分类
    /// </summary>
    public IEnumerable<StandardCategoryDto> StandardCategories { get; set; } = [];

    /// <summary>
    /// 非标标准分类
    /// </summary>
    public IEnumerable<CustomCategoryDto> CustomCategories { get; set; } = [];
}