﻿using Microsoft.AspNetCore.Authorization;

namespace iPlatformExtension.Outsourcing.Infrastructure.Authorization.ProcOutsourcing;

public class BatchProcOutsourcingAuthorizationRequirement : IAuthorizationRequirement
{
    private readonly Dictionary<string, IList<string>> _entries = new();
    
    public BatchProcOutsourcingAuthorizationRequirement Add(string caseType, params string[] allowedRoles)
    {
        if (_entries.TryGetValue(caseType, out var roles))
        {
            foreach (var allowedRole in allowedRoles)
            {
                roles.Add(allowedRole);
            }
        }
        else
        {
            _entries.Add(caseType, allowedRoles.ToList());
        }
        return this;
    }
    
    public bool TryGetRoles(string caseType, out IEnumerable<string> roles)
    {
        if (_entries.TryGetValue(caseType, out var entryRoles))
        {
            roles = entryRoles;
            return true;
        }
        
        roles = [];
        return false;
    }
}