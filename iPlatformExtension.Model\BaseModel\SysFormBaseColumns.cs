using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_form_base_columns", DisableSyncStructure = true)]
	public partial class SysFormBaseColumns {

		[ Column(Name = "column_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ColumnId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_name_en_us", StringLength = 50)]
		public string ColumnNameEnUs { get; set; }

		[ Column(Name = "column_name_ja_jp", StringLength = 50)]
		public string ColumnNameJaJp { get; set; }

		[ Column(Name = "column_name_zh_cn", StringLength = 50)]
		public string ColumnNameZhCn { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "datebase_id", StringLength = 50)]
		public string DatebaseId { get; set; }

		[ Column(Name = "form_data_code", StringLength = 50)]
		public string FormDataCode { get; set; }

		[ Column(Name = "form_type", StringLength = 50)]
		public string FormType { get; set; }

		[ Column(Name = "len", StringLength = 50)]
		public string Len { get; set; }

		[ Column(Name = "match_mode", StringLength = 50)]
		public string MatchMode { get; set; }

		[ Column(Name = "name_as", StringLength = 2000)]
		public string NameAs { get; set; }

		[ Column(Name = "not_null")]
		public bool? NotNull { get; set; }

		[ Column(Name = "readonly")]
		public bool? Readonly { get; set; }

		[ Column(Name = "show_id", StringLength = 50)]
		public string ShowId { get; set; }

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

		[ Column(Name = "table_code", StringLength = 50)]
		public string TableCode { get; set; }

		[ Column(Name = "table_join_code", StringLength = 50)]
		public string TableJoinCode { get; set; }

		[ Column(Name = "table_name", StringLength = 50)]
		public string TableName { get; set; }

	}

}
