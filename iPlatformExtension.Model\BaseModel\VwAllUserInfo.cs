using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_all_user_info", DisableSyncStructure = true)]
	public partial class VwAllUserInfo {

		[ Column(StringLength = 200)]
		public string 工作邮箱 { get; set; }

		[ Column(StringLength = 50)]
		public string 公司电话 { get; set; }

		
		public DateTime? 离职日期 { get; set; }

		
		public DateTime? 入职日期 { get; set; }

		[ Column(StringLength = 50)]
		public string 手机号码 { get; set; }

		[ Column(StringLength = 500)]
		public string 所属部门 { get; set; }

		[ Column(StringLength = 50)]
		public string 所属地区 { get; set; }

		[ Column(DbType = "varchar(2)", IsNullable = false)]
		public string 推送财务 { get; set; }

		[ Column(DbType = "varchar(2)", IsNullable = false)]
		public string 性别 { get; set; }

		[ Column(StringLength = 50)]
		public string 英文名 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 用户标识 { get; set; }

		[ Column(StringLength = 100)]
		public string 用户类型 { get; set; }

		[ Column(StringLength = 50)]
		public string 用户名 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 中文名 { get; set; }

		[ Column(DbType = "varchar(4)", IsNullable = false)]
		public string 状态 { get; set; }

	}

}
