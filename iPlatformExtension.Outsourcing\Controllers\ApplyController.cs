﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Outsourcing.Application.Commands.Apply;
using iPlatformExtension.Outsourcing.Application.Models.Apply;
using iPlatformExtension.Outsourcing.Application.Queries.Apply;
using iPlatformExtension.Outsourcing.Infrastructure.Constants;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Outsourcing.Controllers;

[Tags("开案委外相关接口")]
[ApiController]
[Route("apply")]
public sealed class ApplyController(ISender sender) : ControllerBase
{
    [HttpGet("/app-case/{appCaseId}")]
    [EndpointName(nameof(GetAppCaseOutsourcingAsync))]
    [EndpointSummary("获取开案案件委外信息")]
    [EndpointDescription("获取开案案件境外代理和境外代理备注")]
    [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
    public Task<AppCaseOutsourcingDto> GetAppCaseOutsourcingAsync(
        [FromRoute, Required, Description("开案案件id")] string appCaseId)
    {
        return  sender.Send(new AppCaseOutsourcingQuery(appCaseId), HttpContext.RequestAborted);
    }

    [HttpPatch("/app-case/{appCaseId}")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    [EndpointName(nameof(UpdateAppCaseOutsourcingAsync))]
    [EndpointSummary("更新开案案件委外信息")]
    [EndpointDescription("作为备用接口单独更新开案案件中的委外信息。首选使用业务系统表单更新")]
    [Authorize(Policy = AuthorizationPolicyNames.ApplyOutSourcing)]
    public Task UpdateAppCaseOutsourcingAsync(
        [FromRoute, Required, Description("开案案件id")] string appCaseId,
        [FromBody] JsonPatchDocument<ApplyCasePatchDto> patchDocument)
    {
        return sender.Send(new UpdateAppCaseCommand(appCaseId, patchDocument), HttpContext.RequestAborted);
    }
}