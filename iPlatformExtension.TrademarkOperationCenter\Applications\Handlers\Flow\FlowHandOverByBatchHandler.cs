﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;
using System.Xml.Linq;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public sealed class FlowHandOverByBatchHandler : IRequestHandler<FlowHandOverByBatchCommand, List<FlowSubmitByBatchDto>>
    {
        private readonly IFreeSql _freeSql;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public FlowHandOverByBatchHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor)
        {
            _freeSql = freeSql;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<List<FlowSubmitByBatchDto>> Handle(FlowHandOverByBatchCommand request, CancellationToken cancellationToken)
        {
            var userid = _httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var CnName = _httpContextAccessor.HttpContext?.User.GetGivenName() ?? string.Empty;
            List<FlowSubmitByBatchDto> res = new List<FlowSubmitByBatchDto>();

            var flowActivitys = await _freeSql.Select<SysFlowActivity>().Where(fa => request.ObjId.Any(o => o == fa.ObjId) && fa.FlowType == request.FFlowType).ToListAsync();

            foreach (var flowActivity in flowActivitys)
            {
                bool success = true;
                try
                {
                    if (flowActivity != null)
                    {
                        flowActivity.CurUserId = request.FAuditUserID;
                        flowActivity.UpdateUserId = userid;
                        flowActivity.UpdateTime = DateTime.Now;
                        flowActivity.PrevAuditTypeId = SubmitTypeEnum.Handover;
                        await _freeSql.Update<SysFlowActivity>().SetSource(flowActivity).ExecuteAffrowsAsync();
                        var history = new SysFlowHistory()
                        {
                            HistoryId = Guid.NewGuid().ToString(),
                            ObjId = flowActivity.ObjId,
                            FlowType = flowActivity.FlowType,
                            FlowSubType = flowActivity.FlowSubType,
                            NodeId = flowActivity.CurNodeId,
                            AuditUserId = userid,
                            AuditCnName = CnName,
                            AuditTime = DateTime.Now,
                            Remark = request.FRemark,
                            AuditTypeId = SubmitTypeEnum.Handover
                        };
                        await _freeSql.Insert<SysFlowHistory>(history).ExecuteAffrowsAsync();
                    }
                }
                catch (Exception)
                {
                    success = false;
                }
                res.Add(new FlowSubmitByBatchDto() { ProcID = flowActivity.ObjId, Successfully = success });
            }

            return res;
        }

    }
}
