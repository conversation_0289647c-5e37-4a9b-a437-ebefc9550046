using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "qua_case_accident", DisableSyncStructure = true)]
	public partial class QuaCaseAccident {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "accident_id", StringLength = 50)]
		public string AccidentId { get; set; }

		[ Column(Name = "detail_id", StringLength = 50)]
		public string DetailId { get; set; }

	}

}
