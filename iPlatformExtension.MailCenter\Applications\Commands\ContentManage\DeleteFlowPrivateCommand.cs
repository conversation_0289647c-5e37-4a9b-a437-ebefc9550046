﻿using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.ContentManage
{
    /// <summary>
    /// 标签Id
    /// </summary>
    /// <param name="mailTagId"></param>
    public record DeleteFlowPrivateCommand(string FlowPrivateId) : IRequest, IUnitOfWorkCommandMysql;
}
