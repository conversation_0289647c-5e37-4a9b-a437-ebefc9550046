﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers
{
    /// <summary>
    /// 团队管理模块控制器
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    [Authorize]
    public class TeamController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// 构造函数注入中间件组件
        /// </summary>
        /// <param name="mediator"></param>
        public TeamController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 创建团队
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task CreateTeamAsync(InsertTeamCommand insertTeamCommand)
        {
            await _mediator.Send(insertTeamCommand);
        }

        /// <summary>
        /// 编辑团队
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        public async Task EditTeamAsync(EditTeamCommand editTeamCommand)
        {
            await _mediator.Send(editTeamCommand);
        }

        /// <summary>
        /// 删除团队
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public async Task DeleteTeamAsync(DeleteTeamCommand deleteTeamCommand)
        {
            await _mediator.Send(deleteTeamCommand);
        }

        /// <summary>
        /// 获取团队列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IEnumerable<TeamDto>> GetTeamListAsync([FromQuery] TeamQuery teamQuery)
        {
            return await _mediator.Send(teamQuery);
        }

        /// <summary>
        /// 获取移交团队列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("HandoverTeamList")]
        public async Task<IEnumerable<HandoverTeamListDto>> GetHandoverTeamListAsync()
        {
            return await _mediator.Send(new HandoverTeamListQuery());
        }

        /// <summary>
        /// 移交团队
        /// </summary>
        /// <returns></returns>
        [HttpPost("HandoverAllot")]
        public async Task GetHandoverTeamListAsync([FromBody] HandoverAllotCommand handoverAllotCommand)
        {
            await _mediator.Send(handoverAllotCommand);
        }

        /// <summary>
        /// 登陆用户团队列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetLoginTeamListQuery")]
        public async Task<IEnumerable<LoginTeamListDto>> GetLoginTeamListQueryAsync()
        {
            return await _mediator.Send(new LoginTeamListQuery());
        }

        /// <summary>
        /// 获取团队负责人
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetTeamLeaderQuery")]
        public async Task<IEnumerable<GetTeamLeaderDto>> GetTeamLeaderQueryAsync([FromBody]GetTeamLeaderQuery query)
        {
            return await _mediator.Send(query);
        }

    }
}
