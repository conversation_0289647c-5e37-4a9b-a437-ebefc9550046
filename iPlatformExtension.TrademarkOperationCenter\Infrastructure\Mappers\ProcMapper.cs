﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Mappers;

/// <summary>
/// 任务信息映射
/// </summary>
public sealed class ProcMapper : Profile
{
    /// <summary>
    /// 映射配置
    /// </summary>
    public ProcMapper()
    {
        CreateMap<ProcPropertiesDynamicConfig, ProcExtensionItem>();

        CreateMap<ProcTrademarkDeliveryDto, CaseProcInfo>()
            .ForMember(info => info.AgentUser, options =>
            {
                options.Condition(dto => dto.AgentUser is not null);
                options.MapFrom(dto => dto.AgentUser);
            });
        
        CreateMap<CaseProcInfo, ProcTrademarkDeliveryDto>();

        CreateMap<CaseProcApplicant, ProcApplicantDto>();

        CreateMap<ProcTrademarkLawBasisDto, TrademarkLawBasis>()
            .ForMember(basis => basis.Reason, expression => expression.MapFrom(dto => dto.FactualReason))
            .ForMember(basis => basis.LawId, expression => expression.MapFrom(dto => dto.LawBasisId))
            .ForMember(basis => basis.FileId, expression => expression.MapFrom(dto => dto.FileId ?? -1));

        CreateMap<ProcCitedTrademarkDto, ProcCitedTrademark>();

        CreateMap<ProcCitedTrademark, ProcCitedTrademarkDto>();

        CreateMap<ProcCitedTrademark, DeliveryCitedTrademark>()
            .ForMember(trademark => trademark.Id, expression => expression.Ignore());

        CreateMap<TrademarkLawBasis, DeliveryLawBasis>()
            .ForMember(basis => basis.Id, expression => expression.Ignore());

    }
}