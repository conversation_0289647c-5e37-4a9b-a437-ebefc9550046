﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class SuggestionProcPointConfigRepository(
    IFreeSql<PlatformFreeSql> freeSql, 
    UnitOfWorkManage<PlatformFreeSql> unitOfWorkManager) : 
    DefaultRepository<SuggestionProcPointConfig, int>(freeSql, unitOfWorkManager),
    ISuggestionProcPointConfigRepository;