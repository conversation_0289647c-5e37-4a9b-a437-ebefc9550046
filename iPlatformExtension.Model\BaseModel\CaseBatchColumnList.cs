using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_batch_column_list", DisableSyncStructure = true)]
	public partial class CaseBatchColumnList {

		[ Column(Name = "batch_column_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string BatchColumnId { get; set; }

		[ Column(Name = "allow_null")]
		public bool? AllowNull { get; set; }

		[ Column(Name = "batch_type", StringLength = 50)]
		public string BatchType { get; set; }

		[ Column(Name = "column_code", StringLength = 50)]
		public string ColumnCode { get; set; }

		[ Column(Name = "column_name", StringLength = 50)]
		public string ColumnName { get; set; }

		[ Column(Name = "column_type", StringLength = 50)]
		public string ColumnType { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "is_show")]
		public bool? IsShow { get; set; }

		[ Column(Name = "is_updatecolumn")]
		public bool? IsUpdatecolumn { get; set; }

		[ Column(Name = "left_join", StringLength = 500)]
		public string LeftJoin { get; set; }

		[ Column(Name = "name_as", StringLength = 200)]
		public string NameAs { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
