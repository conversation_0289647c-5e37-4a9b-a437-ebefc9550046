﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Public.Applications.Models.Statistics;



public record DueTimeErrorWarningDto()
{
    /// <summary>
    /// 官方期限计算
    /// </summary>
    /// <param name="Volume">案卷号</param>
    /// <param name="BelongCompany">案源分所</param>
    /// <param name="ManageCompany">管理分所</param>
    /// <param name="CaseName">案件名称</param>
    /// <param name="CtrlProcId">任务名称</param>
    /// <param name="IntFirstDate">初稿期限（内）</param>
    /// <param name="CusFirstDate">初稿期限（外）</param>
    /// <param name="IntFinishDate">定稿期限（内）</param>
    /// <param name="CusFinishDate">定稿期限（外）</param>
    /// <param name="LegalDueDate">官方期限</param>
    public DueTimeErrorWarningDto(string Volume, string? BelongCompany, string? ManageCompany, string? CaseName, string? CtrlProcId, DateTime? IntFirstDate, DateTime? CusFirstDate, DateTime? IntFinishDate, DateTime? CusFinishDate, DateTime? LegalDueDate, string? CustomerId, string? UndertakeUser) : this()
    {
        this.Volume = Volume;
        this.BelongCompany = BelongCompany;
        this.ManageCompany = ManageCompany;
        this.CaseName = CaseName;
        this.CtrlProcId = CtrlProcId;
        this.IntFirstDate = IntFirstDate;
        this.CusFirstDate = CusFirstDate;
        this.IntFinishDate = IntFinishDate;
        this.CusFinishDate = CusFinishDate;
        this.LegalDueDate = LegalDueDate;
        this.CustomerId = CustomerId;
        this.UndertakeUser = UndertakeUser;
    }

    [ExcelColumn(Name = "我方文号")]
    public string Volume { get; private set; }

    [ExcelColumn(Name = "案源分所", Ignore = true)]
    public string? BelongCompany { get; private set; }

    [ExcelColumn(Name = "管理分所", Ignore = true)]
    public string? ManageCompany { get; private set; }

    [ExcelColumn(Name = "案源分所")]
    public string? BelongCompanyValue { get; set; }

    [ExcelColumn(Name = "管理分所")]
    public string? ManageCompanyValue { get; set; }

    [ExcelColumn(Name = "案件名称")]
    public string? CaseName { get; private set; }

    [ExcelColumn(Name = "客户ID", Ignore = true)]
    public string? CustomerId { get; private set; }

    [ExcelColumn(Name = "客户名称")]
    public string? CustomerName { get; set; }

    [ExcelColumn(Name = "客户是否为专属")]
    public string? IsExclusive { get; set; }

    [ExcelColumn(Ignore = true)]
    public string? CtrlProcId { get; private set; }

    [ExcelColumn(Name = "任务名称")]
    public string? CtrlProcZhCn { get; set; }

    [ExcelColumn(Name = "任务承办人id", Ignore = true)]
    public string? UndertakeUser { get; private set; }

    [ExcelColumn(Name = "任务承办人")]
    public string? UndertakeUserValue { get; set; }

    [ExcelColumn(Name = "初稿期限(内)")]
    public DateTime? IntFirstDate { get; private set; }

    [ExcelColumn(Name = "初稿期限(外)")]
    public DateTime? CusFirstDate { get; private set; }

    [ExcelColumn(Name = "定稿期限(内)")]
    public DateTime? IntFinishDate { get; private set; }

    [ExcelColumn(Name = "定稿期限(外)")]
    public DateTime? CusFinishDate { get; private set; }

    [ExcelColumn(Name = "官方期限")]
    public DateTime? LegalDueDate { get; private set; }
}

