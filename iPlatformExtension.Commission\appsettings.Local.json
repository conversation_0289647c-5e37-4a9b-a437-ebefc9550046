{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information", "Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware": "Debug", "IFreeSql": "Information"}, "Console": {"FormatterOptions": {"TimestampFormat": "[yyyy-MM-dd HH:mm:ss.fff zzz]"}}, "FreeSql": {"Curd": {"IncludeCurdTypes": ["Select", "Delete", "Update", "Insert", "InsertOrUpdate"], "Environments": ["Local", "Development"]}}}, "NLog": {"autoReload": true, "throwConfigExceptions": true, "internalLogLevel": "Debug", "targets": {"async": true, "infoLog": {"type": "File", "encoding": "utf-8", "maxArchiveFiles": 7, "fileName": "${basedir}/logs/${shortdate}.log", "layout": "[${longdate}] [${uppercase:${level}}] [${logger}] [${activity:property=TraceId}] [${aspnet-request-url}] [${aspnet-mvc-action}] : ${newline}${message} ${newline}\n                ${exception:format=ToString} "}, "errorLog": {"type": "File", "encoding": "utf-8", "maxArchiveFiles": 7, "fileName": "${basedir}/logs/${shortdate}-error.log", "layout": "[${longdate}] [${uppercase:${level}}] [${logger}] [${activity:property=TraceId}] [${aspnet-request-url}] [${aspnet-mvc-action}] : ${newline}${message} ${newline}\n                ${exception:format=ToString} "}}, "rules": [{"logger": "*", "minLevel": "Info", "writeTo": "InfoLog", "filterDefaultAction": "Log", "filters": [{"action": "Ignore", "type": "when", "condition": "logger == 'IFreeSql' and contains(message, 'SELECT')"}]}, {"logger": "*", "minLevel": "Error", "writeTo": "errorLog"}]}, "ConnectionStrings": {"Default": "data source=***************,7433;initial catalog=acip_iplatform_1009;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true", "Redis": "*************:6379,password=Acip_cc@54,defaultDatabase=10", "Hangfire": "data source=***************,7433;initial catalog=acip_iplatform;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true;Min Pool Size=5;Max Pool Size=10;Connection Lifetime=300;MultipleActiveResultSets=True"}, "IPlatformAuth": {"Issuers": ["ipr.aciplaw.com"], "Audiences": ["patas.aciplaw.com", "patas-test.aciplaw.com", "patas-dev.aciplaw.com"], "SecurityKey": "MDBkMDVkMmVlZGI1MTQ4NDE0ZTI3ZmNiODI4MDdhMDA="}, "BladeAuth": {"Issuer": "", "Audience": "iPlatformExtend", "SecurityKey": "bladexisapowerfulmicroservicearchitectureupgradedandoptimizedfromacommercialproject"}, "PatentPushingOptions": {"ExcludeRules": [{"WorkflowName": "专利质量管理部质检提成排除规则", "Rules": [{"RuleName": "是否手动录入", "Expression": "list.IsAdd == true"}, {"RuleName": "是否为质检提成", "Expression": "list.BonusType == \"quality\""}, {"RuleName": "是否为专利质量管理部", "Expression": "commissionWeight.Weight.DeptName.Contains(\"专利质量管理部\")"}]}]}}