﻿using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.AuditUser
{
    /// <summary>
    /// 保存发件必审人命令
    /// </summary>
    /// <param name="Id">记录ID，为空表示新增</param>
    /// <param name="RequireAuditUserIds">必审人ID列表</param>
    /// <param name="DesignatedAuditUserIds">指定审核人ID列表</param>
    public record SaveFlowAuditUserCommand(
        string? Id,
        List<string> RequireAuditUserIds,
        List<string> DesignatedAuditUserIds) : IRequest<string>, IUnitOfWorkCommandMysql;
}
