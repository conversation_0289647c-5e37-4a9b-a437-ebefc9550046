using System.Diagnostics;
using Microsoft.Net.Http.Headers;

namespace iPlatformExtension.Common.Clients.HttpMessageHandlers;

public class W3CTraceContextHandler : DelegatingHandler
{
    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var activity = Activity.Current;
        if (activity is null) 
            return base.SendAsync(request, cancellationToken);
        
        var headers = request.Headers;
        headers.Add(HeaderNames.TraceParent, activity.Id);
        return base.SendAsync(request, cancellationToken);
    }
}