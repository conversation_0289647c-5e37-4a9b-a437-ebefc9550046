﻿// See https://aka.ms/new-console-template for more information

using System.Net.Http.Json;
using System.Text.Encodings.Web;
using System.Text.Json;
using ConsoleScript;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Model.Dto;

// var jsonOptions = new JsonSerializerOptions()
    // {
    //     WriteIndented = true,
    //     Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
    //     DefaultBufferSize = 128,
    //     PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    // };
    // jsonOptions.Converters.Add(new DateTimeJsonConverter());
    //
    // var httpClient = new HttpClient();
    // httpClient.BaseAddress= new Uri("http://localhost:5123");
    // var stream = await httpClient.GetStreamAsync("/supplier/case-count/input?manageCompanyId=1C0BCDD1-2BCC-4EF7-9E5A-20AC214E204D&caseDirection=IO&ctrlProcId=89EEDA70-E934-41C3-8858-39A909DB3EF9&applicationTypeId=AF89AF57-A489-4F9D-9F35-64338E68E767&startDate=2024-01-01&endDate=2024-12-31&supplierIds=1868933196105240578&supplierIds=1881981907957829633&supplierIds=1882265800866910209&supplierIds=1882322447698087938&supplierIds=1882681009817374722&supplierIds=1882681275211960321&supplierIds=1882681397983432705");
    // await foreach (var data in JsonSerializer.DeserializeAsyncEnumerable<ResultData<SupplierTrademarkProcCountDto>>(stream, true, jsonOptions))
    // {
    //     if (data is null)
    //     {
    //         break;
    //     }
    //
    //     Console.WriteLine(DateTime.Now.Ticks);
    //     Console.WriteLine(JsonSerializer.Serialize(data, jsonOptions));
    // }

    await new SyncPatentForeignSupplierTask().RunAsync();

