﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class ApplicantTypeRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    CacheExpirationToken<BasApplicantType> expirationToken,
    IRedisCache<RedisCacheOptionsBase> redisCache)
    : BaseRepository<BasApplicantType, string>(freeSql), IApplicantTypeRepository
{
    public IMemoryCache MemoryCache { get; } = memoryCache;

    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;

    public CacheExpirationToken<BasApplicantType> ExpirationToken { get; } = expirationToken;
}