﻿namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 受让人信息
/// </summary>
public struct AssigneeInfo
{
    public string country { get; set; }
    public int ownerType { get; set; }
    public int bookType { get; set; }
    public string applicantName { get; set; }
    public string applicantEnglishName { get; set; }
    public string applicantAddress { get; set; }
    public string applicantEnglishAddress { get; set; }
    public int prov { get; set; }
    public int city { get; set; }
    public int area { get; set; }
    public string code { get; set; }
    public int certificatesType { get; set; }
    public string newApplicantName { get; set; }
    public string idCard { get; set; }
    public string unifiedSocialCreditCode { get; set; }
    public string post { get; set; }
    public string legalPerson { get; set; }
    public int? subjectType { get; set; }
}