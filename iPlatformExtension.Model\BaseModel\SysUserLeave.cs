using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_user_leave", DisableSyncStructure = true)]
	public partial class SysUserLeave {

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "dept_id", StringLength = 50, IsNullable = false)]
		public string DeptId { get; set; }

		[ Column(Name = "end_time")]
		public DateTime? EndTime { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; }

		[ Column(Name = "leave_reason", StringLength = 300)]
		public string LeaveReason { get; set; }

		[ Column(Name = "leave_time")]
		public int? LeaveTime { get; set; }

		[ Column(Name = "start_time")]
		public DateTime? StartTime { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

		[ Column(Name = "work_agent", StringLength = 200)]
		public string WorkAgent { get; set; }

	}

}
