﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 商标法律依据信息
/// </summary>
[Table(Name = "deli_law_basis", DisableSyncStructure = true)]
public sealed class DeliveryLawBasis 
{

	/// <summary>
	/// 主键
	/// </summary>
	[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
	public int Id { get; set; }

	/// <summary>
	/// 创建时间
	/// </summary>
	[Column(Name = "creation_time", InsertValueSql = "getdate()")]
	public DateTime CreationTime { get; set; }

	/// <summary>
	/// 创建者
	/// </summary>
	[Column(Name = "creator", StringLength = 50, IsNullable = false)]
	public string Creator { get; set; } = "";

	/// <summary>
	/// file_list_a的主键
	/// </summary>
	[Column(Name = "file_id")]
	public int FileId { get; set; } = -1;

	/// <summary>
	/// 文件名称
	/// </summary>
	[Column(Name = "file_name", StringLength = 50, IsNullable = false)]
	public string FileName { get; set; } = "";

	/// <summary>
	/// 法律条款id（对应权大师）
	/// </summary>
	[Column(Name = "law_id", StringLength = 50, IsNullable = false)]
	public string LawId { get; set; } = string.Empty;

	/// <summary>
	/// 任务id
	/// </summary>
	[Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
	public string ProcId { get; set; } = "";

	/// <summary>
	/// 事实理由
	/// </summary>
	[Column(Name = "reason", StringLength = -2, IsNullable = false)]
	public string Reason { get; set; } = "";

	/// <summary>
	/// 更新时间
	/// </summary>
	[Column(Name = "update_time", InsertValueSql = "getdate()")]
	public DateTime UpdateTime { get; set; }

	/// <summary>
	/// 更新者
	/// </summary>
	[Column(Name = "updater", StringLength = 50, IsNullable = false)]
	public string Updater { get; set; } = "";

	/// <summary>
	/// 文件下载url
	/// </summary>
	[Column(Name = "url", StringLength = -2, IsNullable = false)]
	public string Url { get; set; } = "";

}