using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_weichao_yesterday_shenqin_backup", DisableSyncStructure = true)]
	public partial class VwWeichaoYesterdayShenqinBackup {

		[ Column(StringLength = 4000, IsNullable = false)]
		public string 案件备注 { get; set; }

		[ Column(StringLength = 100)]
		public string 案件类型 { get; set; }

		[ Column(StringLength = 100)]
		public string 案件流向 { get; set; }

		[ Column(StringLength = 500)]
		public string 案件名称 { get; set; }

		[ Column(StringLength = 500, IsNullable = false)]
		public string 承办部门 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 承办人 { get; set; }

		[ Column(StringLength = 200, IsNullable = false)]
		public string 承办人邮箱 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 代理人处理状态 { get; set; }

		[ Column(StringLength = 4000, IsNullable = false)]
		public string 个案要求 { get; set; }

		[ Column(StringLength = 1000)]
		public string 客户名称 { get; set; }

		[ Column(StringLength = 100)]
		public string 客户文号 { get; set; }

		[ Column(StringLength = -2, IsNullable = false)]
		public string 联系人 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 名义承办人 { get; set; }

		[ Column(StringLength = 10, IsNullable = false)]
		public string 配案日期 { get; set; }

		[ Column(StringLength = 4000, IsNullable = false)]
		public string 任务备注 { get; set; }

		[ Column(StringLength = 50)]
		public string 任务名称 { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string 任务属性 { get; set; }

		[ Column(StringLength = 50)]
		public string 申请类型 { get; set; }

		[ Column(StringLength = -2)]
		public string 申请人 { get; set; }

		[ Column(StringLength = 50)]
		public string 我方文号 { get; set; }

	}

}
