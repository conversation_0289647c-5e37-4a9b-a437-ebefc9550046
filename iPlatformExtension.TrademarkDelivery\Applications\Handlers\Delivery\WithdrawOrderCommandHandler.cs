﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery;

internal sealed class WithdrawOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IDeliveryInfoRepository deliveryInfoRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    UnitOfWorkManager unitOfWorkManager,
    ILoggerFactory loggerFactory,
    PhoenixClientFactory phoenixClientFactory)
    :
        PhoenixDeliveryHandleBase<WithdrawOrderCommand, PhoenixOrderOperationParameters,
            PhoenixResponseParameters<OrderData>>(stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository,
            unitOfWorkManager, loggerFactory, phoenixClientFactory),
        IDeliveryHandler<WithdrawOrderCommand, PhoenixOrderOperationParameters, PhoenixResponseParameters<OrderData>>
{
    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.WithdrawDelivery;

    public override Task<PhoenixOrderOperationParameters> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        return Task.FromResult(new PhoenixOrderOperationParameters()
        {
            OrderNo = _deliveryInfo.OrderNo,
            NiceClassNumbers = string.Join(',',  _deliveryInfo.OtherInfo?.TrademarkNiceClasses?.Split(';')
                .Select(category => category.PadLeft(2, '0')) ?? ["01"]),
        });
    }

    public override Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(PhoenixOrderOperationParameters request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_phoenixClient);
        return _phoenixClient.OperateOrderAsync(PhoenixUri.WithdrawOrder, request);
    }

    public override async Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);

        await Handler.UpdateDeliveryInfoInternalAsync(deliveryInfo =>
        {
            deliveryInfo.OperationResult = true;
            return ValueTask.CompletedTask;
        }, cancellationToken);
    }

    public Task<bool> HandleExceptionAsync(Exception ex, CancellationToken cancellationToken = default)
    {
        return Handler.HandleExceptionInternalAsync(ex, false, true, cancellationToken);
    }
}