﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class InsertDisplayCommandHandler(IDeliveryDisplayJsonRepository displayJsonRepository)
    : IRequestHandler<InsertDisplayCommand, DeliveryDisplayJson>
{
    public Task<DeliveryDisplayJson> Handle(InsertDisplayCommand request, CancellationToken cancellationToken)
    {
        return displayJsonRepository.InsertAsync(new DeliveryDisplayJson()
        {
            Creator = request.OperatorId,
            CreationTime = DateTime.Now,
            DisplayJson = request.DisplayJson,
            ProcId = request.ProcId,
            Updater = request.OperatorId,
            UpdateTime = DateTime.Now
        }, cancellationToken);
    }
}