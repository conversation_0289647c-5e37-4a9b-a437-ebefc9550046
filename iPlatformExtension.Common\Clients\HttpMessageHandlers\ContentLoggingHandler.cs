﻿using System.Diagnostics;
using iPlatformExtension.Common.Clients.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Clients.HttpMessageHandlers;

/// <summary>
/// 请求第三方接口时，记录对应的请求报文和响应报文
/// </summary>
public class ContentLoggingHandler : DelegatingHandler
{
    private readonly ILogger<ContentLoggingHandler> _logger;

    private readonly IOptionsMonitor<HttpClientLoggingOptions> _options;

    /// <summary>
    /// 构造函数注入
    /// </summary>
    /// <param name="logger">日志组件</param>
    /// <param name="options">选项属性</param>
    public ContentLoggingHandler(ILogger<ContentLoggingHandler> logger, IOptionsMonitor<HttpClientLoggingOptions> options)
    {
        _logger = logger;
        _options = options;
    }
    
    /// <summary>
    /// 记录请求对方接口的请求报文和响应报文
    /// </summary>
    /// <param name="request">请求消息</param>
    /// <param name="cancellationToken">异步取消令牌</param>
    /// <returns>响应消息</returns>
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var options = _options.CurrentValue;

        var path = request.RequestUri?.AbsolutePath;
        _logger.LogInformation("requestPath: {}", path);
        var needToLog = options.TryGetValue(path ?? string.Empty, out var loggingOptions);
        if (needToLog && (loggingOptions?.RequestBody ?? false) && request.Content != null)
        {
            var requestBody = await request.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogInformation(new EventId(30, Activity.Current?.TraceId.ToString()), "request body: {requestBody}", requestBody);
        }
        var response = await base.SendAsync(request, cancellationToken);
        if (needToLog && (loggingOptions?.ResponseBody ?? false))
        {
            var responseBody = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogInformation(new EventId(30, Activity.Current?.TraceId.ToString()), "response body: {responseBody}", responseBody);
        }
        return response;
    }
}