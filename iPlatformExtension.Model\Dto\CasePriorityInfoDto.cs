﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto
{
    public class CasePriorityInfoDto
    {
		/// <summary>
		/// 优先权主键ID
		/// </summary>
		public string PriorityId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 案件ID
		/// </summary>
		[JsonIgnore]
		public string CaseId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 接入码
		/// </summary>
		[JsonPropertyName("dasCode")]
		public string? DasCode { get; set; }

		/// <summary>
		/// 优先权地区
		/// </summary>
		[JsonPropertyName("priorityCountry")]
		public string PriorityCountryId { get; set; }

		/// <summary>
		/// 优先权日
		/// </summary>
		[JsonPropertyName("priorityDate")]
		public DateTime? PriorityDate { get; set; }

		/// <summary>
		/// 优先权号
		/// </summary>
		[JsonPropertyName("priorityNo")]
		public string PriorityNo { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		public short? Seq { get; set; }

		/// <summary>
		/// 我方文号
		/// </summary>
		[JsonPropertyName("caseCode")]
		public string Volume { get; set; }
		
	}
}
