﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class SystemFormBaseColumnsRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    CacheExpirationToken<SysFormBaseColumns> expirationToken,
    IRedisCache<RedisCacheOptionsBase> redisCache)
    : BaseRepository<SysFormBaseColumns, string>(freeSql), ISystemFormBaseColumnsRepository
{
    public IMemoryCache MemoryCache { get; } = memoryCache;
    
    public CacheExpirationToken<SysFormBaseColumns> ExpirationToken { get; } = expirationToken;
    
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}