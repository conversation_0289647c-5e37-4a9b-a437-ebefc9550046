﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Commands.Proc;

/// <summary>
/// 移除任务外所联系人
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="ContactId">联系人id</param>
/// <param name="Version">任务版本号</param>
[Description("任务境外代理联系人移除参数")]
public sealed record RemoveProcSupplierContactCommand(
    [property: Description("任务id"), Required] string ProcId, 
    [property: Description("联系人id"), Required] string ContactId, 
    [property: Description("任务版本号"), Required] int Version) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;