﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using MediatR;

namespace iPlatformExtension.Public.Applications.Commands.MyTrademarkCase;

/// <summary>
/// 保存公认证规则
/// </summary>
public record SaveAuthenticationRulesCommand(AuthenticationRulesDto dto) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

