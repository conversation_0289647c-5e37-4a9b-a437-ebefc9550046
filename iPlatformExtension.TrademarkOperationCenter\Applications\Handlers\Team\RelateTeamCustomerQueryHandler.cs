﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;
using System.Linq;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 获取关联客户列表
    /// </summary>
    public class RelateTeamCustomerQueryHandler : IRequestHandler<RelateTeamCustomerQuery, RelateTeamCustomerDto>
    {
        private readonly IFreeSql _freeSql;

        public RelateTeamCustomerQueryHandler(IFreeSql freeSql)
        {
            _freeSql = freeSql;
        }

        public async Task<RelateTeamCustomerDto> Handle(RelateTeamCustomerQuery request, CancellationToken cancellationToken)
        {
            //获取当前已配置所有客户
            var allCustomer = _freeSql.Select<SysTeamCustomer>().ToList(it => new { it.CustomerId, it.TeamId });

            //获取当前团队客户
            var selectCustomerList = allCustomer.Where(it => it.TeamId == request.TeamId).Select(it => it.CustomerId).ToList();
            var any = allCustomer.Where(it => it.TeamId != request.TeamId).Select(it => it.CustomerId).ToList();
            var customerList = _freeSql.Select<CusCustomer, SysUserInfo>()
                .LeftJoin((c, u) => c.BusiUserId == u.UserId)
                .WhereIf(any.Any(), (c, u) => !any.Contains(c.CustomerId))
                .Where((c, u) => c.IsEnabled == true)
                .ToList((c, u) => new CustomerInfoDto(c.CustomerId, c.CustomerName, c.BusiUserId, u.CnName));

            return new RelateTeamCustomerDto(customerList.Where(it => !selectCustomerList.Contains(it.CustomerId)),
                customerList.Where(it => selectCustomerList.Contains(it.CustomerId)));
        }
    }
}

