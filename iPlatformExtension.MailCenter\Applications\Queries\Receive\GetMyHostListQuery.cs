﻿using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.Receive
{
    public class GetMyHostListQuery : QueryBase, IRequest<PageResult<GetMailHostListDto>>
    {
        /// <summary>
        /// 全部:all/查看:read/发件:write/分拣:sorter/管理:manager
        /// </summary>
        public string AccessMode { get; set; }
        
        /// <summary>
        /// 模糊查询
        /// </summary>
        public string? Search { get; set; }
    }
}
