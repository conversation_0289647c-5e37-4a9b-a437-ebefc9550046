﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting.Internal;
using Microsoft.Extensions.Hosting;
using Microsoft.Identity.Client;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.Clients.WxWork
{
    public static class WxConfiguration
    {
        public static void AddWxConfiguration(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<IWxWorkClient>(new WxWorkClient(configuration));
        }
    }
}
