﻿namespace iPlatformExtension.Public.Applications.Models.MyTrademarkCase;

/// <summary>
/// 公认证列表
/// </summary>
/// <param name="Id">主键</param>
/// <param name="RecognizedCertificateId">公认证标识id</param>
/// <param name="CtrlProcId">任务id</param>
/// <param name="CtrlProcZhCn">任务名称</param>
/// <param name="CountryId">国家/地区</param>
/// <param name="CountryIds">国家/地区</param>
/// <param name="IsEnable">是否有效</param>
/// <param name="CreateUserId">创建用户id</param>
/// <param name="UpdateUserId">更新用户id</param>
/// <param name="CreateTime">创建时间</param>
/// <param name="UpdateTime">更新时间</param>
/// <param name="Mark">备注</param>
public record GetPublicAuthenticationRulesListDto(
    string Id,
    string RecognizedCertificateId,
    string CtrlProcId,
    string CtrlProcZhCn,
    string CountryId,
    bool IsEnable,
    string CreateUserId,
    string UpdateUserId,
    DateTime CreateTime,
    DateTime? UpdateTime,
    string Mark)
{
    public string CreateUserName { get; set; }
    public string UpdateUserName { get; set; }
    public List<string>? CountryIds { get; set; }
    public List<string>? CountryZhCn { get; set; }

};

