using iPlatformExtension.Common.Filters;
using iPlatformExtension.Public.Applications.Commands.File;
using iPlatformExtension.Public.Applications.Models.File;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 文件控制器
/// </summary>
[ApiController]
[Route("[controller]")]
public sealed class FileController : ControllerBase
{
    private readonly IMediator _mediator;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="mediator"></param>
    public FileController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// 文件上传
    /// </summary>
    /// <param name="dto">文件上传参数</param>
    /// <returns>文件id</returns>
    [HttpPost]
    [DisableFormValueModelBinding]
    [RequestFormLimits(BufferBodyLengthLimit = long.MaxValue, MultipartBodyLengthLimit = long.MaxValue)]
    public async Task<long> UploadFileAsync([FromForm] FileUploadDto dto)
    {
        return await _mediator.Send(new UploadFileCommand(dto));
    }
}