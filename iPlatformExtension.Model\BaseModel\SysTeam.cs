﻿using System;
using System.Collections.Generic;
using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{

    [Table(Name = "sys_team", DisableSyncStructure = true)]
    public partial class SysTeam
    {

        /// <summary>
        /// 团队id
        /// </summary>
        [Column(Name = "team_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string TeamId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column(Name = "create_time")]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        [Column(Name = "create_user_id", StringLength = 50, IsNullable = false)]
        public string CreateUserId { get; set; }

        /// <summary>
        /// 状态:1-正常，2-删除
        /// </summary>
        [Column(Name = "enable")]
        public int Enable { get; set; }

        /// <summary>
        /// 是否生效
        /// </summary>
        [Column(Name = "is_effect")]
        public bool IsEffect { get; set; } = false;

        /// <summary>
        /// 是否专属
        /// </summary>
        [Column(Name = "is_exclusive")]
        public bool IsExclusive { get; set; } = false;

        /// <summary>
        /// 团队说明
        /// </summary>
        [Column(Name = "team_description", StringLength = 500)]
        public string TeamDescription { get; set; }

        /// <summary>
        /// 团队名称
        /// </summary>
        [Column(Name = "team_name", StringLength = 50)]
        public string TeamName { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [Column(Name = "seq")]
        public int? Seq { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column(Name = "update_time")]
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 更新员工id
        /// </summary>
        [Column(Name = "update_user_id", StringLength = 50)]
        public string UpdateUserId { get; set; }

        /// <summary>
        /// 授权用户id
        /// </summary>
        [Column(Name = "authorize_user", StringLength = 1000)]
        public string AuthorizeUser { get; set; }

    }

}
