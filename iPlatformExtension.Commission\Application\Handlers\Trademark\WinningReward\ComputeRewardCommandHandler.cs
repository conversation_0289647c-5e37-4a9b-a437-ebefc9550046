﻿using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Commission.Infrastructure.Logging;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class ComputeRewardCommandHandler(
    IFreeSql<PlatformFreeSql> freeSql, 
    ILogger<ComputeRewardCommandHandler> logger
    ) : IRequestHandler<ComputeRewardCommand, bool>
{
    public async Task<bool> Handle(ComputeRewardCommand request, CancellationToken cancellationToken)
    {
        var leaveDeadline = DateTime.Today.AddMonths(-3);
        var proc = request.RewardProc;
        var procId = proc.ProcId;
        var matchedRules = await freeSql.Select<WinningRewardRule>()
            .Where(rule => rule.IsEnabled == true)
            .Where(rule => rule.CaseDirection == proc.CaseDirection)
            .Where(rule => rule.CtrlProcId == proc.CtrlProcId)
            .Where(rule => rule.RulingResult == proc.RulingResult)
            .Where(rule => rule.SituationChanged == proc.SituationChanged)
            .ToListAsync(cancellationToken);

        if (matchedRules.Count != 1)
        {
            logger.LogInvalidMatchedDomesticRewardRuleCount(procId, matchedRules.Count);
            return false;
        }
        
        var matchedRule = matchedRules[0];
        
        var undertakerReward = proc.Beneficiaries[WinningRewardBeneficiaryType.Undertaker];
        if (string.IsNullOrWhiteSpace(undertakerReward.UserId))
        {
            logger.LogUndertakerIsNull(procId);
            return false;
        }

        var undertakerLeaveDate = await freeSql.Select<SysUserInfo>(undertakerReward.UserId)
            .Where(info => info.IsEnabled == false)
            .ToOneAsync(info => info.LeaveDate, cancellationToken);
        if (undertakerLeaveDate != null && undertakerLeaveDate.Value.Date <= leaveDeadline)
        {
            proc.Beneficiaries.Remove(WinningRewardBeneficiaryType.Undertaker);
        }
        else
        {
            undertakerReward.Reward = matchedRule.WriterReward;
        }

        var mentorMatchedNodeId = matchedRule.MentorFlowNodeId;
        var mentorId = await freeSql.Select<SysFlowHistory>().WithLock()
            .InnerJoin<CaseProcFlow>((history, flow) => history.ObjId == flow.ProcFlowId)
            .Where<CaseProcFlow>(flow => flow.ProcId == procId)
            .Where(history => history.NodeId == mentorMatchedNodeId)
            .Where(history => history.FlowType == FlowType.Proofreading)
            .Where(history => history.FlowSubType == "TII")
            .Where(history => history.AuditTypeId == FlowAuditType.Submit)
            .OrderByDescending(history => history.AuditTime)
            .FirstAsync(history => history.AuditUserId, cancellationToken);

       
        if (mentorId != null)
        {
            var mentorLeaveDate = await freeSql.Select<SysUserInfo>(mentorId)
                .Where(info => info.IsEnabled == false)
                .ToOneAsync(info => info.LeaveDate, cancellationToken);
        
            // 如果导师未离职或离职时间晚于截止日期，则添加为受益人
            if (!mentorLeaveDate.HasValue || mentorLeaveDate.Value > leaveDeadline)
            {
                proc.Beneficiaries[WinningRewardBeneficiaryType.Mentor] = new WinningRewardUser()
                {
                    BeneficiaryType = WinningRewardBeneficiaryType.Mentor,
                    UserId = mentorId,
                    ProcId = procId,
                    Reward = matchedRule.MentorReward
                };
            }
        }


        return proc.Beneficiaries.Count > 0;
    }
}