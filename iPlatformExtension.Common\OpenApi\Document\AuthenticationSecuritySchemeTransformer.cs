﻿using iPlatformExtension.Common.Authentication.BladeAuth;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.OpenApi;
using Microsoft.Net.Http.Headers;
using Microsoft.OpenApi.Models;

namespace iPlatformExtension.Common.OpenApi.Document;

public class AuthenticationSecuritySchemeTransformer(IAuthenticationSchemeProvider schemeProvider) : IOpenApiDocumentTransformer
{
    public async Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context,
        CancellationToken cancellationToken)
    {
        var authenticationSchemes = await schemeProvider.GetAllSchemesAsync();
        var securitySchemes = authenticationSchemes.ToDictionary(scheme => scheme.Name, scheme =>
            new OpenApiSecurityScheme()
            {
                Type = SecuritySchemeType.Http,
                Scheme = "bearer",
                In = ParameterLocation.Header,
                BearerFormat = "Json Web Token",
                Description = $"JWT Bearer {scheme.DisplayName}",
                Name = scheme.Name == BladeAuthOptions.SchemeName ? "blade-auth" : HeaderNames.Authorization,
                Reference = new OpenApiReference()
                {
                    Id = scheme.Name,
                    Type = ReferenceType.SecurityScheme
                }
            });

        document.Components ??= new OpenApiComponents();
        document.Components.SecuritySchemes = securitySchemes;
        
        // foreach (var (operationType, operation) in document.Paths.Values.SelectMany(path => path.Operations))
        // {
        //     operation.Security.Add(new OpenApiSecurityRequirement
        //     {
        //         [new OpenApiSecurityScheme
        //         {
        //             Reference = new OpenApiReference
        //             {
        //                 Id = "Bearer", 
        //                 Type = ReferenceType.SecurityScheme
        //             }
        //         }] = Array.Empty<string>()
        //     });
        // }
    }
}