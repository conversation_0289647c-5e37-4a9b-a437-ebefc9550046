﻿using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Net.Mime;
using System.Security.Claims;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Authorization.SendDelivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Filters;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers;

/// <summary>
/// 递交信息控制器
/// </summary>
[ApiController]
[Route("[controller]")]
[Authorize]
[Produces(MediaTypeNames.Application.Json)]
[Consumes(MediaTypeNames.Application.Json)]
public sealed class DeliveryInfoController(
    IMediator mediator,
    IMemoryCache memoryCache,
    ILogger<DeliveryInfoController> logger,
    IRedisCache<RedisCacheOptionsBase> redisCache) : ControllerBase
{


    internal const string UpdateDeliveryResultKey = nameof(UpdateDeliveryResultKey);

    internal const string RequestLockTracingKey = nameof(RequestLockTracingKey);

    /// <summary>
    /// 保存新的递交信息
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <returns>递交信息数据</returns>
    [HttpPost]
    [HttpPut]
    [Route("{procId}")]
    public async Task<TrademarkDeliveryDto?> SaveAsync(string procId)
    {
        var operatorId = HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
        if (string.IsNullOrWhiteSpace(operatorId))
        {
            throw new NotFoundException(HttpContext.Request.Headers.Authorization, "用户");
        }

        await mediator.Send(new SaveDeliveryCommand(procId, operatorId));
        return await mediator.Send(new DeliverySnapshotQuery(procId));
    }

    /// <summary>
    /// 获取商标任务的递交信息
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <returns>递交信息数据</returns>
    [HttpGet("{procId}")]
    [AllowAnonymous]
    public Task<TrademarkDeliveryDto?> GetAsync([Required] string procId)
    {
        return mediator.Send(new DeliverySnapshotQuery(procId));
    }

    /// <summary>
    /// 获取递交任务的状态
    /// </summary>
    /// <param name="procId"></param>
    /// <returns></returns>
    [HttpGet("{procId}/status")]
    public Task<DeliveryInfoStatus> GetStatusAsync([Required] string procId)
    {
        return mediator.Send(new DeliveryInfoStatusQuery(procId));
    }

    /// <summary>
    /// 添加递交文件
    /// </summary>
    /// <param name="caseFileIds">文件id</param>
    /// <param name="procId">递交对应的任务id</param>
    /// <returns>添加后的递交文件快照信息</returns>
    [HttpPost("{procId}/files")]
    public async Task<IEnumerable<FileInfoSnapshot>> CreateDeliveryFilesAsync([FromBody] IEnumerable<string> caseFileIds, [FromRoute] string procId)
    {
        await mediator.Send(new BuildDeliveryFilesCommand(procId, caseFileIds));
        return await mediator.Send(new DeliveryFileSnapshotQuery(procId));
    }

    /// <summary>
    /// 移除递交文件信息
    /// </summary>
    /// <param name="fileIds">文件id</param>
    /// <param name="procId">递交对应的任务id</param>
    /// <returns>移除后的递交文件快照信息</returns>
    [HttpDelete("{procId}/files")]
    public async Task<IEnumerable<FileInfoSnapshot>> DeleteDeliveryFilesAsync([FromBody] IEnumerable<string> fileIds, [FromRoute] string procId)
    {
        await mediator.Send(new DeleteDeliveryFilesCommand(procId, fileIds));
        await mediator.Send(new DeleteOldDeliveryFilesCommand(procId));
        return await mediator.Send(new DeliveryFileSnapshotQuery(procId));
    }

    /// <summary>
    /// 发起递交
    /// </summary>
    /// <param name="procId">递交任务ID</param>
    /// <param name="button">递交按钮枚举值</param>
    /// <returns>统一响应接口</returns>
    [HttpPut("{procId}/order")]
    [Authorize("SendDelivery", AuthenticationSchemes = "IPlatformAuth")]
    public async Task SendDeliveryAsync(string procId, [FromQuery] DeliveryButton button)
    {
        var sendInternalCommand =
            await mediator.Send(new SendDeliveryCommand(procId, button, HttpContext.User.GetUserId()));
        if (sendInternalCommand is not null)
        {
            await mediator.Send(sendInternalCommand);
        }
    }

    /// <summary>
    /// 发起批量自动递交
    /// </summary>
    /// <param name="procIds">任务id集合</param>
    /// <param name="button">递交按钮枚举值</param>
    /// <returns></returns>
    [HttpPost("orders")]
    [BatchDeliveriesAuthorize(BatchDeliveriesValidateType.Deliver)]
    public Task SendBatchDeliveriesAsync(IEnumerable<string> procIds, [FromQuery, Required] DeliveryButton button)
    {
        return mediator.Send(new SendBatchDeliveriesCommand(procIds, button, HttpContext.User.GetUserId()),
            HttpContext.RequestAborted);
    }

    /// <summary>
    /// 获取递交历史记录
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <returns></returns>
    [HttpGet("{procId}/histories")]
    public Task<IEnumerable<DeliveryHistoryDto>> GetHistoriesAsync(string procId)
    {
        return mediator.Send(new DeliveryHistoryQuery(procId));
    }

    /// <summary>
    /// 获取递交预览结果
    /// </summary>
    /// <param name="resultId"></param>
    /// <returns></returns>
    [HttpGet("result/{resultId:int}")]
    public Task<string> GetResultAsync(int resultId)
    {
        return mediator.Send(new DisplayJsonResultQuery(resultId));
    }

    /// <summary>
    /// 保存递交结果
    /// </summary>
    /// <param name="procId">递交任务id</param>
    /// <param name="dto">递交结果数据</param>
    /// <returns>公共响应</returns>
    [HttpPost("{procId}/result")]
    [TypeFilter<ReleaseUpdateLockFilter>]
    public async Task UpdateResultAsync([FromRoute] string procId, [FromBody] DeliveryResultDto dto)
    {
        var isAuto = IsAuto();
        if (await redisCache.TryLockAsync(UpdateDeliveryResultKey, procId, TimeSpan.FromMinutes(1)))
        {
            var traceId = Activity.Current?.SpanId.ToString() ?? ControllerContext.HttpContext.TraceIdentifier;
            var lockTracingKey = $"{RequestLockTracingKey}:{traceId}";
            memoryCache.Set(lockTracingKey, procId);
            logger.LogLockProcSuccessfully(procId, Activity.Current?.TraceId.ToString());

            var hasNext = await mediator.Send(new SaveDeliveryResultCommand(procId, dto, HttpContext.User.GetUserId(), isAuto));

            await redisCache.RemoveCacheValueAsync(UpdateDeliveryResultKey, procId);
            memoryCache.Remove(lockTracingKey);
            logger.LogReleaseProcSuccessfully(procId, Activity.Current?.TraceId.ToString());

            logger.LogInformation("是否需要出发流程：{HasNext}", hasNext);
            if (!hasNext) return;

            switch (dto.Operation)
            {
                case TrademarkDeliveryOperation.SubmitOfficial when dto.Success:
                    await mediator.Send(new NextFlowCommand(procId, NodeCode: "TII_DE_End", isAuto, Remark: "递交成功"));
                    break;
                case TrademarkDeliveryOperation.WithdrawDelivery when dto.Success:
                    await mediator.Send(new NextFlowCommand(procId, "DJ", isAuto, isAuto ? "撤回已递交" : dto.Message, FlowSubmitType.Reject));
                    break;
            }
        }
        else
        {
            logger.LogReleaseProcFailed(procId, Activity.Current?.TraceId.ToString());
            throw new ApplicationException($"无法锁定任务：[{procId}]。请稍后重试");
        }

        return;

        bool IsAuto()
        {
            var username = HttpContext.User.GetGivenName();
            return !string.IsNullOrWhiteSpace(username) && username.Contains("权大师");
        }
    }

    /// <summary>
    /// 删除递交
    /// </summary>
    /// <param name="procId"></param>
    /// <returns>统一公共接口</returns>
    [HttpDelete("{procId}")]
    public async Task DeleteAsync(string procId)
    {
        await mediator.Send(new DeleteOldDeliveryFilesCommand(procId));
        await mediator.Send(new CancelOrderCommand(procId));
        await mediator.Send(new DeleteDeliveryCommand(procId, false));
    }



    /// <summary>
    /// 更新递交信息
    /// </summary>
    /// <param name="procId">任务ID</param>
    /// <param name="patchDocument">更新数据</param>
    /// <returns>统一响应接口</returns>
    [HttpPatch("{procId}")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    public Task UpdateAsync([FromRoute] string procId, [FromBody] JsonPatchDocument<DeliInfo> patchDocument)
    {
        var operatorId = HttpContext.User.GetUserId();
        return mediator.Send(new UpdateDeliveryCommand(procId, patchDocument, operatorId));
    }

    /// <summary>
    /// 创建或更新批量递交数据
    /// </summary>
    /// <param name="procItems">递交任务项</param>
    /// <param name="refresh">创建为false。更新为true。</param>
    /// <returns>递交数据预览集合</returns>
    [HttpPut("batch")]
    public async Task<IEnumerable<ProcItemDto>> CreateOrUpdateBatchAsync(IEnumerable<DeliveryItem> procItems, [FromQuery, Required] bool refresh)
    {
        var procIds = procItems.Select(item => item.ProcId);
        await mediator.Send(new SaveBatchDeliveryCommand(procItems, refresh), HttpContext.RequestAborted);
        return await mediator.Send(new DeliveriesQuery(procIds));
    }

    /// <summary>
    /// 批量删除递交数据
    /// </summary>
    /// <param name="procItems">递交数据项</param>
    /// <returns>公共统一接口</returns>
    [HttpDelete("batch")]
    public Task DeleteBatchAsync(IEnumerable<DeliveryItem> procItems)
    {
        return mediator.Send(
            new DeleteBatchDeliveryCommand(procItems.Select(item => new DeleteDeliveryBatchItemCommand(item.ProcId, item.Version))),
            HttpContext.RequestAborted);
    }

    /// <summary>
    /// 查询代操作的递交数据
    /// </summary>
    /// <param name="procIds">递交任务id集合</param>
    /// <returns>递交预览数据</returns>
    [HttpPost("batch")]
    public Task<IEnumerable<ProcItemDto>> GetBatchAsync(IEnumerable<string> procIds)
    {
        return mediator.Send(new DeliveriesQuery(procIds));
    }

    /// <summary>
    /// 递交流程批量操作验证
    /// </summary>
    /// <param name="procIds">任务id集合</param>
    /// <param name="validateType">验证类型</param>
    /// <returns>批量操作验证结果</returns>
    [HttpPost("batch/delivery/validation")]
    public async Task ValidateBatchDeliveriesAsync(IEnumerable<string> procIds, [FromQuery] BatchDeliveriesValidateType validateType)
    {

        var result = validateType switch
        {
            BatchDeliveriesValidateType.Startup => await mediator.Send(new StartupDeliveryValidateCommand(procIds),
                HttpContext.RequestAborted),
            BatchDeliveriesValidateType.Update => await mediator.Send(new UpdateDeliveryValidateCommand(procIds),
                HttpContext.RequestAborted),
            BatchDeliveriesValidateType.FlowSubmit => await mediator.Send(new SubmitDeliveryValidateCommand(procIds),
                HttpContext.RequestAborted),
            BatchDeliveriesValidateType.Deliver => await mediator.Send(new LaunchDeliveryValidateCommand(procIds),
                HttpContext.RequestAborted),
            BatchDeliveriesValidateType.HandOver => await mediator.Send(new HandOverDeliveryValidateCommand(procIds),
                HttpContext.RequestAborted),
            BatchDeliveriesValidateType.Reject => await mediator.Send(new RejectDeliveryValidateCommand(procIds),
                HttpContext.RequestAborted),
            BatchDeliveriesValidateType.StartupByT => await mediator.Send(new StartupDeliveryValidateByTCommand(procIds),
         HttpContext.RequestAborted),
            _ => throw new ArgumentOutOfRangeException(nameof(validateType))
        };
        result.ThrowIfValidateFail();
    }
}