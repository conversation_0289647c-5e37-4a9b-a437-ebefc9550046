using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_request_object", DisableSyncStructure = true)]
	public partial class CusRequestObject {

		/// <summary>
		/// 请款对象标识ID
		/// </summary>
		[ Column(Name = "request_object_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string RequestObjectId { get; set; }

		/// <summary>
		/// 账户编号
		/// </summary>
		[ Column(Name = "account_no", StringLength = 50)]
		public string AccountNo { get; set; }

		/// <summary>
		/// 客户地址
		/// </summary>
		[ Column(Name = "address_cn", StringLength = 500)]
		public string AddressCn { get; set; }

		/// <summary>
		/// 银行名称
		/// </summary>
		[ Column(Name = "bank_name", StringLength = 50)]
		public string BankName { get; set; }

		/// <summary>
		/// 账单语言
		/// </summary>
		[ Column(Name = "bill_language", StringLength = 50, IsNullable = false)]
		public string BillLanguage { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建者ID
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 客户ID
		/// </summary>
		[ Column(Name = "customer_id", StringLength = 50, IsNullable = false)]
		public string CustomerId { get; set; }

		[ Column(Name = "flow_cur_user_id", StringLength = 50)]
		public string FlowCurUserId { get; set; }

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		[ Column(Name = "flow_update_time", StringLength = 50)]
		public string FlowUpdateTime { get; set; }

		/// <summary>
		/// 纳税识别号
		/// </summary>
		[ Column(Name = "identify_number", StringLength = 50)]
		public string IdentifyNumber { get; set; }

		/// <summary>
		/// 发票代码
		/// </summary>
		[ Column(Name = "invoices_code", StringLength = 80)]
		public string InvoicesCode { get; set; }

		/// <summary>
		/// 发票抬头
		/// </summary>
		[ Column(Name = "invoices_title", StringLength = 200, IsNullable = false)]
		public string InvoicesTitle { get; set; }

		/// <summary>
		/// 发票类型
		/// </summary>
		[ Column(Name = "invoices_type", StringLength = 50)]
		public string InvoicesType { get; set; }

		/// <summary>
		/// 服务费发票类型
		/// </summary>
		[ Column(Name = "invoices_type_a", StringLength = 50)]
		public string InvoicesTypeA { get; set; }

		/// <summary>
		/// 国内官费发票类型
		/// </summary>
		[ Column(Name = "invoices_type_o_i", StringLength = 50)]
		public string InvoicesTypeOI { get; set; }

		/// <summary>
		/// 国外官费发票类型
		/// </summary>
		[ Column(Name = "invoices_type_o_o", StringLength = 50)]
		public string InvoicesTypeOO { get; set; }

		/// <summary>
		/// 第三方发票类型
		/// </summary>
		[ Column(Name = "invoices_type_t", StringLength = 50)]
		public string InvoicesTypeT { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 是否一般纳税人
		/// </summary>
		[ Column(Name = "is_general_taxpayer", StringLength = 60)]
		public string IsGeneralTaxpayer { get; set; }

		[ Column(Name = "old_account_no", StringLength = 50)]
		public string OldAccountNo { get; set; }

		[ Column(Name = "old_address_cn", StringLength = 500)]
		public string OldAddressCn { get; set; }

		[ Column(Name = "old_bank_name", StringLength = 50)]
		public string OldBankName { get; set; }

		[ Column(Name = "old_identify_number", StringLength = 50)]
		public string OldIdentifyNumber { get; set; }

		[ Column(Name = "old_invoices_code", StringLength = 500)]
		public string OldInvoicesCode { get; set; }

		[ Column(Name = "old_invoices_title", StringLength = 200)]
		public string OldInvoicesTitle { get; set; }

		[ Column(Name = "old_is_enabled", StringLength = 50)]
		public string OldIsEnabled { get; set; }

		[ Column(Name = "old_is_general_taxpayer", StringLength = 60)]
		public string OldIsGeneralTaxpayer { get; set; }

		[ Column(Name = "old_tel", StringLength = 50)]
		public string OldTel { get; set; }

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 1000, IsNullable = false)]
		public string Remark { get; set; }

		[ Column(Name = "request_object_code", StringLength = 50)]
		public string? RequestObjectCode { get; set; }

		/// <summary>
		/// 请款对象名称
		/// </summary>
		[ Column(Name = "request_object_name", StringLength = 200, IsNullable = false)]
		public string RequestObjectName { get; set; }

		/// <summary>
		/// 结算币别
		/// </summary>
		[ Column(Name = "settlement_currency", StringLength = 50, IsNullable = false)]
		public string SettlementCurrency { get; set; }

		/// <summary>
		/// 联系方式
		/// </summary>
		[ Column(Name = "tel", StringLength = 50)]
		public string Tel { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 更新人ID
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// 是否境外
		/// </summary>
		[Column(Name = "is_outbound")]
		public bool IsOutbound { get; set; }
	}

}
