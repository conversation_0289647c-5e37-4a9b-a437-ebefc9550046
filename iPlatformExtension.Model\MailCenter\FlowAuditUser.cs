﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "flow_audit_user", DisableSyncStructure = true)]
	public partial class FlowAuditUser {

		/// <summary>
		/// 主键
		/// </summary>
		[Column(StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 审核人id
		/// </summary>
		[Column(Name = "auditing_id", StringLength = 1000)]
		public string AuditingId { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[Column(Name = "update_by", StringLength = 50)]
		public string UpdateBy { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

	}

}
