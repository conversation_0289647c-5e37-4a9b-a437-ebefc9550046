﻿namespace iPlatformExtension.Finance.Applications.Models.Fees;

/// <summary>
/// 缴官费状态
/// </summary>
public class OfficialPaymentStatus
{
    
    
    /// <summary>
    /// 未缴费
    /// </summary>
    public static readonly OfficialPaymentStatus NonPayment = new("0");

    /// <summary>
    /// 缴费中
    /// </summary>
    public static readonly OfficialPaymentStatus Paying = new("1000");

    /// <summary>
    /// 可缴费
    /// </summary>
    public static readonly OfficialPaymentStatus PaymentAvailable = new("2000");

    /// <summary>
    /// 不缴费
    /// </summary>
    public static readonly OfficialPaymentStatus NotPayment = new("3000");

    /// <summary>
    /// 已缴费
    /// </summary>
    public static readonly OfficialPaymentStatus Paid = new("5000");

    /// <summary>
    /// 不缴费处理中
    /// </summary>
    public static readonly OfficialPaymentStatus NotPaymentHandling = new("6000");
    
    /// <summary>
    /// 缴官费状态值
    /// </summary>
    public string Value { get; }
    
    private OfficialPaymentStatus(string value)
    {
        Value = value;
    }

    /// <summary>
    /// 获取哈希值
    /// </summary>
    /// <returns>缴费状态值的哈希值</returns>
    public override int GetHashCode()
    {
        return Value.GetHashCode();
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="other"></param>
    /// <returns></returns>
    protected bool Equals(OfficialPaymentStatus other)
    {
        return Value == other.Value;
    }

    /// <summary>
    /// 重写Equals方法
    /// </summary>
    /// <param name="obj">带比较的对象</param>
    /// <returns>对象的字符串值是否与当前的值相等</returns>
    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj)) return true;

        return obj switch
        {
            string value => value == Value,
            OfficialPaymentStatus status => Equals(status),
            _ => false
        };
    }


    /// <summary>
    /// 字符串隐式转换为缴费状态
    /// </summary>
    /// <param name="paymentStatusValue">字符串值</param>
    /// <returns><see cref="OfficialPaymentStatus"/></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public static implicit operator OfficialPaymentStatus (string? paymentStatusValue)
    {
        return paymentStatusValue switch
        {
            "0" => NonPayment,
            "1000" => Paying,
            "2000" => PaymentAvailable,
            "3000" => NotPayment,
            "5000" => Paid,
            "6000" => NotPaymentHandling,
            _ => throw new ArgumentOutOfRangeException(nameof(paymentStatusValue), paymentStatusValue, "缴费状态值不在给定枚举范围内")
        };
    }

    /// <summary>
    /// 缴费状态隐式转换为字符串值
    /// </summary>
    /// <param name="officialPaymentStatus">缴费状态</param>
    /// <returns>缴费状态对应的值</returns>
    public static implicit operator string(OfficialPaymentStatus officialPaymentStatus)
    {
        return officialPaymentStatus.Value;
    }

    /// <summary>
    /// 字符串与缴费状态比较
    /// </summary>
    /// <param name="value">字符串</param>
    /// <param name="officialPaymentStatus">缴费状态</param>
    /// <returns>字符串的值与缴费状态的值是否相等</returns>
    public static bool operator ==(string value, OfficialPaymentStatus officialPaymentStatus)
    {
        return value == officialPaymentStatus.Value;
    }

    /// <summary>
    /// 字符串与缴费状态比较
    /// </summary>
    /// <param name="value">字符串</param>
    /// <param name="officialPaymentStatus">缴费状态</param>
    /// <returns>符串的值与缴费状态的值不相等</returns>
    public static bool operator !=(string value, OfficialPaymentStatus officialPaymentStatus)
    {
        return !(value == officialPaymentStatus);
    }

    /// <summary>
    /// 重写toString。
    /// </summary>
    /// <returns>直接返回状态值</returns>
    public override string ToString()
    {
        return Value;
    }
}