﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.KafKa.Models;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.MQ.KafKa.Handlers.Consumer;

public sealed class IgnoreConsumeFailedHandler(ILoggerFactory loggerFactory) : IConsumeFailedHandler
{
    public ValueTask HandleAsync<TMessageKey, TMessage>(ConsumeFailedContext<TMessageKey, TMessage> context)
    {
        var messageResult = context.MessageResult;
        var exception = context.CurrentException;
        var logger = loggerFactory.CreateLogger(GetType());
        
        logger.LogKafkaConsumeFailed(messageResult.Topic, messageResult.Partition.Value, messageResult.Offset.Value, exception);

        context.Handled = true;
        return ValueTask.CompletedTask;
    }
}