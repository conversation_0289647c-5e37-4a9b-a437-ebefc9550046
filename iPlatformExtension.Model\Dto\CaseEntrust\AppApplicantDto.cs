﻿using FreeSql.DataAnnotations;


namespace iPlatformExtension.Model.Dto.CaseEntrust
{
    public class AppApplicantDto
    {
        /// <summary>
        /// 申请人ID
        /// </summary>
        [ Column(Name = "applicant_id", StringLength = 50)]
        public string ApplicantId { get; set; }

        /// <summary>
        /// 案件ID
        /// </summary>
        [ Column(Name = "case_id", StringLength = 50)]
        public string? CaseId { get; set; }

        /// <summary>
        /// 类别
        /// </summary>
        public string Classify { get; set; } = "A";

        /// <summary>
        /// 唯一ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

        /// <summary>
        /// 是否存在
        /// </summary>
        public bool IsRepresent { get; set; } = false;
       
    }
}
