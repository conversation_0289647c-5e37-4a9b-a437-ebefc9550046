using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{

    [ Table(Name = "product_info", DisableSyncStructure = true)]
    public partial class ProductInfo
    {

        /// <summary>
        /// 产品实例ID
        /// </summary>
        [ Column(StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string ID { get; set; }

        /// <summary>
        /// 委案ID
        /// </summary>
        [ Column(StringLength = 50)]
        public string EntrustID { get; set; }

        /// <summary>
        /// 业务系统任务ID
        /// </summary>
        [ Column(StringLength = 50)]
        public string ProcID { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [ Column(StringLength = 100)]
        public string ProductName { get; set; }

        /// <summary>
        /// 初始任务ID
        /// </summary>
        [ Column(StringLength = 100)]
        public string InitialTaskID { get; set; }


        /// <summary>
        /// 备注
        /// </summary>
        [ Column(StringLength = 255)]
        public string? Remark { get; set; }

        /// <summary>
		/// 任务状态
		/// </summary>
		
        public int ProcStatus { get; set; }

        /// <summary>
		/// 更新时间
		/// </summary>
		[ Column(InsertValueSql = "getdate()")]
        public DateTime? UpdateTime { get; set; }


        /// <summary>
        /// 定稿期限（外）
        /// </summary>
        public DateTime? CusFinishDate { get; set; }

        /// <summary>
        /// 初稿期限（外）
        /// </summary>
        public DateTime? CusFirstDate { get; set; }


        /// <summary>
        /// 定稿期限(内)
        /// </summary>
        public DateTime? IntFinishDate { get; set; }

        /// <summary>
        /// 初稿期限(内)
        /// </summary>
        public DateTime? IntFirstDate { get; set; }
        
        /// <summary>
        /// 官方期限
        /// </summary>
        public DateTime? LegalDueDate { get; set; }

        /// <summary>
        /// 官方来文日
        /// </summary>
        public DateTime? ReceiveDate { get; set; }

        /// <summary>
        /// 发文序列号
        /// </summary>
        public string? ReceiveNo { get; set; }

        /// <summary>
        /// 官文名称
        /// </summary>
        public string? NoticeName { get; set; }

        /// <summary>
        /// 任务属性
        /// </summary>
        [ Column(StringLength = 50)]
        public string? CtrlProcProperty { get; set; }

        /// <summary>
        /// 承办部门
        /// </summary>
        public string? UndertakeDeptId { get; set; }

        /// <summary>
        /// 承办人
        /// </summary>
        public string? UndertakeUserId { get; set; }

        /// <summary>
        /// 名义承办人
        /// </summary>
        public string? TitularWriteUser { get; set; }



    }

}
