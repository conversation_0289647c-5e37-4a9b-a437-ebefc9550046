using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.Send;

/// <summary>
/// 预览邮件查询
/// </summary>
/// <param name="MailId">邮件ID</param>
public record PreviewMailQuery(string MailId) : IRequest<PreviewMailResult>;

/// <summary>
/// 预览邮件结果
/// </summary>
public class PreviewMailResult
{
    /// <summary>
    /// 邮件主题
    /// </summary>
    public string Subject { get; set; } = string.Empty;

    /// <summary>
    /// 处理后的HTML正文
    /// </summary>
    public string HtmlBody { get; set; } = string.Empty;

    /// <summary>
    /// 原始HTML正文
    /// </summary>
    public string OriginalHtmlBody { get; set; } = string.Empty;

    /// <summary>
    /// 发件人
    /// </summary>
    public string MailFrom { get; set; } = string.Empty;

    /// <summary>
    /// 收件人
    /// </summary>
    public string MailTo { get; set; } = string.Empty;

    /// <summary>
    /// 抄送人
    /// </summary>
    public string MailCc { get; set; } = string.Empty;

    /// <summary>
    /// 密送人
    /// </summary>
    public string MailBcc { get; set; } = string.Empty;
}
