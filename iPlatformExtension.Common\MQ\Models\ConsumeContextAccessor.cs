﻿namespace iPlatformExtension.Common.MQ.Models;

public class ConsumeContextAccessor
{
    private static readonly AsyncLocal<ConsumeContextHolder> currentContext = new();

    public ConsumeContext? ConsumeContext
    {
        get => currentContext.Value?.Context;
        set
        {
            var holder = currentContext.Value;
            if (holder is not null)
            {
                holder.Context = null;
            }

            if (value is not null)
            {
                currentContext.Value = new ConsumeContextHolder()
                {
                    Context = value
                };
            }
        }
    }
    
    private sealed class ConsumeContextHolder
    {
        public ConsumeContext? Context { get; set; }
    }
}