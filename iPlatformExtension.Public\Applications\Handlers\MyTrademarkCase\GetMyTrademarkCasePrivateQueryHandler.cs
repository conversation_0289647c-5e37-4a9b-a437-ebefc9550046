﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;
using iPlatformExtension.Repository.Interface;
using MediatR;
using System.Collections;

namespace iPlatformExtension.Public.Applications.Handlers.MyTrademarkCase
{
    /// <summary>
    /// 获取页签
    /// </summary>
    internal sealed class GetMyTrademarkCasePrivateQueryHandler(IFreeSql freeSql, ISystemDictionaryRepository systemDictionaryRepository)
        : IRequestHandler<GetMyTrademarkCasePrivateQuery, IEnumerable<GetMyTrademarkCasePrivateDto>>
    {
        public async Task<IEnumerable<GetMyTrademarkCasePrivateDto>> Handle(GetMyTrademarkCasePrivateQuery request, CancellationToken cancellationToken)
        {
            return await systemDictionaryRepository.Where(it => it.DictionaryName == request.DictionaryName)
                            .ToListAsync(it => new GetMyTrademarkCasePrivateDto(it.DictionaryId, it.DictionaryName, it.DictionaryDesc, it.TextZhCn, it.Value), cancellationToken);
        }
    }
}

