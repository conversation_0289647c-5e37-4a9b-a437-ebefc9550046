using CommonTest.Models;
using iPlatformExtension.Model.Dto;

namespace CommonTest;

public class ReflectionTest
{
    [Fact]
    public void TestRecordNoArgumentsInstantiation()
    {
        var record = Activator.CreateInstance(typeof(TestRecord), false);
        Assert.NotNull(record);
    }

    [Fact]
    public void TestResultDataBaseType()
    {
        string[] values = ["1"];

        var value = string.Join(';', values);
        
        Assert.Equal("1", value);
    }

    [Fact]
    public void TestTupleCast()
    {
        var tes = new Dictionary<object, object>()
        {
            { 1, "1"},
            { 2, "2"}
        };

        var tesList = tes.Select(x => (x.Key, x.Value)).Cast<(int, string)>().ToList();
        Assert.Equal(2, tesList.Count);
    }
}