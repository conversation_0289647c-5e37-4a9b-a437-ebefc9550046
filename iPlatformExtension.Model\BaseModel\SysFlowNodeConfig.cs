using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_flow_node_config", DisableSyncStructure = true)]
	public partial class SysFlowNodeConfig {

		[ Column(Name = "config_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ConfigId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "dept_code", StringLength = 50)]
		public string DeptCode { get; set; }

		[ Column(Name = "dept_id", StringLength = 50)]
		public string DeptId { get; set; }

		[ Column(Name = "flow_sql", StringLength = 2000)]
		public string FlowSql { get; set; }

		[ Column(Name = "flow_type", StringLength = 50)]
		public string FlowType { get; set; }

		[ Column(Name = "is_cover")]
		public bool IsCover { get; set; } = false;

		[ Column(Name = "is_skip")]
		public bool? IsSkip { get; set; } = false;

		[ Column(Name = "node_code", StringLength = 50)]
		public string NodeCode { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "user_ids", StringLength = 2000)]
		public string UserIds { get; set; }

		[ Column(Name = "user_names", StringLength = 2000)]
		public string UserNames { get; set; }

		[ Column(Name = "user_type", StringLength = 20)]
		public string UserType { get; set; }

	}

}
