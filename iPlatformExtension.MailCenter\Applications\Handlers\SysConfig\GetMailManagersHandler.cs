﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;
using Microsoft.AspNetCore.Http;
using System.Xml.Linq;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    public class GetMailManagersHandler(IFreeSql<PlatformFreeSql> msSql) : IRequestHandler<GetMailManagersQuery, List<GetMailManagersDto>>
    {
        public async Task<List<GetMailManagersDto>> Handle(GetMailManagersQuery request, CancellationToken cancellationToken)
        {
            var users = await msSql.Select<SysUserInfo>().Where(o => o.Roles.Any(o => o.RoleCode == "MailManager")).ToListAsync(o => new GetMailManagersDto
            {
                UserId = o.UserId,
                UserName = o.UserName,
                CnName = o.CnName
            });

            return users;
        }
    }
}
