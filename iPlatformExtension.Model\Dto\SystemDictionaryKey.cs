﻿using iPlatformExtension.Model.Extensions;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 字典键
/// </summary>
/// <remarks>可以通过字典键快速寻找字典内容。而不需要每次都查库</remarks>
/// <param name="Name">名称</param>
/// <param name="Value">值</param>
/// <param name="Description">描述</param>
public record struct SystemDictionaryValueKey(string Name, string Value, string? Description = default);

/// <summary>
/// 用字典值做键的哈希比较器
/// </summary>
public class SystemDictionaryValueKeyEqualityComparer : IEqualityComparer<SystemDictionaryValueKey>
{
    /// <summary>
    /// 比较两个<see cref="SystemDictionaryValueKey"/>对象相等
    /// </summary>
    /// <param name="x">第一个对象</param>
    /// <param name="y">第二个对象</param>
    /// <returns>两个对象相等返回<c>true</c>，不等返回<c>false</c></returns>
    public bool Equals(SystemDictionaryValueKey x, SystemDictionaryValueKey y)
    {
        return x.Equals(y);
    }

    /// <summary>
    /// 获取<see cref="SystemDictionaryValueKey"/>对象的哈希值
    /// </summary>
    /// <param name="obj"><see cref="SystemDictionaryValueKey"/>对象</param>
    /// <returns>哈希值</returns>
    public int GetHashCode(SystemDictionaryValueKey obj)
    {
        return HashCode.Combine(obj.Name, obj.Value, obj.Description);
    }
}

/// <summary>
/// 字典文本键。
/// 通过字典的名称，字典的中文文本作为键
/// </summary>
/// <param name="Name">名称</param>
/// <param name="Text">文本</param>
/// <param name="Description">描述</param>
public record struct SystemDictionaryTextKey(string Name, string Text, string? Description = default);

/// <summary>
/// 用字典中文名做键的哈希比较器
/// </summary>
public class SystemDictionaryTextKeyEqualityComparer : IEqualityComparer<SystemDictionaryTextKey>
{
    /// <summary>
    /// 比较两个<see cref="SystemDictionaryTextKey"/>对象是否相等
    /// </summary>
    /// <param name="x">第一个对象</param>
    /// <param name="y">第二个对象</param>
    /// <returns>两个对象相等返回<c>true</c>，不等返回<c>false</c></returns>
    public bool Equals(SystemDictionaryTextKey x, SystemDictionaryTextKey y)
    {
        return x.Equals(y);
    }

    /// <summary>
    /// 返回<see cref="SystemDictionaryTextKey"/>对象的哈希值
    /// </summary>
    /// <param name="obj"><see cref="SystemDictionaryTextKey"/>对象</param>
    /// <returns>哈希值</returns>
    public int GetHashCode(SystemDictionaryTextKey obj)
    {
        return HashCode.Combine(obj.Name, obj.Text, obj.Description);
    }
}

