using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;
using MessagePack;

namespace iPlatformExtension.Model.BaseModel {
	
	[ Table(Name = "bas_ctrl_proc", DisableSyncStructure = true)]
	public partial class BasCtrlProc {

		/// <summary>
		/// 案件任务主键ID
		/// </summary>
		[ Column(Name = "ctrl_proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CtrlProcId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 申请类型
		/// </summary>
		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		/// <summary>
		/// 任务分类
		/// </summary>
		[ Column(Name = "attribute_id", StringLength = 50)]
		public string AttributeId { get; set; }

		/// <summary>
		/// 案件业务类型
		/// </summary>
		[ Column(Name = "business_type_id", StringLength = 50)]
		public string BusinessTypeId { get; set; }

		/// <summary>
		/// 案件类型
		/// </summary>
		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		/// <summary>
		/// 国家
		/// </summary>
		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 案件任务名称简称
		/// </summary>
		[ Column(Name = "ctrl_proc_code", StringLength = 50)]
		public string CtrlProcCode { get; set; }

		/// <summary>
		/// 案件任务英文名称
		/// </summary>
		[ Column(Name = "ctrl_proc_en_us", StringLength = 100)]
		public string CtrlProcEnUs { get; set; }

		/// <summary>
		/// 案件任务日文名称
		/// </summary>
		[ Column(Name = "ctrl_proc_ja_jp", StringLength = 50)]
		public string CtrlProcJaJp { get; set; }

		/// <summary>
		/// 案件任务中文名称
		/// </summary>
		[ Column(Name = "ctrl_proc_zh_cn", StringLength = 50)]
		public string CtrlProcZhCn { get; set; }

		/// <summary>
		/// 任务标识
		/// </summary>
		[ Column(Name = "ctrlProcMark", StringLength = 2000)]
		public string? CtrlProcMark { get; set; }

		/// <summary>
		/// 标记
		/// </summary>
		[ Column(Name = "have_mark")]
		public bool? HaveMark { get; set; }

		/// <summary>
		/// 有权
		/// </summary>
		[ Column(Name = "have_property")]
		public bool? HaveProperty { get; set; }

		// [ Column(Name = "i_ctrl_code", StringLength = 50)]
		// public string ICtrlCode { get; set; }

		/// <summary>
		/// 是否配置
		/// </summary>
		[ Column(Name = "is_config")]
		public bool? IsConfig { get; set; } = true;

		/// <summary>
		/// 是否默认
		/// </summary>
		[ Column(Name = "is_default")]
		public bool? IsDefault { get; set; } = false;

		/// <summary>
		/// 是否配案
		/// </summary>
		[ Column(Name = "is_distribution")]
		public bool? IsDistribution { get; set; } = false;

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		/// <summary>
		/// 是否面审
		/// </summary>
		[ Column(Name = "is_free_trial")]
		public bool? IsFreeTrial { get; set; } = false;

		/// <summary>
		/// 是否官方的
		/// </summary>
		[ Column(Name = "is_official")]
		public bool? IsOfficial { get; set; } = false;

		/// <summary>
		/// PCT国际阶段
		/// </summary>
		[ Column(Name = "is_pct_stage")]
		public bool? IsPctStage { get; set; } = false;

		/// <summary>
		/// 开案显示
		/// </summary>
		[ Column(Name = "is_show_apply")]
		public bool? IsShowApply { get; set; }

		[ Column(Name = "is_ud")]
		public bool? IsUd { get; set; } = true;

		/// <summary>
		/// 是否到期提醒
		/// </summary>
		[ Column(Name = "is_deadline_reminder")]
		public bool? IsDeadlineReminder { get; set; }

		/// <summary>
		/// 需要准备的文件
		/// </summary>
		[ Column(Name = "necessaryfiles", StringLength = 2000)]
		public string Necessaryfiles { get; set; }

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		/// <summary>
		/// 专用表单
		/// </summary>
		[ Column(Name = "special_forms", StringLength = 50)]
		public string SpecialForms { get; set; }

		/// <summary>
		/// 任务阶段
		/// </summary>
		[ Column(Name = "stage_code", StringLength = 50)]
		public string StageCode { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }


        /// <summary>
        /// 页签
        /// </summary>
        [Column(Name = "trademark_tab", StringLength = 50)]
        public string TrademarkTab { get; set; }

        /// <summary>
        /// 是否委外
        /// </summary>
        [Column(Name = "is_outsourcing")]
        public bool IsOutsourcing { get; set; }
    }

}
