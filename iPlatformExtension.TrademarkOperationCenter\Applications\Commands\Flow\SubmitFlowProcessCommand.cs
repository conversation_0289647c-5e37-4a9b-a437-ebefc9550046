﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;
using FlowInfo = iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow.FlowInfo;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow
{
    public record SubmitFlowProcessCommand(FlowInfo FlowInfo, UserBaseInfo CurrentUser) 
        : IRequest,IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;
}
