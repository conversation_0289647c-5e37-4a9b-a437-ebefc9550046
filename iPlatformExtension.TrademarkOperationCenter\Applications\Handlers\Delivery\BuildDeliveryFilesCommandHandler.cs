﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class BuildDeliveryFilesCommandHandler(
    IDeliveryFilesRepository deliveryFilesRepository,
    HuaweiObsClient huaweiObsClient,
    IUserInfoRepository infoRepository)
    : IRequestHandler<BuildDeliveryFilesCommand>
{
    public async Task Handle(BuildDeliveryFilesCommand request, CancellationToken cancellationToken)
    {
        ICacheableRepository<string, UserBaseInfo> userInfoRepository = infoRepository;
        IEnumerable<DeliFiles> deliveryFiles = await deliveryFilesRepository.Orm.Select<CaseFile>()
            .From<BasFileDesc, BasFileType>()
            .LeftJoin((file, description, type) => file.DescId == description.FileDescId)
            .LeftJoin((file, description, type) => description.FileTypeId == type.FileTypeId)
            .Where((file, description, type) => request.CaseFileIds.Contains(file.FileId))
            .ToListAsync((file, description, type) => new DeliFiles
            {
                FileDesc = description.FileDescZhCn,
                FileEx = file.FileEx,
                FileName = file.FileName,
                FileNo = file.FileNo,
                ProcId = request.ProcId,
                BaseFileType = type.FileTypeZhCn,
                IsIdentity = false,
                UploadTime = DateTime.Now,
                Uploader = file.CreateUserId,
                CaseFileId = file.FileId,
                FileCode = description.TextCode
            }, cancellationToken);

        var fileIds = deliveryFiles.Select(deliveryFile =>
        {
            deliveryFile.Id = int.Parse(deliveryFile.FileNo[4..]);
            return deliveryFile.Id;
        }).ToArray();
        var fileList = await deliveryFilesRepository.Orm.Select<FileListA>().Where(a => fileIds.Contains(a.Id))
            .ToListAsync(a => new FileListA
            {
                Id = a.Id,
                FileName = a.FileName,
                RealName = a.RealName,
                ServerPath = a.ServerPath,
                Bucket = a.Bucket,
                InputTime = a.InputTime
            }, cancellationToken);

        deliveryFiles = await deliveryFiles.Join(fileList, deliveryFile => deliveryFile.Id,
            fileA => fileA.Id,
            (deliveryFile, fileA) =>
            {
                deliveryFile.FileId = Guid.NewGuid().ToString().ToUpper();
                deliveryFile.UploadTime = fileA.InputTime;
                deliveryFile.Url = huaweiObsClient
                    .GenerateTemporaryUrl(fileA.GetObjectName(), fileA.Bucket, TimeSpan.FromDays(7 * 365))
                    .SignUrl;
                return deliveryFile;
            }).ToAsyncEnumerable().SelectAwait(async file =>
            {
                file.Uploader = (await userInfoRepository.GetCacheValueAsync(file.Uploader))?.CnName ?? string.Empty;
                return file;
            }).ToListAsync(cancellationToken);

        await deliveryFilesRepository.InsertAsync(deliveryFiles, cancellationToken);
    }
}