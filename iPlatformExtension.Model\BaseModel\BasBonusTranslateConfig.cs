using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_bonus_translate_config", DisableSyncStructure = true)]
	public partial class BasBonusTranslateConfig {

		/// <summary>
		/// 翻译类型主键ID
		/// </summary>
		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 点数总量
		/// </summary>
		[ Column(Name = "bonus_amount", DbType = "money")]
		public decimal? BonusAmount { get; set; }

		/// <summary>
		/// 校对点数
		/// </summary>
		[ Column(Name = "bonus_check_point", DbType = "money")]
		public decimal? BonusCheckPoint { get; set; }

		/// <summary>
		/// 定稿点数
		/// </summary>
		[ Column(Name = "bonus_finish_point", DbType = "money")]
		public decimal? BonusFinishPoint { get; set; }

		/// <summary>
		/// 固定点数
		/// </summary>
		[ Column(Name = "bonus_fixed_point", DbType = "money")]
		public decimal? BonusFixedPoint { get; set; }

		/// <summary>
		/// 奖金点数
		/// </summary>
		[ Column(Name = "bonus_point", DbType = "money")]
		public decimal? BonusPoint { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		/// <summary>
		/// 等级
		/// </summary>
		[ Column(Name = "rank_id", StringLength = 50, IsNullable = false)]
		public string RankId { get; set; }

		/// <summary>
		/// 翻译类型
		/// </summary>
		[ Column(Name = "translate_type", StringLength = 50, IsNullable = false)]
		public string TranslateType { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
