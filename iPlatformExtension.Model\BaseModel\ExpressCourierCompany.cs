using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "express_courier_company", DisableSyncStructure = true)]
	public partial class ExpressCourierCompany {

		[ Column(Name = "courier_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CourierId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "courier_code", StringLength = 50)]
		public string CourierCode { get; set; }

		[ Column(Name = "courier_name", StringLength = 50)]
		public string CourierName { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_appointment")]
		public bool? IsAppointment { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "is_online_order")]
		public bool? IsOnlineOrder { get; set; }

		[ Column(Name = "is_partner")]
		public bool? IsPartner { get; set; }

		[ Column(Name = "is_track")]
		public bool? IsTrack { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
