﻿using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal sealed class BaseBankForeignFeeResultHandler(IBaseBankRepository baseBankRepository) : ForeignFeeResultHandlerBase
{
    protected override async ValueTask GetResultAsync(ForeignBillFeeExportDto dto)
    {
        dto.PaidBank = await baseBankRepository.GetTextValueAsync(dto.PaidBank) ?? string.Empty;
    }
}