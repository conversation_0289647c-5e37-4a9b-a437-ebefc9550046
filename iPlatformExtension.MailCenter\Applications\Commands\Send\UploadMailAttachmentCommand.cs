using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.MailCenter.Applications.Commands.Send
{
    /// <summary>
    /// 上传邮件附件命令
    /// </summary>
    /// <param name="MailId">邮件ID，可选，如果为空则表示临时附件</param>
    /// <param name="File">上传的文件</param>
    public record UploadMailAttachmentCommand(
        string? MailId,
        [Required] IFormFile File) : IRequest<UploadMailAttachmentResult>, IUnitOfWorkCommandMysql;

    /// <summary>
    /// 上传邮件附件结果
    /// </summary>
    public class UploadMailAttachmentResult
    {
        /// <summary>
        /// 附件ID
        /// </summary>
        public string AttachmentId { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public double FileSize { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 文件URL
        /// </summary>
        public string FileUrl { get; set; }
    }
}
