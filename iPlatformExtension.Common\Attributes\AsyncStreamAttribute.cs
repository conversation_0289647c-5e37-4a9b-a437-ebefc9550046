﻿using Microsoft.AspNetCore.Mvc.Filters;

namespace iPlatformExtension.Common.Attributes;
/// <summary>
/// 用于标记类或方法，以指示其支持异步流操作的属性。
/// </summary>
/// <param name="stopOnException">指示在遇到异常时是否停止处理。默认为false。</param>
/// <param name="delay">在每次处理前引入的延迟，以毫秒为单位。默认为0。</param>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class AsyncStreamAttribute(bool stopOnException = false, int delay = 0) : Attribute, IFilterMetadata
{
    /// <summary>
    /// 获取或设置一个值，指示在遇到异常时是否停止处理。
    /// </summary>
    public bool StopOnException { get; set; } = stopOnException;
    
    /// <summary>
    /// 获取或设置在每次处理前引入的延迟，以毫秒为单位。
    /// </summary>
    public int Delay { get; set; } = delay;
}
