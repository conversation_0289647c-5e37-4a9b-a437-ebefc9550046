using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;
/// <summary>
/// 委案结果
/// </summary>
public class CaseEntrustResultDto
{
    /// <summary>
    /// 委案ID
    /// </summary>
    [JsonIgnore]
    public string ID { get; set; }

    /// <summary>
    /// 订单id
    /// </summary>
    [JsonIgnore]
    public string OrderId { get; set; }
    
    /// <summary>
    /// 订单编号
    /// </summary>
    public string OrderNo { get; set; } = null!;

    /// <summary>
    /// 订单案件号
    /// </summary>
    [JsonPropertyName("caseId")]
    public string? OrderCaseID { get; set; }

    /// <summary>
    /// 我方文号
    /// </summary>
    [JsonPropertyName("caseCode")]
    public string? Volume { get; set; }

    /// <summary>
    /// 委案状态
    /// </summary>
    [JsonPropertyName("result")]
    public int Status { get; set; }

    /// <summary>
    /// 退回原因
    /// </summary>
    [JsonPropertyName("reason")]
    public string? TurnBackReason { get; set; }

    /// <summary>
    /// 操作员
    /// </summary>
    [JsonPropertyName("operator")]
    public string? Operator { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    [JsonPropertyName("operateTime")]
    public DateTime OperationTime { get; set; }

    /// <summary>
    /// 开案案件号
    /// </summary>
    [JsonIgnore]
    public string? ApplyCaseID { get; set; }

    /// <summary>
    /// 开案ID
    /// </summary>
    [JsonIgnore]
    public string? ApplyID { get; set; }

    /// <summary>
    /// 待补充资料
    /// </summary>
    [JsonPropertyName("orderAdditionalResourcesList")]
    public List<AppFurtherInfoDto> FurtherInfoDtos { get; set; } = new();

    /// <summary>
    /// 新案标识
    /// </summary>
    public bool IsNewCase { get; set; }

    /// <summary>
    /// 旧案标识
    /// </summary>
    public bool IsOldCase => !IsNewCase;

    /// <summary>
    /// 委案信息的我方文号
    /// </summary>
    [JsonIgnore]
    public string? EntrustVolume { get; set; }
}