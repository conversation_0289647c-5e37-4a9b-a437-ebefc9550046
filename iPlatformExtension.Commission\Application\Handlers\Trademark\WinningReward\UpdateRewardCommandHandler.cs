﻿using AutoMapper;
using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class UpdateRewardCommandHandler(
    IMapper mapper,
    IHttpContextAccessor httpContextAccessor,
    IWinningRewardProcRepository rewardProcRepository) 
    : IRequestHandler<UpdateRewardCommand>
{
    public async Task Handle(UpdateRewardCommand request, CancellationToken cancellationToken)
    {
        var (procId, document) = request;
        var rewardProc = await rewardProcRepository.Where(proc => proc.ProcId == procId).ToOneAsync(cancellationToken);

        if (rewardProc is null)
        {
            throw new NotFoundException(procId, "胜诉奖励数据");
        }

        var dto = mapper.Map<RewardPatchDto>(rewardProc);
        document.ApplyTo(dto);
        mapper.Map(dto, rewardProc);

        rewardProc.Updater = (httpContextAccessor.HttpContext?.User).GetUserId();
        rewardProc.UpdateTime = DateTime.Now;
        
        await rewardProcRepository.UpdateAsync(rewardProc, cancellationToken);
    }
}