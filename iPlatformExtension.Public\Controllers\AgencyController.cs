﻿using iPlatformExtension.Public.Applications.Models.Agency;
using iPlatformExtension.Public.Applications.Queries.Agency;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 代理机构控制器
/// </summary>
/// <param name="mediator">中介者</param>
[ApiController]
[Route("[controller]")]
public sealed class AgencyController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// 查询有关商标递交的代理机构信息
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="agencyId">代理机构id</param>
    /// <returns>商标递交的代理机构信息</returns>
    [HttpGet("trademark-delivery")]
    [ResponseCache(Duration = 3600, Location = ResponseCacheLocation.Any, VaryByQueryKeys = ["keyword", "agencyId"])]
    public Task<IEnumerable<AgencyTrademarkDeliveryInfoDto>>
        GetTrademarkDeliveryInfosAsync([FromQuery] string? keyword, [FromQuery] string? agencyId) =>
        mediator.Send(new AgencyTrademarkDeliveryQuery(keyword, agencyId, true));
}