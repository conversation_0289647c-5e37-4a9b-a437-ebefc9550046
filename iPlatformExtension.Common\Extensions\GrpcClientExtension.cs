using System.Net.Security;
using Grpc.Net.ClientFactory;
using iPlatformExtension.Common.ServiceDiscovery.Nacos;
using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Extensions;

public static class GrpcClientExtension
{
    public static IHttpClientBuilder AddGrpcClientFromNacos<TClient>(
        this IServiceCollection services, string serviceName, string groupName = "DEFAULT_GROUP") where TClient : class
    {
        return services.AddGrpcClientFromNacos<TClient>(
            new NacosServiceDiscoveryEndpointOptions<GrpcClientFactoryOptions>()
            {
                ServiceName = serviceName,
                GroupName = groupName,
                UriScheme = "https"
            });
    }
    
    public static IHttpClientBuilder AddGrpcClientFromNacos<TClient>(
        this IServiceCollection services, NacosServiceDiscoveryEndpointOptions<GrpcClientFactoryOptions> discoveryOptions) where TClient : class
    {
        var (serviceName, groupName, uriScheme) = discoveryOptions;
        
        return services.AddGrpcClient<TClient>((provider, options) =>
        {
            var hostUri = new NacosClusterId(serviceName, groupName).GetServiceEndpointUri(uriScheme);
            options.Address = hostUri;

            discoveryOptions.ConfigureOptions?.Invoke(provider, options);
        }).AddServiceDiscovery().ConfigurePrimaryHttpMessageHandler(() => new SocketsHttpHandler()
        {
            SslOptions = new SslClientAuthenticationOptions()
            {
                RemoteCertificateValidationCallback = delegate { return true; },
            }
        });
    }
}