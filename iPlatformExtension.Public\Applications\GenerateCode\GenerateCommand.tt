﻿<#@ template debug="false" hostspecific="True" language="C#" #>
<#@ assembly name="System.Core" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Text" #>
<#@ import namespace="System.IO" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ output extension=".cs" #>
<#@ include file="ModelAuto.ttinclude"#>


<# var manager = new Manager(Host, GenerationEnvironment, true); #>
<#
	//生成Command(inputModel)  Handle(处理函数)
	var FileName = "MessageSend"; //文件名字
	var FileFolderName = "SignLR"; //文件夹名字
	string templateDirectory = Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..");
	string outputFilePath = Path.Combine(templateDirectory, "Commands",FileFolderName);
	if (!Directory.Exists(outputFilePath))
	{
	    Directory.CreateDirectory(outputFilePath);
	}
	manager.StartBlock(FileName+"Command.cs",outputFilePath);
#>
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.<#=FileFolderName#>;

public record <#=FileName#>Command() : IRequest;

<#  manager.EndBlock();
string HandlerPath = Path.Combine(templateDirectory, "Handlers",FileFolderName);
if (!Directory.Exists(HandlerPath))
{
	Directory.CreateDirectory(HandlerPath);
}
manager.StartBlock(FileName+"CommandHandler.cs",HandlerPath);
#>
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.<#=FileFolderName#>;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.<#=FileFolderName#>
{
    /// <summary>
    /// 
    /// </summary>
    internal sealed class <#=FileName#>CommandHandler : IRequestHandler<<#=FileName#>Command>
    {
        private readonly IFreeSql _freeSql;

        public <#=FileName#>CommandHandler(IFreeSql freeSql)
        {
            _freeSql = freeSql;
        }

        public async Task Handle(<#=FileName#>Command request, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }
    }
}

<# manager.EndBlock();#>
<# manager.Process(true); //输出文件   #>