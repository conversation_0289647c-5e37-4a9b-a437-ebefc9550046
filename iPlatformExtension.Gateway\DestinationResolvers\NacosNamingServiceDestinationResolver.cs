using Nacos.V2;
using Yarp.ReverseProxy.Configuration;
using Yarp.ReverseProxy.ServiceDiscovery;

namespace iPlatformExtension.Gateway.DestinationResolvers;

internal sealed class NacosNamingServiceDestinationResolver(
    INacosNamingService nacosNamingService) : IDestinationResolver
{
    
    /// <inheritdoc />
    public ValueTask<ResolvedDestinationCollection> ResolveDestinationsAsync(IReadOnlyDictionary<string, DestinationConfig> destinations, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }
}