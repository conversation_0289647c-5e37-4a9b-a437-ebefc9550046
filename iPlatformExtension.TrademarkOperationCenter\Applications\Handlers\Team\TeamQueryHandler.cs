﻿﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;
using System.Drawing;
using System.Linq.Expressions;
using System.Text;
using iPlatformExtension.Common.Cache;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 团队列表查询处理者
    /// </summary>
    public class TeamQueryHandler(IFreeSql freeSql, IUserInfoRepository userInfoRepository) : IRequestHandler<TeamQuery, IEnumerable<TeamDto>>
    {
        public async Task<IEnumerable<TeamDto>> Handle(TeamQuery request, CancellationToken cancellationToken)
        {
            var dbSelect = freeSql.Select<SysTeam, SysTeamAssociateMember>()
                .LeftJoin(it => it.t1.TeamId == it.t2.TeamId)
                .WhereIf(request.Name is not null, (it, m) => it.TeamName.Contains(request.Name) || m.UserName.Contains(request.Name))
                .WhereIf(request.IsEffect is not null, (it, m) => it.IsEffect == request.IsEffect)
                .WhereIf(request.IsExclusive is not null, (it, m) => it.IsExclusive == request.IsExclusive)
                .Where((it, m) => it.Enable == SysEnum.SystemTeamStatus.Normal.GetHashCode()).Distinct();

            if (request.PageIndex is not null && request.PageSize is not null)
            {
                dbSelect = dbSelect.Page(request.PageIndex.Value, request.PageSize.Value).Count(out var totalCount).OrderBy(it => it.t1.TeamId);
                var result = await dbSelect.WithLock().ToListAsync((it, m) => new TeamDto(it.TeamId, it.TeamName, it.IsExclusive, it.IsEffect, it.TeamDescription, it.CreateTime, it.Seq, it.AuthorizeUser), cancellationToken);

                // 处理授权用户信息
                await ProcessAuthorizeUserInfo(result, cancellationToken);

                return new PageResult<TeamDto>()
                {
                    Data = result,
                    Page = request.PageIndex.Value,
                    PageSize = request.PageSize.Value,
                    Total = totalCount
                };
            }

            var teams = await dbSelect.WithLock().ToListAsync((it, m) =>
                  new TeamDto(it.TeamId, it.TeamName, it.IsExclusive, it.IsEffect, it.TeamDescription, it.CreateTime, it.Seq, it.AuthorizeUser), cancellationToken);

            // 处理授权用户信息
            await ProcessAuthorizeUserInfo(teams, cancellationToken);

            return teams;
        }

        /// <summary>
        /// 处理授权用户信息
        /// </summary>
        private async Task ProcessAuthorizeUserInfo(IEnumerable<TeamDto> teams, CancellationToken cancellationToken)
        {
            foreach (var team in teams)
            {
                if (string.IsNullOrEmpty(team.AuthorizeUser))
                    continue;

                var userIds = team.AuthorizeUser.Split(';', StringSplitOptions.RemoveEmptyEntries);
                if (userIds.Length == 0)
                    continue;

                var userNamesSb = new StringBuilder();

                foreach (var userId in userIds)
                {
                    var userInfo = await ((ICacheableRepository<string, SysUserInfo>)userInfoRepository)
                        .GetCacheValueAsync(userId,  cancellationToken: cancellationToken);
                    if (userInfo != null)
                    {
                        // 添加用户中文名
                        userNamesSb.Append(userInfo.CnName).Append(';');
                    }
                }

                // 移除最后一个分号
                if (userNamesSb.Length > 0)
                    userNamesSb.Length--;

                team.AuthorizeUserNames = userNamesSb.ToString();
            }
        }
    }
}
