﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 国家信息
/// </summary>
/// <param name="Id">国家简码</param>
/// <param name="CnName">中文名称</param>
/// <param name="EnName">英文名称</param>
public sealed record CountryInfoDto(string Id, string CnName, string EnName) : INameInfo
{
    /// <summary>
    /// 国家区号
    /// </summary>
    public string? CountryCallingCode { get; set; }

    /// <summary>
    /// 大洲名称
    /// </summary>
    public string? ContinentCn { get; set; }
}