using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.NiceCategory;
using iPlatformExtension.Public.Applications.Queries.NiceCategory;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 尼斯分类控制器
/// </summary>
[ApiController]
[Route("[controller]")]
public class NiceCategoryController : ControllerBase
{
    private readonly IMediator _mediator;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="mediator">中介者</param>
    public NiceCategoryController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// 分页查询尼斯分类
    /// </summary>
    /// <param name="keywords">关键字</param>
    /// <param name="levels">尼斯分类的层级</param>
    /// <param name="grandNumbers">大类尼斯分类编号</param>
    /// <param name="parentNumbers">父级尼斯分类编号</param>
    /// <param name="versionId">版本号</param>
    /// <param name="isPrecision">是否精准匹配关键字</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">页面大小</param>
    /// <returns>尼斯分类分页数据</returns>
    [HttpGet]
    [Produces(MediaTypeNames.Application.Json)]
    public Task<IEnumerable<NiceCategoryItemDto>> GetAsync(string? keywords, [Required]string levels, string? grandNumbers,
        string? parentNumbers, [Required]string versionId, bool isPrecision, [Required]int page, [Required]int pageSize)
    {
        var query = new NicePaginationQuery(
            GrandNumbers: string.IsNullOrWhiteSpace(grandNumbers)
                ? Array.Empty<string>()
                : grandNumbers.Split(',', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries),
            ParentNumbers: string.IsNullOrWhiteSpace(parentNumbers)
                ? Array.Empty<string>()
                : parentNumbers.Split(',', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries),
            Levels: levels.Split(',', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries)
                .Select(level => Convert.ToInt32(level)),
            Keywords: string.IsNullOrWhiteSpace(keywords)
                ? Array.Empty<string>()
                : keywords.Split(',', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries),
            VersionId: versionId,
            IsPrecision: isPrecision,
            Page: page,
            PageSize: pageSize);

        return _mediator.Send(query);
    }
}