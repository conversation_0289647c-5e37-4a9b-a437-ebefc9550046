﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailService.Applications.Queries;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailService.Applications.Handlers
{
    /// <summary>
    /// 生成邮件测试数据的处理程序
    /// </summary>
    /// <param name="freeSql"></param>
    public class GenerateMailTestDataHandler(
        IFreeSql<MailCenterFreeSql> freeSql
        ) : IRequestHandler<GenerateMailTestDataQuery, bool>
    {
        public async Task<bool> Handle(GenerateMailTestDataQuery request, CancellationToken cancellationToken)
        {
            var mailSend = await freeSql.Select<MailSend>().Where(o => o.MailId == request.MailId).FirstAsync();
            var mailSendList = await freeSql.Select<MailSendList>().Where(o => o.MailId == request.MailId).FirstAsync();
            var mailTos = await freeSql.Select<MailUser>().Where(o => o.MailId == request.MailId).ToListAsync();

            var mailAttactments = await freeSql.Select<MailAttachments>().Where(o => o.MailId == request.MailId).ToListAsync();

            if (mailSend == null || mailSendList == null)
            {
                throw new ArgumentNullException("复制对象数据不完整,无法复制");
            }
            var dateTime = DateTime.Now.ToString("MM-dd HH:mm:ss");
            for (int i = 0; i < request.Count; i++)
            {
                var mailId = Guid.NewGuid().ToString();
                mailSend.MailId = mailId;
                mailSend.MailSubject = $"[{i}]{request.MailSubject}{dateTime}";
                mailSend.Status = SendStatusType.PendingSend.GetHashCode();

                mailSendList.MailId = mailId;
                mailSendList.Id = Guid.NewGuid().ToString();
                mailSendList.Status = SendStatusType.PendingSend.GetHashCode();

                mailTos.ForEach(o =>
                {
                    o.Id = Guid.NewGuid().ToString();
                    o.MailId = mailId;
                });

                mailAttactments.ForEach(o =>
                {
                    o.MailId = mailId;
                    o.AttachmentId = Guid.NewGuid().ToString();
                });

                await freeSql.Insert(mailSend).ExecuteAffrowsAsync();
                await freeSql.Insert(mailSendList).ExecuteAffrowsAsync();
                await freeSql.Insert(mailTos).ExecuteAffrowsAsync();
                await freeSql.Insert(mailAttactments).ExecuteAffrowsAsync();
            }

            return true;
        }
    }
}
