﻿using AutoMapper;
using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Commission.Application.Queries.WinningReward;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class WinningRewardRuleQueryHandler(IFreeSql freeSql, IMapper mapper) : IRequestHandler<WinningRewardRuleQuery, RuleDto>
{
    public async Task<RuleDto> Handle(WinningRewardRuleQuery request, CancellationToken cancellationToken)
    {
        var ruleId = request.RuleId;
        var rule = await freeSql.Select<WinningRewardRule>(ruleId).WithLock().ToOneAsync(cancellationToken);

        if (rule is null)
        {
            throw new NotFoundException(ruleId, "胜诉奖励规则");
        }

        var dto = mapper.Map<RuleDto>(rule);
        return dto;
    }
}