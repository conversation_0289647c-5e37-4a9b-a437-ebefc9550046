﻿using iPlatformExtension.Finance.Applications.Handlers.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal sealed class BaseCtrlProcForeignFeeResultHandler(IBaseCtrlProcRepository baseCtrlProcRepository) 
    : ForeignFeeResultHandlerBase
{
    protected override async ValueTask GetResultAsync(ForeignBillFeeExportDto dto)
    {
        dto.ProcName = await baseCtrlProcRepository.GetTextValueAsync(dto.ProcName ?? string.Empty) ?? string.Empty;
    }
}