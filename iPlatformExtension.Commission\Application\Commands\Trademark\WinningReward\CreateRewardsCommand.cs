﻿using iPlatformExtension.Commission.Infrastructure.Models;
using MediatR;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;

/// <summary>
/// 创建胜诉奖励命令
/// </summary>
public record CreateRewardsCommand() : IBackgroundTracingCommand, IRequest
{
    /// <inheritdoc />
    public string TraceParentId { get; set; } = null!;

    /// <inheritdoc />
    public string OperationName { get; set; } = "国内商标胜诉奖励固化";
}