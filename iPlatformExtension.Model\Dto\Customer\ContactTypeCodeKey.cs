﻿namespace iPlatformExtension.Model.Dto.Customer;

/// <summary>
/// 联系人编码键
/// </summary>
/// <param name="ContactCode"></param>
public record struct ContactTypeCodeKey(string ContactCode)
{
    /// <summary>
    /// 隐式转换为字符串
    /// </summary>
    /// <param name="key">联系人编码键</param>
    /// <returns>编码的字符串值</returns>
    public static implicit operator string(ContactTypeCodeKey key) => key.ContactCode;
}