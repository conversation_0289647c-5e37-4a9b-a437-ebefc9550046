﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;

/// <summary>
/// 子项尼斯分类
/// </summary>
/// <remarks>没有子项</remarks>
/// <param name="CategoryId">分类id</param>
/// <param name="CategoryNumber">分类编号</param>
/// <param name="CategoryName">分类名称</param>
/// <param name="Selected">是否选中</param>
[JsonDerivedType(typeof(NiceCategoryParent))]
public record NiceCategoryChild(string CategoryId, string CategoryNumber, string CategoryName, bool Selected);