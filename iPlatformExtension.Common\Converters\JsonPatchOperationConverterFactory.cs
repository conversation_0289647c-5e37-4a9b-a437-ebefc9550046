﻿using System.Collections.Concurrent;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.JsonPatch.Operations;

namespace iPlatformExtension.Common.Converters;

public sealed class JsonPatchOperationConverterFactory : JsonConverterFactory
{
    private readonly ConcurrentDictionary<Type, Lazy<JsonConverter?>> _jsonConverters = new();
    
    public override bool CanConvert(Type typeToConvert)
    {
        return typeToConvert.IsGenericType && typeToConvert.GetGenericTypeDefinition() == typeof(Operation<>);
    }

    public override JsonConverter? CreateConverter(Type typeToConvert, JsonSerializerOptions options)
    {
        var converter = options.Converters.FirstOrDefault(jsonConverter =>
            jsonConverter.CanConvert(typeToConvert) && jsonConverter != this);

        return converter ?? _jsonConverters.GetOrAdd(typeToConvert, static type => new Lazy<JsonConverter?>(() =>
        {
            var converterType = typeof(JsonPatchOperationConverter<>).MakeGenericType(type.GetGenericArguments()[0]);
            return Activator.CreateInstance(converterType) as JsonConverter;
        })).Value;
    }
}