﻿using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Public.Applications.Models.Flow;
using MediatR;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Public.Applications.Commands.Statistics;

/// <summary>
/// 发送邮件命令
/// </summary>
public record UrgentDraftMailCommand([Required(ErrorMessage = "任务id是必须的")] List<string> ProcId, int DateType, List<GetUrgentDraftUserListDto> UrgentUserList,string? Warning) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

