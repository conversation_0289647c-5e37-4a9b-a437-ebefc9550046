using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "customer_grant_user", DisableSyncStructure = true)]
	public partial class CustomerGrantUser {

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "grant_id", StringLength = 50)]
		public string GrantId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 授权类型  0表示创建人 1表示案源人 2表示跟案人 3表示授权人
		/// </summary>
		[ Column(Name = "grant_type", StringLength = 50)]
		public string GrantType { get; set; }

		[ Column(Name = "is_add")]
		public bool IsAdd { get; set; } = false;

		[ Column(Name = "is_busi")]
		public bool IsBusi { get; set; } = false;

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
