﻿namespace iPlatformExtension.Model.Dto.Flow;

/// <summary>
/// 流程事件消息
/// </summary>
public class FlowEventMessage
{
    /// <summary>
    /// 流程上下文id
    /// </summary>
    public required string ActivityId { get; set; }

    /// <summary>
    /// 节点id
    /// </summary>
    public required string NodeId { get; set; }

    /// <summary>
    /// 上一个节点id
    /// </summary>
    public required string PreviousNodeId { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public int Version { get; set; }

    /// <summary>
    /// 提交类型
    /// </summary>
    public required string SubmitType { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreationTime { get; set; }
}