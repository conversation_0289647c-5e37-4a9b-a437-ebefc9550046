﻿using iPlatformExtension.Public.Applications.Models.District;
using iPlatformExtension.Public.Applications.Queries.District;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 地区控制器
/// </summary>
[ApiController]
[Route("[controller]")]
public sealed class DistrictController(ISender sender) : ControllerBase
{
    /// <summary>
    /// 地区信息查询
    /// </summary>
    /// <param name="keyword">地区名称的关键字</param>
    /// <param name="isEnabled">是否有效</param>
    /// <returns>地区信息</returns>
    [HttpGet]
    public async Task<IEnumerable<DistrictInfo>> GetAsync(string? keyword, bool isEnabled = true)
    {
        var query = new DistrictQuery(keyword, isEnabled);
        return await sender.Send(query, HttpContext.RequestAborted);
    }
}