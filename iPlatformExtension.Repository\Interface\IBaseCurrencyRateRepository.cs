﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Repository.Interface;

public interface IBaseCurrencyRateRepository 
    : IBaseRepository<BasCurrencyRate, string>, 
        IScopeDependency, 
        IRedisCacheableRepository<CurrencyRateKey, BasCurrencyRate>
{
    Task<BasCurrencyRate?> ICacheableRepository<CurrencyRateKey, BasCurrencyRate>.GetValueFromDbAsync(CurrencyRateKey key, CancellationToken cancellationToken)
    {
        return Select.WithLock().Where(rate => rate.CurrencyId == key.Currency && rate.BaseCurrencyId == key.TargetCurrency)
            .FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasCurrencyRate>> ICacheableRepository<CurrencyRateKey, BasCurrencyRate>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().ToListAsync(cancellationToken);
    }

    CurrencyRateKey ICacheableRepository<CurrencyRateKey, BasCurrencyRate>.GenerateKey(BasCurrencyRate value)
    {
        return new CurrencyRateKey(value.CurrencyId, value.BaseCurrencyId);
    }
}