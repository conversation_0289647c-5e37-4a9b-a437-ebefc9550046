using iPlatformExtension.Common.Mediator.Commands;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.MailCenter.Applications.Commands.Send;

/// <summary>
/// 更新定时发送时间命令
/// </summary>
/// <param name="MailId">邮件ID</param>
/// <param name="SendTime">新的发送时间</param>
public record UpdateSendTimeCommand(
    [Required(ErrorMessage = "邮件ID不能为空")] string MailId,
    [Required(ErrorMessage = "发送时间不能为空")] DateTime SendTime) : IRequest, IUnitOfWorkCommandMysql;
