﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow
{
    [Serializable]
    public class FlowHistoryQuery : IRequest<GetFlowHistoryDto>
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string ObjId { get; set; }

        /// <summary>
        /// 流程类型(递交流程此参数传:DE)
        /// </summary>
        public string FlowType { get; set; }

        /// <summary>
        /// 案件类型
        /// </summary>
        public string FlowSubType { get; set; }
    }
}
