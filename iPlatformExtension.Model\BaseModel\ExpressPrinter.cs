using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "express_printer", DisableSyncStructure = true)]
	public partial class ExpressPrinter {

		[ Column(Name = "id", IsIdentity = true)]
		public int Id { get; set; }

		[ Column(Name = "ip_address", StringLength = 50)]
		public string IpAddress { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "port", StringLength = 10)]
		public string Port { get; set; }

		[ Column(Name = "printer_name", StringLength = 50)]
		public string PrinterName { get; set; }

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
