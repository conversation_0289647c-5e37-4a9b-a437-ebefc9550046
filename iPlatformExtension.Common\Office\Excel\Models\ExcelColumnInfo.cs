﻿using System.Text.RegularExpressions;
using iPlatformExtension.Common.TypeInfo;

namespace iPlatformExtension.Common.Office.Excel.Models;

public partial class ExcelColumnInfo : IComparable<ExcelColumnInfo>
{
    public static readonly IComparer<ExcelColumnInfo> ColumnIndexComparer = new ExcelColumnIndexComparer();
    
    public object? Key { get; set; }
    
    public int? ExcelColumnIndex { get; set; }

    public string ExcelColumnName { get; set; } = string.Empty;
    
    public string[]? ExcelColumnAliases { get; set; }

    public Type ExcludeNullableType { get; set; } = null!;
    
    public bool Nullable { get; internal set; }
    
    public string? ExcelFormat { get; internal set; }
    
    public double? ExcelColumnWidth { get; internal set; }

    internal EntityPropertyInfo PropertyInfo { get; set; } = null!;

    public string? ExcelIndexName
    {
        get;
        set
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                field = null;
                return;
            }

            var excelIndexName = value.Trim();
            var matchResult = ExcelColumnIndexNameRegex().Match(excelIndexName);
            if (matchResult.Success)
            {
                field = matchResult.Value.ToUpper();

                if (ExcelColumnIndex is null or < 0)
                {
                    var indexValue = 0;
                    for (var i = field.Length - 1; i >= 0; i--)
                    {
                        indexValue += field[i] - 65 + i * 10;
                    }

                    ExcelColumnIndex = indexValue;
                }
            }
            else
            {
                throw new ArgumentException("excel索引名称错误", nameof(value));
            }
        }
    }

    public bool ExcelIgnore { get; set; }
    
    public int CompareTo(ExcelColumnInfo? other)
    {
        if (other is null)
        {
            return int.MaxValue;
        }

        if (ExcelColumnIndex is not null && other.ExcelColumnIndex is not null)
        {
            return ExcelColumnIndex.Value - other.ExcelColumnIndex.Value;
        }

        return 1;
    }
    
    public class ExcelColumnIndexComparer : IComparer<ExcelColumnInfo>
    {
        /// <inheritdoc />
        public int Compare(ExcelColumnInfo? x, ExcelColumnInfo? y)
        {
            ArgumentNullException.ThrowIfNull(x);
            ArgumentNullException.ThrowIfNull(y);

            return x.CompareTo(y);
        }
    }
    
    [GeneratedRegex("^[A-z]+$", RegexOptions.IgnoreCase, "zh-CN")]
    private static partial Regex ExcelColumnIndexNameRegex();
}