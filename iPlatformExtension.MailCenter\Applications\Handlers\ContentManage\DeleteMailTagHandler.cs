﻿using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using iPlatformExtension.Common.Extensions;
using Microsoft.AspNetCore.Http;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Common.Helper;
using Microsoft.AspNetCore.Server.HttpSys;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class DeleteMailTagHandler(IMailTagRepository mailTagRepository, IMailTagListRepository mailTagListRepository, IHttpContextAccessor httpContextAccessor) : IRequestHandler<DeleteMailTagCommand>
    {
        public async Task Handle(DeleteMailTagCommand request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var tag = await mailTagRepository.Where(o => request.mailTagId.Any(i=>i == o.Id) && o.UserId == userId).ToListAsync();
            if (tag != null)
            {
                await mailTagListRepository.DeleteAsync(o => request.mailTagId.Any(i => i == o.Id));
                await mailTagRepository.DeleteAsync(o => request.mailTagId.Any(i => i == o.Id) && o.UserId == userId);
            }
            else
            {
                throw new Exception("删除失败,没有找到对应Id");
            }
        }
    }
}
