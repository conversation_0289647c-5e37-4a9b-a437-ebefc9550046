﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.Signature;
using iPlatformExtension.MailCenter.Applications.Queries.Signature;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Signature
{
    /// <summary>
    /// 获取签名列表查询处理器
    /// </summary>
    internal sealed class GetSignatureListQueryHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IHttpContextAccessor httpContextAccessor
    ) : IRequestHandler<GetSignatureListQuery, PageResult<GetSignatureListDto>>
    {
        public async Task<PageResult<GetSignatureListDto>> Handle(
            GetSignatureListQuery request,
            CancellationToken cancellationToken
        )
        {
            // 获取当前用户ID
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;

            // 构建查询
            var query = freeSql
                .Select<MailSignature>()
                .WithLock()
                .Where(it => it.UserId == userId);

            // 应用名称模糊查询条件
            if (!string.IsNullOrWhiteSpace(request.Name))
            {
                query = query.Where(it => it.Name.Contains(request.Name));
            }

            query = request.Sort is null ? query.OrderByDescending(it => it.Name) : string.Equals(request.SortType, "asc", comparisonType: StringComparison.OrdinalIgnoreCase) ?
                query.OrderByPropertyName(request.Sort) : query.OrderByPropertyName(request.Sort, false);

            // 执行分页查询
            var result = await query
                .Page(request.PageIndex, request.PageSize)
                .Count(out var totalCount)
                .ToListAsync(it => new GetSignatureListDto
                {
                    Id = it.Id,
                    Name = it.Name ?? string.Empty,
                    CreateTime = it.CreateTime,
                    UpdateTime = it.UpdateTime
                }, cancellationToken);

            // 返回分页结果
            return new PageResult<GetSignatureListDto>
            {
                Data = result,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };
        }
    }
}
