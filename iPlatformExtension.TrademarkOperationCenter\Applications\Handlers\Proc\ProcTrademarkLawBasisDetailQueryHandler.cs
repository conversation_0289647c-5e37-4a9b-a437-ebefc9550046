﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class ProcTrademarkLawBasisDetailQueryHandler(IFreeSql freeSql) : IRequestHandler<ProcTrademarkLawBasisDetailQuery, ProcTrademarkLawBasisDetail>
{
    public Task<ProcTrademarkLawBasisDetail> Handle(ProcTrademarkLawBasisDetailQuery request, CancellationToken cancellationToken)
    {
        return freeSql.Select<TrademarkLawBasis>(request.Id).WithLock().ToOneAsync(basis =>
            new ProcTrademarkLawBasisDetail()
            {
                Id = basis.Id,
                FileId = basis.FileId,
                EvidenceName = basis.FileName,
                FactualReason = basis.Reason,
                LawBasisId = basis.LawId,
                ProcId = basis.ProcId
            }, cancellationToken);
    }
}