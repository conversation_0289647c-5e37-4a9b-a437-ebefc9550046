﻿using System.Linq.Expressions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File;

internal sealed class UpdateDeliveryFilesCommandHandler(
    IDeliveryFilesRepository deliveryFilesRepository,
    IMediator mediator)
    : IRequestHandler<UpdateDeliveryFilesCommand>
{
    public async Task Handle(UpdateDeliveryFilesCommand request, CancellationToken cancellationToken)
    {
        var operation = request.Operation;

        if (operation != DeliveryFilesOperation.Ignore)
        {
            var groups = request.Files.GroupBy(dto => dto.ProcId);
            foreach (var group in groups)
            {
                var procId = group.Key;
                Expression<Func<DeliFiles, bool>> deleteCondition = files => files.ProcId == procId;

                if (operation == DeliveryFilesOperation.Replace)
                {
                    var fileTypes = group.Select(dto => new FileTypeKey(dto.BaseFileType, dto.FileDescription)).Distinct();
                    deleteCondition = deleteCondition.And(fileTypes.Aggregate(default(Expression<Func<DeliFiles, bool>>),
                        (expression, fileType) => expression.Or(files =>
                            files.BaseFileType == fileType.FileType && files.FileDesc == fileType.FileDescription)));
                }

                await deliveryFilesRepository.DeleteAsync(deleteCondition, cancellationToken);
            }
        }

        await mediator.Send(new AddDeliFileCommand(request.Files), cancellationToken);
    }

    /// <summary>
    /// 文件类型key
    /// </summary>
    /// <param name="FileType">文件类型</param>
    /// <param name="FileDescription">文件描述</param>
    private readonly record struct FileTypeKey(string FileType, string FileDescription);
}