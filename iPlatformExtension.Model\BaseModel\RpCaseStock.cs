using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_case_stock", DisableSyncStructure = true)]
	public partial class RpCaseStock {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_status", StringLength = 50)]
		public string CaseStatus { get; set; }

		[ Column(Name = "case_status_zh_cn", StringLength = 50)]
		public string CaseStatusZhCn { get; set; }

		[ Column(Name = "ctrl_proc", StringLength = 50)]
		public string CtrlProc { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "month", StringLength = 50)]
		public string Month { get; set; }

		[ Column(Name = "number")]
		public int? Number { get; set; }

		[ Column(Name = "proc_status", StringLength = 50)]
		public string ProcStatus { get; set; }

		[ Column(Name = "quarter", StringLength = 50)]
		public string Quarter { get; set; }

		[ Column(Name = "rank_zh_cn", StringLength = 50)]
		public string RankZhCn { get; set; }

		[ Column(Name = "tech_classify", StringLength = 50)]
		public string TechClassify { get; set; }

		[ Column(Name = "year", StringLength = 50)]
		public string Year { get; set; }

	}

}
