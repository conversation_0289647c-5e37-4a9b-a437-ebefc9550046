using iPlatformExtension.Common.Clients.Mail;
using iPlatformExtension.Common.ObjectPools.SMTP;
using MailKit.Net.Smtp;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace iPlatformExtension.Common.Extensions;

public static class MailClientExtension
{
    private static readonly ObjectFactory<SmtpClientPooledObjectPolicy> smtpClientPooledObjectPolicyFactory =
        ActivatorUtilities.CreateFactory<SmtpClientPooledObjectPolicy>([typeof(string)]);

    private static readonly ObjectFactory<ScopedSmtpClient> scopedClientFactory =
        ActivatorUtilities.CreateFactory<ScopedSmtpClient>([typeof(string)]);
    
    public static IServiceCollection AddSmtpClientPool(this IServiceCollection services, string serviceKey, Action<MailAccount>? buildOptions = null)
    {
        services.TryAddSingleton<PooledSmtpClientBackgroundService>();
        services.AddHostedService(provider => provider.GetRequiredService<PooledSmtpClientBackgroundService>());
        services.TryAddKeyedSingleton<SmtpClientPooledObjectPolicy>(serviceKey,
            (provider, key) => smtpClientPooledObjectPolicyFactory(provider, [key]));
        services.AddOptions<MailAccount>(serviceKey).Configure(buildOptions ?? (_ => { }));
        services.TryAddKeyedSingleton<SmtpClientPool>(serviceKey, (provider, key) =>
        {
            var policy = provider.GetRequiredKeyedService<SmtpClientPooledObjectPolicy>(key);
            var backgroundService = provider.GetRequiredService<PooledSmtpClientBackgroundService>();
            return new SmtpClientPool(policy, backgroundService);
        });
        services.TryAddKeyedScoped<SmtpClient>(serviceKey, (provider, key) =>
        {
            var pool = provider.GetRequiredKeyedService<SmtpClientPool>(key);
            return new SmtpClientAccessor(pool).Client;
        });

        return services;
    }

    public static IServiceCollection AddSmtpClient(this IServiceCollection services, string serviceKey, Action<MailAccount>? buildAccount = null)
    {
        services.AddOptions<MailAccount>(serviceKey).Configure(buildAccount ?? (_ => { }));
        
        services.AddKeyedScoped<SmtpClient, ScopedSmtpClient>(serviceKey, (provider, key) =>
        {
            var service = scopedClientFactory(provider, [key]);
            service.EnsureInitAsync(CancellationToken.None).ConfigureAwait(false).GetAwaiter().GetResult();
            return service;
        });

        return services;
    }

    public static WebApplicationBuilder AddSmtpClients(this WebApplicationBuilder applicationBuilder,
        string sectionPath = "SmtpClients")
    {
        var accountInfos = applicationBuilder.Configuration.Get<Dictionary<string, MailAccount>>(sectionPath);
        if (accountInfos is null) return applicationBuilder;
        foreach (var (key, mailAccount) in accountInfos)
        {
            applicationBuilder.Services.AddSmtpClient(key, account =>
            {
                account.Host = mailAccount.Host;
                account.PooledCount = 0;
                account.Port = mailAccount.Port;
                account.Username = mailAccount.Username;
                account.Password = mailAccount.Password;
            });
        }

        return applicationBuilder;
    }

    public static WebApplicationBuilder AddSmtpClientPools(this WebApplicationBuilder applicationBuilder, string sectionPath = "SmtpClients")
    {
        var accountInfos = applicationBuilder.Configuration.Get<Dictionary<string, MailAccount>>(sectionPath);
        if (accountInfos is null) return applicationBuilder;
        foreach (var (key, mailAccount) in accountInfos)
        {
            applicationBuilder.Services.AddSmtpClientPool(key, account =>
            {
                account.Host = mailAccount.Host;
                account.PooledCount = mailAccount.PooledCount;
                account.Port = mailAccount.Port;
                account.Username = mailAccount.Username;
                account.Password = mailAccount.Password;
            });
        }

        return applicationBuilder;
    }

    public static Task EnsureInitAsync(this SmtpClient smtpClient, CancellationToken cancellationToken)
    {
        if (smtpClient is ScopedSmtpClient scopedSmtpClient)
        {
            return scopedSmtpClient.EnsureInitAsync(cancellationToken);
        }

        return Task.CompletedTask;
    }
}