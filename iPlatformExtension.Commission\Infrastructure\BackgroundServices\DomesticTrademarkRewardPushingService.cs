﻿using System.Threading.Channels;
using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;

namespace iPlatformExtension.Commission.Infrastructure.BackgroundServices;

internal sealed class DomesticTrademarkRewardPushingService(
    Channel<PushDomesticRewardCommand> channel, 
    ILogger<PushDomesticRewardCommand> logger, 
    IServiceScopeFactory serviceScopeFactory) 
    : BackgroundConsumeService<PushDomesticRewardCommand>(channel, logger, serviceScopeFactory);