﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface IBaseCtrlProcRepository : 
    IStringKeyCacheableRepository<BasCtrlProc>, 
    IRedisCacheableRepository<string, BasCtrlProc>,
    IBaseRepository<BasCtrlProc, string>,
    IScopeDependency
{
    Task<BasCtrlProc?> ICacheableRepository<string, BasCtrlProc>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Where(proc => proc.CtrlProcId == key).WithLock().ToOneAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasCtrlProc>> ICacheableRepository<string, BasCtrlProc>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasCtrlProc>.GenerateKey(BasCtrlProc value)
    {
        ArgumentNullException.ThrowIfNull(value);
        return value.CtrlProcId;
    }

    string IStringKeyCacheableRepository<BasCtrlProc>.GetCacheTextValue(BasCtrlProc value)
    {
        return value.CtrlProcZhCn;
    }
}