using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_contact_list", DisableSyncStructure = true)]
	public partial class AppContactList {

		[ Column(Name = "contact_id", StringLength = 50, IsNullable = false)]
		public string ContactId { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; }

	}

}
