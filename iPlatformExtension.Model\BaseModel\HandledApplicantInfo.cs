﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 处理的申请人信息
/// </summary>
[Table(Name = "handled_applicant_info", DisableSyncStructure = true)]
public sealed class HandledApplicantInfo 
{

	/// <summary>
	/// 自增主键
	/// </summary>
	[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
	public int Id { get; set; }

	/// <summary>
	/// 证件编号
	/// </summary>
	[Column(Name = "certificate_number", StringLength = 50, IsNullable = false)]
	public string CertificateNumber { get; set; } = "";

	/// <summary>
	/// 证件类型
	/// </summary>
	[Column(Name = "certificate_type", StringLength = 50, IsNullable = false)]
	public string CertificateType { get; set; } = "";

	/// <summary>
	/// 中文地址
	/// </summary>
	[Column(Name = "cn_address", IsNullable = false)]
	public string CnAddress { get; set; } = "";

	/// <summary>
	/// 中文名称
	/// </summary>
	[Column(Name = "cn_name", IsNullable = false)]
	public string CnName { get; set; } = "";

	/// <summary>
	/// 国家id
	/// </summary>
	[Column(Name = "country_id", StringLength = 50, IsNullable = false)]
	public string CountryId { get; set; } = "";

	/// <summary>
	/// 创建时间
	/// </summary>
	[Column(Name = "creation_time", InsertValueSql = "getdate()")]
	public DateTime CreationTime { get; set; }

	/// <summary>
	/// 创建者
	/// </summary>
	[Column(Name = "creator", StringLength = 50, IsNullable = false)]
	public string Creator { get; set; } = "";

	/// <summary>
	/// 英文地址
	/// </summary>
	[Column(Name = "en_address", IsNullable = false)]
	public string EnAddress { get; set; } = "";

	/// <summary>
	/// 英文名称
	/// </summary>
	[Column(Name = "en_name", IsNullable = false)]
	public string EnName { get; set; } = "";

	/// <summary>
	/// 处理类型
	/// </summary>
	[Column(Name = "handle_type", StringLength = 5, IsNullable = false)]
	public string HandleType { get; set; } = "";

	/// <summary>
	/// 身份证明原件是否为中文
	/// </summary>
	[Column(Name = "is_chinese_identity")]
	public bool IsChineseIdentity { get; set; } = true;

	/// <summary>
	/// 邮编
	/// </summary>
	[Column(Name = "post_code", StringLength = 50, IsNullable = false)]
	public string PostCode { get; set; } = "";

	/// <summary>
	/// 任务id
	/// </summary>
	[Column(Name = "proc_id", StringLength = 50)]
	public string ProcId { get; set; }

	/// <summary>
	/// 申请人类型
	/// </summary>
	[Column(Name = "type_id", StringLength = 50, IsNullable = false)]
	public string TypeId { get; set; } = "";

	/// <summary>
	/// 更新时间
	/// </summary>
	[Column(Name = "update_time", InsertValueSql = "getdate()")]
	public DateTime UpdateTime { get; set; }

	/// <summary>
	/// 更新者
	/// </summary>
	[Column(Name = "updater", StringLength = 50, IsNullable = false)]
	public string Updater { get; set; } = "";

}