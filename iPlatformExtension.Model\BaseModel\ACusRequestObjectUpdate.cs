using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_cus_request_object_update", DisableSyncStructure = true)]
	public partial class ACusRequestObjectUpdate {

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "customer_name", StringLength = 50)]
		public string CustomerName { get; set; }

		[ Column(Name = "is_del")]
		public bool? IsDel { get; set; }

		[ Column(Name = "is_do")]
		public bool? IsDo { get; set; } = false;

		[ Column(Name = "new_id", StringLength = 50)]
		public string NewId { get; set; }

		[ Column(Name = "new_name", StringLength = 500)]
		public string NewName { get; set; }

		[ Column(Name = "old_id", StringLength = 50)]
		public string OldId { get; set; }

		[ Column(Name = "old_name", StringLength = 500)]
		public string OldName { get; set; }

	}

}
