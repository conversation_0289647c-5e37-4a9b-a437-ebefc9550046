using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_templet_columns", DisableSyncStructure = true)]
	public partial class MailTempletColumns {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_en_us", StringLength = 50)]
		public string ColumnEnUs { get; set; }

		[ Column(Name = "column_id", StringLength = 50)]
		public string ColumnId { get; set; }

		[ Column(Name = "column_ja_jp", StringLength = 50)]
		public string ColumnJaJp { get; set; }

		[ Column(Name = "column_zh_cn", StringLength = 50)]
		public string ColumnZhCn { get; set; }

		[ Column(Name = "flow_sub_type", StringLength = 50)]
		public string FlowSubType { get; set; }

		[ Column(Name = "flow_type", StringLength = 50)]
		public string FlowType { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
