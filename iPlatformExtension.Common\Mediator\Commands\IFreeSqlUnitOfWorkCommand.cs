﻿using System.Data;
using FreeSql;

namespace iPlatformExtension.Common.Mediator.Commands;

public interface IFreeSqlUnitOfWorkCommand
{
    Propagation TransactionPropagation => Propagation.Required;

    IsolationLevel? IsolationLevel => null;

    internal Type FreeSqlType { get; }
}

public interface IFreeSqlUnitOfWorkCommand<T> : IFreeSqlUnitOfWorkCommand
{
    Type IFreeSqlUnitOfWorkCommand.FreeSqlType => typeof(T);
}