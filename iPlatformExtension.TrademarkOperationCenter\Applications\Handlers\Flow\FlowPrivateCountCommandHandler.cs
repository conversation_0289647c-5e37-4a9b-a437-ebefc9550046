﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using MediatR;
using Microsoft.AspNetCore.SignalR.Client;
using MongoDB.Bson;
using System.Linq.Expressions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using static System.Runtime.InteropServices.JavaScript.JSType;
using SysFlowPrivate = iPlatformExtension.Model.BaseModel.SysFlowPrivate;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    /// <summary>
    /// 个人标签数量统计
    /// </summary>
    internal sealed class FlowPrivateCountCommandHandler(
        IFreeSql freeSql,
        HubConnection hubConnection,
        ILogger<FlowPrivateCountCommandHandler> logger)
        : INotificationHandler<FlowPrivateCountCommand>
    {
        public async Task Handle(FlowPrivateCountCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var list = new List<FlowAnalyseDto>();

                //自定义标签统计
                var sysFlowPrivateCount = freeSql.Select<SysFlowActivity, SysFlowPrivate, SysFlowNode, DeliInfo>()
                    .LeftJoin(it => it.t1.PrivateId == it.t2.PrivateId)
                    .LeftJoin(it => it.t1.CurNodeId == it.t3.NodeId)
                    .LeftJoin(it => it.t1.ObjId == it.t4.ProcId)
                    .Where((o) => o.t1.FlowType == "DE" && o.t1.FlowSubType == "TII")
                    .Where(o => (o.t3.NodeCode == null || o.t3.NodeCode != "TII_DE_End"))
                    .Where(o => o.t4.Status == (int)DeliveryStatus.Ready && o.t4.OperationResult == true).WithLock()
                    .GroupBy(o => new { o.t1.CurUserId, o.t2.PrivateId, o.t2.PrivateName }).Select(o =>
                        new FlowAnalyseDto
                        {
                            Count = o.Count(),
                            UserId = o.Value.Item1.CurUserId,
                            Type = "FlowPrivate".ToString(),
                            Title = o.Value.Item2.PrivateId
                        });
                list.AddRange(sysFlowPrivateCount);

                #region 先不做推送



                //                //自定义标签统计
                //                var procPrivateCount = freeSql.Select<CaseProcInfo, SysFlowPrivate, CaseInfo>()
                //                    .LeftJoin(it => it.t1.PrivateId == it.t2.PrivateId)
                //                    .LeftJoin(it => it.t1.CaseId == it.t3.Id)
                //.Where(it => it.t3.CaseTypeId == "T" && it.t1.FinishDate == null
                //             && !new List<string>() { CASE_PROC_STATUS.WC, CASE_PROC_STATUS.BCL, CASE_PROC_STATUS.YCL }.Contains(it.t1.ProcStatusId)
                //             && (it.t1.OutUser == null || it.t1.OutUser == ""))
                //                    .Where(it => it.t1.UndertakeUserId != null).WithLock()
                //                    .GroupBy(it => new { it.t1.UndertakeUserId, it.t2.PrivateId, it.t2.PrivateName }).Select(o =>
                //                        new FlowAnalyseDto
                //                        {
                //                            Count = o.Count(),
                //                            UserId = o.Value.Item1.UndertakeUserId,
                //                            Type = "OutTabPrivate".ToString(),
                //                            Title = o.Value.Item2.PrivateId ?? "PENDING"
                //                        });
                //                list.AddRange(procPrivateCount);
                //                if (hubConnection.State == HubConnectionState.Connected)
                //                {
                //                    await hubConnection.InvokeAsync("NotificationFlowMessageAsync", "FlowPrivateMessage", list, cancellationToken: cancellationToken);
                //                }
                //                list.Clear();
                //                //出口标签统计
                //                var procCount = freeSql.Select<CaseProcInfo, CaseInfo, SysPaperTab, SysDictionary>()
                //                    .LeftJoin(it => it.t1.CaseId == it.t2.Id)
                //                    .LeftJoin(it => it.t1.CtrlProcId == it.t3.CtrlProcId)
                //                    .LeftJoin(it => it.t3.DictionaryId == it.t4.DictionaryId)
                //                    .Where(it => it.t4.DictionaryName == "trademark_tab_out")
                //.Where(it => it.t2.CaseTypeId == "T" && it.t1.FinishDate == null
                //             && !new List<string>() { CASE_PROC_STATUS.WC, CASE_PROC_STATUS.BCL, CASE_PROC_STATUS.YCL }.Contains(it.t1.ProcStatusId)
                //             && (it.t1.OutUser == null || it.t1.OutUser == ""))
                //                    .Where(it => it.t1.UndertakeUserId != null).WithLock()
                //                    .GroupBy(it => new { it.t1.UndertakeUserId, it.t3.DictionaryId, it.t4.Value }).Select(o =>
                //                        new FlowAnalyseDto
                //                        {
                //                            Count = o.Count(),
                //                            UserId = o.Value.Item1.UndertakeUserId,
                //                            Type = "OutTab".ToString(),
                //                            Title = o.Value.Item4.Value
                //                        });
                //                list.AddRange(procCount);
                #endregion

                if (hubConnection.State == HubConnectionState.Connected)
                {
                    await hubConnection.InvokeAsync("NotificationFlowMessageAsync", "FlowPrivateMessage", list, cancellationToken: cancellationToken);
                }
            }
            catch (Exception e)
            {
                logger.LogError(e, $"流程统计执行失败！");
            }
        }
    }
}

