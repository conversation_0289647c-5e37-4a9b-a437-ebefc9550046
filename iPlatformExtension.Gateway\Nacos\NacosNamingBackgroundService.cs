using System.Collections.Immutable;
using System.Threading.Channels;
using Microsoft.Extensions.Options;
using Nacos.V2;
using Nacos.V2.Naming.Dtos;
using Nacos.V2.Naming.Event;

namespace iPlatformExtension.Gateway.Nacos;

internal sealed class NacosNamingBackgroundService(
    IHostEnvironment hostEnvironment,
    ILogger<NacosNamingBackgroundService> logger,
    IOptionsMonitor<NacosYarpOptions> options,
    INacosNamingService nacosNamingService, 
    NacosNamingProxyConfigProvider configProvider) : 
    IHostedLifecycleService
{
    private const int PageSize = 500;

    private HashSet<string>? _currentServiceNames;

    private readonly CancellationTokenSource _cancellationTokenSource = new();

    private CancellationTokenSource _optionsChangeTokenSource = new();

    private Task _refreshServicesTask = Task.CompletedTask;
    
    

    private readonly Channel<NacosYarpOptions> _refreshOptionsQueue = Channel.CreateUnbounded<NacosYarpOptions>();
    
    public IReadOnlySet<string> CurrentServiceNames => _currentServiceNames ??= [];
    
    /// <inheritdoc />
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        var scope = logger.BeginScope("网关路由初始化");
        var instanceInfos = await GetRemoteServiceInfoAsync();
        _currentServiceNames = instanceInfos.Keys.Where(serviceName =>
        {
            NacosClusterId clusterId = serviceName;
            return !StringComparer.CurrentCultureIgnoreCase.Equals(clusterId.ServiceName,
                hostEnvironment.ApplicationName);
        }).ToHashSet(StringComparer.CurrentCultureIgnoreCase);
        foreach (var (serviceGroupName, instances) in instanceInfos)
        {
            NacosClusterId clusterId = serviceGroupName;
            var (serviceName, groupName) = clusterId;
            if (!StringComparer.CurrentCultureIgnoreCase.Equals(serviceName, hostEnvironment.ApplicationName))
            {
                configProvider.RefreshServiceConfigs(serviceName, groupName, instances);
            }
        }
        scope?.Dispose();
    }

    /// <inheritdoc />
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        await _cancellationTokenSource.CancelAsync();
        
        ArgumentNullException.ThrowIfNull(_currentServiceNames);
        
        foreach (NacosClusterId currentServiceName in _currentServiceNames)
        {
            var (serviceName, groupName) = currentServiceName;
            await nacosNamingService.Unsubscribe(serviceName, groupName, configProvider);
        }
    }

    /// <inheritdoc />
    public async Task StartedAsync(CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(_currentServiceNames);
        
        foreach (NacosClusterId currentServiceName in _currentServiceNames)
        {
            var (serviceName, groupName) = currentServiceName;
            await nacosNamingService.Subscribe(serviceName, groupName, configProvider);
        }

        _refreshServicesTask = Task.Factory.StartNew(async () =>
        {
            while (!_cancellationTokenSource.IsCancellationRequested)
            {
                var currentInstanceInfos = await GetRemoteServiceInfoAsync();
                var currentServiceGroupNames =
                    currentInstanceInfos.Keys.Where(serviceName =>
                    {
                        NacosClusterId clusterId = serviceName;
                        return !StringComparer.CurrentCultureIgnoreCase.Equals(clusterId.ServiceName,
                            hostEnvironment.ApplicationName);
                    }).ToImmutableHashSet(StringComparer.CurrentCultureIgnoreCase);
                var currentOptions = options.CurrentValue;
                if (_currentServiceNames.IsSupersetOf(currentServiceGroupNames))
                {
                    logger.LogInformation("服务列表无变化，跳过更新");
                    await Task.Delay(currentOptions.RefreshDuration, cancellationToken);
                    continue;
                }
        
                var scope = logger.BeginScope("定时刷新服务列表更新网关路由信息");

                var newOnlineServices = currentServiceGroupNames.Except(_currentServiceNames);

                foreach (var newOnlineService in newOnlineServices)
                {
                    logger.LogInformation("新增服务{ServiceName}，开始订阅", newOnlineService);
                }
        
                // var offlineServices = _currentServiceNames.Except(currentServiceGroupNames);
                // foreach (NacosClusterId offlineService in offlineServices)
                // {
                //     var (serviceName, groupName) = offlineService;
                //     await nacosNamingService.Unsubscribe(serviceName, groupName, configProvider);
                // }
                
                foreach (var instancesChangeEvent in newOnlineServices.Select(name =>
                         {
                             NacosClusterId clusterId = name;
                             var (serviceName, groupName) = clusterId;
                             return new InstancesChangeEvent(serviceName, groupName, string.Empty, currentInstanceInfos[name]);
                         }))
                {
                    await nacosNamingService.Subscribe(instancesChangeEvent.ServiceName, instancesChangeEvent.GroupName,
                        configProvider);
                    await configProvider.OnEvent(instancesChangeEvent);
                    _currentServiceNames.Add(new NacosClusterId(instancesChangeEvent.ServiceName,
                        instancesChangeEvent.GroupName));
                }
                
                scope?.Dispose();
        
                await Task.Delay(currentOptions.RefreshDuration, cancellationToken);
            }
        }, TaskCreationOptions.LongRunning);
        
        if (_refreshServicesTask.IsCompleted)
        {
            await _refreshServicesTask;
        }
    }

    /// <inheritdoc />
    public Task StartingAsync(CancellationToken cancellationToken)
    {
        options.OnChange(configProvider.RefreshOptions);
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task StoppedAsync(CancellationToken cancellationToken)
    {
        return _refreshServicesTask.WaitAsync(TimeSpan.FromSeconds(5), cancellationToken);
    }

    /// <inheritdoc />
    public Task StoppingAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    internal async Task<Dictionary<string, List<Instance>>> GetRemoteServiceInfoAsync()
    {
        var currentOptions = options.CurrentValue;
        var groupNames = currentOptions.GroupNames;
        var instances = new Dictionary<string, List<Instance>>(StringComparer.CurrentCultureIgnoreCase);
        
        foreach (var groupName in groupNames)
        {
            var pageIndex = 1;
            var listView = await nacosNamingService.GetServicesOfServer(pageIndex, PageSize, groupName);
            
            var serviceNames = new HashSet<string>(listView.Data);
            var searchCount = listView.Count;
                
            while (PageSize <= searchCount)
            {
                listView = await nacosNamingService.GetServicesOfServer(++pageIndex, PageSize, groupName);
                searchCount = listView.Count;
                serviceNames.UnionWith(listView.Data);
            }
            
            logger.LogDebug("服务数量：{ServiceCount}", serviceNames.Count);
            
            foreach (var serviceName in serviceNames)
            {
                logger.LogDebug("服务名称：{ServiceName}", serviceName);
                var nacosClusterId = new NacosClusterId(serviceName, groupName);
                var instanceInfos = await nacosNamingService.GetAllInstances(serviceName, groupName, false);
                if (instanceInfos.Count != 0)
                {
                    instances[nacosClusterId] = instanceInfos;
                }
                logger.LogDebug("NacosClusterId: {NacosClusterId}", nacosClusterId);
            }
        }

        return instances;
    }
}