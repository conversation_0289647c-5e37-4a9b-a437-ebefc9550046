using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Queries.Public;

/// <summary>
/// 请款对象查询
/// </summary>
public class RequestObjectQuery : IRequest<IEnumerable<RequestObjectReDto>>
{
    /// <summary>
    /// 请款对象名称
    /// </summary>
    public string? Name { get; init; }

    /// <summary>
    /// 请款单ID
    /// </summary>
    public string? BillId { get; init; }

    /// <summary>
    /// 客户ID集合
    /// </summary>
    public IEnumerable<string> CustomerIds { get; init; } = Array.Empty<string>();
}