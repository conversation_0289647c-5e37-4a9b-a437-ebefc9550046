﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Copy;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Copy
{
    /// <summary>
    /// 复制案件
    /// </summary>
    internal sealed class CopyCaseCommandHandler(
        IFreeSql freeSql,
        IMediator mediator)
        : IRequestHandler<CopyCaseCommand>
    {
        private readonly List<string> _oneToOne = ["CaseInfo", "CaseExtendInfo"];
        private readonly List<string> _manyToMany = ["CaseApplicantList", "CaseTrademarkNiceCategory"];

        public async Task Handle(CopyCaseCommand request, CancellationToken cancellationToken)
        {
            var copyFiledLists = await freeSql.Select<CopyFiledList>()
                .Where(it => request.CopyFieldList.Contains(it.Id) || request.CopyFieldList.Contains(it.FatherId))
                .WithLock().ToListAsync(cancellationToken);
            var tableList = copyFiledLists.Select(it => it.Table).Distinct();

            foreach (var table in tableList.Where(it => _oneToOne.Contains(it)))
            {
                await mediator.Send(new CopyCommand(table, request.SourceId, request.TargetId,
                    copyFiledLists.Where(it => it.Table == table).Select(it => new Tuple<string, string>(it.Property, it.KeyType)).ToList()), cancellationToken);
            }

            foreach (var table in tableList.Where(it => _manyToMany.Contains(it)))
            {
                await mediator.Send(new CopyManyCommand(table, request.SourceId, request.TargetId,
                    copyFiledLists.Where(it => it.Table == table).Select(it => new Tuple<string, string, string>(it.Property, it.KeyType, it.Id)).ToList()), cancellationToken);
            }
        }
    }
}

