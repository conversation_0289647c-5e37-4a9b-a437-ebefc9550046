﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.Receive;

/// <summary>
/// 获取流程历史
/// </summary>
/// <param name="MailId">邮件id</param>
/// <param name="MailType">动作类型：Receive/收件,Send/发件</param>
public record GetFlowHistoryQuery([Required] string MailId,string? MailType = "Recevie") : IRequest<IEnumerable<GetFlowHistoryDto>>;

