﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow
{
    /// <summary>
    /// 配案流程后续更新案件信息
    /// </summary>
    public record UpdateCaseInfoByATCommand(SysFlowActivity fa,FlowInfo info, UserBaseInfo CurrentUser) : IRequest;
}
