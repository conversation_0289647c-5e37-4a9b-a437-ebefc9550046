﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.SystemDictionary;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 国家信息
/// </summary>
/// <param name="mediator">中介者</param>
[ApiController]
[Route("[controller]")]
public sealed class CountryController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// 查询国家信息
    /// </summary>
    /// <param name="keyword">查询关键字</param>
    /// <param name="countryId">国家id</param>
    /// <returns>国家信息</returns>
    [HttpGet]
    [ResponseCache(Duration = 3600, VaryByQueryKeys = ["keyword"], Location = ResponseCacheLocation.Any)]
    public Task<IEnumerable<CountryInfoDto>> GetAsync([FromQuery]string? keyword, string? countryId) => mediator.Send(new CountryQuery(keyword, countryId));
}