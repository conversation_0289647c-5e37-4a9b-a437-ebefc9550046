﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.Applicant;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Applicant;

internal sealed class ApplicantAddressQueryHandler(IFreeSql freeSql) : 
    IRequestHandler<ApplicantAddressQuery, IEnumerable<AddressInfoDto>>
{
    public async Task<IEnumerable<AddressInfoDto>> Handle(ApplicantAddressQuery request, CancellationToken cancellationToken)
    {
        return await freeSql.Select<CusAddressList>().WithLock()
            .InnerJoin<CusJoinList>((address, joinList) => address.AddressId == joinList.FormObjId)
            .Where<CusJoinList>(joinList => joinList.JoinObjId == request.ApplicantId)
            .Where(address => address.IsEnabled == true)
            .ToListAsync(address => new AddressInfoDto
            {
                AddressId = address.AddressId,
                AddressCn = address.AddressCn,
                AddressEn = address.AddressEn,
                PostCode = address.Postcode
            }, cancellationToken);
    }
}