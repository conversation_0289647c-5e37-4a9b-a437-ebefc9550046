using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_unique_guid", DisableSyncStructure = true)]
	public partial class MailUniqueGuid {

		[ Column(Name = "mail_guid", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string MailGuid { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "display_name", StringLength = 200)]
		public string DisplayName { get; set; }

		[ Column(Name = "down_remark", StringLength = 500)]
		public string DownRemark { get; set; }

		[ Column(Name = "down_time")]
		public DateTime? DownTime { get; set; }

		[ Column(Name = "host_id", StringLength = 50, IsNullable = false)]
		public string HostId { get; set; }

		[ Column(Name = "mail_date")]
		public DateTime MailDate { get; set; }

		[ Column(Name = "mail_subject", StringLength = 500)]
		public string MailSubject { get; set; }

	}

}
