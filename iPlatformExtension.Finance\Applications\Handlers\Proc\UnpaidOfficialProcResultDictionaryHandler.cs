﻿using iPlatformExtension.Finance.Applications.Queries.Proc;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Proc;

internal sealed class UnpaidOfficialProcResultDictionaryHandler(ISystemDictionaryRepository dictionaryRepository) 
    : INotificationHandler<UnpaidOfficialProcResultNotification>
{
    public async Task Handle(UnpaidOfficialProcResultNotification notification, CancellationToken cancellationToken)
    {
        var results = notification.Results;

        foreach (var unpaidOfficialProcDto in results)
        {
            unpaidOfficialProcDto.CaseDirection =
                await dictionaryRepository.GetChineseKeyValueAsync(unpaidOfficialProcDto.CaseDirection);
            unpaidOfficialProcDto.DeliveryKey =
                await dictionaryRepository.GetChineseKeyValueAsync(unpaidOfficialProcDto.DeliveryKey);
        }
    }
}