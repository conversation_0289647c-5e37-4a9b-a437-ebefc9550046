﻿using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Infrastructure.Authorization.SupplierAssignment;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal interface IBuildSupplierAssignmentFilterHandler : IMatchNotificationHandler<SupplierAssignmentAuthorizationContext>
{
    ValueTask<bool> IMatchNotificationHandler<SupplierAssignmentAuthorizationContext>.MatchAsync(SupplierAssignmentAuthorizationContext notification, CancellationToken cancellationToken)
    {
        return ValueTask.FromResult(notification is {CanQueryMine: true, QueryType: QueryType.Personal});
    }
}