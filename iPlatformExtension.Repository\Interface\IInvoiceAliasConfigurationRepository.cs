﻿using System.Diagnostics;
using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Repository.Interface;

public interface IInvoiceAliasConfigurationRepository : 
    IBaseRepository<FinancialInvoiceAliasConfig, string>, 
    ICacheableRepository<KingdeeMaterialTaxationKey, IGrouping<KingdeeMaterialTaxationKey, KingdeeMaterialTaxationInfo>>,
    IScopeDependency
{
    private static readonly KingdeeMaterialTaxationKeyEqualityComparer kingdeeMaterialTaxationKeyEqualityComparer =
        new();

    IEqualityComparer<KingdeeMaterialTaxationKey>? ICacheableRepository<KingdeeMaterialTaxationKey, IGrouping<KingdeeMaterialTaxationKey, KingdeeMaterialTaxationInfo>>.KeyEqualityComparer => kingdeeMaterialTaxationKeyEqualityComparer;

    async Task<IGrouping<KingdeeMaterialTaxationKey, KingdeeMaterialTaxationInfo>?> ICacheableRepository<KingdeeMaterialTaxationKey, IGrouping<KingdeeMaterialTaxationKey, KingdeeMaterialTaxationInfo>>.GetValueFromDbAsync(KingdeeMaterialTaxationKey key, CancellationToken cancellationToken)
    {
        var configurationList = await Select.WithLock().From<FinancialInvoiceDetailConfig>()
            .InnerJoin((aliasConfig, detailConfig) => aliasConfig.ConfigId == detailConfig.Id)
            .Where((aliasConfig, detailConfig) => detailConfig.CaseTypeId == key.CaseType &&
                                                  detailConfig.FeeTypeNameId == key.FeeTypeNameId
                                                  && aliasConfig.CaseDirection == key.CaseDirection &&
                                                  aliasConfig.FeeClass == key.FeeClass)
            .ToListAsync((aliasConfig, detailConfig) =>
            new KingdeeMaterialTaxationInfo()
            {
                CaseType = detailConfig.CaseTypeId,
                CaseDirection = aliasConfig.CaseDirection,
                FeeClass = aliasConfig.FeeClass,
                ApplyType = aliasConfig.ApplyTypeId,
                FeeTypeNameId = detailConfig.FeeTypeNameId,
                FeeNameAlias = aliasConfig.FeeNameAlias,
                KingdeeMaterialCode = aliasConfig.KingdeeMaterialCode,
                KingdeeMaterialName = aliasConfig.KingdeeMaterialName,
                TaxationCode = aliasConfig.TaxationCode,
                TaxationName = aliasConfig.TaxationName
            }, cancellationToken);

        var groups = configurationList.GroupBy(dto =>
            new KingdeeMaterialTaxationKey(dto.FeeTypeNameId, dto.CaseType, dto.CaseDirection, dto.FeeClass));
        Debug.Assert(groups.Count() == 1, "金蝶物料配置有多个分组");
        return groups.FirstOrDefault();
    }

    async Task<IEnumerable<IGrouping<KingdeeMaterialTaxationKey, KingdeeMaterialTaxationInfo>>> ICacheableRepository<KingdeeMaterialTaxationKey, IGrouping<KingdeeMaterialTaxationKey, KingdeeMaterialTaxationInfo>>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        var configurationList = await Select.WithLock().From<FinancialInvoiceDetailConfig>()
            .InnerJoin((aliasConfig, detailConfig) => aliasConfig.ConfigId == detailConfig.Id)
            .ToListAsync((aliasConfig, detailConfig) =>
                new KingdeeMaterialTaxationInfo()
                {
                    CaseType = detailConfig.CaseTypeId,
                    CaseDirection = aliasConfig.CaseDirection,
                    FeeClass = aliasConfig.FeeClass,
                    ApplyType = aliasConfig.ApplyTypeId,
                    FeeTypeNameId = detailConfig.FeeTypeNameId,
                    FeeNameAlias = aliasConfig.FeeNameAlias,
                    KingdeeMaterialCode = aliasConfig.KingdeeMaterialCode,
                    KingdeeMaterialName = aliasConfig.KingdeeMaterialName,
                    TaxationCode = aliasConfig.TaxationCode,
                    TaxationName = aliasConfig.TaxationName
                }, cancellationToken);

        return configurationList.GroupBy(dto =>
            new KingdeeMaterialTaxationKey(dto.FeeTypeNameId, dto.CaseType, dto.CaseDirection, dto.FeeClass));
    }

    KingdeeMaterialTaxationKey ICacheableRepository<KingdeeMaterialTaxationKey, IGrouping<KingdeeMaterialTaxationKey, KingdeeMaterialTaxationInfo>>.GenerateKey(IGrouping<KingdeeMaterialTaxationKey, KingdeeMaterialTaxationInfo> value)
    {
        return value.Key;
    }
}