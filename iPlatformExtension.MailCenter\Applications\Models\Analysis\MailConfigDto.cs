﻿namespace iPlatformExtension.MailCenter.Applications.Models.Analysis
{
    /// <summary>
    /// 邮箱配置dto
    /// </summary>
    /// <param name="ConfigId">配置id</param>
    /// <param name="FilterHead">匹配字段</param>
    /// <param name="FilterType">匹配方式</param>
    /// <param name="FilterValue">匹配值</param>
    public record MailConfigDto(string ConfigId, string FilterHead, string FilterType, string FilterValue);

}
