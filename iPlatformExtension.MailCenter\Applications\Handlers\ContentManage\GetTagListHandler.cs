﻿using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using iPlatformExtension.Common.Extensions;
using Microsoft.AspNetCore.Http;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Common.Helper;
using Microsoft.AspNetCore.Server.HttpSys;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class GetTagListHandler(IFreeSql<MailCenterFreeSql> mySql, IHttpContextAccessor httpContextAccessor) : IRequestHandler<GetTagListQuery, List<MailTagDto>>
    {
        public async Task<List<MailTagDto>> Handle(GetTagListQuery request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var lst = await mySql.Select<MailTag>()
                .Where(o => o.UserId == userId && o.MailType == request.MailType)
                 .ToListAsync<MailTagDto>();
            return lst;

        }
    }
}
