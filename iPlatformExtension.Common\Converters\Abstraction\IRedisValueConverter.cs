using System.Numerics;
using StackExchange.Redis;

namespace iPlatformExtension.Common.Converters.Abstraction;

public interface IRedisValueConverter : IConverter
{
    RedisValue ConvertToRedisValue(object? obj);

    object? ConvertFromRedisValue(RedisValue redisValue, Type objectType);
    
    ReadOnlyMemory<byte> IConverter.ConvertTo(object? obj)
    {
        return TryParse(obj, out var redisValue) ? redisValue : ConvertToRedisValue(obj);
    }

    object? IConverter.ConvertFrom(ReadOnlyMemory<byte> bytes, Type objectType)
    {
        return TryParse(bytes, objectType, out var obj) ? obj : ConvertFromRedisValue(bytes, objectType);
    }

    internal new static bool CanConvert(Type type)
    {
        return type == typeof(string) || typeof(INumber<>).IsAssignableFrom(type) || type == typeof(byte[]) 
               || type == typeof(ReadOnlyMemory<byte>) || type == typeof(Memory<byte>) || type == typeof(bool);
    }

    protected bool TryParse(object? obj, out RedisValue redisValue)
    {
        redisValue = RedisValue.Null;
        switch (obj)
        {
            case null:
                return true;
            case string str:
                redisValue = str;
                return true;
            case int num1:
                redisValue = num1;
                return true;
            case uint num2:
                redisValue = num2;
                return true;
            case double num3:
                redisValue = num3;
                return true;
            case byte[] numArray:
                redisValue = numArray;
                return true;
            case bool flag:
                redisValue = flag;
                return true;
            case long num4:
                redisValue = num4;
                return true;
            case ulong num5:
                redisValue = num5;
                return true;
            case float num6:
                redisValue = (double) num6;
                return true;
            case ReadOnlyMemory<byte> readOnlyMemory:
                redisValue = readOnlyMemory;
                return true;
            case Memory<byte> memory:
                redisValue = memory;
                return true;
            case RedisValue value:
                redisValue = value;
                return true;
            default:
                return false;
        }
    }

    protected bool TryParse(RedisValue redisValue, Type objectType, out object? obj)
    {
        obj = null;
        if (redisValue.IsNullOrEmpty)
        {
            return true;
        }

        if (objectType == typeof(string))
        {
            obj = redisValue.ToString();
            return true;
        }

        if (objectType == typeof(bool))
        {
            obj = (bool) redisValue;
            return true;
        }
        
        

        obj = redisValue.Box();
        return obj is not RedisValue;
    }
}

public interface IRedisValueConverter<T> : IRedisValueConverter
{
    bool IConverter.CanConvert(Type type)
    {
        return type == typeof(T);
    }

    RedisValue ConvertToRedisValue(T? obj);

    RedisValue IRedisValueConverter.ConvertToRedisValue(object? obj)
    {
        if (obj is T t)
            return ConvertToRedisValue(t);
        
        return RedisValue.Null;
    }

    T? ConvertFromRedisValue(RedisValue redisValue);

    object? IRedisValueConverter.ConvertFromRedisValue(RedisValue redisValue, Type objectType)
    {
        if (objectType == typeof(T))
        {
            return ConvertFromRedisValue(redisValue);
        }

        return null;
    }

    ReadOnlyMemory<byte> IConverter.ConvertTo(object? obj)
    {
        if (obj is null)
        {
            return ReadOnlyMemory<byte>.Empty;
        }

        return ((IConverter) this).CanConvert(obj.GetType()) ? ConvertToRedisValue((T) obj) : throw new InvalidOperationException("当前转换器不能转换对象到二进制数据");
    }

    object? IConverter.ConvertFrom(ReadOnlyMemory<byte> bytes, Type objectType)
    {
        if (bytes.IsEmpty)
        {
            return null;
        }
        
        return ((IConverter) this).CanConvert(objectType) ? ConvertFromRedisValue(bytes) : throw new InvalidOperationException("当前转换器不能转换二进制数据到对象");
    }
}