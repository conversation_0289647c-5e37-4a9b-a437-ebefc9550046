using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_message_inner_history", DisableSyncStructure = true)]
	public partial class MailMessageInnerHistory {

		[ Column(Name = "mail_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string MailId { get; set; }

		[ Column(Name = "allow_send")]
		public bool AllowSend { get; set; }

		[ Column(Name = "create_time")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "display_name", StringLength = 500, IsNullable = false)]
		public string DisplayName { get; set; }

		[ Column(Name = "finish_time")]
		public DateTime? FinishTime { get; set; }

		[ Column(Name = "fixed_send_time")]
		public DateTime? FixedSendTime { get; set; }

		[ Column(Name = "host_id", StringLength = 50, IsNullable = false)]
		public string HostId { get; set; }

		[ Column(Name = "html_body", DbType = "ntext", IsNullable = false)]
		public string HtmlBody { get; set; }

		[ Column(Name = "is_attachment")]
		public bool IsAttachment { get; set; }

		[ Column(Name = "is_error")]
		public bool IsError { get; set; } = false;

		[ Column(Name = "is_inside")]
		public bool IsInside { get; set; }

		[ Column(Name = "is_new")]
		public bool? IsNew { get; set; }

		[ Column(Name = "is_private")]
		public bool IsPrivate { get; set; }

		[ Column(Name = "is_send")]
		public bool? IsSend { get; set; }

		[ Column(Name = "mail_date")]
		public DateTime MailDate { get; set; }

		[ Column(Name = "mail_from", StringLength = 200)]
		public string MailFrom { get; set; }

		[ Column(Name = "mail_guid", StringLength = 200, IsNullable = false)]
		public string MailGuid { get; set; }

		[ Column(Name = "mail_size", StringLength = 50)]
		public string MailSize { get; set; }

		[ Column(Name = "mail_subject", StringLength = 500, IsNullable = false)]
		public string MailSubject { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "replay_to", StringLength = 200)]
		public string ReplayTo { get; set; }

		[ Column(Name = "update_time")]
		public DateTime UpdateTime { get; set; }

	}

}
