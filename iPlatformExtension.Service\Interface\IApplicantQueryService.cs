﻿using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Service.Interface;

public interface IApplicantQueryService : IFreeSqlQueryService, ITransientDependency
{

    async Task<Dictionary<string, IEnumerable<ApplicantInfo>>> GetApplicantsByCaseIdsAsync(IEnumerable<string> caseIds)
    {
        if (!caseIds.Any())
        {
            return new Dictionary<string, IEnumerable<ApplicantInfo>>();
        }

        var applicants = await DbQuery.Select<CusApplicant>().WithLock().From<CaseApplicantList>()
            .InnerJoin((applicant, caseApplicant) => caseApplicant.ApplicantId == applicant.ApplicantId && applicant.IsEnabled == true)
            .Where(table => caseIds.Contains(table.t2.CaseId))
            .OrderBy((applicant, caseApplicant) => caseApplicant.Seq).ToListAsync((applicant, caseApplicant) =>
                new KeyValuePair<string, ApplicantInfo>(caseApplicant.CaseId,
                    new ApplicantInfo(applicant.ApplicantId, applicant.ApplicantNameCn, applicant.ApplicantNameEn)
                    {
                        IsRepresent = caseApplicant.IsRepresent
                    }));

        return applicants.GroupBy(applicantInfo => applicantInfo.Key, applicantInfo => applicantInfo.Value)
            .ToDictionary(group => group.Key, group => group as IEnumerable<ApplicantInfo>);
    }

    async Task<IEnumerable<ApplicantInfo>> GetApplicantsAsync(string? keyword, string? customerId)
    {
        var query = DbQuery.Select<CusApplicant>().WithLock()
            .WhereIf(!string.IsNullOrWhiteSpace(keyword), applicant =>
                applicant.ApplicantNameCn.Contains(keyword!) || applicant.ApplicantNameEn.Contains(keyword!));
        if (!string.IsNullOrWhiteSpace(customerId))
        {
            query.InnerJoin<CusJoinList>((applicant, joinList) => applicant.ApplicantId == joinList.FormObjId)
                .Where<CusJoinList>((applicant, joinList) => joinList.JoinObjId == customerId);
        }
        return await query.Where(applicant => applicant.IsEnabled == true).Page(1, 20)
            .ToListAsync(applicant =>
                new ApplicantInfo(applicant.ApplicantId, applicant.ApplicantNameCn, applicant.ApplicantNameEn));
    }
}