﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

/// <summary>
/// 费项更新处理者
/// </summary>
/// <typeparam name="TRequest">费项更新命令</typeparam>
internal interface IFeesUpdateCommandHandler<in TRequest> : IRequestHandler<TRequest, IEnumerable<FeesUpdateResult>>
    where TRequest : IFeesUpdateCommand, IRequest<IEnumerable<FeesUpdateResult>>
{
    /// <summary>
    /// 费项仓储
    /// </summary>
    ICaseFeeRepository CaseFeeRepository { get; }
    
    /// <summary>
    /// http上下文辅助器
    /// </summary>
    IHttpContextAccessor HttpContextAccessor { get; }
    
    async Task<IEnumerable<FeesUpdateResult>> IRequestHandler<TRequest, IEnumerable<FeesUpdateResult>>.Handle(TRequest request, CancellationToken cancellationToken)
    {
        var operatorId = (HttpContextAccessor.HttpContext?.User).GetUserId();
        var results = new List<FeesUpdateResult>(request.FeesParameters.Count());
        foreach (var feeUpdateInfoDto in request.FeesParameters)
        {
            var caseFeeList = await CaseFeeRepository.GetAsync(feeUpdateInfoDto.FeeId, cancellationToken);
            if (caseFeeList is null) 
                throw FeesUpdateException.NotExist(feeUpdateInfoDto.FeeId);

            var updateResult = UpdateFee(caseFeeList, feeUpdateInfoDto, operatorId);
            if (!updateResult.Success || await CaseFeeRepository.UpdateAsync(caseFeeList, cancellationToken) <= 0)
            {
                throw new FeesUpdateException(updateResult);
            }
            results.Add(updateResult);
        }

        return results;
    }

    /// <summary>
    /// 更新费项
    /// </summary>
    /// <param name="caseFeeList">费项数据</param>
    /// <param name="feeUpdateInfoDto">更新数据</param>
    /// <param name="operatorId">操作人</param>
    /// <returns>更新结果</returns>
    FeesUpdateResult UpdateFee(CaseFeeList caseFeeList, FeeUpdateInfoDto feeUpdateInfoDto, string operatorId);
}