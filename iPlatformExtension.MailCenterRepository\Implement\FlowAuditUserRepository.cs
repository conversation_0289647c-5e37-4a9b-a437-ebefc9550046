using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;

namespace iPlatformExtension.MailCenterRepository.Implement;

/// <summary>
/// 发件审核人仓储实现
/// </summary>
internal class FlowAuditUserRepository(
    IFreeSql<MailCenterFreeSql> fsql,
    UnitOfWorkManage<MailCenterFreeSql> manager)
    : DefaultRepository<FlowAuditUser, string>(fsql, manager), IFlowAuditUserRepository;
