﻿using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Cache;

public sealed class CacheProvider
{
    private readonly IServiceProvider _serviceProvider;

    public CacheProvider(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public ICacheableRepository<TKey, TValue>? GetCacheableRepository<TKey, TValue>() where TKey : notnull =>
        _serviceProvider.GetService<ICacheableRepository<TKey, TValue>>();

    public ValueTask<TValue?> GetCacheValueAsync<TKey, TValue>(TKey key) where TK<PERSON> : notnull
    {
        var repository = GetCacheableRepository<TKey, TValue>();
        return repository?.GetCacheValueAsync(key) ?? default;
    }
}