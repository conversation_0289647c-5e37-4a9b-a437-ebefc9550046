﻿using System.Linq;
using System.Reflection;
using FreeSql;
using FreeSql.DataAnnotations;
using FreeSql.Internal.Model;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Copy;
using MediatR;
using NLog.Targets;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Copy
{
    /// <summary>
    /// 
    /// </summary>
    internal sealed class CopyManyCommandHandler(
        IFreeSql freeSql,
        UnitOfWorkManager unitOfWorkManager,
        EntityTypeInfoProvider entityTypeInfoProvider,
        ISender sender)
        : IRequestHandler<CopyManyCommand>
    {
        private Dictionary<string, string?> InsertDictionary = new();
        public async Task Handle(CopyManyCommand request, CancellationToken cancellationToken)
        {
            var assembly = typeof(CaseInfo).Assembly;
            var type = Type.GetType($"iPlatformExtension.Model.BaseModel.{request.Table},{assembly}");
            if (type is null) throw new ArgumentNullException($"{nameof(type)}获取类型失败");
            var keyList = request.CopyFieldList.Where(it => it.Item2 is "ForeignKey" or "TempKey").ToArray().OrderBy(it => it.Item2);
            if (!keyList.Any()) throw new ArgumentException("缺少关联外键");
            var baseRepository = freeSql.GetRepository<object>();
            baseRepository.AsType(type);
            baseRepository.UnitOfWork = unitOfWorkManager.Current;
            foreach (var keyTuple in keyList)
            {
                switch (keyTuple.Item2)
                {
                    case "TempKey":
                        {
                            string? key = String.Empty;
                            var childEntityTypeInfo = entityTypeInfoProvider.Get(type);
                            foreach (var property in childEntityTypeInfo.EntityPropertyInfos)
                            {
                                if (!property.IsKey) continue;
                                key = property.ColumnName;
                                break;
                            }
                            var sourceList = await baseRepository.Select.WhereDynamic(new DynamicFilterInfo
                            {
                                Field = keyTuple.Item1,
                                Operator = DynamicFilterOperator.Any,
                                Value = request.SourceId,
                                Logic = DynamicFilterLogic.Or,
                            }).ToListAsync<string>(key, cancellationToken);
                            foreach (var source in sourceList)
                            {
                                await sender.Send(new CopyProcCommand(source, InsertDictionary.Where(it => it.Key == source).Select(it => it.Value).ToArray()
                                    , request.CopyFieldList.Where(it => it.Item2 is "TempKey").Select(it => it.Item3).ToArray(), true), cancellationToken);

                            }
                            break;
                        }
                    default:
                        {
                            var sourceList = await baseRepository.Select.WhereDynamic(new DynamicFilterInfo
                            {
                                Field = keyTuple.Item1,
                                Operator = DynamicFilterOperator.Any,
                                Value = request.SourceId,
                                Logic = DynamicFilterLogic.Or,
                            }).ToListAsync(cancellationToken);
                            List<object>? targetList = new List<object>();
                            if (request.TargetId.Any(it => !string.IsNullOrWhiteSpace(it)))
                            {
                                targetList = await baseRepository.Select.WhereDynamic(new DynamicFilterInfo
                                {
                                    Field = keyTuple.Item1,
                                    Operator = DynamicFilterOperator.Any,
                                    Value = request.TargetId,
                                    Logic = DynamicFilterLogic.Or,
                                }).ToListAsync(cancellationToken);
                                await baseRepository.DeleteAsync(targetList, cancellationToken);
                                targetList.Clear();
                            }

                            var entityTypeInfo = entityTypeInfoProvider.Get(type);

                            foreach (var id in request.TargetId)
                            {
                                foreach (var source in sourceList)
                                {
                                    var entity = entityTypeInfo.CreateInstance();
                                    foreach (var field in request.CopyFieldList)
                                    {
                                        string? key = null;
                                        string? sourceKey = null;
                                        foreach (var propertyInfo in entityTypeInfo.EntityPropertyInfos)
                                        {
                                            if (propertyInfo.IsKey)
                                            {
                                                key = propertyInfo.Get?.Invoke(entity)?.ToString();
                                                sourceKey = propertyInfo.Get?.Invoke(source)?.ToString();
                                            }
                                            if (propertyInfo.PropertyName != field.Item1) continue;
                                            switch (field.Item2)
                                            {
                                                case "Key":
                                                    propertyInfo.Set?.Invoke(entity, Guid.NewGuid());
                                                    break;
                                                case "ForeignKey":
                                                    propertyInfo.Set?.Invoke(entity, id);
                                                    break;
                                                case "TempKey":
                                                    break;
                                                default:
                                                    {
                                                        var value = propertyInfo?.Get(source);
                                                        propertyInfo.Set(entity, value);
                                                        break;
                                                    }
                                            }

                                        }

                                        if (key is not null && sourceKey is not null)
                                        {
                                            InsertDictionary.TryAdd(sourceKey, key);
                                        }

                                    }
                                    targetList.Add(entity);
                                }
                            }

                            await baseRepository.InsertAsync(targetList, cancellationToken);
                            break;
                        }
                }
            }

        }
    }
}

