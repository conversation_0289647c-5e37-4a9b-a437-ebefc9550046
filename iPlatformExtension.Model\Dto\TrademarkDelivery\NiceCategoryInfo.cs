﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 官方尼斯分类信息
/// </summary>
public class NiceCategoryInfo
{
    /// <summary>
    /// 尼斯分类id
    /// </summary>
    [JsonPropertyName("cgid")]
    public string CategoryId { get; set; } = default!;

    /// <summary>
    /// 尼斯分类名称
    /// </summary>
    [JsonPropertyName("cgname")]
    public string CategoryName { get; set; } = default!;

    /// <summary>
    /// 分类编号
    /// </summary>
    [JsonPropertyName("cgnum")]
    public string CategoryNumber { get; set; } = default!;

    /// <summary>
    /// 目录等级
    /// </summary>
    [JsonPropertyName("dirlevel")]
    public string Level { get; set; } = default!;

    /// <summary>
    /// 排序
    /// </summary>
    [JsonPropertyName("sort")]
    public string Sort { get; set; } = default!;

    /// <summary>
    /// 分类简介
    /// </summary>
    [JsonPropertyName("cgintro")]
    public string CategoryIntroduction { get; set; } = default!;

    /// <summary>
    /// 父级分类的编号
    /// </summary>
    [JsonPropertyName("cgparent")]
    public string ParentCategory { get; set; } = default!;
}