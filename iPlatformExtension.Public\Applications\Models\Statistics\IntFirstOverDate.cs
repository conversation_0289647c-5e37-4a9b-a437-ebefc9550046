﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Public.Applications.Models.Statistics
{
    /// <summary>
    /// 7天初稿期限内超期
    /// </summary>
    public class IntFirstOverDate : InOverDate
    {

        [ExcelColumn(Name = "初稿期限(内)"), ExcelColumnIndex("N")]
        public DateTime? IntFirstDate { get; init; }

        [ExcelColumn(Name = "超期天数"), ExcelColumnIndex("M")]

        public int? OverDate => (DateTime.Now - IntFirstDate!.Value).Days;
    }
}
