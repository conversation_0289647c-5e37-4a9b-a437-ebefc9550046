﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface IBaseCaseStatusRepository : 
    IBaseRepository<BasCaseStatus, string>,
    IStringKeyCacheableRepository<BasCaseStatus>,
    IScopeDependency,
    IRedisCacheableRepository<string, BasCaseStatus>
{
    Task<BasCaseStatus?> ICacheableRepository<string, BasCaseStatus>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Select.WithLock().Where(status => status.CaseStatusId == key).FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasCaseStatus>> ICacheableRepository<string, BasCaseStatus>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasCaseStatus>.GenerateKey(BasCaseStatus value)
    {
        return value.CaseStatusId;
    }

    string IStringKeyCacheableRepository<BasCaseStatus>.GetCacheTextValue(BasCaseStatus value)
    {
        return value.CaseStatusZhCn;
    }
}