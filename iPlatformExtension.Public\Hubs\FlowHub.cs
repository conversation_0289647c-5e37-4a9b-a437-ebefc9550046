﻿using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.Public.Applications.Commands.Flow;
using iPlatformExtension.Public.Applications.Commands.SignLR;
using MediatR;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace iPlatformExtension.Public.Hubs;

/// <summary>
/// 流程集线器
/// </summary>
public sealed class FlowHub : Hub<FlowHub>
{
    private readonly IMediator _mediator;
    private readonly ILogger<FlowHub> _logger;

    /// <summary>
    /// 构造函数注入
    /// </summary>
    /// <param name="mediator">中介者</param>
    /// <param name="logger">日志记录器</param>
    public FlowHub(IMediator mediator, ILogger<FlowHub> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// 当客户端连接时触发
    /// </summary>
    public override Task OnConnectedAsync()
    {
        var connectionId = Context.ConnectionId;
        var user = Context.User;
        var userId = Context.UserIdentifier;

        _logger.LogInformation("新客户端连接 - ConnectionId: {ConnectionId}, UserId: {UserId}", connectionId, userId);

        if (user?.Identity?.IsAuthenticated == true)
        {
            var claims = user.Claims.Select(c => new { c.Type, c.Value }).ToList();
            _logger.LogInformation("用户信息 - UserId: {UserId}, Claims: {@Claims}", userId, claims);
        }
        else
        {
            _logger.LogWarning("未认证的用户连接 - ConnectionId: {ConnectionId}", connectionId);
        }

        return base.OnConnectedAsync();
    }

    /// <summary>
    /// 当客户端断开连接时触发
    /// </summary>
    public override Task OnDisconnectedAsync(Exception exception)
    {
        var connectionId = Context.ConnectionId;
        var userId = Context.UserIdentifier;

        if (exception != null)
        {
            _logger.LogWarning(exception, "客户端断开连接出错 - ConnectionId: {ConnectionId}, UserId: {UserId}", connectionId, userId);
        }
        else
        {
            _logger.LogInformation("客户端断开连接 - ConnectionId: {ConnectionId}, UserId: {UserId}", connectionId, userId);
        }

        return base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 推送流程数据
    /// </summary>
    /// <param name="method">客户端回调方法</param>
    /// <param name="flowMessages">流程信息数据</param>
    /// <returns></returns>
    public Task NotificationFlowMessageAsync(string method, FlowAnalyseDto[] flowMessages)
    {
        return _mediator.Send(new FlowNotificationCommand(method, flowMessages), Context.ConnectionAborted);
    }

    /// <summary>
    /// 推送数据
    /// </summary>
    /// <param name="method">客户端回调方法</param>
    /// <param name="userId">用户id</param>
    /// <param name="messages">流程信息数据</param>
    /// <returns></returns>
    public async Task MailCountMessageAsync(string method, string userId, object messages)
    {
            // 将消息发送到NotificationHub
            await _mediator.Send(new MessageSendCommand(method, userId, messages), Context.ConnectionAborted);
    }

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    /// <returns>用户信息对象</returns>
    public object GetCurrentUserInfo()
    {
        var connectionId = Context.ConnectionId;
        var user = Context.User;
        var userId = Context.UserIdentifier;

        var claims = new List<object>();
        if (user?.Identity?.IsAuthenticated == true)
        {
            claims = user.Claims.Select(c => new { c.Type, c.Value }).Cast<object>().ToList();
        }

        var userInfo = new
        {
            ConnectionId = connectionId,
            UserId = userId,
            IsAuthenticated = user?.Identity?.IsAuthenticated ?? false,
            Claims = claims
        };

        _logger.LogInformation("客户端请求用户信息 - ConnectionId: {ConnectionId}, UserId: {UserId}", connectionId, userId);

        return userInfo;
    }
}