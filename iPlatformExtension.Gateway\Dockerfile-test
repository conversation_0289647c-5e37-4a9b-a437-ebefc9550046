FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
ENV ASPNETCORE_URLS=http://+:7879
ENV ASPNETCORE_ENVIRONMENT=Staging
ENV DOTNET_GCHeapHardLimit=80000000
ENV TZ=Asia/Shanghai
ENV DOTNET_GCHeapHardLimit=80000000
ENV COMPlus_DbgEnableMiniDump=1
ENV COMPlus_DbgMiniDumpName=/tmp/iplatform-gateway.dmp
ENV COMPlus_CreateDumpDiagnostics=1
WORKDIR /app
EXPOSE 7879

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["iPlatformExtension.Gateway/iPlatformExtension.Gateway.csproj", "iPlatformExtension.Gateway/"]
COPY ["iPlatformExtension.Common/iPlatformExtension.Common.csproj", "iPlatformExtension.Common/"]
COPY ["iPlatformExtension.Model/iPlatformExtension.Model.csproj", "iPlatformExtension.Model/"]
RUN dotnet restore "iPlatformExtension.Gateway/iPlatformExtension.Gateway.csproj"
COPY . .
WORKDIR "/src/iPlatformExtension.Gateway"
RUN dotnet build "iPlatformExtension.Gateway.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "iPlatformExtension.Gateway.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
RUN mkdir "logs"
ENTRYPOINT ["dotnet", "iPlatformExtension.Gateway.dll"]
