﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Outsourcing.Application.Models.Apply;
using MediatR;
using Microsoft.AspNetCore.JsonPatch;

namespace iPlatformExtension.Outsourcing.Application.Commands.Apply;

internal sealed record UpdateAppCaseCommand(
    string AppCaseId,
    JsonPatchDocument<ApplyCasePatchDto> PatchDocument) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;