﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Confluent.Kafka;
using iPlatformExtension.Common.MQ;
using iPlatformExtension.Common.MQ.KafKa.Consumer;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using iPlatformExtension.Common.MQ.KafKa.Models;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace KafkaProvider
{
    public class KafkaService : IKafkaService
    {
        private readonly ILoggerFactory? _loggerFactory;
        private readonly KafkaConsumerFactory _consumerFactory;
        private readonly KafkaConsumerManager _consumerManager;
        private readonly KafkaErrorHandler _errorHandler;
        private ProducerConfig? _producerConfig;
        private ConsumerConfig? _consumerConfig;

        public KafkaService(ILoggerFactory? loggerFactory = null)
        {
            _loggerFactory = loggerFactory;

            // 创建各个组件
            var consumerFactoryLogger = _loggerFactory?.CreateLogger<KafkaConsumerFactory>();
            _consumerFactory = new KafkaConsumerFactory(consumerFactoryLogger);

            var consumerManagerLogger = _loggerFactory?.CreateLogger<KafkaConsumerManager>();
            _consumerManager = new KafkaConsumerManager(_consumerFactory, consumerManagerLogger);

            var errorHandlerLogger = _loggerFactory?.CreateLogger<KafkaErrorHandler>();
            _errorHandler = new KafkaErrorHandler(errorHandlerLogger);
        }

        public ProducerConfig GetProducerConfig(string bootstrapServers)
        {
            _producerConfig = new ProducerConfig()
            {
                BootstrapServers = bootstrapServers,
                ClientId = Dns.GetHostName(),
                //Partitioner = Partitioner.Murmur2Random
            };
            return _producerConfig;
        }

        public ConsumerConfig GetConsumerConfig(string bootstrapServers, string groupId = "Group1")
        {
            _consumerConfig = new ConsumerConfig
            {
                GroupId = groupId,
                BootstrapServers = bootstrapServers,
                AutoOffsetReset = AutoOffsetReset.Latest,
                //EnableAutoCommit = false,
                EnableAutoCommit = true, // (the default)
                EnableAutoOffsetStore = false,
            };
            return _consumerConfig;
        }

        public async Task PublishAsync<TMessage>(
            string topicName,
            MessageKey key,
            TMessage message,
            CancellationToken cancellationToken
        )
            where TMessage : class
        {
            if (_producerConfig == null)
                throw new ArgumentNullException("ProducerConfig is Required");
            try
            {
                using var producer = new ProducerBuilder<MessageKey, TMessage>(_producerConfig)
                    .SetKeySerializer(new JsonMessageKeyConverter(JsonSerializerOptions.Default))
                    .Build();
                await producer.ProduceAsync(
                    topicName,
                    new Message<MessageKey, TMessage>
                    {
                        Key = key,
                        Value = message,
                        //Value = JsonSerializer.Serialize(message)
                    },
                    cancellationToken
                );
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw e;
            }
        }

        public async Task SubscribeAsync<TMessage>(
            IEnumerable<string> topics,
            Action<TMessage> messageFunc,
            CancellationToken cancellationToken
        )
            where TMessage : class
        {
            if (_consumerConfig == null)
                throw new ArgumentNullException("ServerOptions is Required");

            // 使用消费者管理器订阅消息
            await _consumerManager.SubscribeAsync(
                _consumerConfig,
                topics,
                messageFunc,
                cancellationToken
            );
        }

        /// <summary>
        /// 批量订阅消息
        /// </summary>
        /// <typeparam name="TMessage">消息类型</typeparam>
        /// <param name="topics">主题列表</param>
        /// <param name="batchAction">批量处理函数</param>
        /// <param name="batchSize">批量大小</param>
        /// <param name="maxBatchTimeMilliseconds">最大批处理时间（毫秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task SubscribeBatchAsync<TMessage>(
            IEnumerable<string> topics,
            Action<IReadOnlyList<TMessage>> batchAction,
            int batchSize = 100,
            int maxBatchTimeMilliseconds = 1000,
            CancellationToken cancellationToken = default
        )
            where TMessage : class
        {
            if (_consumerConfig == null)
                throw new ArgumentNullException("ServerOptions is Required");
            if (topics == null)
                throw new ArgumentNullException(nameof(topics));
            if (batchAction == null)
                throw new ArgumentNullException(nameof(batchAction));

            // 创建批量消息处理器
            var batchProcessorLogger = _loggerFactory?.CreateLogger<
                KafkaBatchMessageProcessor<TMessage>
            >();
            var batchProcessor = new KafkaBatchMessageProcessor<TMessage>(
                batchProcessorLogger,
                batchSize,
                maxBatchTimeMilliseconds
            );

            // 创建消费者
            using var consumer = _consumerFactory.CreateStringConsumer(_consumerConfig);

            // 订阅主题
            consumer.Subscribe(topics);

            try
            {
                // 批量处理消息
                await batchProcessor.ProcessBatchAsync(consumer, batchAction, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                var logger = _loggerFactory?.CreateLogger<KafkaService>();
                logger?.LogInformation("Closing batch consumer.");
                Console.WriteLine("Closing batch consumer.");
                consumer.Close();
            }
        }
    }
}
