using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_auth_form", DisableSyncStructure = true)]
	public partial class SysAuthForm {

		[ Column(Name = "form_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FormId { get; set; }

		[ Column(Name = "config_type", StringLength = 50)]
		public string ConfigType { get; set; }

		[ Column(Name = "form_name_en_us", StringLength = 100)]
		public string FormNameEnUs { get; set; }

		[ Column(Name = "form_name_ja_jp", StringLength = 100)]
		public string FormNameJaJp { get; set; }

		[ Column(Name = "form_name_zh_cn", StringLength = 100)]
		public string FormNameZhCn { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "sql_expand", StringLength = 2000)]
		public string SqlExpand { get; set; }

	}

}
