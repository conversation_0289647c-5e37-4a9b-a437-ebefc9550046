using System.Text.Json;
using iPlatformExtension.Common.Converters.Abstraction;
using StackExchange.Redis;

namespace iPlatformExtension.Common.Converters;

public sealed class JsonRedisValueConverter(JsonSerializerOptions serializerOptions) : IRedisValueConverter
{
    public JsonRedisValueConverter() : this(JsonSerializerOptions.Default)
    {
        
    }

    public RedisValue ConvertToRedisValue(object? obj)
    {
        return JsonSerializer.SerializeToUtf8Bytes(obj, serializerOptions);
    }

    public object? ConvertFromRedisValue(RedisValue redisValue, Type objectType)
    {
        return JsonSerializer.Deserialize(new ReadOnlySpan<byte>(redisValue), objectType, serializerOptions);
    }

    bool IConverter.CanConvert(Type type)
    {
        return IRedisValueConverter.CanConvert(type) || serializerOptions.Converters.Any(converter => converter.CanConvert(type));
    }
}