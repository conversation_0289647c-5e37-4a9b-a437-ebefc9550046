﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;

/// <summary>
/// 添加团队命令
/// </summary>
/// <param name="TeamName">团队名称</param>
/// <param name="TeamDescription">团队说明</param>
/// <param name="IsExclusive">是否专属</param>
/// <param name="IsEffect">是否生效</param>
/// <param name="TeamId">团队id</param>
/// <param name="Members">团队成员</param>
/// <param name="CustomerId">客户id</param>
/// <param name="Seq">排序</param>
/// <param name="AuthorizeUser">授权用户</param>

public record InsertTeamCommand(string TeamName, string? TeamDescription, bool IsExclusive, bool IsEffect,
    string? TeamId, IEnumerable<MemberInput>? Members,string? AuthorizeUser,
    List<string>? CustomerId,int? Seq) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;
