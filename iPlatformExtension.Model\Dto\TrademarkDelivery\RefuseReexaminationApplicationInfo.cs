﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 驳回复审申请人信息
/// </summary>
public class RefuseReexaminationApplicationInfo : DeliveryApplicantInfo
{
    /// <summary>
    /// 申请人变更名称
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ChangeName { get; set; }

    /// <summary>
    /// 是否变更名义: 6 不变更，7申请人名义，8共有商标代理人，9其他
    /// </summary>
    [JsonPropertyName("isChangeName")]
    public string IsChangeName { get; set; } = default!;

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    [JsonPropertyName("contactEmail")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ContactEmail { get; set; }

    /// <summary>
    /// 联系人名称
    /// </summary>
    [JsonPropertyName("contactName")]
    public string ContactName { get; set; } = default!;

    /// <summary>
    /// 联系电话
    /// </summary>
    [JsonPropertyName("contactTel")]
    public string ContactTel { get; set; } = default!;
}