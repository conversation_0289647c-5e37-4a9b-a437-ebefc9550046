using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_payment_setting", DisableSyncStructure = true)]
	public partial class BasPaymentSetting {

		[ Column(Name = "balance_way", StringLength = 50)]
		public string BalanceWay { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "charge_rule", StringLength = 50)]
		public string ChargeRule { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		[ Column(Name = "is_default")]
		public bool IsDefault { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_officer")]
		public bool? IsOfficer { get; set; } = true;

		[ Column(Name = "is_request")]
		public bool? IsRequest { get; set; } = true;

		[ Column(Name = "pay_way", StringLength = 50)]
		public string PayWay { get; set; }

		[ Column(Name = "payment_name", StringLength = 50)]
		public string PaymentName { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "setting_id", StringLength = 50, IsNullable = false)]
		public string SettingId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
