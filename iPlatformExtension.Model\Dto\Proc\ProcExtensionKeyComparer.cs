﻿namespace iPlatformExtension.Model.Dto.Proc;

/// <summary>
/// <see cref="ProcExtensionKey"/>比较器
/// </summary>
public sealed class ProcExtensionKeyComparer : IComparer<ProcExtensionKey>
{
    /// <summary>
    /// 默认比较器
    /// </summary>
    public static readonly ProcExtensionKeyComparer Default = new();
    
    private ProcExtensionKeyComparer()
    {
    }

    /// <summary>
    /// 比较两个<see cref="ProcExtensionKey"/>。
    /// 优先根据<see cref="ProcExtensionKey.Order"/>比较。
    /// 如果相等再根据<see cref="ProcExtensionKey.Key"/>比较。
    /// </summary>
    /// <param name="x"></param>
    /// <param name="y"></param>
    /// <returns></returns>
    public int Compare(ProcExtensionKey x, ProcExtensionKey y)
    {
        var result = x.Order.CompareTo(y.Order);
        if (result == 0)
        {
            result = StringComparer.CurrentCulture.Compare(x.Key, y.Key);
        }

        return result;
    }
}