﻿using FreeSql;

namespace iPlatformExtension.Common.Db.Log;

internal class EntityChangeLogEntry
{
    /// <summary>
    /// 实体更改信息
    /// </summary>
    public List<DbContext.EntityChangeReport.ChangeInfo> Reports { get; set; } = default!;

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime ExecuteTime { get; set; }

    /// <summary>
    /// 操作人id。
    /// 没有给空字符串。
    /// </summary>
    public string Operator { get; set; } = string.Empty;

    /// <summary>
    /// 追踪id
    /// </summary>
    public string TraceId { get; set; } = default!;
}