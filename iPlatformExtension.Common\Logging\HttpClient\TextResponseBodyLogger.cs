using System.Net.Mime;
using iPlatformExtension.Common.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;

namespace iPlatformExtension.Common.Logging.HttpClient;

public class TextResponseBodyLogger(ILogger<TextResponseBodyLogger> logger) : IHttpClientOnlyAsyncLogger
{
    public ValueTask<object?> LogRequestStartAsync(HttpRequestMessage request,
        CancellationToken cancellationToken = new CancellationToken())
    {
        return new ValueTask<object?>(null!);
    }

    public async ValueTask LogRequestStopAsync(object? context, HttpRequestMessage request, HttpResponseMessage response,
        TimeSpan elapsed, CancellationToken cancellationToken = new CancellationToken())
    {
        var responseHeaders = response.Headers;
        if (responseHeaders.TryGetValues(HeaderNames.ContentType, out var contentTypes)
            && (contentTypes.Any(MediaTypeNames.Application.Json.Equals)
            || contentTypes.Any(MediaTypeNames.Application.ProblemJson.Equals)
            || contentTypes.Any(MediaTypeNames.Application.Xml.Equals)
            || contentTypes.Any(MediaTypeNames.Application.ProblemXml.Equals)))
        {
            var content = response.Content;
            var responseBody = await content.ReadAsStringAsync(cancellationToken);
            logger.LogHttpTextResponseBody(responseBody);
        }
    }

    public ValueTask LogRequestFailedAsync(object? context, HttpRequestMessage request, HttpResponseMessage? response,
        Exception exception, TimeSpan elapsed, CancellationToken cancellationToken = new CancellationToken())
    {
        return ValueTask.CompletedTask;
    }
}