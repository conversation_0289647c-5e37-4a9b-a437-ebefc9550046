using System.Reflection;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;
using Confluent.Kafka;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Db;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Filters;
using iPlatformExtension.Common.Mediator.PipelineBehaviors;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkDelivery.Consumers;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Behaviors;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Extensions;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Nacos.AspNetCore.V2;
using NLog.Web;

[assembly:ApplicationPart("iPlatformExtension.Common")]

var builder = WebApplication.CreateBuilder(args);

var configuration = builder.Configuration;
var environment = builder.Environment;
var logging = builder.Logging;

logging.ClearProviders();
logging.AddSimpleConsole(options => options.IncludeScopes = true);
logging.AddNLogWeb(new NLogAspNetCoreOptions
{
    RemoveLoggerFactoryFilter = false,
    LoggingConfigurationSectionName = "NLog"
});

if (!environment.IsLocal())
{
     builder.Services.AddNacosAspNet(configuration);
     builder.Services.AddNacosServiceDiscovery(options => options.AllowedSchemes = [Uri.UriSchemeHttp]);
     configuration.AddNacosV2Configuration(configuration.GetSection("nacos"));
}
else
{
    configuration.AddJsonFile($"clients.{environment.EnvironmentName}.json", true, true);
    configuration.AddJsonFile($"mqs.{environment.EnvironmentName}.json", true, true);
}

// Add services to the container.

builder.Services.AddControllers(options =>
{
    options.Filters.Add<ActionExceptionFilter<ResultData>>();
    options.Filters.Add<ActionResultFilter<ResultData>>();
}).AddJsonOptions(options =>
{
    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
    options.JsonSerializerOptions.Converters.Add(new DateTimeJsonConverter());
    options.JsonSerializerOptions.Converters.Add(new NullableDateTimeJsonConverter());
    if (environment.IsLocal())
    {
        options.JsonSerializerOptions.WriteIndented = true;
    }
});
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddObjectPools();
builder.Services.AddValidation();
builder.Services.AddPhoenixClient("AcipKey1");
builder.Services.AddPhoenixClient("AcipKey2");
builder.Services.AddPhoenixClient("BeijingKey");
builder.Services.AddPhoenixClient();

builder.Services.AddMediatRServices().Add(ApplicationDependencyInjectionExtension.AddApplicationServices).Build();
builder.Services.AddDataService();
builder.Services.AddCache().ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
{
    options.ConnectionString = builder.Configuration.GetConnectionString("Redis");
    options.Converters.Add(new ClaimsIdentityConverter());
});

builder.Services.AddKafKaConsumers<DeliveryMessage>().ConfigureServerOptions(serverOptions =>
{
    serverOptions.GroupId = $"{environment.ApplicationName}-{environment.EnvironmentName}-{nameof(DeliveryMessage)}";
    serverOptions.AutoOffsetReset = AutoOffsetReset.Latest;
    serverOptions.EnableAutoCommit = false;
}, "KafKa:Consumer:ServerOptions").ConfigureConsumerOptions((consumerOptions, _) =>
{
    var consumerBuilder = consumerOptions.ConsumerBuilder!;
    consumerBuilder.SetKeyDeserializer(new JsonMessageKeyConverter(new JsonSerializerOptions(JsonSerializerDefaults.Web)));
    consumerBuilder.SetValueDeserializer(
        new JsonMessageValueConverter<DeliveryMessage>(new JsonSerializerOptions(JsonSerializerDefaults.Web)));

}, "KafKa:Consumer")
    .AddConsumer<TrademarkRegistrationDeliveryConsumer>()
    .AddConsumer<RefuseReexaminationDeliveryConsumer>()
    .AddConsumer<NominalChangeDeliveryConsumer>()
    .AddConsumer<TrademarkTransferDeliveryConsumer>()
    .AddConsumer<TrademarkRenewalDeliveryConsumer>()
    .AddConsumer<TrademarkAnnulmentDeliveryConsumer>()
    .AddConsumer<TrademarkWithdraw3YearsDeliveryConsumer>()
    .AddConsumer<TrademarkObjectionsDeliveryConsumer>()
    .AddConsumer<TrademarkLicenseFilingDeliveryConsumer>()
    .Build();

builder.Services.AddPlatformFreeSql(configuration.GetConnectionString("Default") ?? throw new ArgumentNullException())
    .ConfigureGlobalCurdLogging("Logging:FreeSql:Curd");

if (environment.IsProduction())
{
    builder.Services.AddMongoDbContext<PlatformMongoDbContext>(configuration.GetConnectionString("Log")
                                                     ?? throw new ArgumentNullException($"ConnectionStrings:Log", "缺少数据库连接字符串"));
    builder.Services.AddEntityChangeLogs();
}

builder.Services.AddMediatRServices().Add(servicesConfiguration =>
{
    servicesConfiguration.AddOpenBehavior(typeof(DeliveryVersionValidBehavior<,>));
    servicesConfiguration.AddOpenBehavior(typeof(DefaultFreeSqlUnitOfWorkPipelineBehavior<,>));
    servicesConfiguration.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
}).Build();

var app = builder.Build();

app.UseW3CTraceResponse();

// Configure the HTTP request pipeline.
if (!app.Environment.IsProduction())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.HandleUnsuccessfulResponse();

app.UseAuthorization();

app.MapControllers();

app.Run();