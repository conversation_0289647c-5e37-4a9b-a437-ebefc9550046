using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_inventor", DisableSyncStructure = true)]
	public partial class BasInventor {

		[ Column(Name = "inventor_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string InventorId { get; set; }

		[ Column(Name = "address_cn", StringLength = 200)]
		public string AddressCn { get; set; }

		[ Column(Name = "address_en", StringLength = 500)]
		public string AddressEn { get; set; }

		[ Column(Name = "card_no", StringLength = 50)]
		public string CardNo { get; set; }

		[ Column(Name = "card_type", StringLength = 50)]
		public string CardType { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "email", StringLength = 50)]
		public string Email { get; set; }

		[ Column(Name = "entry_date")]
		public DateTime? EntryDate { get; set; }

		[ Column(Name = "inventor_name_cn", StringLength = 200)]
		public string InventorNameCn { get; set; }

		[ Column(Name = "inventor_name_en", StringLength = 200)]
		public string InventorNameEn { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_unpub")]
		public bool IsUnpub { get; set; } = false;

		[ Column(Name = "mobile", StringLength = 50)]
		public string Mobile { get; set; }

		[ Column(Name = "qq", StringLength = 50)]
		public string Qq { get; set; }

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		[ Column(Name = "tel", StringLength = 50)]
		public string Tel { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "work_no", StringLength = 50)]
		public string WorkNo { get; set; }

	}

}
