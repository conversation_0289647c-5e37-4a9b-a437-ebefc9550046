﻿using FluentValidation;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Infrastructure.Validators;

internal class FeeUpdateInfoDtoValidator : AbstractValidator<FeeUpdateInfoDto>
{
    public FeeUpdateInfoDtoValidator()
    {

        RuleFor(dto => dto.ReceiveDate).Must((dto, receiveTime) =>
        {
            if (dto.Operation == FinanceOperation.Allocate)
            {
                return receiveTime is not null;
            }

            return true;
        }).WithMessage("划拨缺少到款时间");

        RuleFor(dto => dto.RequestDate).Must((dto, requestTime) =>
        {
            if (dto.Operation == FinanceOperation.RequestOrConfirmBill)
            {
                return requestTime is not null;
            }

            return true;
        }).WithMessage("确认请款操作缺少请款时间");
        
        RuleFor(dto => dto.ReceiveDueDate).Must((dto, receiveDueDate) =>
        {
            if (dto.Operation == FinanceOperation.RequestOrConfirmBill)
            {
                return receiveDueDate is not null;
            }

            return true;
        }).WithMessage("确认请款操作缺少应收款日期时间");

        RuleFor(dto => dto.InvoiceNo).Must((dto, invoiceNo) =>
        {
            if (dto.Operation == FinanceOperation.Invoicing)
            {
                return !string.IsNullOrWhiteSpace(invoiceNo);
            }

            return true;
        }).WithMessage("保存发票操作缺少发票号码");

        RuleFor(dto => dto.CustomerId).Must((dto, customerId) =>
        {
            if (dto.Operation == FinanceOperation.Allocate)
            {
                return !string.IsNullOrWhiteSpace(customerId);
            }

            return true;
        }).WithMessage("划拨操作缺少客户数据");
    }
}