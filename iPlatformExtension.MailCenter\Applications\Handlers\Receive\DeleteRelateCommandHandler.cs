﻿using iPlatformExtension.MailCenter.Applications.Commands.Receive;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 删除关联处理者
    /// </summary>
    internal sealed class DeleteRelateCommandHandler(IMailCorrelativeRepository mailCorrelativeRepository) : IRequestHandler<DeleteRelateCommand>
    {
        public async Task Handle(DeleteRelateCommand request, CancellationToken cancellationToken)
        {
            if (request.Id is not null && request.Id.Count > 0)
            {
                await mailCorrelativeRepository.DeleteAsync(it => request.Id.Contains(it.Id), cancellationToken);
            }
        }
    }
}

