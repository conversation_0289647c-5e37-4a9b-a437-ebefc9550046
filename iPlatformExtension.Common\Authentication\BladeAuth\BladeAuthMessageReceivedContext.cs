﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.Common.Authentication.BladeAuth;

public sealed class BladeAuthMessageReceivedContext(
    HttpContext context,
    AuthenticationScheme scheme,
    BladeAuthOptions options)
    : ResultContext<BladeAuthOptions>(context, scheme, options)
{
    public string? Token { get; set; }

    public string? TokenName { get; set; }

    public string? TokenPath { get; set; }
}