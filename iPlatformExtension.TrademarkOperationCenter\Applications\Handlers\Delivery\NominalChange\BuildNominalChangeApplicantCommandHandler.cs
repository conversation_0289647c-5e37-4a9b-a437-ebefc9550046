﻿using AutoMapper;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.NominalChange;

internal sealed class BuildNominalChangeApplicantCommandHandler(IMapper mapper) : 
    BuildProcApplicantCommandHandler(mapper)
{
    public override string CtrlProcId => CtrlProcIds.NominalChange;

    public override IEnumerable<string> CaseDirections => [CaseDirection.II];
}