using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_design_desc", DisableSyncStructure = true)]
	public partial class DeliDesignDesc {

		[ Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ProcId { get; set; }

		[ Column(Name = "desc_2", StringLength = 500)]
		public string Desc2 { get; set; }

		[ Column(Name = "desc_3", StringLength = 4000)]
		public string Desc3 { get; set; }

		[ Column(Name = "desc_3_0", StringLength = 50)]
		public string Desc30 { get; set; }

		[ Column(Name = "desc_3_1", StringLength = 500)]
		public string Desc31 { get; set; }

		[ Column(Name = "desc_3_2", StringLength = 50)]
		public string Desc32 { get; set; }

		[ Column(Name = "desc_3_3")]
		public bool? Desc33 { get; set; } = false;

		[ Column(Name = "desc_4", StringLength = 4000)]
		public string Desc4 { get; set; }

		[ Column(Name = "desc_5", StringLength = 4000)]
		public string Desc5 { get; set; }

		[ Column(Name = "desc_5_0", StringLength = 500)]
		public string Desc50 { get; set; }

		[ Column(Name = "desc_5_1", StringLength = 50)]
		public string Desc51 { get; set; }

		[ Column(Name = "desc_5_2")]
		public bool? Desc52 { get; set; } = false;

		[ Column(Name = "desc_5_3", StringLength = 50)]
		public string Desc53 { get; set; }

		[ Column(Name = "desc_5_4", StringLength = 50)]
		public string Desc54 { get; set; }

		[ Column(Name = "desc_5_5", StringLength = 50)]
		public string Desc55 { get; set; }

		[ Column(Name = "desc_5_6", StringLength = 50)]
		public string Desc56 { get; set; }

		[ Column(Name = "desc_6", StringLength = 50)]
		public string Desc6 { get; set; }

		[ Column(Name = "desc_6_0", StringLength = 4000)]
		public string Desc60 { get; set; }

		[ Column(Name = "desc_6_1", StringLength = 4000)]
		public string Desc61 { get; set; }

		[ Column(Name = "desc_6_2", StringLength = 4000)]
		public string Desc62 { get; set; }

		[ Column(Name = "desc_6_3", StringLength = 4000)]
		public string Desc63 { get; set; }

		[ Column(Name = "desc_7", StringLength = 200)]
		public string Desc7 { get; set; }

		[ Column(Name = "desc_7_0", StringLength = 4000)]
		public string Desc70 { get; set; }

		[ Column(Name = "desc_7_1", StringLength = 4000)]
		public string Desc71 { get; set; }

		[ Column(Name = "desc_7_2", StringLength = 4000)]
		public string Desc72 { get; set; }

		[ Column(Name = "desc_7_3", StringLength = 4000)]
		public string Desc73 { get; set; }

		[ Column(Name = "desc_7_4", StringLength = 4000)]
		public string Desc74 { get; set; }

		[ Column(Name = "desc_8", StringLength = 4000)]
		public string Desc8 { get; set; }

	}

}
