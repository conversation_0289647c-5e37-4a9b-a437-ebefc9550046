using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_form_initdata", DisableSyncStructure = true)]
	public partial class SysFormInitdata {

		[ Column(Name = "form_data_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FormDataId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "form_data_code", StringLength = 50)]
		public string FormDataCode { get; set; }

		[ Column(Name = "sql", StringLength = 4000)]
		public string Sql { get; set; }

	}

}
