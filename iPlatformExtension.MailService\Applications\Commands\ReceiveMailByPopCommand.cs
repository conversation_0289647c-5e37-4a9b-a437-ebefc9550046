using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.MailService.Applications.Models;
using MediatR;

namespace iPlatformExtension.MailService.Applications.Commands
{
    /// <summary>
    ///  pop3收件
    /// </summary>
    /// <param name="ConfigDto"></param>
    /// <param name="repeatLimit">重复收件限制</param>
    public record ReceiveMailByPopCommand(MailConfigDto ConfigDto, int repeatLimit = 100) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;
}
