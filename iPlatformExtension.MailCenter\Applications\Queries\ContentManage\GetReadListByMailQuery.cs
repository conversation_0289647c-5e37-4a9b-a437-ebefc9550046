﻿using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.ContentManage
{
    public class GetReadListByMailQuery : QueryBase, IRequest<PageResult<GetReadListByMailDto>>
    {
        /// <summary>
        /// 邮件ID
        /// </summary>
        public string MailId { get; set; }


    }
}
