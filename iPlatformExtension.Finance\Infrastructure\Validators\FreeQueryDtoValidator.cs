﻿using FluentValidation;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Infrastructure.Validators;

internal class FreeQueryDtoValidator : AbstractValidator<FeeQueryDto>
{
    
    public FreeQueryDtoValidator(IValidator<PeriodQueryDto> periodValidator)
    {
        ClassLevelCascadeMode = CascadeMode.Stop;
        RuleFor(dto => dto.PayOfficerLegalDatePeriod).SetValidator(periodValidator);
        RuleFor(dto => dto.CreationTimePeriod).SetValidator(periodValidator);
        RuleFor(dto => dto.ApplyTimePeriod).SetValidator(periodValidator);
        RuleFor(dto => dto.CompletionTimePeriod).SetValidator(periodValidator);
        RuleFor(dto => dto.EntrustTimePeriod).SetValidator(periodValidator);
        RuleFor(dto => dto.PayOfficerDatePeriod).SetValidator(periodValidator);
        RuleFor(dto => dto.RequestTimePeriod).SetValidator(periodValidator);
        RuleFor(dto => dto.SendOfficialDatePeriod).SetValidator(periodValidator);
        RuleFor(dto => dto.ReceiveDatePeriod).SetValidator(periodValidator);
        RuleFor(dto => dto.Page).GreaterThan(0);
        RuleFor(dto => dto.PageSize).GreaterThan(0).LessThanOrEqualTo(1000);
        RuleFor(dto => dto.ApplyTypes).Must((dto, applyTypes) =>
        {
            var caseTypes = !dto.CaseTypes.Any() ? CaseType.AllTypes : dto.CaseTypes;
            return !applyTypes.Any() || caseTypes.All(caseType => caseType == CaseType.Patent);
        }).WithMessage("只有专利类型的案件才可以使用申请类型筛选");
    }
}