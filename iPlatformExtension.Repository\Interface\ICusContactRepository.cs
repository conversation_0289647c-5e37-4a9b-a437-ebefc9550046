﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface ICusContactRepository : 
    IBaseRepository<CusContact, string>,
    IScopeDependency,
    IRedisCacheableRepository<string, CusContact>
{
    Task<CusContact?> ICacheableRepository<string, CusContact>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Select.WithLock().Where(contact => contact.ContactId == key).FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<CusContact>> ICacheableRepository<string, CusContact>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().Take(100).ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, CusContact>.GenerateKey(CusContact value)
    {
        return value.ContactId;
    }
}