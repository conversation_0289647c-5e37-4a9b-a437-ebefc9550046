using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_message_user", DisableSyncStructure = true)]
	public partial class MailMessageUser {

		[ Column(Name = "address_type", StringLength = 50, IsNullable = false)]
		public string AddressType { get; set; }

		[ Column(Name = "display_name", StringLength = 500)]
		public string DisplayName { get; set; }

		[ Column(Name = "mail_address", StringLength = 200, IsNullable = false)]
		public string MailAddress { get; set; }

		[ Column(Name = "mail_id", StringLength = 50, IsNullable = false)]
		public string MailId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
