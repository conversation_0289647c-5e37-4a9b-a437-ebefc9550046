using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using System.Text.Json.Serialization;
using FreeSql.DataAnnotations;
using iPlatformExtension.Model.Attributes;
using iPlatformExtension.Model.Constants;

namespace iPlatformExtension.Model.BaseModel
{
    /// <summary>
    /// 案件任务信息
    /// </summary>
    [Table(Name = "case_proc_info", DisableSyncStructure = true)]
    public class CaseProcInfo : IVersionEntity, ISystemLoggable<CaseProcInfo>
    {

        /// <summary>
        /// 主键ID
        /// </summary>
        [Display(Name = "任务ID", Order = 0)]
        [Hide]
        [Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string ProcId { get; set; } = Guid.NewGuid().ToString().ToUpper();

        /// <summary>
        /// 分案日
        /// </summary>
        [Column(Name = "allocate_date")]
        public DateTime? AllocateDate { get; set; }

        [Column(Name = "audit_result", StringLength = 500)]
        public string AuditResult { get; set; }

        /// <summary>
        /// 返发明人日
        /// </summary>
        [Column(Name = "back_inventor_date")]
        public DateTime? BackInventorDate { get; set; }

        /// <summary>
        /// 返接口人日
        /// </summary>
        [Column(Name = "back_ipr_date")]
        public DateTime? BackIprDate { get; set; }

        [Column(Name = "bus_type_id", StringLength = 50)]
        public string BusTypeId { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        [Navigate(nameof(BusTypeId))]
        public virtual BasBusinessType? BusinessType { get; set; }

        [Column(Name = "cancellation_reason", StringLength = 2000)]
        public string CancellationReason { get; set; }

        /// <summary>
        /// 主承办人
        /// 商标用
        /// </summary>
        [Column(Name = "proc_undertake_main_user_id")]
        public string? ProcUndertakeMainUserId { get; set; }

        /// <summary>
        /// 案件ID
        /// </summary>
        [Column(Name = "case_id", StringLength = 50, IsNullable = false)]
        public string CaseId { get; set; }

        /// <summary>
        /// 案件表导航属性
        /// </summary>
        [Navigate(nameof(CaseId))]
        public virtual CaseInfo CaseInfo { get; set; } = default!;

        [Column(Name = "create_time")]
        public DateTime? CreateTime { get; set; }

        [Column(Name = "create_user_id", StringLength = 50)]
        public string CreateUserId { get; set; }

        /// <summary>
        /// 事项名称ID
        /// </summary>
        [Column(Name = "ctrl_proc_id", StringLength = 200)]
        public string CtrlProcId { get; set; }

        [Column(Name = "ctrl_proc_mark", StringLength = 50)]
        public string? CtrlProcMark { get; set; }

        /// <summary>
        /// 任务属性
        /// </summary>
        [Column(Name = "ctrl_proc_property", StringLength = 50)]
        public string CtrlProcProperty { get; set; }

        /// <summary>
        /// 客户期限
        /// </summary>
        [Column(Name = "cus_due_date")]
        public DateTime? CusDueDate { get; set; }

        /// <summary>
        /// 定稿期限(外)
        /// </summary>
        [Column(Name = "cus_finish_date")]
        public DateTime? CusFinishDate { get; set; }

        /// <summary>
        /// 初稿期限(外)
        /// </summary>
        [Column(Name = "cus_first_date")]
        public DateTime? CusFirstDate { get; set; }

        /// <summary>
        /// 客户要求返发明人期限
        /// </summary>
        [Column(Name = "cus_inventor_date")]
        public DateTime? CusInventorDate { get; set; }

        /// <summary>
        /// 客户要求返接口人期限
        /// </summary>
        [Column(Name = "cus_ipr_date")]
        public DateTime? CusIprDate { get; set; }

        [Column(Name = "customer_case_no", StringLength = 50)]
        public string CustomerCaseNo { get; set; }

        /// <summary>
        /// 客户文号(商标)
        /// </summary>
        [Column(Name = "customer_case_no_t", StringLength = 50)]
        public string CustomerCaseNoT { get; set; }

        /// <summary>
        /// 委案日期
        /// </summary>
        [Column(Name = "entrust_date")]
        public DateTime? EntrustDate { get; set; }

        /// <summary>
        /// 提成比例
        /// </summary>
        [Column(Name = "examine_percentage", DbType = "money")]
        public decimal? ExaminePercentage { get; set; }

        [Column(Name = "examiner", StringLength = 50)]
        public string Examiner { get; set; }

        [Column(Name = "examiner_dept", StringLength = 500)]
        public string ExaminerDept { get; set; }

        [Column(Name = "examiner_tel", StringLength = 50)]
        public string ExaminerTel { get; set; }

        [Column(Name = "file_desc_id", StringLength = 50)]
        public string FileDescId { get; set; }

        /// <summary>
        /// 递交方式
        /// </summary>
        [Column(Name = "filing_type", StringLength = 50)]
        public string? FilingType { get; set; }

        /// <summary>
        /// 完成日
        /// </summary>
        [Column(Name = "finish_date")]
        public DateTime? FinishDate { get; set; }

        /// <summary>
        /// 定稿日
        /// </summary>
        [Column(Name = "finish_doc_date")]
        public DateTime? FinishDocDate { get; set; }

        /// <summary>
        /// 初稿日
        /// </summary>
        [Column(Name = "first_doc_date")]
        public DateTime? FirstDocDate { get; set; }

        /// <summary>
        /// 核稿人
        /// </summary>
        [Column(Name = "first_examine_user_id", StringLength = 50)]
        public string FirstExamineUserId { get; set; }

        /// <summary>
        /// 境外代理(商标)
        /// </summary>
        [Column(Name = "foregin_agency_id", StringLength = 50)]
        public string? ForeginAgencyId { get; set; }

        /// <summary>
        /// 内部期限
        /// </summary>
        [Column(Name = "int_due_date")]
        public DateTime? IntDueDate { get; set; }

        /// <summary>
        /// 内部定稿日/定稿期限(内)
        /// </summary>
        [Column(Name = "int_finish_date")]
        public DateTime? IntFinishDate { get; set; }

        /// <summary>
        /// 初稿期限(内)
        /// </summary>
        [Column(Name = "int_first_date")]
        public DateTime? IntFirstDate { get; set; }

        [Column(Name = "invalidate_code", StringLength = 50)]
        public string InvalidateCode { get; set; }

        [Column(Name = "invalidate_holder_name", StringLength = 200)]
        public string InvalidateHolderName { get; set; }

        [Column(Name = "invalidate_request_user", StringLength = 200)]
        public string InvalidateRequestUser { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        [Column(Name = "is_enabled")]
        public bool? IsEnabled { get; set; } = true;

        [Column(Name = "is_insteadofsubmitting")]
        public bool? IsInsteadofsubmitting { get; set; }

        [Column(Name = "is_substance", StringLength = 50)]
        public string IsSubstance { get; set; }

        /// <summary>
        /// 法定期限(官方期限)
        /// </summary>
        [Column(Name = "legal_due_date")]
        public DateTime? LegalDueDate { get; set; }

        [Column(Name = "legal_provisions", StringLength = 50)]
        public string LegalProvisions { get; set; }

        [Column(Name = "n_CameFileID", StringLength = 50)]
        public string NCameFileID { get; set; }

        [Column(Name = "n_StatusID", StringLength = 50)]
        public string NStatusID { get; set; }

        [Column(Name = "notice_name", StringLength = 500)]
        public string NoticeName { get; set; }

        [Column(Name = "objection", StringLength = 50)]
        public string Objection { get; set; }

        [Column(Name = "objection_id", StringLength = 1000)]
        public string ObjectionId { get; set; }

        [Column(Name = "objection_name", StringLength = 2000)]
        public string ObjectionName { get; set; }

        /// <summary>
        /// 官方信息
        /// </summary>
        [Column(Name = "official_note", StringLength = 4000)]
        public string OfficialNote { get; set; }

        [Column(Name = "opponents", StringLength = 50)]
        public string Opponents { get; set; }

        [Column(Name = "out_user", StringLength = 50)]
        public string OutUser { get; set; }

        /// <summary>
        /// 父级任务id
        /// </summary>
        [Column(Name = "parent_proc_id", StringLength = 50)]
        public string ParentProcId { get; set; }

        [Column(Name = "postmark_date")]
        public DateTime? PostmarkDate { get; set; }

        [Column(Name = "private_id", StringLength = 50)]
        public string PrivateId { get; set; }

        [Column(Name = "proc_app_date")]
        public DateTime? ProcAppDate { get; set; }

        [Column(Name = "proc_app_no", StringLength = 50)]
        public string ProcAppNo { get; set; }

        [Column(Name = "proc_dept_id", StringLength = 50)]
        public string ProcDeptId { get; set; }

        /// <summary>
        /// 任务编号
        /// </summary>
        [Column(Name = "proc_no", StringLength = 50)]
        public string ProcNo { get; set; }

        /// <summary>
        /// 事项备注
        /// </summary>
        [Column(Name = "proc_note", StringLength = 4000)]
        public string ProcNote { get; set; }

        /// <summary>
        /// 事项状态ID
        /// </summary>
        [Column(Name = "proc_status_id", StringLength = 50)]
        public string ProcStatusId { get; set; }

        [Column(Name = "proof_amount", StringLength = 50)]
        public string ProofAmount { get; set; }

        [Column(Name = "reason_remark", StringLength = 2000)]
        public string ReasonRemark { get; set; }

        /// <summary>
        /// 官方来文日期
        /// </summary>
        [Column(Name = "receive_date")]
        public DateTime? ReceiveDate { get; set; }

        [Column(Name = "receive_no", StringLength = 50)]
        public string ReceiveNo { get; set; }

        [Column(Name = "requestor", StringLength = 2000)]
        public string Requestor { get; set; }

        [Column(Name = "result_receive_date")]
        public DateTime? ResultReceiveDate { get; set; }

        [Column(Name = "result_remark", StringLength = 2000)]
        public string ResultRemark { get; set; }

        /// <summary>
        /// 送官方日期
        /// </summary>
        [Column(Name = "send_official_date")]
        public DateTime? SendOfficialDate { get; set; }

        /// <summary>
        /// 送合作所日期
        /// </summary>
        [Column(Name = "send_partner_date")]
        public DateTime? SendPartnerDate { get; set; }

        /// <summary>
        /// 事项序号
        /// </summary>
        [Column(Name = "seq")]
        public short? Seq { get; set; }

        /// <summary>
        /// 用于显示的任务名称，一般后续不会做修改
        /// </summary>
        [Column(Name = "show_name", StringLength = 50)]
        public string ShowName { get; set; }

        [Column(Name = "simple_deliver_date")]
        public DateTime? SimpleDeliverDate { get; set; }

        [Column(Name = "sub_proc_status_id", StringLength = 50)]
        public string SubProcStatusId { get; set; }

        [Column(Name = "supplementary_deliver_date")]
        public DateTime? SupplementaryDeliverDate { get; set; }

        [Column(Name = "titular_write_user", StringLength = 50)]
        public string TitularWriteUser { get; set; }

        /// <summary>
        /// 经办人
        /// </summary>
        [Column(Name = "track_user_id", StringLength = 50)]
        public string TrackUserId { get; set; }

        [Column(Name = "translate_amount", StringLength = 50)]
        public string TranslateAmount { get; set; }

        [Column(Name = "translate_reader", StringLength = 50)]
        public string TranslateReader { get; set; }

        [Column(Name = "translate_type", StringLength = 50)]
        public string TranslateType { get; set; }

        [Column(Name = "translator", StringLength = 50)]
        public string Translator { get; set; }

        /// <summary>
        /// 承办人
        /// </summary>
        [Column(Name = "undertake_user_id", StringLength = 50)]
        public string? UndertakeUserId { get; set; }

        [Column(Name = "update_time")]
        public DateTime? UpdateTime { get; set; }

        [Column(Name = "update_user_id", StringLength = 50)]
        public string UpdateUserId { get; set; }

        /// <summary>
        /// 是否订单添加任务
        /// </summary>
        [Column(Name = "is_orderadd")]
        public bool? IsOrderAdd { get; set; } = false;

        /// <summary>
        /// 对应产品Id
        /// </summary>
        [Column(Name = "product_id", StringLength = 50)]
        public string ProductId { get; set; }

        /// <summary>
        /// 境内代理机构id
        /// </summary>
        [Column(Name = "trademark_delivery_agency_id")]
        [Display(Name = "代理名义", Order = 1)]
        [KeyValueDisplay(DisplayValueSourceType = typeof(BasAgency))]
        public string TrademarkDeliveryAgencyId { get; set; } = string.Empty;

        /// <summary>
        /// 递交联系地址
        /// </summary>
        [Column(Name = "trademark_delivery_contact_address_cn")]
        [Display(Name = "联系地址", Order = 3)]
        public string TrademarkDeliveryContactAddressCn { get; set; } = string.Empty;

        /// <summary>
        /// 商标递交联系人名称
        /// </summary>
        [Column(Name = "trademark_delivery_contactor")]
        [Display(Name = "国内商标联系人", Order = 5)]
        public string TrademarkDeliveryContactor { get; set; } = string.Empty;

        /// <summary>
        /// 商标递交联系人邮箱
        /// </summary>
        [Column(Name = "trademark_delivery_contact_mailbox")]
        [Display(Name = "国内商标联系邮箱", Order = 7)]
        public string TrademarkDeliveryContactMailbox { get; set; } = string.Empty;

        /// <summary>
        /// 商标递交联系人邮编
        /// </summary>
        [Column(Name = "trademark_delivery_contact_postcode")]
        [Display(Name = "联系地址邮编", Order = 4)]
        public string TrademarkDeliveryContactPostCode { get; set; } = string.Empty;

        /// <summary>
        /// 商标递交联系人联系电话
        /// </summary>
        [Column(Name = "trademark_delivery_contact_tel")]
        [Display(Name = "国内商标联系电话", Order = 6)]
        public string TrademarkDeliveryContactTel { get; set; } = string.Empty;

        /// <summary>
        /// 官方代理人
        /// </summary>
        [Column(Name = "agent_user")]
        [Display(Order = 2, Name = "官方代理人")]
        public string AgentUser { get; set; } = string.Empty;

        /// <summary>
        /// 要求递交日
        /// </summary>
        [Column(Name = "requirement_submit_date")]
        public DateTime? RequirementSubmitDate { get; set; }


        /// <summary>
        /// 返稿期限
        /// </summary>
        [Column(Name = "return_doc_date")]
        public DateTime? ReturnDocDate { get; set; }



        /// <summary>
        /// 递交key
        /// </summary>
        [Column(Name = "delivery_key")]
        [Display(Name = "国内网交用Key", Order = 8)]
        [KeyValueDisplay(IsSystemDictionary = true, DictionaryName = SystemDictionaryName.DeliveryKey)]
        public string DeliveryKey { get; set; } = string.Empty;

        /// <summary>
        /// 申请类别
        /// </summary>
        [Display(Name = "申请类别", Order = 9)]
        [Column(Name = "trademark_nice_class")]
        public string TrademarkNiceClasses { get; set; } = string.Empty;

        /// <summary>
        /// 是否拆分
        /// </summary>
        [Display(Name = "是否分割", Order = 10)]
        [Column(Name = "is_split")]
        public bool? IsSplit { get; set; }

        /// <summary>
        /// 名义变更类型
        /// </summary>
        [Display(Name = "名义变更类型", Order = 11)]
        [Column(Name = "nominal_changes_type")]
        [KeyValueDisplay(IsSystemDictionary = true, DictionaryName = SystemDictionaryName.NominalChangesType)]
        public string? NominalChangesType { get; set; }

        /// <summary>
        /// 法律条款
        /// </summary>
        [Column(Name = "law_provisions")]
        [Display(Name = "法律条款", Order = 12)]
        [KeyValueDisplay(IsSystemDictionary = true, DictionaryName = SystemDictionaryName.AnnulmentLawProvision)]
        public string LawProvisions { get; set; } = string.Empty;

        /// <summary>
        /// 是否涉及绝对理由
        /// </summary>
        [Column(Name = "has_absolute_reason")]
        [Display(Name = "是否涉及绝对理由", Order = 13)]
        public bool? HasAbsoluteReason { get; set; }

        /// <summary>
        /// 引证商标注册号
        /// </summary>
        [Column(Name = "cited_register_numbers")]
        [Display(Name = "引证商标注册号", Order = 14)]
        public string CitedRegisterNumbers { get; set; } = string.Empty;

        /// <summary>
        /// 同意地址延及后续
        /// </summary>
        [Column(Name = "extend_to_same_address")]
        [Display(Name = "同意地址延及后续", Order = 15)]
        public bool? ExtendToSameAddress { get; set; }

        /// <summary>
        /// 许可类型
        /// </summary>
        [Column(Name = "license_type")]
        [Display(Name = "许可类型", Order = 16)]
        [KeyValueDisplay(IsSystemDictionary = true, DictionaryName = SystemDictionaryName.LicenseType)]
        public string LicenseType { get; set; } = string.Empty;

        /// <summary>
        /// 合同生效日期
        /// </summary>
        [Column(Name = "contract_effective_date", MapType = typeof(DateTime?))]
        [Display(Name = "合同生效日期", Order = 17)]
        public DateOnly? ContractEffectiveDate { get; set; }

        /// <summary>
        /// 合同终止日期
        /// </summary>
        [Column(Name = "contract_termination_date", MapType = typeof(DateTime?))]
        [Display(Name = "合同终止日期", Order = 18)]
        public DateOnly? ContractTerminationDate { get; set; }

        /// <summary>
        /// 是否保留补充材料权利
        /// </summary>
        [Column(Name = "reservation_of_supplementary_material")]
        [Display(Name = "是否保留补充材料权利", Order = 19)]
        public bool? ReservationOfSupplementaryMaterial { get; set; }

        /// <summary>
        /// 交管名称
        /// </summary>
        [Column(Name = "official_name")]
        [Display(Name = "交官名称", Order = 20)]
        public string? OfficialName { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [Display(Name = "版本号", Order = int.MaxValue)]
        [Hide]
        [Column(Name = "version", IsVersion = true)]
        public int Version { get; set; }

        /// <summary>
        /// 团队ID
        /// </summary>
        [Column(Name = "team_id")]
        public string? TeamId { get; set; }

        /// <summary>
        /// 指示外所期限
        /// </summary>
        [Column(Name = "duration_outpost")]
        public DateTime? DurationOutpost { get; set; }

        /// <summary>
        /// 反馈客户期限
        /// </summary>
        [Column(Name = "duration_customer")]
        public DateTime? DurationCustomer { get; set; }
        
        /// <summary>
        /// 提成生效日
        /// </summary>
        [Column(Name = "commission_effective_date")]
        public DateTime? CommissionEffectiveDate { get; set; }

        /// <summary>
        /// 反馈日
        /// </summary>
        [Column(Name = "feedback_day")]
        public DateTime? FeedbackDay { get; set; }

        /// <summary>
        /// 指示外所日
        /// </summary>
        [Column(Name = "instruct_day")]
        public DateTime? InstructDay { get; set; }

        /// <summary>
        /// 外所提供凭证日
        /// </summary>
        [Column(Name = "voucher_day")]
        public DateTime? VoucherDay { get; set; }

        /// <summary>
        /// 通知书收文日
        /// </summary>
        [Column(Name = "notification_receipt_date")]
        public DateTime? NotificationReceiptDate { get; set; }

        /// <summary>
        /// 宽展期限
        /// </summary>
        [Column(Name = "extension_period")]
        public DateTime? ExtensionPeriod { get; set; }

        /// <summary>
        /// 是否新增任务
        /// </summary>
        [Column(Name = "is_add_proc")]
        public string IsAddProc { get; set; }

        /// <summary>
        /// 奖励生效日
        /// </summary>
        [Column(Name = "reward_effective_date")]
        public DateTime? RewardEffectiveDate { get; set; }
        
        /// <summary>
        /// 后补文件期限
        /// </summary>
        [Column(Name = "deadline_documents")]
        public DateTime? DeadlineDocuments { get; set; }
         
        /// <summary>
        /// 案件跟进期限
        /// </summary>
        [Column(Name = "case_follow_deadline")]
        public DateTime? CaseFollowDeadline { get; set; }
        
        /// <summary>
        /// 形势变更
        /// </summary>
        [Column(Name = "situation_changed")]
        public bool SituationChanged { get; set; }

        /// <summary>
        /// 外所文号
        /// </summary>
        [Column(Name = "foreign_number")]
        public string ForeignNumber { get; set; } = string.Empty;
        
        /// <summary>
        /// 选所备注
        /// </summary>
        [Column(Name = "foreign_supplier_remark", IsNullable = false)]
        public string ForeignSupplierRemark { get; set; } = string.Empty;

        /// <summary>
        ///  任务类型信息
        /// </summary>
        [Navigate(nameof(CtrlProcId))]
        public virtual BasCtrlProc BasCtrlProc { get; set; } = default!;

        /// <summary>
        ///  承办人信息
        /// </summary>
        [Navigate(nameof(UndertakeUserId))]
        public virtual SysUserInfo UndertakeUserInfo { get; set; } = default!;

        /// <summary>
        ///  任务递交信息
        /// </summary>
        [Navigate(nameof(ProcId))]
        public virtual DeliInfo DeliInfo { get; set; } = default!;

        /// <summary>
        ///  代理人状态信息
        /// </summary>
        [Navigate(nameof(SubProcStatusId), TempPrimary = nameof(BasSubProcStatus.Code))]
        public virtual BasSubProcStatus SubProcStatus { get; set; } = default!;

        /// <summary>
        ///  代理机构
        /// </summary>
        [Navigate(nameof(TrademarkDeliveryAgencyId))]
        public virtual BasAgency Agency { get; set; } = default!;

        /// <summary>
        ///  个人标签
        /// </summary>
        [Navigate(nameof(PrivateId))]
        public virtual SysFlowPrivate FlowPrivate { get; set; } = default!;

        /// <summary>
        /// 任务尼斯分类导航属性
        /// </summary>
        [Display(Name = "尼斯分类", Order = 12)]
        [Navigate(nameof(ProcNiceCategory.ProcId))]
        public virtual ICollection<ProcNiceCategory>? NiceCategories { get; set; }

        /// <summary>
        /// 变更前申请人信息
        /// </summary>
        [Display(Name = "变更前信息", Order = 13)]
        [Navigate(nameof(CaseProcApplicant.ProcId))]
        public virtual ICollection<CaseProcApplicant>? Applicants { get; set; }

        /// <summary>
        /// 法律与事实依据
        /// </summary>
        [Display(Name = "法律与事实依据", Order = 14)]
        [Navigate(nameof(TrademarkLawBasis.ProcId))]
        public virtual ICollection<TrademarkLawBasis>? LawBasis { get; set; }

        /// <summary>
        /// 引证商标
        /// </summary>
        [Display(Name = "引证商标", Order = 15)]
        [Navigate(nameof(ProcCitedTrademark.ProcId))]
        public virtual ICollection<ProcCitedTrademark>? CitedTrademarks { get; set; }
        
        /// <summary>
        /// 任务境外代理联系人
        /// </summary>
        [Navigate(nameof(ProcForeignSupplierContact.ProcId))]
        public virtual ICollection<ProcForeignSupplierContact>? ForeignSupplierContacts { get; set; }

        /// <inheritdoc />
        [JsonIgnore]
        [Column(IsIgnore = true)]
        public Expression<Func<CaseProcInfo, string>> BaseKey => procInfo => procInfo.CaseId;
    }

}
