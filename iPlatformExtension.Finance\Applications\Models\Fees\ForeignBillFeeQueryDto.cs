﻿using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Applications.Models.Fees;

/// <summary>
/// 外所账单查询参数
/// </summary>
public class ForeignBillFeeQueryDto : IPaginationParameters
{
    /// <summary>
    /// 外所账单号
    /// </summary>
    public IEnumerable<string> ForeignBillNumbers { get; init; } = [];

    /// <summary>
    /// 银行账号
    /// 外所账号
    /// </summary>
    public IEnumerable<string> BankAccountNumbers { get; init; } = [];

    /// <summary>
    /// 外所账单付款状态
    /// </summary>
    public IEnumerable<string> ForeignBillPaymentStatus { get; init; } = [];

    /// <summary>
    /// 外所账单应付日期
    /// </summary>
    public PeriodQueryDto ForeignBillPaymentDueDate { get; set; }

    /// <summary>
    /// 外所账单申请批次名称
    /// </summary>
    public string? ForeignApplyBatchName { get; set; }

    /// <summary>
    /// 外所账单付款日期
    /// </summary>
    public PeriodQueryDto ForeignBillPaymentDate { get; set; }

    /// <summary>
    /// 外所账单案件类型
    /// </summary>
    public string? ForeignBillCaseType { get; init; }

    /// <summary>
    /// 外所账单所属分所
    /// </summary>
    public string? ForeignDistrict { get; set; }

    /// <summary>
    /// 外所账单币别
    /// </summary>
    public string? ForeignBillCurrency { get; set; }

    /// <summary>
    /// 外所账单
    /// </summary>
    public PeriodQueryDto ForeignBillReceiveDate { get; set; }

    /// <summary>
    /// 外所账单日期
    /// </summary>
    public PeriodQueryDto ForeignBillDate { get; set; }

    /// <summary>
    /// 外所账单付款批次名称
    /// </summary>
    public string? ForeignPaymentBatchName { get; set; }

    /// <summary>
    /// 我方文号
    /// </summary>
    public IEnumerable<string> Volumes { get; set; } = [];

    /// <summary>
    /// 境外客户代理
    /// </summary>
    public IEnumerable<string> CustomerIds { get; set; } = [];

    /// <summary>
    /// 客户文号
    /// </summary>
    public IEnumerable<string> CustomerCaseNumbers { get; set; } = [];

    /// <summary>
    /// 页码
    /// </summary>
    public int? PageIndex { get; internal set; } = 1;

    /// <summary>
    /// 页面大小
    /// </summary>
    public int? PageSize => 500;
}