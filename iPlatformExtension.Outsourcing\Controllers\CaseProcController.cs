﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using System.Web;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Outsourcing.Application.Handlers.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using iPlatformExtension.Outsourcing.Infrastructure.Authorization.NotAssignedOutsourcing;
using iPlatformExtension.Outsourcing.Infrastructure.Constants;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;

namespace iPlatformExtension.Outsourcing.Controllers;

[Description("案件任务控制器")]
[Tags("委外信息", "境外代理", "外所文号")]
[ApiController]
[Route("proc")]
[Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
[ProducesResponseType<ResultData>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
public sealed class CaseProcController(ISender sender) : ControllerBase
{
    [HttpPatch("{procId}")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    [EndpointSummary("更新任务委外信息")]
    [EndpointDescription("""
                         更新任务境外代理，外所文号等信息。
                         path值如下：
                         外所文号：/foreignNumber
                         选所备注：/foreignSupplierRemark
                         联系人：/contactorIds
                         费项集合id：/feesId
                         """)]
    [EndpointName(nameof(UpdateProcSupplierAsync))]
    [Authorize(Policy = AuthorizationPolicyNames.ProcOutsourcing)]
    public Task UpdateProcSupplierAsync(
        [FromRoute, Description("任务id"), Required] string procId, 
        [FromQuery, Description("版本号"), Required] int version,
        [FromBody, Description("json-patch更新信息")] JsonPatchDocument<ProcPatchDto> document)
    {
        return sender.Send(new UpdateProcCommand(procId, version, document), HttpContext.RequestAborted);
    }
    
    [EndpointName(nameof(GetProcOutsourcingAsync))]
    [EndpointSummary("获取案件任务委外信息")]
    [EndpointDescription("任务境外代理，任务外所文号等。我的商标获取单个任务是否已分配外所")]
    [HttpGet("{procId}/outsourcing")]
    [ProducesResponseType<ResultData<ProcOutsourcingDto>>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    public  Task<ProcOutsourcingDto> GetProcOutsourcingAsync([FromRoute, Description("任务id"), Required] string procId)
    {
        return sender.Send(new ProcOutsourcingQuery(procId), HttpContext.RequestAborted);
    }

    [EndpointName(nameof(AnyProcSupplierAsync))]
    [EndpointSummary("判断案件任务下的外所是否存在")]
    [EndpointDescription("没有请求体返回。如果任务下有境外代理返回200的响应码，不存在返回404的响应码")]
    [HttpHead("{procId}/supplier")]
    [AllowAnonymous]
    public async Task<IActionResult> AnyProcSupplierAsync([FromRoute, Description("任务id"), Required] string procId)
    {
        var dto = await sender.Send(new ProcOutsourcingQuery(procId), HttpContext.RequestAborted);
        return string.IsNullOrWhiteSpace(dto.SupplierId) ? NotFound() : NoContent();
    }

    [EndpointName(nameof(GetNotAssignedCountAsync))]
    [EndpointSummary("获取待分配外所任务的总数")]
    [EndpointDescription("获取所有未分配和待分配的总数。页签的显示的总数选待分配总数")]
    [HttpGet("not-assigned-count")]
    [ProducesResponseType<ResultData<NotAssignedCountDto>>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    public Task<NotAssignedCountDto> GetNotAssignedCountAsync(
        [FromQuery, Required, Description("权限类型")] string authorizationType)
    {
        return sender.Send(new NotAssignedCountCommand(authorizationType), HttpContext.RequestAborted);
    }

    [HttpGet("not-assigned/trademark")]
    [EndpointName(nameof(GetNotAssignedTrademarkProcInfosAsync))]
    [EndpointSummary("商标待分配外所任务列表")]
    [EndpointDescription("分页获取个人或所有商标待分配外所任务列表。每次获取的总数都要同步更新标签上的总数。仅针对商标用。")]
    [NotAssignedOutsourcingAuthorize(AuthorizationType.TrademarkOutsourcing, AuthorizationType.TrademarkOutsourcingAll)]
    [ProducesResponseType<PaginationResult<NotAssignedTrademarkProcDto>>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    public Task<IEnumerable<NotAssignedTrademarkProcDto>> GetNotAssignedTrademarkProcInfosAsync(
        [FromQuery, Required, Description("""
                                          权限类型
                                          商标个人待分配：foreign_entrust_trademark
                                          商标所有未分配：foreign_entrust_trademark_all
                                          """)] string authorizationType,
        [FromQuery, Description("关键字")] string? keyword,
        [FromQuery, Required, Range(1, int.MaxValue), Description("页码")] int pageIndex,
        [FromQuery, Required, Range(5, int.MaxValue), Description("页面大小")] int pageSize,
        [FromQuery, Description("""
                                排序字段
                                委案日期：EntrustDate
                                我方文号：CaseInfo.Volume
                                任务编号：ProcNo
                                商标名称：CaseId
                                客户名称：CaseInfo.CustomerId
                                任务名称：CtrlProcId
                                任务状态：ProcStatusId
                                """)] string sortField = nameof(CaseProcInfo.EntrustDate),
        [FromQuery, Description($"""
                                排序方式
                                顺序：{nameof(SortOrder.Ascending)}
                                倒序：{nameof(SortOrder.Descending)}
                                """)] SortOrder sortOrder = SortOrder.Descending)
    {
        return sender.Send(new NotAssignedTrademarkProcQuery
                (authorizationType, keyword, new SortCondition(sortField, sortOrder), pageIndex, pageSize), 
            HttpContext.RequestAborted);
    }
    
    [HttpGet("not-assigned/patent")]
    [EndpointName(nameof(GetNotAssignedPatentProcInfosAsync))]
    [EndpointSummary("专利待分配外所任务列表")]
    [EndpointDescription("分页获取个人或所有专利待分配外所任务列表。每次获取的总数都要同步更新标签上的总数。仅针对专利用。")]
    [NotAssignedOutsourcingAuthorize(AuthorizationType.PatentOutsourcing, AuthorizationType.PatentOutsourcingAll)]
    [ProducesResponseType<PaginationResult<NotAssignedPatentProcDto>>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    public Task<IEnumerable<NotAssignedPatentProcDto>> GetNotAssignedPatentProcInfosAsync(
        [FromQuery, Required, Description("""
                                          权限类型
                                          商标个人待分配：foreign_entrust
                                          商标所有未分配：foreign_entrust_all
                                          """)] string authorizationType,
        [FromQuery, Description("关键字")] string? keyword,
        [FromQuery, Required, Range(1, int.MaxValue), Description("页码")] int pageIndex,
        [FromQuery, Required, Range(5, int.MaxValue), Description("页面大小")] int pageSize,
        [FromQuery, Description("""
                                排序字段
                                初稿期限：IntFirstDate
                                我方文号：CaseInfo.Volume
                                任务编号：ProcNo
                                案件名称：CaseId
                                客户名称：CaseInfo.CustomerId
                                任务名称：CtrlProcId
                                官方期限：LegalDueDate
                                """)] string sortField = nameof(CaseProcInfo.IntFirstDate),
        [FromQuery, Description($"""
                                排序方式
                                顺序：{nameof(SortOrder.Ascending)}
                                倒序：{nameof(SortOrder.Descending)}
                                """)] SortOrder sortOrder = SortOrder.Ascending)
    {
        return sender.Send(new NotAssignedPatentProcQuery
                (authorizationType, keyword, new SortCondition(sortField, sortOrder), pageIndex, pageSize), 
            HttpContext.RequestAborted);
    }
    
//     [HttpGet("not-assigned/whole")]
//     [EndpointName(nameof(GetWholeNotAssignedProcInfosAsync))]
//     [EndpointSummary("分页获取所有未分配外所任务列表")]
//     [EndpointDescription("所有未分配外所任务列表。每次获取的总数都要同步更新标签上的总数")]
//     [NotAssignedOutsourcingAuthorize]
//     [ProducesResponseType<PaginationResult<NotAssignedProcDto>>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
//     public Task<IEnumerable<NotAssignedProcDto>> GetWholeNotAssignedProcInfosAsync(
//         [FromQuery, Required, Description("权限类型")] string authorizationType,
//         [FromQuery, Description("关键字")] string? keyword,
//         [FromQuery, Required, Range(1, int.MaxValue), Description("页码")] int pageIndex,
//         [FromQuery, Required, Range(5, int.MaxValue), Description("页面大小")] int pageSize,
//         [FromQuery, Description("""
//                                 排序字段
//                                 委案日期：EntrustDate
//                                 我方文号：CaseInfo.Volume
//                                 任务编号：ProcNo
//                                 商标名称：CaseId
//                                 客户名称：CaseInfo.CustomerId
//                                 任务名称：CtrlProcId
//                                 任务状态：ProcStatusId
//                                 """)] string sortField = nameof(CaseProcInfo.EntrustDate),
//         [FromQuery, Description($"""
//                                  排序方式
//                                  顺序：{nameof(SortOrder.Ascending)}
//                                  倒序：{nameof(SortOrder.Descending)}
//                                  """)] SortOrder sortOrder = SortOrder.Descending)
//     {
//         return sender.Send(new NotAssignedProcQuery(
//                 authorizationType, keyword, new SortCondition(sortField, sortOrder), pageIndex, pageSize), 
//             HttpContext.RequestAborted);
//     }

    [HttpPatch]
    [Consumes(MediaTypeNames.Application.JsonPatch, MediaTypeNames.Application.Json)]
    [EndpointSummary("批量更新任务委外信息")]
    [EndpointDescription("""
                         批量更新任务境外代理，外所文号等信息。
                         path值如下：
                         外所文号：/foreignNumber
                         选所备注：/foreignSupplierRemark
                         联系人：/contactorIds
                         费项集合id：/feesId
                         """)]
    [Authorize(AuthorizationPolicyNames.BatchProcOutsourcing)]
    public Task UpdateProcInfosAsync([FromBody] List<ProcJsonPatchDocument> documents)
    {
        return sender.Send(new UpdateBatchProcCommand(documents), HttpContext.RequestAborted);
    }

    [EndpointName(nameof(ExportNotAssignedTrademarkProcInfosAsync))]
    [EndpointSummary("商标待分配导出")]
    [EndpointDescription("导出个人或所有商标待分配外所任务清单")]
    [HttpGet("not-assigned/trademark/excel")]
    [NotAssignedOutsourcingAuthorize(AuthorizationType.TrademarkOutsourcing, AuthorizationType.TrademarkOutsourcingAll)]
    [Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", MediaTypeNames.Application.Json)]
    public async Task<PhysicalFileResult> ExportNotAssignedTrademarkProcInfosAsync(
        [Required, Description("权限类型")] string authorizationType,
        [Description("关键字")] string keyword = "")
    {
        var data = await sender.Send(
            new NotAssignedTrademarkProcExcelQuery(authorizationType, keyword.Trim()), HttpContext.RequestAborted);
        
        const string fileName = "待配外所任务清单.xlsx";
        var filePath = Path.Combine(TempDirectory.Current, TempDirectory.NotAssignedForeignSupplier, $"{DateTimeOffset.Now.Ticks}-{Guid.NewGuid()}-{fileName}");
        Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
        
        await using var stream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None);
        await stream.SaveAsAsync(data);
        
        return new PhysicalFileResult(Path.GetFullPath(filePath), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = HttpUtility.UrlEncode(fileName)
        };
    }
    
    [EndpointName(nameof(ExportNotAssignedPatentProcInfosAsync))]
    [EndpointSummary("专利待分配导出")]
    [EndpointDescription("导出个人或所有专利待分配外所任务清单")]
    [HttpGet("not-assigned/patent/excel")]
    [NotAssignedOutsourcingAuthorize(AuthorizationType.PatentOutsourcing, AuthorizationType.PatentOutsourcingAll)]
    [Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", MediaTypeNames.Application.Json)]
    public async Task<PhysicalFileResult> ExportNotAssignedPatentProcInfosAsync(
        [Required, Description("权限类型")] string authorizationType,
        [Description("关键字")] string keyword = "")
    {
        var data = await sender.Send(
            new NotAssignedPatentProcExcelQuery(authorizationType, keyword.Trim()), HttpContext.RequestAborted);
        
        const string fileName = "待配外所任务清单.xlsx";
        var filePath = Path.Combine(TempDirectory.Current, TempDirectory.NotAssignedForeignSupplier, $"{DateTimeOffset.Now.Ticks}-{Guid.NewGuid()}-{fileName}");
        Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
        
        await using var stream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None);
        await stream.SaveAsAsync(data);
        
        return new PhysicalFileResult(Path.GetFullPath(filePath), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = HttpUtility.UrlEncode(fileName)
        };
    }
    
    // [NotAssignedOutsourcingAuthorize()]
    // [EndpointName(nameof(ExportWholeNotAssignedProcInfosAsync))]
    // [EndpointSummary("导出所有外所任务清单")]
    // [HttpGet("not-assigned/whole/excel")]
    // [Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", MediaTypeNames.Application.Json)]
    // public async Task<PhysicalFileResult> ExportWholeNotAssignedProcInfosAsync(
    //     [FromQuery, Required, Description("权限类型")] string authorizationType,
    //     [Description("关键字")] string keyword = "")
    // {
    //     var data = await sender.Send(
    //         new NotAssignedProcExcelQuery(authorizationType, QueryType.Whole, keyword.Trim()), HttpContext.RequestAborted);
    //     
    //     const string fileName = "所有未配外所任务清单.xlsx";
    //     var filePath = Path.Combine(TempDirectory.Current, TempDirectory.NotAssignedForeignSupplier, $"{DateTimeOffset.Now.Ticks}-{Guid.NewGuid()}-{fileName}");
    //     Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
    //     
    //     await using var stream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None);
    //     await stream.SaveAsAsync(data);
    //     
    //     return new PhysicalFileResult(Path.GetFullPath(filePath), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    //     {
    //         FileDownloadName = HttpUtility.UrlEncode(fileName)
    //     };
    // }
    
    [HttpGet("my-proc-info")]
    [EndpointName(nameof(GetMyProcInfoAsync))]
    [EndpointSummary("选择境外代理弹窗下的任务信息")]
    [EndpointDescription("我的商标-选择境外代理-任务信息")]
    [ProducesResponseType<ResultData<MyProcInfoDto>>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    public Task<MyProcInfoDto> GetMyProcInfoAsync(
        [FromQuery, Required, Description("任务id")] string procId)
    {
        return sender.Send(new MyProcInfoQuery(procId), HttpContext.RequestAborted);
    }

    [AllowAnonymous]
    [HttpPost("supplier-query")]
    [EndpointName(nameof(GenerateSupplierQueryParametersAsync))]
    [EndpointSummary("生成境外代理查询参数")]
    [EndpointDescription("根据任务id验证和生成境外代理查询参数")]
    [Consumes(MediaTypeNames.Application.Json)]
    [ProducesResponseType<ResultData<SupplierQueryParameters>>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    public Task<SupplierQueryParameters> GenerateSupplierQueryParametersAsync([FromBody, Required, Description("任务id集合")] string[] procIds)
    {
        return sender.Send(new GenerateSupplierQueryParametersCommand(procIds), HttpContext.RequestAborted);
    }
}