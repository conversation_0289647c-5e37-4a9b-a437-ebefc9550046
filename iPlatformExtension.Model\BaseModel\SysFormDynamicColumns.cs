using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_form_dynamic_columns", DisableSyncStructure = true)]
	public partial class SysFormDynamicColumns {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_id", StringLength = 50)]
		public string ColumnId { get; set; }

		/// <summary>
		/// 如果同一个栏位不同表单名字不一样,可以额外设定
		/// </summary>
		[ Column(Name = "column_name", StringLength = 50)]
		public string ColumnName { get; set; }

		[ Column(Name = "container_id", StringLength = 50)]
		public string ContainerId { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "dynamic_form_code", StringLength = 50)]
		public string DynamicFormCode { get; set; }

		/// <summary>
		/// 如果同一个栏位不同表单下拉选择项不一样,可以额外设定
		/// </summary>
		[ Column(Name = "form_data_code", StringLength = 50)]
		public string FormDataCode { get; set; }

		[ Column(Name = "not_null")]
		public bool? NotNull { get; set; }

		/// <summary>
		/// 标示变量ID,比如任务ID
		/// </summary>
		[ Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

		[ Column(Name = "readonly")]
		public bool? Readonly { get; set; }

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

	}

}
