﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.MailCenter.Applications.Models.AuditUser;
using iPlatformExtension.MailCenter.Applications.Queries.AuditUser;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AuditUser
{
    /// <summary>
    /// 获取发件必审人列表查询处理程序
    /// </summary>
    internal sealed class GetFlowAuditUserQueryHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IFreeSql<PlatformFreeSql> platformFreeSql,
        IUserInfoRepository userInfoRepository) : IRequestHandler<GetFlowAuditUserQuery, IEnumerable<GetFlowAuditUserDto>>
    {
        public async Task<IEnumerable<GetFlowAuditUserDto>> Handle(GetFlowAuditUserQuery request, CancellationToken cancellationToken)
        {
            // 查询用户信息，用于筛选条件
            var userInfoQuery = platformFreeSql.Select<SysUserInfo>();

            // 如果有必审人名称筛选条件
            List<string>? auditingUserIds = null;
            if (!string.IsNullOrWhiteSpace(request.AuditingName))
            {
                auditingUserIds = await userInfoQuery
                    .Where(u => u.CnName.Contains(request.AuditingName))
                    .ToListAsync(u => u.UserId, cancellationToken);

                // 如果没有找到匹配的用户，返回空结果
                if (auditingUserIds.Count == 0)
                {
                    return Enumerable.Empty<GetFlowAuditUserDto>();
                }
            }

            // 如果有指定审核人名称筛选条件
            List<string>? designatedAuditUserIds = null;
            if (!string.IsNullOrWhiteSpace(request.DesignatedAuditName))
            {
                designatedAuditUserIds = await userInfoQuery
                    .Where(u => u.CnName.Contains(request.DesignatedAuditName))
                    .ToListAsync(u => u.UserId, cancellationToken);

                // 如果没有找到匹配的用户，返回空结果
                if (designatedAuditUserIds.Count == 0)
                {
                    return Enumerable.Empty<GetFlowAuditUserDto>();
                }
            }

            // 查询发件必审人列表
            var auditUsersQuery = freeSql.Select<FlowRequireAudit>()
                .WithLock();

            // 应用筛选条件
            if (auditingUserIds != null && auditingUserIds.Count > 0)
            {
                // 必审人字段包含多个ID，使用分号分隔
                auditUsersQuery = auditUsersQuery.Where(require =>
                    auditingUserIds.Any(id => require.RequireAuditUser != null && require.RequireAuditUser.Contains(id)));
            }

            if (designatedAuditUserIds != null && designatedAuditUserIds.Count > 0)
            {
                // 指定审核人字段包含多个ID，使用分号分隔
                auditUsersQuery = auditUsersQuery.Where(require =>
                    designatedAuditUserIds.Any(id => require.AuditUserId != null && require.AuditUserId.Contains(id)));
            }

            // 获取结果
            var auditUsers = await auditUsersQuery
                .OrderByDescending(require => require.UpdateTime)
                .ToListAsync(cancellationToken);

            // 构建结果
            var result = new List<GetFlowAuditUserDto>();

            // 使用缓存获取用户信息
            ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;

            foreach (var item in auditUsers)
            {
                // 必审人可能有多个，使用分号分隔
                var requireAuditUserIds = !string.IsNullOrEmpty(item.RequireAuditUser) ?
                    item.RequireAuditUser.Split(';', StringSplitOptions.RemoveEmptyEntries) : Array.Empty<string>();

                // 指定审核人可能有多个，使用分号分隔
                var auditUserIds = !string.IsNullOrEmpty(item.AuditUserId) ?
                    item.AuditUserId.Split(';', StringSplitOptions.RemoveEmptyEntries) : Array.Empty<string>();

                // 获取创建人信息
                var createUser = await userBaseInfoRepository.GetCacheValueAsync(item.CreateBy, cancellationToken: cancellationToken);
                var createByUser = new { userId = item.CreateBy, name = createUser?.CnName ?? string.Empty };

                // 获取更新人信息
                var updateUser = await userBaseInfoRepository.GetCacheValueAsync(item.UpdateBy, cancellationToken: cancellationToken);
                var updateByUser = new { userId = item.UpdateBy, name = updateUser?.CnName ?? string.Empty };

                // 将必审人转换为匿名数组形式
                var requireAuditUserArray = new List<object>();
                foreach (var userId in requireAuditUserIds)
                {
                    var userInfo = await userBaseInfoRepository.GetCacheValueAsync(userId, cancellationToken: cancellationToken);
                    requireAuditUserArray.Add(new { userId, name = userInfo?.CnName ?? string.Empty });
                }

                // 将指定审核人转换为匿名数组形式
                var designatedAuditUserArray = new List<object>();
                foreach (var userId in auditUserIds)
                {
                    var userInfo = await userBaseInfoRepository.GetCacheValueAsync(userId, cancellationToken: cancellationToken);
                    designatedAuditUserArray.Add(new { userId, name = userInfo?.CnName ?? string.Empty });
                }

                result.Add(new GetFlowAuditUserDto
                {
                    Id = item.Id,
                    RequireAuditUsers = requireAuditUserArray,
                    DesignatedAuditUsers = designatedAuditUserArray,
                    CreateByUser = createByUser,
                    CreateTime = item.CreateTime,
                    UpdateByUser = updateByUser,
                    UpdateTime = item.UpdateTime
                });
            }

            return result;
        }


    }
}
