﻿namespace ConsoleScript;

public class SupplierTrademarkProcCountDto
{
    // /// <summary>
    // /// 供应商名称
    // /// </summary>
    // public string SupplierName { get; set; }=string.Empty;
    //
    /// <summary>
    /// 供应商id
    /// </summary>
    public long SupplierId { get; set; }
    
    /// <summary>
    /// 任务数量
    /// </summary>
    public int Count { get; set; }=0;

    // /// <summary>
    // /// 任务名称
    // /// </summary>
    // public string ProcName { get; set; }=string.Empty;
    //
    // /// <summary>
    // /// 国家名称
    // /// </summary>
    // public string CountryName { get; set; }=string.Empty;
    //
    // /// <summary>
    // /// 最新使用日期
    // /// </summary>
    // public DateTime LatestUsedTime { get; set; }
}