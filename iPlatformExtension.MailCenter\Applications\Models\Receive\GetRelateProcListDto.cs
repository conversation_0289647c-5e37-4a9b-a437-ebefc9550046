﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 关联任务
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="ProcNo">任务编号</param>
/// <param name="CaseTypeId">案件类型</param>
/// <param name="CtrlProcId">任务id</param>
/// <param name="Volume">案卷号</param>
/// <param name="CaseName">案件名称</param>
/// <param name="CreateTime">创建时间</param>
/// <param name="CtrlPorcName">任务名称</param>
/// <param name="CtrlProcMark">任务标识</param>
/// <param name="ProcStatus">任务状态</param>
/// <param name="CtrlProcProperty">任务属性</param>
public record GetRelateProcListDto(
    string ProcId,
    string ProcNo,
    string CaseTypeId,
    string CtrlProcId,
    string Volume,
    string CaseName,
    DateTime? CreateTime,
    string? CtrlProcMarkId,
    string ProcStatusId,
    string CtrlProcPropertyId
)
{
    public string? CtrlProcName { get; set; }
    public object? CtrlProcMark { get; set; }
    public object? ProcStatus { get; set; }
    public object? CtrlProcProperty { get; set; }
};

