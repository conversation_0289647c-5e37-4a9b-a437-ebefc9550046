using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.Process;
using iPlatformExtension.MailCenter.Applications.Queries.Process;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Process;

/// <summary>
/// 获取发件待审清单列表
/// </summary>
internal sealed class SendProcessListQueryHandler(IFreeSql<MailCenterFreeSql> freeSql, IMediator mediator, IHttpContextAccessor content, IUserInfoRepository userInfoRepository) : IRequestHandler<SendProcessListQuery, IEnumerable<SendProcessListDto>>
{
    public async Task<IEnumerable<SendProcessListDto>> Handle(SendProcessListQuery request, CancellationToken cancellationToken)
    {
        var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
        var privateList = request.CheckedPrivateList?.Contains("") | request.CheckedPrivateList?.Contains(null);

        // 查询发件待审清单
        var mailSends = freeSql.Select<MailSend, MailSendFlow, FlowRecord, FlowRecord, FlowPrivateList, MailUser>()
            .LeftJoin(it => it.t1.MailId == it.t2.MailId)
            .LeftJoin(it => it.t2.MailId == it.t3.MailId && it.t3.IsCurrent == SysEnum.Status.Enable.GetHashCode())
            .LeftJoin(it => it.t3.PreRecordId == it.t4.Id)
            .LeftJoin(it => it.t1.MailId == it.t5.MailId)
            .LeftJoin(it => it.t1.MailId == it.t6.MailId && it.t6.AddressType == "from")
            .Where(it => it.t3 != null)
            .Where(it => it.t3.AuditUser == userId)
            .Where(it => it.t1.Status == SysEnum.SendStatusType.Reviewing.GetHashCode()) // 审核中状态
            .WithLock()
            .WhereIf(privateList is not null && privateList == true, it => request.CheckedPrivateList!.Contains(it.t5.PrivateId) || it.t5 == null)
            .WhereIf(privateList == false, it => request.CheckedPrivateList!.Contains(it.t5.PrivateId))
            .WhereIf(!string.IsNullOrEmpty(request.Search), it =>
                it.t1.MailNo == request.Search ||
                it.t1.MailSubject.Contains(request.Search) ||
                it.t1.MailFrom.Contains(request.Search))
            .Page(request.PageIndex!.Value, request.PageSize!.Value).Count(out var count);

        // 排序
        mailSends = request.Sort is null ?
            mailSends.OrderByDescending(it => it.t1.CreateTime) :
            string.Equals(request.SortType, "asc", comparisonType: StringComparison.OrdinalIgnoreCase) ?
                mailSends.OrderByPropertyName(request.Sort) :
                mailSends.OrderByPropertyName(request.Sort, false);

        // 查询结果
        var listAsync = await mailSends.ToListAsync(it => new SendProcessListDto(
            it.t1.MailId,
            it.t1.MailNo ?? string.Empty,
            it.t1.MailSubject ?? string.Empty,
            it.t1.MailTo ?? string.Empty,
            it.t1.CreateBy,
            it.t1.IsRead ?? false,
            it.t1.MailDate,
            it.t1.Status,
            it.t4.AuditRemark ?? string.Empty,
            it.t3.CurNodeId ?? string.Empty,
            it.t4.AuditRemark ?? string.Empty,
            it.t4.AuditTime,
            it.t4.AuditType ?? string.Empty,
            it.t2.UndertakeUserId ?? string.Empty,
            it.t3.AuditUser ?? string.Empty),
            cancellationToken);

        // 返回分页结果
        return new PageResult<SendProcessListDto>()
        {
            Data = await listAsync.ToAsyncEnumerable().SelectAwait(async sendList =>
            {
                // 处理发件人信息
                if (sendList.MailFromTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    sendList.MailFrom = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(sendList.MailFromTemp))?.CnName ?? "", UserId = sendList.MailFromTemp };
                }

                // 处理承办人信息
                if (sendList.UndertakeUserTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    sendList.UndertakeUser = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(sendList.UndertakeUserTemp))?.CnName ?? "", UserId = sendList.UndertakeUserTemp };
                }

                // 处理审核人信息
                if (sendList.AuditUserTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    sendList.AuditUser = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(sendList.AuditUserTemp))?.CnName ?? "", UserId = sendList.AuditUserTemp };
                }

                return sendList;
            }).ToListAsync(cancellationToken: cancellationToken),
            Page = request.PageIndex.Value,
            PageSize = request.PageSize.Value,
            Total = count
        };
    }
}
