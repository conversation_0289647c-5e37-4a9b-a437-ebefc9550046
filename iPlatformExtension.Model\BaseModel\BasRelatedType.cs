using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_related_type", DisableSyncStructure = true)]
	public partial class BasRelatedType {

		[ Column(Name = "related_type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string RelatedTypeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "related_type_code", StringLength = 50)]
		public string RelatedTypeCode { get; set; }

		[ Column(Name = "related_type_en_us", StringLength = 500)]
		public string RelatedTypeEnUs { get; set; }

		[ Column(Name = "related_type_ja_jp", StringLength = 50)]
		public string RelatedTypeJaJp { get; set; }

		[ Column(Name = "related_type_zh_cn", StringLength = 50)]
		public string RelatedTypeZhCn { get; set; }

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
