﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Commands.Proc;

/// <summary>
/// 添加外所联系人命令
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="SupplierId">外所id</param>
/// <param name="ContactId">联系人id</param>
/// <param name="Version">任务版本号</param>
[Description("任务境外代理联系人")]
public sealed record AddProcSupplierContactCommand(
    [property: Description("任务id"), Required] string ProcId,
    [property: Description("业务系统供应商id"), Required] string SupplierId,
    [property: Description("业务系统供应商联系人id"), Required] HashSet<string> ContactIds,
    [property: Description("任务版本号"), Required] int Version) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;