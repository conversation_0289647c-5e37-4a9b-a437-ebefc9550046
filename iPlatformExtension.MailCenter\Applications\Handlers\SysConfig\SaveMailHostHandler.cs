﻿using iPlatformExtension.Common.Encryption;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Helper;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Hosting;
using iPlatformExtension.MailCenter.Infrastructure.Extension;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    public class SaveMailHostHandler(IMailHostRepository mailHostRepository,
        IMailAccessRepository mailAccessRepository,
        IMediator mediator,
        IHttpContextAccessor httpContextAccessor,
        MailTool mailTool
        ) : IRequestHandler<SaveMailHostCommand>
    {
        public async Task Handle(SaveMailHostCommand request, CancellationToken cancellationToken)
        {
            string privateUserId = "";
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            if (request.IsPrivate)
            {
                privateUserId = userId;
            }

            if (!string.IsNullOrEmpty(request.HostId))
            {
                var host = await mailHostRepository.GetAsync(request.HostId, cancellationToken);
                if (host != null)
                {
                    if (host.Account != request.Account)
                    {
                        var verifyMail = await mailHostRepository.Where(o => o.Account == request.Account).AnyAsync();
                        if (verifyMail)
                        {
                            throw new ApplicationException("邮箱帐号已存在,保存失败。");
                        }
                    }
                    request.CopyFields(ref host);
                    if (!string.IsNullOrWhiteSpace(request.Password))
                    {
                        host.Password = DESHelper.DESEncrypt(request.Password);
                        await mailTool.VerifyMailAvailableAsync(new Models.SysConfig.VerifyMailDto
                        {
                            ImapHost = request.ImapHost,
                            ImapPort = request.ImapPort,
                            SenderAccount = request.Account,
                            SenderPassword = request.Password
                        }, cancellationToken);
                    }


                    host.UpdateBy = userId;
                    host.UpdateTime = DateTime.Now;
                    host.PrivateUserId = privateUserId;
                    await mailHostRepository.UpdateAsync(host, cancellationToken);
                    await mailAccessRepository.Where(o => o.HostId == host.HostId).ToDelete().ExecuteAffrowsAsync(cancellationToken);
                    await mediator.Send(new SaveMailAccessCommand(host.HostId, request.Accesses), cancellationToken);
                }
            }
            else
            {
                ArgumentException.ThrowIfNullOrWhiteSpace(request.Password);
                var verifyMail = await mailHostRepository.Where(o => o.Account == request.Account).AnyAsync();
                if (verifyMail)
                {
                    throw new ApplicationException("邮箱帐号已存在,不允许重复创建");
                }

                var mailHost = request.Clone<MailHost>();
                mailHost.HostId = Guid.NewGuid().ToString();
                mailHost.CreateBy = userId;
                mailHost.CreateTime = DateTime.Now;
                mailHost.UpdateBy = userId;
                mailHost.UpdateTime = DateTime.Now;
                mailHost.PrivateUserId = privateUserId;
                mailHost.Password = DESHelper.DESEncrypt(request.Password);

                await mailTool.VerifyMailAvailableAsync(new Models.SysConfig.VerifyMailDto
                {
                    ImapHost = request.ImapHost,
                    ImapPort = request.ImapPort,
                    SenderAccount = request.Account,
                    SenderPassword = request.Password
                }, cancellationToken);


                await mailHostRepository.InsertAsync(mailHost, cancellationToken);
                await mediator.Send(new SaveMailAccessCommand(mailHost.HostId, request.Accesses), cancellationToken);
            }
        }
    }
}
