﻿using FreeSql;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Hosting.Internal;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using iPlatformExtension.Model.Constants;

namespace CommonTest;

public class FreeSqlTest
{
    private readonly IServiceProvider _serviceProvider;

    public FreeSqlTest()
    {
        var services = new ServiceCollection().AddSingleton<IHostEnvironment>(new HostingEnvironment()
        {
            EnvironmentName = "Local",
            ApplicationName = "FreeSqlTest"
        }).AddPlatformFreeSql(
            "data source=***************,7433;initial catalog=acip_iplatform_1009;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true")
            .Services.AddObjectPools().AddLogging(builder => builder.AddConsole()).AddDataService();
        _serviceProvider = services.BuildServiceProvider();
    }

    [Fact]
    public async Task TestUnitOfWorkVersion()
    {
        using var scope = _serviceProvider.CreateScope();
        var provider = scope.ServiceProvider;
        var manager = provider.GetRequiredService<UnitOfWorkManager>();

        using var unitOfWork = manager.Begin();
        var repository = provider.GetRequiredService<IDeliveryInfoRepository>();
        var deliveryInfo = await repository.GetAsync("24c48d5f-c2c3-4ee0-8c19-d531808f87ac");
        deliveryInfo.Status = 0;
        var count = await repository.UpdateAsync(deliveryInfo);
        unitOfWork.Commit();

        Assert.Equal(0, count);
    }


    [Fact]
    public async Task testPage()
    {
       
        var fsql = _serviceProvider.GetRequiredService<IFreeSql>();
        var sss = fsql.Select<CaseInfo>().Where(o => o.CustomerId == "98284C01-AD18-4EEF-A3A5-5D7CAD4F848C").Page(10,20).ToList();
        var sss2 = fsql.Select<CaseInfo>().Where(o => o.CustomerId == "98284C01-AD18-4EEF-A3A5-5D7CAD4F848C").OrderBy(o=>o.CreateUserId).Page(10,20).ToList();

    }

    [Theory]
    [InlineData("0015E54C-9B12-4520-B0CB-915A3E25CE53", OfficialFeeMark.None)]
    [InlineData("C3305ED7-D412-448E-B047-55D440F78E38", OfficialFeeMark.ToBeConfirmed)]
    public async Task TestEnumWithLambdaParameters(string feeId, OfficialFeeMark? officialFeeMark)
    {
        var freeSql = _serviceProvider.GetRequiredService<IFreeSql>();

        var query = freeSql.Select<CaseFeeList>().As(nameof(CaseFeeList))
            .InnerJoin(caseFeeList =>
                caseFeeList.CaseProcInfo.ProcId == caseFeeList.ProcId)
            .Where(list => list.FeeId == feeId).Where(list => list.OfficialFeeMark == officialFeeMark);
        var countTask = query.ToAggregateAsync(aggregate => SqlExt.DistinctCount(aggregate.Key.FeeId));

        var caseFees = await freeSql.Select<CaseFeeList>().As(nameof(CaseFeeList))
            .InnerJoin(caseFeeList =>
                caseFeeList.CaseProcInfo.ProcId == caseFeeList.ProcId)
            .Where(list => list.FeeId == feeId).Where(list => list.OfficialFeeMark == officialFeeMark)
            .OrderByPropertyName(nameof(CaseFeeList.FeeId))
            .Distinct()
            .Page(1, 10)
            .ToListAsync(list => list.FeeId);

        var count = await countTask;
    }

    [Theory]
    [InlineData(2024, 7)]
    [InlineData(2024, 8)]
    [InlineData(2024, 9)]
    public async Task TestSelectToKeyValue(int year, int month)
    {
        var freeSql = _serviceProvider.GetRequiredService<IFreeSql>();
        
        var patentBonusList = await freeSql.Select<BillBonusCaseList, CaseProcInfo, CaseInfo>().WithLock()
            .LeftJoin((list, proInfo, caseInfo) => list.ProcId == proInfo.ProcId)
            .LeftJoin((list, procInfo, caseInfo) => list.CaseId == caseInfo.Id)
            .Where((list, procInfo, caseInfo) => list.Pushed == false)
            .Where((list, procInfo, caseInfo) => list.IsEnabled == true)
            .Where((list, procInfo, caseInfo) => list.Year == year.ToString())
            .Where((list, procInfo, caseInfo) => list.Month == month.ToString())
            .Where((list, procInfo, caseInfo) => list.BonusType == "draft")
            .Where((list, procInfo, caseInfo) => list.CaseTypeId == CaseType.Patent)
            .ToListAsync((list, procInfo, caseInfo) => new KeyValuePair<CommissionWeightKey, BillBonusCaseList>(
                new CommissionWeightKey(
                    list.ProcId,
                    list.CtrlProcId,
                    caseInfo.CaseName,
                    list.CaseDirection,
                    list.CaseTypeId,
                    procInfo.UndertakeUserId ?? string.Empty,
                    caseInfo.Volume,
                    caseInfo.CustomerId),
                list));
        
        Assert.True(patentBonusList.Count() > 0);
    }
    
    internal sealed record CommissionWeightKey(
        string ProcId,
        string CtrlProcId,
        string CaseName,
        string CaseDirection,
        string CaseType,
        string UndertakerId,
        string Volume,
        string CustomerId);
}