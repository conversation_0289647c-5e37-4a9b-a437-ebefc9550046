﻿using System.Data.Common;
using System.Linq.Expressions;
using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Notifications.Proc;

internal sealed class NotAssignedProcQueryContext(
    NotAssignedProcQueryOptions options,
    ISelect<CaseProcInfo> query,
    IReadOnlyDictionary<string, SysAuthFilter> filters,
    ISet<string> ctrlProcIds,
    NotAssignedProcQueryContext.NotAssignedCountQueryBuilder countQueryBuilder)
    : INotification
{
    public ISelect<CaseProcInfo> Query { get; } = query;

    public IReadOnlyDictionary<string, SysAuthFilter> Filters { get; } = filters;

    /// <summary>
    /// 任务名称id集合
    /// </summary>
    public ISet<string> CtrlProcIds { get; } = ctrlProcIds;

    /// <summary>
    /// 案件id集合
    /// </summary>
    public ISet<string> CaseIds { get; } = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
    
    public NotAssignedProcQueryOptions Options { get; } = options;

    public NotAssignedCountQueryBuilder CountQueryBuilder { get; } = countQueryBuilder;

    internal sealed class NotAssignedCountQueryBuilder(ISelect<CaseProcInfo> mainQuery)
    {
        public Dictionary<string, ISelect<CaseProcInfo>> OtherCountQueries { get; } = new();
        
        public ISelect<CaseProcInfo> MainQuery { get; } = mainQuery;
        
        internal NotAssignedCountQueryBuilder Where(Expression<Func<CaseProcInfo, bool>> filter)
        {
            MainQuery.Where(filter);
            foreach (var query in OtherCountQueries.Values)
            {
                query.Where(filter);
            }

            return this;
        }
        
        
        internal NotAssignedCountQueryBuilder WhereIf(bool condition, Expression<Func<CaseProcInfo, bool>> filter)
        {
            return condition ? Where(filter) : this;
        }

        internal ISelect<string> Build()
        {
            return OtherCountQueries.Values.Aggregate(MainQuery.WithTempQuery(info => info.ProcId), 
                (current, query) => current.UnionAll(query.WithTempQuery(info => info.ProcId)));
        }
    }
}