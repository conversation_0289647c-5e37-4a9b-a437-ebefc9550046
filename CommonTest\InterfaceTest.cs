﻿namespace CommonTest;

public class InterfaceTest
{
    [Fact]
    public void TestInterfaceImplementMethod()
    {
        IC a = new C();
        Assert.Equal(3, a.GetNumber());
    }
    
    public interface IA
    {
        int GetNumber() => 1;
    }
    
    public interface IB : IA
    {
        int IA.GetNumber()
        {
            return 2;
        }
    }
    
    public interface IC : IB
    {
        int IA.GetNumber()
        {
            return 3;
        }
    }
    
    public class B : IB
    {
        
    }
    
    public class C : IC
    {
        
    }
}