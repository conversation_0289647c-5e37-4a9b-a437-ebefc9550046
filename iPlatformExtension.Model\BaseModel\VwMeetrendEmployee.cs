using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_meetrend_Employee", DisableSyncStructure = true)]
	public partial class VwMeetrendEmployee {

		[ Column(StringLength = 100)]
		public string EMAIL { get; set; }

		[ Column(StringLength = 100)]
		public string FALIAS { get; set; }

		[ Column(StringLength = 50)]
		public string FAREAID { get; set; }

		[ Column(StringLength = 50)]
		public string FCOMPANY { get; set; }

		[ Column(StringLength = 50)]
		public string FDEPARTMENTID { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string FID { get; set; }

		
		public DateTime? FJOINDATE { get; set; }

		
		public DateTime? FLEAVEDATE { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string FNAME { get; set; }

		[ Column(StringLength = 50)]
		public string FNUMBER { get; set; }

		
		public bool ISENABLED { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "user_type")]
		public int UserType { get; set; }

	}

}
