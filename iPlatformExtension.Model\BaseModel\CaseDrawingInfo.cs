using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 案件任务的制图信息表
	/// </summary>
	[ Table(Name = "case_drawing_info", DisableSyncStructure = true)]
	public partial class CaseDrawingInfo {

		/// <summary>
		/// 主键
		/// </summary>
		[ Column(StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DrawingId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 实际交付日期
		/// </summary>
		
		public DateTime? ActualDeliveryDate { get; set; }

		/// <summary>
		/// 代理人id
		/// </summary>
		[ Column(StringLength = 50, IsNullable = false)]
		public string AgentId { get; set; }

		/// <summary>
		/// 关联的案件id
		/// </summary>
		[ Column(StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		
		public DateTime CreationTime { get; set; }

		/// <summary>
		/// 交付数量
		/// </summary>
		
		public short DeliveryQuantity { get; set; }

		/// <summary>
		/// 制图类型
		/// </summary>
		[ Column(StringLength = 50, IsNullable = false)]
		public string DrawingType { get; set; }

		/// <summary>
		/// 期望交付日期
		/// </summary>
		
		public DateTime ExpectedDeliverDate { get; set; }

		/// <summary>
		/// 流程活动id
		/// </summary>
		[ Column(StringLength = 50, IsNullable = false)]
		public string FlowActivityId { get; set; }

		/// <summary>
		/// 制图主管用户id
		/// </summary>
		[ Column(StringLength = 50)]
		public string ManagerId { get; set; }

		/// <summary>
		/// 制图员id
		/// </summary>
		[ Column(StringLength = 50)]
		public string PainterId { get; set; }

		/// <summary>
		/// 关联的任务id
		/// </summary>
		[ Column(StringLength = 50, IsNullable = false)]
		public string ProcId { get; set; }

		/// <summary>
		/// 制图流程的状态（0：启动。1000：制图中。5000：完成）
		/// </summary>
		
		public int Status { get; set; } = 0;

		/// <summary>
		/// 三维模型（个）
		/// </summary>
		
		public byte ThreeDQuantity { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		
		public DateTime UpdateTime { get; set; }

	}

}
