﻿using System.ComponentModel;

namespace iPlatformExtension.Outsourcing.Application.Models.Fee;

/// <summary>
/// 供应商费用
/// </summary>
[Description("供应商费用")]
public class SupplierFeeDto
{
    /// <summary>
    /// 费用类别
    /// </summary>
    [Description("费用类别")]
    public string FeeClass { get; set; } = string.Empty;

    /// <summary>
    /// 费用名称
    /// </summary>
    [Description("费用名称")]
    public string FeeName { get; set; } = string.Empty;

    /// <summary>
    /// 费用金额
    /// </summary>
    [Description("费用金额")]
    public decimal FeeAmount { get; set; }

    /// <summary>
    /// 货币类型
    /// </summary>
    [Description("货币类型")]
    public string Currency { get; set; } = "CNY";
}