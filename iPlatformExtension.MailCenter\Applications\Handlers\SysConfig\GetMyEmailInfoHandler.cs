﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using MailHost = iPlatformExtension.Model.MailCenter.MailHost;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    public class GetMyEmailInfoHandler(IFreeSql<MailCenterFreeSql> freeSql, IFreeSql<PlatformFreeSql> msSql, IHttpContextAccessor httpContextAccessor) : IRequestHandler<GetMyEmailInfoQuery, GetMailHostListDto>
    {
        public async Task<GetMailHostListDto> Handle(GetMyEmailInfoQuery request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var mailInfo = await freeSql.Select<MailHost>().Where(o => o.PrivateUserId == userId && o.IsPrivate).ToOneAsync<GetMailHostListDto>();
            if (mailInfo != null)
            {
                var users = await msSql.Select<SysUserInfo>()
                    .Where(o => o.UserId == mailInfo.CreateBy || o.UserId == mailInfo.UpdateBy)
                    .ToListAsync(o => new { UserId = o.UserId, CnName = o.CnName });
                mailInfo.CreateBy = users.FirstOrDefault(o => o.UserId == mailInfo.CreateBy)?.CnName;
                mailInfo.UpdateBy = users.FirstOrDefault(o => o.UserId == mailInfo.UpdateBy)?.CnName;
            }
            return mailInfo;
        }
    }
}
