using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_case", DisableSyncStructure = true)]
	public partial class MailCase {

		[ Column(Name = "mail_case_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string MailCaseId { get; set; }

		[ Column(Name = "input_time")]
		public DateTime? InputTime { get; set; }

		[ Column(Name = "input_user_id", StringLength = 50)]
		public string InputUserId { get; set; }

		[ Column(Name = "mail_case_type", StringLength = 50)]
		public string MailCaseType { get; set; }

		[ Column(Name = "mail_id", StringLength = 50)]
		public string MailId { get; set; }

		[ Column(Name = "obj_id", StringLength = 50)]
		public string ObjId { get; set; }

	}

}
