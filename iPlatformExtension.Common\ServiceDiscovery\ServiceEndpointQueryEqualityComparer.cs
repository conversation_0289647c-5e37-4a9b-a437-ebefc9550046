﻿using Microsoft.Extensions.ServiceDiscovery;

namespace iPlatformExtension.Common.ServiceDiscovery;

internal sealed class ServiceEndpointQueryEqualityComparer : IEqualityComparer<ServiceEndpointQuery>
{
    public bool Equals(ServiceEndpointQuery? x, ServiceEndpointQuery? y)
    {
        if (x is null)
        {
            return false;
        }

        if (y is null)
        {
            return false;
        }

        return x.ToString() == y.ToString();
    }

    public int GetHashCode(ServiceEndpointQuery obj)
    {
        return obj.ToString()!.GetHashCode();
    }
}