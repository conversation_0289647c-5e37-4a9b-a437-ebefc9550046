﻿using AutoMapper;
using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Commission.Application.Notifications.Trademark.Domestic;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class UpdateDomesticWeightCommandHandler(
    IMapper mapper,
    ISender sender,
    IHttpContextAccessor httpContextAccessor,
    IDomesticTrademarkCommissionRepository commissionRepository) 
    : IRequestHandler<UpdateDomesticWeightCommand>
{
    public async Task Handle(UpdateDomesticWeightCommand request, CancellationToken cancellationToken)
    {
        var userId = httpContextAccessor.HttpContext?.User.GetUserId();
        var (procId, patchDto) = request;
        var commission = await commissionRepository.GetAsync(procId, cancellationToken);

        if (commission is null)
        {
            throw new NotFoundException(procId, "权值数据");
        }

        if (commission.Pushed)
        {
            throw new ApplicationException($"任务{commission.ProcNo}权值数据已推送，无法修改");
        }

        var dto = mapper.Map<DomesticWeightPatchDto>(commission);
        patchDto.ApplyTo(dto);
        var updateUndertaker = dto.UndertakerId != commission.ProcMainUndertakerId;
        mapper.Map(dto, commission);

        if (updateUndertaker)
        {
            await sender.Send(new DomesticCommissionResultNotification(commission), cancellationToken);
        }
        
        commission.UpdateTime = DateTime.Now;
        commission.Updater = userId ?? string.Empty;
        
        await commissionRepository.UpdateAsync(commission, cancellationToken);
    }
}