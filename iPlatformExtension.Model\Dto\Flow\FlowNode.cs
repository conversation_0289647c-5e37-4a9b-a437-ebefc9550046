﻿namespace iPlatformExtension.Model.Dto.Flow;

/// <summary>
/// 某个流程下的节点数据
/// </summary>
public class FlowListNode
{
    /// <summary>
    /// 流程Id
    /// </summary>
    public string FlowId { get; set; } = default!;

    /// <summary>
    /// 部门Id
    /// </summary>
    public string DepartmentId { get; set; } = default!;

    /// <summary>
    /// 流程类型
    /// </summary>
    public string FlowType { get; set; } = default!;

    /// <summary>
    /// 流程子类型
    /// </summary>
    public string FlowSubType { get; set; } = default!;

    /// <summary>
    /// 节点Id
    /// </summary>
    public string NodeId { get; set; } = default!;

    /// <summary>
    /// 下个节点的序号
    /// </summary>
    public string Next { get; set; } = default!;

    /// <summary>
    /// 当前节点的序号
    /// </summary>
    public int Seq { get; set; }

    /// <summary>
    /// 关联的用户
    /// </summary>
    public string? Users { get; set; }

    /// <summary>
    /// 节点名称
    /// </summary>
    public string NodeName { get; set; } = default!;
}