﻿using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Outsourcing.Application.Commands.Fee;
using iPlatformExtension.Outsourcing.Application.Models.Fee;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Outsourcing.Controllers;

[Route("fees")]
[ApiController]
[Tags("供应商费项")]
public sealed class FeesController(ISender sender) : ControllerBase
{
    [HttpPost]
    [EndpointName(nameof(CreateSupplierFeesAsync))]
    [EndpointDescription("""
                         创建临时供应商费项数据。
                         每次返回一个费项id，用户后续更新任务或者删除临时费项。
                         临时费项数据不会入库，而是保存在redis缓存中。
                         """)]
    [EndpointSummary("创建供应商费项")]
    [Consumes(MediaTypeNames.Application.Json)]
    [ProducesResponseType<ResultData<SupplierFeesDto>>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    public Task<SupplierFeesDto> CreateSupplierFeesAsync(
        [FromQuery, Required] string ctrlProcId,
        [FromBody, Required] IEnumerable<SupplierFeeCreateDto> dtoList)
    {
        return sender.Send(new SupplierFeeCreateCommand(ctrlProcId, dtoList), HttpContext.RequestAborted);
    }
    
    [HttpDelete]
    [EndpointName(nameof(DeleteSupplierFeesAsync))]
    [EndpointDescription("""
                         删除临时供应商费项数据。
                         如果用户选择不创建费项则调用此接口。
                         根据创建时候返回的费项id，在redis中移除缓存
                         """)]
    [EndpointSummary("删除供应商费项")]
    [ProducesResponseType<ResultData>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    public Task DeleteSupplierFeesAsync(
        [FromQuery, Required] string feesId)
    {
        return sender.Send(new SupplierFeeDeleteCommand(feesId), HttpContext.RequestAborted);
    }
}