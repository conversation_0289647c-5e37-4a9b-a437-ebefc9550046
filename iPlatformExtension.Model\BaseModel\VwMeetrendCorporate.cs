using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_meetrend_Corporate", DisableSyncStructure = true)]
	public partial class VwMeetrendCorporate {

		[ Column(Name = "area_id", StringLength = 50)]
		public string AreaId { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string FACCOUNT { get; set; }

		[ Column(StringLength = 1000)]
		public string FACCOUNTADDRESS { get; set; }

		[ Column(StringLength = 1000)]
		public string FBANKNAME { get; set; }

		[ Column(StringLength = 50)]
		public string FCOMPANYID { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string FID { get; set; }

		[ Column(StringLength = 1000)]
		public string FNAME { get; set; }

		
		public bool FSTATUS { get; set; }

	}

}
