using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "file_code", DisableSyncStructure = true)]
	public partial class FileCode {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "code", StringLength = 50)]
		public string Code { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

	}

}
