using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_keyword_read", DisableSyncStructure = true)]
	public partial class SysKeywordRead {

		[ Column(Name = "read_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ReadId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "noun_id", StringLength = 50)]
		public string NounId { get; set; }

		[ Column(Name = "read_time")]
		public DateTime? ReadTime { get; set; }

		[ Column(Name = "read_user_id", StringLength = 50)]
		public string ReadUserId { get; set; }

	}

}
