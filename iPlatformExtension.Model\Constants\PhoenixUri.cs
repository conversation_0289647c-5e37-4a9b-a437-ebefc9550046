﻿namespace iPlatformExtension.Model.Constants;

/// <summary>
/// 权大师URI
/// </summary>
public static class PhoenixUri
{
    /// <summary>
    /// 尼斯分类
    /// </summary>
    public const string NiceCategories = "/product/niceNiceList";

    /// <summary>
    /// 法律条款
    /// </summary>
    public const string LawProvisions = "/getLawItemList";

    /// <summary>
    /// 下单
    /// </summary>
    public const string CreateOrder = "/createOrder";

    /// <summary>
    /// 把订单添加到待提交队列
    /// </summary>
    public const string SubmitOrder = "/doSubmit";

    /// <summary>
    /// 中止订单
    /// </summary>
    public const string StopOrder = "/overSubmit";

    /// <summary>
    /// 撤回递交到商标局上的订单
    /// </summary>
    public const string WithdrawOrder = "/returnSubmitOrder";

    /// <summary>
    /// 核验已撤回
    /// </summary>
    public const string CheckWithdraw = "/checkReturnOrder";

    /// <summary>
    /// 取消订单
    /// </summary>
    public const string CancelOrder = "/cancelOrder";

    /// <summary>
    /// 获取订单信息
    /// </summary>
    public const string GetOrder = "/getOrderInfo";

    /// <summary>
    /// 商标注销
    /// </summary>
    public const string CancelTrademark = "/createSBZXOrder";

    /// <summary>
    /// 商标更正
    /// </summary>
    public const string CorrectionTrademark = "/createSBZXOrder";

    /// <summary>
    /// 撤回转让/转移
    /// </summary>
    public const string WithdrawTrademark = "/createCHSBZRZYSQOrder";

    /// <summary>
    /// 撤回许可备案
    /// </summary>
    public const string WithdrawalLicenseFiling = "/createCHXKOrder";

    /// <summary>
    /// 撤回变更代理人
    /// </summary>
    public const string WithdrawalChangeAgent = "/createChbgdlrwjjsrOrder";

    /// <summary>
    /// 变更许可人
    /// </summary>
    public const string ChangeLicensor = "/createBgxkrbxkrOrder";

    /// <summary>
    /// 删减商品项或服务项目申请
    /// </summary>
    public const string DeleteProduct = "/createSJSPOrder";

    /// <summary>
    /// 撤回商标续展申请
    /// </summary>
    public const string WithdrawalTrademarkRenewal = "/createCHSBXZSQOrder";

    /// <summary>
    /// 补发变更/转让/续展证明申请
    /// </summary>
    public const string ReissueChangeCertificate = "/createBFZMOrder";

    /// <summary>
    /// 许可提前终止备案
    /// </summary>
    public const string EarlyTerminationLicense = "/createSbsyxktqzzOrder";

    /// <summary>
    /// 商标许可备案
    /// </summary>
    public const string TrademarkLicenseFiling = "/createXKBAOrder";

    /// <summary>
    /// 出具优先权证明申请
    /// </summary>
    public const string IssuePriorityCertificate = "/createCjyxqsqOrder";

    /// <summary>
    /// 商标续展申请
    /// </summary>
    public const string TrademarkRenewal = "/trademarkExtensionOrder";

    /// <summary>
    /// 商标驳回复审申请
    /// </summary>
    public const string TrademarkRefuseReexamination = "/createBHFSOrder";

    /// <summary>
    /// 商标变更下单
    /// </summary>
    public const string ChangeOrder = "/createChangeOrder";

    /// <summary>
    /// 商标异议申请下单
    /// </summary>
    public const string TrademarkObjections = "/createYYSQOrder";

    /// <summary>
    /// 商标撤销三年下单
    /// </summary>
    public const string WithdrawThreeYears = "/createCSSQOrder";

    /// <summary>
    /// 商标转让申请
    /// </summary>
    public const string TransferOrder = "/createTransferOrder";

    /// <summary>
    /// 商标无效宣告下单
    /// </summary>
    public const string InvalidationOrder = "/createWXSQOrder";

    /// <summary>
    /// 变更代理人、文件接收人
    /// </summary>
    public const string ChangeAgentReceiverOrder = "/createBgdlrwjjsrOrder";

    /// <summary>
    /// 撤回注册申请
    /// </summary>
    public const string WithdrawRegistrationOrder = "/createCHSBZXOrder";

    /// <summary>
    /// 商标补发证书下单
    /// </summary>
    public const string ReplenishCertificationOrder = "/createBFSBZCZSOrder";
}