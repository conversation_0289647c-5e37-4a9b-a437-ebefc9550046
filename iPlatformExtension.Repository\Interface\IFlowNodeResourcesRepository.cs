﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface IFlowNodeResourcesRepository :
    IBaseRepository<FlowNodeResources, int>,
    ICacheableRepository<string, IEnumerable<FlowNodeResources>>,
    IScopeDependency
{
    string ICacheableRepository<string, IEnumerable<FlowNodeResources>>.GenerateKey(IEnumerable<FlowNodeResources> value)
    {
        if (value is IGrouping<string, FlowNodeResources> group)
            return group.Key;

        return value.GroupBy(resource => resource.NodeId).Single().Key;
    }

    async Task<IEnumerable<FlowNodeResources>?> ICacheableRepository<string, IEnumerable<FlowNodeResources>>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return await Select.Where(resource => resource.NodeId == key).ToListAsync(cancellationToken);
    }

    Task<IEnumerable<IEnumerable<FlowNodeResources>>> ICacheableRepository<string, IEnumerable<FlowNodeResources>>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return Task.FromResult(
            Array.Empty<IEnumerable<FlowNodeResources>>() as IEnumerable<IEnumerable<FlowNodeResources>>);
    }

    IEqualityComparer<string>? ICacheableRepository<string, IEnumerable<FlowNodeResources>>.KeyEqualityComparer => StringComparer.OrdinalIgnoreCase;
}