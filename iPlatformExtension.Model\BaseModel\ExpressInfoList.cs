using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "express_info_list", DisableSyncStructure = true)]
	public partial class ExpressInfoList {

		[ Column(Name = "app_date")]
		public DateTime? AppDate { get; set; }

		[ Column(Name = "app_no", StringLength = 50)]
		public string AppNo { get; set; }

		[ Column(Name = "applicant_name", StringLength = 500)]
		public string ApplicantName { get; set; }

		[ Column(Name = "apply_type", StringLength = 50)]
		public string ApplyType { get; set; }

		[ Column(Name = "case_name", StringLength = 200)]
		public string CaseName { get; set; }

		[ Column(Name = "create_time", DbType = "date")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50, IsNullable = false)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_case_no", StringLength = 50)]
		public string CustomerCaseNo { get; set; }

		[ Column(Name = "express_id", StringLength = 50, IsNullable = false)]
		public string ExpressId { get; set; }

		[ Column(Name = "fax", StringLength = 50)]
		public string Fax { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; }

		[ Column(Name = "is_accessory")]
		public bool IsAccessory { get; set; }

		[ Column(Name = "obj_code", StringLength = 50, IsNullable = false)]
		public string ObjCode { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; }

		[ Column(Name = "obj_name", StringLength = 50)]
		public string ObjName { get; set; }

		[ Column(Name = "obj_num", StringLength = 50, IsNullable = false)]
		public string ObjNum { get; set; }

		[ Column(Name = "seq", IsIdentity = true)]
		public int Seq { get; set; }

		[ Column(Name = "trademark_class", StringLength = 200)]
		public string TrademarkClass { get; set; }

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

	}

}
