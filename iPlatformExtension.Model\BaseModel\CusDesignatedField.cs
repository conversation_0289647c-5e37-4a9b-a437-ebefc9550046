using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_designated_field", DisableSyncStructure = true)]
	public partial class CusDesignatedField {

		[ Column(Name = "designated_id", StringLength = 50, IsNullable = false)]
		public string DesignatedId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "tech_field_id", StringLength = 50)]
		public string TechFieldId { get; set; }

	}

}
