﻿using iPlatformExtension.Common.Db.Log;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Mediator.Notifications;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace iPlatformExtension.Common.Extensions;

public static class EntityExtension
{
    public static IServiceCollection AddEntityChangeLogs(this IServiceCollection services)
    {
        ArgumentNullException.ThrowIfNull(services);

        services.TryAddTransient<INotificationHandler<EntityChangeNotification>, EntityChangeLoggingHandler>();
        services.TryAddSingleton<EntityTypeInfoProvider>();

        return services;
    }

    /// <summary>
    /// 版本号验证
    /// </summary>
    /// <param name="entity">带版本号的实体</param>
    /// <param name="currentVersion">当前版本号</param>
    /// <exception cref="VersionException">当前版本号和实际版本号不一致抛出异常</exception>
    public static void ValidateVersion(this IVersionEntity entity, int currentVersion)
    {
        if (entity.Version != currentVersion)
        {
            throw new VersionException(currentVersion, entity.Version);
        }
    }
}