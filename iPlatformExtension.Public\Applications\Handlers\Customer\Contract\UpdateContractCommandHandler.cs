﻿using AutoMapper;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Public.Applications.Commands.Customer.Contract;
using iPlatformExtension.Public.Applications.Models.Customer.Contract;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Customer.Contract;

internal sealed class UpdateContractCommandHandler(
    IMapper mapper, 
    IUserInfoRepository userInfoRepository,
    ICusContractRepository contractRepository) : IRequestHandler<UpdateContractCommand>
{
    public async Task Handle(UpdateContractCommand request, CancellationToken cancellationToken)
    {
        var (crmContractId, document) = request;
        var contract = await contractRepository.Where(contract => contract.CrmContractId == crmContractId).ToOneAsync(cancellationToken);

        if (contract is null)
        {
            throw new NotFoundException(crmContractId, "CRM合同信息");
        }

        var patchDto = mapper.Map<CrmContractPatchDto>(contract);
        document.ApplyTo(patchDto);

        mapper.Map(patchDto, contract);
        contract.UpdateTime = DateTime.Now;
        contract.UpdateUserId = await userInfoRepository.GetUserIdByUserNameAsync(patchDto.Operator) ?? string.Empty;
        
        await contractRepository.UpdateAsync(contract, cancellationToken);
    }
}