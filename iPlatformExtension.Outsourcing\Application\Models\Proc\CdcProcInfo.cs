﻿namespace iPlatformExtension.Outsourcing.Application.Models.Proc;

public class CdcProcInfo
{
    /// <summary>
    /// 任务id
    /// </summary>
    public string ProcId { get; set; } = string.Empty;

    /// <summary>
    /// 案件id
    /// </summary>
    public string CaseId { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称id
    /// </summary>
    public string CtrlProcId { get; set; } = string.Empty;

    /// <summary>
    /// 境外代理id
    /// </summary>
    public string? ForeignAgencyId { get; set; }
}