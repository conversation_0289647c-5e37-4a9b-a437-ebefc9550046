using FreeSql;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Commands.Fees;

/// <summary>
/// 构建官费带出代理费
/// </summary>
/// <param name="Dto"></param>
/// <param name="FeesQuery"></param>
internal sealed record BuildFeeClassCommand(FeeQueryDto Dto, ISelect<CaseFeeList> FeesQuery) : IRequest, IBuildFeeQueryCommand;