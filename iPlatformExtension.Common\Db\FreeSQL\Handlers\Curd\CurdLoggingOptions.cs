﻿using FreeSql.Aop;

namespace iPlatformExtension.Common.Db.FreeSQL.Handlers.Curd;

public class CurdLoggingOptions
{
    /// <summary>
    /// 执行时间阈值。大于该阈值就做记录
    /// </summary>
    public long ExecuteDurationThreshold { get; set; } = 5000;

    /// <summary>
    /// CURD类型。针对相应的CURD类型才做记录。
    /// </summary>
    public IList<CurdType> IncludeCurdTypes { get; init; } = new List<CurdType>();

    /// <summary>
    /// 不做记录的实体类型名称
    /// </summary>
    public IList<string> ExcludeEntityTypes { get; init; } = new List<string>();

    /// <summary>
    /// 执行环境。判断对应的环境才做记录
    /// </summary>
    public IList<string> Environments { get; init; } = new List<string>();

    /// <summary>
    /// 在CURD前做记录
    /// </summary>
    public bool LogCurdBefore { get; set; }

    /// <summary>
    /// 在CURD后做记录
    /// </summary>
    public bool LogCurdAfter { get; set; } = true;
}