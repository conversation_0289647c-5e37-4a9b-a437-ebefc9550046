﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.Clients.WxWork
{
    public class WxConfig
    {
        public string Corpid { get; set; }
        public string Corpsecret { get; set; }
        public string Agentid { get; set; }
        public string RedirectUri { get; set; }
        public string RedirctTargetName { get; set; }

        /// <summary>
        /// 机器人
        /// </summary>
        public List<Robot> Robots { get; set; }
    }

    public class Robot
    {
        /// <summary>
        /// 生效范围
        /// </summary>
        public string Scope { get; set; }
        public string MessageTitle { get; set; }
        public List<Group> Groups { get; set; }
    }

    public class Group
    {
        /// <summary>
        /// 群组名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 机器人key,勿泄露
        /// </summary>
        public string Key { get; set; }
        /// <summary>
        /// 是否开启发送消息
        /// </summary>
        public bool IsOpen { get; set; }
    }
}
