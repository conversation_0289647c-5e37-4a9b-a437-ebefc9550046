﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class SubmitDeliveryValidateCommandHandler(
    IMediator mediator,
    IRedisCache<RedisCacheOptionsBase> redisCache)
    : IRequestHandler<SubmitDeliveryValidateCommand, BatchDeliveryValidateResult>
{
    public async Task<BatchDeliveryValidateResult> Handle(SubmitDeliveryValidateCommand request, CancellationToken cancellationToken)
    {
        var deliveryItems = await mediator.Send(new BatchDeliveryValidationQuery(request.ProcIds), cancellationToken);

        var validateResult =
            await mediator.Send(new MultipartCtrlProcValidateCommand(deliveryItems), cancellationToken);
        if (!validateResult.Success)
        {
            return validateResult;
        }

        validateResult.Success = deliveryItems.Where(item => item.CurrentNodeId is not null).GroupBy(item => item.CurrentNodeId).Count() == 1;
        if (!validateResult.Success)
        {
            return validateResult.MultipartNode();
        }

        validateResult = await mediator.Send(new UndertakerValidateCommand(deliveryItems), cancellationToken);
        if (!validateResult.Success)
        {
            return validateResult;
        }

        validateResult = await deliveryItems.ToAsyncEnumerable().WhereAwait(async dto =>
        {
            var locked =
                (await redisCache.GetCacheValueAsync<string, long?>(LockKey.DeliveringLockKey, dto.ProcId) ?? 0) >
                DateTimeOffset.Now.ToUnixTimeMilliseconds();

            var deliveryStatus = (DeliveryStatus) dto.DeliveryStatus;
            return deliveryStatus == DeliveryStatus.Ordered || deliveryStatus == DeliveryStatus.Delivering || locked;
        }).AggregateAsync(validateResult, (result, dto) => result.CannotSubmit(dto.ProcNo), cancellationToken);

        return validateResult;
    }
}