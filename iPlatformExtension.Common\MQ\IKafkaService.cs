﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Confluent.Kafka;
using iPlatformExtension.Common.MQ;
using iPlatformExtension.Common.MQ.KafKa.Models;

namespace KafkaProvider
{
    public interface IKafkaService
    {
        /// <summary>
        /// 发送消息至指定主题
        /// </summary>
        /// <typeparam name="TMessage"></typeparam>
        /// <param name="topicName"></param>
        /// <param name="message"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task PublishAsync<TMessage>(string topicName, MessageKey Key, TMessage message, CancellationToken cancellationToken) where TMessage : class;

        /// <summary>
        /// 从指定主题订阅消息
        /// </summary>
        /// <typeparam name="TMessage">返回消息模型</typeparam>
        /// <param name="topics">主题</param>
        /// <param name="messageFunc"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task SubscribeAsync<TMessage>(IEnumerable<string> topics, Action<TMessage> messageFunc, CancellationToken cancellationToken) where TMessage : class;

        /// <summary>
        /// 设置生产者配置
        /// </summary>
        /// <param name="bootstrapServers">服务器地址</param>
        ProducerConfig GetProducerConfig(string bootstrapServers);

        /// <summary>
        /// 设置消费者配置
        /// </summary>
        /// <param name="config"></param>
        ConsumerConfig GetConsumerConfig(string bootstrapServers, string groupId = "Group1");
    }
}
