using System.Globalization;
using iPlatformExtension.Finance.Applications.Commands.Commission;
using iPlatformExtension.Finance.Applications.Models.Commission;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Commission;

internal sealed class SaveIncomesCommandHandler : IRequestHandler<SaveIncomesCommand>
{
    private readonly IMediator _mediator;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="mediator">中介者</param>
    public SaveIncomesCommandHandler(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// 收入明细保存处理
    /// </summary>
    /// <param name="request">划拨数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task Handle(SaveIncomesCommand request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return;
        
        var feeIds = request.IncomeInfos.Select(dto => dto.FeeId).ToArray();
        var feeInfos = await _mediator.Send(new FeesQuery(new FeeQueryDto()
        {
            FeeIds = feeIds
        }), cancellationToken);
        
        
        var incomeInfos = feeInfos.Join(request.IncomeInfos, itemDto => itemDto.FeeId, incomeDto => incomeDto.FeeId,
            (itemDto, incomeDto) =>
            {
                var result = new MPaymentDetail()
                {
                    FID = itemDto.FeeId,
                    FUSERID = GetSalesOrFeesTracker(incomeDto, SalesOrTracker.Sales),
                    FCOMPANYID = incomeDto.PayeeId,
                    FCASEID = itemDto.CaseId,
                    FDATE = incomeDto.ReceiveDate,
                    FCUSTOMERID = incomeDto.CustomerId,
                    FCASETYPE = itemDto.CaseType.Value,
                    FCASEFLOW = itemDto.CaseDirection.Value,
                    FAPPLYTYPE = CaseType.Project.Equals(itemDto.CaseType.Key, StringComparison.OrdinalIgnoreCase) 
                        ? itemDto.BusinessType.Value : itemDto.ApplyType.Value,
                    FBUTYPE = itemDto.BusinessType.Value,
                    FTASKTYPE = itemDto.ProcAttribute.Value,
                    FDOUBLETYPE = itemDto.IsSameDayCase ? "是" : "否",
                    FCHARGEDETAIL = itemDto.FeeName.CnName,
                    FINVOCIE = incomeDto.HasInvoice ? "是" : "否",
                    FSOURCETYPE = itemDto.FeesCaseSourceType.Value,
                    FARRIVALTYPE = incomeDto.PaymentMethod,
                    FARRIVALAMO = GetFeeAmount(incomeDto, "O"),
                    FAGENCYAMO = GetFeeAmount(incomeDto, "A"),
                    FTHIRDCOST = GetFeeAmount(incomeDto, "T"),
                    FUPDATETIME = DateTime.Now,
                    FFLOWPERSONID = GetSalesOrFeesTracker(incomeDto, SalesOrTracker.Tracker),
                    FCLUEUSERID = incomeDto.ClueUser,
                    FBUSIUSERID = incomeDto.BusinessUser
                };
                return result;
            }).ToArray();

        await _mediator.Send(new InsertIncomesCommand(incomeInfos), cancellationToken);
    }
    
    private static float GetFeeAmount(FeesIncomeDto feesIncomeDto, string feeClass) =>
        feesIncomeDto.FeeClass == feeClass ?  Convert.ToSingle(Math.Round(feesIncomeDto.Amount, 2).ToString(CultureInfo.CurrentCulture)) : 0F;

    private static string? GetSalesOrFeesTracker(FeesIncomeDto incomeInfo, SalesOrTracker salesOrTracker)
    {
        // 收款主体是杭州的特殊处理
        if (incomeInfo.PayeeId is "740e5671-e1a5-47c2-8c79-dd0b01af4d55" or "7a986ff8-c554-40c3-b684-41046c56fc5f")
        {
            return "be72114f-62f8-4997-b040-be471bb66385";
        }

        return salesOrTracker switch
        {
            SalesOrTracker.Sales => incomeInfo.Sales,
            SalesOrTracker.Tracker => incomeInfo.Tracker,
            _ => throw new ArgumentOutOfRangeException(nameof(salesOrTracker), salesOrTracker, "只能是销售或者跟案人。超出了枚举范围")
        };
    }
    
    private enum SalesOrTracker : byte
    {
        Sales, Tracker
    }
}