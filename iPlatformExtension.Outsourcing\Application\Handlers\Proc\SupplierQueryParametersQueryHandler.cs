﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class SupplierQueryParametersQueryHandler(IFreeSql<PlatformFreeSql> freeSql) : IRequestHandler<SupplierQueryParametersQuery, SupplierQueryParameters>
{
    public async Task<SupplierQueryParameters> Handle(SupplierQueryParametersQuery request, CancellationToken cancellationToken)
    {
        var procInfo = await freeSql.Select<CaseProcInfo>(request.ProcId).WithLock()
            .Include(procInfo => procInfo.CaseInfo).ToOneAsync(procInfo => new CaseProcInfo
            {
                ProcId = procInfo.ProcId,
                CtrlProcId = procInfo.CtrlProcId,
                CaseInfo = new CaseInfo
                {
                    Id = procInfo.CaseInfo.Id,
                    TrademarkClass = procInfo.CaseInfo.TrademarkClass,
                    CustomerId = procInfo.CaseInfo.CustomerId,
                    ApplyChannel = procInfo.CaseInfo.ApplyChannel,
                    CountryId = procInfo.CaseInfo.CountryId,
                    MemberCountry = procInfo.CaseInfo.MemberCountry
                }
            }, cancellationToken);

        return new SupplierQueryParameters(procInfo);
    }
}