using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_due_time", DisableSyncStructure = true)]
	public partial class CusDueTime {

		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "base_field", StringLength = 50)]
		public string BaseField { get; set; }

		[ Column(Name = "case_direction", StringLength = 50, IsNullable = false)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_type_id", StringLength = 50, IsNullable = false)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50, IsNullable = false)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "ctrl_proc_property", StringLength = 50)]
		public string CtrlProcProperty { get; set; }

		[ Column(Name = "customer_id", StringLength = 50, IsNullable = false)]
		public string CustomerId { get; set; }

		[ Column(Name = "date_type", StringLength = 50, IsNullable = false)]
		public string DateType { get; set; }

		[ Column(Name = "date_value")]
		public int DateValue { get; set; } = 0;

		[ Column(Name = "due_field", StringLength = 50)]
		public string DueField { get; set; }

		[ Column(Name = "due_id", StringLength = 50, IsNullable = false)]
		public string DueId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

	}

}
