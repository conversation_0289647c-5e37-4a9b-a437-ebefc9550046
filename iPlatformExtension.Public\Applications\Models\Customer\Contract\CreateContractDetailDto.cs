﻿using System.ComponentModel;

namespace iPlatformExtension.Public.Applications.Models.Customer.Contract;

/// <summary>
/// 合同明细
/// </summary>
[Description("合同明细")]
public class CreateContractDetailDto
{
    /// <summary>
    /// 业务类型
    /// </summary>
    [Description("业务类型")]
    public required string BusinessType { get; set; }

    /// <summary>
    /// 售前支持人员（工号）
    /// </summary>
    [Description("售前支持人员（工号）")]
    public string? PreSalesUser { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [Description("备注")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 生效日期
    /// </summary>
    [Description("生效日期")]
    public required DateOnly EffectiveDate { get; set; }

    /// <summary>
    /// 修改人（工号）
    /// </summary>
    [Description("修改人（工号）")]
    public required string Updater { get; set; }
    
}