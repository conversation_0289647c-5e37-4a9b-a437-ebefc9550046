﻿using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.MailCenter.Infrastructure.Extension;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Message
{
    public class ReaderMessageConsumerHandler(IWxWorkClient wxWorkClientExtension, IFreeSql<PlatformFreeSql> msSql) : IRequestHandler<ReaderChangeMessageQuery>
    {
        public async Task Handle(ReaderChangeMessageQuery request, CancellationToken cancellationToken)
        {
            var operatorUser = await GetName(request.MailCenterMessageContent.OperatorUser, false);
            var RecipientUser = await GetName(request.MailCenterMessageContent.RecipientBy, false);
            var UndertakeUser = await <PERSON>N<PERSON>(request.MailCenterMessageContent.Undertaker, false);
            TextCardContent news = null;

            ArgumentNullException.ThrowIfNull(request.MailCenterMessageContent.MailNo);

            var mailSubject = request.MailCenterMessageContent.MailTitle;
            if (mailSubject.Length >= 20)
            {
                mailSubject = $"{request.MailCenterMessageContent.MailTitle.Substring(0, 20)}..."; ;
            }

            switch (request.MailCenterMessageContent.OperationType)
            {
                case Model.Enum.OperationTypeEnum.Submit:
                case Model.Enum.OperationTypeEnum.Reject:
                case Model.Enum.OperationTypeEnum.Transfer:
                    news = new TextCardContent()
                    {
                        title = $"邮件中心-收件办理",
                        description = $"<div class=\"black\">请您办理收件\"{request.MailCenterMessageContent.MailNo}\"。</div>" +
                                      $" <div class=\"black\">收件主题：{mailSubject}</div>" +
                                      $"<div class=\"black\">操作人：{operatorUser}</div>" +
                                      $"<div class=\"black\">操作方式：{EnumTool.GetDescription(request.MailCenterMessageContent.OperationType)}</div>" +
                                      $"<div class=\"black\">操作时间：{request.MailCenterMessageContent.DateTime}</div>" +
                                      $"<div class=\"black\">办理建议：{request.MailCenterMessageContent.Remark}</div>" +
                                      $"<div class=\"black\">接收人：{RecipientUser}</div>"
                    };
                    break;

                case Model.Enum.OperationTypeEnum.Sort:
                    news = new TextCardContent()
                    {
                        title = $"邮件中心-收件阅读",
                        description = $"<div class=\"black\">收件\"{request.MailCenterMessageContent.MailNo}\"已分拣。您是阅读人，请您阅读。</div>" +
                                      $" <div class=\"black\">收件主题：{mailSubject}</div>" +
                                      $"<div class=\"black\">分拣人：{operatorUser}</div>" +
                                      $"<div class=\"black\">分拣时间：{request.MailCenterMessageContent.DateTime}</div>" +
                                      $"<div class=\"black\">阅读人：{RecipientUser}</div>"
                    };
                    break;
                case Model.Enum.OperationTypeEnum.Add:
                    news = new TextCardContent()
                    {
                        title = $"邮件中心-收件阅读",
                        description = $"<div class=\"black\">您已被添加为收件\"{request.MailCenterMessageContent.MailNo}\"的阅读人，请您阅读。</div>" +
                                      $" <div class=\"black\">收件主题：{mailSubject}</div>" +
                                      $"<div class=\"black\">添加人：{operatorUser}</div>" +
                                      $"<div class=\"black\">添加时间：{request.MailCenterMessageContent.DateTime}</div>" +
                                      $"<div class=\"black\">阅读人：{RecipientUser}</div>"
                    };
                    break;
                case Model.Enum.OperationTypeEnum.Delete:
                    news = new TextCardContent()
                    {
                        title = $"邮件中心-收件阅读",
                        description = $"<div class=\"black\">您已从收件\"{request.MailCenterMessageContent.MailNo}\"的阅读人中被删除，无需您阅读。</div>" +
                                      $" <div class=\"black\">收件主题：{mailSubject}</div>" +
                                      $"<div class=\"black\">删除人：{operatorUser}</div>" +
                                      $"<div class=\"black\">删除时间：{request.MailCenterMessageContent.DateTime}</div>" +
                                      $"<div class=\"black\">阅读人：{RecipientUser}</div>"
                    };
                    break;
                case Model.Enum.OperationTypeEnum.Ignore:
                    news = new TextCardContent()
                    {
                        title = $"邮件中心-收件阅读",
                        description = $"<div class=\"black\">收件\"{request.MailCenterMessageContent.MailNo}\"已被忽略，无需您阅读。</div>" +
                                      $" <div class=\"black\">收件主题：{mailSubject}</div>" +
                                      $"<div class=\"black\">忽略人：{operatorUser}</div>" +
                                      $"<div class=\"black\">忽略时间：{request.MailCenterMessageContent.DateTime}</div>" +
                                      $"<div class=\"black\">阅读人：{RecipientUser}</div>"
                    };
                    break;

                case Model.Enum.OperationTypeEnum.SentAudit:
                    news = new TextCardContent()
                    {
                        title = $"邮件中心-发件审核",
                        description = $"<div class=\"black\">请您审核发件\"{request.MailCenterMessageContent.MailNo}\"。</div>" +
                                      $" <div class=\"black\">发件主题：{mailSubject}</div>" +
                                      $"<div class=\"black\">操作人：{operatorUser}</div>" +
                                      $"<div class=\"black\">操作方式：提交</div>" +
                                      $"<div class=\"black\">操作时间：{request.MailCenterMessageContent.DateTime}</div>" +
                                      $"<div class=\"black\">办理建议：{request.MailCenterMessageContent.Remark}</div>" +
                                      $"<div class=\"black\">接收人：{RecipientUser}</div>"
                    };
                    break;

                //case Model.Enum.OperationTypeEnum.ScheduledSent:
                //    news = new TextCardContent()
                //    {
                //        title = $"邮件中心-审核通过，定时发送",
                //        description = $"<div class=\"black\">您的发件\"{request.MailCenterMessageContent.MailNo}\"已审核通过，到定时时间后自动发送。</div>" +
                //                      $" <div class=\"black\">发件主题：{mailSubject}</div>" +
                //                      $"<div class=\"black\">发件人：{operatorUser}</div>" +
                //                      $"<div class=\"black\">承办人：提交</div>" +
                //                      $"<div class=\"black\">审核人：{UndertakeUser}</div>" +
                //                      $"<div class=\"black\">定时发送时间：{RecipientUser}</div>"
                //    };
                //    break;

                //case Model.Enum.OperationTypeEnum.SendError:
                //    news = new TextCardContent()
                //    {
                //        title = $"邮件中心-发送失败",
                //        description = $"<div class=\"black\">您的发件\"{request.MailCenterMessageContent.MailNo}\"发送失败，请处理。</div>" +
                //                      $" <div class=\"black\">发件主题：{mailSubject}</div>" +
                //                      $"<div class=\"black\">发件人：{operatorUser}</div>" +
                //                      $"<div class=\"black\">承办人：{UndertakeUser}</div>"
                //    };
                //    break;

                case Model.Enum.OperationTypeEnum.Send:
                    news = new TextCardContent()
                    {
                        title = $"邮件中心-已发送",
                        description = $"<div class=\"black\">您的发件\"{request.MailCenterMessageContent.MailNo}\"已发送。</div>" +
                                      $" <div class=\"black\">发件主题：{mailSubject}</div>" +
                                      $"<div class=\"black\">发件人：{request.MailCenterMessageContent.SentMail}</div>" +
                                      $"<div class=\"black\">承办人：{UndertakeUser}</div>"
                    };
                    break;

                //case Model.Enum.OperationTypeEnum.SentAddReader:
                //    news = new TextCardContent()
                //    {
                //        title = $"邮件中心-发件阅读",
                //        description = $"<div class=\"black\">您的发件\"{request.MailCenterMessageContent.MailNo}\"已发送，您是阅读人，请您阅读。</div>" +
                //                      $" <div class=\"black\">发件主题：{mailSubject}</div>" +
                //                      $"<div class=\"black\">发件人：{operatorUser}</div>" +
                //                      $"<div class=\"black\">承办人：{UndertakeUser}</div>" +
                //                      $"<div class=\"black\">阅读人：{RecipientUser}</div>"
                //    };
                //    break;

                case Model.Enum.OperationTypeEnum.SendAddReader:
                    news = new TextCardContent()
                    {
                        title = $"邮件中心-发件阅读",
                        description = $"<div class=\"black\">您已被添加为发件\"{request.MailCenterMessageContent.MailNo}\"的阅读人，请您阅读。</div>" +
                                      $" <div class=\"black\">发件主题：{mailSubject}</div>" +
                                      $"<div class=\"black\">发件人：{operatorUser}</div>" +
                                      $"<div class=\"black\">承办人：{UndertakeUser}</div>" +
                                      $"<div class=\"black\">阅读人：{RecipientUser}</div>"
                    };
                    break;

                case Model.Enum.OperationTypeEnum.SendDelReader:
                    news = new TextCardContent()
                    {
                        title = $"邮件中心-发件阅读",
                        description = $"<div class=\"black\">您已从发件\"{request.MailCenterMessageContent.MailNo}\"的阅读人中被删除，无需您阅读。</div>" +
                                      $" <div class=\"black\">发件主题：{mailSubject}</div>" +
                                      $"<div class=\"black\">发件人：{operatorUser}</div>" +
                                      $"<div class=\"black\">承办人：{UndertakeUser}</div>" +
                                      $"<div class=\"black\">阅读人：{RecipientUser}</div>"
                    };
                    break;

                case Model.Enum.OperationTypeEnum.SendReject:
                    news = new TextCardContent()
                    {
                        title = $"邮件中心-退回发件草稿",
                        description = $"<div class=\"black\">您审核过的发件\"{request.MailCenterMessageContent.MailNo}\"已被承办人退回草稿。</div>" +
                                      $" <div class=\"black\">发件主题：{mailSubject}</div>" +
                                      $"<div class=\"black\">承办人：{UndertakeUser}</div>" +
                                      $"<div class=\"black\">审核人：{RecipientUser}</div>"
                    };
                    break;

                case Model.Enum.OperationTypeEnum.SentInvalid:
                    news = new TextCardContent()
                    {
                        title = $"邮件中心-作废发件",
                        description = $"<div class=\"black\">您审核过的发件\"{request.MailCenterMessageContent.MailNo}\"已被承办人作废。</div>" +
                                      $" <div class=\"black\">发件主题：{mailSubject}</div>" +
                                      $"<div class=\"black\">承办人：{UndertakeUser}</div>" +
                                      $"<div class=\"black\">审核人：{RecipientUser}</div>" +
                                      $"<div class=\"black\">作废时间：{request.MailCenterMessageContent.DateTime}</div>"
                    };
                    break;
            }

            var RecipientUserName = await GetName(request.MailCenterMessageContent.RecipientBy);
            var redirectConfig = new RedirectConfig
            {
                code = "EmailDetails",
                title = mailSubject,
                id = request.MailCenterMessageContent.MailId,
                search = $"mailId={request.MailCenterMessageContent.MailId}"
            };

            var res = await wxWorkClientExtension.SendCardMessageAsync(RecipientUserName, news, redirectConfig);

            Console.WriteLine($"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}企业微信发送:{res.errmsg}");

            return;
        }
        private async Task<string> GetName(string userId, bool isUserName = true)
        {
            var userinfo = await msSql.Select<SysUserInfo>().Where(o => o.UserId == userId).ToOneAsync();
            if (userinfo != null)
            {
                return isUserName ? userinfo.UserName : userinfo.CnName;
            }
            return userId;
        }
    }
}
