using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_file_desc", DisableSyncStructure = true)]
	public partial class BasFileDesc {

		/// <summary>
		/// 文件描述主键ID
		/// </summary>
		[ Column(Name = "file_desc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FileDescId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 案件类型
		/// </summary>
		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		/// <summary>
		/// 国家
		/// </summary>
		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 文件描述英文
		/// </summary>
		[ Column(Name = "file_desc_en_us", StringLength = 500)]
		public string FileDescEnUs { get; set; }

		/// <summary>
		/// 文件描述日文
		/// </summary>
		[ Column(Name = "file_desc_ja_jp", StringLength = 500)]
		public string FileDescJaJp { get; set; }

		/// <summary>
		/// 文件描述中文
		/// </summary>
		[ Column(Name = "file_desc_zh_cn", StringLength = 500)]
		public string FileDescZhCn { get; set; }

		/// <summary>
		/// 默认文件名称
		/// </summary>
		[ Column(Name = "file_name_default", StringLength = 50)]
		public string FileNameDefault { get; set; }

		/// <summary>
		/// 文件后缀类型
		/// </summary>
		[ Column(Name = "file_suffix", StringLength = 50)]
		public string FileSuffix { get; set; }

		/// <summary>
		/// 文件类型
		/// </summary>
		[ Column(Name = "file_type_id", StringLength = 50)]
		public string? FileTypeId { get; set; }
		
		/// <summary>
		/// 文件类型
		/// </summary>
		[Navigate(nameof(FileTypeId))]
		public virtual BasFileType? FileType { get; set; }

		/// <summary>
		/// 是否有期限
		/// </summary>
		[ Column(Name = "has_due_time")]
		public bool HasDueTime { get; set; } = false;

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// OA类型
		/// </summary>
		[ Column(Name = "oa_type_id", StringLength = 50)]
		public string OaTypeId { get; set; }

		/// <summary>
		/// 页编码
		/// </summary>
		[ Column(Name = "page_code", StringLength = 50)]
		public string PageCode { get; set; }

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		/// <summary>
		/// 文件描述编码
		/// </summary>
		[ Column(Name = "text_code", StringLength = 50)]
		public string? TextCode { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
