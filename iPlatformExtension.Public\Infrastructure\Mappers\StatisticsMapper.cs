﻿using AutoMapper;
using iPlatformExtension.Public.Applications.Models.Statistics;

namespace iPlatformExtension.Public.Infrastructure.Mappers
{
    /// <summary>
    /// 统计mapper
    /// </summary>
    public sealed class StatisticsMapper : Profile
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public StatisticsMapper()
        {
            CreateMap<OverDateWarningDto, IntFirstDateWarning>();
            CreateMap<OverDateWarningDto, CusFirstDateWarning>();
            CreateMap<OverDateWarningDto, IntFinishDateWarning>();
            CreateMap<OverDateWarningDto, CusFinishDateWarning>();
            CreateMap<OverDateWarningDto, LegalDueDateWarning>();
            CreateMap<OverDateWarningDto, IntFinishOverDate>();
            CreateMap<OverDateWarningDto, IntFirstOverDate>();
        }
    }
}
