﻿namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 申请人证书类型
/// </summary>
public sealed class ApplicantCertificationType
{
    /// <summary>
    /// 不是任何类型
    /// </summary>
    public static readonly ApplicantCertificationType NotAtAll = new(0, "不是任何类型", string.Empty);
    
    /// <summary>
    /// 身份证
    /// </summary>
    public static readonly ApplicantCertificationType IdCard = new(1, "身份证", "0");

    /// <summary>
    /// 护照
    /// </summary>
    public static readonly ApplicantCertificationType Passport = new(2, "护照", "10");

    /// <summary>
    /// 其他
    /// </summary>
    public static readonly ApplicantCertificationType Other = new(3, "其他", "X");

    private static readonly IReadOnlyDictionary<int, ApplicantCertificationType> applicantCertificationTypes;

    private static readonly IReadOnlyDictionary<string, ApplicantCertificationType> dictionaryValueCertificationTypes;

    static ApplicantCertificationType()
    {
        applicantCertificationTypes = new Dictionary<int, ApplicantCertificationType>()
        {
            {0, NotAtAll},
            {1, IdCard},
            {2, Passport},
            {3, Other},
        };

        dictionaryValueCertificationTypes = new Dictionary<string, ApplicantCertificationType>()
        {
            {string.Empty, NotAtAll},
            {"0", IdCard},
            {"10", Passport},
            {"X", Other}
        };
    }

    /// <summary>
    /// 尝试通过编码值获取申请人证件类型
    /// </summary>
    /// <param name="code">编码值</param>
    /// <param name="applicantCertificationType">申请人证件类型</param>
    /// <returns>存在返回true，不存在返回false</returns>
    public static bool TryGetApplicantCertificationType(int code, out ApplicantCertificationType? applicantCertificationType)
    {
        return applicantCertificationTypes.TryGetValue(code, out applicantCertificationType);
    }

    /// <summary>
    /// 通过编码值获取申请人证件类型
    /// </summary>
    /// <param name="code">编码值</param>
    /// <returns>申请人证件类型</returns>
    /// <exception cref="ArgumentOutOfRangeException">编码值不在给定的枚举范围内</exception>
    public static ApplicantCertificationType GetApplicantCertificationType(int code)
    {
        if (TryGetApplicantCertificationType(code, out var applicantCertificationType) && applicantCertificationType is not null)
            return applicantCertificationType;

        throw new ArgumentOutOfRangeException(nameof(code), $"给定的编码值{code}超出枚举范围");
    }
    
    /// <summary>
    /// 通过系统字典值获取申请人证件类型
    /// </summary>
    /// <param name="dictionaryValue">系统字典值</param>
    /// <returns>申请人证件类型</returns>
    public static ApplicantCertificationType GetApplicantCertificationType(string dictionaryValue)
    {
        if (string.IsNullOrWhiteSpace(dictionaryValue))
        {
            dictionaryValue = "X";
        }
        
        return dictionaryValueCertificationTypes.GetValueOrDefault(dictionaryValue, Other);
    }

    private ApplicantCertificationType(int code, string description, string systemDictionaryValue)
    {
        Description = description;
        SystemDictionaryValue = systemDictionaryValue;
        Code = code;
    }
    
    /// <summary>
    /// 编码值
    /// </summary>
    public int Code { get; }
    
    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; }
    
    /// <summary>
    /// 系统字典值
    /// </summary>
    public string SystemDictionaryValue { get; }
}