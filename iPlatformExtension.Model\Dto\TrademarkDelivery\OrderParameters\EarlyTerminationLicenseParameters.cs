﻿using iPlatformExtension.Model.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters
{
    /// <summary>
    /// 许可提前终止备案请求参数
    /// </summary>
    public class EarlyTerminationLicenseParameters : PhoenixOrderBaseRequestParameters
    {
        /// <summary>
        /// 申请人详情地址（中文）
        /// </summary>
        [JsonPropertyName("applicantAddress")]
        public string ApplicantAddress { get; set; }

        /// <summary>
        /// 申请人详情地址（英文）
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("applicantEnglishAddress")]
        public string ApplicantEnglishAddress { get; set; }

        /// <summary>
        /// 英文申请人名称
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("applicantEnglishName")]
        public string ApplicantEnglishName { get; set; }

        /// <summary>
        /// 申请人名称
        /// </summary>
        [JsonPropertyName("applicantName")]
        public string ApplicantName { get; set; }

        /// <summary>
        /// 受让人信息:{"country":"","ownerType":1,"bookType":1,"applicantName":"福建尚田贸易有限公司","applicantEnglishName":"","applicantAddress":"福建省宁德市霞浦县松港街道东兴社区长溪路8号九龙街D幢D004号","applicantEnglishAddress":"","prov":1,"city":64,"area":0,"code":"102200","certificatesType":1,"newApplicantName":"","idCard":"","unifiedSocialCreditCode":"91350921050303325J","post":"asdfsdf","legalPerson":"商标局改版","subjectType":"主体资格类型
        /// 1表示中文,0表示非中文 "}
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("assigneeInfo")]
        [JsonSerializationSource(nameof(AssigneeInfoList))]
        public string AssigneeInfo { get; set; }

        public AssigneeInfo AssigneeInfoList { get; set; }

        /// <summary>
        /// 书式类型,1表示大陆,2表示海外,3表示台湾,4表示香港,5表示澳门
        /// </summary>
        [JsonPropertyName("bookType")]
        public int BookType { get; set; }

        /// <summary>
        /// 证件类型,0不是任何类型,1身份证,2护照,3其他,
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("certificatesType")]
        public int? CertificatesType { get; set; }

        /// <summary>
        /// 邮编
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("code")]
        public string Code { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        [JsonPropertyName("contactEmail")]
        public string ContactEmail { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [JsonPropertyName("contactName")]
        public string ContactName { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [JsonPropertyName("contactTel")]
        public string ContactTel { get; set; }

        /// <summary>
        /// 国家或地区
        /// </summary>
        [JsonPropertyName("country")]
        public string Country { get; set; }

        /// <summary>
        /// 提前终止日期
        /// </summary>
        [JsonPropertyName("earlyEndDate")]
        public string EarlyEndDate { get; set; }

        /// <summary>
        /// 提前终止理由
        /// </summary>
        [JsonPropertyName("earlyEndReasons")]
        public string EarlyEndReasons { get; set; }

        /// <summary>
        /// 证件编号
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("idCard")]
        public string IdCard { get; set; }

        /// <summary>
        /// 唯一标识
        /// </summary>
        [JsonPropertyName("orderToken")]
        public string OrderToken { get; set; }

        /// <summary>
        /// 申请人资质：0个人 1公司
        /// </summary>
        [JsonPropertyName("ownerType")]
        public int OwnerType { get; set; }

        /// <summary>
        /// 委托人姓名
        /// </summary>
        [JsonPropertyName("principalName")]
        public string PrincipalName { get; set; }

        /// <summary>
        /// 委托人电话
        /// </summary>
        [JsonPropertyName("principalTel")]
        public string PrincipalTel { get; set; }

        /// <summary>
        /// 主体资格类型:1表示中文,0表示非中文
        /// </summary>
        [JsonPropertyName("subjectType")]
        public int SubjectType { get; set; }

        /// <summary>
        /// 递交方式 0 纸质递交，1 电子递交
        /// </summary>
        [JsonPropertyName("submitType")]
        public int SubmitType { get; set; }
    }
}
