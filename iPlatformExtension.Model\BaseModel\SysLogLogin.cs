using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_log_login", DisableSyncStructure = true)]
	public partial class SysLogLogin {

		[ Column(Name = "log_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string LogId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "cn_name", StringLength = 50)]
		public string CnName { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "ip_address", StringLength = 20)]
		public string IpAddress { get; set; }

		[ Column(Name = "login_time")]
		public DateTime? LoginTime { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
