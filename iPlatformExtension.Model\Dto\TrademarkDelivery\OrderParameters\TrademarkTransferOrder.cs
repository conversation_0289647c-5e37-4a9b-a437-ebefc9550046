﻿using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;

/// <summary>
/// 商标转让申请下单
/// </summary>
public sealed class TrademarkTransferOrder : PhoenixOrderRequestParameters
{
    /// <summary>
    /// 转让类型
    /// 1：商标转让
    /// 2：商标转移
    /// </summary>
    public string TransferType { get; set; } = string.Empty;
    
    /// <summary>
    /// 转出方信息
    /// </summary>
    public DeliveryApplicantInfo SellerInfo { get; set; } = default!;

    /// <summary>
    /// 序列化的转出方信息
    /// </summary>
    [JsonSerializationSource(nameof(SellerInfo))]
    public string? Seller { get; private set; }
    
    /// <summary>
    /// 转入方信息
    /// </summary>
    public DeliveryApplicantInfo BuyerInfo { get; set; } = default!;

    /// <summary>
    /// 序列化的转入方信息
    /// </summary>
    [JsonSerializationSource(nameof(BuyerInfo))]
    public string? Buyer { get; private set; }

    /// <summary>
    /// 申请人附件信息
    /// </summary>
    public IEnumerable<ApplicantAttachment> Attachments { get; set; } = Array.Empty<ApplicantAttachment>();

    /// <summary>
    /// 序列化的申请人附件信息
    /// </summary>
    [JsonSerializationSource(nameof(Attachments))]
    public string? ApplicantAttachments { get; private set; }

    /// <summary>
    /// 商标和商品信息
    /// </summary>
    public IEnumerable<BrandInfo> BrandInfos { get; set; } = Array.Empty<BrandInfo>();

    /// <summary>
    /// 序列化的商标商品信息
    /// </summary>
    [JsonSerializationSource(nameof(BrandInfos))]
    public string? BrandTeam { get; private set; }

    /// <summary>
    /// 订单信息
    /// </summary>
    public OrderInfo OrderInfo { get; set; } = default!;

    /// <summary>
    /// 订单信息的序列化
    /// </summary>
    [JsonSerializationSource(nameof(OrderInfo))]
    public string? Order { get; private set; }
}