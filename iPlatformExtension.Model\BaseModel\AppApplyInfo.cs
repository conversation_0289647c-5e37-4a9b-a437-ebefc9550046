using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel
{

    [ Table(Name = "app_apply_info", DisableSyncStructure = true)]
    public partial class AppApplyInfo
    {

        /// <summary>
        /// 开案ID
        /// </summary>
        [ Column(Name = "apply_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
        public string ApplyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

        /// <summary>
        /// 开案名称
        /// </summary>
        [ Column(Name = "apply_name", StringLength = 500)]
        public string ApplyName { get; set; }

        /// <summary>
        /// 开案号
        /// </summary>
        [ Column(Name = "apply_no", StringLength = 50)]
        public string ApplyNo { get; set; }

        /// <summary>
        /// 类型说明
        /// </summary>
        [ Column(Name = "aspx_code", StringLength = 50)]
        public string AspxCode { get; set; }

        [ Column(Name = "assign_user", StringLength = 50)]
        public string AssignUser { get; set; }

        /// <summary>
        /// 案源分所
        /// </summary>
        [ Column(Name = "belong_company", StringLength = 50)]
        public string BelongCompany { get; set; }

        /// <summary>
        /// 案源地区
        /// </summary>
        [ Column(Name = "belong_district", StringLength = 50)]
        public string BelongDistrict { get; set; }

        /// <summary>
        /// 案件类型
        /// </summary>
        [ Column(Name = "case_type_id", StringLength = 50)]
        public string CaseTypeId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [ Column(Name = "create_time")]
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        [ Column(Name = "create_user_id", StringLength = 50)]
        public string CreateUser { get; set; }

        /// <summary>
        /// 客户联系人
        /// </summary>
        [ Column(Name = "cust_contact_id", StringLength = 50)]
        public string CustContactId { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        [ Column(Name = "customer_id", StringLength = 50)]
        public string CustomerId { get; set; }

        /// <summary>
        /// （审核人）未使用
        /// </summary>
        [ Column(Name = "disclosure_user", StringLength = 200)]
        public string DisclosureUser { get; set; }

        /// <summary>
        /// 委案时间
        /// </summary>
        [ Column(Name = "entrust_date")]
        public DateTime? EntrustDate { get; set; }

        /// <summary>
        /// 家族案ID
        /// </summary>
        [ Column(Name = "family_case_id", StringLength = 50)]
        public string FamilyCaseId { get; set; }

        /// <summary>
        /// 其他家族ID
        /// </summary>
        [ Column(Name = "family_other_id", StringLength = 500)]
        public string FamilyOtherId { get; set; }

        /// <summary>
        /// 流程状态代码
        /// </summary>
        [ Column(Name = "flow_status")]
        public int? FlowStatus { get; set; } = 0;

        [ Column(Name = "is_crm")]
        public bool IsCrm { get; set; } = false;

        /// <summary>
        /// 是否有效
        /// </summary>
        [ Column(Name = "is_enabled")]
        public bool? IsEnabled { get; set; } = true;

        [ Column(Name = "is_ep")]
        public bool? IsEp { get; set; } = false;

        /// <summary>
        /// 是否再次提交（未使用）
        /// </summary>
        [ Column(Name = "is_submit_again")]
        public bool? IsSubmitAgain { get; set; } = false;

        /// <summary>
        /// 通知ID（未使用？）
        /// </summary>
        [ Column(Name = "notice_id", StringLength = 50)]
        public string NoticeId { get; set; }

        /// <summary>
        /// 附件ID
        /// </summary>
        [ Column(Name = "pic_file_no", StringLength = 50)]
        public string PicFileNo { get; set; }

        /// <summary>
        /// 设计ID（未使用）
        /// </summary>
        [ Column(Name = "project_id", StringLength = 50)]
        public string ProjectId { get; set; }

        /// <summary>
        /// 备注（未使用）
        /// </summary>
        [ Column(Name = "remark", StringLength = 4000, IsNullable = true)]
        public string Remark { get; set; }

        /// <summary>
        /// 案源人
        /// </summary>
        [ Column(Name = "sales_user_id", StringLength = 50)]
        public string SalesUserId { get; set; }

        /// <summary>
        /// 同日ID（？）
        /// </summary>
        [ Column(Name = "same_day_id", StringLength = 500)]
        public string SameDayId { get; set; }

        /// <summary>
        /// 同递交ID
        /// </summary>
        [ Column(Name = "same_submit_id", StringLength = 500)]
        public string SameSubmitId { get; set; }

        /// <summary>
        /// 新案重提ID
        /// </summary>
        [Column(Name = "case_resubmit_id", StringLength = 500)]
        public string? CaseResubmitId { get; set; }

        /// <summary>
        /// 再次提交（未使用）
        /// </summary>
        [ Column(Name = "submit_again")]
        public bool SubmitAgain { get; set; } = false;

        /// <summary>
        /// 技术见证人（未使用）
        /// </summary>
        [ Column(Name = "tech_witness", StringLength = 100)]
        public string TechWitness { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [ Column(Name = "update_time")]
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 更新用户
        /// </summary>
        [ Column(Name = "update_user_id", StringLength = 50)]
        public string UpdateUserId { get; set; }

    }

}
