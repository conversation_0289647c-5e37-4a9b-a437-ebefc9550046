﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class UpdateProcCommandHandler(
    IMapper mapper,
    IHttpContextAccessor httpContextAccessor,
    ICaseProcInfoRepository caseProcInfoRepository,
    ICaseFeeRepository caseFeeRepository,
    IForeignSupplierFeeRepository foreignSupplierFeeRepository,
    IProcForeignSupplierContactRepository procForeignSupplierContactRepository) 
    : IRequestHandler<UpdateProcCommand>
{
    public async Task Handle(UpdateProcCommand request, CancellationToken cancellationToken)
    {
        var (procId, version, document, isBatch) = request;
        var userId = httpContextAccessor.HttpContext?.User.GetUserId() ?? UserIds.Administrator;
        
        var procInfo = await caseProcInfoRepository.GetAsync(procId, cancellationToken);
        procInfo.ValidateVersion(version);

        var formerSupplierId = procInfo.ForeginAgencyId;
        
        var patchDto = mapper.Map<ProcPatchDto>(procInfo);
        document.ApplyTo(patchDto);
        
        mapper.Map(patchDto, procInfo);
        procInfo.UpdateUserId = userId;
        procInfo.UpdateTime = DateTime.Now;
        
        await caseProcInfoRepository.UpdateAsync(procInfo, cancellationToken);

        var currentSupplierId = procInfo.ForeginAgencyId;

        if (formerSupplierId != currentSupplierId && !string.IsNullOrWhiteSpace(formerSupplierId))
        {
            await procForeignSupplierContactRepository.DeleteAsync(contact => contact.ProcId == procId, cancellationToken);
        }

        if (patchDto.ContactorIds is not null)
        {
            var olderContacts = await procForeignSupplierContactRepository.Where(contact => contact.ProcId == procId)
                .ToListAsync(cancellationToken);
            await procForeignSupplierContactRepository.DeleteAsync(olderContacts, cancellationToken);

            if (patchDto.ContactorIds.Any())
            {
                var contacts = patchDto.ContactorIds.Select(contactId => new ProcForeignSupplierContact
                {
                    ContactId = contactId,
                    ProcId = procId,
                    CreationTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    Updater = userId,
                    Representative = false
                }).ToList();
                await procForeignSupplierContactRepository.InsertAsync(contacts, cancellationToken);
            }
        }

        if (patchDto.FeesId is not null)
        {
            var fees = await foreignSupplierFeeRepository.GetAsync(patchDto.FeesId);
            await caseFeeRepository.InsertAsync(fees.Where(fee => fee is not null)
                .Select(fee =>
                {
                    fee!.FeeId = Guid.CreateVersion7(DateTimeOffset.Now).ToString();
                    fee.ProcId = procId;
                    return fee;
                }).ToList(), cancellationToken);

            if (!isBatch)
            {
                await foreignSupplierFeeRepository.DeleteAsync(patchDto.FeesId);
            }
        }
    }
}