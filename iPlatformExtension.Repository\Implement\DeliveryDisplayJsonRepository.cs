﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class DeliveryDisplayJsonRepository : DefaultRepository<DeliveryDisplayJson, int>, IDeliveryDisplayJsonRepository
{
    public DeliveryDisplayJsonRepository(IFreeSql freeSql, UnitOfWorkManager uowManger) : base(freeSql, uowManger)
    {
    }
}