﻿using System.Collections.ObjectModel;
using Yarp.ReverseProxy.Configuration;

namespace iPlatformExtension.Gateway.Nacos;

public sealed class NacosYarpOptions
{
    public IEnumerable<string> GroupNames { get; set; } = ["DEFAULT_GROUP"];

    public TimeSpan RefreshDuration { get; set; } = TimeSpan.FromSeconds(5);

    public Dictionary<string, RouteConfig> Routes { get; set; } = new();

    public Dictionary<string, ClusterConfig> Clusters { get; set; } = new();

    /// <summary>
    /// 
    /// </summary>
    public Dictionary<string, HttpClientConfig> HttpClients { get; set; } = new();
}