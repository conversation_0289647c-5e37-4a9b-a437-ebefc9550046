﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 引证商标信息
/// </summary>
public class QuoteBrandInfo
{
    /// <summary>
    /// 商标注册号
    /// </summary>
    [JsonPropertyName("regNo")]
    public string RegisterNumber { get; set; } = string.Empty;

    /// <summary>
    /// 尼斯分类大类
    /// </summary>
    [JsonPropertyName("firstCgNo")]
    public string FirstCategoryNumber { get; set; } = string.Empty;

    /// <summary>
    /// 联系人姓名
    /// </summary>
    public string ContactName { get; set; } = string.Empty;

    /// <summary>
    /// 联系地址
    /// </summary>
    public string ContactAddress { get; set; } = string.Empty;
}