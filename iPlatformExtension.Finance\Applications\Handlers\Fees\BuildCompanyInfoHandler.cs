﻿using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class BuildCompanyInfoHandler : IRequestHandler<BuildCompanyInfoCommand>, IFeesQueryStatementBuilder
{
    private const string CompanyAlias = nameof(BasCompany);
    
    public Task Handle(BuildCompanyInfoCommand request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested) return Task.FromCanceled(cancellationToken);
        BuildFeesQueryStatement(request.Dto, request.FeesQuery, cancellationToken);

        return Task.CompletedTask;
    }

    public static void BuildFeesQueryStatement(FeeQueryDto dto, ISelect<CaseFeeList> feesQuery, params object?[] args)
    {
        
        var companyCodes = dto.BelongCompanyCodes.Distinct();
        feesQuery.InnerJoin<BasCompany>((list, company) =>
                list.CaseProcInfo.CaseInfo.BelongCompany == company.CompanyId)
            .WhereDynamicFilter(
                companyCodes.BuildContainsDynamicFilter(nameof(BasCompany.CompanyCode),
                    CompanyAlias));
    }
    
    
}