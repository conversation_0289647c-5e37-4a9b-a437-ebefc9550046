﻿using RulesEngine.Models;

namespace iPlatformExtension.Commission.Application.Models.Patent;

/// <summary>
/// 专利提成推送选项
/// </summary>
public class PatentPushingOptions
{
    private RulesEngine.RulesEngine? _excludeRulesEngine;
    
    /// <summary>
    /// 排除推送的规则
    /// </summary>
    /// <remarks>命中这些规则的提成数据不做推送</remarks>
    public Workflow[] ExcludeRules { get; set; } = [];
    
    /// <summary>
    /// 排除推送的规则引擎
    /// </summary>
    public RulesEngine.RulesEngine ExcludeRulesEngine => _excludeRulesEngine ??= new RulesEngine.RulesEngine(ExcludeRules);

    internal void RefreshRulesEngines()
    {
        _excludeRulesEngine = new RulesEngine.RulesEngine(ExcludeRules);
    }
}