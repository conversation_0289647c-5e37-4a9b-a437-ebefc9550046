using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_trademark_proc_flow", DisableSyncStructure = true)]
	public partial class CaseTrademarkProcFlow {

		[ Column(Name = "necessaryfiles", StringLength = 2000)]
		public string Necessaryfiles { get; set; }

		[ Column(Name = "proc_flow_id", StringLength = 50, IsNullable = false)]
		public string ProcFlowId { get; set; }

		[ Column(Name = "repositorypath", StringLength = 1000)]
		public string Repositorypath { get; set; }

	}

}
