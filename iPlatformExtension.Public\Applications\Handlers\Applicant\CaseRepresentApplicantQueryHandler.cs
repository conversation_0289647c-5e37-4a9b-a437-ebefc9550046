﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.Applicant;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Applicant;

internal sealed class CaseRepresentApplicantQueryHandler(IFreeSql freeSql) : 
    IRequestHandler<CaseRepresentApplicantQuery, ApplicantDetailDto>
{
    public Task<ApplicantDetailDto> Handle(CaseRepresentApplicantQuery request, CancellationToken cancellationToken)
    {
        return freeSql.Select<CaseApplicantList, CusApplicant, CusAddressList>().WithLock()
            .InnerJoin((list, applicant, address) => list.ApplicantId == applicant.ApplicantId)
            .InnerJoin((list, applicant, address) => list.AddressId == address.AddressId)
            .Where((list, applicant, address) => list.IsRepresent == true)
            .Where((list, applicant, address) => list.CaseId == request.CaseId)
            .FirstAsync((list, applicant, address) => new ApplicantDetailDto
            {
                ApplicantId = applicant.ApplicantId,
                CountryId = applicant.CountryId,
                ApplicantNameCn = applicant.ApplicantNameCn,
                ApplicantNameEn = applicant.ApplicantNameEn,
                AddressCn = address.AddressCn ?? string.Empty,
                AddressEn = address.AddressEn,
                TypeId = applicant.TypeId,
                IsChineseIdentity = applicant.IsChineseIdentity,
                PostCode = address.Postcode,
                CertificationType = applicant.CardType,
                CertificationNumber = applicant.CardNo
            }, cancellationToken);
    }
}