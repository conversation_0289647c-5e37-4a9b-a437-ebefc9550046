﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.Commission.Application.Models.Trademark;

/// <summary>
/// 创建建议任务权值规则
/// </summary>
public class SuggestionProcWeightRuleCreateDto
{
    /// <summary>
    /// 任务名称id
    /// </summary>
    [Required]
    public required string TargetCtrlProcId { get; set; } = string.Empty;

    /// <summary>
    /// 任务标识
    /// </summary>
    public string TargetProcMark { get; set; } = string.Empty;
    
}