<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <LangVersion>13</LangVersion>
    </PropertyGroup>

<!--    <PropertyGroup>-->
<!--        <ContainerBaseImage>mcr.microsoft.com/dotnet/aspnet:8.0</ContainerBaseImage>-->
<!--        <ContainerRuntimeIdentifier>linux-x64</ContainerRuntimeIdentifier>-->
<!--        <ContainerRegistry>harbor.aciplaw.com</ContainerRegistry>-->
<!--        <ContainerRepository>test/iplatform.trademark-delivery</ContainerRepository>-->
<!--        <ContainerImageTag>latest</ContainerImageTag>-->
<!--        <PublishProfile>DefaultContainer</PublishProfile>-->
<!--    </PropertyGroup>-->

<!--    <ItemGroup>-->
<!--        <ContainerPort Include="9090" Type="tcp" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_HTTP_PORTS" Value="9090" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_URLS" Value="http://+:9090" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_ENVIRONMENT" Value="Staging" />-->
<!--        <ContainerEnvironmentVariable Include="TZ" Value="Asia/Shanghai" />-->
<!--    </ItemGroup>-->

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
        <PackageReference Include="nacos-sdk-csharp.AspNetCore" Version="1.3.9" />
        <PackageReference Include="nacos-sdk-csharp.Extensions.Configuration" Version="1.3.9" />
        <PackageReference Include="NLog.DiagnosticSource" Version="5.2.0" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\iPlatformExtension.Repository\iPlatformExtension.Repository.csproj" />
    </ItemGroup>



    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>




    
</Project>
