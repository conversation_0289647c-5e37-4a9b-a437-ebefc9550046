﻿using System.Security.Claims;
using System.Text;
using Confluent.Kafka;
using iPlatformExtension.Common.Extensions;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.Common.MQ.KafKa.Handlers.Producer;

public class UserIdProducerHandler<TKey, TMessage>(IHttpContextAccessor httpContextAccessor) : DelegatingProducer<TKey, TMessage>
{
    private void SetUserId(Headers headers)
    {
        var userId = httpContextAccessor.HttpContext?.User.FindFirstValue(ClaimTypes.NameIdentifier);
        if (!string.IsNullOrWhiteSpace(userId))
        {
            headers.Add("userId", userId.GetBytes(Encoding.UTF8));
        }
    }

    public override void Produce(TopicPartition topicPartition, Message<TKey, TMessage> message, Action<DeliveryReport<TKey, TMessage>>? deliveryHandler = null)
    {
        SetUserId(message.Headers);
        base.Produce(topicPartition, message, deliveryHandler);
    }

    public override void Produce(string topic, Message<TKey, TMessage> message, Action<DeliveryReport<TKey, TMessage>>? deliveryHandler = null)
    {
        SetUserId(message.Headers);
        base.Produce(topic, message, deliveryHandler);
    }

    public override Task<DeliveryResult<TKey, TMessage>> ProduceAsync(string topic, Message<TKey, TMessage> message, CancellationToken cancellationToken = new CancellationToken())
    {
        SetUserId(message.Headers);
        return base.ProduceAsync(topic, message, cancellationToken);
    }

    public override Task<DeliveryResult<TKey, TMessage>> ProduceAsync(TopicPartition topicPartition, Message<TKey, TMessage> message,
        CancellationToken cancellationToken = new CancellationToken())
    {
        SetUserId(message.Headers);
        return base.ProduceAsync(topicPartition, message, cancellationToken);
    }
}