using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_search_export", DisableSyncStructure = true)]
	public partial class SysSearchExport {

		[ Column(Name = "column_id", StringLength = 50)]
		public string ColumnId { get; set; }

		/// <summary>
		/// 是否显示在列表中，目前仅用于查询结果是否显示
		/// </summary>
		[ Column(Name = "is_show")]
		public bool? IsShow { get; set; } = true;

		[ Column(Name = "search_export_id", StringLength = 50, IsNullable = false)]
		public string SearchExportId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "search_form_code", StringLength = 50)]
		public string SearchFormCode { get; set; }

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

		/// <summary>
		/// 栏位类型  表单构造，列表显示
		/// </summary>
		[ Column(Name = "type", StringLength = 50)]
		public string Type { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
