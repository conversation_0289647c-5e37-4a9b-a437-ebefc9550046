using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_batch_case_list", DisableSyncStructure = true)]
	public partial class AppBatchCaseList {

		[ Column(Name = "batch_case_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string BatchCaseId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "active_case")]
		public bool? ActiveCase { get; set; } = false;

		[ Column(Name = "after_change", StringLength = 1000)]
		public string AfterChange { get; set; }

		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		[ Column(Name = "app_date")]
		public DateTime? AppDate { get; set; }

		[ Column(Name = "app_no", StringLength = 50)]
		public string AppNo { get; set; }

		[ Column(Name = "applicant_ids", StringLength = 2000)]
		public string ApplicantIds { get; set; }

		[ Column(Name = "applicant_ids_text", StringLength = 500)]
		public string ApplicantIdsText { get; set; }

		[ Column(Name = "apply_channel", StringLength = 50)]
		public string ApplyChannel { get; set; }

		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "batch_id", StringLength = 50)]
		public string BatchId { get; set; }

		[ Column(Name = "before_change", StringLength = 1000)]
		public string BeforeChange { get; set; }

		[ Column(Name = "belong_company", StringLength = 50)]
		public string BelongCompany { get; set; }

		[ Column(Name = "belong_district", StringLength = 50)]
		public string BelongDistrict { get; set; }

		[ Column(Name = "business_type_id", StringLength = 50)]
		public string BusinessTypeId { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_emergent_id", StringLength = 50)]
		public string CaseEmergentId { get; set; }

		[ Column(Name = "case_name", StringLength = 200)]
		public string CaseName { get; set; }

		[ Column(Name = "case_status_id", StringLength = 50)]
		public string CaseStatusId { get; set; }

		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "change_time", StringLength = 1000)]
		public string ChangeTime { get; set; }

		[ Column(Name = "change_type", StringLength = 1000)]
		public string ChangeType { get; set; }

		[ Column(Name = "change_type_text", StringLength = 1000)]
		public string ChangeTypeText { get; set; }

		[ Column(Name = "contact_ids", StringLength = 2000)]
		public string ContactIds { get; set; }

		[ Column(Name = "contact_ids_text", StringLength = 500)]
		public string ContactIdsText { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "ctrl_proc_property", StringLength = 50)]
		public string CtrlProcProperty { get; set; }

		[ Column(Name = "cus_finish_date")]
		public DateTime? CusFinishDate { get; set; }

		[ Column(Name = "cus_first_date")]
		public DateTime? CusFirstDate { get; set; }

		[ Column(Name = "customer_case_no", StringLength = 50)]
		public string CustomerCaseNo { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "deliver_agency", StringLength = 50)]
		public string DeliverAgency { get; set; }

		[ Column(Name = "entrust_date")]
		public DateTime? EntrustDate { get; set; }

		[ Column(Name = "error_columns", StringLength = 500)]
		public string ErrorColumns { get; set; }

		[ Column(Name = "error_message", StringLength = -2)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "file_name", StringLength = 500)]
		public string FileName { get; set; }

		[ Column(Name = "filing_type", StringLength = 50)]
		public string FilingType { get; set; }

		[ Column(Name = "first_pay_annual", StringLength = 50)]
		public string FirstPayAnnual { get; set; }

		[ Column(Name = "has_file")]
		public bool? HasFile { get; set; }

		[ Column(Name = "int_due_date")]
		public DateTime? IntDueDate { get; set; }

		[ Column(Name = "int_finish_date")]
		public DateTime? IntFinishDate { get; set; }

		[ Column(Name = "int_first_date")]
		public DateTime? IntFirstDate { get; set; }

		[ Column(Name = "inventor_ids", StringLength = 2000)]
		public string InventorIds { get; set; }

		[ Column(Name = "inventor_ids_text", StringLength = 500)]
		public string InventorIdsText { get; set; }

		[ Column(Name = "is_ahead_pub")]
		public bool? IsAheadPub { get; set; } = false;

		[ Column(Name = "is_color")]
		public bool? IsColor { get; set; }

		[ Column(Name = "is_essence_exam")]
		public bool? IsEssenceExam { get; set; } = false;

		[ Column(Name = "is_priority_review")]
		public bool? IsPriorityReview { get; set; } = false;

		[ Column(Name = "is_same_day")]
		public bool? IsSameDay { get; set; } = false;

		[ Column(Name = "is_secrecy_request")]
		public bool? IsSecrecyRequest { get; set; } = false;

		[ Column(Name = "legal_due_date")]
		public DateTime? LegalDueDate { get; set; }

		[ Column(Name = "manage_company", StringLength = 50)]
		public string ManageCompany { get; set; }

		[ Column(Name = "manage_district", StringLength = 50)]
		public string ManageDistrict { get; set; }

		[ Column(Name = "multi_type", StringLength = 50)]
		public string MultiType { get; set; }

		[ Column(Name = "pct_enter", StringLength = 50)]
		public string PctEnter { get; set; }

		[ Column(Name = "priority_country_id", StringLength = 1000)]
		public string PriorityCountryId { get; set; }

		[ Column(Name = "priority_date", StringLength = 1000)]
		public string PriorityDate { get; set; }

		[ Column(Name = "priority_no", StringLength = 1000)]
		public string PriorityNo { get; set; }

		[ Column(Name = "proc_note", StringLength = 2000)]
		public string ProcNote { get; set; }

		[ Column(Name = "register_no", StringLength = 50)]
		public string RegisterNo { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "sales_user_id", StringLength = 50)]
		public string SalesUserId { get; set; }

		[ Column(Name = "serial_id", StringLength = 50)]
		public string SerialId { get; set; }

		[ Column(Name = "show_mode", StringLength = 50)]
		public string ShowMode { get; set; }

		[ Column(Name = "titular_write_user", StringLength = 50)]
		public string TitularWriteUser { get; set; }

		[ Column(Name = "track_user", StringLength = 50)]
		public string TrackUser { get; set; }

		[ Column(Name = "trademark_class", StringLength = 2000)]
		public string TrademarkClass { get; set; }

		[ Column(Name = "trademark_class_text", StringLength = 2000)]
		public string TrademarkClassText { get; set; }

		[ Column(Name = "undertake_dept_id", StringLength = 50)]
		public string UndertakeDeptId { get; set; }

		[ Column(Name = "undertake_user_id", StringLength = 50)]
		public string UndertakeUserId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "version_id", StringLength = 50)]
		public string VersionId { get; set; }

		[ Column(Name = "volume", StringLength = 50)]
		public string Volume { get; set; }

	}

}
