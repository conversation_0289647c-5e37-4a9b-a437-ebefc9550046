﻿using System.Text.Json;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Filters;

public class JsonAsyncStreamResultFilter<T>(bool stopOnException) : AsyncStreamResultFilter<T>(stopOnException) where T : ApiResult<object>, new()
{
    private JsonOptions? _jsonOptions;

    public override Task OnResultExecutionAsync(ResultExecutingContext context, ResultExecutionDelegate next)
    {
        var services = context.HttpContext.RequestServices;
        _jsonOptions = services.GetRequiredService<IOptions<JsonOptions>>().Value;
        return base.OnResultExecutionAsync(context, next);
    }

    protected override Task WriteAsync(ResultExecutingContext context, object item, CancellationToken cancellationToken)
    {
        var jsonOptions = _jsonOptions!;
        var writer = context.HttpContext.Response.BodyWriter;
        var result = item as T ?? new T().Succeed(item);

        return JsonSerializer.SerializeAsync(writer, result, jsonOptions.JsonSerializerOptions, cancellationToken);
    }

    protected override Task WriteErrorAsync(ResultExecutingContext context, Exception exception, CancellationToken cancellationToken)
    {
        var jsonOptions = _jsonOptions!;
        var writer = context.HttpContext.Response.BodyWriter;
        var result = new T().Fail(exception);

        return JsonSerializer.SerializeAsync(writer, result, jsonOptions.JsonSerializerOptions, cancellationToken);
    }
}