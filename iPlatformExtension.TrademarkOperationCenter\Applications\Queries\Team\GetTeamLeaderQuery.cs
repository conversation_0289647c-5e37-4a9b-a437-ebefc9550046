﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;

/// <summary>
/// 获取团队负责人
/// </summary>
/// <param name="TeamId">团队id</param>
public sealed record GetTeamLeaderQuery([Required(ErrorMessage = "需要团队id")]string TeamId) : IRequest<IEnumerable<GetTeamLeaderDto>>;

