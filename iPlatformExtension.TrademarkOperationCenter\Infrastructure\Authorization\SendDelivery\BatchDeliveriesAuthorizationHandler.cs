﻿using System.Text;
using System.Text.Json;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Authorization.SendDelivery;

internal sealed class BatchDeliveriesAuthorizationHandler(
    ISender sender, 
    IOptions<JsonOptions> jsonOptions, 
    ObjectPool<StringBuilder> stringBuilderPool) 
    : AuthorizationHandler<DeliveriesValidationRequirement, HttpContext>
{
    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, DeliveriesValidationRequirement requirement,
        HttpContext resource)
    {
        var options = jsonOptions.Value.JsonSerializerOptions;
        var procIds = await JsonSerializer.DeserializeAsync<IEnumerable<string>>(resource.Request.Body, options);
        var button =
            resource.Request.Query.TryGetValue("button", out var buttonValue) &&
            Enum.TryParse<DeliveryButton>(buttonValue, true, out var deliveryButton)
                ? deliveryButton
                : (DeliveryButton?) null;
        resource.Request.Body.Seek(0, SeekOrigin.Begin);
        
        if (procIds is not null)
        {
            var request = requirement.CreateValidationCommand(procIds, button);
            if (request is null)
            {
                context.Succeed(requirement);
                return;
            }
            
            var result = await sender.Send(request, resource.RequestAborted);
            if (result.Success)
            {
                context.Succeed(requirement);
            }
            else
            {
                var stringBuilder =
                    result.ProcNos.Aggregate(stringBuilderPool.Get(), (sb, procNo) => sb.AppendLine(procNo));
                context.Fail(new AuthorizationFailureReason(this, result.Message));
                context.Fail(new AuthorizationFailureReason(this, stringBuilder.ToString()));
                stringBuilderPool.Return(stringBuilder);
            }
        }
    }
}