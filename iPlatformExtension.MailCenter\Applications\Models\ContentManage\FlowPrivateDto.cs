﻿using iPlatformExtension.Model.Enum;

namespace iPlatformExtension.MailCenter.Applications.Models.ContentManage
{
    /// <summary>
    /// 流程分类
    /// </summary>
    public class FlowPrivateDto
    {
        /// <summary>
        /// 分类Id(留空为新增,否则为更新)
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 标签名称
        /// </summary>
        public string LabelName { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Seq { get; set; }

        /// <summary>
        /// 默认选中
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// 邮件类型:Receive:收件标签,Send:发件标签
        /// 默认:2
        /// </summary>
        public string MailType { get; set; } = SysEnum.MailType.Receive.ToString();
    }
}
