﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 联系人快照信息
/// </summary>
public class ContactInfoSnapshot
{
    /// <summary>
    /// 接收人、联系人地址
    /// </summary>
    public string ContactAddress { get; set; } = "";

    /// <summary>
    /// 联系人、接收人邮箱
    /// </summary>
    public string ContactMailbox { get; set; } = "";

    /// <summary>
    /// 接收人
    /// </summary>
    public string AgencyName { get; set; } = "";

    /// <summary>
    /// 联系人名称
    /// </summary>
    public string ContactPerson { get; set; } = string.Empty;

    /// <summary>
    /// 联系人、接收人邮编
    /// </summary>
    public string ContactPostCode { get; set; } = "";

    /// <summary>
    /// 接收人、联系人电话
    /// </summary>
    public string ContactTelephone { get; set; } = "";
    
    
}