﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class CreateDomesticCommissionWeightExceptionHandler(
    ILogger<CreateDomesticCommissionWeightCommand> logger) 
    : IRequestExceptionHandler<CreateDomesticCommissionWeightCommand, Unit, Exception>
{
    public Task Handle(CreateDomesticCommissionWeightCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        logger.LogCreatingDomesticTrademarkWeightError(exception);
        state.SetHandled(Unit.Value);
        
        return Task.CompletedTask;
    }
}