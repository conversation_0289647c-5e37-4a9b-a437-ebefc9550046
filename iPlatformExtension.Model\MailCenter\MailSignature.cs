﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_signature", DisableSyncStructure = true)]
	public partial class MailSignature {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		/// <summary>
		/// 签名正文
		/// </summary>
		[Column(Name = "content", StringLength = -2)]
		public string Content { get; set; }

		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 签名名称
		/// </summary>
		[Column(Name = "name", StringLength = 255)]
		public string Name { get; set; }
		
		/// <summary>
		/// 语言类型
		/// </summary>
		[Column(Name = "language", StringLength = 10)]
		public string Language { get; set; }

		[Column(Name = "update_by", StringLength = 50)]
		public string UpdateBy { get; set; }

		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 用户id
		/// </summary>
		[Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
