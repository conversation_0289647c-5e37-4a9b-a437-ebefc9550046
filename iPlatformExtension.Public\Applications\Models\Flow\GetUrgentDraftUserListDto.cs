﻿namespace iPlatformExtension.Public.Applications.Models.Flow;

/// <summary>
/// 获取被催稿人列表
/// </summary>
/// <param name="ProcId">任务id</param>
/// <param name="UserId">被催稿人id</param>
/// <param name="Name">被催稿人名称</param>
/// <param name="ResponsibleName">负责部分名称</param>
/// <param name="UserCode">工号</param>
public record GetUrgentDraftUserListDto(string ProcId,string UserId,string Name, string ResponsibleName,string UserCode);

