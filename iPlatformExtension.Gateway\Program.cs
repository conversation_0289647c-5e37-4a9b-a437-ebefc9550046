using System.Text;
using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Gateway.Infrastructure.Extensions;
using iPlatformExtension.Gateway.Models;
using iPlatformExtension.Gateway.Nacos;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Nacos.AspNetCore.V2;
using Yarp.ReverseProxy.Configuration;

var builder = WebApplication.CreateBuilder(args);

builder.WebHost.ConfigureKestrel(options =>
{
    options.Limits.MaxRequestLineSize = int.MaxValue;
    options.Limits.MaxRequestBodySize = long.MaxValue;
    options.Limits.MaxRequestBufferSize = long.MaxValue;
    options.Limits.MaxRequestLineSize = int.MaxValue;
});

builder.Logging.ClearProviders();
builder.Logging.AddSimpleConsole(options =>
{
    options.IncludeScopes = true;
    options.UseUtcTimestamp = false;
    options.TimestampFormat = "[yyyy-MM-dd HH:mm:ss.sss] ";
});

Console.WriteLine(builder.Environment.EnvironmentName);

builder.Services.AddNacosAspNet(builder.Configuration);

builder.Configuration.AddNacosV2Configuration(builder.Configuration.GetSection("nacos"));

builder.Services.AddReverseProxy().LoadFromNacos();

builder.Services.AddObjectPools();

builder.Services.AddAuthentication().AddJwtBearer(PlatformAuthOptions.SchemeName,
    options =>
    {
        var platformAuthenticationOptions =
            builder.Configuration.Get<PlatformAuthOptions>(sectionKey: "IPlatformAuth");
        options.TokenValidationParameters = new TokenValidationParameters
        {
            IssuerSigningKey = new SymmetricSecurityKey(
                platformAuthenticationOptions?.SecurityKey.GetBytes(Encoding.UTF8) ??
                throw new ArgumentNullException(nameof(builder.Configuration))),
            ValidateIssuerSigningKey = true,
            ValidAudiences = platformAuthenticationOptions.Audiences,
            ValidIssuers = platformAuthenticationOptions.Issuers
        };
        options.Events = new JwtBearerEvents
        {
            OnTokenValidated = AuthenticationExtension.ValidateUserAsync,
            OnChallenge = AuthenticationExtension.OnChallengeAsync
        };
        options.ForwardChallenge = BladeAuthOptions.SchemeName;
        options.ForwardForbid = BladeAuthOptions.SchemeName;
        options.SaveToken = true;
    }).AddScheme<BladeAuthOptions, BladeAuthenticationHandler>(BladeAuthOptions.SchemeName, "bladeX token验证", options =>
    {
        Func<BladeAuthenticatedContext, ValueTask<AuthenticateResult?>> tokenValidated = AuthenticationExtension.ValidateUserAsync;
        tokenValidated += options.Events.OnTokenValidated;
        options.Events.OnTokenValidated = tokenValidated;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            ValidateIssuer = false,
            ValidateAudience = false,
            IssuerSigningKey = new SymmetricSecurityKey(
                builder.Configuration["BladeAuth:SecurityKey"]?.GetBytes(Encoding.UTF8) ??
                throw new ArgumentNullException(nameof(builder.Configuration), "缺少blade-auth秘钥"))
        };
    });

builder.Services.AddAuthorizationBuilder()
    .AddPolicy("PlatformOrBlade", policyBuilder =>
    {
        policyBuilder.AuthenticationSchemes.Add(BladeAuthOptions.SchemeName);
        policyBuilder.AuthenticationSchemes.Add(PlatformAuthOptions.SchemeName);
        policyBuilder.RequireAuthenticatedUser();
    })
    .AddPolicy("Blade", policyBuilder =>
    {
        policyBuilder.AuthenticationSchemes.Add(BladeAuthOptions.SchemeName);
        policyBuilder.RequireAuthenticatedUser();
    })
    .AddPolicy("Platform", policyBuilder =>
    {
        policyBuilder.AuthenticationSchemes.Add(PlatformAuthOptions.SchemeName);
        policyBuilder.RequireAuthenticatedUser();
    });


builder.Services.AddCache().ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
{
    options.ConnectionString = builder.Configuration.GetConnectionString("Redis");
    options.Converters.Add(new ClaimsIdentityConverter());
});

var app = builder.Build();

app.UseAuthentication();
app.UseAuthorization();

app.MapReverseProxy();

app.MapGet("/routes", (IProxyConfigProvider proxyConfigProvider) => proxyConfigProvider.GetConfig());

app.MapGet("/routes/{routeId}", ([FromServices] IProxyConfigProvider proxyConfigProvider, string routeId) =>
{
    var configs = proxyConfigProvider.GetConfig();
    var routes = configs.Routes;
    var clusters = configs.Clusters;

    var routeProxyConfig = new RouteProxyConfig
    {
        Routes = routes.Where(config => config.RouteId == routeId).ToList()
    };
    
    var clusterIds = routeProxyConfig.Routes.Select(r => r.ClusterId).Distinct().ToList();
    routeProxyConfig.Clusters = clusters.Where(config => clusterIds.Contains(config.ClusterId)).ToList();

    return routeProxyConfig;
});

app.MapGet("/cluster/{clusterId}", ([FromServices] IProxyConfigProvider proxyConfigProvider, string clusterId) =>
{
    var configs = proxyConfigProvider.GetConfig();
    return configs.Clusters.Where(config => config.ClusterId == clusterId).ToList();
});

app.MapDelete("/routes/{routeId}", ([FromServices] IProxyConfigProvider proxyConfigProvider, string routeId) =>
{
    var routeConfigs = proxyConfigProvider.GetRouteConfigs();
    routeConfigs?.Remove(routeId);
});

app.MapGet("/service-names", (NacosNamingBackgroundService backgroundService) => backgroundService.CurrentServiceNames);

app.Run();