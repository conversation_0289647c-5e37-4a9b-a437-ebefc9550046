﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Finance.Applications.Models.Proc;

/// <summary>
/// 代缴费导出实体类
/// </summary>
public class UnpaidOfficialProcExportDto
{
    /// <summary>
    /// 任务编号
    /// </summary>
    [ExcelColumnName("任务编号")]
    public string ProcNo { get; set; } = string.Empty;

    /// <summary>
    /// 我方文号
    /// </summary>
    [ExcelColumnName("我方文号")]
    public string Volume { get; set; } = string.Empty;

    /// <summary>
    /// 申请人名称（中文）
    /// </summary>
    [ExcelColumnName("申请人名称（中文）")]
    public string ApplicantNames { get; set; } = string.Empty;

    /// <summary>
    /// 客户名称
    /// </summary>
    [ExcelColumnName("客户名称")]
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// 商标名称
    /// </summary>
    [ExcelColumnName("商标名称")]
    public string TrademarkName { get; set; } = string.Empty;

    /// <summary>
    /// 国际分类
    /// </summary>
    [ExcelColumnName("国际分类")]
    public string TrademarkClasses { get; set; } = string.Empty;

    /// <summary>
    /// 送官方日
    /// </summary>
    [ExcelColumn(Name = "送官方日", Format = "yyyy-MM-dd")]
    public DateTime? SendOfficialDate { get; set; }

    /// <summary>
    /// 任务名称
    /// </summary>
    [ExcelColumnName("任务名称")]
    public string ProcName { get; set; } = string.Empty;

    /// <summary>
    /// 缴费key
    /// </summary>
    [ExcelColumnName("缴费key")]
    public string DeliveryKey { get; set; } = string.Empty;

    /// <summary>
    /// 案件流向
    /// </summary>
    [ExcelColumnName("案件流向")]
    public string CaseDirection { get; set; } = string.Empty;

    /// <summary>
    /// 申请号/注册号
    /// </summary>
    [ExcelColumnName("申请号/注册号")]
    public string ApplyNoOrRegistrationNo { get; set; } = string.Empty;

    /// <summary>
    /// 递交方式
    /// </summary>
    [ExcelColumnName("递交方式")]
    public string DeliveryType { get; set; } = string.Empty;

    /// <summary>
    /// 官费
    /// </summary>
    [ExcelColumnName("官费")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 缴费发文日期
    /// </summary>
    [ExcelColumnName("缴费发文日期")]
    public string OfficialPublicationDates { get; set; } = string.Empty;

    /// <summary>
    /// 管控标识
    /// </summary>
    [ExcelColumnName("管控标识")]
    public string ControlIdentifiers { get; set; } = string.Empty;
}