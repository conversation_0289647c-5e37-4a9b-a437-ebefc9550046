﻿﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;

namespace iPlatformExtension.MailCenterRepository.Implement;

/// <summary>
/// 邮件模板仓储实现
/// </summary>
internal class MailTemplateRepository(
    IFreeSql<MailCenterFreeSql> fsql,
    UnitOfWorkManage<MailCenterFreeSql> manager)
    : DefaultRepository<MailTemplate, string>(fsql, manager), IMailTemplateRepository;
