﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class ProcCitedTrademarkQueryHandler(IFreeSql freeSql) : IRequestHandler<ProcCitedTrademarkQuery, ProcCitedTrademarkDetail>
{
    public Task<ProcCitedTrademarkDetail> Handle(ProcCitedTrademarkQuery request, CancellationToken cancellationToken)
    {
        return freeSql.Select<ProcCitedTrademark>(request.Id).WithLock().ToOneAsync(citedTrademark =>
            new ProcCitedTrademarkDetail
            {
                Id = citedTrademark.Id,
                RegisterNumber = citedTrademark.RegisterNumber,
                TrademarkClasses = citedTrademark.TrademarkClasses,
                Contactor = citedTrademark.Contactor,
                ContactAddress = citedTrademark.ContactAddress,
                ProcId = citedTrademark.ProcId
            }, cancellationToken);
    }
}