﻿using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Helper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;
using Microsoft.CodeAnalysis.Operations;
using System.Linq.Expressions;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public class GetFlowProcessHistoryHandler : IRequestHandler<GetFlowProcessHistoryQuery, PageResult<GetFlowProcessHistoryDto>>
    {
        private readonly IFreeSql _freeSql;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMediator _mediator;

        public GetFlowProcessHistoryHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor, IMediator mediator)
        {
            _freeSql = freeSql;
            _httpContextAccessor = httpContextAccessor;
            _mediator = mediator;
        }

        public async Task<PageResult<GetFlowProcessHistoryDto>> Handle(GetFlowProcessHistoryQuery request, CancellationToken cancellationToken)
        {
            var userid = _httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var list = new List<GetFlowProcessHistoryDto>();
            ArgumentNullException.ThrowIfNull(userid);
            Expression<Func<SysFlowActivity, bool>> where = null;
            var sql = _freeSql.Select<SysFlowActivity>().Where((fa) => fa.FlowType == request.FFlowType).WithLock();  //&& fa.AuditTime >= DateTime.Now.Date.AddDays(-6)
            long totalCount = 0L;
            if (request.FFlowType == FlowTypeEnum.DE)
            {
                where = (fa) => fa.FlowSubType == request.FSubType && fa.FlowType == request.FFlowType && fa.FlowHistory.AuditUserId == userid && fa.FlowHistory.AuditTime >= DateTime.Now.Date.AddDays(-6);
            }
            else
            {
                where = (fa) => fa.FlowHistory.AuditUserId == userid && fa.FlowHistory.FlowType == request.FFlowType && fa.FlowSubType == request.FSubType && fa.FlowHistory.AuditTime >= DateTime.Now.Date.AddDays(-6);
            }

            if (!string.IsNullOrWhiteSpace(request.SearchKey))
            {
                where = where.And((fa) => fa.CaseProcInfo.CaseInfo.CaseName.Contains(request.SearchKey) || fa.CurFlowNode.NameZhCn.Contains(request.SearchKey) ||
                fa.CaseProcInfo.CaseInfo.Volume.Contains(request.SearchKey) || fa.CaseProcInfo.CaseInfo.Customer.CustomerName.Contains(request.SearchKey) || fa.CaseProcInfo.BasCtrlProc.CtrlProcZhCn.Contains(request.SearchKey)
                || fa.CaseProcInfo.CaseInfo.RegisterNo.Contains(request.SearchKey) || fa.CaseProcInfo.CaseInfo.AppNo.Contains(request.SearchKey) ||
                fa.CaseProcInfo.CaseInfo.Applicants.Any(app => app.CusApplicant.ApplicantNameCn.Contains(request.SearchKey))
                );
            }

            if (request.FFlowType == FlowTypeEnum.DE)
            {
                list = await sql.Where(where).OrderByDescending((fa) => fa.UpdateTime)
              .Aggregate(a => SqlExt.DistinctCount(a.Key.ObjId), out totalCount)
                .Take(request.PageSize).Skip(request.PageSize * (request.PageIndex - 1))
                .Distinct()
                .ToListAsync((fa) => new GetFlowProcessHistoryDto()
                {
                    procNo = fa.CaseProcInfo.ProcNo,
                    caseName = fa.CaseProcInfo.CaseInfo.CaseName,
                    UndertakeUserName = fa.CaseProcInfo.UndertakeUserInfo.CnName,
                    ctrlProcName = fa.CaseProcInfo.BasCtrlProc.CtrlProcZhCn,
                    appNo = fa.CaseProcInfo.DeliInfo.AppNo,
                    TrademarkClass = fa.CaseProcInfo.DeliInfo.OtherInfo.TrademarkNiceClasses,
                    LegalDueDate = fa.CaseProcInfo.LegalDueDate,
                    Status = fa.CaseProcInfo.DeliInfo.Status,
                    OperationResult = fa.CaseProcInfo.DeliInfo.OperationResult,
                    IsAuto = fa.CaseProcInfo.DeliInfo.IsAuto,
                    CurNode = fa.CurFlowNode.NameZhCn,
                    CurHandler = fa.CurNodeUserInfo.CnName,
                    SubmitType = fa.PrevAuditTypeId,
                    UpdateTime = fa.UpdateTime,
                    ObjectId = fa.CaseProcInfo.ProcId,
                    Country = fa.CaseProcInfo.CaseInfo.Country.CountryZhCn,
                    ApplyTypeId = fa.CaseProcInfo.CaseInfo.ApplyType.ApplyTypeZhCn,
                    RequirementSubmitDate = fa.CaseProcInfo.RequirementSubmitDate,
                    CtrlProcMark = _freeSql.Select<SysDictionary>().Where(o => o.DictionaryName == "ctrl_proc_mark" && o.Value == fa.CaseProcInfo.DeliInfo.OtherInfo.CtrlProcMark).ToOne(o => o.TextZhCn),
                    ReturnDocDate = fa.CaseProcInfo.ReturnDocDate,
                    Volume = fa.CaseProcInfo.CaseInfo.Volume,
                    ProcID = fa.CaseProcInfo.ProcId,
                    ApplicantName = _freeSql.Select<DeliApplicant>().Any(o => o.ProcId == fa.CaseProcInfo.ProcId) ? _freeSql.Select<DeliApplicant>().Where(o => o.ProcId == fa.CaseProcInfo.ProcId).OrderByDescending(o => o.IsRepresent).ToOne(o => o.ApplicantNameCn) : string.Empty,
                    CustomerName = fa.CaseProcInfo.CaseInfo.Customer.CustomerName,
                    RemainingDays = (fa.CaseProcInfo.BasCtrlProc.TrademarkTab == "0" || fa.CaseProcInfo.BasCtrlProc.TrademarkTab == "3") ? (!fa.CaseProcInfo.ReturnDocDate.HasValue ? null : (fa.CaseProcInfo.ReturnDocDate.Value - DateTime.Now.Date).Days + 1) :
                         (fa.CaseProcInfo.CtrlProcId == "DD886CBE-D8D4-4BE0-97F7-FD521674269A" ? (!fa.CaseProcInfo.RequirementSubmitDate.HasValue ? null : (fa.CaseProcInfo.RequirementSubmitDate.Value - DateTime.Now.Date).Days + 1) :
                         (!fa.CaseProcInfo.LegalDueDate.HasValue ? null : (fa.CaseProcInfo.LegalDueDate.Value - DateTime.Now.Date).Days + 1)
                         )
                });
            }
            else if (request.FFlowType == FlowTypeEnum.EX)
            {
                var query = request.Clone<GetFlowProcessHistoryByEXQuery>();
                return await _mediator.Send(query);
            }
            else
            {
                list = await sql.Where(where).OrderByDescending((fa) => fa.UpdateTime)
                   .Aggregate(a => SqlExt.DistinctCount(a.Key.ObjId), out totalCount)
                   .Take(request.PageSize).Skip(request.PageSize * (request.PageIndex - 1))
                   .Distinct()
                   .ToListAsync((fa) => new GetFlowProcessHistoryDto()
                   {
                       procNo = fa.CaseProcInfo.ProcNo,
                       caseName = fa.CaseProcInfo.CaseInfo.CaseName,
                       UndertakeUserName = fa.CaseProcInfo.UndertakeUserInfo.CnName,
                       ctrlProcName = fa.CaseProcInfo.BasCtrlProc.CtrlProcZhCn,
                       appNo = fa.CaseProcInfo.CaseInfo.AppNo,
                       TrademarkClass = fa.CaseProcInfo.CaseInfo.TrademarkClass,
                       LegalDueDate = fa.CaseProcInfo.LegalDueDate,
                       Status = fa.CaseProcInfo.DeliInfo.Status,
                       CurNode = fa.CurFlowNode.NameZhCn,
                       CurHandler = fa.CurNodeUserInfo.CnName,
                       SubmitType = fa.PrevAuditTypeId,
                       UpdateTime = fa.UpdateTime,
                       ObjectId = fa.ObjId,
                       Country = fa.CaseProcInfo.CaseInfo.Country.CountryZhCn,
                       ApplyTypeId = fa.CaseProcInfo.CaseInfo.ApplyType.ApplyTypeZhCn,
                       RequirementSubmitDate = fa.CaseProcInfo.RequirementSubmitDate,
                       CtrlProcMark = _freeSql.Select<SysDictionary>().Where(o => o.DictionaryName == "ctrl_proc_mark" && o.Value == fa.CaseProcInfo.CtrlProcMark).ToOne(o => o.TextZhCn),
                       ReturnDocDate = fa.CaseProcInfo.ReturnDocDate,
                       Volume = fa.CaseProcInfo.CaseInfo.Volume,
                       ApplicantName = _freeSql.Select<CusApplicant, CaseApplicantList>().InnerJoin((a, b) => a.ApplicantId == b.ApplicantId).Where((a, b) => b.CaseId == fa.CaseProcInfo.CaseId).OrderByDescending((a, b) => b.IsRepresent).ToOne((a, b) => a.ApplicantNameCn),
                       CustomerName = fa.CaseProcInfo.CaseInfo.Customer.CustomerName,
                       RemainingDays = (fa.CaseProcInfo.BasCtrlProc.TrademarkTab == "0" || fa.CaseProcInfo.BasCtrlProc.TrademarkTab == "3") ? (!fa.CaseProcInfo.ReturnDocDate.HasValue ? null : (fa.CaseProcInfo.ReturnDocDate.Value - DateTime.Now.Date).Days + 1) :
                         (fa.CaseProcInfo.CtrlProcId == "DD886CBE-D8D4-4BE0-97F7-FD521674269A" ? (!fa.CaseProcInfo.RequirementSubmitDate.HasValue ? null : (fa.CaseProcInfo.RequirementSubmitDate.Value - DateTime.Now.Date).Days + 1) :
                         (!fa.CaseProcInfo.LegalDueDate.HasValue ? null : (fa.CaseProcInfo.LegalDueDate.Value - DateTime.Now.Date).Days + 1)),
                       ProcID = fa.CaseProcInfo.ProcId,
                       ProcFlowId = fa.CaseProcFlow.ProcFlowId
                   });
            }

            return new PageResult<GetFlowProcessHistoryDto>()
            {
                Data = list,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };
        }
    }
}
