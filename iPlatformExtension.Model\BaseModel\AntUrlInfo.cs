using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ant_url_info", DisableSyncStructure = true)]
	public partial class AntUrlInfo {

		[ Column(Name = "info_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string InfoId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "conf_id", StringLength = 50, IsNullable = false)]
		public string ConfId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "info_title", StringLength = 500, IsNullable = false)]
		public string InfoTitle { get; set; }

		[ Column(Name = "info_url", StringLength = 500, IsNullable = false)]
		public string InfoUrl { get; set; }

		[ Column(Name = "is_htmltopdf")]
		public bool IsHtmltopdf { get; set; } = true;

		[ Column(Name = "pub_date")]
		public DateTime PubDate { get; set; }

		[ Column(Name = "url_file", StringLength = 500)]
		public string UrlFile { get; set; }

	}

}
