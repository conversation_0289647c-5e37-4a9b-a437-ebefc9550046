using System.Net.Http.Headers;
using iPlatformExtension.Common.Clients.IPlatformWeb.Options;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Dto;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OBS;

namespace iPlatformExtension.Common.Clients.IPlatformWeb;

public class PlatformHttpClient
{
    private readonly HttpClient _client;

    private readonly ILogger _logger;

    public PlatformHttpClient(HttpClient httpClient, ILoggerFactory loggerFactory, IOptionsMonitor<PlatformHttpClientOptions> options)
    {
        _client = httpClient;
        _logger = loggerFactory.CreateLogger(GetType());

        var clientOptions = options.CurrentValue;
        _client.BaseAddress = new Uri(clientOptions.BaseAddress);
        _client.Timeout = clientOptions.Timeout;
        _client.DefaultRequestHeaders.Accept.Add(MediaTypeWithQualityHeaderValue.Parse("application/json"));
    }

    /// <summary>
    /// �Զ����ɷ���
    /// </summary>
    /// <returns></returns>
    public ValueTask<OldPlatformResult<object>> GetApplyAutoSqlAsync(FeeConfigDto feeConfig)
    {
        ArgumentNullException.ThrowIfNull(feeConfig);
        return new ValueTask<OldPlatformResult<object>>(
            _client.PostJsonAsync<FeeConfigDto, OldPlatformResult<object>>("/acipapi/CaseEntrust/GetApplyAutoSql",
                feeConfig)!);
    }

    /// <summary>
    /// ��ȡ���̻�����Ϣ
    /// </summary>
    /// <returns></returns>
    public async ValueTask<FlowResultDto?> GetFlowInfoAsync(string objId, string flowType, string flowSubType, string deptId = "")
    {
        var requestParameters = new Dictionary<string, string>()
        {
            ["obj_id"] = objId,
            ["flow_type"] = flowType,
            ["flow_sub_type"] = flowSubType,
            ["dept_id"] = deptId
        };
        return await _client.PostOldPlatformOldApiAsync<FlowResultDto>("/acipapi/CaseEntrust/GetFlowInfo", "GetFlowInfo",
            requestParameters);
    }

   
    public async Task<OldPlatformResult<object>?> UploadCaseEntrustFileAsync(CaseEntrustUploadDto requestParameters, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(requestParameters);
        ArgumentNullException.ThrowIfNull(requestParameters.FileName);
        ArgumentNullException.ThrowIfNull(requestParameters.ObjectId);

        var boundary = $"----Boundary{DateTime.Now.Ticks:x}";
        var content = new MultipartFormDataContent(boundary);
        content.Add(new ByteArrayContent(requestParameters.File), nameof(requestParameters.File), requestParameters.FileName);
        content.Add(new StringContent("1"), "is_draft");
        content.Add(new StringContent("0"), "is_batch");
        content.Add(new StringContent(requestParameters.ObjectId), "obj_id");
        content.Add(new StringContent(""), "file_role");
        var name = $"{Guid.NewGuid().ToString()}_*_{requestParameters.FileName}_*_{requestParameters.DescId}";
        content.Add(new StringContent(name), nameof(name));
        content.Add(new StringContent(requestParameters.Creator), "Creator");
        return await _client.PostAsync<OldPlatformResult<object>>("/acipapi/CaseEntrust/UploadFile", content);
    }
}