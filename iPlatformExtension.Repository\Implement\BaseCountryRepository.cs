﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class BaseCountryRepository : BaseRepository<BasCountry, string>, IBaseCountryRepository
{
    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="freeSql">sqlORM</param>
    /// <param name="memoryCache">内存缓存</param>
    /// <param name="redisCache">redis缓存</param>
    /// <param name="expirationToken">过期令牌</param>
    public BaseCountryRepository(
        IFreeSql<PlatformFreeSql> freeSql, 
        IMemoryCache memoryCache, 
        DefaultRedisCache redisCache, 
        CacheExpirationToken<BasCountry> expirationToken) 
        : base(freeSql)
    {
        MemoryCache = memoryCache;
        RedisCache = redisCache;
        ExpirationToken = expirationToken;
    }

    public IMemoryCache MemoryCache { get; }
    
    public CacheExpirationToken<BasCountry> ExpirationToken { get; }

    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; }
}