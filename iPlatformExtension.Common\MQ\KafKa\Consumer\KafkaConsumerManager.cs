using Confluent.Kafka;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.MQ.KafKa.Consumer
{
    /// <summary>
    /// Kafka消费者管理器，负责管理消费者的生命周期和提供订阅方法
    /// </summary>
    public class KafkaConsumerManager
    {
        private readonly KafkaConsumerFactory _consumerFactory;
        private readonly ILogger<KafkaConsumerManager>? _logger;

        public KafkaConsumerManager(KafkaConsumerFactory? consumerFactory = null, ILogger<KafkaConsumerManager>? logger = null)
        {
            _consumerFactory = consumerFactory ?? new KafkaConsumerFactory();
            _logger = logger;
        }

        /// <summary>
        /// 订阅消息
        /// </summary>
        /// <typeparam name="TMessage">消息类型</typeparam>
        /// <param name="config">消费者配置</param>
        /// <param name="topics">主题列表</param>
        /// <param name="messageFunc">消息处理函数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task SubscribeAsync<TMessage>(ConsumerConfig config, IEnumerable<string> topics,
            Action<TMessage> messageFunc, CancellationToken cancellationToken) where TMessage : class
        {
            if (config == null) throw new ArgumentNullException(nameof(config));
            if (topics == null) throw new ArgumentNullException(nameof(topics));
            if (messageFunc == null) throw new ArgumentNullException(nameof(messageFunc));

            // 创建消息处理器
            var messageProcessor =
                new KafkaMessageProcessor<TMessage>(_logger as ILogger<KafkaMessageProcessor<TMessage>>);

            // 创建消费者
             var consumer = _consumerFactory.CreateStringConsumer(config);

            // 订阅主题
            consumer.Subscribe(topics);
            await Task.Factory.StartNew(async () =>
            {
                try
                {
                    await ConsumeMessagesAsync(consumer, messageProcessor, messageFunc, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    var message = "Closing consumer.";
                    _logger?.LogInformation(message);
                    Console.WriteLine(message);
                    consumer.Close();
                }
            }, cancellationToken);
        }

        /// <summary>
        /// 消费消息
        /// </summary>
        /// <typeparam name="TMessage">消息类型</typeparam>
        /// <param name="consumer">消费者</param>
        /// <param name="messageProcessor">消息处理器</param>
        /// <param name="messageFunc">消息处理函数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        private async Task ConsumeMessagesAsync<TMessage>(
            IConsumer<Ignore, string> consumer,
            KafkaMessageProcessor<TMessage> messageProcessor,
            Action<TMessage> messageFunc,
            CancellationToken cancellationToken) where TMessage : class
        {

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // 消费消息
                    var consumeResult = consumer.Consume(cancellationToken);

                    // 处理消息
                    bool processSuccess = messageProcessor.ProcessMessage(consumeResult, messageFunc);

                    // 如果处理成功，存储偏移量
                    if (processSuccess)
                    {
                        try
                        {
                            consumer.Commit(consumeResult);
                        }
                        catch (KafkaException e)
                        {
                            _logger?.LogError(e, $"Failed to store offset: {e.Message}");
                            Console.WriteLine(e.Message);
                        }
                    }
                }
                catch (ConsumeException e)
                {
                    _logger?.LogError(e, $"Consume error: {e.Error.Reason}");
                    Console.WriteLine($"Consume error: {e.Error.Reason}");
                }

                // 添加一个短暂的延迟，避免CPU使用率过高
                await Task.Delay(10, cancellationToken);
            }
        }

    }
}