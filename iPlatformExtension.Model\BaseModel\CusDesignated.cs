using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_designated", DisableSyncStructure = true)]
	public partial class CusDesignated {

		[ Column(Name = "designated_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DesignatedId { get; set; } = "getdate()";

		[ Column(Name = "agent_classify", StringLength = 100)]
		public string AgentClassify { get; set; }

		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
