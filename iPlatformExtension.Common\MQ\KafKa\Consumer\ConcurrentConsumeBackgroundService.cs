using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.KafKa.Route;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.MQ.KafKa.Consumer;

internal sealed class ConcurrentConsumeBackgroundService<TMessage>(
    IOptions<ConsumerOptions<TMessage>> options,
    IServiceScopeFactory scopeFactory,
    ConsumeDelegates<TMessage> delegates) : BackgroundService
{
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var consumeOptions = options.Value;
        if (consumeOptions is not {ConcurrentConsume:true, ConcurrentCount: > 0})
        {
            return;
        }
        
        var channel = consumeOptions.BackgroundConsumeChannel;
        var backgroundConsumeTasks = new List<Task<bool>>(consumeOptions.ConcurrentCount);

        await foreach (var messageResult in channel.Reader.ReadAllAsync(stoppingToken))
        {
            backgroundConsumeTasks.Add(delegates.TryConsumerAsync(scopeFactory, messageResult));
            if (backgroundConsumeTasks.Count >= consumeOptions.ConcurrentCount)
            {
                await Task.WhenAll(backgroundConsumeTasks);
            }
        }
    }
}