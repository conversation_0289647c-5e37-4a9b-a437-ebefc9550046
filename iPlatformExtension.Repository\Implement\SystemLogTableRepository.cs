﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class SystemLogTableRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    CacheExpirationToken<SysLogTable> expirationToken,
    IRedisCache<RedisCacheOptionsBase> redisCache)
    : BaseRepository<SysLogTable, string>(freeSql), ISystemLogTableRepository
{
    public IMemoryCache MemoryCache { get; } = memoryCache;
    
    public CacheExpirationToken<SysLogTable> ExpirationToken { get; } = expirationToken;
    
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}