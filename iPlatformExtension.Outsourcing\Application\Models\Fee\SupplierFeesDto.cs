﻿using System.ComponentModel;

namespace iPlatformExtension.Outsourcing.Application.Models.Fee;

/// <summary>
/// 供应商费用集合
/// </summary>
[Description("供应商费用集合")]
public class SupplierFeesDto
{
    /// <summary>
    /// 费项集合id
    /// </summary>
    [Description("费项集合id")]
    public Guid FeesId { get; set; }
    
    /// <summary>
    /// 费项集合
    /// </summary>
    [Description("费项集合")]
    public IEnumerable<SupplierFeeDto> SupplierFees { get; set; } = [];
}