﻿namespace iPlatformExtension.MailCenter.Infrastructure.Provide
{
    /// <summary>
    /// 全局唯一 多方法共用时慎重
    /// </summary>
    public class SemaphoreProvider : ISemaphoreProvider
    {
        public SemaphoreSlim Semaphore { get; private set; }

        /// <summary>
        /// 初始化信号量
        /// </summary>
        public SemaphoreProvider()
        {
            Semaphore = new SemaphoreSlim(1); // 初始化信号量，这里的1表示同时只允许一个线程访问
        }
    }
}
