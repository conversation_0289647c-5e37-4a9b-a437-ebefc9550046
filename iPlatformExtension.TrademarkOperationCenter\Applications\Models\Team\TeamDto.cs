﻿﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;

/// <summary>
/// 团队列表
/// </summary>
/// <param name="TeamId">团队id主键</param>
/// <param name="TeamName">团队名称</param>
/// <param name="IsExclusive">是否专属</param>
/// <param name="IsEffect">是否生效</param>
/// <param name="TeamDescription">团队描述</param>
/// <param name="CreateTime">创建时间</param>
/// <param name="Seq">排序</param>
/// <param name="AuthorizeUser">授权用户ID</param>
/// <param name="AuthorizeUserNames">授权用户中文</param>
public record TeamDto(string TeamId, string TeamName, bool IsExclusive, bool IsEffect, string TeamDescription, DateTime CreateTime, int? Seq, string? AuthorizeUser = null)
{
    /// <summary>
    /// 授权用户中文名字列表
    /// </summary>
    public string? AuthorizeUserNames { get; set; }
};
