﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow
{
    public class GetFlowInfoDto
    {
        /// <summary>
        /// 创建用户名称
        /// </summary>
        public string CnName { get; set; }
        /// <summary>
        /// 创建用户ID
        /// </summary>
        public string CreateUserId { get; set; }
        /// <summary>
        /// 当前节点ID
        /// </summary>
        public string CurNodeId { get; set; }
        /// <summary>
        /// 流程实例ID
        /// </summary>
        public string FlowId { get; set; }
        /// <summary>
        /// 案件类型
        /// </summary>
        public string FlowSubType { get; set; }
        /// <summary>
        /// 案件类型
        /// </summary>
        public string FlowType { get; set; }
        /// <summary>
        /// 部门全称
        /// </summary>
        public string FullName { get; set; }
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsEnable { get; set; }
        /// <summary>
        /// 是否跳过
        /// </summary>
        public bool IsSkip { get; set; }
        /// <summary>
        /// 流程名称
        /// </summary>
        public string NameZhCn { get; set; }
        /// <summary>
        /// 节点编码
        /// </summary>
        public string NodeCode { get; set; }
        /// <summary>
        /// 任务ID
        /// </summary>
        public string ObjId { get; set; }
        /// <summary>
        /// 退回用户名称
        /// </summary>
        public string RejectCnName { get; set; }
        /// <summary>
        /// 退回节点
        /// </summary>
        public string RejectNodeId { get; set; }
        /// <summary>
        /// 退回用户ID
        /// </summary>
        public string RejectUserId { get; set; }
        /// <summary>
        /// 流程状态
        /// </summary>
        public int? Status { get; set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
        
        /// <summary>
        ///  当前处理人名称
        /// </summary>
        public string CurAuditName { get; set; }

        /// <summary>
        /// 当前处理人ID
        /// </summary>
        public string CurAuditUserID { get; set; }
        /// <summary>
        /// 当前处理人工号
        /// </summary>
        public string CurAuditUserName { get; set; }

        /// <summary>
        /// 核稿任务流程id
        /// </summary>
        public string ProcFlowId { get; set; }


        /// <summary>
        /// 任务ID
        /// </summary>
        public string ProcID { get; set; }
    }
}
