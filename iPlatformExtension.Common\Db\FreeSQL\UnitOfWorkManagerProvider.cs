﻿using System.Collections.Concurrent;
using FreeSql;
using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Db.FreeSQL;

public class UnitOfWorkManagerProvider(IServiceProvider serviceProvider)
{
    private static readonly ConcurrentDictionary<Type, Lazy<Type>> lazies = new();
    
    public UnitOfWorkManager GetUnitOfWorkManager(Type freeSqlType)
    {
        var managerType = lazies.GetOrAdd(freeSqlType,
            type => new Lazy<Type>(() => typeof(UnitOfWorkManage<>).MakeGenericType(type)));

        return (serviceProvider.GetRequiredService(managerType.Value) as UnitOfWorkManager)!;
    }
}