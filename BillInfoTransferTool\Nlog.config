<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      throwConfigExceptions="true"
      internalLogLevel="info">
    <!--autoReload：修改后自动加载，可能会有延迟-->
    <!--throwConfigExceptions：NLog日志系统抛出异常-->
    <!--internalLogLevel：内部日志的级别-->
    <!--internalLogFile：内部日志保存路径，日志的内容大概就是NLog的版本信息，配置文件的地址等等-->
    <!--输出日志的配置，用于rules读取-->
    <targets>
        <!--write logs to file-->
        <!--将日志写入文件中,fileName可以指定日志生成的路径-->
        <target xsi:type="File" name="allfile" fileName="logs\${shortdate}.log"
                layout="[${longdate}] [${uppercase:${level}}] [${logger}] : ${newline}${message} ${newline}
                ${exception:format=ToString} ${newline}" />
        <!--同样是将文件写入日志中，写入的内容有所差别，差别在layout属性中体现。写入日志的数量有差别，差别在路由逻辑中体现-->
<!--        <target xsi:type="File" name="ownFile-web" fileName="D:\Log\nlog-my-${shortdate}.log"-->
<!--                layout="${longdate}|${logger}|${uppercase:${level}}|${message} ${exception}" />-->
        <target xsi:type="Null" name="blackhole" />
    </targets>
    <rules>
        <!--路由顺序会对日志打印产生影响。路由匹配逻辑为顺序匹配。-->
        <!--All logs, including from Microsoft-->
        <logger name="*" minlevel="Warn" writeTo="allfile" />

        <!--Skip Microsoft logs and so log only own logs-->
        <!--以Microsoft打头的日志将进入此路由，由于此路由没有writeTo属性，所有会被忽略-->
        <!--且此路由设置了final，所以当此路由被匹配到时。不会再匹配此路由下面的路由。未匹配到此路由时才会继续匹配下一个路由-->
        <logger name="Microsoft.*" minlevel="Warn"  final="true" />
        <!--上方已经过滤了所有Microsoft.*的日志，所以此处的日志只会打印除Microsoft.*外的日志-->
        <!--<logger name="*" minlevel="Trace" writeTo="ownFile-web" />-->
    </rules>
</nlog> 