﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 已关联客户
/// </summary>
/// <param name="CustomerId">客户id</param>
/// <param name="CustomerName">客户名称</param>
/// <param name="CrmCustomerCode">客户编码</param>
/// <param name="CountryId">国家/地区</param>
/// <param name="IsCooperation">境外代理</param>
/// <param name="DistrictTemp">案源地区</param>
/// <param name="BigArea">所属大区</param>
/// <param name="CustomerType">客户分类</param>
/// <param name="BusiUserId">商务人员id</param>
/// <param name="BusiUser">商务人员</param>
/// <param name="Corporation">客户负责人</param>
/// <param name="RelateId">关联id</param>
public record GetRelateCustomerDto(
    string CustomerId,
    string CustomerName,
    string CrmCustomerCode,
    string CountryId,
    bool? IsCooperation,
    string DistrictTemp,
    string BigArea,
    string CustomerType,
    string BusiUserId,
    string Corporation)
{
    public object CreateBy { get; set; }
    public object BusiUser { get; set; }
    public DateTime? CreateTime { get; set; }
    public string RelateId { get; set; }

    public object Country { get; set; }

    public object District { get; set; }
};

