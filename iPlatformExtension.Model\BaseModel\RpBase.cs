using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_base", DisableSyncStructure = true)]
	public partial class RpBase {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "name", StringLength = 50)]
		public string Name { get; set; }

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

		[ Column(Name = "type", StringLength = 50)]
		public string Type { get; set; }

		[ Column(Name = "value", StringLength = 50)]
		public string Value { get; set; }

	}

}
