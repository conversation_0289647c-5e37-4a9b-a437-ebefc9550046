﻿using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Clients.Phoenix;

public class TokenHandler(IOptionsMonitor<PhoenixClientOptions> options, IHostEnvironment hostEnvironment)
    : DelegatingHandler
{
    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        if (hostEnvironment.IsProduction())
            return base.SendAsync(request, cancellationToken);

        var options1 = options.CurrentValue;
        var token = $"satoken={options1.SaToken}";
        request.Headers.Add("Cookie", [token]);

        return base.SendAsync(request, cancellationToken);
    }
}