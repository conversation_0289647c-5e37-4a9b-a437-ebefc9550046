﻿using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using iPlatformExtension.Model.Dto.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Nice;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Nice;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Proc;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers;

/// <summary>
/// 任务信息控制器
/// </summary>
[ApiController]
[Route("[controller]")]
[Produces(MediaTypeNames.Application.Json)]
public sealed class ProcInfoController(IMediator mediator) : ControllerBase
{

    /// <summary>
    /// 查询任务列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IEnumerable<ProcItemDto>> GetProcInfoAsync([FromQuery] ProcIdsQuery query)
    {
        return await mediator.Send(query);
    }

    /// <summary>
    /// 动态获取任务扩展信息
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <returns>扩展信息</returns>
    [HttpGet("{procId}/extension")]
    [AllowAnonymous]
    public Task<IReadOnlyDictionary<ProcExtensionKey, ProcExtensionItem>> GetProcExtensionInfoAsync(string procId)
    {
        return mediator.Send(new ProcExtensionQuery(procId), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 对某个类型的任务添加动态字段配置
    /// </summary>
    /// <param name="command">动态配置参数</param>
    /// <returns>统一响应</returns>
    [HttpPut("extension-config")]
    [Consumes(MediaTypeNames.Application.Json)]
    [AllowAnonymous]
    public Task SaveDynamicConfigAsync(SaveProcExtensionDynamicConfigCommand command)
    {
        return mediator.Send(command);
    }

    /// <summary>
    /// 更新任务
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <param name="jsonPatch">任务更新属性</param>
    /// <param name="version">版本号</param>
    /// <returns>统一返回接口</returns>
    [HttpPatch("{procId}")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    [Authorize(Policy = "Proc")]
    public Task UpdateAsync(
        [FromRoute, Required] string procId, 
        [FromBody] JsonPatchDocument<ProcTrademarkDeliveryDto> jsonPatch, 
        [FromQuery, Required] int version)
    {
        return mediator.Send(new UpdateProcCommand(procId, jsonPatch, version));
    }

    /// <summary>
    /// 获取默认的递交代理信息
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <returns>默认的递交代理信息</returns>
    [HttpGet("{procId}/default-agency-info")]
    public Task<DefaultAgencyDto> GetDefaultDeliveryAgencyInfoAsync(string procId)
    {
        return mediator.Send(new DefaultDeliveryAgencyInfoQuery(procId));
    }

    /// <summary>
    /// 按照给定的父级尼斯分类编号，查询尼斯分类信息
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <returns>尼斯分类数据</returns>
    [HttpGet("{procId}/nice-categories")]
    public Task<IEnumerable<NiceCategoryDto>> GetProcNiceCategoriesAsync([FromRoute, Required] string procId)
    {
        return mediator.Send(new ProcNiceCategoryQuery(procId));
    }

    /// <summary>
    /// 保存任务尼斯分类
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <param name="dto">尼斯分类数据</param>
    /// <returns>统一响应</returns>
    [HttpPost("{procId}/nice-categories")]
    [Consumes(MediaTypeNames.Application.Json)]
    public Task SaveProcNiceCategoriesAsync([Required]string procId, NiceCategoriesDto dto)
    {
        return mediator.Send(new SaveProcNiceCommand(procId, dto.CustomCategories, dto.GrandNumbers));
    }

    /// <summary>
    /// 新增一条法律与事实依据
    /// </summary>
    /// <param name="dto">法律与事实依据信息</param>
    /// <param name="procId">任务id</param>
    /// <returns>统一接口</returns>
    [HttpPost("{procId}/law-basis")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Authorize(Policy = "Proc")]
    public Task CreateTrademarkLawBasisAsync([FromBody] ProcTrademarkLawBasisDto dto, [FromRoute, Required]string procId)
    {
        dto.ProcId = procId;
        return mediator.Send(new InsertProcLawBasisCommand(dto));
    }

    /// <summary>
    /// 删除法律信息与事实
    /// </summary>
    /// <param name="id">律信息与事实id</param>
    /// <param name="procId">任务id</param>
    /// <returns>统一返回接口</returns>
    [HttpDelete("{procId}/law-basis/{id:int}")]
    [Authorize(Policy = "Proc")]
    public Task DeleteTrademarkLawBasisAsync([FromRoute]int id, [FromRoute, Required]string procId) =>
        mediator.Send(new DeleteProcLawBasisCommand(id));

    /// <summary>
    /// 更新法律信息与事实
    /// </summary>
    /// <param name="id">律信息与事实id</param>
    /// <param name="procId">任务id</param>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPut("{procId}/law-basis/{id:int}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Authorize(Policy = "Proc")]
    public Task UpdateTrademarkLawBasisAsync(
        [FromRoute, Required]int id, 
        [FromRoute, Required]string procId,
        [FromBody] ProcTrademarkLawBasisDto dto)
    {
        dto.ProcId = procId;
        return mediator.Send(new UpdateProcLawBasisCommand(id, dto));
    }

    /// <summary>
    /// 获取律信息与事实详情
    /// </summary>
    /// <param name="id">律信息与事实id</param>
    /// <returns>律信息与事实详情</returns>
    [HttpGet("/law-basis/{id:int}")]
    public Task<ProcTrademarkLawBasisDetail> GetTrademarkLawBasisAsync(int id) =>
        mediator.Send(new ProcTrademarkLawBasisDetailQuery(id));

    /// <summary>
    /// 创建引证商标信息
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <param name="dto">引证商标信息</param>
    /// <returns>统一接口</returns>
    [HttpPost("{procId}/cited-trademarks")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Authorize(Policy = "Proc")]
    public Task CreateCitedTrademarkAsync(
        [FromRoute, Required] string procId,
        [FromBody] ProcCitedTrademarkDto dto)
    {
        dto.ProcId = procId;
        return mediator.Send(new InsertProcCitedTrademarkCommand(dto));
    }

    /// <summary>
    /// 删除引证商标
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <param name="id">引证商标id</param>
    /// <returns>统一接口</returns>
    [HttpDelete("{procId}/cited-trademarks/{id:int}")]
    public Task DeleteCitedTrademarkAsync(
        [FromRoute, Required] string procId,
        [FromRoute, Required] int id) => mediator.Send(new DeleteProcCitedTrademarkCommand(id));

    /// <summary>
    /// 修改引证商标注册信息
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <param name="id">引证商标信息id</param>
    /// <param name="dto">引证商标内容</param>
    /// <returns>统一接口</returns>
    [HttpPut("{procId}/cited-trademarks/{id:int}")]
    [Consumes(MediaTypeNames.Application.Json)]
    public Task UpdateCitedTrademarkAsync(
        [FromRoute, Required] string procId,
        [FromRoute, Required] int id,
        [FromBody] ProcCitedTrademarkDto dto)
    {
        dto.ProcId = procId;
        return mediator.Send(new UpdateProcCitedTrademarkCommand(id, dto));
    }
    
    /// <summary>
    /// 获取引证商标详情信息
    /// </summary>
    /// <param name="id">引证商标信息id</param>
    /// <returns>引证商标详情信息</returns>
    [HttpGet("/cited-trademarks/{id:int}")]
    public Task<ProcCitedTrademarkDetail> GetCitedTrademarkAsync(int id) =>
        mediator.Send(new ProcCitedTrademarkQuery(id));
}