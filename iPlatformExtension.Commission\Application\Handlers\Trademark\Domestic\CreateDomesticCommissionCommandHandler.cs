using AutoMapper;
using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Commission.Application.Notifications.Trademark.Domestic;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class CreateDomesticCommissionCommandHandler(
    IDomesticTrademarkCommissionRepository commissionRepository,
    IMediator mediator, IMapper mapper, IHttpContextAccessor httpContextAccessor) 
    : IRequestHandler<CreateDomesticCommissionCommand>
{
    public async Task Handle(CreateDomesticCommissionCommand request, CancellationToken cancellationToken)
    {
        var (procId, dto) = request;
        var freeSql = commissionRepository.Orm;
        var domesticTrademarkCommission = await freeSql.Select<CaseProcInfo>(procId).ToOneAsync(info => new DomesticTrademarkCommission
        {
            ProcId = info.ProcId,
            AppNo = info.CaseInfo.AppNo,
            CaseName = info.CaseInfo.CaseName,
            CommissionDate = info.CommissionEffectiveDate!.Value,
            CtrlProcId = info.CtrlProcId,
            CtrlProcMark = info.CtrlProcMark ?? string.Empty,
            PointDate = DateTime.Now,
            ProcNo = info.ProcNo,
            ProcStatusId = info.ProcStatusId,
            RegisterNo = info.CaseInfo.RegisterNo,
            TrademarkClasses = info.CaseInfo.TrademarkClass ?? string.Empty,
            TrademarkItemsNum = 0,
            ProcMainUndertakerId = info.ProcUndertakeMainUserId ?? string.Empty,
            Volume = info.CaseInfo.Volume,
            CustomerId = info.CaseInfo.CustomerId,
            Year = info.CommissionEffectiveDate!.Value.Year,
            Month = info.CommissionEffectiveDate!.Value.Month,
            CaseId = info.CaseId,
            Creator = UserIds.Administrator.ToSqlStringConstant(),
            Updater = UserIds.Administrator.ToSqlStringConstant()
        }, cancellationToken);
        
        await mediator.Publish(new DomesticCommissionResultNotification(domesticTrademarkCommission), cancellationToken);

        if (dto is not null)
        {
            if (await freeSql.Select<DomesticTrademarkCommission>(procId).AnyAsync(cancellationToken))
            {
                throw new ApplicationException("数据已存在，不能重复添加");
            }
            
            mapper.Map(dto, domesticTrademarkCommission);
            
            var userId = httpContextAccessor.HttpContext?.User.GetUserId() ?? string.Empty;
            domesticTrademarkCommission.Creator = userId;
            domesticTrademarkCommission.Updater = userId;
            
            await commissionRepository.InsertAsync(domesticTrademarkCommission, cancellationToken);
        } 
        else if (await mediator.Send(new ComputeDomesticCommissionPointCommand(domesticTrademarkCommission), cancellationToken))
        {
            await commissionRepository.InsertAsync(domesticTrademarkCommission, cancellationToken);
        }
    }
}