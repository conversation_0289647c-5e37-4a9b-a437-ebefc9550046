﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 申请人信息
/// </summary>
public class DeliveryApplicantInfo
{
    /// <summary>
    /// 申请人地址(中文)
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("applicantAddress")]
    public string? ApplicantAddress { get; set; }

    /// <summary>
    /// 申请主体名称(英文)
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("applicantEnglishAddress")]
    public string? ApplicantEnglishAddress { get; set; }

    /// <summary>
    /// 申请主体名称(英文)
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("applicantEnglishName")]
    public string? ApplicantEnglishName { get; set; }

    /// <summary>
    /// 申请主体名称(中文)
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("applicantName")]
    public string ApplicantName { get; set; } = null!;

    /// <summary>
    /// 区
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("area")]
    public long? Area { get; set; }

    /// <summary>
    /// 书式类型,1表示大陆,2表示海外,3表示台湾,4表示香港,5表示澳门
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("bookType")]
    public string BookType { get; set; } = null!;

    /// <summary>
    /// 证件类型,0不是任何类型,1身份证,2护照,3其他
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("certificatesType")]
    public string? CertificatesType { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("city")]
    public long? City { get; set; }

    /// <summary>
    /// 邮编
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("code")]
    public string? Code { get; set; }

    /// <summary>
    /// 国家或地区
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("country")]
    public string Country { get; set; } = null!;

    /// <summary>
    /// 国家英文简称
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("country01")]
    public string? CountryEnglishName { get; set; }

    /// <summary>
    /// 申请人联系地址
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("domesticReceiverAddress")]
    public string? DomesticReceiverAddress { get; set; }

    /// <summary>
    /// 申请人联系邮编
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("domesticReceiverCode")]
    public string? DomesticReceiverCode { get; set; } 

    /// <summary>
    /// 申请人联系邮箱
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("domesticReceiverEmail")]
    public string? DomesticReceiverEmail { get; set; }

    /// <summary>
    /// 申请人国内联系名称
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? DomesticReceiverName { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("idCard")]
    public string? IdCard { get; set; }

    /// <summary>
    /// 法人
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("legalPerson")]
    public string? LegalPerson { get; set; }

    /// <summary>
    /// 商标申请人类型,商标持有人类型,0个人持有，1公司持有
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("ownerType")]
    public string OwnerType { get; set; } = null!;

    /// <summary>
    /// 职务
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("post")]
    public string? Post { get; set; }

    /// <summary>
    /// 省
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("prov")]
    public long? Province { get; set; }

    /// <summary>
    /// 主体资格类型,主体资格是否为中文类型,和身份证原件是否为中文共用,1表示中文,0表示非中文
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("subjectType")]
    public string SubjectType { get; set; } = null!;

    /// <summary>
    /// 统一社会信用代码
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("unifiedSocialCreditCode")]
    public string? UnifiedSocialCreditCode { get; set; }
}