﻿{
  "nacos": {
    "ServerAddresses": [ "************:18848" ],
    "DefaultTimeOut": 15000,
    "Namespace": "test",
    "ListenInterval": 1000,
    "ServiceName": "iPlatformExtension.MailCenter",
    "UserName": "nacos",
    "Password": "Acip1234",
    "Ip": "************",
    "Port": 8081,
    "Listeners": [
      {
        "Optional": true,
        "DataId": "appsettings.json",
        "Group": "iPlatformExtension"
      },
      {
        "Optional": true,
        "DataId": "clients.json",
        "Group": "iPlatformExtension.MailCenter"
      },
      {
        "Optional": true,
        "DataId": "mqs.json",
        "Group": "iPlatformExtension.MailCenter"
      }
    ]
  },
  "IPlatformAuth": {
    "Issuers": [
      "ipr.aciplaw.com"
    ],
    "Audiences": [
      "patas.aciplaw.com",
      "patas-test.aciplaw.com",
      "patas-dev.aciplaw.com"
    ],
    "Metadata": {
      "ASPNETCORE_HTTPS_PORTS": "8081"
    }
  }
}
