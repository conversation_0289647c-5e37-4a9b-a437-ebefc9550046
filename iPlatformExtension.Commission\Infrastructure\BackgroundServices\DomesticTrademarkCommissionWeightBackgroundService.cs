using System.Threading.Channels;
using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;

namespace iPlatformExtension.Commission.Infrastructure.BackgroundServices;

internal class DomesticTrademarkCommissionWeightBackgroundService(
    IServiceScopeFactory serviceScopeFactory,
    Channel<CreateDomesticCommissionWeightCommand> channel,
    ILogger<DomesticTrademarkCommissionWeightBackgroundService> logger) 
    : BackgroundConsumeService<CreateDomesticCommissionWeightCommand>(channel, logger, serviceScopeFactory);