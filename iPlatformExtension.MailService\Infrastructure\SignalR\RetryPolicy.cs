﻿using Microsoft.AspNetCore.SignalR.Client;

namespace iPlatformExtension.MailService.Infrastructure.SignalR
{
    public class RetryPolicy : IRetryPolicy
    {
        public TimeSpan? NextRetryDelay(RetryContext retryContext)
        {
            var count = retryContext.PreviousRetryCount / 50;
            if (count < 1)
            {
                return new TimeSpan(0, 0, 1);
            }
            else if (count < 5)
            {
                return new TimeSpan(0, 0, 30);
            }
            else
            {
                return new TimeSpan(0, 1, 0);
            }
        }
    }
}
