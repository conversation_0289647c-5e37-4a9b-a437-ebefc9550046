﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 相关案id集合
/// </summary>
public class RelatedCasesIds
{
    /// <summary>
    /// 新案的id集合
    /// </summary>
    public HashSet<string> NewCaseIds { get; set; } = new();

    /// <summary>
    /// 旧案的id集合
    /// </summary>
    public HashSet<string> OldCaseIds { get; set; } = new();

    /// <summary>
    /// 相关按数量
    /// </summary>
    [JsonIgnore]
    public int RelatedCount => NewCaseIds.Count + OldCaseIds.Count;
}