using System.Diagnostics;
using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;

namespace ConsoleScript;

public class ApplyCaseRelatedTask
{
    public async Task RunAsync()
    {
        var freeSql = new FreeSqlBuilder()
            .UseConnectionString(DataType.SqlServer, "data source=***************,7433;initial catalog=acip_iplatform_1009;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true;Min Pool Size=5;Max Pool Size=100;Connection Lifetime=300").Build();
        
        var applyCaseRelatedGroups = (await freeSql.Select<ApplyCaseRelated>().WithLock()
                .Where(related => related.ApplyId == "50ebde03-b3a7-436f-8f76-d16a786f171a").ToListAsync())
            .GroupBy(related => related.CaseId);
        
        
        var caseRelatedList = new List<CaseRelatedList>(5);
            var updateCaseRelatedList = new List<CaseRelatedKeyInfo>(5);
            var sameDaySets = new Dictionary<string, HashSet<string>>(5);
            var sameSubmitSets = new Dictionary<string, HashSet<string>>(5);
            var familyOtherSet = new HashSet<string>(5);
            
            var sameDaySetList = new List<HashSet<string>>(5);
            var sameSubmitSetList = new List<HashSet<string>>(5);

            var familyRelatedList = new Dictionary<string, CaseRelatedList>(5);

            foreach (var applyCaseRelatedList in applyCaseRelatedGroups)
            {
                foreach (var applyCaseRelated in applyCaseRelatedList)
                {
                    if (applyCaseRelated.RelatedType == CaseRelatedType.CaseResubmit.GetDescription())
                    {
                        var caseRelated = new CaseRelatedList()
                        {
                            CreateUserId = "admin",
                            CreateTime = DateTime.Now,
                            CaseId = applyCaseRelated.CaseId,
                            RelatedCaseId = applyCaseRelated.RelatedCaseId,
                            RelatedType = "case_resubmit",
                            RelatedId = Guid.NewGuid().ToString()
                        };
                        caseRelatedList.Add(caseRelated);
                    }
                    else if (applyCaseRelated.RelatedType == CaseRelatedType.Other.GetDescription())
                    {
                        var caseRelated = new CaseRelatedList()
                        {
                            CreateUserId = "admin",
                            CreateTime = DateTime.Now,
                            CaseId = applyCaseRelated.CaseId,
                            RelatedCaseId = applyCaseRelated.RelatedCaseId,
                            RelatedType = "tech_related",
                            RelatedId = Guid.NewGuid().ToString()
                        };
                        caseRelatedList.Add(caseRelated);
                    }
                    else
                    {
                        var caseId = applyCaseRelated.CaseId;
                        // var familyId = Guid.NewGuid().ToString();
                        // var sameDayId = Guid.NewGuid().ToString();
                        // var sameSubmitId = Guid.NewGuid().ToString();
                        var caseRelated1 = new CaseRelatedList()
                        {
                            CreateUserId = "admin",
                            CreateTime = DateTime.Now,
                            CaseId = applyCaseRelated.CaseId,
                            RelatedType = "family",
                            RelatedId = Guid.NewGuid().ToString()
                        };
                        
                        var caseRelated2 = new CaseRelatedList()
                        {
                            CreateUserId = "admin",
                            CreateTime = DateTime.Now,
                            CaseId = applyCaseRelated.RelatedCaseId,
                            RelatedType = "family",
                            RelatedId = Guid.NewGuid().ToString()
                        };
                        
                        switch (applyCaseRelated.RelatedType)
                        {
                            case "same_day":
                                if (!sameDaySets.ContainsKey(applyCaseRelated.RelatedCaseId))
                                {
                                    if (!sameDaySets.TryGetValue(caseId, out var sameDaySet))
                                    {
                                        sameDaySet = new HashSet<string>(2);
                                        if (sameDaySet.Add(applyCaseRelated.CaseId))
                                        {
                                            sameDaySetList.Add(sameDaySet);
                                            sameDaySets.Add(caseId, sameDaySet);
                                        }
                                    }

                                    if (sameDaySet.Add(applyCaseRelated.RelatedCaseId))
                                    {
                                        sameDaySets.Add(applyCaseRelated.RelatedCaseId, sameDaySet);
                                    }
                                }
                                break;
                            case "same_submit":
                                if (!sameSubmitSets.ContainsKey(applyCaseRelated.RelatedCaseId))
                                {
                                    if (!sameSubmitSets.TryGetValue(caseId, out var sameSubmitSet))
                                    {
                                        sameSubmitSet = new HashSet<string>(2);
                                        if (sameSubmitSet.Add(applyCaseRelated.CaseId))
                                        {
                                            sameSubmitSetList.Add(sameSubmitSet);
                                            sameSubmitSets.Add(caseId, sameSubmitSet);
                                        }
                                    }

                                    if (sameSubmitSet.Add(applyCaseRelated.RelatedCaseId))
                                    {
                                        sameSubmitSets.Add(applyCaseRelated.RelatedCaseId, sameSubmitSet);
                                    }
                                }
                                break;
                            default:
                                familyOtherSet.Add(applyCaseRelated.CaseId);
                                familyOtherSet.Add(applyCaseRelated.RelatedCaseId);
                                break;
                        }

                        familyRelatedList[caseId] = caseRelated1;
                        familyRelatedList[applyCaseRelated.RelatedCaseId] = caseRelated2;
                    }
    
                    
                }
            }

            // 验证1+1套案的数量
            if (sameDaySets.Any(set => set.Value.Count != 2))
            {
                throw new ApplicationException("1+1套案数量有误");
            }

            var sameDayCaseIdSet = sameDaySets.SelectMany(set => set.Value).ToHashSet();
            var sameSubmitCaseIdSet = sameSubmitSets.SelectMany(set => set.Value).ToHashSet();
            familyOtherSet.UnionWith(sameDayCaseIdSet);
            familyOtherSet.UnionWith(sameSubmitCaseIdSet);
            
            // 验证1+1是否已关联
            if (await freeSql.Select<CaseRelatedList>().AnyAsync(list => sameDayCaseIdSet.Contains(list.CaseId) && !string.IsNullOrEmpty(list.SameDayId)))
            {
                throw new ApplicationException("1+1套案已存在关联");
            }

            var caseList = new Dictionary<string, CaseRelatedKeyInfo>(familyOtherSet.Count);
            foreach (var caseFamilyRelatedKeyInfo in await freeSql.Select<CaseInfo>().WithLock()
                         .From<BasApplyType, CaseRelatedList>((caseInfo, applyType, caseRelated) =>
                             caseInfo.InnerJoin(info => info.ApplyTypeId == applyType.ApplyTypeId)
                                 .InnerJoin(info => info.Id == caseRelated.CaseId))
                         .Where((caseInfo, applyType, caseRelated) => familyOtherSet.Contains(caseInfo.Id) && caseRelated.RelatedType == "family")
                         .ToListAsync((caseInfo, applyType, caseRelated) => new CaseRelatedKeyInfo()
                         {
                             RelatedId = caseRelated.RelatedId,
                             CaseId = caseInfo.Id,
                             ApplyTypeCode = applyType.ApplyTypeCode,
                             DeliveryDate = caseInfo.DeliverDate,
                             FamilyId = caseRelated.FamilyId,
                             SameSayId = caseRelated.SameDayId,
                             SameSubmitId = caseRelated.SameSubmitId,
                             RelatedType = caseRelated.RelatedType
                         }))
            {
                caseList[caseFamilyRelatedKeyInfo.CaseId] = caseFamilyRelatedKeyInfo;
            }
            
            // 验证套案的申请类型和递交日期
            // foreach (var sameDaySet in sameDaySets)
            // {
            //     var set = sameDaySet.Value;
            //     if (!set.Any(id =>
            //             caseList.TryGetValue(id, out var caseRelatedKeyInfo) &&
            //             caseRelatedKeyInfo.ApplyTypeCode == "UT"))
            //         throw new ApplicationException("1+1套案中没有实用新型申请类型");
            //     
            //     if (!set.Any(id =>
            //             caseList.TryGetValue(id, out var caseRelatedKeyInfo) &&
            //             caseRelatedKeyInfo.ApplyTypeCode == "IV"))
            //         throw new ApplicationException("1+1套案中没有发明申请类型");
            //
            //     var deliverDate = caseList[set.First()].DeliveryDate?.Date;
            //     if (set.Any(id =>
            //             caseList.TryGetValue(id, out var caseRelatedKeyInfo) &&
            //             caseRelatedKeyInfo.DeliveryDate?.Date != deliverDate))
            //     {
            //         throw new ApplicationException("1+1套案中新申请递交日期不一致");
            //     }
            //     
            // }

            // 验证同日递交的递交日期
            // foreach (var sameSubmitSet in sameSubmitSets)
            // {
            //     var set = sameSubmitSet.Value;
            //
            //     var deliverDate = caseList.TryGetValue(set.First(), out var defaultSet)
            //         ? defaultSet.DeliveryDate?.Date
            //         : null;
            //     if (set.Any(id =>
            //             caseList.TryGetValue(id, out var caseRelatedKeyInfo) &&
            //             caseRelatedKeyInfo.DeliveryDate?.Date != deliverDate))
            //     {
            //         throw new ApplicationException("同日递交新申请递交日期不一致");
            //     }
            //     
            // }
            
            // 赋值sameDayId
            foreach (var sameDaySet in sameDaySetList)
            {
                string oldFamilyId = null;
                
                // 验证一个案件是否只关联了一个家族
                var oldFamilyIds = sameDaySet.SelectMany(id =>
                {
                    var familyIds = new List<string>();
                    if (caseList.TryGetValue(id, out var sameDayRelatedInfo) && !string.IsNullOrWhiteSpace(sameDayRelatedInfo.FamilyId))
                    {
                        familyIds.Add(sameDayRelatedInfo.FamilyId);
                    }
                    
                    if (sameSubmitSets.TryGetValue(id, out var submitSet))
                    {
                        familyIds.AddRange(submitSet.Select(submitId =>
                            caseList.TryGetValue(submitId, out var submitRelatedInfo)
                                ? submitRelatedInfo.FamilyId
                                : string.Empty));
                    }

                    return familyIds;
                }).ToHashSet();

                oldFamilyIds.Remove(string.Empty);
                
                // 关联的旧案的家族id不为空。要把所有关联的案件合并为一个家族案
                if (oldFamilyIds.Count > 0)
                {
                    
                    var relatedInfos = await freeSql.Select<CaseRelatedList>().WithLock()
                        .Where(list => oldFamilyIds.Contains(list.FamilyId))
                        .ToListAsync(list => new CaseRelatedKeyInfo()
                        {
                            CaseId = list.CaseId,
                            FamilyId = list.FamilyId,
                            RelatedId = list.RelatedId,
                            SameSayId = list.SameDayId,
                            SameSubmitId = list.SameSubmitId,
                            RelatedType = list.RelatedType
                        });
                    oldFamilyId = oldFamilyIds.First();
                    foreach (var caseRelatedKeyInfo in relatedInfos)
                    {
                        if (caseList.TryGetValue(caseRelatedKeyInfo.CaseId, out var relatedKeyInfo))
                        {
                            relatedKeyInfo.FamilyId = oldFamilyId;
                            relatedKeyInfo.NeedToUpdate = true;
                        }
                        else
                        {
                            caseRelatedKeyInfo.FamilyId = oldFamilyId;
                            caseRelatedKeyInfo.NeedToUpdate = true;
                            caseList[caseRelatedKeyInfo.CaseId] = caseRelatedKeyInfo;
                        }
                    }
                    // throw new ApplicationException("一个案件只能关联一个家族案");
                }

                var sameDayId = Guid.NewGuid().ToString();
                foreach (var sameDayCaseId in sameDaySet)
                {
                    Debug.Assert(familyRelatedList.ContainsKey(sameDayCaseId), $"{sameDayCaseId}相关案数据没有添加到对应的缓存中");
                    var caseRelated = familyRelatedList[sameDayCaseId];

                    
                    caseRelated.SameDayId = sameDayId;
                    if (oldFamilyIds.Count > 0)
                    {
                        caseRelated.FamilyId = oldFamilyId;
                    }

                    if (caseList.TryGetValue(sameDayCaseId, out var relatedKeyInfo))
                    {
                        relatedKeyInfo.SameSayId = sameDayId;
                        relatedKeyInfo.NeedToUpdate = true;
                        // caseRelated.FamilyId = relatedKeyInfo.FamilyId;
                    }
                }
            }
            
            // 赋值sameSubmitId
            foreach (var sameSubmitSet in sameSubmitSetList)
            {
                string oldFamilyId = null;
                
                // 验证一个案件是否只关联了一个家族
                var oldFamilyIds = sameSubmitSet.SelectMany(id =>
                {
                    var familyIds = new List<string>();
                    if (caseList.TryGetValue(id, out var sameSubmitInfo) && !string.IsNullOrWhiteSpace(sameSubmitInfo.FamilyId))
                    {
                        familyIds.Add(sameSubmitInfo.FamilyId);
                    }
                    
                    if (sameDaySets.TryGetValue(id, out var sameDaySet))
                    {
                        
                        familyIds.AddRange(sameDaySet.Select(sameDayId =>
                            caseList.TryGetValue(sameDayId, out var sameDayRelatedInfo)
                                ? sameDayRelatedInfo.FamilyId
                                : string.Empty)) ;
                    }

                    return familyIds;
                }).ToHashSet();

                oldFamilyIds.Remove(string.Empty);
                if (oldFamilyIds.Count > 0)
                {
                    var relatedInfos = await freeSql.Select<CaseRelatedList>().WithLock()
                        .Where(list => oldFamilyIds.Contains(list.FamilyId))
                        .ToListAsync(list => new CaseRelatedKeyInfo()
                        {
                            CaseId = list.CaseId,
                            FamilyId = list.FamilyId,
                            RelatedId = list.RelatedId,
                            SameSayId = list.SameDayId,
                            SameSubmitId = list.SameSubmitId,
                            RelatedType = list.RelatedType
                        });
                    oldFamilyId = oldFamilyIds.First();
                    foreach (var caseRelatedKeyInfo in relatedInfos)
                    {
                        if (caseList.TryGetValue(caseRelatedKeyInfo.CaseId, out var relatedKeyInfo))
                        {
                            relatedKeyInfo.FamilyId = oldFamilyId;
                            relatedKeyInfo.NeedToUpdate = true;
                        }
                        else
                        {
                            caseRelatedKeyInfo.FamilyId = oldFamilyId;
                            caseRelatedKeyInfo.NeedToUpdate = true;
                            caseList[caseRelatedKeyInfo.CaseId] = caseRelatedKeyInfo;
                        }
                    }
                    // throw new ApplicationException("一个案件只能关联一个家族案");
                }
                
                var sameSubmitId = Guid.NewGuid().ToString();
                foreach (var sameSubmitCaseId in sameSubmitSet)
                {
                    Debug.Assert(familyRelatedList.ContainsKey(sameSubmitCaseId), $"{sameSubmitCaseId}相关案数据没有添加到对应的缓存中");
                    var caseRelated = familyRelatedList[sameSubmitCaseId];
                    
                    
                    caseRelated.SameSubmitId = sameSubmitId;
                    if (oldFamilyIds.Count > 0)
                    {
                        caseRelated.FamilyId = oldFamilyId;
                    }
                    
                    if (sameDaySets.TryGetValue(sameSubmitCaseId, out var sameDaySet))
                    {
                        // 既是1+1又是同日递交，补全另外关联的1+1的同日递交关系
                        foreach (var sameDayCaseId in sameDaySet)
                        {
                            Debug.Assert(familyRelatedList.ContainsKey(sameDayCaseId), $"{sameDayCaseId}相关案数据没有添加到对应的缓存中");
                            familyRelatedList[sameDayCaseId].SameSubmitId = sameSubmitId;
                            if (sameDayCaseId != sameSubmitCaseId && sameSubmitSets.TryGetValue(sameDayCaseId, out var otherSameSubmitSet) && otherSameSubmitSet != sameSubmitSet)
                            {
                                foreach (var caseId in otherSameSubmitSet)
                                {
                                    familyRelatedList[caseId].SameSubmitId = sameSubmitId;
                                }
                            }
                            if (caseList.TryGetValue(sameDayCaseId, out var sameDayOldCaseRelated))
                            {
                                sameDayOldCaseRelated.SameSubmitId = sameSubmitId;
                                sameDayOldCaseRelated.NeedToUpdate = true;
                            }
                        }
                    }
                    
                    if (caseList.TryGetValue(sameSubmitCaseId, out var relatedKeyInfo))
                    {
                        // caseRelated.FamilyId = relatedKeyInfo.FamilyId;
                       
                        relatedKeyInfo.NeedToUpdate = true;

                        var oldSameSubmitList = caseList.Where(c =>
                            c.Value.SameSubmitId == relatedKeyInfo.SameSubmitId &&
                            c.Value.FamilyId == relatedKeyInfo.FamilyId).Select(c => c.Value).ToList();
                        oldSameSubmitList.ForEach(c =>
                        {
                            c.FamilyId = oldFamilyId;
                            c.SameSubmitId = sameSubmitId;
                            c.NeedToUpdate = true;
                            caseList[c.CaseId] = c;
                        });
                        

                        relatedKeyInfo.SameSubmitId = sameSubmitId;

                        // updateCaseRelatedList.Add(new CaseRelatedKeyInfo()
                        // {
                        //     RelatedId = relatedKeyInfo.RelatedId,
                        //     SameSayId = relatedKeyInfo.SameSayId,
                        //     SameSubmitId = sameSubmitId
                        // });

                        // familyRelatedList.Remove(sameSubmitCaseId);
                    }
                }
            }
            
            familyOtherSet.ExceptWith(sameDayCaseIdSet);
            familyOtherSet.ExceptWith(sameSubmitCaseIdSet);
            string oldOtherFamilyId = null;
            var oldOtherFamilyIds = familyOtherSet.Select(id =>
            {
                if (caseList.TryGetValue(id, out var familyOtherRelatedInfo) &&
                    !string.IsNullOrWhiteSpace(familyOtherRelatedInfo.FamilyId))
                {
                    return familyOtherRelatedInfo.FamilyId;
                }

                return string.Empty;
            }).ToHashSet();
            oldOtherFamilyIds.Remove(string.Empty);
            if (oldOtherFamilyIds.Count > 0)
            {
                var relatedInfos = await freeSql.Select<CaseRelatedList>().WithLock()
                    .Where(list => oldOtherFamilyIds.Contains(list.FamilyId))
                    .ToListAsync(list => new CaseRelatedKeyInfo()
                    {
                        CaseId = list.CaseId,
                        FamilyId = list.FamilyId,
                        RelatedId = list.RelatedId,
                        SameSayId = list.SameDayId,
                        SameSubmitId = list.SameSubmitId,
                        RelatedType = list.RelatedType
                    });
                oldOtherFamilyId = oldOtherFamilyIds.First();

                foreach (var caseRelatedKeyInfo in relatedInfos)
                {
                    if (caseList.TryGetValue(caseRelatedKeyInfo.CaseId, out var relatedKeyInfo))
                    {
                        relatedKeyInfo.FamilyId = oldOtherFamilyId;
                        relatedKeyInfo.NeedToUpdate = true;
                    }
                    else
                    {
                        caseRelatedKeyInfo.FamilyId = oldOtherFamilyId;
                        caseRelatedKeyInfo.NeedToUpdate = true;
                        caseList[caseRelatedKeyInfo.CaseId] = caseRelatedKeyInfo;
                    }
                }
            }
            foreach (var familyOtherCaseId in familyOtherSet)
            {
                Debug.Assert(familyRelatedList.ContainsKey(familyOtherCaseId), $"{familyOtherCaseId}相关案数据没有添加到对应的缓存中");
                var caseRelated = familyRelatedList[familyOtherCaseId];

                if (oldOtherFamilyIds.Count > 0)
                {
                    caseRelated.FamilyId = oldOtherFamilyId;
                }
            }

            // 家族案中的旧案的相关案数据从待添加中移除
            foreach (var caseRelatedKeyInfo in caseList)
            {
                familyRelatedList.Remove(caseRelatedKeyInfo.Key);
            }
            
            
            
            var familyId = Guid.NewGuid().ToString();
            foreach (var entry in familyRelatedList)
            {
                var related = entry.Value;
                if (string.IsNullOrWhiteSpace(related.FamilyId))
                {
                    related.FamilyId = familyId;
                }
                caseRelatedList.Add(related);
            }

            Console.ReadKey();

            // var unitOfWork = freeSql.CreateUnitOfWork();
            // var transaction = unitOfWork.GetOrBeginTransaction();
            // foreach (var relatedList in caseRelatedList)
            // {
            //     await freeSql.Insert(relatedList).WithTransaction(transaction).ExecuteAffrowsAsync();
            // }
            // foreach (var caseRelatedInfo in caseList)
            // {
            //     var caseRelatedKeyInfo = caseRelatedInfo.Value;
            //     if (caseRelatedKeyInfo.NeedToUpdate)
            //     {
            //         await freeSql.Update<CaseRelatedList>(caseRelatedKeyInfo.RelatedId).WithTransaction(transaction)
            //             .Set(list => new CaseRelatedList()
            //             {
            //                 SameDayId = caseRelatedKeyInfo.SameSayId,
            //                 SameSubmitId = caseRelatedKeyInfo.SameSubmitId,
            //                 FamilyId = caseRelatedKeyInfo.FamilyId
            //             }).ExecuteAffrowsAsync();
            //     }
            // }
            //
            // unitOfWork.Commit();
    }
    
    
    public class CaseRelatedKeyInfo
    {
        public string RelatedId { get; set; }
        
        public string CaseId { get; set; }

        public DateTime? DeliveryDate { get; set; }

        public string ApplyTypeCode { get; set; }

        public string FamilyId { get; set; }

        public string SameSayId { get; set; }

        public string SameSubmitId { get; set; }

        public string RelatedType { get; set; }

        public bool NeedToUpdate { get; set; }
        
    }
}