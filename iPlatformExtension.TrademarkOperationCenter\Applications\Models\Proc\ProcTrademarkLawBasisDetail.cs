﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;

/// <summary>
/// 法律事实与依据详情
/// </summary>
public sealed class ProcTrademarkLawBasisDetail
{
    /// <summary>
    /// id
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 法律条款id
    /// </summary>
    public string LawBasisId { get; set; } = string.Empty;

    /// <summary>
    /// 事实理由
    /// </summary>
    public string FactualReason { get; set; } = string.Empty;

    /// <summary>
    /// 证据材料文件id
    /// </summary>
    public int FileId { get; set; }

    /// <summary>
    /// 任务id
    /// </summary>
    public string ProcId { get; set; } = string.Empty;

    /// <summary>
    /// 证据材料
    /// </summary>
    public string EvidenceName { get; set; } = string.Empty;
}