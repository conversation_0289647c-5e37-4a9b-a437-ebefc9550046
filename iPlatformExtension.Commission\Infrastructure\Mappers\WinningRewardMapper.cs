﻿using AutoMapper;
using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Commission.Infrastructure.Mappers;

internal sealed class WinningRewardMapper : Profile
{
    public WinningRewardMapper()
    {
        CreateMap<RuleDto, WinningRewardRule>();
        CreateMap<WinningRewardRule, RuleDto>();
        
        CreateMap<WinningRewardRule, RulePatchDto>();
        CreateMap<RulePatchDto, WinningRewardRule>();

        CreateMap<RewardCreateDto, WinningRewardProc>()
            .ForMember(proc => proc.Year, expression => expression.MapFrom(dto => dto.CommissionDate.Year))
            .ForMember(proc => proc.Month, expression => expression.MapFrom(dto => dto.CommissionDate.Month))
            .ForMember(proc => proc.Beneficiaries, expression => expression.MapFrom((dto, proc) =>
            {
                var beneficiaries = proc.Beneficiaries;
                if (beneficiaries.TryGetValue(WinningRewardBeneficiaryType.Undertaker, out var undertakerBeneficiary))
                {
                    undertakerBeneficiary.EditedReward = dto.WriterReward;
                }

                if (beneficiaries.TryGetValue(WinningRewardBeneficiaryType.Mentor, out var mentorBeneficiary))
                {
                    mentorBeneficiary.EditedReward = dto.MentorReward;
                }
                
                return new SortedList<WinningRewardBeneficiaryType, WinningRewardUser>(beneficiaries);
            }));

        CreateMap<WinningRewardProc, RewardPatchDto>()
            .ForMember(dto => dto.Beneficiaries, expression => expression.MapFrom((proc, _) =>
            {
                var beneficiaries = proc.Beneficiaries;
                return beneficiaries.ToDictionary(pair => pair.Key, pair => new RewardDetailPatchDto
                {
                    Reward = pair.Value.EditedReward ?? pair.Value.Reward
                });
            }));
        CreateMap<RewardPatchDto, WinningRewardProc>()
            .ForMember(proc => proc.Beneficiaries, expression => expression.MapFrom((dto, proc) =>
            {
                var beneficiaries = proc.Beneficiaries;
                foreach (var (winningRewardBeneficiaryType, rewardDetailPatchDto) in dto.Beneficiaries)
                {
                    beneficiaries[winningRewardBeneficiaryType].EditedReward = rewardDetailPatchDto.Reward;
                }

                return new SortedList<WinningRewardBeneficiaryType, WinningRewardUser>(beneficiaries);
            }));
    }
}