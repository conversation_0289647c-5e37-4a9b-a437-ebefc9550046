﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 流程节点资源配置
	/// </summary>
	[Table(Name = "flow_node_resources", DisableSyncStructure = true)]
	public class FlowNodeResources {

		/// <summary>
		/// 主键
		/// </summary>
		[Column(Name = "id", IsIdentity = true)]
		public int Id { get; set; }

		/// <summary>
		/// 历程节点id
		/// </summary>
		[Column(Name = "node_id", StringLength = 50, IsNullable = false)]
		public string NodeId { get; set; } = default!;

		/// <summary>
		/// 资源
		/// </summary>
		[Column(Name = "resource", StringLength = 50, IsNullable = false)]
		public string Resource { get; set; } = default!;

	}

}
