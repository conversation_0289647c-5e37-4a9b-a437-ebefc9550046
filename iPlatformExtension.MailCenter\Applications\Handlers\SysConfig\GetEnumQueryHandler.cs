﻿using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using MediatR;
using System.ComponentModel;
using System.Reflection;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    /// <summary>
    /// 获取枚举值
    /// </summary>
    internal sealed class GetEnumQueryHandler(IMailConfigRepository mailConfigRepository) : IRequestHandler<GetEnumQuery, IEnumerable<GetEnumDto>>
    {
        public async Task<IEnumerable<GetEnumDto>> Handle(GetEnumQuery request, CancellationToken cancellationToken)
        {
            var path = AppDomain.CurrentDomain.RelativeSearchPath ?? AppDomain.CurrentDomain.BaseDirectory;
            var getFiles = Directory.GetFiles(path, "iPlatformExtension.Model.dll").FirstOrDefault();
            if (getFiles is null)
            {
                throw new NullReferenceException("找不到程序集");
            }
            var loadFrom = Assembly.LoadFrom(getFiles);

            return GetEnumerable(loadFrom.GetType("iPlatformExtension.Model.Enum.SysEnum+" + request.Enum));
        }

        private IEnumerable<GetEnumDto> GetEnumerable(Type? type)
        {
            foreach (var enumValue in type?.GetEnumValues()!)
            {
                var field = type.GetField(enumValue.ToString()!);
                if (field.GetCustomAttribute<IgnoreEnumAttribute>() != null)
                    continue;
                
                var des = field.GetCustomAttribute<DescriptionAttribute>();
                yield return new GetEnumDto(enumValue.ToString()!, enumValue.GetHashCode(), des?.Description);
            }
        }
    }
}

