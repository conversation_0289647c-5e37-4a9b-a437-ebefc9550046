using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_currency", DisableSyncStructure = true)]
	public partial class BasCurrency {

		/// <summary>
		/// 货币主键编码ID
		/// </summary>
		[ Column(Name = "currency_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CurrencyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 货币英文名称
		/// </summary>
		[ Column(Name = "currency_en_us", StringLength = 100)]
		public string CurrencyEnUs { get; set; }

		/// <summary>
		/// 货币日文名称
		/// </summary>
		[ Column(Name = "currency_ja_jp", StringLength = 50)]
		public string CurrencyJaJp { get; set; }

		/// <summary>
		/// 货币中文名称
		/// </summary>
		[ Column(Name = "currency_zh_cn", StringLength = 50)]
		public string CurrencyZhCn { get; set; }

		/// <summary>
		/// 货币ID 
		/// </summary>
		[ Column(Name = "id", StringLength = 50)]
		public string Id { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
