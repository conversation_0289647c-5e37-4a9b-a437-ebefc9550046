﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models.Supplier;
using iPlatformExtension.Public.Applications.Queries.Supplier;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Supplier;

internal sealed class SupplierQueryHandler(IFreeSql<PlatformFreeSql> freeSql) : IRequestHandler<SupplierQuery, IEnumerable<SupplierDto>>
{
    public async Task<IEnumerable<SupplierDto>> Handle(SupplierQuery request, CancellationToken cancellationToken)
    {
        var keyword = request.Keyword;

        return await freeSql.Select<ForeignSupplier>().WithLock()
            .WhereIf(!string.IsNullOrEmpty(keyword),
                supplier => supplier.CnName.Contains(keyword) || supplier.EnName.Contains(keyword))
            .ToListAsync(supplier => new SupplierDto(supplier.SupplierId, supplier.CnName, supplier.EnName),
                cancellationToken);
    }
}