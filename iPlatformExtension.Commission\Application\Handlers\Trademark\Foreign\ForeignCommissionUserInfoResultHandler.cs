﻿using iPlatformExtension.Commission.Application.Notifications.Trademark.Foreign;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class ForeignCommissionUserInfoResultHandler(
    ICompanyRepository companyRepository,
    IUserInfoRepository userInfoRepository,
    IDepartmentInfoRepository departmentInfoRepository,
    IDistrictRepository districtRepository) : INotificationHandler<ForeignCommissionResultNotification>
{
    public async Task Handle(ForeignCommissionResultNotification notification, CancellationToken cancellationToken)
    {
        var commission = notification.ForeignTrademarkBonus;
        ICacheableRepository<string, SysUserInfo> userCacheableRepository = userInfoRepository;
        var userInfo =
            await userCacheableRepository.GetCacheValueAsync(commission.UndertakeUserId, cancellationToken: cancellationToken);

        if (userInfo is null)
        {
            return;
        }
        
        commission.CnName = userInfo.CnName;
        commission.UserName = userInfo.UserName;
        commission.DeptId = userInfo.DeptId;

        var companyInfo = await companyRepository.GetCacheValueAsync(userInfo.ManageCompany ?? string.Empty,
            cancellationToken: cancellationToken);
        
        var departmentInfo = await departmentInfoRepository.GetCacheValueAsync(commission.DeptId, cancellationToken: cancellationToken);

        commission.DeptName = departmentInfo?.FullName ?? string.Empty;
        commission.DistrictCode = companyInfo?.DistrictId ?? string.Empty;
        commission.DistrictName =
            await districtRepository.GetChineseValueAsync(commission.DistrictCode) ?? string.Empty;
    }
}