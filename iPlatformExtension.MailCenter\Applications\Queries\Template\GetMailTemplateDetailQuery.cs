using iPlatformExtension.MailCenter.Applications.Models.Template;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.MailCenter.Applications.Queries.Template
{
    /// <summary>
    /// 获取邮件模板详情查询
    /// </summary>
    public record GetMailTemplateDetailQuery(
        [Required(ErrorMessage = "模板ID不能为空")] string TemplateId
    ) : IRequest<GetMailTemplateDetailDto>;
}
