﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class AddOrUpdateDeliveryBatchItemCommandHandler(IMediator mediator)
    : IRequestHandler<AddOrUpdateDeliveryBatchItemCommand, DeliveryItemOperationResult>
{
    public async Task<DeliveryItemOperationResult> Handle(AddOrUpdateDeliveryBatchItemCommand request, CancellationToken cancellationToken)
    {
        var (procId, version, operatorId, refresh) = request;
        await mediator.Send(new SaveDeliveryCommand(procId, operatorId, version, refresh),
            cancellationToken);
        return new DeliveryItemOperationResult(procId);
    }
}