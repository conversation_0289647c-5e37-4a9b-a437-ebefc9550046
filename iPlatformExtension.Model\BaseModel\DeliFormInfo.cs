using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_form_info", DisableSyncStructure = true)]
	public partial class DeliFormInfo {

		[ Column(Name = "form_name", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FormName { get; set; }

		[ Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ProcId { get; set; }

		[ Column(Name = "file_list", StringLength = 2000)]
		public string FileList { get; set; }

		[ Column(Name = "file_no_oa", StringLength = 50)]
		public string FileNoOa { get; set; }

		[ Column(Name = "file_record", StringLength = 50)]
		public string FileRecord { get; set; }

		[ Column(Name = "notice_name", StringLength = 50)]
		public string NoticeName { get; set; }

		[ Column(Name = "notice_xlh", StringLength = 50)]
		public string NoticeXlh { get; set; }

		[ Column(Name = "oa_content", StringLength = -2)]
		public string OaContent { get; set; }

		[ Column(Name = "receive_date")]
		public DateTime? ReceiveDate { get; set; }

		[ Column(Name = "request_type", StringLength = 50)]
		public string RequestType { get; set; }

		[ Column(Name = "revise_list", StringLength = -2)]
		public string ReviseList { get; set; }

		[ Column(Name = "update_time", StringLength = 50)]
		public string UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
