﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    ///
    /// </summary>
    internal sealed class GetRelateCaseListQueryHandler(
        IFreeSql<PlatformFreeSql> freeSql,
        IFreeSql<MailCenterFreeSql> mailFreeSql,
        IBaseCtrlProcRepository baseCtrlProcRepository
    ) : IRequestHandler<GetRelateCaseListQuery, IEnumerable<GetRelateCaseListDto>>
    {
        public async Task<IEnumerable<GetRelateCaseListDto>> Handle(
            GetRelateCaseListQuery request,
            CancellationToken cancellationToken
        )
        {
            var select = freeSql
                .Select<CaseInfo, CaseApplicantList>()
                .WithLock()
                .LeftJoin(it => it.t1.Id == it.t2.CaseId && it.t2.IsRepresent == true);

            var splitAndFilter = (string? input) => input?.Replace(" ", ";").Split(";");

            var volume = splitAndFilter(request.Volume);
            var appNo = splitAndFilter(request.AppNo);
            var applicantId = splitAndFilter(request.ApplicantId);
            var customerId = splitAndFilter(request.CustomerId);
            var caseNo = splitAndFilter(request.CaseNo);
            var registerNo = splitAndFilter(request.RegisterNo);

            select = select
                .WhereIf(volume is not null, it => volume.Contains(it.t1.Volume))
                .WhereIf(appNo is not null, it => appNo.Contains(it.t1.AppNo))
                .WhereIf(applicantId is not null, it => applicantId.Contains(it.t2.ApplicantId))
                .WhereIf(customerId is not null, it => customerId.Contains(it.t1.CustomerId))
                .WhereIf(
                    request.CaseName is not null,
                    it => it.t1.CaseName.Contains(request.CaseName)
                )
                .WhereIf(caseNo is not null, it => caseNo.Contains(it.t1.CustomerCaseNo))
                .WhereIf(registerNo is not null, it => registerNo.Contains(it.t1.RegisterNo));
            List<string>? mailCorrelatives = new List<string>();
            if (!string.IsNullOrWhiteSpace(request.MailId))
            {
                mailCorrelatives = await mailFreeSql
                    .Select<MailCorrelative>()
                    .Where(it =>
                        it.MailId == request.MailId
                        && it.CorrelateType == SysEnum.CorrelateType.Case.ToString()
                    )
                    .ToListAsync(it => it.ObjId, cancellationToken);
            }
            var listAsync = await select
                .WhereIf(request.ApplyNo is not null, it => it.t1.ApplyNo == request.ApplyNo)
                .WhereIf(
                    request.CountryId is not null,
                    it => request.CountryId.Contains(it.t1.CountryId)
                )
                .OrderByDescending(it => it.t1.CreateTime)
                .WhereIf(mailCorrelatives.Count > 0, it => !mailCorrelatives.Contains(it.t1.Id))
                .Page(request.PageIndex!.Value, request.PageSize!.Value)
                .Count(out var totalCount)
                .ToListAsync(
                    it => new GetRelateCaseListDto(
                        it.t1.Id,
                        it.t1.Volume,
                        it.t1.CaseTypeId,
                        it.t1.CaseDirection,
                        it.t1.CaseName,
                        it.t1.Customer.CustomerName
                    ),
                    cancellationToken
                );
            return new PageResult<GetRelateCaseListDto>()
            {
                Data = listAsync,
                Page = request.PageIndex.Value,
                PageSize = request.PageSize.Value,
                Total = totalCount,
            };
        }
    }
}
