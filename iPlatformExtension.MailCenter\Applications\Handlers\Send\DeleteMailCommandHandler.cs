using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.Send;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Enum;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send;

/// <summary>
/// 删除邮件处理器
/// </summary>
internal sealed class DeleteMailCommandHandler(
    IMailSendRepository mailSendRepository,
    IMailSendFlowRepository mailSendFlowRepository,
    IMailReaderListRepository mailReaderListRepository,
    IFlowRecordRepository flowRecordRepository,
    IMailCorrelativeRepository mailCorrelativeRepository,
    IMailUserRepository mailUserRepository,
    IMailAttachmentsRepository mailAttachmentsRepository,
    IHttpContextAccessor httpContextAccessor) : IRequestHandler<DeleteMailCommand>
{
    public async Task Handle(DeleteMailCommand request, CancellationToken cancellationToken)
    {
        var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
        var currentTime = DateTime.Now;

        // 检查请求是否包含邮件ID
        if (request.MailIds == null || !request.MailIds.Any())
        {
            throw new ApplicationException("请提供要删除的邮件ID");
        }

        // 批量处理每个邮件ID
        foreach (var mailId in request.MailIds)
        {
            // 获取邮件信息
            var mailSend = await mailSendRepository
                .Where(it => it.MailId == mailId)
                .FirstAsync(cancellationToken);

            if (mailSend == null)
            {
                // 记录不存在的邮件ID，但继续处理其他邮件
                continue;
            }

            // 检查邮件是否可以删除
            // 条件1：如果是草稿状态，则可以直接删除
            if (mailSend.Status == SendStatusType.Draft.GetHashCode())
            {
                // 草稿状态可以删除，继续执行
            }
            else
            {
                // 条件2：非草稿状态，检查是否处于非结束流程节点
                var currentFlowRecord = await flowRecordRepository
                    .Where(it => it.MailId == mailId && it.IsCurrent == MailFlowActionStatus.Enable.GetHashCode())
                    .OrderByDescending(it => it.AuditTime)
                    .FirstAsync(cancellationToken);

                // 如果没有流程记录或已经是结束节点，则不允许删除
                if (currentFlowRecord == null || currentFlowRecord.CurNodeId == ((int)MailFlowAction.End).ToString())
                {
                    // 跳过不符合条件的邮件，继续处理其他邮件
                    continue;
                }
            }

            // 删除邮件流程记录
            await flowRecordRepository.DeleteAsync(it => it.MailId == mailId, cancellationToken);

            // 删除邮件发送流程记录
            await mailSendFlowRepository.DeleteAsync(it => it.MailId == mailId, cancellationToken);

            // 删除邮件阅读记录
            await mailReaderListRepository.DeleteAsync(it => it.MailId == mailId, cancellationToken);

            // 删除邮件关联数据
            await mailCorrelativeRepository.DeleteAsync(it => it.MailId == mailId, cancellationToken);

            // 删除邮件用户记录
            await mailUserRepository.DeleteAsync(it => it.MailId == mailId, cancellationToken);

            // 删除邮件附件记录
            await mailAttachmentsRepository.DeleteAsync(it => it.MailId == mailId, cancellationToken);

            // 删除邮件主体
            await mailSendRepository.DeleteAsync(mailSend, cancellationToken);
        }
    }
}
