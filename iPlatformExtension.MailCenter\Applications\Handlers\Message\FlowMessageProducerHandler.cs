﻿using Confluent.Kafka;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.MailCenter.Infrastructure.MQ;
using iPlatformExtension.Model.Dto.MailCenter;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using Microsoft.AspNetCore.Http;
using MimeKit;
using Nacos.V2.Utils;
using static iPlatformExtension.Model.Enum.SysEnum;
using iPlatformExtension.Common.MQ.MailMQ;
using Microsoft.AspNetCore.Components.Forms;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Message
{
    public class FlowMessageProducerHandler(
        KafkaProducerService<Null, MailCenterMessageContent> producer,
        IHttpContextAccessor httpContextAccessor,
        IFreeSql<MailCenterFreeSql> sql,
        IMediator mediator
        ) : IRequestHandler<MailCenterFlowMessageQuery>
    {
        public async Task Handle(MailCenterFlowMessageQuery request, CancellationToken cancellationToken)
        {
            var hashCode = request.OperationType.GetHashCode();
            if (hashCode <= OperationTypeEnum.Transfer.GetHashCode())
            {
                await mediator.Send(new ReceiveFlowMessageQuery(request.flowRecords, request.OperationType));
            }
            else
            {
                await mediator.Send(new SentFlowMessageQuery(request.flowRecords, request.OperationType));
            }
        }
    }
}
