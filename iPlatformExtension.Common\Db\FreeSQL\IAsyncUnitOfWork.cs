﻿using System.Data.Common;
using FreeSql;

namespace iPlatformExtension.Common.Db.FreeSQL;

public interface IAsyncUnitOfWork : IUnitOfWork, IAsyncDisposable
{
    ValueTask<DbTransaction> GetOrBeginTransactionAsync(bool isCreate = true, CancellationToken cancellationToken = default);

    ValueTask CommitAsync(CancellationToken cancellationToken = default);

    ValueTask RollbackAsync(CancellationToken cancellationToken = default);
}