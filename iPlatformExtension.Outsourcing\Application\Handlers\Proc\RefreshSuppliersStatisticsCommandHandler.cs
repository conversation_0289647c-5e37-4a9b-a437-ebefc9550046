﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using MediatR;
using StackExchange.Redis;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class RefreshSuppliersStatisticsCommandHandler(
    IFreeSql<PlatformFreeSql> freeSql, 
    ISender sender,
    DefaultRedisCache redisCache) 
    : IRequestHandler<RefreshSuppliersStatisticsCommand>
{
    public async Task Handle(RefreshSuppliersStatisticsCommand request, CancellationToken cancellationToken)
    {
        var supplierIds = await freeSql.Select<ForeignSupplier>().WithLock().ToListAsync(supplier =>
            new KeyValuePair<long, string>(supplier.Id, supplier.SupplierId), cancellationToken);
        var database = await redisCache.GetDatabaseAsync(0);
        
        var tasks = new List<Task>(supplierIds.Count);
        foreach (var (id, supplierId) in supplierIds)
        {
            tasks.Add(Task.Run(async () =>
            {
                var counts = await sender.Send(new AnnualCountSupplierCommand(supplierId), cancellationToken);
                await database.HashSetAsync(GetSupplierYearCountKey(id),
                    counts.Select(count => new HashEntry(count.Key, count.Value)).ToArray());
            }, cancellationToken));
        }
        
        await Task.WhenAll(tasks);
    }

    private static string GetSupplierYearCountKey(long id) => $"supplier:year_count:{id}";
}