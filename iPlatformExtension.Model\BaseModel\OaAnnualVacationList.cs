using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "oa_annual_vacation_list", DisableSyncStructure = true)]
	public partial class OaAnnualVacationList {

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 当年度在公司天数
		/// </summary>
		[ Column(Name = "cur_service_time")]
		public int? CurServiceTime { get; set; }

		/// <summary>
		/// 当年度年份
		/// </summary>
		[ Column(Name = "cur_year", StringLength = 50)]
		public string CurYear { get; set; }

		/// <summary>
		/// 可休假开始时间
		/// </summary>
		[ Column(Name = "effective_start_time")]
		public DateTime? EffectiveStartTime { get; set; }

		/// <summary>
		/// 未休假天数
		/// </summary>
		[ Column(Name = "effective_vacation_time")]
		public double? EffectiveVacationTime { get; set; }

		/// <summary>
		/// 入职时间
		/// </summary>
		[ Column(Name = "entry_time")]
		public DateTime? EntryTime { get; set; }

		/// <summary>
		/// id
		/// </summary>
		[ Column(Name = "id", StringLength = 50)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// 用户id
		/// </summary>
		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

		/// <summary>
		/// 可休假天数
		/// </summary>
		[ Column(Name = "vacation_times")]
		public int? VacationTimes { get; set; }

		/// <summary>
		/// 工龄
		/// </summary>
		[ Column(Name = "working_age")]
		public int? WorkingAge { get; set; }

	}

}
