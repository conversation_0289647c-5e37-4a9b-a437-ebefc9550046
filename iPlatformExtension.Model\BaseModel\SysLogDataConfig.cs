using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_log_data_config", DisableSyncStructure = true)]
	public partial class SysLogDataConfig {

		[ Column(Name = "data_code", StringLength = 50, IsNullable = false)]
		public string DataCode { get; set; }

		[ Column(Name = "sql", StringLength = 4000)]
		public string Sql { get; set; }

	}

}
