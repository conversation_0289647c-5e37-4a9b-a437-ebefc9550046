﻿using System.Collections.Concurrent;
using System.Linq.Expressions;
using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class BuildSortedFeesSelectExpressionCommandHandler : IRequestHandler<FeesSelectExpressionCommand, Expression<Func<CaseFeeList, SortedFees>>>
{
    private static readonly ConcurrentDictionary<string, Expression<Func<CaseFeeList, SortedFees>>> selectExpressions =
        new();
    
    public Task<Expression<Func<CaseFeeList, SortedFees>>> Handle(FeesSelectExpressionCommand request, CancellationToken cancellationToken)
    {
        var expression = selectExpressions.GetOrAdd(request.SortCondition, sortCondition =>
        {
            var sortPropertyNames = sortCondition.Split('.');

            var parameterExpression = Expression.Parameter(typeof(CaseFeeList), "caseFeeList");
            var memberBindings = new List<MemberBinding>(2);

            if (!string.IsNullOrWhiteSpace(request.SortCondition) && request.SortCondition != nameof(CaseFeeList.FeeId))
            {
                var sortPropertyExpression =
                    sortPropertyNames.Aggregate<string, Expression>(parameterExpression, Expression.Property);
                memberBindings.Add(Expression.Bind(typeof(SortedFees).GetProperty(nameof(SortedFees.SortedField))!,
                    sortPropertyExpression));
            }

            var feeIdExpression = Expression.Property(parameterExpression, nameof(CaseFeeList.FeeId));
            memberBindings.Add(Expression.Bind(typeof(SortedFees).GetProperty(nameof(SortedFees.FeeId))!,
                feeIdExpression));

            var memberInitExpression = Expression.MemberInit(Expression.New(typeof(SortedFees)), memberBindings);
            return Expression.Lambda<Func<CaseFeeList, SortedFees>>(memberInitExpression, parameterExpression);
        });

        return Task.FromResult(expression);
    }
}