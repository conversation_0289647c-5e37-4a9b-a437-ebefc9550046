﻿using iPlatformExtension.Model.Constants;

namespace iPlatformExtension.Model.Dto
{
    public class FeeConfigDto
    {

        /// <summary>
        /// 获取标识
        /// 开案则返回  case_id
        /// 案件管理返回  proc_id
        /// </summary>
        public string GetKeyID
        {
            get
            {
                return period == SYS_PERIOD.APPLY ? case_id : proc_id;
            }
        }

        /// <summary>
        /// 阶段
        /// </summary>
        public string period { get; set; }
        //任务管制阶段：newly/start/end/trigger
        public string stage { get; set; }

        //是否处理费用sql
        private bool _add_fee = true;
        public bool add_fee
        {
            get
            {
                return _add_fee;
            }
            set
            {
                _add_fee = value;
            }
        }
        //任务事项
        public string ctrl_proc_id { get; set; }
        public string ctrl_proc_property { get; set; }

        public string translate_type { get; set; }
        public string translate_amount { get; set; }


        //任务id
        public string proc_id { get; set; }

        //承办人
        public string undertake_user_id { get; set; }

        public DateTime? finish_date { get; set; }
        public DateTime? int_first_date { get; set; }
        public DateTime? cus_first_date { get; set; }
        public DateTime? int_finish_date { get; set; }
        public DateTime? cus_finish_date { get; set; }
        //public DateTime? legal_due_date { get; set; }
        public DateTime? notice_date { get; set; }
        public DateTime? postmark_date { get; set; }

        //参数前缀
        public string pre_name { get; set; }
        //更新人
        public string update_user_id { get; set; }

        /// <summary>
        /// 任务创建时间
        /// </summary>
        public DateTime? proc_create_date { get; set; }

        public string proc_status_id { get; set; }
        //规则相关官文id
        public string notice_id { get; set; }
        public string notice_code { get; set; }
        public string ctrl_proc_code { get; set; }

        public string country_id { get; set; }

        public string case_type_id { get; set; }

        public string apply_type_id { get; set; }

        public string apply_type_code { get; set; }

        public string case_direction { get; set; }

        public string customer_id { get; set; }

        public string case_id { get; set; }

        public string volume { get; set; }

        public string undertake_dept_id { get; set; }
        public string undertake_main_user_id { get; set; }

        public DateTime? app_date { get; set; }

        public DateTime? entrust_date { get; set; }
        public DateTime? legal_due_date { get; set; }

        /// <summary>
        /// 权力要求项总数
        /// </summary>
        public int claims_totals { get; set; }

        /// <summary>
        /// 说明书页总数
        /// </summary>
        public int figure_totals { get; set; }

        /// <summary>
        /// 优先权要求项
        /// </summary>
        public int priority_totals { get; set; }

        /// <summary>
        /// PCT国际申请日期
        /// </summary>
        public DateTime? pct_app_date { get; set; }

        /// <summary>
        /// 最早优先权日期
        /// </summary>
        public DateTime? first_priority_date { get; set; }

        /// <summary>
        /// 加急案类型
        /// </summary>
        public string case_emergent { get; set; }

        /// <summary>
        /// 案件所属区域
        /// </summary>
        public string belong_district { get; set; }


        /// <summary>
        /// 费减比例
        /// </summary>
        public int fee_reduce { get; set; }


        private string _reduce_type = "fee_base";

        /// <summary>
        /// 申请人费减比例类型
        /// </summary>
        public string reduce_type
        {
            get
            {
                if (this.fee_reduce == 85 || this.fee_reduce == 80)
                {
                    _reduce_type = "fee_micro";
                }
                else if (this.fee_reduce == 70 || this.fee_reduce == 60)
                {
                    _reduce_type = "fee_small";
                }
                else
                {
                    _reduce_type = "fee_base";
                }
                return _reduce_type;
            }
            set { _reduce_type = value; }
        }

        /// <summary>
        /// 公告日期
        /// </summary>
        public DateTime? issue_date { get; set; }

        /// <summary>
        /// 缴第几年年费
        /// </summary>
        public string annual_year { get; set; }


        /// <summary>
        /// 首缴年度
        /// </summary>
        public string first_pay_annual { get; set; }


        /// <summary>
        /// 是否PCT进入
        /// </summary>
        public bool pct_enter { get; set; }

        /// <summary>
        /// 无效程序标识
        /// </summary>
        public bool is_invalid_program { get; set; }

        /// <summary>
        /// 请求宽限（32个月）
        /// </summary>
        public bool is_grace { get; set; }

        /// <summary>
        /// 是否同时提实审
        /// </summary>
        public bool is_essence_exam { get; set; }

        /// <summary>
        /// 优先审查
        /// </summary>
        public bool is_priority_review { get; set; }

        /// <summary>
        /// 提前公布
        /// </summary>
        public bool is_ahead_pub { get; set; }
        /// <summary>
        /// 请求保密审查
        /// </summary>
        public bool is_secrecy_request { get; set; }
        /// <summary>
        /// 1+1套案申请
        /// </summary>
        public bool is_same_day { get; set; }
        /// <summary>
        /// PPH申请
        /// </summary>
        public bool is_pph { get; set; }
        /// <summary>
        /// CA申请
        /// </summary>
        public bool is_ca { get; set; }
        /// <summary>
        /// CIP申请
        /// </summary>
        public bool is_cip { get; set; }
        /// <summary>
        /// 主动修改
        /// </summary>
        public bool is_active { get; set; }
        /// <summary>
        /// 预审
        /// </summary>
        public bool is_advance_check { get; set; }

        /// <summary>
        /// 分案属性
        /// </summary>
        public string division_property { get; set; }

        /// <summary>
        /// 案件要求
        /// </summary>
        public string case_sub_property { get; set; }

        /// <summary>
        /// 案源分所
        /// </summary>
        public string belong_company { get; set; }

        /// <summary>
        /// 管理分所
        /// </summary>
        public string manage_company { get; set; }

        /// <summary>
        /// 所有申请人，用分号(;)隔开
        /// </summary>
        public string all_applicant { get; set; }

        /// <summary>
        /// 家族案id
        /// </summary>
        public string family_id { get; set; }
        /// <summary>
        /// 1+1套案id
        /// </summary>
        public string same_day_id { get; set; }
        /// <summary>
        /// 同日递交id
        /// </summary>
        public string same_submit_id { get; set; }
        /// <summary>
        /// 创建人ID
        /// </summary>
        public string create_user_id { get; set; }
    }
}
