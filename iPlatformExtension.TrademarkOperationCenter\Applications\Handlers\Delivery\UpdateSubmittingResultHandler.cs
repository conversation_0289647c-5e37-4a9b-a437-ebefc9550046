﻿using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Repository.Implement;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class UpdateSubmittingResultHandler(
    ISender sender, DeliveryInfoRepository deliveryInfoRepository) : IRequestHandler<UpdateSubmittingResultCommand, bool>
{
    public async Task<bool> Handle(UpdateSubmittingResultCommand request, CancellationToken cancellationToken)
    {
        var deliveryInfo = request.DeliveryInfo;
        var result = request.Result;
        var operatorId = request.OperatorId;
        
        deliveryInfo.IsAuto = true;
        deliveryInfo.Status = (int?) DeliveryStatus.Delivering;
        deliveryInfo.OperationResult = true;
        deliveryInfo.UpdateTime = DateTime.Now;
        deliveryInfo.UpdateUserId = operatorId;

        await deliveryInfoRepository.UpdateAsync(deliveryInfo, cancellationToken);
        
        await sender.Send(new InsertHistoryCommand(
            deliveryInfo.ProcId,
            result.GetOperationDescriptionWithResult(),
            operatorId,
            result.Message), cancellationToken);

        return false;
    }
}