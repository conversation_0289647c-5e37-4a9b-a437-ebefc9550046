using iPlatformExtension.Finance.Applications.Commands.Commission;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Commission;

internal sealed class InsertIncomesCommandHandler : IRequestHandler<InsertIncomesCommand>
{
    private readonly IMPaymentDetailRepository _paymentDetailRepository;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="paymentDetailRepository">收入明细仓储</param>
    public InsertIncomesCommandHandler(IMPaymentDetailRepository paymentDetailRepository)
    {
        _paymentDetailRepository = paymentDetailRepository;
    }

    /// <summary>
    /// 收入明细插入逻辑
    /// </summary>
    /// <param name="request">收入明细数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    public Task Handle(InsertIncomesCommand request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request.IncomeDetails);
        return _paymentDetailRepository.InsertAsync(request.IncomeDetails, cancellationToken);
    }
}