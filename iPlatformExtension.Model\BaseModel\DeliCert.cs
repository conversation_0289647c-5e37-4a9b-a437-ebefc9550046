using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_cert", DisableSyncStructure = true)]
	public partial class DeliCert {

		[ Column(Name = "agency_code", StringLength = 50)]
		public string AgencyCode { get; set; }

		[ Column(Name = "cert_code", StringLength = 50)]
		public string CertCode { get; set; }

		[ Column(Name = "cert_pass", StringLength = 50)]
		public string CertPass { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
