﻿using System.Linq;
using System.Reflection;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Statistics;
using iPlatformExtension.Public.Applications.Queries.Statistics;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;
using MiniExcelLibs.Attributes;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.Public.Applications.Handlers.Statistics
{
    /// <summary>
    /// 过期提醒
    /// </summary>
    internal sealed class OverDateWarningQueryHandler(
        IFreeSql freeSql,
        IBaseCtrlProcRepository iBaseCtrlProcRepository,
        IUserInfoRepository iUserInfoRepository,
        IBaseCaseStatusRepository iBaseCaseStatusRepository,
        IBaseSubProcStatusRepository iBaseSubProcStatusRepository,
        ISystemDictionaryRepository dictionaryRepository,
        ICompanyRepository iCompanyRepository,
        IDepartmentInfoRepository departmentInfoRepository,
        IBasCaseProcStatusRepository basCaseProcStatusRepository,
        IApplyTypeRepository applyTypeRepository
    ) : IRequestHandler<OverDateWarningQuery, IEnumerable<OverDateWarningDto>>
    {
        public async Task<IEnumerable<OverDateWarningDto>> Handle(
            OverDateWarningQuery request,
            CancellationToken cancellationToken
        )
        {
            ICacheableRepository<string, UserBaseInfo> userInfoRepository = iUserInfoRepository;

            var caseProcInfoSelect = freeSql
                .Select<CaseProcInfo>()
                .Where(it =>
                        (it.CaseInfo.CaseTypeId == "P" && it.CaseInfo.CaseDirection == "II")
                        || (
                            it.CaseInfo.CaseDirection == "IO"
                            && it.CaseInfo.CaseTypeId == "P"
                            && it.CaseInfo.ApplyTypeId == "8B9326B4-C566-4044-B017-CFAE68236F17"
                        )
                )
                .Where(it=> it.FinishDate == null && it.BasCtrlProc.IsDeadlineReminder == true)
                .Where(it => !string.IsNullOrWhiteSpace(it.UndertakeUserId))
                .Where(it =>
                    it.ProcStatusId != CASE_PROC_STATUS.WC
                    && it.ProcStatusId != CASE_PROC_STATUS.BC
                    && it.ProcStatusId != CASE_PROC_STATUS.BCL
                    && it.ProcStatusId != CASE_PROC_STATUS.YCL
                )
                .WhereIf(
                    request.UserId is not null,
                    it => request.UserId.Contains(it.UndertakeUserId)
                )
                .WhereIf(
                    request.DeptList is not null,
                    it => request.DeptList.Contains(it.UndertakeUserInfo.DeptId)
                );
            var date = DateTime.Now;
            switch (request.ReportType)
            {
                case ReportType.IntFirstDate:
                    caseProcInfoSelect
                        .Where(it =>
                            it.IntFirstDate.Value < date.AddDays(request.Day).Date
                            && it.FirstDocDate == null
                            && it.CusFirstDate == null
                        )
                        .WhereIf(
                            request.EndDateTime is not null,
                            it => it.IntFirstDate >= request.EndDateTime
                        );
                    break;
                case ReportType.CusFirstDate:
                    caseProcInfoSelect
                        .Where(it =>
                            it.CusFirstDate.Value < date.AddDays(request.Day).Date
                            && it.FirstDocDate == null
                        )
                        .WhereIf(
                            request.EndDateTime is not null,
                            it => it.CusFirstDate >= request.EndDateTime
                        );
                    break;
                case ReportType.IntFinishDate:
                    caseProcInfoSelect
                        .Where(it =>
                            it.IntFinishDate.Value < date.AddDays(request.Day).Date
                            && it.FinishDocDate == null
                            && it.CusFinishDate == null
                        )
                        .WhereIf(
                            request.EndDateTime is not null,
                            it => it.IntFinishDate >= request.EndDateTime
                        );
                    break;
                case ReportType.CusFinishDate:
                    caseProcInfoSelect
                        .Where(it =>
                            it.CusFinishDate.Value < date.AddDays(request.Day).Date
                            && it.FinishDocDate == null
                        )
                        .WhereIf(
                            request.EndDateTime is not null,
                            it => it.CusFinishDate >= request.EndDateTime
                        );
                    break;
                case ReportType.LegalDueDate:
                    caseProcInfoSelect
                        .Where(it => it.LegalDueDate.Value < date.AddDays(request.Day).Date)
                        .WhereIf(
                            request.EndDateTime is not null,
                            it => it.LegalDueDate >= request.EndDateTime
                        );
                    break;
            }

            var caseProcInfos = await caseProcInfoSelect
                .WithLock()
                .ToListAsync(
                    it => new OverDateWarningDto(
                        it.CaseInfo.IsAdvanceCheck,
                        it.CaseInfo.IsPriorityReview,
                        it.CaseInfo.Volume,
                        it.CaseInfo.Customer.CustomerName,
                        it.CtrlProcId,
                        it.CtrlProcProperty,
                        it.ProcStatusId,
                        it.ProcNote,
                        it.UndertakeUserId,
                        it.SubProcStatusId,
                        it.TitularWriteUser,
                        it.UndertakeUserInfo.DeptId,
                        it.IntFirstDate,
                        it.CusFirstDate,
                        it.IntFinishDate,
                        it.CusFinishDate,
                        it.LegalDueDate,
                        it.CaseInfo.ApplyTypeId,
                        it.CaseInfo.BelongCompany,
                        it.CaseInfo.ManageCompany
                    ),
                    cancellationToken
                );

            return await caseProcInfos
                .ToAsyncEnumerable()
                .SelectAwait(async procInfo =>
                {
                    if (!string.IsNullOrWhiteSpace(procInfo.CtrlProcId))
                    {
                        var baseCtrlProcInfo = await iBaseCtrlProcRepository.GetCacheValueAsync(
                            procInfo.CtrlProcId, cancellationToken: cancellationToken);
                        procInfo.CtrlProcZhCn = baseCtrlProcInfo?.CtrlProcZhCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.UndertakeUserId))
                    {
                        var userInfo = await userInfoRepository.GetCacheValueAsync(
                            procInfo.UndertakeUserId, cancellationToken: cancellationToken);
                        procInfo.UndertakeUserName = userInfo?.CnName;
                        procInfo.UserAccount = userInfo?.UserName;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.TitularWriteUser))
                    {
                        var userInfo = await userInfoRepository.GetCacheValueAsync(
                            procInfo.TitularWriteUser, cancellationToken: cancellationToken);
                        procInfo.TitularWriteUserName = userInfo?.CnName;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.CtrlProcProperty))
                    {
                        var baseBasCaseStatus = await iBaseCaseStatusRepository.GetCacheValueAsync(
                            procInfo.CtrlProcProperty, cancellationToken: cancellationToken);
                        procInfo.CtrlProcPropertyValue = baseBasCaseStatus?.CaseStatusZhCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.SubProcStatusId))
                    {
                        var baseSubProcStatus =
                            await iBaseSubProcStatusRepository.GetCacheValueAsync(
                                procInfo.SubProcStatusId, cancellationToken: cancellationToken);
                        procInfo.SubProcStatusValue = baseSubProcStatus?.TextZhCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.ProcStatusId))
                    {
                        var chineseKeyValueAsync =
                            await basCaseProcStatusRepository.GetCacheValueAsync(
                                procInfo.ProcStatusId, cancellationToken: cancellationToken);
                        procInfo.ProcStatusName = chineseKeyValueAsync.TextZhCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.BelongCompany))
                    {
                        var chineseKeyValueAsync = await iCompanyRepository.GetCacheValueAsync(
                            procInfo.BelongCompany, cancellationToken: cancellationToken);
                        procInfo.BelongCompanyValue = chineseKeyValueAsync?.CompanyNameCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.ManageCompany))
                    {
                        var chineseKeyValueAsync = await iCompanyRepository.GetCacheValueAsync(
                            procInfo.ManageCompany, cancellationToken: cancellationToken);
                        procInfo.ManageCompanyValue = chineseKeyValueAsync?.CompanyNameCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.DeptId))
                    {
                        var chineseKeyValueAsync =
                            await departmentInfoRepository.GetCacheValueAsync(procInfo.DeptId, cancellationToken: cancellationToken);
                        procInfo.DeptName = chineseKeyValueAsync?.CnName;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.ApplyTypeId))
                    {
                        var chineseKeyValueAsync = await applyTypeRepository.GetCacheValueAsync(
                            procInfo.ApplyTypeId, cancellationToken: cancellationToken);
                        procInfo.ApplyTypeValue = chineseKeyValueAsync?.ApplyTypeZhCn;
                    }

                    return procInfo;
                })
                .ToListAsync(cancellationToken);
        }
    }
}
