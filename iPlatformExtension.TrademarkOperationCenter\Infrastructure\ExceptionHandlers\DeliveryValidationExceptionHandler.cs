﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.ExceptionHandlers;

internal sealed class DeliveryValidationExceptionHandler<TRequest, TResult> 
    : IPipelineBehavior<TRequest, TResult> where TRequest : IRequest<BatchDeliveryValidateResult> where TResult : BatchDeliveryValidateResult
{
    public Task<TResult> Handle(TRequest request, RequestHandlerDelegate<TResult> next, CancellationToken cancellationToken)
    {
        try
        {
            return next();
        }
        catch (Exception e)
        {
            if (e.Data.Contains(nameof(BatchDeliveryValidateResult.ProcNos)) && e.Data[nameof(BatchDeliveryValidateResult.ProcNos)] is IEnumerable<string> procNos)
            {
                return Task.FromResult((TResult) procNos.Aggregate(new BatchDeliveryValidateResult(procNos.Count()),
                    (result, procNo) => result.Fail(e.Message, procNo)));
            }
            
            return Task.FromResult((TResult) new BatchDeliveryValidateResult(1).Fail(e.Message));
        }
    }
}