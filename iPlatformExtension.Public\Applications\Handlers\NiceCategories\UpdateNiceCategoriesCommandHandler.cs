﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Commands.NiceCategories;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.NiceCategories;

internal sealed class UpdateNiceCategoriesCommandHandler(IMediator mediator, IFreeSql freeSql)
    : IRequestHandler<UpdateNiceCategoriesCommand>
{
    public async Task Handle(UpdateNiceCategoriesCommand request, CancellationToken cancellationToken)
    {

        await freeSql.Delete<BasTrademarkItemsLevel>().Where(level => level.VersionId == request.VersionId)
            .ExecuteAffrowsAsync(cancellationToken);
        await freeSql.Delete<BasTrademarkItems>().Where(items => items.VersionId == request.VersionId)
            .ExecuteAffrowsAsync(cancellationToken);

        await mediator.Send(
            new InsertNewCategoriesCommand(request.VersionId, request.NiceCategoryInfos, request.OperatorId),
            cancellationToken);
    }
}