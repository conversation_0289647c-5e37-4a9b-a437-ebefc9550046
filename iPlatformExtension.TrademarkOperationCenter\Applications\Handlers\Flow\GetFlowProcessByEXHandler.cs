﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;
using System.Linq.Expressions;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    /// <summary>
    /// 核稿待办
    /// </summary>
    /// <param name="freeSql"></param>
    public class GetFlowProcessByEXHandler(IFreeSql freeSql) : IRequestHandler<FlowProcessByEXQuery, PageResult<GetFlowProcessDto>>
    {
        public async Task<PageResult<GetFlowProcessDto>> Handle(FlowProcessByEXQuery request, CancellationToken cancellationToken)
        {
            var totalCount = 0L;
            var list = new List<GetFlowProcessDto>();
            var sql = freeSql.Select<SysFlowActivity>().WithLock();

            if (!string.IsNullOrWhiteSpace(request.SearchKey))
            {
                request.whereTodo = request.whereTodo.And((fa) => fa.CaseProcFlow.CaseProcInfo.CaseInfo.CaseName.Contains(request.SearchKey) ||
                    fa.CaseProcFlow.CaseProcInfo.CaseInfo.Volume.Contains(request.SearchKey) || fa.CaseProcFlow.CaseProcInfo.CaseInfo.Customer.CustomerName.Contains(request.SearchKey) || fa.CurFlowNode.NameZhCn.Contains(request.SearchKey)
                   || fa.CaseProcFlow.CaseProcInfo.CaseInfo.Applicants.Any(app => app.CusApplicant.ApplicantNameCn.Contains(request.SearchKey)) || fa.CaseProcFlow.CaseProcInfo.CaseInfo.AppNo.Contains(request.SearchKey) || fa.CaseProcFlow.CaseProcInfo.CaseInfo.RegisterNo.Contains(request.SearchKey)
                   || fa.CaseProcFlow.CaseProcInfo.BasCtrlProc.CtrlProcZhCn.Contains(request.SearchKey)
                   );
            }


            list = await sql.Where(request.whereCommon).Where(request.whereTodo)
                   .Count(out totalCount)
                   .ToListAsync(fa => new GetFlowProcessDto()
                   {
                       procNo = fa.CaseProcFlow.CaseProcInfo.ProcNo,
                       caseName = fa.CaseProcFlow.CaseProcInfo.CaseInfo.CaseName,
                       UndertakeUserName = fa.CaseProcFlow.CaseProcInfo.UndertakeUserInfo.CnName,
                       ctrlProcName = fa.CaseProcFlow.CaseProcInfo.BasCtrlProc.CtrlProcZhCn,
                       appNo = fa.CaseProcFlow.CaseProcInfo.CaseInfo.AppNo,
                       TrademarkClass = fa.CaseProcFlow.CaseProcInfo.CaseInfo.TrademarkClass,
                       LegalDueDate = fa.CaseProcFlow.CaseProcInfo.LegalDueDate,
                       Status = fa.Status,
                       //OperationResult = fa.CaseProcFlow.CaseProcInfo.DeliInfo.OperationResult,
                       CurNode = fa.CurFlowNode.NameZhCn,
                       CurNodeId = fa.CurNodeId,
                       PreNode = fa.PreFlowNode.NameZhCn,
                       SubmitType = freeSql.Select<SysDictionary>().Where(d => d.Value == fa.PrevAuditTypeId && d.DictionaryName == "audit_type").ToOne(o => o.TextZhCn),
                       UpdateTime = fa.UpdateTime,
                       CurHandler = fa.CurNodeUserInfo.CnName,
                       ObjectId = fa.ObjId,
                       //IsAuto = fa.CaseProcFlow.CaseProcInfo.DeliInfo.IsAuto,
                       //IsStandardNice = (!fa.CaseProcFlow.CaseProcInfo.DeliInfo.IsStandardNice.HasValue || fa.CaseProcFlow.CaseProcInfo.DeliInfo.IsStandardNice == true) ? false : true,
                       //IsCheck = fa.CaseProcFlow.CaseProcInfo.DeliInfo.Status == (int)DeliveryStatus.Confirmed ? true : false,
                       Country = fa.CaseProcFlow.CaseProcInfo.CaseInfo.Country.CountryZhCn,
                       ApplyTypeId = fa.CaseProcFlow.CaseProcInfo.CaseInfo.ApplyType.ApplyTypeZhCn,
                       RequirementSubmitDate = fa.CaseProcFlow.CaseProcInfo.RequirementSubmitDate,
                       SubProcStatus = fa.CaseProcFlow.CaseProcInfo.SubProcStatus.TextZhCn,
                       AgencyName = fa.CaseProcFlow.CaseProcInfo.Agency.AgencyNameCn,
                       TeamId = fa.CaseProcFlow.CaseProcInfo.TeamId,
                       Version = fa.Version,
                       ReturnDocDate = fa.CaseProcFlow.CaseProcInfo.ReturnDocDate,
                       ApplicantName = !string.IsNullOrEmpty(fa.CaseProcFlow.CaseProcInfo.CaseInfo.ApplicantId) ? freeSql.Select<CaseApplicantList>().Where(d => d.ApplicantId == fa.CaseProcFlow.CaseProcInfo.CaseInfo.ApplicantId && d.IsRepresent == true).ToOne(o => o.CusApplicant.ApplicantNameCn) : freeSql.Select<CaseApplicantList>().Where(d => d.CaseId == fa.CaseProcFlow.CaseProcInfo.CaseInfo.Id).OrderBy(o => o.Seq).ToOne(o => o.CusApplicant.ApplicantNameCn),
                       Volume = fa.CaseProcFlow.CaseProcInfo.CaseInfo.Volume,
                       RemainingDays = (fa.CaseProcFlow.CaseProcInfo.BasCtrlProc.TrademarkTab == "0" || fa.CaseProcFlow.CaseProcInfo.BasCtrlProc.TrademarkTab == "3") ? (!fa.CaseProcFlow.CaseProcInfo.ReturnDocDate.HasValue ? null : (fa.CaseProcFlow.CaseProcInfo.ReturnDocDate.Value - DateTime.Now.Date).Days + 1) :
                       (fa.CaseProcFlow.CaseProcInfo.CtrlProcId == "DD886CBE-D8D4-4BE0-97F7-FD521674269A" ? (!fa.CaseProcFlow.CaseProcInfo.RequirementSubmitDate.HasValue ? null : (fa.CaseProcFlow.CaseProcInfo.RequirementSubmitDate.Value - DateTime.Now.Date).Days + 1) :
                       (!fa.CaseProcFlow.CaseProcInfo.LegalDueDate.HasValue ? null : (fa.CaseProcFlow.CaseProcInfo.LegalDueDate.Value - DateTime.Now.Date).Days + 1)
                       ),
                       CustomerName = fa.CaseProcFlow.CaseProcInfo.CaseInfo.Customer.CustomerName,
                       CtrlProcMark = freeSql.Select<SysDictionary>().Where(o => o.DictionaryName == "ctrl_proc_mark" && o.Value == fa.CaseProcFlow.CaseProcInfo.CtrlProcMark).ToOne(o => o.TextZhCn),
                       ProcID = fa.CaseProcFlow.ProcId,
                       ProcFlowId = fa.CaseProcFlow.ProcFlowId,
                       ImportantTrademark = fa.CaseProcFlow.CaseProcInfo.CaseInfo.ImportantTrademark,
                       Prioritization = fa.CaseProcFlow.CaseProcInfo.CaseInfo.Prioritization,
                       HighCosts = fa.CaseProcFlow.CaseProcInfo.CaseInfo.HighCosts,
                       RegisterNo = fa.CaseProcFlow.CaseProcInfo.CaseInfo.RegisterNo
                   }, cancellationToken);
            var res = list.CustomSort<GetFlowProcessDto>(request.Sort, request.IsAscending)
          .Skip(request.PageSize * (request.PageIndex - 1)).Take(request.PageSize).ToList();

            return new PageResult<GetFlowProcessDto>()
            {
                Data = res,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };
        }
    }
}
