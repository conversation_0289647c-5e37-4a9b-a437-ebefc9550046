﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 获取关联邮件列表
    /// </summary>
    internal sealed class GetRelateMailQueryHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IUserInfoRepository userInfoRepository
    ) : IRequestHandler<GetRelateMailQuery, IEnumerable<GetRelateMailDto>>
    {
        public async Task<IEnumerable<GetRelateMailDto>> Handle(
            GetRelateMailQuery request,
            CancellationToken cancellationToken
        )
        {
            var receiveTemp = freeSql
                .Select<MailCorrelative, MailReceive, MailReceive>()
                .WithLock()
                .LeftJoin(it => it.t1.ObjId == it.t2.MailId)
                .LeftJoin(it => it.t1.MailId == it.t3.MailId)
                .Where(it =>
                    (it.t1.MailId == request.MailId || it.t1.ObjId == request.MailId)
                    && it.t1.CorrelateType == SysEnum.CorrelateType.ReceiveMail.ToString()
                )
                .WithTempQuery(it => new GetRelateMailDto(
                    it.t1.Id,
                    it.t1.MailId,
                    "'收件'",
                    it.t1.CreateTime,
                    it.t1.MailId == request.MailId ? it.t2.MailNo : it.t3.MailNo,
                    it.t1.MailId == request.MailId ? it.t2.MailFrom : it.t3.MailFrom,
                    it.t1.MailId == request.MailId ? it.t2.MailSubject : it.t3.MailSubject,
                    it.t1.CreateBy,
                    it.t1.MailId == request.MailId ? it.t2.MailDate : it.t3.MailDate,
                    it.t1.MailId == request.MailId ? it.t2.Status : it.t3.Status,
                    it.t1.MailId == request.MailId ? it.t1.MailId : it.t1.ObjId
                ));

            var sendTemp = freeSql
                .Select<MailCorrelative, MailSend, MailSendFlow>()
                .WithLock()
                .InnerJoin(it => it.t1.ObjId == it.t2.MailId)
                .InnerJoin(it => it.t2.MailId == it.t3.MailId)
                .Where(it =>
                    (it.t1.MailId == request.MailId || it.t1.ObjId == request.MailId)
                    && it.t1.CorrelateType == SysEnum.CorrelateType.ReceiveMail.ToString()
                )
                .WithTempQuery(it => new GetRelateMailDto(
                    it.t1.Id,
                    it.t1.MailId,
                    "'发件'",
                    it.t1.CreateTime,
                    it.t2.MailNo,
                    it.t2.MailFrom,
                    it.t2.MailSubject,
                    it.t1.CreateBy,
                    it.t2.MailDate,
                    it.t3.Status,
                    it.t1.ObjId
                ));

            var list = await receiveTemp
                .UnionAll(sendTemp)
                .OrderByDescending(it => it.CreateTime)
                .Page(request.PageIndex!.Value, request.PageSize!.Value)
                .Count(out var totalCount)
                .ToListAsync(cancellationToken);
            return new PageResult<GetRelateMailDto>()
            {
                Data = await list.ToAsyncEnumerable()
                    .SelectAwait(async receiveList =>
                    {
                        if (receiveList.RelateUserId is not null)
                        {
                            ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                                userInfoRepository;
                            receiveList.RelateUser = new
                            {
                                CnName = (
                                    await userBaseInfoRepository.GetCacheValueAsync(
                                        receiveList.RelateUserId,
                                        cancellationToken: cancellationToken
                                    )
                                )?.CnName ?? "",
                                UserId = receiveList.RelateUserId,
                            };
                        }

                        return receiveList;
                    })
                    .ToListAsync(cancellationToken: cancellationToken),
                Page = request.PageIndex.Value,
                PageSize = request.PageSize.Value,
                Total = totalCount,
            };
        }
    }
}
