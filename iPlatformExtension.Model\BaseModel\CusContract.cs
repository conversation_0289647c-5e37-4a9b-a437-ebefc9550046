using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_contract", DisableSyncStructure = true)]
	public partial class CusContract {

		/// <summary>
		/// 合同标识ID
		/// </summary>
		[ Column(Name = "contract_id", StringLength = 50, IsNullable = false, IsPrimary = true)]
		public string ContractId { get; set; } = Guid.CreateVersion7().ToString().ToUpper();

		/// <summary>
		/// 合同编号
		/// </summary>
		[ Column(Name = "contract_no", StringLength = 50)]
		public string ContractNo { get; set; }

		/// <summary>
		/// 合同主题
		/// </summary>
		[ Column(Name = "contract_title", StringLength = 200, IsNullable = false)]
		public string ContractTitle { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 客户ID
		/// </summary>
		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		/// <summary>
		/// 生效时间
		/// </summary>
		[ Column(Name = "effective_date")]
		public DateTime? EffectiveDate { get; set; }

		/// <summary>
		/// 截止时间
		/// </summary>
		[ Column(Name = "end_date")]
		public DateTime? EndDate { get; set; }

		/// <summary>
		/// 合同是否可用
		/// <remarks>合同是否作废取反</remarks>
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 200)]
		public string Remark { get; set; }

		/// <summary>
		/// 签订者
		/// </summary>
		[ Column(Name = "signatory", StringLength = 50, IsNullable = false)]
		public string Signatory { get; set; }

		/// <summary>
		/// 签订者用户工号
		/// </summary>
		[Column(Name = "signatory_user", StringLength = 50, IsNullable = false)]
		public string SignatoryUser { get; set; } = string.Empty;

		/// <summary>
		/// 签订这用户ID
		/// </summary>
		[Column(Name = "signatory_user_id", StringLength = 50, IsNullable = false)]
		public string SignatoryUserId { get; set; } = string.Empty;
		
		/// <summary>
		/// 签订时间
		/// </summary>
		[ Column(Name = "signing_date")]
		public DateTime? SigningDate { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// 合同类型
		/// </summary>
		[Column(Name = "contract_type")]
		public string ContractType { get; set; } = string.Empty;

		/// <summary>
		/// 申请类型
		/// </summary>
		[Column(Name = "application_type")]
		public string ApplicationType { get; set; } = string.Empty;

		/// <summary>
		/// CRM合同id
		/// </summary>
		[Column(Name = "crm_contract_id")]
		public string CrmContractId { get; set; } = string.Empty;

		/// <summary>
		/// 对方签约主体
		/// </summary>
		[Column(Name = "customer_contracting_entity")]
		public string CustomerContractingEntity { get; set; } = string.Empty;

		/// <summary>
		/// 我方签约主体
		/// </summary>
		[Column(Name = "my_contracting_entity")]
		public string MyContractingEntity { get; set; } = string.Empty;

		/// <summary>
		/// 合同来源
		/// </summary>
		[Column(Name = "contract_source")]
		public string ContractSource { get; set; } = string.Empty;

		/// <summary>
		/// 作废原因
		/// </summary>
		[Column(Name = "invalidation_reason")]
		public string InvalidationReason { get; set; } = string.Empty;
		
		/// <summary>
		/// 归档状态
		/// </summary>
		[Column(Name = "archive_status")]
		public string ArchiveStatus { get; set; } = string.Empty;

		/// <summary>
		/// 合同明细
		/// </summary>
		[Navigate(nameof(ContractDetail.ContractId))]
		public ICollection<ContractDetail>? ContractDetails { get; set; }


		/// <summary>
		/// 是否可编辑
		/// </summary>
		[Column(IsIgnore = true)]
		public bool Editable => string.IsNullOrEmpty(CrmContractId);
	}

}
