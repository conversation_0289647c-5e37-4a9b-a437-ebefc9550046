using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_fee_type_bizname", DisableSyncStructure = true)]
	public partial class BasFeeTypeBizname {

		[ Column(Name = "biz_name", StringLength = 300)]
		public string BizName { get; set; }

		[ Column(Name = "n_id", StringLength = 50)]
		public string NId { get; set; }

		[ Column(Name = "n_name", StringLength = 500)]
		public string NName { get; set; }

		[ Column(Name = "num")]
		public int? Num { get; set; }

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

	}

}
