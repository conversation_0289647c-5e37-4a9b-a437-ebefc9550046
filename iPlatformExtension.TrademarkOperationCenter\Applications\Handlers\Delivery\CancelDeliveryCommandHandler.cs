﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class CancelDeliveryCommandHandler : IRequestHandler<CancelDeliveryCommand>
{
    public Task Handle(CancelDeliveryCommand request, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }
}