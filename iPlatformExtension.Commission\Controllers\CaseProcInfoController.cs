﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Commission.Application.Models.Proc;
using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Commission.Application.Queries.Proc;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Commission.Controllers;

/// <summary>
/// 任务控制器
/// </summary>
[ApiController]
[Route("case-proc-info")]
public sealed class CaseProcInfoController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// 通过我方文号查询对应的案件下的任务编号
    /// </summary>
    /// <param name="volume">我方文号</param>
    /// <param name="queryType">查询类型</param>
    /// <returns>任务编号集合</returns>
    [HttpGet("proc-nos")]
    public Task<IEnumerable<KeyValuePair<string, string>>> GetProcNosAsync(
        [FromQuery, Required] string volume, 
        [FromQuery, Required] ProcQueryType queryType)
    {
        return mediator.Send(new ProcNosQuery(volume, queryType), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 通过任务id获取任务预览信息
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <returns>任务预览信息</returns>
    [HttpGet("{procId}")]
    public Task<CaseProcDisplayInfo> GetProcDisplayInfoAsync([Required] string procId)
    {
        return mediator.Send(new CaseProcDisplayQuery(procId), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 通过任务id获取导师信息
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <returns>导师信息</returns>
    [HttpGet("{procId}/mentor")]
    public Task<KeyValuePair<string, string>> GetProcMentorInfoAsync([Required] string procId)
    {
        return mediator.Send(new ProcMentorQuery(procId), HttpContext.RequestAborted);
    }
}