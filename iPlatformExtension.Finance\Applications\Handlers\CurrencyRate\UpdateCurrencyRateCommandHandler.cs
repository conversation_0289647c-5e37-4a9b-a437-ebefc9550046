﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Finance.Applications.Commands.CurrencyRate;
using iPlatformExtension.Finance.Infrastructure;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.CurrencyRate;

internal sealed class UpdateCurrencyRateCommandHandler(
    IFreeSql freeSql, 
    IRedisCache<RedisCacheOptionsBase> redisCache, 
    ILogger<UpdateCurrencyRateCommandHandler> logger) 
    : IRequestHandler<UpdateCurrencyRateCommand>
{
    public async Task Handle(UpdateCurrencyRateCommand request, CancellationToken cancellationToken)
    {
        var rateInfo = request.Rate;
        var sourceCurrency = rateInfo.SourceCurrencyCode;
        var targetCurrency = rateInfo.TargetCurrencyCode;

        if (sourceCurrency == "CNY")
        {
            return;
        }

        if (rateInfo.EndDate < DateTime.Now)
        {
            logger.LogCurrencyRateOverdue(DateTime.Now, rateInfo.BeginDate, rateInfo.EndDate);
            return;
        }

        if (rateInfo.IsDeleted)
        {
            logger.LogIgnoreInvalidCurrencyRate(rateInfo.Id, rateInfo.SourceCurrencyCode, rateInfo.TargetCurrencyCode);
            return;
        }

        var currencyRate = await freeSql.Select<BasCurrencyRate>().Where(rate => rate.BaseCurrencyId == targetCurrency)
            .Where(rate => rate.CurrencyId == sourceCurrency)
            .FirstAsync(cancellationToken);

        if (currencyRate is not null)
        {
            currencyRate.Rate = rateInfo.ExchangeRage;
            currencyRate.StartDate = rateInfo.BeginDate;
            currencyRate.EndDate = rateInfo.EndDate;
            currencyRate.UpdateTime = DateTime.Now;
            
            await freeSql.Update<BasCurrencyRate>(currencyRate.HistoryId)
                .Set(rate => rate.Rate, rateInfo.ExchangeRage)
                .Set(rate => rate.StartDate, rateInfo.BeginDate)
                .Set(rate => rate.EndDate, rateInfo.EndDate)
                .Set(rate => rate.UpdateTime, DateTime.Now)
                .Set(rate => rate.UpdateUserId, "52C742FA-76D4-4E5D-B3AD-C51841D576DD")
                .ExecuteAffrowsAsync(cancellationToken);
            await redisCache.SetCacheValueAsync(typeof(BasCurrencyRate).FullName ?? nameof(BasCurrencyRate),
                new CurrencyRateKey(rateInfo.SourceCurrencyCode, rateInfo.TargetCurrencyCode), currencyRate, cancellationToken: cancellationToken);
        }
        else
        {
            currencyRate = new BasCurrencyRate()
            {
                HistoryId = Guid.NewGuid().ToString(),
                BaseCurrencyId = targetCurrency,
                CurrencyId = sourceCurrency,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now,
                StartDate = rateInfo.BeginDate,
                EndDate = rateInfo.EndDate,
                Rate = rateInfo.ExchangeRage,
                CreateUserId = "52C742FA-76D4-4E5D-B3AD-C51841D576DD",
                UpdateUserId = "52C742FA-76D4-4E5D-B3AD-C51841D576DD"
            };
            await freeSql.Insert(currencyRate).ExecuteAffrowsAsync(cancellationToken);
        }
    }
}