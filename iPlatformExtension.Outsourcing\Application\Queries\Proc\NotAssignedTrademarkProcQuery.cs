﻿using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Queries.Proc;

internal sealed record NotAssignedTrademarkProcQuery(
    string AuthorizationType,
    string? Keyword,
    SortCondition SortCondition,
    int? PageIndex, 
    int? PageSize) 
    : IRequest<IEnumerable<NotAssignedTrademarkProcDto>>, IPaginationParameters, IScopeLoggerCommand;