﻿using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class RefreshProcRewardDateExceptionHandler(ILogger<RefreshProcRewardDateCommand> logger) : IRequestExceptionHandler<RefreshProcRewardDateCommand, Unit, Exception>
{
    public Task Handle(RefreshProcRewardDateCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        logger.LogRefreshRewardEffectiveDateError(exception);
        state.SetHandled(Unit.Value);
        return Task.CompletedTask;
    }
}