﻿using Confluent.Kafka;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.KafKa.Models;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.MQ.KafKa.Handlers.Consumer;

public sealed class DeadLetterConsumeFailedHandler<TMessageKey, TMessageValue>(
    IProducer<TMessageKey, TMessageValue> producer, ILoggerFactory loggerFactory) : IConsumeFailedHandler
{
    public async ValueTask HandleAsync<TMessageKey1, TMessageValue1>(ConsumeFailedContext<TMessageKey1, TMessageValue1> context)
    {
        
        var messageResult = context.MessageResult;
        var exception = context.CurrentException;
        
        var logger = loggerFactory.CreateLogger(GetType());
        logger.LogKafkaConsumeFailed(messageResult.Topic, messageResult.Partition.Value, messageResult.Offset.Value, exception);
        
        var topic = messageResult.Topic;
        var deadLetterTopic = $"{topic}-dead";
        var message = messageResult.Message;

        await producer.ProduceAsync(deadLetterTopic, message as Message<TMessageKey, TMessageValue>);

        context.Handled = true;
    }
}