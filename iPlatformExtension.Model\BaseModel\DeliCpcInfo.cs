using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_cpc_info", DisableSyncStructure = true)]
	public partial class DeliCpcInfo {

		[ Column(Name = "cpc_anjuanbh", StringLength = 50)]
		public string CpcAnjuanbh { get; set; }

		[ Column(Name = "cpc_shenqingbh", StringLength = 50)]
		public string CpcShenqingbh { get; set; }

		[ Column(Name = "down_time")]
		public DateTime? DownTime { get; set; }

		[ Column(Name = "down_user", StringLength = 50)]
		public string DownUser { get; set; }

		[ Column(Name = "file_id", StringLength = 50)]
		public string FileId { get; set; }

		[ Column(Name = "import_time")]
		public DateTime? ImportTime { get; set; }

		[ Column(Name = "import_user", StringLength = 50)]
		public string ImportUser { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

	}

}
