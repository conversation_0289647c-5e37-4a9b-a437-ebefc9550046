﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.Finance.Applications.Models.Commission;

/// <summary>
/// 费项收入DTO
/// </summary>
/// <remarks>
/// 撤销划拨的费项，需要传之前的划拨的收款时间
/// </remarks>
/// <param name="FeeId">费项id</param>
/// <param name="PayeeId">收款主体id</param>
/// <param name="CustomerId">客户id</param>
/// <param name="PaymentMethod">收款方式</param>
/// <param name="HasInvoice">是否开票</param>
/// <param name="FeeClass">费用类型</param>
/// <param name="Amount">金额</param>
/// <param name="ReceiveDate">收款时间</param>
/// <param name="Sales">销售</param>
/// <param name="Tracker">跟案人</param>
/// <param name="BusinessUser">商务</param>
/// <param name="ClueUser">线索人</param>
public record FeesIncomeDto(
    [Required] string FeeId,
    [Required] string PayeeId, 
    [Required] string CustomerId,
    [Required] string PaymentMethod,
    [Required] bool HasInvoice,
    [Required] string FeeClass,
    [Required] decimal Amount,
    [Required] DateTime ReceiveDate,
    string? Sales,
    string? Tracker,
    string? BusinessUser,
    string? ClueUser);