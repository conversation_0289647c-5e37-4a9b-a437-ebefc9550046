﻿using iPlatformExtension.Commission.Infrastructure.Models;
using MediatR;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;

/// <summary>
/// 推送国内商标提成
/// </summary>
/// <param name="UserId">操作用户id</param>
/// <param name="DeptIds">关联部门id</param>
/// <param name="Year">年份</param>
/// <param name="Month">月份</param>
/// <param name="TraceParentId">追踪父id</param>
public sealed record PushDomesticCommissionWeightCommand(
    string UserId,
    IEnumerable<string> DeptIds,
    int Year,
    int Month) : IRequest, IBackgroundTracingCommand
{
    /// <inheritdoc />
    public string TraceParentId { get; set; } = null!;

    /// <inheritdoc />
    public string OperationName { get; set; } = "国内商标权值推送";
}