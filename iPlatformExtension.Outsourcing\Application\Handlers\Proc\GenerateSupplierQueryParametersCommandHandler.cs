﻿using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class GenerateSupplierQueryParametersCommandHandler(ISender sender) 
    : IRequestHandler<GenerateSupplierQueryParametersCommand, SupplierQueryParameters>
{
    public async Task<SupplierQueryParameters> Handle(GenerateSupplierQueryParametersCommand request, CancellationToken cancellationToken)
    {
        var procIds = request.ProcIds;
        var queryParameters = await sender.Send(new SupplierQueryParametersQuery(procIds[0]), cancellationToken);
        
        for (var i = 1; i < procIds.Length; i++)
        {
            var procId = procIds[i];
            var parameters = await sender.Send(new SupplierQueryParametersQuery(procId), cancellationToken);
            queryParameters.Combine(parameters);
        }
        
        return queryParameters;
    }
}