﻿using System.Diagnostics;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Converters.Abstraction;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;

namespace iPlatformExtension.Common.Cache;

public class StackExchangeRedisCache : IRedisCache<StackExchangeRedisCacheOptions>
{
    protected readonly IOptions<StackExchangeRedisCacheOptions> _options;

    protected readonly ILogger _logger;

    protected readonly SemaphoreSlim _connectLock;

    protected volatile IConnectionMultiplexer? _connection;

    private IDatabase? _currentDatabase;

    private readonly int _defaultDatabase;

    private readonly IRedisValueConverter _defaultRedisValueConverter;
    
    public IOptions<StackExchangeRedisCacheOptions> Options => _options;

    public IDatabase CurrentDatabase =>
        _currentDatabase ??= GetDatabase(_defaultDatabase);

    public StackExchangeRedisCache(IOptions<StackExchangeRedisCacheOptions> options, ILoggerFactory loggerFactory)
    {
        _options = options;
        _logger = loggerFactory.CreateLogger(GetType());
        _connectLock = new SemaphoreSlim(1, 1);

        var cacheOptions = options.Value;
        if (!string.IsNullOrWhiteSpace(cacheOptions.ConnectionString))
        {
            cacheOptions.ConfigurationOptions = ConfigurationOptions.Parse(cacheOptions.ConnectionString);
        }
        
        var connectOptions = cacheOptions.ConfigurationOptions;
        if (connectOptions is null)
        {
            var initializeException = new ArgumentNullException(nameof(connectOptions),"没有redis连接选项信息");
            _logger.LogError(initializeException, "redis初始化连接失败");
            throw initializeException;
        }

        _defaultDatabase = connectOptions.DefaultDatabase ?? 0;
        _defaultRedisValueConverter = new JsonRedisValueConverter();
    }

    public ValueTask<IDatabase> CurrentDatabaseAsync => _currentDatabase is null ? ChangeDatabaseAsync(_defaultDatabase) : new ValueTask<IDatabase>(_currentDatabase);

    public async ValueTask<IDatabase> GetDatabaseAsync(int db)
    {
        if (_connection is null)
        {
            await ConnectAsync(CancellationToken.None).ConfigureAwait(false);
        }

        Debug.Assert(_connection != null, "创建redis连接失败");
        return _connection.GetDatabase(db);
    }

    public virtual IDatabase GetDatabase(int db) => GetDatabaseAsync(db).ConfigureAwait(false).GetAwaiter().GetResult();

    public async ValueTask<IDatabase> ChangeDatabaseAsync(int db)
    {
        _currentDatabase = await GetDatabaseAsync(db);
        return _currentDatabase;
    }

    public virtual async Task<bool> SetCacheValueAsync<TKey, TValue>(string cacheKey, TKey key, TValue value, bool updateIfExists = false, CancellationToken cancellationToken = default)
    {
        var db = await CurrentDatabaseAsync;
        var keyConverter = FindConverter(typeof(TKey));
        var valueConverter = FindConverter(typeof(TValue));

        var when = updateIfExists ? When.Always : When.NotExists;
        return await db.HashSetAsync(cacheKey, keyConverter.ConvertTo(key),
            valueConverter.ConvertTo(value), when).ConfigureAwait(false);
    }

    public virtual async Task<bool> RemoveCacheValueAsync<TKey>(string cacheKey, TKey key, CancellationToken cancellationToken = default)
    {
        var db = await CurrentDatabaseAsync;
        var keyConverter = FindConverter(typeof(TKey));

        return await db.HashDeleteAsync(cacheKey, keyConverter.ConvertTo(key)).ConfigureAwait(false);
    }

    public async Task<bool> RemoveCacheValuesAsync(string cacheKey, CancellationToken cancellationToken = default)
    {
        var db = await CurrentDatabaseAsync;
        return await db.KeyDeleteAsync(cacheKey).ConfigureAwait(false);
    }

    public virtual Task<bool> RemoveCacheKeyAsync(string cacheKey, CancellationToken cancellationToken = default)
    {
        return RemoveCacheValuesAsync(cacheKey, cancellationToken);
    }

    public virtual async Task<TValue?> GetCacheValueAsync<TKey, TValue>(string cacheKey, TKey key, CancellationToken cancellationToken = default)
    {
        var db = await CurrentDatabaseAsync;
        var keyConverter = FindConverter(typeof(TKey));
        var valueConverter = FindConverter(typeof(TValue));

        var redisValue = await db.HashGetAsync(cacheKey, keyConverter.ConvertTo(key)).ConfigureAwait(false);
        return redisValue.IsNull ? default : (TValue?) valueConverter.ConvertFrom(redisValue, typeof(TValue));
    }

    public virtual async Task<bool> SetCacheValuesAsync<TKey, TValue>(string cacheKey, IDictionary<TKey, TValue> values, TimeSpan lifeTime, CancellationToken cancellationToken = default)
    {
        var count = values.Count;
        if (count == 0)
        {
            return false;
        }
        
        var keyConverter = FindConverter(typeof(TKey));
        var valueConverter = FindConverter(typeof(TValue));
        var tasks = new List<Task<bool>>(count + 1);

        var db = await CurrentDatabaseAsync;
        foreach (var (key, value) in values)
        {
            tasks.Add(db.HashSetAsync(cacheKey, keyConverter.ConvertTo(key), valueConverter.ConvertTo(value)));
        }
        tasks.Add(db.KeyExpireAsync(cacheKey, lifeTime));
        
        var results = await Task.WhenAll(tasks).WaitAsync(CancellationToken.None).ConfigureAwait(false);
        return results.All(result => result);
    }

    public virtual async Task<IEnumerable<TValue?>> GetCacheValuesAsync<TValue>(string cacheKey, CancellationToken cancellationToken = default)
    {
        var valueConverter = FindConverter(typeof(TValue));
        var db = await CurrentDatabaseAsync;
        var entries = await db.HashGetAllAsync(cacheKey).ConfigureAwait(false) ?? [];
        return entries.Select(entry => (TValue?)valueConverter.ConvertFrom(entry.Value, typeof(TValue)));
    }
    
    public virtual async Task<Dictionary<TKey, TValue?>> GetCacheKeyValuesAsync<TKey, TValue>(string cacheKey, CancellationToken cancellationToken = default)
    {
        var keyConverter = FindConverter(typeof(TKey));
        var valueConverter = FindConverter(typeof(TValue));
        var db = await CurrentDatabaseAsync;
        var entries = await db.HashGetAllAsync(cacheKey).ConfigureAwait(false) ?? [];
        
        var result = new Dictionary<TKey, TValue?>();
        foreach (var entry in entries)
        {
            var key = (TKey?)keyConverter.ConvertFrom(entry.Name, typeof(TKey));
            var value = (TValue?)valueConverter.ConvertFrom(entry.Value, typeof(TValue));
            
            if (key != null)
            {
                result[key] = value;
            }
        }
        
        return result;
    }

    public async Task<TResult?> ExecuteScriptAsync<TResult>(string script, string[]? keys = default, object[]? values = default, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(script);
        var db = await CurrentDatabaseAsync;

        RedisKey[]? keyParameters = null;
        if (keys is not null)
        {
            keyParameters = keys.Select(key => new RedisKey(key)).ToArray();
        }
        
        RedisValue[]? valueParameters = null;
        if (values is not null)
        {
            valueParameters = new RedisValue[values.Length];
            for (var i = 0; i < values.Length; i++)
            {
                var value = values[i];
                valueParameters[i] = FindConverter(value.GetType()).ConvertTo(value);
            }
        }

        var redisResult = await db.ScriptEvaluateAsync(script, keyParameters, valueParameters).ConfigureAwait(false);
        return (TResult?) FindConverter(typeof(TResult)).ConvertFrom((byte[])redisResult!, typeof(TResult));
    }

    /// <summary>
    /// 初始化
    /// </summary>
    /// <returns></returns>
    protected virtual async ValueTask ConnectAsync(CancellationToken token = default)
    {
        var options = _options.Value;
        var connectOptions = options.ConfigurationOptions;

        if (_connection is null)
        {
            await _connectLock.WaitAsync(token);
            if (_connection is not null)
            {
                _connectLock.Release();
                return;
            }
            _connection = await ConnectionMultiplexer.ConnectAsync(connectOptions!, new StringWriter());
            _currentDatabase = _connection.GetDatabase(_defaultDatabase);
            _connectLock.Release();
        }
    }

    protected virtual IConverter FindConverter(Type typeToConvert)
    {
        var options = _options.Value;
        var converter =
            options.Converters.FirstOrDefault(valueConverter => valueConverter.CanConvert(typeToConvert));
        return converter ?? _defaultRedisValueConverter;
    }
}