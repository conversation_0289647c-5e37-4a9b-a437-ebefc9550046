﻿using iPlatformExtension.Repository.Interface;
using FreeSql;
using iPlatformExtension.Model.BaseModel;
using Microsoft.Extensions.Caching.Memory;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;

namespace iPlatformExtension.Repository.Implement
{
    internal class SysFlowNodeRepository(
        IFreeSql<PlatformFreeSql> freeSql, 
        IMemoryCache memoryCache, 
        CacheExpirationToken<SysFlowNode> expirationToken,
        IRedisCache<RedisCacheOptionsBase> redisCache) 
        : BaseRepository<SysFlowNode, string>(freeSql), ISysFlowNodeRepository
    {
        /// <inheritdoc />
        public IMemoryCache MemoryCache { get; } = memoryCache;

        /// <inheritdoc />
        public CacheExpirationToken<SysFlowNode> ExpirationToken { get; } = expirationToken;

        /// <inheritdoc />
        public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
    }
}
