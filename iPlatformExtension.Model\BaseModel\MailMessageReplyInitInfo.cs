using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_message_reply_init_info", DisableSyncStructure = true)]
	public partial class MailMessageReplyInitInfo {

		[ Column(Name = "info_id", StringLength = 50, IsNullable = false)]
		public string InfoId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "init_id", StringLength = 50, IsNullable = false)]
		public string InitId { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; }

		[ Column(Name = "obj_type", StringLength = 50, IsNullable = false)]
		public string ObjType { get; set; }

	}

}
