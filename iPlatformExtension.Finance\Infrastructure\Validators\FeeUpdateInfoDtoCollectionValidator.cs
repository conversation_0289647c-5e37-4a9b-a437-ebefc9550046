﻿using FluentValidation;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Infrastructure.Validators;

internal class FeeUpdateInfoDtoCollectionValidator : AbstractValidator<FeeUpdateInfoDtoCollection>
{
    private static readonly IEqualityComparer<FeeUpdateInfoDto> comparer = new FeeUpdateInfoDtoEqualityComparer();

    public FeeUpdateInfoDtoCollectionValidator(IValidator<FeeUpdateInfoDto> validator)
    {
        RuleFor(collection => collection.UpdateInfo)
            .Must(dto => new HashSet<FeeUpdateInfoDto>(dto, comparer).Count == dto.Count())
            .WithMessage("包含重复的费项id");
        RuleForEach(dto => dto.UpdateInfo).SetValidator(validator);
    }
    
    private class FeeUpdateInfoDtoEqualityComparer : IEqualityComparer<FeeUpdateInfoDto>
    {
        public bool Equals(FeeUpdateInfoDto? x, FeeUpdateInfoDto? y)
        {
            if (x?.FeeId is null) return false;
            if (y?.FeeId is null) return false;
            return x.FeeId == y.FeeId;
        }

        public int GetHashCode(FeeUpdateInfoDto obj)
        {
            return obj.FeeId.GetHashCode();
        }
    }
}