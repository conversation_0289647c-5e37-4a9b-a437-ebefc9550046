﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Copy;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Copy
{
    /// <summary>
    /// 添加复制项
    /// </summary>
    public sealed class AddCopyItemCommandHandler : IRequestHandler<AddCopyItemCommand>
    {
        private readonly IFreeSql _freeSql;
        private readonly ICopyFiledListRepository _copyFiledListRepository;

        public AddCopyItemCommandHandler(IFreeSql freeSql, ICopyFiledListRepository copyFiledListRepository)
        {
            _freeSql = freeSql;
            _copyFiledListRepository = copyFiledListRepository;
        }

        public async Task Handle(AddCopyItemCommand request, CancellationToken cancellationToken)
        {
            await _copyFiledListRepository.InsertAsync(new CopyFiledList
            {
                Id = Guid.NewGuid().ToString(),
                FatherId = request.FatherId,
                Name = request.Name,
                Property = request.Property,
                Table = request.Table,
                Type = request.Type,
                KeyType = request.KeyType
            }, cancellationToken);
        }
    }
}

