﻿namespace iPlatformExtension.Public.Applications.Models.NiceCategory;

/// <summary>
/// 尼斯分类查询数据
/// </summary>
public class NiceCategoryItemDto
{
    /// <summary>
    /// id
    /// </summary>
    public string CategoryId { get; set; } = default!;

    /// <summary>
    /// 商品项编号
    /// </summary>
    public string CategoryNumber { get; set; } = string.Empty;

    /// <summary>
    /// 二级商品项编号
    /// </summary>
    public string ParentNumber { get; set; } = default!;

    /// <summary>
    /// 尼斯分类说明
    /// </summary>
    public string CategoryName { get; set; } = string.Empty;

    /// <summary>
    /// 一级商品项编号
    /// 大类
    /// </summary>
    public string GrandNumber { get; set; } = default!;

    /// <summary>
    /// 层级
    /// </summary>
    public int? Level { get; set; }
}