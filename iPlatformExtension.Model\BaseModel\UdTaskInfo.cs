using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ud_task_info", DisableSyncStructure = true)]
	public partial class UdTaskInfo {

		[ Column(Name = "task_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string TaskId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "flow_cur_user_id", StringLength = 50)]
		public string FlowCurUserId { get; set; }

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		[ Column(Name = "flow_update_time")]
		public DateTime? FlowUpdateTime { get; set; }

		/// <summary>
		/// 附件关联案件ID
		/// </summary>
		[ Column(Name = "relevance_id", StringLength = 50)]
		public string RelevanceId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "task_form_code", StringLength = 50)]
		public string TaskFormCode { get; set; }

		[ Column(Name = "task_name", StringLength = 200)]
		public string TaskName { get; set; }

		[ Column(Name = "task_no", StringLength = 50)]
		public string TaskNo { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
