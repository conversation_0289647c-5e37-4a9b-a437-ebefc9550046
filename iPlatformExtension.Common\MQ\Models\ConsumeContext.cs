﻿using System.Security.Claims;

namespace iPlatformExtension.Common.MQ.Models;

/// <summary>
/// 消费上下文
/// </summary>
public abstract class ConsumeContext(IServiceProvider serviceProvider)
{
    protected readonly Dictionary<string, object?> _items = new();
    
    /// <summary>
    /// 用户信息
    /// </summary>
    public ClaimsPrincipal User { get; internal set; } = new ();

    /// <summary>
    /// 获取主题
    /// </summary>
    public abstract string Topic { get; }

    /// <summary>
    /// 依赖注入容器
    /// </summary>
    public IServiceProvider Services { get; } = serviceProvider;

    /// <summary>
    /// 获取当前消息内容
    /// </summary>
    /// <typeparam name="T">消息序列化后的试题类型</typeparam>
    /// <returns>消息实体</returns>
    public abstract T? GetMessageValue<T>();

    public T? Get<T>(string key) => _items.TryGetValue(key, out var item) && item is T itemValue ? itemValue : default;
}