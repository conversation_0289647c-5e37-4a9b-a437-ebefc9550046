﻿using Confluent.Kafka;
using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Common.MQ.KafKa.Consumer;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Public.Applications.Commands.Flow;
using iPlatformExtension.Public.Applications.Models.Flow;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FlowProcInfoDto = iPlatformExtension.Public.Applications.Models.Flow.FlowInfoDto;

namespace iPlatformExtension.Public.MQ.FlowNotificationConsumer
{
    public class FlowNotificationHostedService : BackgroundService
    {
        private readonly ILogger<FlowNotificationHostedService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IConfiguration _configuration;
        private readonly IMediator _mediator;
        private readonly ConsumerBuilder<string, string> _consumerBuilder;
        private readonly IWxWorkClient _wxWorkClientExtension;

        public FlowNotificationHostedService(ILogger<FlowNotificationHostedService> logger, IWxWorkClient wxWorkClientExtension, IServiceProvider serviceProvider, IConfiguration configuration, IMediator mediator)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _configuration = configuration;
            _mediator = mediator;
            var config = configuration.GetSection("Kafka:FlowConsumer").Get<ConsumerConfig>();
            config.AutoOffsetReset = AutoOffsetReset.Earliest;
            _consumerBuilder = new ConsumerBuilder<string, string>(config)
                  .SetKeyDeserializer(Deserializers.Utf8)
                    .SetValueDeserializer(Deserializers.Utf8);
            _wxWorkClientExtension = wxWorkClientExtension;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("消费者启动");
            var config = _configuration.GetSection("KafKa:FlowConsumer").Get<FlowConsumerDto>();
            if (config != null && !string.IsNullOrEmpty(config.Topic))
            {
                _logger.LogInformation($"读取消费者配置成功:{config.Topic}");
                await Task.Factory.StartNew(async () =>
                {
                    using (var consumer = _consumerBuilder.Build())
                    {
                        _logger.LogInformation($"构建消费者成功{consumer.Name}");
                        consumer.Subscribe(config.Topic);
                        FlowMessageCommand request = null;
                        while (true)
                        {
                            try
                            {
                                var consumeResult = consumer.Consume();
                                if (consumeResult.IsPartitionEOF)
                                {
                                    //Console.WriteLine($"End of partition {consumeResult.TopicPartitionOffset} reached.");
                                    continue;
                                }
                                // 处理消息
                                if (consumeResult != null)
                                {
                                    // Console.WriteLine($"{nameof(FlowNotificationHostedService)} Received message at {consumeResult.TopicPartitionOffset}: {consumeResult.Value}");
                                    await _mediator.Send(new FlowMessageCommand(consumeResult.Message.Value));

                                }
                            }
                            catch (Exception e)
                            {
                                _logger.LogError(e, "FlowConsumerHandler消费异常");
                                Console.WriteLine($"FlowConsumerHandler消费异常,{e.Message}");
                                continue;
                            }
                        }
                    }
                }, stoppingToken);
            }
        }


        /// <summary>
        /// 流程过滤
        /// </summary>
        /// <param name="flowActivity"></param>
        /// <returns></returns>
        private bool FilterFlow(MQMessageData<MQFlowActivity> flowActivity, bool isTMII)
        {
            if (flowActivity.after != null)
            {
                //过滤递交中间过程
                if (flowActivity.after.FlowType == FlowTypeEnum.DE && flowActivity.after.Status < FLOW_STATUS.S5000 && flowActivity.after.PrevAuditTypeId.ToLower() == "submit")
                {
                    return true;
                }
                //仅处理流程类型为TII或者流程部门编码为TMII的流程
                if (flowActivity.after.FlowSubType != "TII" && !isTMII)
                {
                    return true;
                }
            }
            return false;
        }




        public DateTime ConvertTimestampToDateTime(long timestamp)
        {
            // Unix时间戳起始时间
            DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Local);
            // 将时间戳转换为TimeSpan
            TimeSpan time = TimeSpan.FromSeconds(timestamp);
            // 将TimeSpan添加到起始时间，得到结果DateTime
            return epoch.Add(time).ToLocalTime(); // 如果需要转换为本地时间，使用ToLocalTime()
                                                  // 如果需要保持UTC时间，使用以下代码
                                                  // return epoch.Add(time);
        }
    }
}
