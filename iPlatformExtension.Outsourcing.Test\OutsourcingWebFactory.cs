﻿using System.Net.Http.Headers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;

namespace iPlatformExtension.Outsourcing.Test;

public class OutsourcingWebFactory : WebApplicationFactory<Program>
{
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.UseEnvironment("Local");
        base.ConfigureWebHost(builder);
    }

    protected override void ConfigureClient(HttpClient client)
    {
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer",
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDQzMzM5NTksIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6ImNmY2JiNzZmLThhZGItNGMxNy1iODk3LTc4NTc5M2JmY2RhOCIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.QEk0OoJd1Tf2OwpnhbvE9-2MIdXZfehilsGF4jYtdV4");
        client.Timeout = TimeSpan.FromSeconds(300);
        base.ConfigureClient(client);
    }
}