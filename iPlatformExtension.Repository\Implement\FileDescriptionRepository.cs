﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class FileDescriptionRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    CacheExpirationToken<BasFileDesc> expirationToken,
    IRedisCache<RedisCacheOptionsBase> redisCache) : 
    BaseRepository<BasFileDesc, string>(freeSql), 
    IFileDescriptionRepository
{
    public IMemoryCache MemoryCache { get; } = memoryCache;

    public CacheExpirationToken<BasFileDesc> ExpirationToken { get; } = expirationToken;

    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;

    public IEqualityComparer<FileDescriptionKey>? KeyEqualityComparer => FileDescriptionKeyEqualityComparer.DefaultComparer;
}