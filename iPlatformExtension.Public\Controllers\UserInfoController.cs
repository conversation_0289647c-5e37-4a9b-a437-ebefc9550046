﻿using System.ComponentModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.User;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 用户控制器
/// </summary>
[ApiController]
[Route(("user"))]
public class UserInfoController(ISender sender) : ControllerBase
{
    /// <summary>
    /// 根据关键字查询用户信息
    /// </summary>
    /// <param name="keyword">关键字</param>
    /// <returns>用户信息</returns>
    [HttpGet]
    public Task<IEnumerable<UserInfoDto>> GetUsersAsync([FromQuery, Description("关键字")] string keyword = "")
    {
        return sender.Send(new UserKeywordQuery(keyword), HttpContext.RequestAborted);
    }
}