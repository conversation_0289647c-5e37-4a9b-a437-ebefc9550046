using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_trademark_items_history", DisableSyncStructure = true)]
	public partial class BasTrademarkItemsHistory {

		[ Column(Name = "create_time")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "cur_id", StringLength = 50, IsNullable = false)]
		public string CurId { get; set; }

		[ Column(Name = "history_id", StringLength = 50, IsNullable = false)]
		public string HistoryId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "id", StringLength = 50)]
		public string Id { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; }

		[ Column(Name = "is_tm5")]
		public bool? IsTm5 { get; set; }

		[ Column(Name = "item_type", StringLength = 50)]
		public string ItemType { get; set; }

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		[ Column(Name = "text_en_us", StringLength = 200)]
		public string TextEnUs { get; set; }

		[ Column(Name = "text_ja_jp", StringLength = 200)]
		public string TextJaJp { get; set; }

		[ Column(Name = "text_zh_cn", StringLength = 200)]
		public string TextZhCn { get; set; }

		[ Column(Name = "update_time")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "version_id", StringLength = 50, IsNullable = false)]
		public string VersionId { get; set; }

	}

}
