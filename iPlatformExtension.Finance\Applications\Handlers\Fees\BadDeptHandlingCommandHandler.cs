﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class BadDeptHandlingCommandHandler(
    ICaseFeeRepository caseFeeRepository,
    IHttpContextAccessor httpContextAccessor)
    : FeesUpdateCommandHandler(caseFeeRepository, httpContextAccessor)
{

    protected override FinanceOperation UpdateOperation => FinanceOperation.LockBadDept;
    
    
    protected override ValueTask<FeesUpdateResult> UpdateFeeAsync(CaseFeeList caseFeeList, FeeUpdateInfoDto feeUpdateInfoDto, string operatorId)
    {
        return new ValueTask<FeesUpdateResult>(caseFeeList.LockToBadDept(operatorId));
    }
}