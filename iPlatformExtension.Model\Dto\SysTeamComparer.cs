﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.BaseModel
{
    public partial class SysTeam
    {
        public sealed class SysTeamEqualityComparer : IEqualityComparer<SysTeam>
        {
            public bool Equals(SysTeam x, SysTeam y)
            {
                if (ReferenceEquals(x, y)) return true;
                if (ReferenceEquals(x, null)) return false;
                if (ReferenceEquals(y, null)) return false;
                if (x.GetType() != y.GetType()) return false;
                return x.IsEffect == y.IsEffect && x.IsExclusive == y.IsExclusive &&
                       x.TeamDescription == y.TeamDescription && x.TeamName == y.TeamName && x.Seq == y.Seq &&
                       x.AuthorizeUser == y.AuthorizeUser;
            }

            public int GetHashCode(SysTeam obj)
            {
                return HashCode.Combine(obj.IsEffect, obj.IsExclusive, obj.TeamDescription, obj.TeamName, obj.Seq,
                    obj.AuthorizeUser);
            }
        }

        public static IEqualityComparer<SysTeam> SysTeamComparer { get; } = new SysTeamEqualityComparer();
    }
}