﻿namespace iPlatformExtension.MailCenter.Applications.Models.SysConfig;

/// <summary>
/// 获取解析规则列表dto
/// </summary>
/// <param name="ConfigId">规则ID</param>
/// <param name="ConfigName">规则名称</param>
/// <param name="RuleNumber">规则编号</param>
/// <param name="ConfigType">分拣类型/系统动作,finish:分拣到承办人,allot:移入待分拣,ignore:忽略</param>
/// <param name="HandUser">分拣人</param>
/// <param name="HandUserName">分拣人名</param>
/// <param name="Account">应用邮箱</param>
/// <param name="ReadUserTemp">阅读人</param>
/// <param name="ReadUser">阅读人名</param>
/// <param name="IgnoreUser">忽略人</param>
/// <param name="IsEnabled">有效性</param>
/// <param name="UndertakeUser">承办人</param>
/// <param name="UndertakeUserName">承办人</param>
/// <param name="UpdateTime">更新时间</param>
/// <param name="UpdateUser">更新用户</param>
/// <param name="CreateTime">创建时间</param>
/// <param name="CreateUser">创建用户</param>
public record GetAnalysisRuleListDto(
    string ConfigId,
    string ConfigName,
    string RuleNumber,
    string ConfigType,
    string HandUserTemp,
string AccountTemp,
    string ReadUserTemp,
    string IgnoreUserTemp,
    int IsEnabled,
    string UndertakeUserTemp,
    string ConfigRemark,
    DateTime? UpdateTime, string UpdateUserTemp, DateTime? CreateTime, string CreateUserTemp)
{
    public object HandUser { get; set; }

    public object ReadUser { get; set; }

    public object Account { get; set; }

    public object IgnoreUser { get; set; }

    public object UndertakeUser { get; set; }

    public object UpdateUser { get; set; }

    public object CreateUser { get; set; }
};

