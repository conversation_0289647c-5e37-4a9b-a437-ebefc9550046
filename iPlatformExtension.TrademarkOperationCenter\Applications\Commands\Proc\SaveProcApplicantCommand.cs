﻿using MediatR;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;

/// <summary>
/// 任务申请人修改
/// </summary>
/// <param name="Id"></param>
/// <param name="ProcId"></param>
/// <param name="ChangeType"></param>
/// <param name="ChangeTypeName"></param>
/// <param name="TypeId"></param>
/// <param name="ApplicantId"></param>
/// <param name="ApplicantNameCn"></param>
/// <param name="ApplicantNameEn"></param>
/// <param name="AddressCn"></param>
/// <param name="AddressEn"></param>
/// <param name="CountryId"></param>
/// <param name="IsChineseIdentity"></param>
/// <param name="PostCode"></param>
/// <param name="CertificationType"></param>
/// <param name="CertificationNumber"></param>
/// <param name="FileList"></param>
public record SaveProcApplicantCommand(string Id, string ChangeType, string? ChangeTypeName, string? TypeId, string? ApplicantId, string? ApplicantNameCn, string? ApplicantNameEn, string? AddressCn, string? AddressEn, string? CountryId, bool IsChineseIdentity, string? PostCode, string? CertificationType, string? CertificationNumber, IList<ApplicantFileDto> FileList) : IRequest;
