﻿using System.Threading.Channels;
using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;

namespace iPlatformExtension.Commission.Infrastructure.BackgroundServices;

internal sealed class ForeignTrademarkCommissionWeightPushingService(
    Channel<PushForeignCommissionWeightCommand> channel, 
    ILogger<ForeignTrademarkCommissionWeightPushingService> logger, 
    IServiceScopeFactory serviceScopeFactory) 
    : BackgroundConsumeService<PushForeignCommissionWeightCommand>(channel, logger, serviceScopeFactory);