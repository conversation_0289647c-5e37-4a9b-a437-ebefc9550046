﻿using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.SysConfig
{
    public class GetMailHostListQuery : QueryBase, IRequest<PageResult<GetMailHostListDto>>
    {
        public string? Account { get; set; }
        public string? ShowName { get; set; }

        /// <summary>
        /// 模糊搜索关键字
        /// </summary>
        public string? Search { get; set; }
    }


}
