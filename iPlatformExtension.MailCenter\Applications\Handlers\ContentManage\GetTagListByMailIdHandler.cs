﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using System.Collections.Generic;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class GetTagListByMailIdHandler(IFreeSql<MailCenterFreeSql> mySql, IHttpContextAccessor httpContextAccessor) : IRequestHandler<GetTagListByMailIdQuery, List<MailTagDto>>
    {
        public async Task<List<MailTagDto>> Handle(GetTagListByMailIdQuery request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var list = await mySql.Select<MailTag, MailTagList>()
                 .InnerJoin((tag, list) => tag.Id == list.TagId)
                 .Where((tag, list) => tag.UserId == userId && list.MailId == request.MailId && tag.MailType == request.MailType).ToListAsync<MailTagDto>();
            return list;
        }
    }
}
