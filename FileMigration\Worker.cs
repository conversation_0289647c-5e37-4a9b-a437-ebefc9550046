using System.IO;
using System.Threading.Channels;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Clients.IPlatformWeb;
using iPlatformExtension.Model.BaseModel;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using ZstdSharp.Unsafe;

namespace FileMigration;

public sealed class Worker : BackgroundService
{
    private const string LeastIdKey = nameof(LeastIdKey);

    private readonly ILogger<Worker> _logger;

    private readonly IOptionsMonitor<WorkOptions> _options;

    private readonly IFreeSql _freeSql;

    private readonly PlatformFileClient _platformFileClient;

    private readonly HuaweiObsClient _huaweiObsClient;

    private readonly IDistributedCache _distributedCache;

    private readonly Channel<int> _messageQueue;

    private readonly DefaultRedisCache _defaultRedisCache;

    private DateTime? _startTime;

    private DateTime? _endTime;

    private bool? _hasBucket;

    private bool _stopped = false;

    public Worker(ILogger<Worker> logger, IOptionsMonitor<WorkOptions> options, IFreeSql freeSql, PlatformFileClient platformFileClient, HuaweiObsClient huaweiObsClient, IDistributedCache distributedCache, Channel<int> messageQueue, DefaultRedisCache defaultRedisCache)
    {
        _logger = logger;
        _options = options;
        _freeSql = freeSql;
        _platformFileClient = platformFileClient;
        _huaweiObsClient = huaweiObsClient;
        _distributedCache = distributedCache;
        _messageQueue = messageQueue;
        _defaultRedisCache = defaultRedisCache;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        Console.WriteLine("程序开始");
        _hasBucket ??= await _huaweiObsClient.HasBucketAsync(_options.CurrentValue.BucketName);
        if (!_hasBucket.Value)
        {
            _logger.LogError("缺少桶。系统将停止工作");
            return;
        }

        while (!stoppingToken.IsCancellationRequested && !_stopped)
        {
            if (IsExecutingTime())
            {
                try
                {
                    var workOptions = _options.CurrentValue;

                    RedisValue idValue = await _distributedCache.GetAsync(LeastIdKey, stoppingToken);
                    if (!idValue.TryParse(out int currentId) || currentId == default)
                    {
                        currentId = workOptions.DefaultLeastId;
                    }

                    var fileList = await _freeSql.Select<FileListA>().WithLock()
                        .Where(a => a.Migration == false && a.Id > currentId && a.Bucket == null)
                        .OrderBy(a => a.Id).Limit(workOptions.BufferSize).ToListAsync(stoppingToken);

                    if (fileList.Count == 0)
                    {
                        _logger.LogWarning("迁移完成，退出程序");
                        _stopped = true;
                        break;
                    }

                    var uploadTasks = new Task<int>[fileList.Count];
                    for (var i = 0; i < fileList.Count; i++)
                    {
                        uploadTasks[i] = UploadFileAsync(fileList[i], stoppingToken);
                    }

                    var fileId = (await Task.WhenAll(uploadTasks).WaitAsync(stoppingToken)).Max();
                    if (fileId > 0)
                    {
                        idValue = fileId;
                        await _distributedCache.SetAsync(LeastIdKey, idValue, stoppingToken);
                    }

                    await Task.Delay(workOptions.DelayTimeSpan, stoppingToken);
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "迁移文件失败");
                   // continue;
                }
            }
            else
            {
                Console.WriteLine($"{DateTime.Now.ToString()}:未到执行时间,程序休眠300s");
                Thread.Sleep(1000 * 60 * 5);
            }
        }
    }

    public async Task<int> UploadFileAsync(FileListA fileInfo, CancellationToken stoppingToken)
    {
        var workOptions = _options.CurrentValue;
        var update = _freeSql.Update<FileListA>(fileInfo.Id);
        var database = await _defaultRedisCache.CurrentDatabaseAsync;

        try
        {
            _logger.LogInformation("现在迁移文件[{Id}]", fileInfo.Id);
            Console.WriteLine($"现在迁移文件[{fileInfo.Id}]");

            var objectName = Path.Combine(fileInfo.ServerPath, fileInfo.FileName).Replace('\\', '/');
            if (await _huaweiObsClient.HasObjectAsync(workOptions.BucketName, objectName))
            {
                // _logger.LogWarning("华为云已存在相同的对象{ObjectName}，不对文件{Id}做迁移", objectName, fileInfo.Id);
                update.Set(a => a.Bucket, workOptions.BucketName)
                    .Set(a => a.Migration, true)
                    .Set(a => a.MigrationRemark, $"已存在相同的对象{objectName}");
            }
            else
            {
                var fileResponse = await _platformFileClient.GetFileAsync(fileInfo.ServerPath, fileInfo.FileName);
                if (!fileResponse.IsSuccessStatusCode)
                {
                    // _logger.LogWarning("获取文件[{Id}]失败。响应[{StatusCode}]", fileInfo.Id, fileResponse.StatusCode);
                    update.Set(a => a.Migration, true)
                        .Set(a => a.MigrationRemark, $"获取文件[{fileInfo.Id}]失败。响应[{fileResponse.StatusCode}]");
                }
                else
                {
                    await using var file = await fileResponse.Content.ReadAsStreamAsync(stoppingToken);
                    using var putObjectResponse = await _huaweiObsClient.PutFileAsync(objectName, file, workOptions.BucketName, fileInfo.FileName);

                    var statusCode = (int)putObjectResponse.StatusCode;
                    if (statusCode is >= 200 and < 300)
                    {

                        // _logger.LogInformation("成功迁移文件[{Id}]。路径为{ObjectUrl}", fileInfo.Id, putObjectResponse.ObjectUrl);
                        update.Set(a => a.Migration, true).Set(a => a.Bucket, workOptions.BucketName)
                            .Set(a => a.MigrationRemark, $"成功迁移文件[{fileInfo.Id}]。路径为{putObjectResponse.ObjectUrl}");
                    }
                    else
                    {
                        // _logger.LogWarning(putObjectResponse.OriginalResponse.Failure, "迁移文件[{Id}]失败", fileInfo.Id);
                        update.Set(a => a.Migration, true).Set(a => a.MigrationRemark, $"迁移文件[{fileInfo.Id}]失败"); ;
                    }
                }
            }

            await update.ExecuteAffrowsAsync(stoppingToken);
            Console.WriteLine($"--------[{fileInfo.Id}]-----next--");
        }
        catch (Exception e)
        {
            Console.WriteLine($"迁移文件[{fileInfo.Id}]失败");
            _logger.LogError(e, "文件{Id}迁移失败", fileInfo.Id);
            //await database.SortedSetAddAsync(BackupWorker.BackupKey, fileInfo.Id, fileInfo.Id);
           // await _messageQueue.Writer.WriteAsync(fileInfo.Id, stoppingToken);
            return -1;
        }
        return fileInfo.Id;
    }

    private bool IsExecutingTime()
    {
        var now = DateTime.Now;
        var dayOfWeek = now.DayOfWeek;

        if (dayOfWeek is DayOfWeek.Saturday or DayOfWeek.Sunday)
        {
            return true;
        }

        var options = _options.CurrentValue;
        if (_startTime is null || _endTime is null)
        {
            CalculateExecutingTime(options);
        }


        if (now < _startTime || now > _endTime)
        {
            CalculateExecutingTime(options);
        }


        return _startTime <= now && now <= _endTime;

        void CalculateExecutingTime(WorkOptions workOptions)
        {
            _startTime = DateTime.Today.Add(workOptions.WorkStartTime.ToTimeSpan());
            _endTime = _startTime.Value.Add(workOptions.WorkTimeSpan);
        }
    }
}