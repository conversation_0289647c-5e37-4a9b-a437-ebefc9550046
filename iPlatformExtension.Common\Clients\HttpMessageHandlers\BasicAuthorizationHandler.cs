﻿using System.Net.Http.Headers;
using System.Text;

namespace iPlatformExtension.Common.Clients.HttpMessageHandlers;

public abstract class BasicAuthorizationHandlerBase : DelegatingHandler
{
    private const string Scheme = "Basic";
    
    public abstract string UserName { get; }
    
    public abstract string Password { get; }

    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var token = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{UserName}:{Password}"));
        request.Headers.Authorization = new AuthenticationHeaderValue(Scheme, token);
        return base.SendAsync(request, cancellationToken);
    }
}