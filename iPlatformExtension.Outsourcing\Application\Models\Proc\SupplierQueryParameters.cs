﻿using System.ComponentModel;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Outsourcing.Application.Models.Proc;

public class SupplierQueryParameters()
{
    /// <summary>
    /// 客户id集合
    /// </summary>
    [Description("客户id集合")]
    public HashSet<string> CustomerIds { get; set; } = new(StringComparer.OrdinalIgnoreCase);

    /// <summary>
    /// 国家编码集合
    /// </summary>
    [Description("国家编码集合")]
    public HashSet<string> Countries { get; set; } = new(StringComparer.OrdinalIgnoreCase);

    /// <summary>
    /// 申请途径
    /// </summary>
    [Description("申请途径")]
    public string ApplyChannel { get; set; } = string.Empty;

    /// <summary>
    /// 任务id
    /// </summary>
    [Description("任务id")]
    public string CtrlProcId { get; set; } = string.Empty;

    /// <summary>
    /// 国际分类数量
    /// </summary>
    [Description("国际分类数量")]
    public int TrademarkClassedCount { get; set; }

    public SupplierQueryParameters(CaseProcInfo info) : this()
    {
        var caseInfo = info.CaseInfo;
        CustomerIds.Add(caseInfo.CustomerId);

        if (caseInfo.ApplyChannel == "madrid")
        {
            Countries = caseInfo.MemberCountry?.Split(',').Aggregate(Countries, (countries, country) =>
            {
                countries.Add(country);
                return countries;
            }) ?? [];
        }
        else if (!string.IsNullOrWhiteSpace(caseInfo.CountryId))
        {
            Countries.Add(caseInfo.CountryId);
        }

        ApplyChannel = caseInfo.ApplyChannel ?? string.Empty;
        CtrlProcId = info.CtrlProcId;
        TrademarkClassedCount = caseInfo.TrademarkClass?.Split(';').Length ?? 0;
    }

    
    /// <summary>
    /// 将当前查询参数对象与另一个查询参数对象合并。
    /// </summary>
    /// <param name="parameters">要合并的另一个查询参数对象。</param>
    /// <returns>返回合并后的查询参数对象。</returns>
    /// <exception cref="ApplicationException">
    /// 如果以下条件不满足时抛出异常：
    /// - 国家编码集合不一致
    /// - 申请途径集合不一致
    /// - 任务名称不一致
    /// - 国际分类数量不一致
    /// </exception>
    public SupplierQueryParameters Combine(SupplierQueryParameters parameters)
    {
        // 从参数对象中解构出各个查询参数
        parameters.Deconstruct(out var countries, out var applyChannel, out var ctrlProcId, out var trademarkClassedCount);
    
        // 检查国家编码集合是否一致
        if (!Countries.SetEquals(countries))
        {
            throw new ApplicationException("国家编码集合不一致");
        }
    
        // 检查申请途径集合是否一致
        if (ApplyChannel != applyChannel)
        {
            throw new ApplicationException("申请途径集合不一致");
        }
    
        // 检查任务名称是否一致
        if (CtrlProcId != ctrlProcId)
        {
            throw new ApplicationException("任务名称不一致");
        }
    
        // 检查国际分类数量是否一致
        if (TrademarkClassedCount != trademarkClassedCount)
        {
            throw new ApplicationException("国际分类数量不一致");
        }
    
        // 合并客户ID集合
        CustomerIds.UnionWith(parameters.CustomerIds);
        return this;
    }


    public void Deconstruct(out HashSet<string> countries, out string applyChannel, out string ctrlProcId, out int trademarkClassedCount)
    {
        countries = Countries;
        applyChannel = ApplyChannel;
        ctrlProcId = CtrlProcId;
        trademarkClassedCount = TrademarkClassedCount;
    }
}