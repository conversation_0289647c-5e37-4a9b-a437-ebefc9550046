using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_close_info", DisableSyncStructure = true)]
	public partial class CaseCloseInfo {

		/// <summary>
		/// id
		/// </summary>
		[ Column(Name = "case_close_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CaseCloseId { get; set; }

		/// <summary>
		/// 结案事务名称
		/// </summary>
		[ Column(Name = "close_name", StringLength = 200)]
		public string CloseName { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 当前流程处理人
		/// </summary>
		[ Column(Name = "flow_cur_user_id", StringLength = 50)]
		public string FlowCurUserId { get; set; }

		/// <summary>
		/// 流程状态
		/// </summary>
		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
