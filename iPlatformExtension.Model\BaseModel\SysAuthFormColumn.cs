using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_auth_form_column", DisableSyncStructure = true)]
	public partial class SysAuthFormColumn {

		[ Column(Name = "form_column_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FormColumnId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "column_id", StringLength = 50)]
		public string ColumnId { get; set; }

		[ Column(Name = "column_name_en_us", StringLength = 100)]
		public string ColumnNameEnUs { get; set; }

		[ Column(Name = "column_name_ja_jp", StringLength = 100)]
		public string ColumnNameJaJp { get; set; }

		[ Column(Name = "column_name_zh_cn", StringLength = 100)]
		public string ColumnNameZhCn { get; set; }

		[ Column(Name = "column_type", StringLength = 50)]
		public string ColumnType { get; set; }

		[ Column(Name = "form_id", StringLength = 50)]
		public string FormId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
