﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using Microsoft.AspNetCore.Authorization;

namespace iPlatformExtension.Outsourcing.Infrastructure.Authorization.ProcOutsourcing;

internal sealed class ProcOutsourcingAuthorizationHandler(IFreeSql<PlatformFreeSql> freeSql) 
    : AuthorizationHandler<ProcOutsourcingAuthorizationRequirement, HttpContext>
{
    
    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, ProcOutsourcingAuthorizationRequirement requirement,
        HttpContext resource)
    {
        var user = context.User;

        var procId = resource.Request.RouteValues["procId"] ?? string.Empty;
        var procInfo = await freeSql.Select<CaseProcInfo>(procId).WithLock()
                    .ToOneAsync(info => new CaseProcInfo()
                    {
                        ProcId = info.ProcId,
                        UndertakeUserId = info.UndertakeUserId,
                        FinishDate = info.FinishDate,
                        CaseInfo = new CaseInfo()
                        {
                            CaseTypeId = info.CaseInfo.CaseTypeId,
                            Id = info.CaseInfo.Id,
                        }
                    }, resource.RequestAborted);

        if (procInfo is null)
        {
            throw new NotFoundException(procId, "案件任务");
        }

        if (procInfo.CaseInfo.CaseTypeId == CaseType.Trade && user.GetUserId() == procInfo.UndertakeUserId && procInfo.FinishDate is null)
        {
            context.Succeed(requirement);
            return;
        }

        if (requirement.TryGetRoles(procInfo.CaseInfo.CaseTypeId, out var roles) && roles.Any(user.IsInRole))
        {
            context.Succeed(requirement);
        }
        else
        {
            context.Fail(new AuthorizationFailureReason(this, "只有流程人员可以编辑任务委外信息"));
        }
    }
}