﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.MailCenter.Applications.Models.Count;
using iPlatformExtension.MailCenter.Applications.Queries.Count;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using Microsoft.AspNetCore.SignalR.Client;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Count;

/// <summary>
/// Kafka邮件接收计数查询处理程序
/// </summary>
internal sealed class KafkaMailReceiveCountQueryHandler(IRedisCache<RedisCacheOptionsBase> redisCache, HubConnection hubConnection, IMediator mediator, IFreeSql<MailCenterFreeSql> freeSql)
    : RedisCacheHandle(redisCache, mediator, freeSql), IRequestHandler<KafkaMailReceiveCountQuery, IEnumerable<KafkaMailReceiveCountDto>>
{
    public async Task<IEnumerable<KafkaMailReceiveCountDto>> Handle(KafkaMailReceiveCountQuery request, CancellationToken cancellationToken)
    {
        var cdcModelPayload = request.model.Payload;
        var hostId = string.Empty;

        if (cdcModelPayload.Op.ToLower() == "u" &&
            cdcModelPayload.After?.Status == ReceiveFileType.Sort.GetHashCode() &&
            cdcModelPayload.Before?.Status != ReceiveFileType.Sort.GetHashCode())
        {
            hostId = cdcModelPayload.After.HostId;
            await UpdateHandleCache("Allot", hostId, +1, cdcModelPayload.TsDateTime, cancellationToken);
        }

        if (cdcModelPayload.Op.ToLower() == "u" &&
            cdcModelPayload.After?.Status != ReceiveFileType.Sort.GetHashCode() &&
            cdcModelPayload.Before?.Status == ReceiveFileType.Sort.GetHashCode())
        {
            hostId = cdcModelPayload.After?.HostId;
            if (!string.IsNullOrWhiteSpace(hostId))
            {
                await UpdateHandleCache("Allot", hostId, -1, cdcModelPayload.TsDateTime, cancellationToken);
            }
        }

        if (!string.IsNullOrWhiteSpace(hostId))
        {
            var userList = await freeSql.Select<MailAccess>().Where(it => it.HostId == hostId && it.AccessMode == "sorter").ToListAsync(it => it.UseId, cancellationToken);
            foreach (var userId in userList)
            {
                var count = await GetCount(userId, cancellationToken);
                await hubConnection.InvokeAsync("MailCountMessageAsync", "MailCenterCount", userId, count, cancellationToken: cancellationToken);
            }
        }

        return new List<KafkaMailReceiveCountDto>();
    }
}


