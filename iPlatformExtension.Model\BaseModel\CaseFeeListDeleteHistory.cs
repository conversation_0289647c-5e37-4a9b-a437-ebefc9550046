using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_fee_list_delete_history", DisableSyncStructure = true)]
	public partial class CaseFeeListDeleteHistory {

		/// <summary>
		/// 费用主键ID
		/// </summary>
		[ Column(Name = "fee_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FeeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 标准金额
		/// </summary>
		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		/// <summary>
		/// 待确认
		/// </summary>
		[ Column(Name = "auto_rule_id", StringLength = 50)]
		public string AutoRuleId { get; set; }

		/// <summary>
		/// 是否自动规则产生
		/// </summary>
		[ Column(Name = "auto_type", StringLength = 50, IsNullable = false)]
		public string AutoType { get; set; } = "0";

		/// <summary>
		/// 结算方式 d
		/// </summary>
		[ Column(Name = "balance_way", StringLength = 50)]
		public string BalanceWay { get; set; }

		/// <summary>
		/// 待确认?
		/// </summary>
		[ Column(Name = "base_type", StringLength = 50)]
		public string BaseType { get; set; }

		/// <summary>
		/// 账单日期
		/// </summary>
		[ Column(Name = "bill_date")]
		public DateTime? BillDate { get; set; }

		/// <summary>
		/// 账单号码
		/// </summary>
		[ Column(Name = "bill_no", StringLength = 50)]
		public string BillNo { get; set; }

		/// <summary>
		/// 案件ID/案件标识?
		/// </summary>
		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		/// <summary>
		/// 系数
		/// </summary>
		[ Column(Name = "coefficient")]
		public int? Coefficient { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 币别
		/// </summary>
		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		/// <summary>
		/// 日期限?
		/// </summary>
		[ Column(Name = "date_day")]
		public int DateDay { get; set; } = 0;

		/// <summary>
		/// 月期限?
		/// </summary>
		[ Column(Name = "date_month")]
		public int DateMonth { get; set; } = 0;

		/// <summary>
		/// 日期类型
		/// </summary>
		[ Column(Name = "date_type", StringLength = 50)]
		public string DateType { get; set; }

		/// <summary>
		/// 年日期?
		/// </summary>
		[ Column(Name = "date_year")]
		public int DateYear { get; set; } = 0;

		/// <summary>
		/// 折扣(%)
		/// </summary>
		[ Column(Name = "discount", StringLength = 50)]
		public string Discount { get; set; }

		/// <summary>
		/// 费用类型 OA 等 待确认
		/// </summary>
		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		/// <summary>
		/// 费减金额
		/// </summary>
		[ Column(Name = "fee_reduce_amount")]
		public double? FeeReduceAmount { get; set; }

		/// <summary>
		/// 费减类型
		/// </summary>
		[ Column(Name = "fee_reduce_type", StringLength = 50)]
		public string FeeReduceType { get; set; }

		/// <summary>
		/// 费用名称 关联表 bas_fee_type_name
		/// </summary>
		[ Column(Name = "fee_type_name_id", StringLength = 50)]
		public string FeeTypeNameId { get; set; }

		/// <summary>
		/// 未使用?
		/// </summary>
		[ Column(Name = "head_user_type", StringLength = 50)]
		public string HeadUserType { get; set; }

		/// <summary>
		/// 发票编号
		/// </summary>
		[ Column(Name = "invoice_no", StringLength = 300)]
		public string InvoiceNo { get; set; }

		/// <summary>
		/// 发票类型 d
		/// </summary>
		[ Column(Name = "invoice_type", StringLength = 50)]
		public string InvoiceType { get; set; }

		/// <summary>
		/// 是否自动生成费用
		/// </summary>
		[ Column(Name = "is_auto")]
		public bool IsAuto { get; set; } = false;

		/// <summary>
		/// 是否启用编辑(未使用?)
		/// </summary>
		[ Column(Name = "is_edit")]
		public bool IsEdit { get; set; } = true;

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		/// <summary>
		/// 首缴年度
		/// </summary>
		[ Column(Name = "is_first_pay_annual")]
		public bool IsFirstPayAnnual { get; set; } = false;

		/// <summary>
		/// 收入
		/// </summary>
		[ Column(Name = "is_income")]
		public bool? IsIncome { get; set; } = false;

		/// <summary>
		/// 向官方缴费
		/// </summary>
		[ Column(Name = "is_officer")]
		public bool IsOfficer { get; set; } = true;

		/// <summary>
		/// 向客户请款
		/// </summary>
		[ Column(Name = "is_request")]
		public bool IsRequest { get; set; } = true;

		/// <summary>
		/// 待确认
		/// </summary>
		[ Column(Name = "item_value", StringLength = 50)]
		public string ItemValue { get; set; }

		/// <summary>
		/// 32420 待确认
		/// </summary>
		[ Column(Name = "n_FeeID", DbType = "numeric(18,0)")]
		public decimal? NFeeID { get; set; }

		/// <summary>
		/// XXNNN 待确认
		/// </summary>
		[ Column(Name = "n_StatusID", StringLength = 50)]
		public string NStatusID { get; set; }

		/// <summary>
		/// 66628 待确认
		/// </summary>
		[ Column(Name = "n_YearFeeID", DbType = "numeric(18,0)")]
		public decimal? NYearFeeID { get; set; }

		/// <summary>
		/// 公布时间
		/// </summary>
		[ Column(Name = "notice_date")]
		public DateTime? NoticeDate { get; set; }

		/// <summary>
		/// 本所名称
		/// </summary>
		[ Column(Name = "office_name", StringLength = 2000)]
		public string OfficeName { get; set; }

		/// <summary>
		/// 官方名称英文
		/// </summary>
		[ Column(Name = "office_name_en_us", StringLength = 200)]
		public string OfficeNameEnUs { get; set; }

		/// <summary>
		/// 官方名称
		/// </summary>
		[ Column(Name = "office_name_zh_cn", StringLength = 200)]
		public string OfficeNameZhCn { get; set; }

		[ Column(Name = "officer_lock_del")]
		public bool OfficerLockDel { get; set; } = false;

		/// <summary>
		/// 缴官费状态 d
		/// </summary>
		[ Column(Name = "officer_status", StringLength = 50, IsNullable = false)]
		public string OfficerStatus { get; set; } = "0";

		/// <summary>
		/// 付款单号
		/// </summary>
		[ Column(Name = "pay_bill_no", StringLength = 50)]
		public string PayBillNo { get; set; }

		/// <summary>
		/// 实付日期
		/// </summary>
		[ Column(Name = "pay_cooperation_date")]
		public DateTime? PayCooperationDate { get; set; }

		/// <summary>
		/// 付款日期
		/// </summary>
		[ Column(Name = "pay_date")]
		public DateTime? PayDate { get; set; }

		/// <summary>
		/// 应付期限
		/// </summary>
		[ Column(Name = "pay_due_date")]
		public DateTime? PayDueDate { get; set; }

		[ Column(Name = "pay_lock_del")]
		public bool PayLockDel { get; set; } = false;

		/// <summary>
		/// 缴费期限
		/// </summary>
		[ Column(Name = "pay_office", StringLength = 50)]
		public string PayOffice { get; set; }

		/// <summary>
		/// 缴费公司 未使用
		/// </summary>
		[ Column(Name = "pay_office_company", StringLength = 50)]
		public string PayOfficeCompany { get; set; }

		/// <summary>
		/// 缴费编号
		/// </summary>
		[ Column(Name = "pay_office_serial", StringLength = 50)]
		public string PayOfficeSerial { get; set; }

		/// <summary>
		/// 缴费日期
		/// </summary>
		[ Column(Name = "pay_officer_date")]
		public DateTime? PayOfficerDate { get; set; }

		/// <summary>
		/// 缴费日期
		/// </summary>
		[ Column(Name = "pay_officer_due_date")]
		public DateTime? PayOfficerDueDate { get; set; }

		/// <summary>
		/// 缴费期限
		/// </summary>
		[ Column(Name = "pay_officer_legal_date")]
		public DateTime? PayOfficerLegalDate { get; set; }

		/// <summary>
		/// 到款状态 d
		/// </summary>
		[ Column(Name = "pay_status", StringLength = 50, IsNullable = false)]
		public string PayStatus { get; set; } = "0";

		/// <summary>
		/// 支付状态
		/// </summary>
		[ Column(Name = "pay_status_id", StringLength = 50)]
		public string PayStatusId { get; set; }

		/// <summary>
		/// 应付第三方日期
		/// </summary>
		[ Column(Name = "pay_third_due_date")]
		public DateTime? PayThirdDueDate { get; set; }

		/// <summary>
		/// 支付类型
		/// </summary>
		[ Column(Name = "pay_type")]
		public bool? PayType { get; set; }

		/// <summary>
		/// 支付方式
		/// </summary>
		[ Column(Name = "pay_way", StringLength = 50)]
		public string PayWay { get; set; }

		/// <summary>
		/// 缴费人名称
		/// </summary>
		[ Column(Name = "payment_agency", StringLength = 500)]
		public string PaymentAgency { get; set; }

		/// <summary>
		/// 缴费名称 d
		/// </summary>
		[ Column(Name = "payment_name", StringLength = 50)]
		public string PaymentName { get; set; }

		/// <summary>
		/// 预请款日期
		/// </summary>
		[ Column(Name = "pre_request_date")]
		public DateTime? PreRequestDate { get; set; }

		/// <summary>
		/// 任务ID
		/// </summary>
		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "proc_property", StringLength = 100)]
		public string ProcProperty { get; set; }

		/// <summary>
		/// 到款日期
		/// </summary>
		[ Column(Name = "receive_date")]
		public DateTime? ReceiveDate { get; set; }

		/// <summary>
		/// 应收日期
		/// </summary>
		[ Column(Name = "receive_due_date")]
		public DateTime? ReceiveDueDate { get; set; }

		/// <summary>
		/// 收费规则
		/// </summary>
		[ Column(Name = "receive_rule", StringLength = 50)]
		public string ReceiveRule { get; set; }

		/// <summary>
		/// 到款状态 d
		/// </summary>
		[ Column(Name = "receive_status", StringLength = 50, IsNullable = false)]
		public string ReceiveStatus { get; set; } = "0";

		/// <summary>
		/// 应收金额
		/// </summary>
		[ Column(Name = "recieve_amount", DbType = "money")]
		public decimal? RecieveAmount { get; set; }

		/// <summary>
		/// 备注
		/// </summary>
		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		/// <summary>
		/// 请款日期(未使用)
		/// </summary>
		[ Column(Name = "request_date")]
		public DateTime? RequestDate { get; set; }

		/// <summary>
		/// 请款ID（未使用）
		/// </summary>
		[ Column(Name = "request_id", StringLength = 50)]
		public string RequestId { get; set; }

		[ Column(Name = "request_lock_del")]
		public bool RequestLockDel { get; set; } = false;

		/// <summary>
		/// 请款编号（未使用）
		/// </summary>
		[ Column(Name = "request_no", StringLength = 50)]
		public string RequestNo { get; set; }

		/// <summary>
		/// sales_user_id
		/// </summary>
		[ Column(Name = "sales_user_id", StringLength = 50)]
		public string SalesUserId { get; set; }

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public int Seq { get; set; } = 1;

		/// <summary>
		/// 标准金额
		/// </summary>
		[ Column(Name = "standard_amount", DbType = "money")]
		public decimal? StandardAmount { get; set; }

		/// <summary>
		/// 税率
		/// </summary>
		[ Column(Name = "tax_rate", StringLength = 50)]
		public string TaxRate { get; set; }

		/// <summary>
		/// 跟案人
		/// </summary>
		[ Column(Name = "track_user_id", StringLength = 50)]
		public string TrackUserId { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
