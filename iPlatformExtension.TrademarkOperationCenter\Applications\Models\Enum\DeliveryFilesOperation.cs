﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;

/// <summary>
/// 递交文件操作枚举
/// Ignore：忽略原来的文件。只做追加
/// Replace：替换相同类型的文件。其余的追加
/// Cover：全量覆盖原来的文件
/// </summary>
public enum DeliveryFilesOperation
{
    /// <summary>
    /// 忽略原来的文件。只做追加
    /// </summary>
    Ignore,
    
    /// <summary>
    /// 替换相同类型的文件。其余的追加
    /// </summary>
    Replace,
    
    /// <summary>
    /// 全量覆盖原来的文件
    /// </summary>
    Cover
}