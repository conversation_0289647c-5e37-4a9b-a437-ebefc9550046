﻿using iPlatformExtension.Service.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;
using iPlatformExtension.Common.Helper;

namespace iPlatformExtension.Service.Implement
{
    public class DateCalculationService(IFreeSql dbQuery, IMemoryCache memoryCache) : IDateCalculationService
    {
        /// <inheritdoc />
        public IFreeSql DbQuery { get; } = dbQuery;

        /// <summary>
        /// 增加节假日，不判断当天
        /// </summary>
        /// <param name="dateTime">日期</param>
        /// <param name="day">天数</param>
        /// <returns></returns>
        public DateTime AddHoliday(DateTime dateTime, int day)
        {
            if (day <= 0)
            {
                return dateTime;
            }
            for (var i = 0; i < day; i++)
            {
                do
                {
                    dateTime = dateTime.AddDays(1);
                } while (!DataHelper.IsHoliday(GetHolidaySetting(), dateTime));
            }
            return dateTime;
        }

        private Dictionary<DateTime, bool> GetHolidaySetting()
        {
            if (memoryCache.TryGetValue("Holiday", out Dictionary<DateTime, bool>? data))
            {
                return data ?? new Dictionary<DateTime, bool>();
            }

            var sysHoliday = DbQuery.Select<SysHoliday>().WithLock().ToDictionary(it => it.Date, it => it.Holiday);
            memoryCache.Set("Holiday", sysHoliday);
            return sysHoliday;
        }
    }
}
