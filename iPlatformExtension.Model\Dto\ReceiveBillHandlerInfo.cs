﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 到款处理信息
/// </summary>
public record ReceiveBillHandlerInfo
{
    /// <summary>
    /// 请款对象信息
    /// </summary>
    public RequestObjectInfo RequestObjectDto { get; init; } = default!;

    /// <summary>
    /// 客户信息
    /// </summary>
    public MatchedCustomerInfo CustomerDto { get; init; } = default!;

    /// <summary>
    /// 商务人员信息
    /// </summary>
    public UserInfoDto? BusinessPersonnelDto { get; init; }
        
    /// <summary>
    /// 请款对象信息
    /// </summary>
    public record RequestObjectInfo
    {
        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; init; } = default!;

        /// <summary>
        /// 主键id
        /// </summary>
        public string Id { get; init; } = default!;

        /// <summary>
        /// 请款对象编码
        /// </summary>
        public string? RequestObjectCode { get; init; }
    }
    
    /// <summary>
    /// 客户信息
    /// </summary>
    public record MatchedCustomerInfo
    {
        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; init; } = default!;

        /// <summary>
        /// 主键id
        /// </summary>
        public string Id { get; init; } = default!;

        /// <summary>
        /// 客户crm码
        /// </summary>
        public string? CrmCode { get; init; }

        /// <summary>
        /// 是否境外
        /// </summary>
        public bool? IsOutbound { get; init; }
    }

}