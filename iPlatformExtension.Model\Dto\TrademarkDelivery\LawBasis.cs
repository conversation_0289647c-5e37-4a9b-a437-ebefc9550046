﻿namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 法律依据
/// </summary>
public class LawBasis
{
    /// <summary>
    /// 理由
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 法律id
    /// </summary>
    public int LawId { get; set; }

    /// <summary>
    /// 文件名称
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 法律名称
    /// </summary>
    public string LawName { get; set; } = string.Empty;

    /// <summary>
    /// 文件链接
    /// </summary>
    public string FileUrl { get; set; } = string.Empty;
}