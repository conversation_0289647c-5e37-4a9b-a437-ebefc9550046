using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_international_search", DisableSyncStructure = true)]
	public partial class BasInternationalSearch {

		[ Column(Name = "search_id", StringLength = 50, IsNullable = false)]
		public string SearchId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "text_en_us", StringLength = 2000)]
		public string TextEnUs { get; set; }

		[ Column(Name = "text_ja_jp", StringLength = 2000)]
		public string TextJaJp { get; set; }

		[ Column(Name = "text_zh_cn", StringLength = 2000)]
		public string TextZhCn { get; set; }

	}

}
