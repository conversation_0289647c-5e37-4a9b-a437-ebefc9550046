using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.AuditUser
{
    /// <summary>
    /// 保存发件审核人命令
    /// </summary>
    /// <param name="AuditingUserIds">审核人ID列表</param>
    /// <param name="AuditingUserNames">审核人名称列表，当AuditingUserIds为空时使用</param>
    public record SaveAuditUserCommand(
        List<string>? AuditingUserIds,
        List<string>? AuditingUserNames = null) : IRequest<List<string>>, IUnitOfWorkCommandMysql;
}
