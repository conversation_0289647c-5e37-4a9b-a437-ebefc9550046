﻿using iPlatformExtension.Commission.Application.Notifications.Proc;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;

namespace iPlatformExtension.Commission.Application.Handlers.Proc;

internal sealed class DomesticTrademarkMentorQueryHandler(IFreeSql freeSql) : IMatchNotificationHandler<ProcMentorQueryNotification>
{
    public ValueTask<bool> MatchAsync(ProcMentorQueryNotification notification, CancellationToken cancellationToken)
    {
        var (_, caseType, caseDirection) = notification;
        return new ValueTask<bool>(caseType == CaseType.Trade && caseDirection == CaseDirection.II);
    }

    public async Task HandleAsync(ProcMentorQueryNotification notification, CancellationToken cancellationToken)
    {
        var mentorId = await freeSql.Select<SysFlowHistory>().WithLock()
            .InnerJoin<CaseProcFlow>((history, procFlow) => history.ObjId == procFlow.ProcFlowId)
            .Where<CaseProcFlow>(procFlow => procFlow.ProcId == notification.ProcId)
            .Where(history => history.NodeId == "751DDA05-106B-429A-AB48-B64403B17E32")
            .Where(history => history.FlowType == FlowType.Proofreading)
            .Where(history => history.FlowSubType == "TII")
            .Where(history => history.AuditTypeId == FlowAuditType.Submit)
            .OrderByDescending(history => history.AuditTime)
            .FirstAsync(history => history.AuditUserId, cancellationToken);

        notification.MentorId = mentorId ?? string.Empty;
    }
}