using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 递交信息修改DTO
/// </summary>
public class DeliveryResultDto
{
    /// <summary>
    /// 递交日期
    /// </summary>
    public DateTime? DeliveryDate { get; set; }

    /// <summary>
    /// 递交预览JSON
    /// </summary>
    public string DisplayJson { get; set; } = "[]";

    /// <summary>
    /// 成功与否
    /// </summary>
    [Required]
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string Message { get; set; } = "递交成功";

    /// <summary>
    /// 操作类型
    /// </summary>
    [Required]
    public TrademarkDeliveryOperation Operation { get; set; }

    /// <summary>
    /// 获取操作结果的描述
    /// </summary>
    /// <returns>描述</returns>
    public string GetOperationDescriptionWithResult()
    {
        return Operation switch
        {
            TrademarkDeliveryOperation.SubmitOfficial when Success => $"{Operation.GetDescription()}成功",
            TrademarkDeliveryOperation.SubmitOfficial when !Success => $"{Operation.GetDescription()}失败",
            _ => Operation.GetDescription()
        };
    }
}