﻿using System.Linq.Expressions;
using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class CaseInfoRepository : DefaultRepository<CaseInfo, string>, ICaseInfoRepository
{
    public CaseInfoRepository(IFreeSql freeSql, UnitOfWorkManager unitOfWorkManager) : base(freeSql, unitOfWorkManager)
    {
    }
}