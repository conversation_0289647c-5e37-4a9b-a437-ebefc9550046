using MongoDB.Driver.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace iPlatformExtension.Common.Converters;

/// <summary>
/// 统一json时间序列化格式
/// </summary>
public class DateTimeJsonConverter : JsonConverter<DateTime>
{
    /// <summary>
    /// DateTime类型的读取转换
    /// </summary>
    /// <param name="reader">读取器</param>
    /// <param name="typeToConvert"><c>typeof(DateTime)</c></param>
    /// <param name="options">序列化选项</param>
    /// <returns>DateTime类型的变量</returns>
    public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.String)
        {
            if (DateTime.TryParse(reader.GetString(), out DateTime date))
                return date;
        }

        if (reader.TokenType == JsonTokenType.Number)
        {
            if (reader.TryGetInt64(out var timestamp))
            {
                return DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime;
            }
        }
        return reader.GetDateTime();
    }

    /// <summary>
    /// DateTime类型的写出转换
    /// </summary>
    /// <param name="writer">写入器</param>
    /// <param name="value">要转换的DateTime值</param>
    /// <param name="options">序列化选项</param>
    public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString("yyyy-MM-dd HH:mm:ss"));
    }
}