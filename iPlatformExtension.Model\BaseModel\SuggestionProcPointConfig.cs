﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 建议类任务权值配置
	/// </summary>
	[Table(Name = "suggestion_proc_point_config", DisableSyncStructure = true)]
	public class SuggestionProcPointConfig {

		/// <summary>
		/// 主键
		/// </summary>
		[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
		public int Id { get; set; }

		/// <summary>
		/// 权值
		/// </summary>
		[Column(Name = "proc_point", DbType = "decimal(5,2)")]
		public decimal ProcPoint { get; set; } = 0M;

		/// <summary>
		/// 任务id
		/// </summary>
		[Column(Name = "rule_id")]
		public int RuleId { get; set; }

		/// <summary>
		/// 目标任务名称id
		/// </summary>
		[Column(Name = "target_ctrl_proc_id", StringLength = 50, IsNullable = false)]
		public string TargetCtrlProcId { get; set; } = null!;

		/// <summary>
		/// 目标任务标识
		/// </summary>
		[Column(Name = "target_proc_mark", StringLength = 50, IsNullable = false)]
		public string TargetProcMark { get; set; } = "";


		/// <summary>
		/// 商标权值配置id
		/// </summary>
		[Column(Name = "trademark_bonus_config_id", StringLength = 50, IsNullable = false)]
		public string TrademarkBonusConfigId { get; set; } = string.Empty;

		/// <summary>
		/// 是否可用
		/// </summary>
		[Column(Name = "is_enable")]
		public bool IsEnable { get; set; } = true;
	}

}
