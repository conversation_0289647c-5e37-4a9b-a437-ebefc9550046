﻿using System.Diagnostics;
using System.Net.Mime;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Extensions;

/// <summary>
/// 中间件扩展程序
/// </summary>
public static class MiddleWareExtension
{
    /// <summary>
    /// 文件目录浏览
    /// </summary>
    /// <param name="app">app构建者</param>
    /// <param name="contentRootPath">物理根路径</param>
    /// <param name="requestPath">uri请求根目录</param>
    /// <returns>app构建者</returns>
    public static IApplicationBuilder UseFileBrowser(this IApplicationBuilder app, string contentRootPath, string requestPath)
    {
        ArgumentNullException.ThrowIfNull(app);
        ArgumentNullException.ThrowIfNull(contentRootPath);
        ArgumentNullException.ThrowIfNull(requestPath);
        
        var extensionContentTypeProvider = new FileExtensionContentTypeProvider();
        extensionContentTypeProvider.Mappings.Add(".log", "text/plain; charset=utf-8");//识别扩展类型
        
        var fileServerOptions = new FileServerOptions
        {
            EnableDirectoryBrowsing = true,
            FileProvider = new PhysicalFileProvider(contentRootPath),
            RequestPath = requestPath,
            StaticFileOptions =
            {
                ContentTypeProvider = extensionContentTypeProvider,
                ServeUnknownFileTypes = true,
                DefaultContentType = "text/plain; charset=utf-8",
            }
        };

        app.UseFileServer(fileServerOptions);

        return app;
    }

    /// <summary>
    /// 记录远程客户端的IP地址以及端口
    /// </summary>
    /// <param name="app">app构建者</param>
    /// <returns>app构建者</returns>
    public static IApplicationBuilder UseIpAddressLogging(this IApplicationBuilder app)
    {
        ArgumentNullException.ThrowIfNull(app);
        app.Use((context, next) =>
        {
            var loggerFactory = context.RequestServices.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger("RemoteIP");
            var remoteIp = context.Connection.RemoteIpAddress?.ToString();
            logger.LogInformation("远程地址：{RemoteIp}, 端口：{RemotePort}", remoteIp, context.Connection.RemotePort);
            return next(context);
        });
        return app;
    }

    public static IApplicationBuilder UseIcon(this IApplicationBuilder app, string? requestPath = default)
    {
        app.UseWhen(context => context.Request.Path == "/favicon.ico",
            applicationBuilder => applicationBuilder.Use(async (context, next) =>
            {
                context.Request.Path = $"{requestPath}{context.Request.Path}";
                await next(context);
            }));

        return app;
    }

    
    public static IApplicationBuilder UseW3CTraceResponse(this IApplicationBuilder app)
    {
        return app.Use((context, next) =>
        {
            var activity = Activity.Current;
            if (activity is not null)
            {
               context.Response.Headers.TryAdd("TraceResponse", activity.Id);
            }
            return next(context);
        });
    }

    
    /// <summary>
    /// 处理找不到终结点的情况
    /// </summary>
    /// <param name="app">app构建者</param>
    /// <returns>app构建者</returns>
    /// <remarks>必须在<see cref="Microsoft.AspNetCore.Builder.EndpointRoutingApplicationBuilderExtensions.UseRouting(Microsoft.AspNetCore.Builder.IApplicationBuilder)"/>后使用</remarks>
    public static IApplicationBuilder UseEndpointNotFound(this IApplicationBuilder app)
    {
        return app.UseWhen(context => context.GetEndpoint() is null, 
            applicationBuilder => applicationBuilder.Run(async context =>
            {
                var services = context.RequestServices;
                var jsonOptions = services.GetRequiredService<IOptions<JsonOptions>>().Value;
                context.Response.ContentType = "application/json; charset=utf-8";
                context.Response.StatusCode = StatusCodes.Status404NotFound;
                await context.Response.WriteAsJsonAsync(new ResultData()
                {
                    Code = StatusCodes.Status404NotFound,
                    Message = "找不到对应的请求终结点",
                    Success = false
                }, jsonOptions.JsonSerializerOptions);
                await context.Response.CompleteAsync();
            }));
    }

    public static IApplicationBuilder UseRequestEnableBuffering(this IApplicationBuilder app)
    {
        return app.Use((context, next) =>
        {
            context.Request.EnableBuffering();
            return next(context);
        });
    }

    public static IApplicationBuilder HandleUnsuccessfulResponse(this IApplicationBuilder app)
    {
        return app.UseStatusCodePages(context =>
        {
            var httpContext = context.HttpContext;
            var response = httpContext.Response;
            var responseContentType = response.ContentType?.Split(';', StringSplitOptions.TrimEntries)[0];
            switch (responseContentType)
            {
                case MediaTypeNames.Application.Json:
                case MediaTypeNames.Application.ProblemJson:
                case "application/grpc":
                    return Task.CompletedTask;
            }

            var responseStatusCode = response.StatusCode;
            var message = responseStatusCode switch
            {
                StatusCodes.Status404NotFound => "找不到对应的请求资源",
                StatusCodes.Status415UnsupportedMediaType => "不支持对应的请求体格式",
                StatusCodes.Status405MethodNotAllowed => "请求方法错误",
                StatusCodes.Status500InternalServerError => "服务器内部错误",
                _ => "未知错误"
            };
            
            var jsonOptions = httpContext.RequestServices.GetRequiredService<IOptions<JsonOptions>>().Value;
            response.ContentType = "application/json; charset=utf-8";
            return response.WriteAsJsonAsync(new ResultData()
            {
                Code = responseStatusCode,
                Success = false,
                Message = message
            }, jsonOptions.JsonSerializerOptions, httpContext.RequestAborted);
        });
    }
}