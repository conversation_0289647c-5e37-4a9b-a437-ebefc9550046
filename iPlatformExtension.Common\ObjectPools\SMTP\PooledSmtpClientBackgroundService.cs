﻿using System.Collections.Concurrent;
using iPlatformExtension.Common.Clients.Mail;
using Microsoft.Extensions.Hosting;

namespace iPlatformExtension.Common.ObjectPools.SMTP;

public sealed class PooledSmtpClientBackgroundService : BackgroundService
{
    private Task? _cleanExpiredClientsTask;

    internal ConcurrentQueue<ScopedPooledSmtpClient> ExpiredClients { get; } = new();

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _cleanExpiredClientsTask = Task.Factory.StartNew(() =>
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                if (ExpiredClients.TryDequeue(out var expiredClient))
                {
                    expiredClient.Accessor = null;
                    expiredClient.Dispose();
                }
                else
                {
                    Thread.Sleep(5000);
                }
            }
        }, TaskCreationOptions.LongRunning);

        return _cleanExpiredClientsTask;
    }

    public override void Dispose()
    {
        while (ExpiredClients.TryDequeue(out var expiredClient))
        {
            expiredClient.Accessor = null;
            expiredClient.Dispose();
        }
    }
}