﻿using iPlatformExtension.Common.Helper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;
using MediatR;
using Microsoft.Extensions.Caching.Memory;
using System;

namespace iPlatformExtension.Public.Applications.Handlers.MyTrademarkCase
{
    /// <summary>
    /// 工作日计算
    /// </summary>
    internal sealed class DateCalculationQueryHandler(IFreeSql freeSql, IMemoryCache memoryCache)
        : IRequestHandler<DateCalculationQuery, CalDateDto>
    {
        /// <summary>
        /// 增加节假日，不判断当天
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<CalDateDto> Handle(DateCalculationQuery request, CancellationToken cancellationToken)
        {
            var (dateTime, day) = request;
            var value = day < 0 ? -1 : 1;
            var holidaySetting = await GetHolidaySetting();
            if (day == 0)
            {
                return new CalDateDto(dateTime);
            }
            for (var i = 0; i < Math.Abs(day); i++)
            {
                do
                {
                    dateTime = dateTime.AddDays(value);
                } while (!DataHelper.IsHoliday(holidaySetting, dateTime));
            }
            return new CalDateDto(dateTime);
        }

        private async Task<Dictionary<DateTime, bool>> GetHolidaySetting()
        {
            if (memoryCache.TryGetValue("Holiday", out Dictionary<DateTime, bool>? data))
            {
                return data ?? new Dictionary<DateTime, bool>();
            }

            var sysHoliday = await freeSql.Select<SysHoliday>().WithLock().ToDictionaryAsync(it => it.Date, it => it.Holiday);
            memoryCache.Set("Holiday", sysHoliday);
            return sysHoliday;
        }
    }
}

