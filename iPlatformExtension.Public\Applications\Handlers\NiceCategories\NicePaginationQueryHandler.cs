﻿using System.Linq.Expressions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models.NiceCategory;
using iPlatformExtension.Public.Applications.Queries.NiceCategory;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.NiceCategories;

internal sealed class NicePaginationQueryHandler(IFreeSql freeSql)
    : IRequestHandler<NicePaginationQuery, IEnumerable<NiceCategoryItemDto>>
{
    public async Task<IEnumerable<NiceCategoryItemDto>> Handle(NicePaginationQuery request, CancellationToken cancellationToken)
    {
        var (grandNumbers, parentNumbers, levels, keywords, versionId, isPrecision, _, _) = request;
        
        var query = freeSql.Select<BasTrademarkItemsLevel, BasTrademarkItems>().WithLock()
            .InnerJoin((level, items) => level.CurId == items.CurId && level.VersionId == items.VersionId)
            .Where((level, items) => level.VersionId == versionId);

        query.Where((level, items) => levels.Contains(level.Level!.Value));

        var keywordFilter =
            keywords.Aggregate<string, Expression<Func<BasTrademarkItemsLevel, BasTrademarkItems, bool>>?>(
                null,
                (current, keyword) => isPrecision
                    ? current.Or((level, items) => items.TextZhCn == keyword)
                    : current.Or((level, items) => items.TextZhCn.Contains(keyword)));
        query.WhereIf(keywordFilter is not null, keywordFilter);

        var grandNumberFilter =
            grandNumbers.Aggregate<string, Expression<Func<BasTrademarkItemsLevel, BasTrademarkItems, bool>>?>(
                null, (current, grandNumber) => current.Or((level, items) => level.FpId == grandNumber));
        query.WhereIf(grandNumberFilter is not null, grandNumberFilter);

        var parentNumberFilter =
            parentNumbers.Aggregate<string, Expression<Func<BasTrademarkItemsLevel, BasTrademarkItems, bool>>?>(
                null, (current, parentNumber) => current.Or((level, items) => level.PId == parentNumber));
        query.WhereIf(parentNumberFilter is not null, parentNumberFilter);

        return await query.OrderBy((level, items) => level.HistoryId).ToPageableResultAsync(request, (level, items) =>
            new NiceCategoryItemDto
            {
                CategoryId = level.CurId,
                CategoryNumber = level.Lid,
                ParentNumber = level.PId,
                GrandNumber = level.FpId,
                Level = level.Level,
                CategoryName = items.TextZhCn
            }, cancellationToken);
    }
}