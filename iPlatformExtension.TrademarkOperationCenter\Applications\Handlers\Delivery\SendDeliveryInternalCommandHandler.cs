﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class SendDeliveryInternalCommandHandler(IMediator mediator, IHostEnvironment hostEnvironment)
    : IRequestHandler<SendDeliveryInternalCommand>
{
    public async Task Handle(SendDeliveryInternalCommand request, CancellationToken cancellationToken)
    {
        var (message, operatorId, operation, messageKey, displayJsonId, displayHistory) = request;

        await mediator.Send(
            new InsertHistoryCommand(message.ProcId, operation, operatorId, string.Empty, displayJsonId, displayHistory),
            cancellationToken);

        var topic = hostEnvironment.IsLocal() || hostEnvironment.IsDevelopment() ? "delivery-dev" : "delivery";
        await mediator.Send(
            new StartupDeliveryCommand(topic, messageKey, message),
            cancellationToken);
    }
}