﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 邮件详情
/// </summary>
/// <param name="MailSubject">主题</param>
/// <param name="MailHtmlBody">邮件正文</param>
/// <param name="MailTo">收件人</param>
/// <param name="MailFrom">发件人</param>
/// <param name="MailDate">收件日期</param>
/// <param name="Status">邮件状态-1:取消,0:待解析,2:待分拣;3:办理中;4:已忽略;5:已办理;500:出错</param>
/// <param name="MailEmlUrl">邮件附件</param>
/// <param name="MailCc">抄送</param>
/// <param name="CreateTime">创建时间</param>
public record GetReceiveDetailDto(string MailSubject, string MailHtmlBody, string? MailTo, string MailFrom, DateTime? MailDate, int? Status, string MailPriority, string MailEmlUrl, string? MailCc, DateTime? CreateTime);
