using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_history_detail", DisableSyncStructure = true)]
	public partial class DeliHistoryDetail {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "err_message", StringLength = 1000)]
		public string ErrMessage { get; set; }

		[ Column(Name = "error_code", StringLength = 50)]
		public string ErrorCode { get; set; }

		[ Column(Name = "finish_time")]
		public DateTime? FinishTime { get; set; }

		[ Column(Name = "history_id", StringLength = 50)]
		public string HistoryId { get; set; }

		[ Column(Name = "start_time")]
		public DateTime? StartTime { get; set; }

		[ Column(Name = "status", StringLength = 50)]
		public string Status { get; set; }

	}

}
