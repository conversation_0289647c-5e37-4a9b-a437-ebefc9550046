﻿

using iPlatformExtension.Common.Attributes;
using System.Reflection.Metadata;
using System.Reflection;
using System.Text.Json;

namespace iPlatformExtension.Common.Helper
{
    /// <summary>
    /// 数据处理
    /// </summary>
    public static class DataHelper
    {
        /// <summary>
        /// 对象克隆
        /// </summary>
        /// <typeparam name="T1">源对象</typeparam>
        /// <typeparam name="T2">克隆对象类型</typeparam>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static T Clone<T>(this Object obj)
        {
            var json = JsonSerializer.Serialize(obj);
            var n = JsonSerializer.Deserialize<T>(json);
            return n;
        }


        /// <summary>
        /// 对象字段赋值
        /// </summary>
        /// <param name="target">目标</param>
        /// <param name="source">数据源</param>
        public static void CopyFields<T, M>(this M source, ref T target)
        {
            Type targetType = target.GetType();
            Type sourceType = source.GetType();

            System.Attribute[] attrs = System.Attribute.GetCustomAttributes(target.GetType());  // Reflection.
            foreach (var mi in sourceType.GetProperties(BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic))
            {
                var des = targetType.GetProperty(mi.Name);
                if (des != null && mi.GetCustomAttribute<CopyIgnoreFieldAttribute>() == null)
                {
                    des.SetValue(target, mi.GetValue(source));
                }
            }
        }

        /// <summary>
        /// 比较数据是否一致
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="obj2"></param>
        /// <remarks>不准确。最好不用</remarks>
        /// <returns></returns>
        public static bool Comparison(this Object obj, Object ComparisonObj)
        {
            var json1 = JsonSerializer.Serialize(obj);
            var json2 = JsonSerializer.Serialize(ComparisonObj);
            if (json1 == json2)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 是否节假日
        /// </summary>
        /// <param name="holidays"></param>
        /// <param name="date"></param>
        /// <returns>true是，false否</returns>
        public static bool IsHoliday(Dictionary<DateTime, bool> holidays, DateTime date)
        {
            if (date < Convert.ToDateTime("2022-12-31"))
            {
                return true;
            }
            if (holidays.TryGetValue(date, out var value))
            {
                return !value;
            }

            return date.DayOfWeek != DayOfWeek.Sunday && date.DayOfWeek != DayOfWeek.Saturday;
        }
    }
}
