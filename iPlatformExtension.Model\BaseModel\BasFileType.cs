using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_file_type", DisableSyncStructure = true)]
	public partial class BasFileType {

		/// <summary>
		/// 文件类型主键ID
		/// </summary>
		[ Column(Name = "file_type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FileTypeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 文件类型编码
		/// </summary>
		[ Column(Name = "file_type_code", StringLength = 50)]
		public string FileTypeCode { get; set; }

		/// <summary>
		/// 文件类型英文名称
		/// </summary>
		[ Column(Name = "file_type_en_us", StringLength = 100)]
		public string FileTypeEnUs { get; set; }

		/// <summary>
		/// 文件类型日文名称
		/// </summary>
		[ Column(Name = "file_type_ja_jp", StringLength = 100)]
		public string FileTypeJaJp { get; set; }

		/// <summary>
		/// 文件类型中文名称
		/// </summary>
		[ Column(Name = "file_type_zh_cn", StringLength = 50)]
		public string FileTypeZhCn { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 排序
		/// </summary>
		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
