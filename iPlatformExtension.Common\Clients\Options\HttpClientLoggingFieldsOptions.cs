namespace iPlatformExtension.Common.Clients.Options;

/// <summary>
/// 请求旧系统接口的日志选项
/// </summary>
public class HttpClientLoggingFieldsOptions
{

    /// <summary>
    /// 是否记录请求体
    /// </summary>
    public bool RequestBody { get; set; }

    /// <summary>
    /// 是否记录请求头部
    /// </summary>
    public bool RequestHeader { get; set; }

    /// <summary>
    /// 是否记录响应体
    /// </summary>
    public bool ResponseBody { get; set; }

    /// <summary>
    /// 是否记录响应头部
    /// </summary>
    public bool ResponseHeader { get; set; }
}