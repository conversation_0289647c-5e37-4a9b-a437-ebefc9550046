﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class ReturnDeliveryValidateCommandHandler(IMediator mediator, IRedisCache<RedisCacheOptionsBase> redisCache) 
    : IRequestHandler<ReturnDeliveryValidateCommand, BatchDeliveryValidateResult>
{
    public async Task<BatchDeliveryValidateResult> Handle(ReturnDeliveryValidateCommand request, CancellationToken cancellationToken)
    {
        var procIds = request.ProcIds;
        
        var deliveryList = await mediator.Send(new BatchDeliveryValidationQuery(procIds), cancellationToken);
        
        var validResult = await mediator.Send(new MultipartCtrlProcValidateCommand(deliveryList), cancellationToken);
        if (!validResult.Success)
        {
            return validResult;
        }
        
        validResult.Success = deliveryList.Where(item => item.CurrentNodeId is not null).GroupBy(item => item.CurrentNodeId).Count() == 1;
        if (!validResult.Success)
        {
            validResult.Message = "请勿选中多个节点的递交代办";
            return validResult;
        }

        validResult = await mediator.Send(new NoFirstFlowNodeValidateCommand(deliveryList, BatchDeliveriesValidateType.Reject), cancellationToken);
        if (!validResult.Success)
        {
            return validResult;
        }
        
        validResult = await mediator.Send(new UndertakerValidateCommand(deliveryList), cancellationToken);
        if (!validResult.Success)
        {
            return validResult;
        }

        validResult = await deliveryList.ToAsyncEnumerable().WhereAwait(async dto =>
        {
            var locked =
                (await redisCache.GetCacheValueAsync<string, long?>(LockKey.DeliveringLockKey, dto.ProcId) ?? 0) >
                DateTimeOffset.Now.ToUnixTimeMilliseconds();

            var deliveryStatus = (DeliveryStatus) dto.DeliveryStatus;
            return locked || (deliveryStatus >= DeliveryStatus.Ordered && (dto.IsAuto ?? false) && dto.OperationResult);
        }).AggregateAsync(validResult, (result, dto) =>
        {
            result.ProcNos.Add(dto.ProcNo);
            result.Success = false;
            result.Message = "以下待办不可退回，请先取消选中";

            return result;
        }, cancellationToken);

        return validResult;
    }
}