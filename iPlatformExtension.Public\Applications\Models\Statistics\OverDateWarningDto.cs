﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Public.Applications.Models.Statistics;

/// <summary>
/// 超期统计
/// </summary>
/// <param name="IsAdvanceCheck">预审</param>
/// <param name="IsPriorityReview">优审</param>
/// <param name="Volume">我方文号</param>
/// <param name="CustomerName">客户名称</param>
/// <param name="CtrlProcId">任务id</param>
/// <param name="CtrlProcProperty">任务属性</param>
/// <param name="ProcStatusId">任务状态</param>
/// <param name="ProcNote">任务备注</param>
/// <param name="UndertakeUserId">承办人</param>
/// <param name="SubProcStatusId">代理人状态</param>
/// <param name="TitularWriteUser">名义承办人</param>
/// <param name="DeptId">承办人部门id</param>
/// <param name="IntFirstDate">初稿期限(内)</param>
/// <param name="CusFirstDate">初稿期限(外)</param>
/// <param name="IntFinishDate">定稿期限(内)</param>
/// <param name="CusFinishDate">定稿期限(外)</param>
/// <param name="LegalDueDate">官方期限</param>
/// <param name="ApplyTypeId">申请类型</param>
/// <param name="BelongCompany">案源分所</param>
/// <param name="ManageCompany">管理分所</param>
public record OverDateWarningDto(bool? IsAdvanceCheck, bool? IsPriorityReview, string? Volume, string? CustomerName, string? CtrlProcId,
    string? CtrlProcProperty, string? ProcStatusId, string? ProcNote, string? UndertakeUserId, string? SubProcStatusId, string? TitularWriteUser, string? DeptId,
    DateTime? IntFirstDate, DateTime? CusFirstDate, DateTime? IntFinishDate, DateTime? CusFinishDate, DateTime? LegalDueDate, string? ApplyTypeId, string? BelongCompany,
    string? ManageCompany)
{
    private readonly string? _volume = Volume;
    private readonly string? _ctrlProcId = CtrlProcId;
    private readonly DateTime? _intFirstDate = IntFirstDate;
    private readonly DateTime? _cusFirstDate = CusFirstDate;
    private readonly DateTime? _intFinishDate = IntFinishDate;
    private readonly DateTime? _cusFinishDate = CusFinishDate;
    private readonly DateTime? _legalDueDate = LegalDueDate;
    private readonly string? _customerName = CustomerName;
    private readonly string? _ctrlProcProperty = CtrlProcProperty;
    private readonly string? _procStatusId = ProcStatusId;
    private readonly string? _procNote = ProcNote;
    private readonly string? _undertakeUserId = UndertakeUserId;
    private readonly string? _subProcStatusId = SubProcStatusId;
    private readonly string? _titularWriteUser = TitularWriteUser;
    private readonly bool? _isAdvanceCheck = IsAdvanceCheck;
    private readonly bool? _isPriorityReview = IsPriorityReview;
    private readonly string? _deptId = DeptId;

    [ExcelColumn(Name = "我方文号"), ExcelColumnIndex("C")]
    public string Volume
    {
        get => _volume;
        init => _volume = value;
    }

    [ExcelColumn(Ignore = true)]
    public string? CtrlProcId
    {
        get => _ctrlProcId;
        init => _ctrlProcId = value;
    }

    [ExcelColumn(Name = "任务名称"), ExcelColumnIndex("E")]
    public string? CtrlProcZhCn { get; set; }

    [ExcelColumn(Name = "初稿期限(内)", Ignore = true)]
    public DateTime? IntFirstDate
    {
        get => _intFirstDate;
        init => _intFirstDate = value;
    }

    [ExcelColumn(Name = "初稿期限(外)", Ignore = true)]

    public DateTime? CusFirstDate
    {
        get => _cusFirstDate;
        init => _cusFirstDate = value;
    }

    [ExcelColumn(Name = "定稿期限(内)", Ignore = true)]

    public DateTime? IntFinishDate
    {
        get => _intFinishDate;
        init => _intFinishDate = value;
    }

    [ExcelColumn(Name = "定稿期限(外)", Ignore = true)]
    public DateTime? CusFinishDate
    {
        get => _cusFinishDate;
        init => _cusFinishDate = value;
    }

    [ExcelColumn(Name = "官方期限", Ignore = true)]
    public DateTime? LegalDueDate
    {
        get => _legalDueDate;
        init => _legalDueDate = value;
    }

    /// <summary>
    /// 预审
    /// </summary>
    [ExcelColumn(Name = "预审"), ExcelColumnIndex("A")]
    public string? IsAdvanceCheckValue => IsAdvanceCheck switch
    {
        true => "是",
        false => "否",
        _ => ""
    };


    /// <summary>
    /// 优审
    /// </summary>
    [ExcelColumn(Name = "优审"), ExcelColumnIndex("B")]
    public string? IsPriorityReviewValue => IsPriorityReview switch
    {
        true => "是",
        false => "否",
        _ => ""
    };

    [ExcelColumn(Name = "客户名称"), ExcelColumnIndex("D")]
    public string? CustomerName
    {
        get => _customerName;
        init => _customerName = value;
    }

    [ExcelColumn(Ignore = true)]
    public string? CtrlProcProperty
    {
        get => _ctrlProcProperty;
        init => _ctrlProcProperty = value;
    }

    [ExcelColumn(Name = "任务属性"), ExcelColumnIndex("F")]
    public string? CtrlProcPropertyValue { get; set; }

    [ExcelColumn(Ignore = true)]
    public string? ProcStatusId
    {
        get => _procStatusId;
        init => _procStatusId = value;
    }

    [ExcelColumn(Name = "任务状态"), ExcelColumnIndex("G")]
    public string? ProcStatusName { get; set; }

    [ExcelColumn(Name = "任务备注"), ExcelColumnIndex("H")]
    public string? ProcNote
    {
        get => _procNote;
        init => _procNote = value;
    }

    [ExcelColumn(Ignore = true)]
    public string? UndertakeUserId
    {
        get => _undertakeUserId;
        init => _undertakeUserId = value;
    }

    /// <summary>
    /// 账户
    /// </summary>
    [ExcelColumn(Ignore = true)]
    public string? UserAccount { get; set; }

    [ExcelColumn(Name = "承办人"), ExcelColumnIndex("I")]
    public string? UndertakeUserName { get; set; }

    [ExcelColumn(Ignore = true)]
    public string? SubProcStatusId
    {
        get => _subProcStatusId;
        init => _subProcStatusId = value;
    }

    [ExcelColumn(Name = "代理人处理状态"), ExcelColumnIndex("J")]
    public string? SubProcStatusValue { get; set; }

    [ExcelColumn(Ignore = true)]
    public string? TitularWriteUser
    {
        get => _titularWriteUser;
        init => _titularWriteUser = value;
    }

    [ExcelColumn(Name = "名义承办人"), ExcelColumnIndex("K")]
    public string? TitularWriteUserName { get; set; }

    [ExcelColumn(Name = "申请类型")]
    public string? ApplyTypeName { get; set; }

    [ExcelColumn(Ignore = true)]
    public bool? IsAdvanceCheck
    {
        get => _isAdvanceCheck;
        init => _isAdvanceCheck = value;
    }

    [ExcelColumn(Ignore = true)]
    public bool? IsPriorityReview
    {
        get => _isPriorityReview;
        init => _isPriorityReview = value;
    }

    public string? DeptId
    {
        get => _deptId;
        init => _deptId = value;
    }
    /// <summary>
    /// 案源分所
    /// </summary>
    public string? BelongCompany { get; private set; } = BelongCompany;

    /// <summary>
    /// 管理分所
    /// </summary>
    public string? ManageCompany { get; private set; } = ManageCompany;

    [ExcelColumn(Name = "申请类型")]
    public string? ApplyTypeValue { get; set; }

    [ExcelColumn(Name = "案源分所")]
    public string? BelongCompanyValue { get; set; }

    [ExcelColumn(Name = "管理分所")]
    public string? ManageCompanyValue { get; set; }

    public string? DeptName { get; internal set; }
}

