using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "file_sync", DisableSyncStructure = true)]
	public partial class FileSync {

		[ Column(Name = "code", StringLength = 50)]
		public string Code { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "file_name", StringLength = 200)]
		public string FileName { get; set; }

		[ Column(Name = "file_no")]
		public int? FileNo { get; set; }

		[ Column(Name = "id", StringLength = 50, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "input_time")]
		public DateTime? InputTime { get; set; }

		[ Column(Name = "priority")]
		public int? Priority { get; set; } = 0;

		[ Column(Name = "server_path", StringLength = 200)]
		public string ServerPath { get; set; }

		[ Column(Name = "status")]
		public bool? Status { get; set; }

	}

}
