using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 案件列表项
/// </summary>
public class CaseItemDto
{
    /// <summary>
    /// 我方文号
    /// </summary>
    [JsonPropertyName("caseCode")]
    public string Volume { get; set; } = null!;

    /// <summary>
    /// 官方状态
    /// </summary>
    [JsonPropertyName("caseOfficialStatus")]
    public string OfficialStatusId { get; set; } = null!;

    /// <summary>
    /// 申请日期
    /// </summary>
    [JsonPropertyName("appDate")]
    public DateTime? AppDate { get; set; }

    /// <summary>
    /// 申请号
    /// </summary>
    [JsonPropertyName("appNo")]
    public string? AppNo { get; set; }

    /// <summary>
    /// 案件id
    /// </summary>
    [JsonIgnore]
    public string CaseId { get; set; } = null!;

    /// <summary>
    /// 案件名称
    /// </summary>
    [JsonPropertyName("caseName")]
    public string CaseName { get; init; } = null!;

    /// <summary>
    /// 地区
    /// </summary>
    [JsonPropertyName("country")]
    public string Country { get; set; } = null!;

    /// <summary>
    /// 地区名称
    /// </summary>
    [JsonPropertyName("countryName")]
    public string CountryName { get; set; } = null!;

    /// <summary>
    /// das码
    /// </summary>
    [JsonPropertyName("dasCode")]
    public string? DasCode { get; init; }

    /// <summary>
    /// 新申请递交日
    /// </summary>
    [JsonPropertyName("applySubmitDate")]
    public DateTime? DeliverDate { get; set; }

    /// <summary>
    /// 申请类型
    /// </summary>
    [JsonPropertyName("applyType")]
    public string ApplyType { get; set; } = null!;

    /// <summary>
    /// 是否1+1套案
    /// </summary>
    [JsonPropertyName("setCase")]
    public bool SetCase { get; set; }
}