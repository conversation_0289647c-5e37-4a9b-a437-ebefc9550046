﻿using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 删除客户命令
    /// </summary>
    public class DeleteCustomerCommandHandler : IRequestHandler<DeleteCustomerCommand>
    {
        private readonly IFreeSql _freeSql;
        private readonly ISysTeamCustomerRepository _sysTeamCustomerRepository;

        public DeleteCustomerCommandHandler(IFreeSql freeSql, ISysTeamCustomerRepository sysTeamCustomerRepository)
        {
            _freeSql = freeSql;
            _sysTeamCustomerRepository = sysTeamCustomerRepository;
        }

        public async Task Handle(DeleteCustomerCommand request, CancellationToken cancellationToken)
        {
            await _sysTeamCustomerRepository.DeleteAsync(it => request.SysTeamCustomerId.Contains(it.SysTeamCustomerId), cancellationToken);
        }
    }
}

