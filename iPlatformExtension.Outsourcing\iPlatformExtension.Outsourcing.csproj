<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <LangVersion>preview</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Grpc.AspNetCore" Version="2.67.0" />
        <PackageReference Include="KafkaFlow.Extensions.Hosting" Version="3.1.0" />
        <PackageReference Include="KafkaFlow.LogHandler.Microsoft" Version="3.1.0" />
        <PackageReference Include="KafkaFlow.Serializer.JsonCore" Version="3.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
        <PackageReference Include="nacos-sdk-csharp.AspNetCore" Version="1.3.9" />
        <PackageReference Include="nacos-sdk-csharp.Extensions.Configuration" Version="1.3.9" />
        <PackageReference Include="NLog.DiagnosticSource" Version="5.2.1" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.15" />
        <PackageReference Include="Scalar.AspNetCore" Version="2.2.1" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\iPlatformExtension.Repository\iPlatformExtension.Repository.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>
    
    <ItemGroup>
        <Protobuf Include="..\Protos\common_messages.proto" GrpcServices="Service" Link="Protos\common_messages.proto" />
        <Protobuf Include="..\Protos\outsourcing.proto" GrpcServices="Service" Link="Protos\outsourcing.proto" />
        <Protobuf Include="..\Protos\outsourcing_messages.proto" GrpcServices="Service" Link="Protos\outsourcing_messages.proto" />
    </ItemGroup>
    
    <ItemGroup>
        <InternalsVisibleTo Include="iPlatformExtension.Outsourcing.Tests" />
    </ItemGroup>

</Project>
