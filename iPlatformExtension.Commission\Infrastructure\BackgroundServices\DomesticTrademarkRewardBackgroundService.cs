﻿using System.Threading.Channels;
using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;

namespace iPlatformExtension.Commission.Infrastructure.BackgroundServices;

internal sealed class DomesticTrademarkRewardBackgroundService(
    Channel<CreateRewardsCommand> channel, 
    ILogger<DomesticTrademarkRewardBackgroundService> logger, 
    IServiceScopeFactory serviceScopeFactory) 
    : BackgroundConsumeService<CreateRewardsCommand>(channel, logger, serviceScopeFactory);