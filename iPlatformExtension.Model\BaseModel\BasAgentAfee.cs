using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_agent_afee", DisableSyncStructure = true)]
	public partial class BasAgentAfee {

		[ Column(Name = "agent_afee_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string AgentAfeeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "case_type_id", StringLength = 50)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "district", StringLength = 1000)]
		public string District { get; set; }

		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		[ Column(Name = "fee_id", StringLength = 50)]
		public string FeeId { get; set; }

		[ Column(Name = "fee_sum")]
		public int? FeeSum { get; set; }

		[ Column(Name = "is_auto")]
		public bool IsAuto { get; set; } = true;

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "is_repeat")]
		public bool IsRepeat { get; set; } = true;

		[ Column(Name = "pct_enter", StringLength = 50, IsNullable = false)]
		public string PctEnter { get; set; } = "0";

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
