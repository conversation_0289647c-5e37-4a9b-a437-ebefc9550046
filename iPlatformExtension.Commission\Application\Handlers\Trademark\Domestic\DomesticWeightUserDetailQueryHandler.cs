﻿using FreeSql;
using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Commission.Application.Queries.Trademark.Domestic;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class DomesticWeightUserDetailQueryHandler(IFreeSql freeSql) 
    : IRequestHandler<DomesticWeightUserDetailQuery, IEnumerable<UserCommissionWeightProcDetail>>
{
    public async Task<IEnumerable<UserCommissionWeightProcDetail>> Handle(DomesticWeightUserDetailQuery request, CancellationToken cancellationToken)
    {
        var (userId, month, year, keyword) = request;

        return await freeSql.Select<DomesticTrademarkCommission>().WithLock()
            .Where(commission => month == commission.Month && year == commission.Year)
            .Where(commission => commission.ProcMainUndertakerId == userId)
            .WhereIf(!string.IsNullOrWhiteSpace(keyword), commission => commission.Volume.Contains(keyword!) || commission.ProcNo.Contains(keyword!))
            .ToListAsync(commission => new UserCommissionWeightProcDetail
            {
                ProcId = commission.ProcId,
                Volume = commission.Volume,
                RegisterNo = commission.RegisterNo ?? string.Empty,
                AppNo = commission.AppNo,
                ProcNo = commission.ProcNo,
                CaseName = commission.CaseName,
                CtrlProcName = commission.CtrlProcZhCn,
                ProcPoint = commission.EditedProcPoint ?? commission.ProcPoint,
                CustomerName = commission.CustomerName,
                BigClient = commission.BigClient,
                ProcStatus = commission.Status,
                ProcMark = commission.CtrlProcMarkCn,
                UndertakerName = commission.CnName,
                TrademarkClasses = commission.TrademarkClasses,
                CommissionDate = new DateOnly(commission.CommissionDate.Year, commission.CommissionDate.Month, commission.CommissionDate.Day),
                PushedStatus = commission.Pushed == true ? "已推送" : "未推送"
            }, cancellationToken);
    }
}