using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_process_user", DisableSyncStructure = true)]
	public partial class MailProcessUser {

		[ Column(Name = "input_time")]
		public DateTime? InputTime { get; set; }

		[ Column(Name = "mail_id", StringLength = 50)]
		public string MailId { get; set; }

		[ Column(Name = "process_type", StringLength = 50)]
		public string ProcessType { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
