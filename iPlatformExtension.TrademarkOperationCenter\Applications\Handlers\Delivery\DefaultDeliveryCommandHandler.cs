﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DefaultDeliveryCommandHandler(
    IMediator mediator, 
    IRedisCache<RedisCacheOptionsBase> redisCache, 
    IDeliveryInfoRepository deliveryInfoRepository) : 
    DeliveryCommandHandlerBase(mediator, redisCache, deliveryInfoRepository)
{
    public override Task<SendDeliveryInternalCommand?> HandleSendStartupCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        return Task.FromResult(default(SendDeliveryInternalCommand));
    }

    public override Task<SendDeliveryInternalCommand?> HandleSendCancelCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        return Task.FromResult(default(SendDeliveryInternalCommand));
    }

    public override Task<SendDeliveryInternalCommand?> HandleSendStopCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        return Task.FromResult(default(SendDeliveryInternalCommand));
    }

    public override Task<SendDeliveryInternalCommand?> HandleSendWithdrawCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        return Task.FromResult(default(SendDeliveryInternalCommand));
    }
}