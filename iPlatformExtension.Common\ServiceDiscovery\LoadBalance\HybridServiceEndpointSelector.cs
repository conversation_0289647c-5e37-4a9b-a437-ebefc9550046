using Microsoft.Extensions.ServiceDiscovery;

namespace iPlatformExtension.Common.ServiceDiscovery.LoadBalance;

internal sealed class HybridServiceEndpointSelector(IEnumerable<IMatchServiceEndpointSelector> selectors) 
    : IServiceEndpointSelector
{
    /// <inheritdoc />
    public void SetEndpoints(ServiceEndpointSource endpoints)
    {
        foreach (var endpointSelector in selectors)
        {
            endpointSelector.SetEndpoints(endpoints);
        }
    }

    /// <inheritdoc />
    public ServiceEndpoint GetEndpoint(object? context)
    {
        return selectors.First(selector => selector.Match(context)).GetEndpoint(context);
    }
}