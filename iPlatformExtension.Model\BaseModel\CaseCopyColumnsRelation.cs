using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_copy_columns_relation", DisableSyncStructure = true)]
	public partial class CaseCopyColumnsRelation {

		[ Column(Name = "columns_id", StringLength = 50)]
		public string ColumnsId { get; set; }

		[ Column(Name = "related_id", StringLength = 50, IsNullable = false)]
		public string RelatedId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "rule_id", StringLength = 50)]
		public string RuleId { get; set; }

	}

}
