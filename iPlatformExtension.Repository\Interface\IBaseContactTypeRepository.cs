﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.Customer;

namespace iPlatformExtension.Repository.Interface;

public interface IBaseContactTypeRepository : IBaseRepository<BasContactType, string>,
    IRedisCacheableRepository<ContactTypeCodeKey, BasContactType>,
    IRedisCacheableRepository<ContactTypeIdKey, BasContactType>,
    IScopeDependency
{
    string ICacheableRepository<ContactTypeCodeKey, BasContactType>.CacheKey => nameof(ContactTypeCodeKey);

    string ICacheableRepository<ContactTypeIdKey, BasContactType>.CacheKey => nameof(ContactTypeIdKey);

    Task<BasContactType?> ICacheableRepository<ContactTypeIdKey, BasContactType>.GetValueFromDbAsync(ContactTypeIdKey key, CancellationToken cancellationToken)
    {
        return Where(type => type.ContactTypeId == key.ContactId).WithLock().ToOneAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasContactType>> ICacheableRepository<ContactTypeIdKey, BasContactType>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().ToListAsync(cancellationToken);
    }

    Task<BasContactType?> ICacheableRepository<ContactTypeCodeKey, BasContactType>.GetValueFromDbAsync(ContactTypeCodeKey key, CancellationToken cancellationToken)
    {
        return Where(type => type.ContactTypeId == key.ContactCode).WithLock().ToOneAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasContactType>> ICacheableRepository<ContactTypeCodeKey, BasContactType>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().ToListAsync(cancellationToken);
    }

    ContactTypeCodeKey ICacheableRepository<ContactTypeCodeKey, BasContactType>.GenerateKey(BasContactType value)
    {
        return new ContactTypeCodeKey(value.ContactTypeCode);
    }

    ContactTypeIdKey ICacheableRepository<ContactTypeIdKey, BasContactType>.GenerateKey(BasContactType value)
    {
        return new ContactTypeIdKey(value.ContactTypeId);
    }
}