﻿{
    "nacos": {
        "ServerAddresses": [ "************:18848" ],
        "DefaultTimeOut": 15000,
        "Namespace": "uat",
        "ListenInterval": 1000,
        "ServiceName": "iPlatformExtension.MailCenter",
        "UserName": "nacos",
        "Password": "Acip1234",
        "Ip": "************",
        "Port": 8082,
        "Listeners": [
            {
                "Optional": true,
                "DataId": "appsettings.json",
                "Group": "iPlatformExtension"
            },
            {
                "Optional": true,
                "DataId": "clients.json",
                "Group": "iPlatformExtension.MailCenter"
            },
            {
                "Optional": true,
                "DataId": "mqs.json",
                "Group": "iPlatformExtension.MailCenter"
            }
        ]
    },
    "IPlatformAuth": {
        "Issuers": [
            "ipr.aciplaw.com"
        ],
        "Audiences": [
            "patas.aciplaw.com",
            "patas-test.aciplaw.com",
            "patas-dev.aciplaw.com"
        ],
        "Metadata": {
            "ASPNETCORE_HTTPS_PORTS": "8081"
        }
    },
    "NLog": {
        "autoReload": true,
        "throwConfigExceptions": true,
        "internalLogLevel": "Debug",
        "targets": {
            "async": true,
            "infoLog": {
                "type": "File",
                "encoding": "utf-8",
                "maxArchiveFiles": 7,
                "fileName": "${basedir}/logs/${shortdate}-test.log",
                "layout": "[${longdate}] [${uppercase:${level}}] [${logger}] [${activity:property=TraceId}] [${aspnet-request-url}] [${aspnet-mvc-action}] : ${newline}${message} ${newline}\n                ${exception:format=ToString} "
            },
            "errorLog": {
                "type": "File",
                "encoding": "utf-8",
                "maxArchiveFiles": 7,
                "fileName": "${basedir}/logs/${shortdate}-test-error.log",
                "layout": "[${longdate}] [${uppercase:${level}}] [${logger}] [${activity:property=TraceId}] [${aspnet-request-url}] [${aspnet-mvc-action}] : ${newline}${message} ${newline}\n                ${exception:format=ToString} "
            }
        },
        "rules": [
            {
                "logger": "*",
                "minLevel": "Info",
                "writeTo": "infoLog"
            },
            {
                "logger": "*",
                "minLevel": "Error",
                "writeTo": "errorLog"
            }
        ]
    }
}
