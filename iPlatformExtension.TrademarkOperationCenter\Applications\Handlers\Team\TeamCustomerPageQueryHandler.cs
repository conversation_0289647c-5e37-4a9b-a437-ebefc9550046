﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;
using System.Xml.Linq;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 团队客户分页查询处理者
    /// </summary>
    public class TeamCustomerPageQueryHandler : IRequestHandler<TeamCustomerPageQuery, IEnumerable<TeamCustomerPageDto>>
    {
        private readonly IFreeSql _freeSql;

        public TeamCustomerPageQueryHandler(IFreeSql freeSql)
        {
            _freeSql = freeSql;
        }

        public async Task<IEnumerable<TeamCustomerPageDto>> Handle(TeamCustomerPageQuery request, CancellationToken cancellationToken)
        {
            //获取当前已配置所有客户
            var allCustomer = _freeSql.Select<SysTeamCustomer>().ToList(it => it.CustomerId);

            //获取当前团队客户
            var dbSelect = _freeSql.Select<CusCustomer, SysUserInfo>()
                .LeftJoin((c, u) => c.BusiUserId == u.UserId)
                .WhereIf(allCustomer.Any(), (c, u) => !allCustomer.Contains(c.CustomerId))
                .WhereIf(request.ExcludeCustomerId != null && request.ExcludeCustomerId.Count > 0 && !request.ExcludeCustomerId.Contains(null), (c, u) => !request.ExcludeCustomerId.Contains(c.CustomerId))
                .WhereIf(request.Name != null, (c, u) => c.CustomerName.Contains(request.Name))
                .Where((c, u) => c.IsEnabled == true);

            if (request.PageIndex is not null && request.PageSize is not null)
            {
                dbSelect = dbSelect.Page(request.PageIndex.Value, request.PageSize.Value).Count(out var totalCount);
                var result = await dbSelect.WithLock().OrderBy(it=>it.t1.CustomerId).ToListAsync((c, u) => new TeamCustomerPageDto(c.CustomerId, c.CustomerName, c.BusiUserId, u.CnName), cancellationToken);
                return new PageResult<TeamCustomerPageDto>()
                {
                    Data = result,
                    Page = request.PageIndex.Value,
                    PageSize = request.PageSize.Value,
                    Total = totalCount
                };
            }
            return await dbSelect.WithLock().ToListAsync((c, u) => new TeamCustomerPageDto(c.CustomerId, c.CustomerName, c.BusiUserId, u.CnName), cancellationToken);
        }
    }
}

