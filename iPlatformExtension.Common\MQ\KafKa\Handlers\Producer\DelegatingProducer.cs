﻿using Confluent.Kafka;

namespace iPlatformExtension.Common.MQ.KafKa.Handlers.Producer;

public abstract class DelegatingProducer<TKey, TMessage> : IProducer<TKey, TMessage>
{
    private IProducer<TKey, TMessage>? _innerProducer;

    public IProducer<TKey, TMessage> InnerProducer
    {
        get => _innerProducer ?? throw new NullReferenceException("生产者初始化失败");
        internal set => _innerProducer = value ?? throw new InvalidOperationException("生产者为空引用");
    }

    public bool DisposeProducer { get; set; }
    
    public virtual void Dispose()
    {
        if (DisposeProducer)
        {
            InnerProducer.Dispose();
        }
    }
 
    public virtual int AddBrokers(string brokers)
    {
        return InnerProducer.AddBrokers(brokers);
    }

    public virtual void SetSaslCredentials(string username, string password)
    {
        InnerProducer.SetSaslCredentials(username, password);
    }

    public virtual Handle Handle => InnerProducer.Handle;

    public virtual string Name => InnerProducer.Name;
    
    public virtual Task<DeliveryResult<TKey, TMessage>> ProduceAsync(string topic, Message<TKey, TMessage> message, CancellationToken cancellationToken = new CancellationToken())
    {
        return InnerProducer.ProduceAsync(topic, message, cancellationToken);
    }

    public virtual Task<DeliveryResult<TKey, TMessage>> ProduceAsync(TopicPartition topicPartition, Message<TKey, TMessage> message,
        CancellationToken cancellationToken = new CancellationToken())
    {
        return InnerProducer.ProduceAsync(topicPartition, message, cancellationToken);
    }

    public virtual void Produce(string topic, Message<TKey, TMessage> message, Action<DeliveryReport<TKey, TMessage>>? deliveryHandler = null)
    {
        InnerProducer.Produce(topic, message, deliveryHandler);
    }

    public virtual void Produce(TopicPartition topicPartition, Message<TKey, TMessage> message, Action<DeliveryReport<TKey, TMessage>>? deliveryHandler = null)
    {
        InnerProducer.Produce(topicPartition, message, deliveryHandler);
    }

    public virtual int Poll(TimeSpan timeout)
    {
        return InnerProducer.Poll(timeout);
    }

    public virtual int Flush(TimeSpan timeout)
    {
        return InnerProducer.Flush(timeout);
    }

    public virtual void Flush(CancellationToken cancellationToken = new CancellationToken())
    {
        InnerProducer.Flush(cancellationToken);
    }

    public virtual void InitTransactions(TimeSpan timeout)
    {
        InnerProducer.InitTransactions(timeout);
    }

    public virtual void BeginTransaction()
    {
        InnerProducer.BeginTransaction();
    }

    public virtual void CommitTransaction(TimeSpan timeout)
    {
        InnerProducer.CommitTransaction(timeout);
    }

    public virtual void CommitTransaction()
    {
        InnerProducer.CommitTransaction();
    }

    public virtual void AbortTransaction(TimeSpan timeout)
    {
        InnerProducer.AbortTransaction(timeout);
    }

    public virtual void AbortTransaction()
    {
        InnerProducer.AbortTransaction();
    }

    public virtual void SendOffsetsToTransaction(IEnumerable<TopicPartitionOffset> offsets, IConsumerGroupMetadata groupMetadata, TimeSpan timeout)
    {
        InnerProducer.SendOffsetsToTransaction(offsets, groupMetadata, timeout);
    }
}