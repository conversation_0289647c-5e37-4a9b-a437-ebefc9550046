﻿using System.Text;
using FreeSql.Aop;
using iPlatformExtension.Common.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Db.FreeSQL.Handlers.Curd;

internal sealed class DefaultCurdLoggingHandler(
    ILoggerFactory loggerFactory,
    IHttpContextAccessor httpContextAccessor,
    IOptionsMonitor<CurdLoggingOptions> optionsMonitor, 
    ObjectPool<StringBuilder> stringBuilderPool,
    IHostEnvironment hostEnvironment) : ICurdLoggingHandler
{
    internal const string OptionsName = nameof(DefaultCurdLoggingHandler);
    
    public void LogExecutingCurd(object? sender, CurdBeforeEventArgs args)
    {
        var options = optionsMonitor.Get(OptionsName);

        if (!options.LogCurdBefore || !IsEnable(options, args)) return;
        
        var logger = loggerFactory.CreateLogger(GetLoggerCategoryName(httpContextAccessor.HttpContext));
        LogSql(logger, args, LogLevel.Debug);
    }

    public void LogExecutedCurd(object? sender, CurdAfterEventArgs args)
    {
        var options = optionsMonitor.Get(OptionsName);
        var logger = loggerFactory.CreateLogger(GetLoggerCategoryName(httpContextAccessor.HttpContext));
        
        if (args.ElapsedMilliseconds > options.ExecuteDurationThreshold)
        {
            LogSql(logger, args, LogLevel.Warning);
        }
        else if (options.LogCurdAfter && IsEnable(options, args))
        {
            LogSql(logger, args, LogLevel.Information);
        }
        
    }

    private bool IsEnable(CurdLoggingOptions options, CurdBeforeEventArgs args)
    {
        
        if (!options.Environments.Any(hostEnvironment.IsEnvironment))
        {
            return false;
        }

        if (options.ExcludeEntityTypes.Contains(args.EntityType.Name))
        {
            return false;
        }

        if (!options.IncludeCurdTypes.Contains(args.CurdType))
        {
            return false;
        }

        return true;
    }

    private static string GetLoggerCategoryName(HttpContext? httpContext)
    {
        return  httpContext?.Features.Get<IEndpointFeature>()?.Endpoint?.DisplayName ??
                           nameof(DefaultCurdLoggingHandler);
    }

    private void LogSql(ILogger logger, CurdBeforeEventArgs args, LogLevel logLevel)
    {
        var stringBuilder = stringBuilderPool.Get();
        stringBuilder.AppendLine(args.Sql);

        args.DbParms?.Where(parameter => parameter is not null).Aggregate(stringBuilder,
            (builder, parameter) => builder.Append(parameter.ParameterName).Append('=').Append(parameter.Value).Append('\n'));

        if (args is CurdAfterEventArgs afterEventArgs)
        {
            stringBuilder.Append("耗时：").Append(afterEventArgs.ElapsedMilliseconds).AppendLine("ms");
        }
        
        logger.LogSql(logLevel, stringBuilder.ToString());
        
        stringBuilderPool.Return(stringBuilder);
    }
}