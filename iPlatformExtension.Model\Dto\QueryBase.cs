﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto
{
    /// <summary>
    /// 入参基类
    /// </summary>
    public class QueryBase
    {
        /// <summary>
        /// 当前页码(默认第1页)
        /// </summary>
        public int PageIndex { get; init; } = 1;

        /// <summary>
        /// 页面大小(默认每页10个)
        /// </summary>
        public int PageSize { get; init; } = 10;

        /// <summary>
        /// 数据总数
        /// </summary>
       // public long Total { get; init; }
    }
}
