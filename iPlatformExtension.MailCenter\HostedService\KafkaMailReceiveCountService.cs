﻿using iPlatformExtension.MailCenter.Applications.Models.Count;
using iPlatformExtension.MailCenter.Applications.Queries.Count;
using iPlatformExtension.Model.MailCenter;
using KafkaProvider;
using MediatR;

namespace iPlatformExtension.MailCenter.HostedService
{
    public class KafkaMailReceiveCountService(IKafkaService kafkaService, IConfiguration configuration, IHostEnvironment environment, IMediator mediator) : BackgroundService
    {
        /// <inheritdoc />
        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            kafkaService.GetConsumerConfig(configuration.GetSection("KafKa:MailCenterConsumer:BootstrapServers").Value 
                                           ?? throw new ArgumentNullException("kafka连接字符串为空"), nameof(KafkaMailReceiveCountService) + environment.EnvironmentName);
            kafkaService.SubscribeAsync([configuration.GetSection("KafKa:MailCenterConsumer:MailReceive").Value ?? throw new ArgumentNullException("kafka连接字符串为空")], (CdcModel<MailReceive> c) => mediator.Send(new KafkaMailReceiveCountQuery(c), stoppingToken).GetAwaiter().GetResult(),
                stoppingToken);
            return Task.CompletedTask;
        }
    }
}
