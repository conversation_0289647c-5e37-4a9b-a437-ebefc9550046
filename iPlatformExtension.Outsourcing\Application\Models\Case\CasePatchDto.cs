﻿namespace iPlatformExtension.Outsourcing.Application.Models.Case;

public sealed class CasePatchDto
{
    /// <summary>
    /// 境外代理id
    /// </summary>
    public string? ForeignAgencyId { get; set; }

    /// <summary>
    /// 外所文号
    /// </summary>
    public string? ForeignCaseNo { get; set; }
    
    /// <summary>
    /// 选所备注
    /// </summary>
    public string ForeignSupplierRemark { get; set; } = string.Empty;

    /// <summary>
    /// 联系人id集合
    /// </summary>
    public ISet<string>? ContactorIds { get; set; }
}