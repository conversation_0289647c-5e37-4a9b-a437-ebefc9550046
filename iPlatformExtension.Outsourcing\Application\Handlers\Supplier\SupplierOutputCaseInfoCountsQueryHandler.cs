﻿using System.Runtime.CompilerServices;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;
using iPlatformExtension.Outsourcing.Application.Queries.Supplier;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Supplier;

internal sealed class SupplierOutputCaseInfoCountsQueryHandler(IFreeSql<PlatformFreeSql> freeSql, ISender sender)
    : IStreamRequestHandler<SupplierOutputCaseInfoCountsQuery, SupplierCaseCountDto>
{
    public async IAsyncEnumerable<SupplierCaseCountDto> Handle(SupplierOutputCaseInfoCountsQuery request, [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        request.Deconstruct(out var manageCompanyId, out var caseDirection, out var ctrlProcId, out var applicationTypeId, out var dateRange, out var supplierIds);
        
        foreach (var supplierId in supplierIds)
        {
            var query = new SupplierOutputCaseInfoCountQuery(manageCompanyId, caseDirection, ctrlProcId,
                applicationTypeId, dateRange, supplierId);
            
            yield return new SupplierCaseCountDto()
            {
                SupplierId = supplierId,
                Count = await sender.Send(query, cancellationToken)
            };
        }
    }
}