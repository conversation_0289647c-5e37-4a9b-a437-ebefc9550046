﻿using FreeSql.Internal.Model;
using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Commission.Application.Queries.Trademark.Domestic;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class DomesticWeightDetailQueryHandler(
    IFreeSql freeSql, 
    IHttpContextAccessor httpContextAccessor) 
    : IRequestHandler<DomesticWeightDetailQuery, IEnumerable<ProcWeightDetailExport>>
{
    public async Task<IEnumerable<ProcWeightDetailExport>> Handle(DomesticWeightDetailQuery request, CancellationToken cancellationToken)
    {
        var userId = (httpContextAccessor.HttpContext?.User).GetUserId();
        var ((startDate, endDate), deptIds, districtCode, keyword, pageIndex, pageSize) = request;
        var total = request.Total;

        var dateRangeFilter = new DynamicFilterInfo()
        {
            Field = nameof(DomesticTrademarkCommission.CommissionDate),
            Logic = DynamicFilterLogic.Or,
            Operator = DynamicFilterOperator.DateRange,
            Value = new[] {startDate?.ToString("yyyy-MM-dd"), endDate?.ToString("yyyy-MM-dd")},
            Filters =
            [
                new DynamicFilterInfo()
                {
                    Field = nameof(DomesticTrademarkCommission.ProcMainUndertakerId),
                    Operator = DynamicFilterOperator.Equals,
                    Value = userId
                }
            ]
        };

        if (deptIds.Any())
        {
            dateRangeFilter.Filters.Add(new DynamicFilterInfo()
            {
                Field = nameof(DomesticTrademarkCommission.DeptId),
                Operator = DynamicFilterOperator.Any,
                Value = deptIds
            });
        }

        var query = freeSql.Select<DomesticTrademarkCommission>().WithLock()
            .WhereDynamicFilter(dateRangeFilter)
            .WhereIf(!string.IsNullOrEmpty(keyword),
                commission => commission.CnName.Contains(keyword!) || commission.UserName.Contains(keyword!))
            .WhereIf(!string.IsNullOrWhiteSpace(districtCode), commission => commission.DistrictCode == districtCode);

        var pagingQuery = freeSql.Select<DomesticTrademarkCommission>().WithLock()
            .WhereDynamicFilter(dateRangeFilter)
            .WhereIf(!string.IsNullOrEmpty(keyword),
                commission => commission.CnName.Contains(keyword!) || commission.UserName.Contains(keyword!))
            .WhereIf(!string.IsNullOrWhiteSpace(districtCode), commission => commission.DistrictCode == districtCode);

        var totalTask = total is null ? query.CountAsync(cancellationToken) : Task.FromResult(total.Value);

        var results = await pagingQuery.OrderBy(commission => commission.CommissionDate)
            .OrderBy(commission => commission.ProcId)
            .Page(pageIndex, pageSize)
            .ToListAsync(commission => new ProcWeightDetailExport
            {
                ProcId = commission.ProcId,
                SerialNumber = 0,
                Volume = commission.Volume,
                AppNo = commission.AppNo,
                ProcNo = commission.ProcNo,
                CaseName = commission.CaseName,
                CtrlProcName = commission.CtrlProcZhCn,
                ProcPoint = commission.EditedProcPoint ?? commission.ProcPoint,
                CustomerName = commission.CustomerName,
                BigClient = commission.BigClient,
                ProcStatus = commission.Status,
                ProcMark = commission.CtrlProcMarkCn,
                UndertakerName = commission.CnName,
                CommissionDate = new DateOnly(commission.CommissionDate.Year, commission.CommissionDate.Month,
                    commission.CommissionDate.Day),
                AgentFees = commission.AgentFees,
                TrademarkClasses = commission.TrademarkClasses
            }, cancellationToken);

        return new PageResult<ProcWeightDetailExport>
        {
            Page = pageIndex,
            PageSize = pageSize,
            Total = await totalTask,
            Data = results.Select((export, i) =>
            {
                export.SerialNumber = i + 1;
                return export;
            }).ToList()
        };

    }
}