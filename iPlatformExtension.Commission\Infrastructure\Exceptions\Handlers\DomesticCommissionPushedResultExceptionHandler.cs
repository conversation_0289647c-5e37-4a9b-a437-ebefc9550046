﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class DomesticCommissionPushedResultExceptionHandler(ILogger<DomesticCommissionPushedResultCommand> logger) 
    : IRequestExceptionHandler<DomesticCommissionPushedResultCommand, Unit, Exception>
{
    public Task Handle(DomesticCommissionPushedResultCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        logger.LogPushedDomesticTrademarkWeightError(exception);
        state.SetHandled(Unit.Value);
        return Task.CompletedTask;
    }
}