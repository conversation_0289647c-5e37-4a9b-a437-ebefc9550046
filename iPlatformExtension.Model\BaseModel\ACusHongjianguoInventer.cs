using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_cus_hongjianguo_inventer", DisableSyncStructure = true)]
	public partial class ACusHongjianguoInventer {

		[ Column(Name = "code", StringLength = 200)]
		public string Code { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "customer_name", StringLength = 200)]
		public string CustomerName { get; set; }

		[ Column(Name = "id", IsIdentity = true)]
		public int Id { get; set; }

		[ Column(Name = "inventer_name", StringLength = 50)]
		public string InventerName { get; set; }

		[ Column(Name = "inventor_id", StringLength = 50)]
		public string InventorId { get; set; }

	}

}
