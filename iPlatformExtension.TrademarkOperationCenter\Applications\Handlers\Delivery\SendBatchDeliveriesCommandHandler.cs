﻿using System.Text;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class SendBatchDeliveriesCommandHandler(IMediator mediator, ObjectPool<StringBuilder> stringBuilderPool)
    : IRequestHandler<SendBatchDeliveriesCommand, DeliveryBatchOperationResult>
{
    public async Task<DeliveryBatchOperationResult> Handle(SendBatchDeliveriesCommand request, CancellationToken cancellationToken)
    {
        var button = request.DeliveryButton;
        var result = new DeliveryBatchOperationResult();
        
        foreach (var procId in request.ProcIds)
        {
            result.Results.Add(await mediator.Send(new SendBatchDeliveryItemCommand(procId, button, request.OperatorId), cancellationToken));
        }

        if (!result.Success)
        {
            var stringBuilder = stringBuilderPool.Get();
            result.Message = result.GetMessage(stringBuilder);
            stringBuilderPool.Return(stringBuilder);
        }

        return result;
    }
}