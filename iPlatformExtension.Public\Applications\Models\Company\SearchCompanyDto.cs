﻿namespace iPlatformExtension.Public.Applications.Models.Company;


/// <summary>
/// 内部机构字段
/// </summary>
/// <param name="CompanyId">内部机构id</param>
/// <param name="CompanyNameCn">内部机构中文名称</param>
/// <param name="CompanyNameEn">内部机构英文名称</param>
/// <param name="ShortNameCn">内部机构中文缩写</param>
/// <param name="ShortNameEn">内部机构英文缩写</param>
/// <param name="Fax">传真</param>
/// <param name="AddressCn">地址</param>
/// <param name="AddressDetail">详细地址</param>
/// <param name="AddressEn">地址英文名称</param>
/// <param name="Tel">电话</param>
/// <param name="Email">邮箱</param>
/// <param name="PostCode">邮编</param>
/// <param name="Seq">排序</param>
public record SearchCompanyDto(string CompanyId, string CompanyNameCn, string CompanyNameEn, string ShortNameCn, string ShortNameEn,
    string Fax, string AddressCn, string AddressDetail, string AddressEn, string Tel, string Email, string PostCode, int? Seq);

