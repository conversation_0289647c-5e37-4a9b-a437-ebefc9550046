﻿namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 递交状态信息
/// </summary>
public class DeliveryStatusInfo
{
    /// <summary>
    /// 任务id
    /// </summary>
    public string ProcId { get; set; } = default!;

    /// <summary>
    /// 递交状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    public string OrderNo { get; set; } = string.Empty;

    /// <summary>
    /// 过期时间
    /// </summary>
    public TimeSpan ExpiredTime { get; set; } = Timeout.InfiniteTimeSpan;
}