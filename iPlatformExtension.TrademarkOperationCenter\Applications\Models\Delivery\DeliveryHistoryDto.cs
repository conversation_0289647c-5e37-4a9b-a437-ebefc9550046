﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 递交历史数据
/// </summary>
public sealed class DeliveryHistoryDto
{
    /// <summary>
    /// 历史id
    /// </summary>
    public string HistoryId { get; set; } = default!;

    /// <summary>
    /// 操作名称
    /// </summary>
    public string Operation { get; set; } = string.Empty;

    /// <summary>
    /// 操作用户
    /// </summary>
    public string Operator { get; set; } = string.Empty;

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime OperatingTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; } = string.Empty;

    /// <summary>
    /// 递交结果id
    /// </summary>
    public int? ResultId { get; set; }
}