﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Outsourcing.Application.Queries.Supplier;

public sealed record SupplierInputCaseInfoCountsQuery(
    string ManageCompanyId,
    IEnumerable<string> CaseDirections,
    IEnumerable<string> CtrlProcIds,
    IEnumerable<string> ApplicationTypeIds,
    DateRange DateRange,
    string[] SupplierIds) 
    : IStreamRequest<SupplierCaseCountDto>;