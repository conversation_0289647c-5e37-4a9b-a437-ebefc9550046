﻿using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Outsourcing.Infrastructure.Constants;
using Microsoft.AspNetCore.Authorization;

namespace iPlatformExtension.Outsourcing.Infrastructure.Authorization.NotAssignedOutsourcing;

public class NotAssignedOutsourcingAuthorizeAttribute(params string[] authorizationTypes) 
    : AuthorizeAttribute(AuthorizationPolicyNames.NotAssignedOutsourcing), IAuthorizationRequirementData
{
    public IEnumerable<IAuthorizationRequirement> GetRequirements()
    {
        yield return new NotAssignedOutsourcingAuthorizationRequirement(authorizationTypes);
    }
}