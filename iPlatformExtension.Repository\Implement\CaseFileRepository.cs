﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class CaseFileRepository : DefaultRepository<CaseFile, string>, ICaseFileRepository
{
    public CaseFileRepository(IFreeSql freeSql, UnitOfWorkManager unitOfWorkManager) : base(freeSql, unitOfWorkManager)
    {
       
    }

    public async Task<int> DeleteByProcFlowId(string procFlowId, CancellationToken cancellationToken)
    {
       return  await Select.Where(o => o.ObjId == procFlowId).ToDelete().ExecuteAffrowsAsync(cancellationToken);
    }
}
