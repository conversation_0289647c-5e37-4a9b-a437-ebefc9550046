using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_invoice_alias_config", DisableSyncStructure = true)]
	public partial class BasInvoiceAliasConfig {

		[ Column(Name = "alias_config_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string AliasConfigId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "apply_type_id", StringLength = 50)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "config_id", StringLength = 50)]
		public string ConfigId { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		[ Column(Name = "fee_name_alias", StringLength = 100)]
		public string FeeNameAlias { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
