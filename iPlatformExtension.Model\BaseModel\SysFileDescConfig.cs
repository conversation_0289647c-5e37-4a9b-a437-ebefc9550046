using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_file_desc_config", DisableSyncStructure = true)]
	public partial class SysFileDescConfig {

		[ Column(Name = "config_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ConfigId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "apply_type_id", StringLength = 500)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "config_name", StringLength = 200)]
		public string ConfigName { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "file_desc_id", StringLength = 50)]
		public string FileDescId { get; set; }

		[ Column(Name = "file_ex", StringLength = 400)]
		public string FileEx { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "level_deli", StringLength = 50)]
		public string LevelDeli { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
