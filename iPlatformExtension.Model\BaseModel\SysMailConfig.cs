using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_mail_config", DisableSyncStructure = true)]
	public partial class SysMailConfig {

		[ Column(Name = "config_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ConfigId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "config_name", StringLength = 200)]
		public string ConfigName { get; set; }

		[ Column(Name = "config_remark", StringLength = 2000)]
		public string ConfigRemark { get; set; }

		[ Column(Name = "config_type", StringLength = 50)]
		public string ConfigType { get; set; }

		[ Column(Name = "hand_user", StringLength = 50)]
		public string HandUser { get; set; }

		[ Column(Name = "hand_user_type", StringLength = 50)]
		public string HandUserType { get; set; }

		[ Column(Name = "host_id", StringLength = 50)]
		public string HostId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "legal_due_date", StringLength = 50)]
		public string LegalDueDate { get; set; }

		[ Column(Name = "legal_due_date_ad")]
		public int? LegalDueDateAd { get; set; }

		[ Column(Name = "legal_due_date_am")]
		public int? LegalDueDateAm { get; set; }

		[ Column(Name = "legal_due_date_at", StringLength = 50)]
		public string LegalDueDateAt { get; set; }

		[ Column(Name = "read_user", StringLength = 2000)]
		public string ReadUser { get; set; }

		[ Column(Name = "read_user_type", StringLength = 500)]
		public string ReadUserType { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
