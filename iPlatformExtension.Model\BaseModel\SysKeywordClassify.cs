using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_keyword_classify", DisableSyncStructure = true)]
	public partial class SysKeywordClassify {

		[ Column(Name = "classify_id", StringLength = 50)]
		public string ClassifyId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "noun_id", StringLength = 50)]
		public string NounId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "text_en_us", StringLength = 50)]
		public string TextEnUs { get; set; }

		[ Column(Name = "text_zh_ch", StringLength = 50)]
		public string TextZhCh { get; set; }

	}

}
