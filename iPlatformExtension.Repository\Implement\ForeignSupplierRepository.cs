﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class ForeignSupplierRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    UnitOfWorkManage<PlatformFreeSql> uowManger,
    IMemoryCache memoryCache,
    CacheExpirationToken<ForeignSupplier> expirationToken,
    IRedisCache<RedisCacheOptionsBase> redisCache)
    : DefaultRepository<ForeignSupplier, long>(freeSql, uowManger), IForeignSupplierRepository
{
    public IMemoryCache MemoryCache { get; } = memoryCache;

    public CacheExpirationToken<ForeignSupplier> ExpirationToken { get; } = expirationToken;

    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}