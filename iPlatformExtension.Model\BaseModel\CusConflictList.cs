using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_conflict_list", DisableSyncStructure = true)]
	public partial class CusConflictList {

		[ Column(Name = "conflict_id", StringLength = 50, IsNullable = false)]
		public string ConflictId { get; set; }

		[ Column(Name = "product_field_id", StringLength = 50)]
		public string ProductFieldId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
