﻿using System.Net;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ServiceDiscovery;

namespace iPlatformExtension.Common.Extensions;

public static partial class LoggingExtension
{
    [LoggerMessage(LogLevel.Error, EventId = -10, Message = "\n\t请求ID [{TraceId}]\n\t请求路径 [{DisplayName}] \n\t请求参数 [{ErrorMessage}]")]
    public static partial void LogActionFailed(this ILogger logger, Exception ex, string traceId, string? displayName,
        string errorMessage);

    [LoggerMessage(LogLevel.Error, EventId = -20, Message = "路径：{DisplayName}")]
    public static partial void LogInvalidModel(this ILogger logger, Exception ex, string? displayName);

    [LoggerMessage(LogLevel.Error, EventId = -30, Message = "请求[{TraceId}]：记录数据变更历史错误")]
    public static partial void LogDataChangeError(this ILogger logger, Exception ex, string traceId);

    [LoggerMessage(LogLevel.Error, EventId = -40, Message = "事务回滚")]
    public static partial void LogTransactionRollback(this ILogger logger, Exception ex);

    [LoggerMessage(LogLevel.Error, EventId = -50, Message = "消费失败！主题：{Topic}。分区：{Partition}。偏移量：{Offset}")]
    public static partial void LogKafkaConsumeFailed(this ILogger logger, string topic, int partition, long offset,
        Exception? exception);

    [LoggerMessage(LogLevel.Error, EventId = -60, Message = "数据版本号不一致，更新数据失败！更新SQL：{Sql}")]
    public static partial void LogDbUpdateInvalidVersion(this ILogger logger, string sql, Exception exception);

    [LoggerMessage(EventId = 0, Message = "{SqlInfo}")]
    public static partial void LogSql(this ILogger logger, LogLevel logLevel, string sqlInfo);

    [LoggerMessage(LogLevel.Warning, EventId = -70, Message = "解析服务名称：[{ServiceName}]失败")]
    public static partial void LogResolveNacosServiceNameFailed(this ILogger logger, string serviceName, Exception exception);

    [LoggerMessage(LogLevel.Error, EventId = -80, Message = "工作单元事务已成功提交，但是实体变更事件发布失败！错误如下：")]
    public static partial void LogPublishEntitiesChangeEventFailed(this ILogger logger, Exception exception);

    [LoggerMessage(LogLevel.Information, EventId = 45, Message = "Request Body: {RequestBody}")]
    public static partial void LogHttpTextRequestBody(this ILogger logger, string requestBody);
    
    [LoggerMessage(LogLevel.Information, EventId = 45, Message = "Response Body: {ResponseBody}")]
    public static partial void LogHttpTextResponseBody(this ILogger logger, string responseBody);

    [LoggerMessage(LogLevel.Error, EventId = -90, Message = "请求{RequestUri}错误!响应码为：{StatusCode}")]
    public static partial void LogHttpRequestException(this ILogger logger, Uri? requestUri,
        HttpRequestException exception, HttpStatusCode? statusCode);

    [LoggerMessage(LogLevel.Error, EventId = -90, Message = "请求{uri}失败")]
    public static partial void LogHttpRequestFailed(this ILogger logger, Uri? uri, Exception exception);
    
    [LoggerMessage(LogLevel.Warning, EventId = -7, Message = "解析服务名称：[{ServiceName}]失败")]
    public static partial void LogResolveNacosServiceQueryFailed(this ILogger logger, string serviceName, Exception exception);

    [LoggerMessage(LogLevel.Warning, EventId = -7, Message = "解析服务[{Query}]失败。请确认对应的请求协议是否受支持")]
    public static partial void LogResolveNacosServiceQueryFailed(this ILogger logger, ServiceEndpointQuery query);
    
    [LoggerMessage(LogLevel.Error, Message = "GRPC调用异常!")]
    public static partial void LogGrpcException(this ILogger logger, Exception exception);

    [LoggerMessage(LogLevel.Information, Message = "GRPC调用请求参数：{Request}")]
    public static partial void LogGrpcRequestParameter(this ILogger logger, object? request);
}