﻿namespace iPlatformExtension.MailService.Applications.Models
{
    public class MailConfigDto
    {
        public string HostId { get; set; }
        public string ImapHost { get; set; }
        public int? ImapPort { get; set; }

        public string SmtpHost { get; set; }
        public int? SmtpPort { get; set; }

        public bool IsSsl { get; set; } = false;
        public string MailEncoding { get; set; }
        public string SenderAccount { get; set; }
        public string SenderPassword { get; set; }
        public string ShowName { get; set; }

        public bool IsPrivate { get; set; }
    }
}
