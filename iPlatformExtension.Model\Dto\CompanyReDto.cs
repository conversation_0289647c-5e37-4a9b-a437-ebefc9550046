﻿namespace iPlatformExtension.Model.Dto
{
    /// <summary>
    /// 请款/收款主体 返回model
    /// </summary>
    public class CompanyReDto
    {
        /// <summary>
        /// 公司Id
        /// </summary>
        public string Id { get; init; } = default!;

        /// <summary>
        /// 公司id
        /// </summary>
        public string Value { get; set; } = default!;

        /// <summary>
        /// 中文名称
        /// </summary>
        public string? TextZhCn { get; set; }

        /// <summary>
        /// 英文名称
        /// </summary>
        public string? TextEnUs { get; set; }

        /// <summary>
        /// 日文名称
        /// </summary>
        public string? TextJaJp { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 所属地区
        /// </summary>
        public string? DistrictId { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int? Seq { get; set; }

        /// <summary>
        /// 机构代码
        /// </summary>
        public string? CompanyCode { get; set; }
    }
}
