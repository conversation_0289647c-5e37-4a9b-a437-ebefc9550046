using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;

namespace iPlatformExtension.Finance.Applications.PipelineBehaviors.FeeQuery;

internal sealed class CheckForeignBillParametersBehavior(ILoggerFactory loggerFactory)
    : CheckFeesQueryParametersBehaviorBase<BuildForeignBillQueryCommand>(loggerFactory)
{
    public override bool Check(FeeQueryDto dto)
    {
        var needToQueryForeignBillRecord = false;

        if (!string.IsNullOrWhiteSpace(dto.ForeignDistrict))
        {
            needToQueryForeignBillRecord = true;
        }
        else if (!string.IsNullOrWhiteSpace(dto.ForeignBillCurrency))
        {
            needToQueryForeignBillRecord = true;
        }
        else if (!string.IsNullOrWhiteSpace(dto.ForeignApplyBatchName))
        {
            needToQueryForeignBillRecord = true;
        }
        else if (!string.IsNullOrWhiteSpace(dto.ForeignBillCaseType))
        {
            needToQueryForeignBillRecord = true;
        }
        else if (!string.IsNullOrWhiteSpace(dto.ForeignPaymentBatchName))
        {
            needToQueryForeignBillRecord = true;
        }
        else if (dto.ForeignBillDate.NeedToQuery)
        {
            needToQueryForeignBillRecord = true;
        }
        else if (dto.ForeignBillPaymentDate.NeedToQuery)
        {
            needToQueryForeignBillRecord = true;
        }
        else if (dto.ForeignBillReceiveDate.NeedToQuery)
        {
            needToQueryForeignBillRecord = true;
        }
        else if (dto.ForeignBillPaymentDueDate.NeedToQuery)
        {
            needToQueryForeignBillRecord = true;
        }
        else if (dto.ForeignBillNumbers.Any())
        {
            needToQueryForeignBillRecord = true;
        }
        else if (dto.ForeignBillPaymentStatus.Any())
        {
            needToQueryForeignBillRecord = true;
        }
        else if (dto.BankAccountNumbers.Any())
        {
            needToQueryForeignBillRecord = true;
        }

        return needToQueryForeignBillRecord;
    }
}