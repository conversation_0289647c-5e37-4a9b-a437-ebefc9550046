FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
ENV ASPNETCORE_URLS=http://+:9394
ENV ASPNETCORE_ENVIRONMENT=Staging
ENV TZ=Asia/Shanghai
ENV DOTNET_GCHeapHardLimit=80000000
ENV COMPlus_DbgEnableMiniDump=1
ENV COMPlus_DbgMiniDumpName=/tmp/iplatform-trademark-delivery.dmp
ENV COMPlus_CreateDumpDiagnostics=1
WORKDIR /app
EXPOSE 9090

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["obs_net/esdk_obs_.net_core.csproj", "obs_net/"]
COPY ["iPlatformExtension.TrademarkDelivery/iPlatformExtension.TrademarkDelivery.csproj", "iPlatformExtension.TrademarkDelivery/"]
COPY ["iPlatformExtension.Repository/iPlatformExtension.Repository.csproj", "iPlatformExtension.Repository/"]
COPY ["iPlatformExtension.Common/iPlatformExtension.Common.csproj", "iPlatformExtension.Common/"]
COPY ["iPlatformExtension.Model/iPlatformExtension.Model.csproj", "iPlatformExtension.Model/"]
RUN dotnet restore "iPlatformExtension.TrademarkDelivery/iPlatformExtension.TrademarkDelivery.csproj" -s "https://nuget.cdn.azure.cn/v3/index.json" --disable-parallel
COPY . .
WORKDIR "/src/iPlatformExtension.TrademarkDelivery"
RUN dotnet build "iPlatformExtension.TrademarkDelivery.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "iPlatformExtension.TrademarkDelivery.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "iPlatformExtension.TrademarkDelivery.dll"]
