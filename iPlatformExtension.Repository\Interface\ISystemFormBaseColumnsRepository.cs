﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Repository.Interface;

public interface ISystemFormBaseColumnsRepository : 
    IBaseRepository<SysFormBaseColumns, string>, 
    IRedisCacheableRepository<DatabaseColumnKey, SysFormBaseColumns>,
    IScopeDependency
{
    Task<SysFormBaseColumns?> ICacheableRepository<DatabaseColumnKey, SysFormBaseColumns>.GetValueFromDbAsync(DatabaseColumnKey key, CancellationToken cancellationToken)
    {
        var (tableName, columnName) = key;
        return Where(columns => columns.TableName == tableName).WithLock()
            .Where(columns => columns.DatebaseId == columnName)
            .ToOneAsync(cancellationToken)!;
    }

    async Task<IEnumerable<SysFormBaseColumns>> ICacheableRepository<DatabaseColumnKey, SysFormBaseColumns>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Where(columns => columns.TableName == "case_proc_info").WithLock()
            .WithLock()
            .ToListAsync(cancellationToken);
    }

    DatabaseColumnKey ICacheableRepository<DatabaseColumnKey, SysFormBaseColumns>.GenerateKey(SysFormBaseColumns value)
    {
        return new DatabaseColumnKey(value.TableName, value.DatebaseId);
    }
}