﻿using System.Diagnostics;
using iPlatformExtension.Commission.Infrastructure.Models;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;

internal sealed record RefreshProcRewardDateCommand((WinningRewardRule, WinningRewardRule) RuleChangedInfo) : 
    IRequest, IBackgroundTracingCommand, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>
{
    public string TraceParentId { get; set; } = Activity.Current?.ParentId ?? string.Empty;

    public string OperationName { get; set; } = "胜诉奖励配置更新联动刷新任务信息";
}