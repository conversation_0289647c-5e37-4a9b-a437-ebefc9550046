﻿using System.Reflection;
using FluentValidation;
using FluentValidation.AspNetCore;
using iPlatformExtension.Common.Validation;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Extensions;

public static class ValidationExtension
{
    /// <summary>
    /// 添加请求入参验证以及参数验证失败的格式化返回
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="options">设置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddValidation(this IServiceCollection services, ModelValidationOptions options)
    {
        if (options.ParameterBinder is not null)
        {
            services.AddTransient(options.ParameterBinder);
            services.AddTransient<ParameterBinder>(provider => (provider.GetService(options.ParameterBinder) as ParameterBinder)!);
        }

        options.ConfigGlobalValidatorOptions?.Invoke(ValidatorOptions.Global);

        services.AddSingleton<InvalidModelResponseFactory>();
        services.Configure(options.ConfigApiBehaviorOptions);
        options.Assemblies.Add(Assembly.GetExecutingAssembly());
        var entryAssembly = Assembly.GetEntryAssembly();
        if (entryAssembly is not null)
        {
            options.Assemblies.Add(entryAssembly);
        }
        services.AddFluentValidationAutoValidation(options.AutoValidationConfiguration);
        services.AddValidatorsFromAssemblies(options.Assemblies, options.Lifetime, options.Filter, options.IncludeInternalTypes);
        return services;
    }

    /// <summary>
    /// 添加默认配置的模型验证中间件
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddValidation(this IServiceCollection services) =>
        services.AddValidation(new ModelValidationOptions());
}