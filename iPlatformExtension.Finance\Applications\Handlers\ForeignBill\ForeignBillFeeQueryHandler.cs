﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Finance.Applications.Queries.ForeignBill;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal sealed class ForeignBillFeeQueryHandler(IFreeSql freeSql, IMediator mediator, ICustomerBankRepository customerBankRepository) 
    : IRequestHandler<ForeignBillFeesQuery, IEnumerable<ForeignBillFeeExportDto>>
{
    public async Task<IEnumerable<ForeignBillFeeExportDto>> Handle(ForeignBillFeesQuery request, CancellationToken cancellationToken)
    {
        var foreignBillFeesQuery =
            freeSql.Select<CaseFeeList>().From<BillRecordForeignCase, BillRecordForeign>().WithLock()
                .InnerJoin((list, billFee, bill) => list.FeeId == billFee.FeeId)
                .InnerJoin((list, billFee, bill) => bill.BillId == billFee.BillId);

        var queryDto = request.Dto;
        List<string> bankIds = [];

        foreach (var bankAccountNumber in queryDto.BankAccountNumbers)
        {
            var accountInfos = await customerBankRepository.GetCacheValueAsync(bankAccountNumber);
            bankIds.AddRange(accountInfos?.Select(info => info.BankId) ?? []);
        }

        foreignBillFeesQuery
            .WhereIf(queryDto.ForeignBillNumbers.Any(),
                (list, billFee, bill) => queryDto.ForeignBillNumbers.Contains(bill.BillNo))
            .WhereIf(bankIds.Count > 0, (list, billFee, bill) => bankIds.Contains(bill.BankId))
            .WhereIf(queryDto.ForeignBillPaymentStatus.Any(),
                (list, billFee, bill) => queryDto.ForeignBillPaymentStatus.Contains(bill.FlowStatusCode))
            .WhereDynamicFilter(
                queryDto.ForeignBillPaymentDueDate.BuildDynamicTimePeriodFilter(nameof(BillRecordForeign.IntDueDate),
                    nameof(BillRecordForeign)))
            .WhereIf(!string.IsNullOrWhiteSpace(queryDto.ForeignApplyBatchName),
                (list, billFee, bill) => bill.ApplyBatchTitle == queryDto.ForeignApplyBatchName)
            .WhereDynamicFilter(
                queryDto.ForeignBillPaymentDate.BuildDynamicTimePeriodFilter(nameof(BillRecordForeign.PayDate),
                    nameof(BillRecordForeign)))
            .WhereIf(!string.IsNullOrWhiteSpace(queryDto.ForeignBillCaseType),
                (list, billFee, bill) => bill.CaseType == queryDto.ForeignBillCaseType)
            .WhereIf(!string.IsNullOrWhiteSpace(queryDto.ForeignDistrict),
                (list, billFee, bill) => bill.District == queryDto.ForeignDistrict)
            .WhereIf(!string.IsNullOrWhiteSpace(queryDto.ForeignBillCurrency),
                (list, billFee, bill) => bill.BillCurrency == queryDto.ForeignBillCurrency)
            .WhereDynamicFilter(
                queryDto.ForeignBillReceiveDate.BuildDynamicTimePeriodFilter(nameof(BillRecordForeign.ReceiveDate),
                    nameof(BillRecordForeign)))
            .WhereDynamicFilter(
                queryDto.ForeignBillDate.BuildDynamicTimePeriodFilter(nameof(BillRecordForeign.BillDate),
                    nameof(BillRecordForeign)))
            .WhereIf(!string.IsNullOrWhiteSpace(queryDto.ForeignPaymentBatchName),
                (list, billFee, bill) => bill.PayBatchTitle == queryDto.ForeignPaymentBatchName)
            .WhereIf(queryDto.Volumes.Any(),
                (list, billFee, bill) => queryDto.Volumes.Contains(list.CaseProcInfo.CaseInfo.Volume))
            .WhereIf(queryDto.CustomerCaseNumbers.Any(),
                (list, billFee, bill) =>
                    queryDto.CustomerCaseNumbers.Contains(list.CaseProcInfo.CaseInfo.CustomerCaseNo))
            .WhereDynamicFilter(queryDto.CustomerIds.BuildContainsDynamicFilter(nameof(BillRecordForeign.CustomerId),
                nameof(BillRecordForeign)));

        var queryResult = await foreignBillFeesQuery.OrderBy((list, billFee, bill) => list.FeeId)
            .ToPageableResultAsync(queryDto, (list, billFee, bill) =>
            new ForeignBillFeeExportDto
            {
                BillId = bill.BillId,
                Volume = list.CaseProcInfo.CaseInfo.Volume,
                ForeignCaseNo = list.CaseProcInfo.CaseInfo.CustomerCaseNo,
                ProcName = list.CaseProcInfo.CtrlProcId,
                FeeClass = list.FeeClass,
                FeeName = list.FeeTypeNameId,
                Amount = list.Amount ?? 0,
                Currency = list.CurrencyId,
                CaseFeeReceiveDate = list.ReceiveDate,
                BillNo = list.BillNo,
                CaseSales = list.CaseProcInfo.CaseInfo.SalesUserId,
                ForeignBillNo = bill.BillNo,
                CaseCustomer = list.CaseProcInfo.CaseInfo.CustomerId,
                ForeignFeeName = bill.FeeType,
                BillCurrency = bill.BillCurrency,
                BillAmount = bill.BillAmount ?? 0,
                ExchangeRateBillToDomestic = bill.ExchangeRateBillToDomestic,
                ExchangeRateBillToPay = bill.ExchangeRateBillToPay,
                ExchangeRatePayToDomestic = bill.ExchangeRatePayToDomestic,
                PaymentDate = bill.PayDate,
                PaidCurrency = bill.PaidCurrency,
                PaidAmount = bill.PaidAmount,
                DomesticAmount = bill.DomesticAmount,
                RemittanceChargesCurrency = bill.RemittanceChargesCurrency,
                RemittanceCharges = bill.RemittanceCharges,
                ForeignBillCustomer = bill.CustomerId,
                Remark = bill.Remark,
                CaseType = bill.CaseType,
                ForeignBillDate = bill.BillDate,
                ForeignReceiveDate = bill.ReceiveDate,
                ForeignPaymentDueDate = bill.IntDueDate,
                District = bill.District,
                ApplyBatchTitle = bill.ApplyBatchTitle,
                PaymentBatchTitle = bill.PayBatchTitle,
                PaymentStatus = bill.FlowStatusCode
            }, cancellationToken);

        var billIds = queryResult.Select(dto => dto.BillId).Distinct().ToList();
        var paymentBatchDto = await mediator.Send(new ForeignBillPaymentBatchQuery(billIds),
            cancellationToken);

        _ = queryResult.Join(paymentBatchDto, dto => dto.BillId, dto => dto.BillId, (dto, batchDto) =>
        {
            dto.RemittanceFeeSharingMethod = batchDto.RemittanceFeeSharingMethod;
            dto.RemittanceCompany = batchDto.RemittanceCompany;
            dto.PaidBank = batchDto.PaidBank;
            dto.BelongCountry = batchDto.Country;
            return dto;
        }).ToList();

        await mediator.Publish(new ForeignBillFeeResultNotification(queryResult), cancellationToken);

        return queryResult;

    }
}