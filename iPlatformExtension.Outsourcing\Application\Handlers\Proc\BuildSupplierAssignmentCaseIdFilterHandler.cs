﻿using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Outsourcing.Application.Notifications.Proc;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class BuildSupplierAssignmentCaseIdFilterHandler : IMatchNotificationHandler<NotAssignedProcQueryContext>
{
    public ValueTask<bool> MatchAsync(NotAssignedProcQueryContext notification, CancellationToken cancellationToken)
    {
        return ValueTask.FromResult(notification.CaseIds.Count > 0);
    }

    public Task HandleAsync(NotAssignedProcQueryContext notification, CancellationToken cancellationToken)
    {
        notification.Query.Where(info => notification.CaseIds.Contains(info.CaseId));
        notification.CountQueryBuilder.Where(info => notification.CaseIds.Contains(info.CaseId));
        return Task.CompletedTask;
    }
}