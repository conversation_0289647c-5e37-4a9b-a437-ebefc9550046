﻿using System.Security.Claims;
using iPlatformExtension.Common.Converters.Abstraction;
using StackExchange.Redis;

namespace iPlatformExtension.Common.Converters;

public class ClaimPrincipalRedisValueConverter : IRedisValueConverter<ClaimsPrincipal>
{
    public RedisValue ConvertToRedisValue(ClaimsPrincipal? obj)
    {
        using var memoryStream = new MemoryStream();
        var writer = new BinaryWriter(memoryStream);
        obj?.WriteTo(writer);
        return memoryStream.ToArray();
    }

    public ClaimsPrincipal? ConvertFromRedisValue(RedisValue redisValue)
    {
        var data = new BinaryData((ReadOnlyMemory<byte>) redisValue);
        return new ClaimsPrincipal(new BinaryReader(data.ToStream()));
    }
}