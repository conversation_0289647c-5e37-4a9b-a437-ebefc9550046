using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "app_further_type_config", DisableSyncStructure = true)]
	public partial class AppFurtherTypeConfig {

		[ Column(Name = "type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string TypeId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "case_type_id", StringLength = 10)]
		public string CaseTypeId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "type_name", StringLength = 50)]
		public string TypeName { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
