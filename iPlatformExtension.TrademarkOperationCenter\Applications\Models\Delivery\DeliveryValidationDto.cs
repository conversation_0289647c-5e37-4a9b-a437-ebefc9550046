﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 流程上下文信息
/// </summary>
public sealed class DeliveryValidationDto
{
    /// <summary>
    /// 流程id
    /// </summary>
    public string? FlowId { get; set; } 

    /// <summary>
    /// 关联的对象id
    /// </summary>
    public string ProcId { get; set; } = default!;

    /// <summary>
    /// 任务编号
    /// </summary>
    public string ProcNo { get; set; } = default!;

    /// <summary>
    /// 流程状态
    /// </summary>
    public int? FlowStatus { get; set; }

    /// <summary>
    /// 当前流程节点id
    /// </summary>
    public string? CurrentNodeId { get; set; }

    /// <summary>
    /// 当前流程节点编码
    /// </summary>
    public string? CurrentNodeCode { get; set; }

    /// <summary>
    /// 当前流程代办用户id 
    /// </summary>
    public string? CurrentUserId { get; set; }
    
    /// <summary>
    /// 递交状态
    /// </summary>
    public int DeliveryStatus { get; set; }

    /// <summary>
    /// 递交操作结果
    /// </summary>
    public bool OperationResult { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public int Version { get; set; }

    /// <summary>
    /// 任务类型id
    /// </summary>
    public string CtrlProcId { get; set; } = default!;

    /// <summary>
    /// 流程类型
    /// </summary>
    public string? FlowType { get; set; }

    /// <summary>
    /// 流程子类型
    /// </summary>
    public string? FlowSubType { get; set; }

    /// <summary>
    /// 递交数据的流程子类型
    /// </summary>
    public string? DeliveryFlowSubType { get; set; }

    /// <summary>
    /// 是否自动递交
    /// </summary>
    public bool? IsAuto { get; set; }
}