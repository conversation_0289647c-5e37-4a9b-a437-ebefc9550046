﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Copy;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Copy
{
    /// <summary>
    /// 复制任务
    /// </summary>
    internal sealed class CopyProcCommandHandler(IFreeSql freeSql, IMediator mediator)
        : IRequestHandler<CopyProcCommand>
    {
        private readonly List<string> _oneToOne = ["CaseInfo", "CaseExtendInfo", "CaseProcInfo"];
        private readonly List<string> _manyToMany = ["CaseApplicantList", "CaseTrademarkNiceCategory", "CaseProcApplicant", "CaseFile", "ProcNiceCategory", "TrademarkLawBasis", "ProcCitedTrademark"];

        public async Task Handle(CopyProcCommand request, CancellationToken cancellationToken)
        {
            var copyFiledLists = await freeSql.Select<CopyFiledList>()
                .WhereIf(request.OnlyChild, it => request.CopyFieldList.Contains(it.FatherId))
                .WhereIf(!request.OnlyChild, it => request.CopyFieldList.Contains(it.Id) || request.CopyFieldList.Contains(it.FatherId))
                .WithLock().ToListAsync(cancellationToken);
            var tableList = copyFiledLists.Select(it => it.Table).Distinct();

            foreach (var table in tableList.Where(it => _oneToOne.Contains(it)))
            {
                await mediator.Send(new CopyCommand(table, request.SourceId, request.TargetId,
                    copyFiledLists.Where(it => it.Table == table).Select(it => new Tuple<string, string>(it.Property, it.KeyType)).ToList()), cancellationToken);
            }

            foreach (var table in tableList.Where(it => _manyToMany.Contains(it)))
            {
                await mediator.Send(new CopyManyCommand(table, request.SourceId, request.TargetId,
                    copyFiledLists.Where(it => it.Table == table).Select(it => new Tuple<string, string, string>(it.Property, it.KeyType, it.Id)).ToList()), cancellationToken);
            }
        }
    }
}

