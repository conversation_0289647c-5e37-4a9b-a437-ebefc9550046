﻿FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
ENV ASPNETCORE_URLS=http://+:8089
ENV ASPNETCORE_ENVIRONMENT=Development
ENV TZ=Asia/Shanghai
ENV DOTNET_GCHeapHardLimit=80000000
ENV COMPlus_DbgEnableMiniDump=1
ENV COMPlus_DbgMiniDumpName=/tmp/iplatform-public.dmp
ENV COMPlus_CreateDumpDiagnostics=1
WORKDIR /app
EXPOSE 8089

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["obs_net/esdk_obs_.net_core.csproj", "obs_net/"]
COPY ["iPlatformExtension.Public/iPlatformExtension.Public.csproj", "iPlatformExtension.Public/"]
COPY ["iPlatformExtension.Repository/iPlatformExtension.Repository.csproj", "iPlatformExtension.Repository/"]
COPY ["iPlatformExtension.Common/iPlatformExtension.Common.csproj", "iPlatformExtension.Common/"]
COPY ["iPlatformExtension.Model/iPlatformExtension.Model.csproj", "iPlatformExtension.Model/"]
RUN dotnet restore "iPlatformExtension.Public/iPlatformExtension.Public.csproj" -s "https://nuget.cdn.azure.cn/v3/index.json"
COPY . .
WORKDIR "/src/iPlatformExtension.Public"
RUN dotnet build "iPlatformExtension.Public.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "iPlatformExtension.Public.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
RUN mkdir "logs"
RUN mkdir "temp"
ENTRYPOINT ["dotnet", "iPlatformExtension.Public.dll"]
