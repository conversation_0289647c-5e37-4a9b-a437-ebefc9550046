﻿namespace iPlatformExtension.Model.Dto.Proc;

/// <summary>
/// 任务动态扩展键名
/// </summary>
public readonly struct ProcExtensionKey
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="key">键名</param>
    /// <param name="order">排序</param>
    public ProcExtensionKey(string key, int order = -1)
    {
        Key = key;
        Order = order;
    }

    /// <summary>
    /// 键名
    /// </summary>
    public string Key { get; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public int Order { get; }

    /// <inheritdoc />
    public override string ToString()
    {
        return Key;
    }
}