using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "auto_case_fee_data", DisableSyncStructure = true)]
	public partial class AutoCaseFeeData {

		[ Column(Name = "auto_type", StringLength = 50)]
		public string AutoType { get; set; }

		[ Column(Name = "batch_id", StringLength = 50)]
		public string BatchId { get; set; }

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "key_id", StringLength = 50, IsNullable = false)]
		public string KeyId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "new_fee_id", StringLength = 50)]
		public string NewFeeId { get; set; }

		[ Column(Name = "new_proc_id", StringLength = 50)]
		public string NewProcId { get; set; }

	}

}
