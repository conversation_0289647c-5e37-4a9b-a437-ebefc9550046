using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "temp_flow_list_ch", DisableSyncStructure = true)]
	public partial class TempFlowListCh {

		[ Column(Name = "audit_cn_name", StringLength = 50)]
		public string AuditCnName { get; set; }

		[ Column(Name = "audit_time")]
		public DateTime? AuditTime { get; set; }

		[ Column(Name = "audit_type_id", StringLength = 50)]
		public string AuditTypeId { get; set; }

		[ Column(Name = "audit_user_id", StringLength = 50)]
		public string AuditUserId { get; set; }

		[ Column(Name = "node_name", DbType = "varchar(8)", IsNullable = false)]
		public string NodeName { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "start")]
		public DateTime? Start { get; set; }

	}

}
