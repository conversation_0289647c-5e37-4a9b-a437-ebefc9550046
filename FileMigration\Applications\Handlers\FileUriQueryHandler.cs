﻿using FileMigration.Applications.Queries;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace FileMigration.Applications.Handlers;

public sealed class FileUriQueryHandler : IRequestHandler<FileUriQuery, string>
{
    private readonly IFreeSql _freeSql;

    private readonly IMediator _mediator;

    public FileUriQueryHandler(IFreeSql freeSql, IMediator mediator)
    {
        _freeSql = freeSql;
        _mediator = mediator;
    }

    public async Task<string> Handle(FileUriQuery request, CancellationToken cancellationToken)
    {
        var fileId = request.FileNo[4..];
        var fileInfo = await _freeSql.Select<FileListA>(fileId).FirstAsync(cancellationToken);
        if (fileInfo is null)
        {
            throw new NotFoundException(fileId, nameof(FileListA));
        }

        if (string.IsNullOrWhiteSpace(fileInfo.Bucket))
        {
            return await _mediator.Send(new PlatformFileUriQuery(fileInfo), cancellationToken);
        }
        else
        {
            return await _mediator.Send(new HuaweiObsTemporaryUriQuery(fileInfo), cancellationToken);
        }
    }
}