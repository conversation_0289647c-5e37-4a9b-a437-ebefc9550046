﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery.TrademarkTransfer;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Exceptions;
using iPlatformExtension.TrademarkDelivery.Infrastructure.Extensions;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery.TrademarkTransfer;

internal sealed class TrademarkTransferOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IDeliveryInfoRepository deliveryInfoRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    IBaseCountryRepository countryRepository,
    IApplicantTypeRepository applicantTypeRepository,
    IOptionsMonitor<PhoenixClientOptions> optionsMonitor,
    UnitOfWorkManager unitOfWorkManager,
    ILoggerFactory loggerFactory,
    PhoenixClientFactory phoenixClientFactory)
    :
        PhoenixDeliveryHandleBase<TrademarkTransferOrderCommand, TrademarkTransferOrder,
            PhoenixResponseParameters<OrderData>>(stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository,
            unitOfWorkManager, loggerFactory, phoenixClientFactory),
        IDeliveryHandler<TrademarkTransferOrderCommand, TrademarkTransferOrder,
            PhoenixResponseParameters<OrderData>>
{
    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.StartupDelivery;

    public override async Task InitializeAsync(TrademarkTransferOrderCommand command, CancellationToken cancellationToken)
    {
        var procId = command.ProcId;
        _deliveryInfo = await DeliveryInfoRepository.Select
            .IncludeMany(deliInfo => deliInfo.Applicants)
            .IncludeMany(deliInfo => deliInfo.Files)
            .Where(info => info.ProcId == procId).ToOneAsync(cancellationToken);
        if (_deliveryInfo is null)
        {
            throw new NotFoundException(procId, "递交任务");
        }

        if (command.Version != _deliveryInfo.Version)
        {
            throw new VersionException(command.Version, _deliveryInfo.Version);
        }

        _phoenixClient = _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
    }

    public override async Task<TrademarkTransferOrder> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        
        var otherInfo = _deliveryInfo.OtherInfo;
        ArgumentNullException.ThrowIfNull(otherInfo);

        var clientOptions = optionsMonitor.Get(_deliveryInfo.DeliveryKey);

        var formerApplicant = _deliveryInfo.Applicants!.First(applicant => applicant.DeliveryBusinessType is not null);

        var formerApplicantType = await applicantTypeRepository.GetCacheValueAsync(formerApplicant.TypeId.GetOrDefaultEmpty());
        if (formerApplicantType is null)
        {
            throw new ApplicantTypeMissingException(formerApplicant, "转让人");
        }

        var formerApplicantCountryId = formerApplicant.CountryId ??
                                       throw new PropertyMissingException(formerApplicant.Id,
                                           formerApplicant.CountryId, "转让人", "国家信息");

        var formerApplicantBookType = ApplicantBookType.GetApplicantBookType(formerApplicantCountryId);

        string formerApplicantCountry;
        if (formerApplicantBookType == ApplicantBookType.Overseas)
        {
            var countryInfo = await countryRepository.GetCacheValueAsync(formerApplicantCountryId);
            if (countryInfo is null)
            {
                throw new NotFoundException(formerApplicantCountryId, "国家信息");
            }

            if (string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCode) || string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCountryName))
            {
                throw new ApplicationException("商标局没有对应的国家编码和名称");
            }

            formerApplicantCountry = $"{countryInfo.TrademarkDeliveryCode}  {countryInfo.TrademarkDeliveryCountryName}";
        }
        else
        {
            formerApplicantCountry = formerApplicantBookType.Description;
        }
        
        var currentApplicant = _deliveryInfo.Applicants!.Single(applicant => applicant.IsRepresent ?? false);
        
        var countryId = currentApplicant.CountryId ??
                        throw new PropertyMissingException(currentApplicant.ApplicantId, currentApplicant.CountryId, "申请人", "国家信息");
        var applicantBookType = ApplicantBookType.GetApplicantBookType(countryId);
        
        string country;
        if (applicantBookType == ApplicantBookType.Overseas)
        {
            var countryInfo = await countryRepository.GetCacheValueAsync(countryId);
            if (countryInfo is null)
            {
                throw new NotFoundException(countryId, "国家");
            }

            if (string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCode) || string.IsNullOrWhiteSpace(countryInfo.TrademarkDeliveryCountryName))
            {
                throw new ApplicationException("商标局没有对应的国家编码和名称");
            }

            country = $"{countryInfo.TrademarkDeliveryCode}  {countryInfo.TrademarkDeliveryCountryName}";
        }
        else
        {
            country = applicantBookType.Description;
        }

        var applicantType = await applicantTypeRepository.GetCacheValueAsync(currentApplicant.TypeId.GetOrDefaultEmpty());
        if (applicantType is null)
        {
            throw new ApplicantTypeMissingException(currentApplicant, "当前申请人");
        }
        
        var attachments = _deliveryInfo.Files?
                              .Where(file => int.TryParse(file.FileCode, out _) && file.BaseFileType is "申请人文件" or "递交官方")
                              .Select(file =>
                                  new ApplicantAttachment(
                                      file.FileName, file.Url ?? string.Empty, ApplicantAttachmentType.OfficialAttachment,
                                      new ApplicantAttachmentSubType(int.Parse(file.FileCode!), file.FileDesc ?? string.Empty))) 
                          ?? Array.Empty<ApplicantAttachment>();

        var order = new TrademarkTransferOrder
        {
            OrderToken = _deliveryInfo.ProcId,
            AgentOrganTel = _deliveryInfo.ContactTel ?? throw new PropertyMissingException(_deliveryInfo.ProcId, _deliveryInfo.ContactTel, "递交任务", "联系电话"),
            TransferType = formerApplicant.DeliveryBusinessType ?? throw new PropertyMissingException(formerApplicant.Id, formerApplicant.DeliveryBusinessType, "转让申请人信息", "业务办理类型"),
            SellerInfo = new DeliveryApplicantInfo()
            {
                BookType = formerApplicantBookType.Code.ToString(),
                Country = formerApplicantCountry,
                OwnerType = formerApplicantType.GetOwnerType(),
                ApplicantName = formerApplicant.ApplicantNameCn,
                IdCard = formerApplicant.CardNo,
                ApplicantEnglishName = formerApplicant.ApplicantNameEn,
                ApplicantAddress = formerApplicant.AddressCn,
                ApplicantEnglishAddress = formerApplicant.AddressEn,
                UnifiedSocialCreditCode = formerApplicant.CardNo,
                SubjectType = formerApplicant.GetSubjectType(),
                CertificatesType = formerApplicant.CardType.GetApplicantCertificationType(),
                Code = null
            },
            BuyerInfo = new DeliveryApplicantInfo()
            {
                BookType = applicantBookType.Code.ToString(),
                Country = country,
                OwnerType = applicantType.GetOwnerType(),
                ApplicantName = currentApplicant.ApplicantNameCn,
                IdCard = currentApplicant.CardNo,
                ApplicantEnglishName = currentApplicant.ApplicantNameEn,
                ApplicantAddress = currentApplicant.AddressCn,
                ApplicantEnglishAddress = currentApplicant.AddressEn,
                Code = currentApplicant.Postcode,
                DomesticReceiverAddress = _deliveryInfo.ContactAddress,
                DomesticReceiverCode = _deliveryInfo.ContactPostCode,
                DomesticReceiverEmail = _deliveryInfo.ContactMailBox,
                DomesticReceiverName = _deliveryInfo.AgencyName,
                SubjectType = currentApplicant.GetSubjectType(),
                CertificatesType = currentApplicant.CardType.GetApplicantCertificationType(),
                UnifiedSocialCreditCode = currentApplicant.CardNo,
            },
            Attachments = attachments,
            BrandInfos = [new BrandInfo()
            {
                BrandRegisterNo = _deliveryInfo.AppNo
            }],
            OrderInfo = new OrderInfo()
            {
                PrincipalName = _deliveryInfo.ContactPerson,
                PrincipalTel = _deliveryInfo.ContactTel,
                AgentOrganConName = _deliveryInfo.AgentUser ?? throw new PropertyMissingException(_deliveryInfo.ProcId, _deliveryInfo.AgentUser, "递交任务", "官方代理人"),
                ContactEmail = _deliveryInfo.ContactMailBox,
                AgentOrganId = clientOptions.OrganizationId,
                AgentOrganName = clientOptions.UserName,
                ContactName = _deliveryInfo.ContactPerson,
                ContactTel = _deliveryInfo.ContactTel,
                DomesticReceiverAddress = _deliveryInfo.ContactAddress,
                DomesticReceiverCode = _deliveryInfo.ContactPostCode,
                DomesticReceiverName = _deliveryInfo.AgencyName,
            }
        };

        return order;
    }

    public override Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(TrademarkTransferOrder request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        _phoenixClient ??= _phoenixClientFactory.CreateClient(_deliveryInfo.DeliveryKey);
        return _phoenixClient.CreateOrderAsync(PhoenixUri.TransferOrder, request);
    }

    public override Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        var orderNo = response.Data?.OrderNo!;
        const int newStatus = (int) DeliveryStatus.Ordered;
        
        return Handler.UpdateDeliveryInfoInternalAsync(deliveryInfo =>
        {
            deliveryInfo.Status = newStatus;
            deliveryInfo.OrderNo = orderNo;
            deliveryInfo.OperationResult = true;
            return ValueTask.CompletedTask;
        }, cancellationToken);
    }

    public async Task<bool> HandleExceptionAsync(Exception ex, CancellationToken cancellationToken = default)
    {
        await Handler.HandleExceptionInternalAsync(ex, true, false, cancellationToken);
        return false;
    }
}