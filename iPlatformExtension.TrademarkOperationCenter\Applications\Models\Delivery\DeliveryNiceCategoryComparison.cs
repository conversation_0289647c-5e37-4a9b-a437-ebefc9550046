﻿using FreeSql.DataAnnotations;
using System.Text.Json.Serialization;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery {

	/// <summary>
	/// 案件商标尼斯分类
	/// </summary>
	public class DeliveryNiceCategoryComparison
    {

		/// <summary>
		/// 案件id
		/// </summary>
		[Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; } = default!;

		/// <summary>
		/// 商品分类
		/// </summary>
		[Column(Name = "category_name", StringLength = 100, IsNullable = false)]
		public string CategoryName { get; set; } = "";

		/// <summary>
		/// 商品分类编码
		/// </summary>
		[Column(Name = "category_number", StringLength = 50, IsNullable = false)]
		public string CategoryNumber { get; set; } = "";
		
		/// <summary>
		/// 商品分类id
		/// </summary>
		[Column(Name = "category_id", StringLength = 50, IsNullable = false)]
		public string CategoryId { get; set; } = "";

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonIgnore]
        public DateTime CreationTime { get; set; }

		/// <summary>
		/// 商品分类大类编码
		/// </summary>
		[Column(Name = "grand_number", StringLength = 50, IsNullable = false)]
		public string GrandNumber { get; set; } = "";
		
		/// <summary>
		/// 分类名称
		/// </summary>
		[Column(Name = "grand_name", StringLength = 50, IsNullable = false)]
		public string GrandName { get; set; } = "";

		/// <summary>
		/// 分类id
		/// </summary>
		[Column(Name = "grand_id", StringLength = 50, IsNullable = false)]
		public string GrandId { get; set; } = "";

		/// <summary>
		/// 主键
		/// </summary>
		[Column(Name = "id", IsIdentity = true, IsPrimary = true)]
		public long Id { get; set; }

		/// <summary>
		/// 父项商品分类编码
		/// </summary>
		[Column(Name = "parent_number", StringLength = 50, IsNullable = false)]
		public string ParentNumber { get; set; } = "";
		
		/// <summary>
		/// 群编码
		/// </summary>
		[Column(Name = "parent_name", StringLength = 50, IsNullable = false)]
		public string ParentName { get; set; } = "";

		/// <summary>
		/// 群id
		/// </summary>
		[Column(Name = "parent_id", StringLength = 50, IsNullable = false)]
		public string ParentId { get; set; } = "";

        /// <summary>
        /// 更新时间
        /// </summary>
        [JsonIgnore]
        public DateTime UpdateTime { get; set; }

		/// <summary>
		/// 任务id
		/// </summary>
		[Column(Name = "proc_id")]
		public string ProcId { get; set; } = default!;

	}

}
