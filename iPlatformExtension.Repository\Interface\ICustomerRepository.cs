﻿using FreeSql;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Common.Cache;

namespace iPlatformExtension.Repository.Interface
{
    public interface ICustomerRepository :  
        IBaseRepository<CusCustomer, string>, 
        IScopeDependency, 
        IStringKeyCacheableRepository<CusCustomer>
    {

        Task<CusCustomer?> ICacheableRepository<string, CusCustomer>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
        {
            return Orm.Select<CusCustomer>().WithLock().Where(customer => customer.CustomerId == key).ToOneAsync(cancellationToken)!;
        }

        async Task<IEnumerable<CusCustomer>> ICacheableRepository<string, CusCustomer>.GetValuesFromDbAsync(CancellationToken cancellationToken)
        {
            return await Orm.Select<CusCustomer>().WithLock().Take(100).ToListAsync(cancellationToken);
        }

        string ICacheableRepository<string, CusCustomer>.GenerateKey(CusCustomer value)
        {
            return value.CustomerId;
        }

        string? IStringKeyCacheableRepository<CusCustomer>.GetCacheTextValue(CusCustomer value)
        {
            return value.CustomerFullName;
        }
    }
}
