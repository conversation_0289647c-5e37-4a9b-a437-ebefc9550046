﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.NiceCategory;
using MediatR;

namespace iPlatformExtension.Public.Applications.Queries.NiceCategory;

internal record NicePaginationQuery(
    IEnumerable<string> GrandNumbers,
    IEnumerable<string> ParentNumbers,
    IEnumerable<int> Levels,
    IEnumerable<string> Keywords,
    string VersionId,
    bool IsPrecision = false,
    int Page = 1,
    int PageSize = 100) : IRequest<IEnumerable<NiceCategoryItemDto>>, IPaginationParameters
{
    public int? PageIndex => Page;

    int? IPaginationParameters.PageSize => PageSize;
}