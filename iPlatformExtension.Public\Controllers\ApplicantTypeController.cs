﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.SystemDictionary;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 申请人类型控制器
/// </summary>
/// <param name="mediator"></param>
[ApiController]
[Route("[controller]")]
public sealed class ApplicantTypeController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// 获取申请人类型
    /// </summary>
    /// <param name="isEnable">是否可用</param>
    /// <returns>申请人类型</returns>
    [HttpGet]
    [ResponseCache(Duration = 3600, Location = ResponseCacheLocation.Any)]
    public Task<IEnumerable<ApplicantTypeDto>> GetAsync(bool? isEnable) => mediator.Send(new ApplicantTypeQuery(isEnable));
}