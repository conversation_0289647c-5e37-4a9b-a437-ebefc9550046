﻿using System.Net.Http.Headers;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.Common.Clients.HttpMessageHandlers;

public class NacosServiceTokenHandler(IHttpContextAccessor contextAccessor) : DelegatingHandler
{
    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var httpContext = contextAccessor.HttpContext;
        if (httpContext is not null)
        {
            var headers = request.Headers;
            IEnumerable<string> bladeAuthToken = httpContext.Request.Headers["blade-auth"];
            if (bladeAuthToken.Any())
            {
                headers.Add("blade-auth", bladeAuthToken);
            }

            var authorizationToken = httpContext.Request.Headers.Authorization.ToString();
            var tokenParameters = authorizationToken.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (tokenParameters.Length == 1)
            {
                headers.Authorization = new AuthenticationHeaderValue(tokenParameters[0]);
            }
            else if (tokenParameters.Length == 2)
            {
                headers.Authorization = new AuthenticationHeaderValue(tokenParameters[0], tokenParameters[1]);
            }

        }
        return base.SendAsync(request, cancellationToken);
    }
}