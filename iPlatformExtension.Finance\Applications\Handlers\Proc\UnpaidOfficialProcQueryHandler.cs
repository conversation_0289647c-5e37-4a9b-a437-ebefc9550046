﻿using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Proc;
using iPlatformExtension.Finance.Applications.Queries.Proc;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Proc;

internal sealed class UnpaidOfficialProc<PERSON><PERSON>y<PERSON><PERSON><PERSON>(IMediator mediator) : IRequestHandler<UnpaidOfficialProcQuery, IEnumerable<UnpaidOfficialProcDto>>
{
    public async Task<IEnumerable<UnpaidOfficialProcDto>> Handle(UnpaidOfficialProcQuery request, CancellationToken cancellationToken)
    {
        var dto = request.Dto;
        var feesQuery = await mediator.Send(new FeeQueryBuildCommand(dto), cancellationToken);
        return await mediator.Send(
            new UnpaidProcPagingQuery(feesQuery, dto.SortCondition, dto.SortOrder, dto.Page ?? 1, dto.PageSize ?? 500,
                dto.Total), cancellationToken);
    }
}