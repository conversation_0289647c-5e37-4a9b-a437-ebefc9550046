﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 尼斯分类
/// 没有子项
/// </summary>
[JsonDerivedType(typeof(NiceClass))]
public class NiceCategory
{
    /// <summary>
    /// id
    /// </summary>
    [JsonPropertyName("cgId")]
    public int Id { get; set; }

    /// <summary>
    /// 尼斯分类编号
    /// </summary>
    [JsonPropertyName("cgNum")]
    public string Number { get; set; } = default!;

    /// <summary>
    /// 尼斯分类名称
    /// </summary>
    [JsonPropertyName("cgName")]
    public string Name { get; set; } = default!;
}