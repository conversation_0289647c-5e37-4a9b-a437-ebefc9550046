﻿using System.ComponentModel.DataAnnotations;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow
{
    /// <summary>
    /// 流程提交信息
    /// </summary>
    public class FlowInfo
    {
        //ht.Add("f_obj_id", GetQueryValue("f_obj_id"));
        //    ht.Add("f_cur_status", GetQueryValue("f_cur_status"));
        //    ht.Add("f_status", GetQueryValue("f_status"));
        //    ht.Add("f_allow_edit", GetQueryValue("f_allow_edit"));
        //    ht.Add("f_flow_id", GetQueryValue("f_flow_id"));
        //    ht.Add("f_flow_type", GetQueryValue("f_flow_type"));
        //    ht.Add("f_flow_sub_type", GetQueryValue("f_flow_sub_type"));
        //    ht.Add("f_cur_node_id", GetQueryValue("f_cur_node_id"));
        //    ht.Add("f_cur_node_code", GetQueryValue("f_cur_node_code"));
        //    ht.Add("f_cur_user_id", login_user_id);
        //    //ht.Add("f_cur_cn_name", USERMODEL.real_name);
        //    ht.Add("f_next_node_id", GetQueryValue("f_next_node_id"));
        //    ht.Add("f_next_node_code", GetQueryValue("f_next_node_code"));
        //    ht.Add("f_next_user_id", GetQueryValue("f_next_user_id"));
        //    ht.Add("f_audit_type_id", GetQueryValue("f_audit_type_id"));
        //    ht.Add("f_remark", GetQueryValue("f_remark"));

        /// <summary>
        /// 任务ID
        /// </summary>
        [Required]
        public string ProcID { get; set; }

        /// <summary>
        /// 流程状态
        /// </summary>
        [Required]
        public int FStatus { get; set; }

        /// <summary>
        /// 商标案件流程ID
        /// </summary>'
        [Required]
        public string FFlowID { get; set; }

        /// <summary>
        /// 当前节点
        /// </summary>
        [Required]
        public string FCurNodeID { get; set; }
        /// <summary>
        /// 下个节点
        /// </summary>
        [Required]
        public string FNextNodeID { get; set; }
        /// <summary>
        /// 审核人ID
        /// </summary>
        public string? FNextUserID { get; set; }

        /// <summary>
        /// 流程提交类型
        /// submit
        /// reject
        /// </summary>
        [Required]
        public string FAuditTypeID { get; set; }

        /// <summary>
        /// 流程备注
        /// </summary>
        [Required]
        public string FRemark { get; set; }

        /// <summary>
        /// 允许编辑
        /// </summary>
        [Required]
        public bool FAllowEdit { get; set; } = false;

        /// <summary>
        /// 案件类型
        /// </summary>
        [Required]
        public string FFlowSubType { get; set; }

        /// <summary>
        /// 流程类型,
        /// 递交:DE
        /// 配案:AT
        /// 核稿:EX
        /// </summary>
        [Required]
        public string FFlowType { get; set; }

        /// <summary>
        /// 核稿任务流程id
        /// </summary>
        public string? ProcFlowId { get; set; }

        /// <summary>
        /// 流程ObjID
        /// </summary>
        public string? ObjId { get; set; }

        /// <summary>
        /// 任务承办人UserID
        /// </summary>
        //[Required]
        public string? UnderTakerUserId { get; set; }

        /// <summary>
        /// 是否新增任务
        /// </summary>
        public int? IsAddProc { get; set; }

        /// <summary>
        /// 任务备注
        /// </summary>
        public string? ProcNote { get; set; }
    }
}
