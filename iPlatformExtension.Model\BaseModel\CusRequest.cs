using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_request", DisableSyncStructure = true)]
	public partial class CusRequest {

		[ Column(Name = "request_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string RequestId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "content", StringLength = -2)]
		public string Content { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "effective_date")]
		public DateTime? EffectiveDate { get; set; }

		[ Column(Name = "end_date")]
		public DateTime? EndDate { get; set; }

		[ Column(Name = "is_applicant")]
		public bool? IsApplicant { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "request_type", StringLength = 50)]
		public string RequestType { get; set; }

		[ Column(Name = "title", StringLength = 500)]
		public string Title { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
