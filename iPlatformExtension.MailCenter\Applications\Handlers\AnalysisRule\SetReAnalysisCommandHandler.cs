﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.AnalysisRule;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AnalysisRule
{
    /// <summary>
    /// 重新解析处理者
    /// </summary>
    internal sealed class SetReAnalysisCommandHandler(
        IMailReceiveRepository mailReceiveRepository,
        IFlowRecordRepository flowRecordRepository,
        IMailCorrelativeRepository mailCorrelativeRepository,
        IMailReceiveFlowRepository mailReceiveFlowRepository,
        IMailReaderListRepository mailReaderListRepository,
        IHttpContextAccessor content,
        IFreeSql<MailCenterFreeSql> freeSql,
        IMediator mediator,
        IFlowPrivateListRepository flowPrivateListRepository
    ) : IRequestHandler<SetReAnalysisCommand>
    {
        public async Task Handle(SetReAnalysisCommand request, CancellationToken cancellationToken)
        {
            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            var mailReceives = await mailReceiveRepository
                .Where(it => request.MailId.Contains(it.MailId))
                .ToListAsync(cancellationToken);
            mailReceives.ForEach(it => it.Status = request.Status);
            var mailReceiveFlows = await mailReceiveFlowRepository
                .Where(it => request.MailId.Contains(it.MailId))
                .ToListAsync(cancellationToken);

            switch (request.Status)
            {
                case 4:
                    mailReceiveFlows.ForEach(it =>
                    {
                        if (it.FinishDate is not null)
                        {
                            throw new Exception("流程已结束");
                        }
                        it.IgnoreBy = userId;
                        it.IgnoreTime = DateTime.Now;
                        it.SortBy = null;
                        it.SortTime = null;
                        it.UndertakeUserId = null;
                        it.UpdateBy = userId;
                        it.UpdateTime = DateTime.Now;
                    });
                    await mailReceiveFlowRepository.UpdateAsync(
                        mailReceiveFlows,
                        cancellationToken
                    );
                    var mailReaderLists = await mailReaderListRepository
                        .Where(it => request.MailId.Contains(it.MailId))
                        .ToListAsync(cancellationToken);
                    await mailReaderListRepository.DeleteAsync(mailReaderLists, cancellationToken);
                    var flowPrivateLists = await flowPrivateListRepository
                        .Where(it => request.MailId.Contains(it.MailId))
                        .ToListAsync(cancellationToken);
                    await flowPrivateListRepository.DeleteAsync(
                        flowPrivateLists,
                        cancellationToken
                    );

                    //添加阅读人企业微信通知
                    await mediator.Send(
                        new MailCenterMessageQuery(mailReaderLists, OperationTypeEnum.Ignore)
                    );

                    break;
                case 0:
                    var flowPrivateList = await flowPrivateListRepository
                        .Where(it => request.MailId.Contains(it.MailId))
                        .ToListAsync(cancellationToken);
                    await flowPrivateListRepository.DeleteAsync(flowPrivateList, cancellationToken);
                    await mailReceiveFlowRepository.DeleteAsync(
                        mailReceiveFlows,
                        cancellationToken
                    );
                    var mailReaderList = await mailReaderListRepository
                        .Where(it => request.MailId.Contains(it.MailId))
                        .ToListAsync(cancellationToken);
                    await mailReaderListRepository.DeleteAsync(mailReaderList, cancellationToken);
                    break;
                case 2:
                    var hostIds = mailReceives.Select(x => x.HostId);
                    var privateHostList = await freeSql
                        .Select<MailHost>()
                        .Where(o => hostIds.Contains(o.HostId) && o.IsPrivate)
                        .WithLock()
                        .ToListAsync(it => it.HostId, cancellationToken);
                    var mailId = mailReceives
                        .Where(it => privateHostList.Contains(it.HostId))
                        .Select(it => it.MailId);
                    mailReceiveFlows.ForEach(it =>
                    {
                        it.IgnoreBy = null;
                        it.IgnoreTime = null;
                        it.UpdateBy = userId;
                        it.UpdateTime = DateTime.Now;
                        //是否个人邮件
                        if (mailId.Contains(it.MailId))
                        {
                            it.UndertakeUserId = userId;
                        }
                    });
                    await mailReceiveFlowRepository.UpdateAsync(
                        mailReceiveFlows,
                        cancellationToken
                    );
                    break;
            }

            await mailReceiveRepository.UpdateAsync(mailReceives, cancellationToken);

            var mailCorrelatives = await mailCorrelativeRepository
                .Where(it =>
                    request.MailId.Contains(it.MailId) || request.MailId.Contains(it.ObjId)
                )
                .ToListAsync(cancellationToken);
            await mailCorrelativeRepository.DeleteAsync(mailCorrelatives, cancellationToken);
            var flowRecords = await flowRecordRepository
                .Where(it => request.MailId.Contains(it.MailId))
                .ToListAsync(cancellationToken);
            await flowRecordRepository.DeleteAsync(flowRecords, cancellationToken);
        }
    }
}
