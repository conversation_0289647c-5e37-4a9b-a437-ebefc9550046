using FreeSql;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Finance.Applications.Commands.Public;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Public;

/// <summary>
/// 请款对象查询构建处理者
/// </summary>
public sealed class BuildRequestObjectQueryCommandHandler : IRequestHandler<BuildRequestObjectQueryCommand, ISelect<CusRequestObject>>
{
    private readonly ISelect<CusRequestObject> _requestObjectQuery;

    private readonly ISelect<CusCustomer> _customerQuery;

    private const string RequestObjectAlias = nameof(CusRequestObject);

    private const string CustomerAlias = nameof(CusCustomer);

    private const string BillInfoAlias = nameof(BillInfo);

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="freeSql">数据库组件</param>
    public BuildRequestObjectQueryCommandHandler(IFreeSql freeSql)
    {
        _requestObjectQuery =
            freeSql.Select<CusRequestObject>().As(RequestObjectAlias).WithLock();
        _customerQuery = freeSql.Select<CusCustomer>().As(CustomerAlias)
            .WithLock();
    }
    
    /// <summary>
    /// 构建请款对象查询语句
    /// </summary>
    /// <param name="request">查询参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>请款对象查询</returns>
    public Task<ISelect<CusRequestObject>> Handle(BuildRequestObjectQueryCommand request, CancellationToken cancellationToken)
    {
        var customerIds = request.CustomerIds;
        _requestObjectQuery
            .WhereIf(customerIds.Any(), requestObject => _customerQuery.Where(customer => 
                    (customer.CustomerId == requestObject.CustomerId || customer.ParentId == requestObject.CustomerId) && customerIds.Contains(customer.CustomerId))
                .Any())
            .WhereIf(customerIds.Any(), requestObject => !string.IsNullOrEmpty(requestObject.RequestObjectCode));
        
        var billId = request.BillId;
        _requestObjectQuery.WhereIf(!string.IsNullOrWhiteSpace(billId), requestObject =>
            _customerQuery
                .InnerJoin<BillInfo>((customer, billInfo) => billInfo.CustomerId == customer.CustomerId)
                .WhereDynamicFilter(billId.BuildEqualsDynamicFilter(nameof(BillInfo.BillId), BillInfoAlias))
                .Any(customer =>
                    customer.CustomerId == requestObject.CustomerId ||
                    customer.ParentId == requestObject.CustomerId));

        var requestObjectName = request.Name;
        _requestObjectQuery.WhereIfDynamicFilter(!string.IsNullOrWhiteSpace(requestObjectName),
            requestObjectName.BuildLikeDynamicFilterInfo(nameof(CusRequestObject.RequestObjectName),
                RequestObjectAlias));
        if (!string.IsNullOrWhiteSpace(requestObjectName))
        {
            _requestObjectQuery.Take(100);
        }

        var isEnable = request.IsEnable;
        _requestObjectQuery.WhereDynamicFilter(
            isEnable.BuildEqualsDynamicFilter(nameof(CusRequestObject.IsEnabled), RequestObjectAlias));
        
        return Task.FromResult(_requestObjectQuery);
    }
}