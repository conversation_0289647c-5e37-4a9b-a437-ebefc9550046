using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.Controllers;

/// <summary>
/// 缓存操作控制器
/// </summary>
/// <param name="expirationTokenProvider">缓存过期提供者</param>
[ApiController]
[AllowAnonymous]
[Route("[controller]")]
[ApiExplorerSettings(IgnoreApi = true)]
public sealed class CacheController(
    CacheExpirationTokenProvider expirationTokenProvider, 
    ILogger<CacheController> logger) : ControllerBase
{

    /// <summary>
    /// 获取所有缓存信息
    /// </summary>
    /// <returns>缓存信息</returns>
    [HttpGet]
    [ApiExplorerSettings(IgnoreApi = true)]
    public IEnumerable<CacheInfo> GetCacheInfos()
    {
        logger.LogInformation("获取缓存信息");
        
        var tokens = expirationTokenProvider.GetCacheExpirationTokens();
        return tokens.Select(token =>
        {
            if (token is null)
            {
                throw new NullReferenceException("缓存对应的token信息");
            }

            return new CacheInfo(token.Name, token.HasChanged);
        });
    }

    /// <summary>
    /// 删除缓存
    /// </summary>
    /// <param name="name">缓存令牌名称</param>
    /// <exception cref="ApplicationException">对应的缓存令牌不存在</exception>
    [HttpDelete]
    [ApiExplorerSettings(IgnoreApi = true)]
    public void Expire(string? name)
    {
        if (!string.IsNullOrWhiteSpace(name))
        {
            var token = expirationTokenProvider.GetCacheExpirationToken(name);
            if (token is null)
            {
                throw new ApplicationException($"无法找到对应的缓存[{name}]");
            }
            token.ExpireCache();
            return;

        }
        
        var tokens = expirationTokenProvider.GetCacheExpirationTokens();
        foreach (var cacheExpirationToken in tokens)
        {
            cacheExpirationToken?.ExpireCache();
        }
    }
    
    
}