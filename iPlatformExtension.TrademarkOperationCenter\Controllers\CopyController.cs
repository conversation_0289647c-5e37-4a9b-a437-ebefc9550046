﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Copy;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Copy;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Copy;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers
{
    /// <summary>
    /// 复制信息模块控制器
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    [Authorize]
    public class CopyController : ControllerBase
    {
        private readonly IMediator _mediator;

        public CopyController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 添加复制项
        /// </summary>
        /// <returns></returns>
        [HttpPost("AddCopyItem")]
        public async Task AddCopyItemAsync([FromBody] AddCopyItemCommand command)
        {
            await _mediator.Send(command);
        }

        /// <summary>
        /// 复制案件
        /// </summary>
        /// <returns></returns>
        [HttpPost("CopyCase")]
        public async Task CopyCaseCommand([FromBody] CopyCaseCommand command)
        {
            await _mediator.Send(command);
        }

        /// <summary>
        /// 复制任务
        /// </summary>
        /// <returns></returns>
        [HttpPost("CopyProc")]
        public async Task CopyProcCommand([FromBody] CopyProcCommand command)
        {
            await _mediator.Send(command);
        }

        /// <summary>
        /// 获取复制列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetCopyList")]
        public async Task<IEnumerable<GetCopyListDto>> GetCopyList([FromQuery] GetCopyListQuery command)
        {
            return await _mediator.Send(command);
        }

    }
}
