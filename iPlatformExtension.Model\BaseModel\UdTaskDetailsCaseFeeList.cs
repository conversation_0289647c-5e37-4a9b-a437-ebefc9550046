using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ud_task_details_case_fee_list", DisableSyncStructure = true)]
	public partial class UdTaskDetailsCaseFeeList {

		[ Column(Name = "obj_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ObjId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(StringLength = 1000)]
		public string 币别 { get; set; }

		[ Column(StringLength = 100)]
		public string 创建日期 { get; set; }

		[ Column(StringLength = 100)]
		public string 到款日期 { get; set; }

		[ Column(StringLength = 1000)]
		public string 到款状态 { get; set; }

		[ Column(StringLength = 1000)]
		public string 到款状态ID { get; set; }

		[ Column(StringLength = 1000)]
		public string 发票号 { get; set; }

		[ Column(StringLength = 1000)]
		public string 费减比例 { get; set; }

		[ Column(StringLength = 1000)]
		public string 费用备注 { get; set; }

		[ Column(StringLength = 1000)]
		public string 费用标识ID { get; set; }

		[ Column(StringLength = 1000)]
		public string 费用类型 { get; set; }

		[ Column(StringLength = 1000)]
		public string 费用名称 { get; set; }

		[ Column(StringLength = 1000)]
		public string 费用名称ID { get; set; }

		[ Column(StringLength = 1000)]
		public string 付款单号 { get; set; }

		[ Column(StringLength = 1000)]
		public string 付款类型 { get; set; }

		[ Column(StringLength = 1000)]
		public string 付款状态 { get; set; }

		[ Column(StringLength = 500)]
		public string 官方名称 { get; set; }

		[ Column(StringLength = 1000)]
		public string 缴费名义 { get; set; }

		[ Column(StringLength = 100)]
		public string 缴费期限 { get; set; }

		[ Column(StringLength = 1000)]
		public string 缴费人名称 { get; set; }

		[ Column(StringLength = 100)]
		public string 缴费日期 { get; set; }

		[ Column(StringLength = 1000)]
		public string 缴费状态 { get; set; }

		[ Column(StringLength = 1000)]
		public string 结算方式 { get; set; }

		[ Column(StringLength = 100)]
		public string 请款日期 { get; set; }

		[ Column(StringLength = 1000)]
		public string 任务名称 { get; set; }

		[ Column(StringLength = 100)]
		public string 实付日期 { get; set; }

		[ Column(StringLength = 100)]
		public string 实际金额 { get; set; }

		[ Column(StringLength = 1000)]
		public string 是否带出代理费 { get; set; }

		[ Column(StringLength = 1000)]
		public string 是否请款 { get; set; }

		[ Column(StringLength = 1000)]
		public string 是否首缴 { get; set; }

		[ Column(StringLength = 1000)]
		public string 收费规则 { get; set; }

		[ Column(StringLength = 1000)]
		public string 我方文号 { get; set; }

		[ Column(StringLength = 100)]
		public string 应付日期 { get; set; }

		[ Column(StringLength = 100)]
		public string 应收金额 { get; set; }

		[ Column(StringLength = 100)]
		public string 应收日期 { get; set; }

		[ Column(StringLength = 100)]
		public string 预请款日期 { get; set; }

		[ Column(StringLength = 1000)]
		public string 账单编号 { get; set; }

		[ Column(StringLength = 1000)]
		public string 支付方式 { get; set; }

		[ Column(Name = "a_fee_together", StringLength = 50)]
		public string AFeeTogether { get; set; }

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "balance_way", StringLength = 50)]
		public string BalanceWay { get; set; }

		[ Column(Name = "bill_no", StringLength = 100)]
		public string BillNo { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "discount", StringLength = 100)]
		public string Discount { get; set; }

		[ Column(Name = "error_columns", StringLength = 2000)]
		public string ErrorColumns { get; set; }

		[ Column(Name = "error_message", StringLength = -2)]
		public string ErrorMessage { get; set; }

		[ Column(Name = "fee_class", StringLength = 50)]
		public string FeeClass { get; set; }

		[ Column(Name = "fee_id", StringLength = 100)]
		public string FeeId { get; set; }

		[ Column(Name = "fee_type_name_id", StringLength = 100)]
		public string FeeTypeNameId { get; set; }

		[ Column(Name = "invoice_no", StringLength = 100)]
		public string InvoiceNo { get; set; }

		[ Column(Name = "is_first_pay_annual", StringLength = 50)]
		public string IsFirstPayAnnual { get; set; }

		[ Column(Name = "is_request", StringLength = 50)]
		public string IsRequest { get; set; }

		[ Column(Name = "o_amount", DbType = "money")]
		public decimal? OAmount { get; set; }

		[ Column(Name = "o_balance_way", StringLength = 50)]
		public string OBalanceWay { get; set; }

		[ Column(Name = "o_bill_no", StringLength = 50)]
		public string OBillNo { get; set; }

		[ Column(Name = "o_case_id", StringLength = 50)]
		public string OCaseId { get; set; }

		[ Column(Name = "o_create_time")]
		public DateTime? OCreateTime { get; set; }

		[ Column(Name = "o_currency_id", StringLength = 50)]
		public string OCurrencyId { get; set; }

		[ Column(Name = "o_discount", StringLength = 50)]
		public string ODiscount { get; set; }

		[ Column(Name = "o_fee_class", StringLength = 50)]
		public string OFeeClass { get; set; }

		[ Column(Name = "o_fee_id", StringLength = 50)]
		public string OFeeId { get; set; }

		[ Column(Name = "o_fee_type_name_id", StringLength = 50)]
		public string OFeeTypeNameId { get; set; }

		[ Column(Name = "o_invoice_no", StringLength = 50)]
		public string OInvoiceNo { get; set; }

		[ Column(Name = "o_is_first_pay_annual")]
		public bool? OIsFirstPayAnnual { get; set; }

		[ Column(Name = "o_is_request")]
		public bool? OIsRequest { get; set; }

		[ Column(Name = "o_office_name_zh_cn", StringLength = 500)]
		public string OOfficeNameZhCn { get; set; }

		[ Column(Name = "o_officer_status", StringLength = 50)]
		public string OOfficerStatus { get; set; }

		[ Column(Name = "o_pay_bill_no", StringLength = 50)]
		public string OPayBillNo { get; set; }

		[ Column(Name = "o_pay_cooperation_date")]
		public DateTime? OPayCooperationDate { get; set; }

		[ Column(Name = "o_pay_due_date")]
		public DateTime? OPayDueDate { get; set; }

		[ Column(Name = "o_pay_officer_date")]
		public DateTime? OPayOfficerDate { get; set; }

		[ Column(Name = "o_pay_officer_legal_date")]
		public DateTime? OPayOfficerLegalDate { get; set; }

		[ Column(Name = "o_pay_status", StringLength = 50)]
		public string OPayStatus { get; set; }

		[ Column(Name = "o_pay_way", StringLength = 50)]
		public string OPayWay { get; set; }

		[ Column(Name = "o_payment_agency", StringLength = 500)]
		public string OPaymentAgency { get; set; }

		[ Column(Name = "o_payment_name", StringLength = 50)]
		public string OPaymentName { get; set; }

		[ Column(Name = "o_pre_request_date")]
		public DateTime? OPreRequestDate { get; set; }

		[ Column(Name = "o_proc_id", StringLength = 50)]
		public string OProcId { get; set; }

		[ Column(Name = "o_receive_date")]
		public DateTime? OReceiveDate { get; set; }

		[ Column(Name = "o_receive_due_date")]
		public DateTime? OReceiveDueDate { get; set; }

		[ Column(Name = "o_receive_rule", StringLength = 50)]
		public string OReceiveRule { get; set; }

		[ Column(Name = "o_receive_status", StringLength = 50)]
		public string OReceiveStatus { get; set; }

		[ Column(Name = "o_recieve_amount", DbType = "money")]
		public decimal? ORecieveAmount { get; set; }

		[ Column(Name = "o_remark", StringLength = 4000)]
		public string ORemark { get; set; }

		[ Column(Name = "o_request_date")]
		public DateTime? ORequestDate { get; set; }

		[ Column(Name = "o_seq")]
		public int? OSeq { get; set; }

		[ Column(Name = "office_name_zh_cn", StringLength = 500)]
		public string OfficeNameZhCn { get; set; }

		[ Column(Name = "officer_status", StringLength = 50)]
		public string OfficerStatus { get; set; }

		[ Column(Name = "pay_bill_no", StringLength = 100)]
		public string PayBillNo { get; set; }

		[ Column(Name = "pay_cooperation_date")]
		public DateTime? PayCooperationDate { get; set; }

		[ Column(Name = "pay_due_date")]
		public DateTime? PayDueDate { get; set; }

		[ Column(Name = "pay_officer_date")]
		public DateTime? PayOfficerDate { get; set; }

		[ Column(Name = "pay_officer_legal_date")]
		public DateTime? PayOfficerLegalDate { get; set; }

		[ Column(Name = "pay_status", StringLength = 50)]
		public string PayStatus { get; set; }

		[ Column(Name = "pay_way", StringLength = 50)]
		public string PayWay { get; set; }

		[ Column(Name = "payment_agency", StringLength = 1000)]
		public string PaymentAgency { get; set; }

		[ Column(Name = "payment_name", StringLength = 50)]
		public string PaymentName { get; set; }

		[ Column(Name = "pre_request_date")]
		public DateTime? PreRequestDate { get; set; }

		[ Column(Name = "receive_date")]
		public DateTime? ReceiveDate { get; set; }

		[ Column(Name = "receive_due_date")]
		public DateTime? ReceiveDueDate { get; set; }

		[ Column(Name = "receive_rule", StringLength = 50)]
		public string ReceiveRule { get; set; }

		[ Column(Name = "receive_status", StringLength = 100)]
		public string ReceiveStatus { get; set; }

		[ Column(Name = "recieve_amount", DbType = "money")]
		public decimal? RecieveAmount { get; set; }

		[ Column(Name = "remark", StringLength = 4000)]
		public string Remark { get; set; }

		[ Column(Name = "request_date")]
		public DateTime? RequestDate { get; set; }

		[ Column(Name = "task_id", StringLength = 50)]
		public string TaskId { get; set; }

		[ Column(Name = "ud_create_time", InsertValueSql = "getdate()")]
		public DateTime? UdCreateTime { get; set; }

	}

}
