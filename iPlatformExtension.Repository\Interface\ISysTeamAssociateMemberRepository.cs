﻿using FreeSql;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Repository.Interface
{
    public interface ISysTeamAssociateMemberRepository : IBaseRepository<SysTeamAssociateMember, string>, IScopeDependency
    {
    }
}
