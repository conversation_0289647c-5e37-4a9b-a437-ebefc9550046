﻿using FreeSql.Internal.Model;
using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Commission.Application.Queries.WinningReward;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class WinningRewardStatisticsQueryHandler(
    IFreeSql freeSql,
    IHttpContextAccessor httpContextAccessor
    ) : IRequestHandler<WinningRewardStatisticsQuery, IEnumerable<RewardStatistics>>
{
    public async Task<IEnumerable<RewardStatistics>> Handle(WinningRewardStatisticsQuery request, CancellationToken cancellationToken)
    {
        var (dateRange, districtCode, deptIds, keyword) = request;
        var (pageIndex, pageSize, totalCount) = request as IPageQuery;

        var userId = httpContextAccessor.HttpContext?.User.GetUserId();

        var userFilter = userId.BuildEqualsDynamicFilter(nameof(WinningRewardUser.UserId), nameof(WinningRewardUser));
        var deptFilter = deptIds.BuildContainsDynamicFilter(nameof(WinningRewardUser.DeptId), nameof(WinningRewardUser));
        var dateRangeFilter =
            dateRange.BuildDateRangeDynamicFilterInfo(nameof(WinningRewardProc.CommissionDate),
                nameof(WinningRewardProc), userFilter, deptFilter);
        dateRangeFilter.Logic = DynamicFilterLogic.Or;

        var query = freeSql.Select<WinningRewardProc, WinningRewardUser>().WithLock()
            .LeftJoin((proc, user) => proc.ProcId == user.ProcId)
            .WhereDynamicFilter(dateRangeFilter)
            .WhereIf(!string.IsNullOrWhiteSpace(districtCode), (proc, user) => user.DistrictCode == districtCode)
            .WhereIf(!string.IsNullOrWhiteSpace(keyword),
                (proc, user) => user.UserName.Contains(keyword!) || user.CnName.Contains(keyword!))
            .GroupBy((proc, user) => new RewardStatistics
            {
                UserId = user.UserId,
                Username = user.UserName,
                Name = user.CnName,
                District = user.DistrictCode,
                DeptName = user.DeptName,
                Month = proc.Month,
                Year = proc.Year
            });
        
        var totalTask = totalCount is not null ? Task.FromResult(totalCount.Value) : query.CountAsync(cancellationToken);
        
        var pagingQuery = freeSql.Select<WinningRewardProc, WinningRewardUser>().WithLock()
            .LeftJoin((proc, user) => proc.ProcId == user.ProcId)
            .WhereDynamicFilter(dateRangeFilter)
            .WhereIf(!string.IsNullOrWhiteSpace(districtCode), (proc, user) => user.DistrictCode == districtCode)
            .WhereIf(!string.IsNullOrWhiteSpace(keyword),
                (proc, user) => user.UserName.Contains(keyword!) || user.CnName.Contains(keyword!))
            .GroupBy((proc, user) => new RewardStatistics
            {
                UserId = user.UserId,
                Username = user.UserName,
                Name = user.CnName,
                District = user.DistrictName,
                DeptName = user.DeptName,
                Month = proc.Month,
                Year = proc.Year
            });
        
        var result = await pagingQuery.OrderBy(aggregate => aggregate.Key.UserId)
            .Page(pageIndex, pageSize).ToListAsync(aggregate => new RewardStatistics
            {
                SerialNumber = 0,
                UserId = aggregate.Key.UserId,
                Username = aggregate.Key.Username,
                Name = aggregate.Key.Name,
                District = aggregate.Key.District,
                Reward = aggregate.Sum(aggregate.Value.Item2.EditedReward ?? aggregate.Value.Item2.Reward),
                DeptName = aggregate.Key.DeptName,
                Month = aggregate.Key.Month,
                Year = aggregate.Key.Year,

            }, cancellationToken);

        return new PageResult<RewardStatistics>
        {
            Page = pageIndex,
            PageSize = pageSize,
            Total = await totalTask,
            Data = result.Select((export, i) =>
            {
                export.SerialNumber = i + 1;
                return export;
            }).ToList()
        };
    }
}