using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "deli_priority_transfer", DisableSyncStructure = true)]
	public partial class DeliPriorityTransfer {

		[ Column(Name = "file_id", StringLength = 50)]
		public string FileId { get; set; }

		[ Column(Name = "priority_no", StringLength = 50)]
		public string PriorityNo { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "transferor", StringLength = 500)]
		public string Transferor { get; set; }

	}

}
