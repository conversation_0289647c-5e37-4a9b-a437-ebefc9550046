using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_crm_send", DisableSyncStructure = true)]
	public partial class SysCrmSend {

		[ Column(Name = "id", IsPrimary = true, IsIdentity = true)]
		public int Id { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "crm_obj_id", StringLength = 50)]
		public string CrmObjId { get; set; }

		[ Column(Name = "exec_status")]
		public int? ExecStatus { get; set; } = 0;

		[ Column(Name = "exec_type")]
		public int ExecType { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; }

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		[ Column(Name = "send_type")]
		public int SendType { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

	}

}
