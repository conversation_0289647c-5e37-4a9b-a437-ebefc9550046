﻿using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow
{
    /// <summary>
    /// 商标查询待办
    /// </summary>
    /// <param name="flowType">流程类型</param>
    /// <param name="flowSubType">流程子类型</param>
    public record MyCasePrivateCountQuery(string flowType, string? flowSubType) : IRequest<IEnumerable<MyCasePrivateCountDto>>
    {
    }
}
