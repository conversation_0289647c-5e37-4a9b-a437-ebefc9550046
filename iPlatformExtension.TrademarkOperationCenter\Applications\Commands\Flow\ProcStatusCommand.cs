﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow
{
    public record class ProcStatusCommand(Models.Flow.FlowInfo info, SysFlowActivity fa, string ProcStatus, string SubProcStatus, string curUserID) 
        : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;
 
}
