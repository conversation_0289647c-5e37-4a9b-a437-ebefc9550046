﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Clients.OrderCaseEntrust;

/// <summary>
/// 
/// </summary>
public class CaseEntrustHttpClient
{
    private readonly HttpClient _client;

    private readonly ILogger _logger;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="client"></param>
    /// <param name="logger"></param>
    public CaseEntrustHttpClient(HttpClient client, ILogger<CaseEntrustHttpClient> logger)
    {
        _client = client;
        _logger = logger;
    }

    /// <summary>
    /// 下载委案系统的文件
    /// </summary>
    /// <param name="uri">文件地址</param>
    /// <returns>文件对应的字节数组</returns>
    public Task<byte[]> DownloadFileAsync(string uri) => _client.GetByteArrayAsync(uri);
}