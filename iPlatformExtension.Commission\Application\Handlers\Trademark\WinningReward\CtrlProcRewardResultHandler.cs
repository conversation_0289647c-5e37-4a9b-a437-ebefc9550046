﻿using iPlatformExtension.Commission.Application.Notifications.WinningReward;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class CtrlProcRewardResultHandler(IBaseCtrlProcRepository ctrlProcRepository) : INotificationHandler<RewardResultNotification>
{
    public async Task Handle(RewardResultNotification notification, CancellationToken cancellationToken)
    {
        var proc = notification.RewardProc;
        proc.ProcName = await ctrlProcRepository.GetTextValueAsync(proc.CtrlProcId) ?? string.Empty;
    }
}