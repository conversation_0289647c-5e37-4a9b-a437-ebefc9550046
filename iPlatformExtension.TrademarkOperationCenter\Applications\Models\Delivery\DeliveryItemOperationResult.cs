using System.Text;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 启动递交流程结果
/// </summary>
internal class DeliveryItemOperationResult : IBatchItemOperationResult
{
    public string ItemId => ProcId;
    
    public bool Success { get; set; } = true;

    internal string? ProcId { get; set; }

    public string Message { get; set; } = "操作成功";

    internal DeliveryItemOperationResult()
    {
        
    }

    public DeliveryItemOperationResult(string procId)
    {
        ProcId = procId;
    }

    public DeliveryItemOperationResult Fail(string message)
    {
        Success = false;
        Message = message;
        return this;
    }

    public DeliveryItemOperationResult Fail(Exception exception, StringBuilder stringBuilder)
    {
        return Fail(stringBuilder.BuildExceptionMessage(exception));
    }
}