using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_cus_address_list_update", DisableSyncStructure = true)]
	public partial class ACusAddressListUpdate {

		[ Column(Name = "address_cn", StringLength = 500)]
		public string AddressCn { get; set; }

		[ Column(Name = "address_en", StringLength = 500)]
		public string AddressEn { get; set; }

		[ Column(Name = "address_id", StringLength = 50, IsNullable = false)]
		public string AddressId { get; set; }

		[ Column(Name = "address_type", StringLength = 50)]
		public string AddressType { get; set; }

		[ Column(Name = "city_id", StringLength = 50)]
		public string CityId { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "is_del")]
		public bool? IsDel { get; set; } = false;

		[ Column(Name = "is_do")]
		public bool? IsDo { get; set; } = false;

		[ Column(Name = "new_address_id", StringLength = 50)]
		public string NewAddressId { get; set; }

		[ Column(Name = "num")]
		public int? Num { get; set; }

		[ Column(Name = "province_id", StringLength = 50)]
		public string ProvinceId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
