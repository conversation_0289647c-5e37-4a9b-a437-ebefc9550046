﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_tag_list", DisableSyncStructure = true)]
	public partial class MailTagList {

		[Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; }

		[Column(Name = "mail_id", StringLength = 50)]
		public string MailId { get; set; }

		[Column(Name = "tag_id", StringLength = 50, IsNullable = false)]
		public string TagId { get; set; }

	}

}
