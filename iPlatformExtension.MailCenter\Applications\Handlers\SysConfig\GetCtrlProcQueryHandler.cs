﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using MediatR;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig;

/// <summary>
/// 
/// </summary>
public sealed class GetCtrlProcQueryHandler(IFreeSql<PlatformFreeSql> freeSql) : IRequestHandler<GetCtrlProcQuery, IEnumerable<GetCtrlProcDto>>
{
    public async Task<IEnumerable<GetCtrlProcDto>> Handle(GetCtrlProcQuery request, CancellationToken cancellationToken)
    {
        var result = await freeSql.Select<BasCtrlProc>().WithLock().Where(it => it.IsEnabled == true)
        .ToListAsync(it => new GetCtrlProcDto(it.CtrlProcId, it.CaseTypeId, it.CtrlProcZhCn), cancellationToken);
        return result;
    }
}


