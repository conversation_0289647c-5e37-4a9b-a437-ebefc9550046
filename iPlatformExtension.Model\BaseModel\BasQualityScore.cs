using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_quality_score", DisableSyncStructure = true)]
	public partial class BasQualityScore {

		[ Column(Name = "score_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ScoreId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "ctrl_proc_type", StringLength = 50)]
		public string CtrlProcType { get; set; }

		[ Column(Name = "deduction")]
		public int? Deduction { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "is_score")]
		public bool? IsScore { get; set; }

		[ Column(Name = "issue_level", StringLength = 50)]
		public string IssueLevel { get; set; }

		[ Column(Name = "parent_score_id", StringLength = 50)]
		public string ParentScoreId { get; set; }

		[ Column(Name = "root_score_id", StringLength = 50)]
		public string RootScoreId { get; set; }

		[ Column(Name = "score_type", StringLength = 50)]
		public string ScoreType { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "serial_no", StringLength = 50)]
		public string SerialNo { get; set; }

		[ Column(Name = "title", StringLength = 1000)]
		public string Title { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
