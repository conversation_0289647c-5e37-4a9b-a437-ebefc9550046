﻿using iPlatformExtension.Commission.Application.Queries.Trademark.Foreign;
using iPlatformExtension.Commission.Grpc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class MentorWeightQueryHandler(
    IMentorRepository mentorRepository, 
    IDistrictRepository districtRepository,
    IDepartmentInfoRepository departmentInfoRepository) : IRequestHandler<MentorWeightQuery, UserWeight?>
{
    public async Task<UserWeight?> Handle(MentorWeightQuery request, CancellationToken cancellationToken)
    {
        var userId = request.UserId;
        var mentorInfo = await mentorRepository.GetCacheValueAsync(userId, cancellationToken: cancellationToken);

        if (mentorInfo == null)
        {
            return null;
        }

        var mentorWeight = new UserWeight
        {
            Username = mentorInfo.MentorUserName,
            CnName = mentorInfo.MentorCnName,
            DeptId = mentorInfo.DeptId,
        };
        
        var departmentInfo = await departmentInfoRepository.GetCacheValueAsync(mentorInfo.DeptId, cancellationToken:cancellationToken);
        mentorWeight.DeptName = departmentInfo?.FullName ?? string.Empty;
        mentorWeight.DistrictCode = departmentInfo?.DistrictCode ?? string.Empty;
        mentorWeight.DistrictName = await districtRepository.GetTextValueAsync(mentorWeight.DistrictCode) ?? string.Empty;
        
        return mentorWeight;
    }
}