﻿using FluentValidation;
using FluentValidation.Validators;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Common.Validation.Validators;

public class SystemDictionaryValueValidator<T>(
    ICacheableRepository<SystemDictionaryValueKey, SysDictionary> repository, 
    string systemDictionaryName) : AsyncPropertyValidator<T, string>
{
    public override async Task<bool> IsValidAsync(ValidationContext<T> context, string value, CancellationToken cancellation)
    {
        var dictionary =
            await repository.GetCacheValueAsync(new SystemDictionaryValueKey(Name, value),
                cancellationToken: cancellation);

        var result = dictionary != null;
        if (!result)
        {
            context.AddFailure(context.PropertyPath, $"{context.DisplayName}的值不存在于数据字典中！");
        }

        return result;
    }

    public override string Name { get; } = systemDictionaryName;
}