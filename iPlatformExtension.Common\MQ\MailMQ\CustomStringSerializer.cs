﻿using Confluent.Kafka;
using System.Text;
using Newtonsoft.Json;
using System.Text.Json;

namespace iPlatformExtension.Common.MQ.MailMQ
{
    public class CustomStringSerializer<TKey>(JsonSerializerOptions serializerOptions) : ISerializer<TKey>, IDeserializer<TKey>
    {
        public TKey Deserialize(ReadOnlySpan<byte> data, bool isNull, SerializationContext context)
        {
            if (isNull || data.IsEmpty)
                return default;

            return System.Text.Json.JsonSerializer.Deserialize<TKey>(data);
        }

        public byte[] Serialize(TKey data, SerializationContext context)
        {
            if (data == null)
                return null;

            var json = JsonConvert.SerializeObject(data);
            return Encoding.UTF8.GetBytes(json);
        }
    }
}
