﻿using System.Text.Json;
using System.Text.Json.Serialization;
using iPlatformExtension.Finance.Applications.Models.Fees;

namespace iPlatformExtension.Finance.Infrastructure.Converters;

internal sealed class OfficialPaymentStatusJsonConverter : JsonConverter<OfficialPaymentStatus>
{
    public override OfficialPaymentStatus Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        return reader.GetString();
    }

    public override void Write(Utf8JsonWriter writer, OfficialPaymentStatus value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value);
    }
}