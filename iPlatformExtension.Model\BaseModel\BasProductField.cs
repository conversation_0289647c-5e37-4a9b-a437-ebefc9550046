using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_product_field", DisableSyncStructure = true)]
	public partial class BasProductField {

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "full_name", StringLength = 2000)]
		public string FullName { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "parent_id", StringLength = 50)]
		public string ParentId { get; set; }

		[ Column(Name = "product_field_id", StringLength = 50, IsNullable = false)]
		public string ProductFieldId { get; set; }

		[ Column(Name = "product_field_level", StringLength = 500)]
		public string ProductFieldLevel { get; set; }

		[ Column(Name = "product_field_name", StringLength = 500)]
		public string ProductFieldName { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
