﻿using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Newtonsoft.Json.Serialization;

namespace iPlatformExtension.Commission.Application.Models.Trademark;

/// <summary>
/// 国内商标权值修改DTO
/// </summary>
/// <param name="operations">修改操作</param>
public class DomesticWeightJsonPatchDocument(List<Operation<DomesticWeightPatchDto>> operations) 
    : JsonPatchDocument<DomesticWeightPatchDto>(operations, contractResolver)
{
    private static readonly IContractResolver contractResolver = new CamelCasePropertyNamesContractResolver();

    /// <summary>
    /// 任务id
    /// </summary>
    [Required]
    public required string ProcId { get; set; } = string.Empty;
}