using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_search_page", DisableSyncStructure = true)]
	public partial class SysSearchPage {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "search_form_code", StringLength = 50)]
		public string SearchFormCode { get; set; }

		[ Column(Name = "search_form_name", StringLength = 50)]
		public string SearchFormName { get; set; }

		[ Column(Name = "sub_codes", StringLength = 500)]
		public string SubCodes { get; set; }

	}

}
