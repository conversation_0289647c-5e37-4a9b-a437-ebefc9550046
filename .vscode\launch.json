{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "iPlatformExtension.MailCenter",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "clean & build",
            "program": "${workspaceFolder}/iPlatformExtension.MailCenter/bin/Debug/net9.0/iPlatformExtension.MailCenter.dll",
            "args": [],
            "cwd": "${workspaceFolder}/iPlatformExtension.MailCenter",
            "stopAtEntry": false,
            "justMyCode": false,        // 添加此行
            "suppressJITOptimizations": true,  // 添加此行
            "enableStepFiltering": false,      // 添加此行
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Local"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "iPlatformExtension.TrademarkOperationCenter (web)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/iPlatformExtension.TrademarkOperationCenter/bin/Debug/net8.0/iPlatformExtension.TrademarkOperationCenter.dll",
            "args": [
                "/configurl"
            ],
            "cwd": "${workspaceFolder}",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Local"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "BillInfoTransferTool (console)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/BillInfoTransferTool/bin/Debug/net8.0/BillInfoTransferTool.dll",
            "args": [],
            "cwd": "${workspaceFolder}",
            "stopAtEntry": false,
            "console": "internalConsole"
        },

        {
            // Use IntelliSense to find out which attributes exist for C# debugging
            // Use hover for the description of the existing attributes
            // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
            "name": "iPlatformExtension.Finance (web)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            // If you have changed target frameworks, make sure to update the program path.
            "program": "${workspaceFolder}/iPlatformExtension.Finance/bin/Debug/net7.0/iPlatformExtension.Finance.dll",
            "args": [],
            "cwd": "${workspaceFolder}/iPlatformExtension.Finance",
            "stopAtEntry": false,
            // Enable launching a web browser when ASP.NET Core starts. For more information: https://aka.ms/VSCode-CS-LaunchJson-WebBrowser
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Local"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": ".NET Core Attach",
            "type": "coreclr",
            "request": "attach"
        }
    ]
}