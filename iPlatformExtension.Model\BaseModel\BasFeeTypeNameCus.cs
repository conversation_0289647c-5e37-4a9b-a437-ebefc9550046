using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_fee_type_name_cus", DisableSyncStructure = true)]
	public partial class BasFeeTypeNameCus {

		[ Column(Name = "curr_id", StringLength = 50, IsNullable = false)]
		public string CurrId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "name_en_us", StringLength = 4000)]
		public string NameEnUs { get; set; }

		[ Column(Name = "name_id", StringLength = 50)]
		public string NameId { get; set; }

		[ Column(Name = "name_zh_cn", StringLength = 1000)]
		public string NameZhCn { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
