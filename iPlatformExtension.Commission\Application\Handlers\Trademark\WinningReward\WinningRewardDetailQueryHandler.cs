﻿using FreeSql;
using FreeSql.Internal.Model;
using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Commission.Application.Notifications.WinningReward;
using iPlatformExtension.Commission.Application.Queries.WinningReward;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class WinningRewardDetailQueryHandler(
    IFreeSql freeSql,
    IPublisher publisher,
    IHttpContextAccessor httpContextAccessor) 
    : IRequestHandler<WinningRewardDetailQuery, IEnumerable<RewardUserDetail>>
{
    public async Task<IEnumerable<RewardUserDetail>> Handle(WinningRewardDetailQuery request, CancellationToken cancellationToken)
    {
        var (dateRange, districtCode, deptIds, keyword) = request;
        var (pageIndex, pageSize, total) = request as IPageQuery;
        var userId = (httpContextAccessor.HttpContext?.User).GetUserId();
        
        var userIdFilter = userId.BuildEqualsDynamicFilter(nameof(WinningRewardUser.UserId), nameof(WinningRewardUser));
        var deptIdFilter =
            deptIds.BuildContainsDynamicFilter(nameof(WinningRewardUser.DeptId), nameof(WinningRewardUser));
        var dataRangeFilter = dateRange.BuildDateRangeDynamicFilterInfo(nameof(WinningRewardProc.CommissionDate),
            nameof(WinningRewardProc), userIdFilter, deptIdFilter);
        dataRangeFilter.Logic = DynamicFilterLogic.Or;

        var query = freeSql.Select<WinningRewardProc, WinningRewardUser>().WithLock()
            .InnerJoin((proc, user) => proc.ProcId == user.ProcId)
            .WhereDynamicFilter(dataRangeFilter)
            .WhereIf(!string.IsNullOrWhiteSpace(districtCode), (proc, user) => user.DistrictCode == districtCode)
            .WhereIf(!string.IsNullOrWhiteSpace(keyword),
                (proc, user) => user.UserName.Contains(keyword!) || user.CnName.Contains(keyword!));

        var totalTask = total is null ? query.CountAsync(cancellationToken) : Task.FromResult(total.Value);
        
        var results = await freeSql.Select<WinningRewardProc, WinningRewardUser>().WithLock()
            .InnerJoin((proc, user) => proc.ProcId == user.ProcId)
            .WhereDynamicFilter(dataRangeFilter)
            .WhereIf(!string.IsNullOrWhiteSpace(districtCode), (proc, user) => user.DistrictCode == districtCode)
            .WhereIf(!string.IsNullOrWhiteSpace(keyword),
                (proc, user) => user.UserName.Contains(keyword!) || user.CnName.Contains(keyword!))
            .Page(pageIndex, pageSize)
            .ToListAsync((proc, user) => new RewardUserDetail
            {
                SerialNumber = 0,
                ProcId = proc.ProcId,
                Volume = proc.Volume,
                AppNo = proc.AppNo,
                ProcNo = proc.ProcNo,
                CaseName = proc.CaseName,
                CtrlProcName = proc.CtrlProcId,
                CustomerName = proc.CustomerName,
                RulingResult = proc.RulingResult,
                SituationChanged = SqlExt.Case()
                    .When(proc.SituationChanged == true, "是")
                    .When(proc.SituationChanged == false, "否")
                    .Else(string.Empty).End(),
                BeneficiaryName = user.CnName,
                BeneficiaryReward = user.EditedReward ?? user.Reward,
                RewardBeneficiaryType = user.BeneficiaryType,
                CommissionDate = new DateOnly(proc.CommissionDate.Year, proc.CommissionDate.Month,
                    proc.CommissionDate.Day),
                PushedStatus = SqlExt.Case()
                    .When(proc.Pushed == true, "已推送")
                    .When(proc.Pushed == false, "未推送")
                    .Else(string.Empty).End()
            }, cancellationToken);
        
        await publisher.Publish(new RewardUserDisplayNotification(results), cancellationToken);

        return new PageResult<RewardUserDetail>
        {
            Page = pageIndex,
            PageSize = pageSize,
            Total = await totalTask,
            Data = results.Select((detail, i) =>
            {
                detail.SerialNumber = i + 1;
                return detail;
            }).ToList()
        };
    }
}