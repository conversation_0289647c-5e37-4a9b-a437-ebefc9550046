﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <LangVersion>13</LangVersion>
    </PropertyGroup>

<!--    <PropertyGroup>-->
<!--        <ContainerBaseImage>mcr.microsoft.com/dotnet/aspnet:8.0</ContainerBaseImage>-->
<!--        <ContainerRuntimeIdentifier>linux-x64</ContainerRuntimeIdentifier>-->
<!--        <ContainerRegistry>harbor.aciplaw.com</ContainerRegistry>-->
<!--        <ContainerRepository>acip/iplatform.mail</ContainerRepository>-->
<!--        <ContainerImageTag>latest</ContainerImageTag>-->
<!--        <ContainerUser>root</ContainerUser>-->
<!--        <PublishProfile>DefaultContainer</PublishProfile>-->
<!--    </PropertyGroup>-->

<!--    <ItemGroup>-->
<!--        <ContainerPort Include="7295" Type="tcp" />-->
<!--        <ContainerPort Include="5164" Type="tcp" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_HTTP_PORTS" Value="5164" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_HTTPS_PORTS" Value="7295" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_URLS" Value="http://+:5164;https://+:7295" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_ENVIRONMENT" Value="Production" />-->
<!--        <ContainerEnvironmentVariable Include="TZ" Value="Asia/Shanghai" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_Kestrel__Certificates__Default__Path" Value="/https/cert.crt" />-->
<!--        <ContainerEnvironmentVariable Include="ASPNETCORE_Kestrel__Certificates__Default__KeyPath" Value="/https/rsa_private.key" />-->
<!--    </ItemGroup>-->

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DocumentationFile>bin\Debug\iPlatformExtension.Mail.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DocumentationFile>bin\Release\iPlatformExtension.Mail.xml</DocumentationFile>
    </PropertyGroup>
    
    <ItemGroup>
        <Protobuf Include="..\Protos\mails.proto" GrpcServices="Server" Link="Protos\mails.proto" />
        <Protobuf Include="..\Protos\mail_messages.proto" GrpcServices="Server" Link="Protos\mail_messages.proto" />
        <Protobuf Include="..\Protos\common_messages.proto" GrpcServices="Server" Link="Protos\common_messages.proto" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Grpc.AspNetCore" Version="2.66.0" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
        <PackageReference Include="nacos-sdk-csharp.AspNetCore" Version="1.3.9" />
        <PackageReference Include="nacos-sdk-csharp.Extensions.Configuration" Version="1.3.9" />
        <PackageReference Include="NLog.DiagnosticSource" Version="5.2.1" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\iPlatformExtension.Repository\iPlatformExtension.Repository.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

</Project>
