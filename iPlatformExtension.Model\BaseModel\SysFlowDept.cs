using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_flow_dept", DisableSyncStructure = true)]
	public partial class SysFlowDept {

		[ Column(Name = "config_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ConfigId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "flow_sql", StringLength = 2000, IsNullable = false)]
		public string FlowSql { get; set; }

		[ Column(Name = "flow_sub_type", StringLength = 20)]
		public string FlowSubType { get; set; }

		[ Column(Name = "flow_type", StringLength = 20, IsNullable = false)]
		public string FlowType { get; set; }

		[ Column(Name = "menu_code", StringLength = 50)]
		public string MenuCode { get; set; }

		[ Column(Name = "node_sql", StringLength = 2000)]
		public string NodeSql { get; set; }

	}

}
