﻿﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;

namespace iPlatformExtension.MailCenterRepository.Implement;

/// <summary>
/// 邮件发送列表仓储实现
/// </summary>
internal class MailSendListRepository(
    IFreeSql<MailCenterFreeSql> fsql,
    UnitOfWorkManage<MailCenterFreeSql> manager)
    : DefaultRepository<MailSendList, string>(fsql, manager), IMailSendListRepository;
