using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkDelivery.Applications.Queries;
using MediatR;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers;

internal sealed class OrderInfoQueryHandler(IFreeSql freeSql, PhoenixClientFactory phoenixClientFactory)
    : IRequestHandler<OrderInfoQuery, PhoenixOrderInfo?>
{
    public async Task<PhoenixOrderInfo?> Handle(OrderInfoQuery request, CancellationToken cancellationToken)
    {
        var deliveryInfo = await freeSql.Select<DeliInfo>(request.ProcId).ToOneAsync(info => new DeliInfo()
        {
            ProcId = info.ProcId,
            OrderNo = info.OrderNo,
            DeliveryKey = info.DeliveryKey
        }, cancellationToken);

        if (deliveryInfo is null)
        {
            throw new NotFoundException(request.ProcId, "递交任务");
        }

        if (string.IsNullOrWhiteSpace(deliveryInfo.DeliveryKey))
        {
            throw new PropertyMissingException(request.ProcId, deliveryInfo.OrderNo, "递交任务");
        }

        if (string.IsNullOrWhiteSpace(deliveryInfo.OrderNo))
        {
            throw new PropertyMissingException(request.ProcId, deliveryInfo.OrderNo, "递交任务");
        }

        var client = phoenixClientFactory.CreateClient(deliveryInfo.DeliveryKey);
        var orderResponse = await client.GetOrderInfoAsync(new PhoenixOrderInfoParameters()
        {
            OrderNo = deliveryInfo.OrderNo
        });

        return orderResponse?.Data;
    }
}