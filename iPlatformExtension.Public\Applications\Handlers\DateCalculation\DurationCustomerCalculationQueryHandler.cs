﻿using iPlatformExtension.Common.TypeInfo;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models.DateCalculation;
using iPlatformExtension.Public.Applications.Models.MyTrademarkCase;
using iPlatformExtension.Public.Applications.Queries.DateCalculation;
using iPlatformExtension.Public.Applications.Queries.MyTrademarkCase;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.DateCalculation
{
    /// <summary>
    /// 返客户期限处理者
    /// </summary>
    internal sealed class DurationCustomerCalculationQueryHandler(IFreeSql freeSql, IMediator mediator, EntityTypeInfoProvider entityTypeInfoProvider)
        : IRequestHandler<DurationCustomerCalculationQuery, CalDateDto>
    {
        public async Task<CalDateDto> Handle(DurationCustomerCalculationQuery request, CancellationToken cancellationToken)
        {
            var dateCalDto = request.DateCalDto;
            if (_calRuleDictionary.TryGetValue(dateCalDto.CtrlProcId, out var valueTuple))
            {
                var (field, day) = valueTuple;
                if (field == "TransferringText")
                {
                    //1）官方期限、收文日期均有值时，若“官方期限 - 收文日期≤20天”，则返客户期限 = 收文日期 + 4个工作日；
                    //2）官方期限、收文日期均有值时，若“20天 < 官方期限 - 收文日期≤40天”，则返客户期限 = 收文日期 + 10个工作日；
                    //3）官方期限、收文日期均有值时，若“官方期限 - 收文日期 > 40天”，则返客户期限 = 收文日期 + 20个工作日；
                    //4）无论官方期限是否有值，当“收文日期为空”时，则返馈客户期限为空；
                    //5）若官方期限为空，且收文日期不为空，则返客户期限 = 收文日期 + 4个工作日；
                    if (dateCalDto is { LegalDueDate: not null, NotificationReceiptDate: not null })
                    {
                        var dateCount = (dateCalDto.LegalDueDate - dateCalDto.NotificationReceiptDate).Value.Days;
                        var dateSettings = _dateSettings.ExpressionCompile(dateCount);
                        if (dateSettings.Count() > 1 || !dateSettings.Any())
                        {
                            throw new Exception($"找到{dateSettings.Count()}条范围规则");
                        }

                        var dateSetting = dateSettings.FirstOrDefault();
                        return await mediator.Send(new DateCalculationQuery(dateCalDto.NotificationReceiptDate.Value, dateSetting!.day), cancellationToken);
                    }

                    if (dateCalDto.NotificationReceiptDate is null)
                    {
                        return new CalDateDto(null);
                    }

                    if (dateCalDto.NotificationReceiptDate is not null && dateCalDto.LegalDueDate is null)
                    {
                        return await mediator.Send(new DateCalculationQuery(dateCalDto.NotificationReceiptDate.Value, 10), cancellationToken);
                    }
                }

                var entityTypeInfo = entityTypeInfoProvider.Get(dateCalDto.GetType());
                if (entityTypeInfo.EntityPropertyInfos.TryGetValue(field, out var value))
                {
                    return await mediator.Send(new DateCalculationQuery((DateTime)(value.Get?.Invoke(dateCalDto) ?? throw new Exception($"{field}日期为空")), day), cancellationToken);
                }
            }

            return new CalDateDto(null);
        }

        /// <summary>
        /// 转文配置
        /// </summary>
        private readonly List<DateSetting> _dateSettings =
        [new DateSetting { day = 4, MaxValue = 30 },
            new DateSetting { day = 10, MinValue = 30, MaxValue = 50 },
            new DateSetting { day = 20, MinValue = 50 }];


        /// <summary>
        /// key：ctrlProcId, value:item1:字段名称,item2:天数
        /// </summary>
        private readonly Dictionary<string, ValueTuple<string, int>> _calRuleDictionary = new()
{
    //转文
    {"d81affa6-5cbc-41e0-885d-2ec59229c97e",("TransferringText",0)},
    {"b4393db3-c52f-4e50-96e3-5c2b8b41e795",("TransferringText",0)},
    {"fbed932b-8e31-4016-b690-49824045dc14",("TransferringText",0)},
    {"f9f7b7ef-1203-485b-b924-a84e420e125f",("TransferringText",0)},
    {"39de2416-2897-4bae-86b8-8bad866a89c1",("TransferringText",0)},
    {"7afdca40-1cfc-4980-995b-94334294a57a",("TransferringText",0)},
    {"551a3227-95d8-4a6f-9f74-8cefd3854ef1",("TransferringText",0)},
    {"462b2bc8-fe92-4da5-a5fa-8cc3a6285ade",("TransferringText",0)},
    {"cc9ee878-c844-4bcd-b719-55640d4cc2aa",("TransferringText",0)},
    {"8f2056e5-7e94-4db0-a770-a54ae5fb0811",("TransferringText",0)},
    {"ae992810-3d77-41d4-84fe-c94c0a29af81",("TransferringText",0)},
    {"693696a9-4eaa-4116-a6e3-616ab64325ad",("TransferringText",0)},
    {"32b299b7-d969-4528-a5f1-5ebaaf70badc",("TransferringText",0)},
    {"26902598-7a55-4a5c-9138-043d24a7df7f",("TransferringText",0)},

    //查询类
    {"29bcb883-aa43-4175-a8d9-f26830cae74a",(nameof(CaseProcInfo.EntrustDate),2)},
    {"bd3e9fae-89fa-484c-b196-4823ec5e6bdb",(nameof(CaseProcInfo.EntrustDate),2)},
    //注册查询
    {"6EE51434-9C6A-4DD7-B14A-21051F1E9B93",(nameof(CaseProcInfo.EntrustDate),3)},
    {"0af989ea-2d02-461a-88d2-a2581df4464b",(nameof(CaseProcInfo.EntrustDate),3)},
    {"023e72b4-f832-4669-ab80-393817e12f32",(nameof(CaseProcInfo.EntrustDate),3)},

};
    }
}

