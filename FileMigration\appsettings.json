﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "NLog": {
    "autoReload": true,
    "throwConfigExceptions": true,
    "internalLogLevel": "Info",
    "targets": {
      "infoLog": {
        "type": "File",
        "encoding": "utf-8",
        "fileName": "${basedir}/logs/${shortdate}.log",
        "layout": "[${longdate}] [${uppercase:${level}}] [${logger}] : ${newline}${message} ${newline}\n                ${exception:format=ToString} "
      },
      "errorLog": {
        "type": "File",
        "encoding": "utf-8",
        "fileName": "${basedir}/logs/${shortdate}-error.log",
        "layout": "[${longdate}] [${uppercase:${level}}] [${logger}] : ${newline}${message} ${newline}\n                ${exception:format=ToString} "
      }
    },
    "rules": [
      {
        "logger": "*",
        "minLevel": "Information",
        "writeTo": "InfoLog"
      },
      {
        "logger": "*",
        "minLevel": "Error",
        "writeTo": "errorLog"
      }
    ]
  },
  "WorkOptions": {
    "BufferSize": 100,
    "WorkStartTime": "20:00:00",
    "WorkTimeSpan": "12:00:00",
    "DelayTimeSpan": "00:00:10",
    "BucketName": "iplatform"
  },
  "HuaweiObs": {
    "AccessKey": "XIJB954KKXWSOMKA4LRQ",
    "SecretKey": "0pXYV9UKUgH4Xdz0k3WIcjNEKafNhNYnWcR32uoG",
    "Host": "http://obs.cn-south-1.myhuaweicloud.com"
  },
  "PlatformFile": {
    "UserName": "webdav",
    "Password": "Acip.file",
    "HostAddress": "http://***********/webdav"
  },
  "ConnectionStrings": {
    "Default": "data source=***************,7433;initial catalog=acip_iplatform_1009;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true",
    "Redis": "*************:6379,password=Acip_cc@54,defaultDatabase=10"
  }
}
