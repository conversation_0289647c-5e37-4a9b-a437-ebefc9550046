using iPlatformExtension.Commission.Application.Notifications.Trademark.Domestic;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class SystemDictionaryDomesticCommissionResultHandler(
    ISystemDictionaryRepository systemDictionaryRepository) 
    : INotificationHandler<DomesticCommissionResultNotification>
{
    public async Task Handle(DomesticCommissionResultNotification notification, CancellationToken cancellationToken)
    {
        var commission = notification.Commission;
        commission.CtrlProcMarkCn = await systemDictionaryRepository.GetChineseValueAsync(SystemDictionaryName.CtrlProcMark, commission.CtrlProcMark);
        commission.Status = await systemDictionaryRepository.GetChineseValueAsync(SystemDictionaryName.ProcStatus, commission.ProcStatusId);
    }
}