﻿using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class StartupDeliveryButtonQueryHandler : IRequestHandler<StartupDeliveryButtonQuery, bool>
{
    public Task<bool> Handle(StartupDeliveryButtonQuery request, CancellationToken cancellationToken)
    {
        var deliveryInfo = request.DeliveryInfo;
        var deliveryStatus = (DeliveryStatus) (deliveryInfo.Status ?? 0);
        switch (deliveryStatus)
        {
            case DeliveryStatus.Delivering:
            case DeliveryStatus.Complete:
            case DeliveryStatus.Confirmed:
                return Task.FromResult(false);
            case DeliveryStatus.Stopped:
            case DeliveryStatus.Ready:
            case DeliveryStatus.Ordered:
            default:
                return Task.FromResult(true);
        }
    }
}