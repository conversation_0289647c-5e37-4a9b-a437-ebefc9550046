﻿using iPlatformExtension.Outsourcing.Application.Queries.Apply;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace iPlatformExtension.Outsourcing.Infrastructure.Authorization.ApplyOutsourcing;

internal sealed class ApplyCaseOutsourcingAuthorizationHandler(ISender sender)
    : AuthorizationHandler<ApplyCaseOutSourcingAuthorizationRequirement, HttpContext>
{
    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, ApplyCaseOutSourcingAuthorizationRequirement requirement,
        HttpContext resource)
    {
        var appCaseId = resource.Request.RouteValues.TryGetValue("appCaseId", out var appCaseIdValue) 
            ? appCaseIdValue?.ToString() ?? string.Empty
            : string.Empty;

        var applyCaseOutSourcing = await sender.Send(new AppCaseOutsourcingQuery(appCaseId), resource.RequestAborted);
        if (applyCaseOutSourcing.Editable)
        {
            context.Succeed(requirement);
        }
        else
        {
            context.Fail(new AuthorizationFailureReason(this, "开案审核阶段不允许编辑境外代理"));
        }
    }
}