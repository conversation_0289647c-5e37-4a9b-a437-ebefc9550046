﻿using System.Buffers;
using System.IO.Compression;
using System.Text;
using System.Xml;
using iPlatformExtension.Common.Extensions;

namespace iPlatformExtension.Common.Office.Excel;

public sealed class SheetDataAppendWriter : IDisposable, IAsyncDisposable
{
    private static readonly Encoding utf8WithBom = new UTF8Encoding(true);

    private const string RNamespaceUri = "http://schemas.openxmlformats.org/officeDocument/2006/relationships";

    private const string XNamespaceUri = "http://schemas.openxmlformats.org/spreadsheetml/2006/main";
    
    private readonly XmlDocument _sheetDocument;

    private readonly XmlNamespaceManager _namespaceManager;

    private readonly string _temporaryDirectory;

    private readonly string _fileName;

    private const int BufferSize = 4096;

    internal int Count { get; private set; }
    
    internal string TemporaryContentPath { get; }
    
    internal string SheetName { get; }

    internal SheetDataAppendWriter(string fileName, string sheetName, Stream stream)
    {
        _sheetDocument = new XmlDocument();
        _namespaceManager = new XmlNamespaceManager(_sheetDocument.NameTable);
        _namespaceManager.AddNamespace("r", RNamespaceUri);
        _namespaceManager.AddNamespace("x", XNamespaceUri);
        _sheetDocument.Load(stream);

        _temporaryDirectory = $"{Path.GetFileNameWithoutExtension(fileName)}-{sheetName}";
        Directory.CreateDirectory(_temporaryDirectory);
        TemporaryContentPath = Path.Combine(_temporaryDirectory, "ContentTemporary.xml");
        _fileName = fileName;
        SheetName = sheetName;

    }

    /// <summary>
    /// 初始化
    /// </summary>
    /// <returns></returns>
    internal async Task InitializeAsync()
    {
        await using var contentFileStream = new FileStream(TemporaryContentPath, FileMode.OpenOrCreate, FileAccess.ReadWrite);
        await using var temporaryContentStreamWriter = new StreamWriter(contentFileStream, utf8WithBom, BufferSize);

        var sheetDataNode = _sheetDocument.SelectSingleNode("x:worksheet/x:sheetData", _namespaceManager);
        if (sheetDataNode is not null)
        {
            var rowNodes = sheetDataNode.SelectNodes("x:row", _namespaceManager);
            Count = rowNodes?.Count ?? 0;
            
            await temporaryContentStreamWriter.WriteAsync(sheetDataNode.InnerXml);
            await temporaryContentStreamWriter.FlushAsync();
        }
    }

    internal async Task WriteDataAsync(StringBuilder dataBuilder, int dataCount)
    {
        await using var contentFileStream = File.OpenWrite(TemporaryContentPath);
        contentFileStream.Seek(0, SeekOrigin.End);
        await using var streamWriter = new StreamWriter(contentFileStream, utf8WithBom, BufferSize);

        await streamWriter.WriteAsync(dataBuilder);
        await streamWriter.FlushAsync();

        Count += dataCount;
    }

    public async Task CombineAsync()
    {
        await using var fileStream = File.Open(_fileName, FileMode.Open);
        using var zipArchive = new ZipArchive(fileStream, ZipArchiveMode.Update, true, utf8WithBom);
        var entry = zipArchive.GetEntry($"xl/worksheets/{SheetName}.xml");
        if (entry is null)
        {
            return;
        }

        await using var targetStream = entry.Open();
        
        int? columnCount = null;
        
        var columnsNode = _sheetDocument.SelectSingleNode("x:worksheet/x:cols", _namespaceManager);
        if (columnsNode is not null)
        {
            var columnNodes = columnsNode.SelectNodes("x:col", _namespaceManager);
            columnCount = columnNodes?.Count;
        }

        string? dataLocation = null;
        if (columnCount.HasValue)
        {
            var columnName = ExcelExtension.ConvertXyToCell(columnCount.Value, Count);
            
            var dimensionNode = _sheetDocument.SelectSingleNode("x:worksheet/x:dimension", _namespaceManager);
            var dimensionNodeAttribute = dimensionNode?.Attributes?["ref"];
            if (dimensionNodeAttribute != null)
            {
                dimensionNodeAttribute.Value = $"A1:{columnName}";
                dataLocation = dimensionNodeAttribute.Value;
            }

            var autoFilter = _sheetDocument.SelectSingleNode("x:worksheet/x:autoFilter", _namespaceManager);
            var autoFilterAttribute = autoFilter?.Attributes?["ref"];
            if (autoFilterAttribute is not null)
            {
                autoFilterAttribute.Value = $"A1:{columnName}";
            }
        }

        targetStream.Seek(0, SeekOrigin.Begin);
        targetStream.SetLength(0);

        await using var streamWriter = new StreamWriter(targetStream);
        await streamWriter.WriteAsync("""<?xml version="1.0" encoding="utf-8"?>""");
        await streamWriter.WriteAsync(
            """<x:worksheet xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:x="http://schemas.openxmlformats.org/spreadsheetml/2006/main">""");
        await streamWriter.WriteAsync($"""<x:dimension ref="{dataLocation ?? string.Empty}" />""");
        await streamWriter.WriteAsync(columnsNode?.OuterXml);
        await streamWriter.WriteAsync("<x:sheetData>");

        var buffer = ArrayPool<char>.Shared.Rent(BufferSize);
        using var dataReader = new StreamReader(File.OpenRead(TemporaryContentPath), utf8WithBom);
        while (!dataReader.EndOfStream)
        {
            var readCount = await dataReader.ReadAsync(buffer, 0, BufferSize);
            if (readCount > 0)
            {
                await streamWriter.WriteAsync(buffer, 0, readCount);
            }

            if (readCount < BufferSize)
            {
                break;
            }
        }
        ArrayPool<char>.Shared.Return(buffer);

        await streamWriter.WriteAsync("</x:sheetData>");
        await streamWriter.WriteAsync($"""<x:autoFilter ref="{dataLocation ?? string.Empty}" />""");
        await streamWriter.WriteAsync("""<x:drawing r:id="drawing1" />""");
        await streamWriter.WriteAsync("</x:worksheet>");
        await streamWriter.FlushAsync();
    }

    /// <inheritdoc />
    public void Dispose()
    {
        Directory.Delete(_temporaryDirectory, true);
    }

    /// <inheritdoc />
    public ValueTask DisposeAsync()
    {
        Directory.Delete(_temporaryDirectory, true);
        return ValueTask.CompletedTask;
    }
}