using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_work_agent", DisableSyncStructure = true)]
	public partial class SysWorkAgent {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 代理工作的用户ID
		/// </summary>
		[ Column(Name = "agent_user_id", StringLength = 50)]
		public string AgentUserId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "end_date")]
		public DateTime? EndDate { get; set; }

		[ Column(Name = "flow_info", StringLength = 100)]
		public string FlowInfo { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "remark", StringLength = 500)]
		public string Remark { get; set; }

		[ Column(Name = "start_date")]
		public DateTime? StartDate { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		/// <summary>
		/// 用户ID
		/// </summary>
		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
