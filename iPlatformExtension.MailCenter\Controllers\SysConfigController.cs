﻿using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.MailCenter.Applications.Commands.MapManage;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenter.Applications.Models.MapManage;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.MapManage;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.MailCenter.Controllers
{
    /// <summary>
    /// 系统配置控制器
    /// </summary>
    /// <param name="mediator"></param>
    [Route("[controller]")]
    [ApiController]
    public class SysConfigController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// 保存收件解析规则
        /// </summary>
        /// <param name="query"></param>
        [HttpPost("SaveAnalysisRule")]
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        public async Task SaveAnalysisRule([FromBody] SaveAnalysisRuleCommand query)
        {
            await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 获取收件解析规则列表
        /// </summary>
        /// <param name="query"></param>
        [HttpGet("GetAnalysisRuleList")]
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        public async Task<IEnumerable<GetAnalysisRuleListDto>> GetAnalysisRuleList(
            [FromQuery] GetAnalysisRuleListQuery query
        )
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 获取收件解析规则详情
        /// </summary>
        /// <param name="query"></param>
        [HttpGet("GetAnalysisRuleDetail")]
        public async Task<IEnumerable<GetAnalysisRuleDetailDto>> GetAnalysisRuleDetail(
            [FromQuery] GetAnalysisRuleDetailQuery query
        )
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 删除收件解析规则
        /// </summary>
        /// <param name="query"></param>
        [HttpPost("DeleteAnalysisRule")]
        public async Task DeleteAnalysisRule([FromBody] DeleteAnalysisRuleCommand query)
        {
            await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 获取枚举值
        /// </summary>
        /// <param name="query"></param>
        [HttpGet("GetEnum")]
        public async Task<IEnumerable<GetEnumDto>> GetEnumDetail([FromQuery] GetEnumQuery query)
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 获取任务
        /// </summary>
        /// <param name="query"></param>
        [HttpGet("GetCtrlProc")]
        public async Task<IEnumerable<GetCtrlProcDto>> GetCtrlProc(
            [FromQuery] GetCtrlProcQuery query
        )
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 保存邮箱
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("SaveMailHost")]
        public async Task SaveMailHost([FromBody] SaveMailHostCommand command)
        {
            await mediator.Send(command, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 查询邮箱列表
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpGet("GetMailHostList")]
        public async Task<PageResult<GetMailHostListDto>> GetMailHostList(
            [FromQuery] GetMailHostListQuery query
        )
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 删除邮箱
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpGet("DeleteMailHost")]
        public async Task DeleteMailHost([FromQuery] string hostId)
        {
            await mediator.Send(new DeleteMailHostCommand(hostId), HttpContext.RequestAborted);
        }

        /// <summary>
        /// 获取管理员人列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetMailManagers")]
        public async Task<List<GetMailManagersDto>> GetMailManagers(
            [FromQuery] GetMailManagersQuery query
        )
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 获取邮箱权限
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetMailAccess")]
        public async Task<IEnumerable<GetMailAccessDto>> GetMailAccess(
            [FromQuery] GetMailAccessQuery query
        )
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 获取邮箱基本信息
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetMailInfo")]
        public async Task<GetMailHostListDto> GetMailInfo([FromQuery] GetMailInfoQuery query)
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 获取用户列表(分页)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetUserInfo")]
        public async Task<PageResult<GetUserInfoDto>> GetMailInfo(
            [FromQuery] GetUserInfoQuery query
        )
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 获取用户个人邮箱信息
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetMyEmailInfo")]
        public async Task<GetMailHostListDto> GetMyEmailInfo()
        {
            return await mediator.Send(new GetMyEmailInfoQuery(), HttpContext.RequestAborted);
        }

        /// <summary>
        /// 获取映射列表
        /// </summary>
        /// <param name="query"></param>
        [HttpGet("GetMapListList")]
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        public async Task<IEnumerable<GetMapListDto>> GetMapListList(
            [FromQuery] GetMapListQuery query
        )
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 保存映射
        /// </summary>
        /// <param name="command">映射保存命令</param>
        /// <returns></returns>
        [HttpPost("SaveMap")]
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        public async Task SaveMap([FromBody] SaveMapCommand command)
        {
            await mediator.Send(command, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 删除映射
        /// </summary>
        /// <param name="command">映射删除命令</param>
        /// <returns></returns>
        [HttpDelete("DeleteMap")]
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        public async Task DeleteMap([FromBody] DeleteMapCommand command)
        {
            await mediator.Send(command, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 获取任务
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetCtrlProcList")]
        [Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
        public async Task<IEnumerable<GetCtrlProcDto>> GetCtrlProcList(
            [FromQuery] GetCtrlProcQuery query
        )
        {
            return await mediator.Send(query, HttpContext.RequestAborted);
        }
    }
}
