﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 到款账单处理者匹配上下文
/// </summary>
public readonly struct ReceiveBillMatchHandlerContext
{

    /// <summary>
    /// 处理人信息
    /// </summary>
    public List<ReceiveBillHandlerInfo> HandlerInfos { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="handlerInfos">处理人信息</param>
    public ReceiveBillMatchHandlerContext(List<ReceiveBillHandlerInfo> handlerInfos)
    {
        HandlerInfos = handlerInfos;
    }

    /// <summary>
    /// 返回失败结果
    /// </summary>
    /// <param name="message">匹配失败信息</param>
    /// <returns><see cref="ReceivedBillHandlerMatchResult"/></returns>
    public ReceivedBillHandlerMatchResult Fail(string? message = default)
    {
        return new ReceivedBillHandlerMatchResult()
        {
            Message = message
        };
    }

    /// <summary>
    /// 返回成功结果
    /// </summary>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException">没有处理人信息</exception>
    public ReceivedBillHandlerMatchResult Success()
    {
        if (!HandlerInfos.Any())
        {
            throw new ApplicationException("没有处理人信息。无法匹配成功");
        }

        var handlerInfo = HandlerInfos.First();
        var result = new ReceivedBillHandlerMatchResult
        {
            MatchSuccess = true,
            Customer = handlerInfo.CustomerDto,
            BusinessPersonnel = handlerInfo.BusinessPersonnelDto ?? throw new ApplicationException("没有处理人信息。无法匹配成功"),
            RequestObject = handlerInfo.RequestObjectDto,
            Message = "匹配成功"
        };

        return result;
    }
}

