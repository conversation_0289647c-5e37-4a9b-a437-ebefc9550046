﻿namespace iPlatformExtension.Commission.Application.Models.Trademark;

internal class ProcCommissionWeightFactor
{
    public decimal BigClientPoint { get; set; } = 1;

    public decimal ProcPoint { get; set; }

    public decimal UserPoint { get; set; } = 0.5M;

    /// <summary>
    /// 建议类任务
    /// </summary>
    public decimal SuggestionProcPoint { get; set; }

    /// <summary>
    /// 多类权值
    /// </summary>
    public decimal TrademarkClassesPoint { get; set; }

    /// <summary>
    /// 计算国内商标权值
    /// </summary>
    /// <returns>国内商标任务的总权值</returns>
    public decimal CalculateDomesticTrademarkPoint()
    {
        return (ProcPoint + SuggestionProcPoint) * UserPoint * BigClientPoint;
    }

    /// <summary>
    /// 计算出口商标权值
    /// </summary>
    /// <returns>出口商标任务总权值</returns>
    public decimal CalculateForeignTrademarkPoint(int trademarkClassesCount)
    {
        var otherClassesCount = trademarkClassesCount - 1;
        return (ProcPoint + (otherClassesCount > 0 ? otherClassesCount : 0) * TrademarkClassesPoint) * BigClientPoint;
    }
}