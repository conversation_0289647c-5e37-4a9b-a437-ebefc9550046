using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ud_task_form", DisableSyncStructure = true)]
	public partial class UdTaskForm {

		[ Column(Name = "task_form_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string TaskFormId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 获取外键ID,仅新增时使用,比如新增任务时获取案件ID
		/// </summary>
		[ Column(Name = "f_key_id_sql", StringLength = -2)]
		public string FKeyIdSql { get; set; }

		[ Column(Name = "key_id_name", StringLength = 50)]
		public string KeyIdName { get; set; }

		/// <summary>
		/// 操作类型，更新or新增
		/// </summary>
		[ Column(Name = "o_type", StringLength = 50)]
		public string OType { get; set; }

		[ Column(Name = "sort")]
		public int? Sort { get; set; }

		[ Column(Name = "table_name", StringLength = 50)]
		public string TableName { get; set; }

		[ Column(Name = "task_form_code", StringLength = 50)]
		public string TaskFormCode { get; set; }

		[ Column(Name = "task_form_name", StringLength = 50)]
		public string TaskFormName { get; set; }

		[ Column(Name = "task_table_name", StringLength = 50)]
		public string TaskTableName { get; set; }

	}

}
