﻿using System.Collections.Concurrent;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using System.Reflection;
using FreeSql;
using FreeSql.Internal;
using FreeSql.Internal.CommonProvider;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Db.FreeSQL;

internal sealed class FreeSqlWrapper<T> : IFreeSql<T>
{
    private int _disposeCounter;
    
    private IFreeSql<T>? _freeSql;

    private ScopedAdoProvider? _scopedAdoProvider;

    [SuppressMessage("ReSharper", "StaticMemberInGenericType")] 
    private static readonly Func<ConcurrentDictionary<Guid, DbContextOptions>> getDbContextOptionsCache;

    static FreeSqlWrapper()
    {

        var fieldInfo = typeof(FreeSqlDbContextExtensions).GetField("_dicSetDbContextOptions",
            BindingFlags.Static | BindingFlags.NonPublic);
        var field = Expression.Field(null, fieldInfo!);
        getDbContextOptionsCache =
            Expression.Lambda<Func<ConcurrentDictionary<Guid, DbContextOptions>>>(field).Compile();
    }

    public FreeSqlWrapper(
        FreeSqlAccessor<T> freeSqlAccessor, 
        IEnumerable<IPostConfigureOptions<IFreeSql>> freeSqlConfiguration)
    {
        _freeSql = freeSqlAccessor.FreeSql;
        
        if (_freeSql.Ado is AdoProvider adoProvider)
        {
            _scopedAdoProvider = new ScopedAdoProvider(adoProvider);
        }
        
        foreach (var postConfigureOptions in freeSqlConfiguration)
        {
            postConfigureOptions.PostConfigure(Options.DefaultName, this);
        }
    }

    private IFreeSql<T> FreeSql
    {
        get
        {
            ObjectDisposedException.ThrowIf(_freeSql == null, this);
            return _freeSql;
        }
    }
    
    public void Dispose()
    {
        if (Interlocked.Increment(ref _disposeCounter) != 1)
        {
            return;
        }

        try
        {
            var options = getDbContextOptionsCache();
            options.TryRemove(Ado.Identifier, out _);

            _scopedAdoProvider?.Dispose();

            _scopedAdoProvider = null;
            _freeSql = null;
        }
        finally
        {
            GC.SuppressFinalize(this);
        }
    }
    
    ~FreeSqlWrapper()
    {
        Dispose();
    }

    public IInsert<T1> Insert<T1>() where T1 : class
    {
        return FreeSql.Insert<T1>();
    }

    public IInsert<T1> Insert<T1>(T1 source) where T1 : class
    {
        return FreeSql.Insert(source);
    }

    public IInsert<T1> Insert<T1>(T1[] source) where T1 : class
    {
        return FreeSql.Insert(source);
    }

    public IInsert<T1> Insert<T1>(List<T1> source) where T1 : class
    {
        return FreeSql.Insert(source);
    }

    public IInsert<T1> Insert<T1>(IEnumerable<T1> source) where T1 : class
    {
        return FreeSql.Insert(source);
    }

    public IInsertOrUpdate<T1> InsertOrUpdate<T1>() where T1 : class
    {
        return FreeSql.InsertOrUpdate<T1>();
    }

    public IUpdate<T1> Update<T1>() where T1 : class
    {
        return FreeSql.Update<T1>();
    }

    public IUpdate<T1> Update<T1>(object dywhere) where T1 : class
    {
        return FreeSql.Update<T1>(dywhere);
    }

    public ISelect<T1> Select<T1>() where T1 : class
    {
        return FreeSql.Select<T1>();
    }

    public ISelect<T1> Select<T1>(object dywhere) where T1 : class
    {
        return FreeSql.Select<T1>(dywhere);
    }

    public IDelete<T1> Delete<T1>() where T1 : class
    {
        return FreeSql.Delete<T1>();
    }

    public IDelete<T1> Delete<T1>(object dywhere) where T1 : class
    {
        return FreeSql.Delete<T1>(dywhere);
    }

    public void Transaction(Action handler)
    {
        FreeSql.Transaction(handler);
    }

    public void Transaction(IsolationLevel isolationLevel, Action handler)
    {
        FreeSql.Transaction(isolationLevel, handler);
    }

    public IAdo Ado => _scopedAdoProvider ?? FreeSql.Ado;

    public IAop Aop => FreeSql.Aop;

    public ICodeFirst CodeFirst => FreeSql.CodeFirst;

    public IDbFirst DbFirst => FreeSql.DbFirst;

    public GlobalFilter GlobalFilter => FreeSql.GlobalFilter;
}