﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
        <remove name="WebDAV" />
      </handlers>
      <modules runAllManagedModulesForAllRequests="true" runManagedModulesForWebDavRequests="true">
        <remove name="WebDAVModule"/>
      </modules>
      <aspNetCore processPath="dotnet" arguments=".\iPlatformExtension.Finance.Fin.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Staging" />
   	    </environmentVariables>
      </aspNetCore>
    </system.webServer>
  </location>
</configuration>
<!--ProjectGuid: 09099385-B0AA-4982-ADB1-B6DA6E7C08A8-->