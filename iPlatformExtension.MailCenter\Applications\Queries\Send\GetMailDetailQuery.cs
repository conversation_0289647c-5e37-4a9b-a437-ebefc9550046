using iPlatformExtension.MailCenter.Applications.Commands.Send;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.Send;

/// <summary>
/// 获取邮件详情查询
/// </summary>
/// <param name="MailId">邮件ID</param>
public record GetMailDetailQuery(string MailId) : IRequest<GetMailDetailDto>;

/// <summary>
/// 邮件详情DTO
/// </summary>
public class GetMailDetailDto
{
    /// <summary>
    /// 邮件ID
    /// </summary>
    public string MailId { get; set; } = string.Empty;

    /// <summary>
    /// 发件人
    /// </summary>
    public List<MailAddressList>? MailFrom { get; set; }

    /// <summary>
    /// 收件人
    /// </summary>
    public List<MailAddressList>? MailTo { get; set; }

    /// <summary>
    /// 抄送人
    /// </summary>
    public List<MailAddressList>? MailCc { get; set; }

    /// <summary>
    /// 密送人
    /// </summary>
    public List<MailAddressList>? MailBcc { get; set; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime? SendTime { get; set; }

    /// <summary>
    /// 主题
    /// </summary>
    public string Subject { get; set; } = string.Empty;

    /// <summary>
    /// 附件
    /// </summary>
    public int? Attachments { get; set; }

    /// <summary>
    /// 转发正文
    /// </summary>
    public string MailRelayBody { get; set; } = string.Empty;

    /// <summary>
    /// 正文
    /// </summary>
    public string HtmlBody { get; set; } = string.Empty;

    /// <summary>
    /// 簽名正文
    /// </summary>
    public string SignatureBody { get; set; } = string.Empty;

    /// <summary>
    /// 紧急
    /// </summary>
    public bool? IsImportant { get; set; }

    /// <summary>
    /// 要求已读回执
    /// </summary>
    public bool? IsRead { get; set; }

    /// <summary>
    /// 是否定时发送
    /// </summary>
    public bool? IsRequiredProcessTime { get; set; }
    
    /// <summary>
    /// 邮箱配置ID
    /// </summary>
    public string HostId { get; set; } = string.Empty;
}
