﻿using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.File;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers;

/// <summary>
/// 文件相关接口
/// </summary>
[ApiController]
[Route("[controller]")]
[Produces(MediaTypeNames.Application.Json)]
public class FileController : ControllerBase
{
    private readonly IMediator _mediator;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="mediator">中介者</param>
    public FileController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// 案件文件选择列表
    /// </summary>
    /// <param name="caseId">案件id</param>
    /// <param name="keyword">查询关键字</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页面大小</param>
    /// <param name="returnUrl">是否返回URL</param>
    /// <returns>文件列表数据</returns>
    [HttpGet("/case/{caseId}/files")]
    public Task<IEnumerable<FileListDto>> GetCaseFilesAsync([Required] string caseId, string? keyword, int pageIndex = 1, int pageSize = 10, bool returnUrl = false)
    {
        return _mediator.Send(new CaseInfoCaseFileQuery(caseId, keyword, pageIndex, pageSize, returnUrl));
    }

    /// <summary>
    /// 申请人附件选择列表
    /// </summary>
    /// <param name="applicantId">申请人id</param>
    /// <param name="keyword">查询关键字</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页面大小</param>
    /// <param name="returnUrl">是否返回URL</param>
    /// <returns>文件列表数据</returns>
    [HttpGet("/applicant/{applicantId}/files")]
    public Task<IEnumerable<FileListDto>> GetApplicantFilesAsync([Required]string applicantId, string? keyword, int? pageIndex,
        int? pageSize, bool returnUrl = false)
    {
        return _mediator.Send(new ApplicantCaseFileQuery(applicantId, keyword, pageIndex, pageSize, returnUrl));
    }

    /// <summary>
    /// 任务申请人附件列表选择
    /// </summary>
    /// <param name="applicantId">申请人id</param>
    /// <param name="businessType">递交业务类型</param>
    /// <returns>文件列表数据</returns>
    [HttpGet("/proc-applicant/{applicantId}/files")]
    public Task<IEnumerable<FileListDto>> GetProcApplicantFilesAsync([Required] string applicantId,
        [FromQuery] string? businessType)
    {
        return _mediator.Send(new ProcApplicantCaseFileQuery(businessType, applicantId));
    }

    /// <summary>
    /// 添加递交文件记录
    /// </summary>
    /// <param name="files">需要保存的文件</param>
    /// <returns>统一接口</returns>
    [HttpPost]
    public Task AddDeliFileAsync(IEnumerable<AddDeliFileDto> files)
    {
        return _mediator.Send(new AddDeliFileCommand(files));
    }

    /// <summary>
    /// 批量更新递交数据对应的文件
    /// </summary>
    /// <param name="files">文件数据</param>
    /// <param name="operation">批量操作选项</param>
    /// <returns>统一响应接口</returns>
    [HttpPut]
    public Task UpdateDeliveryFilesAsync(IEnumerable<AddDeliFileDto> files, [FromQuery, Required] DeliveryFilesOperation operation)
    {
        return _mediator.Send(new UpdateDeliveryFilesCommand(files, operation), HttpContext.RequestAborted);
    }


    /// <summary>
    /// 批量上传案件文件
    /// </summary>
    /// <param name="files">文件数据</param>
    /// <param name="operation">批量操作选项</param>
    /// <returns>统一响应接口</returns>
    [HttpPut("UploadFileToCase")]
    public Task UploadFileToCaseAsync(IEnumerable<UploadFileToCaseDto> files, [FromQuery, Required] DeliveryFilesOperation operation)
    {
        return _mediator.Send(new UploadFileToCaseCammand(files, operation), HttpContext.RequestAborted);
    }

}