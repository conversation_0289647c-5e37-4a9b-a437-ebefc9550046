﻿using iPlatformExtension.Model.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters
{
    /// <summary>
    /// 撤回许可备案
    /// </summary>
    public class WithdrawalLicenseFilingParameters : PhoenixOrderBaseRequestParameters
    {
        /// <summary>
        /// 申请人地址
        /// </summary>
        [JsonPropertyName("applicantAddress")]
        public string ApplicantAddress { get; set; }

        /// <summary>
        /// 申请人地址（英文）
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("applicantEnglishAddress")]
        public string ApplicantEnglishAddress { get; set; }

        /// <summary>
        /// 申请人英文名称
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("applicantEnglishName")]
        public string ApplicantEnglishName { get; set; }

        /// <summary>
        /// 申请人名称
        /// </summary>
        [JsonPropertyName("applicantName")]
        public string ApplicantName { get; set; }

        /// <summary>
        /// 撤回理由
        /// </summary>
        [JsonPropertyName("applyProve")]
        public string ApplyProve { get; set; }

        /// <summary>
        /// 被许可人信息
        /// </summary>
        [JsonPropertyName("assigneeInfo")]
        [JsonSerializationSource(nameof(AssigneeInfoList))]
        public string AssigneeInfo { get; set; }

        public AssigneeInfo AssigneeInfoList { get; set; }

        /// <summary>
        /// 书证类型：1表示大陆,2表示海外,3表示台湾,4表示香港,5表示澳门
        /// </summary>
        [JsonPropertyName("bookType")]
        public int BookType { get; set; }

        /// <summary>
        /// 证件类型：0不是任何类型,1身份证,2护照,3其他
        /// </summary>
        [JsonPropertyName("certificatesType")]
        public int CertificatesType { get; set; }

        /// <summary>
        /// 申请人地址邮编
        /// </summary>
        [JsonPropertyName("code")]
        public string Code { get; set; }

        /// <summary>
        /// 联系人邮箱
        /// </summary>
        [JsonPropertyName("contactEmail")]
        public string ContactEmail { get; set; }

        /// <summary>
        /// 联系人姓名
        /// </summary>
        [JsonPropertyName("contactName")]
        public string ContactName { get; set; }

        /// <summary>
        /// 联系人电话
        /// </summary>
        [JsonPropertyName("contactTel")]
        public string ContactTel { get; set; }

        /// <summary>
        /// 国家或地区
        /// </summary>
        [JsonPropertyName("country")]
        public string Country { get; set; }

        /// <summary>
        /// 证件编号
        /// </summary>
        [JsonPropertyName("idCard")]
        public string IdCard { get; set; }

        /// <summary>
        /// 申请人资质：0个人 1公司
        /// </summary>
        [JsonPropertyName("ownerType")]
        public int OwnerType { get; set; }

        /// <summary>
        /// 机构委托人姓名
        /// </summary>
        [JsonPropertyName("principalName")]
        public string PrincipalName { get; set; }

        /// <summary>
        /// 机构委托人电话
        /// </summary>
        [JsonPropertyName("principalTel")]
        public string PrincipalTel { get; set; }

        /// <summary>
        /// 主体资格类型：1表示中文,0表示非中文
        /// </summary>
        [JsonPropertyName("subjectType")]
        public int SubjectType { get; set; }
    }
}
