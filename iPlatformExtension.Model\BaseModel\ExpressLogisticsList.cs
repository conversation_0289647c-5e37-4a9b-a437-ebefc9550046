using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "express_logistics_list", DisableSyncStructure = true)]
	public partial class ExpressLogisticsList {

		[ Column(Name = "logistic_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string LogisticId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "ebusinessid", StringLength = 50, IsNullable = false)]
		public string Ebusinessid { get; set; }

		[ Column(Name = "logistic_code", StringLength = 50, IsNullable = false)]
		public string LogisticCode { get; set; }

		[ Column(Name = "push_time")]
		public DateTime PushTime { get; set; }

		[ Column(Name = "shipper_code", StringLength = 50, IsNullable = false)]
		public string ShipperCode { get; set; }

	}

}
