using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_info_正式2", DisableSyncStructure = true)]
	public partial class BillInfo正式2 {

		[ Column(Name = "address_cn", StringLength = 2000)]
		public string AddressCn { get; set; }

		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		[ Column(Name = "amount_with_tax", DbType = "money")]
		public decimal? AmountWithTax { get; set; }

		[ Column(Name = "annual_amount", DbType = "money")]
		public decimal? AnnualAmount { get; set; }

		[ Column(Name = "belong_district", StringLength = 50)]
		public string BelongDistrict { get; set; }

		[ Column(Name = "bill_amount", DbType = "money")]
		public decimal? BillAmount { get; set; }

		[ Column(Name = "bill_amount_difference", DbType = "money")]
		public decimal? BillAmountDifference { get; set; }

		[ Column(Name = "bill_id", StringLength = 50, IsNullable = false)]
		public string BillId { get; set; }

		[ Column(Name = "bill_no", StringLength = 50)]
		public string BillNo { get; set; }

		[ Column(Name = "bill_status", StringLength = 50)]
		public string BillStatus { get; set; }

		[ Column(Name = "bill_type", StringLength = 50)]
		public string BillType { get; set; }

		[ Column(Name = "claim_time")]
		public DateTime? ClaimTime { get; set; }

		[ Column(Name = "claim_user", StringLength = 50)]
		public string ClaimUser { get; set; }

		[ Column(Name = "claim_user_id", StringLength = 50)]
		public string ClaimUserId { get; set; }

		[ Column(Name = "company_id", StringLength = 50)]
		public string CompanyId { get; set; }

		[ Column(Name = "contact_id", StringLength = 50)]
		public string ContactId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "currency_id", StringLength = 50)]
		public string CurrencyId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "flow_cur_user_id", StringLength = 50)]
		public string FlowCurUserId { get; set; }

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		[ Column(Name = "flow_update_time")]
		public DateTime? FlowUpdateTime { get; set; }

		[ Column(Name = "has_invoice")]
		public bool? HasInvoice { get; set; }

		[ Column(Name = "has_made_invoice")]
		public bool? HasMadeInvoice { get; set; }

		[ Column(Name = "i_invoices_type", StringLength = 50)]
		public string IInvoicesType { get; set; }

		[ Column(Name = "invoice_amount_gs", StringLength = 50)]
		public string InvoiceAmountGs { get; set; }

		[ Column(Name = "invoice_amount_p", StringLength = 50)]
		public string InvoiceAmountP { get; set; }

		[ Column(Name = "invoice_amount_ps", StringLength = 50)]
		public string InvoiceAmountPs { get; set; }

		[ Column(Name = "invoice_amount_z", StringLength = 50)]
		public string InvoiceAmountZ { get; set; }

		[ Column(Name = "invoice_status", StringLength = 50)]
		public string InvoiceStatus { get; set; }

		[ Column(Name = "is_invoice")]
		public bool? IsInvoice { get; set; }

		[ Column(Name = "language_id", StringLength = 50)]
		public string LanguageId { get; set; }

		[ Column(Name = "nopay_reason", StringLength = 50)]
		public string NopayReason { get; set; }

		[ Column(Name = "nopay_remark", StringLength = 500)]
		public string NopayRemark { get; set; }

		[ Column(Name = "payment_term", StringLength = 50)]
		public string PaymentTerm { get; set; }

		[ Column(Name = "print_time")]
		public DateTime? PrintTime { get; set; }

		[ Column(Name = "print_user_id", StringLength = 50)]
		public string PrintUserId { get; set; }

		[ Column(Name = "receive_bank_id", StringLength = 50)]
		public string ReceiveBankId { get; set; }

		[ Column(Name = "receive_date")]
		public DateTime? ReceiveDate { get; set; }

		[ Column(Name = "receive_due_date")]
		public DateTime? ReceiveDueDate { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "remark_to_fa", StringLength = 2000)]
		public string RemarkToFa { get; set; }

		[ Column(Name = "request_invoice_time")]
		public DateTime? RequestInvoiceTime { get; set; }

		[ Column(Name = "request_object_id", StringLength = 50)]
		public string RequestObjectId { get; set; }

		[ Column(Name = "sales_user_id", StringLength = 50)]
		public string SalesUserId { get; set; }

		[ Column(Name = "send_date")]
		public DateTime? SendDate { get; set; }

		[ Column(Name = "service_amount", DbType = "money")]
		public decimal? ServiceAmount { get; set; }

		[ Column(Name = "tax", DbType = "money")]
		public decimal? Tax { get; set; }

		[ Column(Name = "tax_calculate_type", StringLength = 50)]
		public string TaxCalculateType { get; set; }

		[ Column(Name = "tax_invoices_type", StringLength = 50)]
		public string TaxInvoicesType { get; set; }

		[ Column(Name = "tax_rate", DbType = "money")]
		public decimal? TaxRate { get; set; }

		[ Column(Name = "third_party_amount", DbType = "money")]
		public decimal? ThirdPartyAmount { get; set; }

		[ Column(Name = "title", StringLength = 500)]
		public string Title { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
