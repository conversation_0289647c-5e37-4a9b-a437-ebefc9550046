﻿using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Models.ForeignBill;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Finance.Applications.Handlers.ForeignBill;

internal sealed class BaseDistrictForeignFeeResultHandler(IDistrictRepository districtRepository) : ForeignFeeResultHandlerBase
{
    protected override async ValueTask GetResultAsync(ForeignBillFeeExportDto dto)
    {
        dto.District = await districtRepository.GetTextValueAsync(dto.District ?? string.Empty) ?? string.Empty;
    }
}