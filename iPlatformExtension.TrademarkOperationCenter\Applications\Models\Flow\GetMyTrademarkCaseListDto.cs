﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow
{
    /// <summary>
    /// 流程列表dto
    /// </summary>
    /// <param name="privateId">标签id</param>
    /// <param name="userId">用户id</param>
    /// <param name="flowType">流程类型</param>
    /// <param name="flowSubType">流程子类型</param>
    /// <param name="privateName">标签名字</param>
    /// <param name="isShow">是否默认显示</param>
    /// <param name="seq">排序</param>
    public record GetMyTrademarkCaseListDto(string privateId, string userId, string flowType, string flowSubType,
        string privateName, bool? isShow, int? seq);

}
