﻿using iPlatformExtension.MailCenter.Applications.Models.AuditUser;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.AuditUser
{
    /// <summary>
    /// 获取发件必审人列表查询
    /// </summary>
    /// <param name="AuditingName">必审人名称</param>
    /// <param name="DesignatedAuditName">指定审核人名称</param>
    public record GetFlowAuditUserQuery(string? AuditingName = null, string? DesignatedAuditName = null) : IRequest<IEnumerable<GetFlowAuditUserDto>>;
}
