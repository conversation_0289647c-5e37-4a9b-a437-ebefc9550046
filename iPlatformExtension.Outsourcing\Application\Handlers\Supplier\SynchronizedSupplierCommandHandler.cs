﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Commands.Supplier;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Supplier;

internal sealed class SynchronizedSupplierCommandHandler(
    IMapper mapper,
    IHttpContextAccessor httpContextAccessor,
    IForeignSupplierRepository foreignSupplierRepository) 
    : IRequestHandler<SynchronizedSupplierCommand>
{
    public async Task Handle(SynchronizedSupplierCommand request, CancellationToken cancellationToken)
    {
        var supplier = request.Supplier;
        var id = supplier.Id;
        var userId = httpContextAccessor.HttpContext?.User.GetUserId() ?? string.Empty;
        
        var foreignSupplier = await foreignSupplierRepository.GetAsync(id, cancellationToken);
        if (foreignSupplier is null)
        {
            foreignSupplier = mapper.Map<ForeignSupplier>(supplier);
            foreignSupplier.Updater = userId;
            foreignSupplier.UpdateTime = DateTime.Now;
            
            await foreignSupplierRepository.InsertAsync(foreignSupplier, cancellationToken);
        }
        else
        {
            foreignSupplier.ValidateVersion(supplier.Version);
            
            mapper.Map(supplier, foreignSupplier);
            foreignSupplier.Updater = userId;
            foreignSupplier.UpdateTime = DateTime.Now;
            if (supplier.IsRollback)
            {
                await foreignSupplierRepository.RollbackVersionEntityAsync(foreignSupplier, supplier.Version,
                    cancellationToken);
            }
            else
            {
                await foreignSupplierRepository.UpdateAsync(foreignSupplier, cancellationToken);
            }

            if (!foreignSupplier.IsEnable)
            {
                await foreignSupplierRepository.RemoveCacheValueAsync(foreignSupplier.SupplierId, cancellationToken);
            }
        }
    }
}