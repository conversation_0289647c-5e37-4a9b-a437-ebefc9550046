﻿namespace iPlatformExtension.Model.Dto.TrademarkDelivery;

/// <summary>
/// 申请人附件业务类型
/// </summary>
public sealed class ApplicantAttachmentSubType
{
    private static readonly IReadOnlyDictionary<int, ApplicantAttachmentSubType> attachmentSubTypes;

    private static readonly IReadOnlyDictionary<string, ApplicantAttachmentSubType> descriptionSubTypes;

    /// <summary>
    /// 中文主体资格证明文件(经营许可)
    /// </summary>
    public static readonly ApplicantAttachmentSubType
        ChineseSubjectQualification = new(5, "中文主体资格证明文件(经营许可)");

    /// <summary>
    /// 英文主体资格证明文件(经营许可)
    /// </summary>
    public static readonly ApplicantAttachmentSubType
        ForeignSubjectQualification = new(8, "英文主体资格证明文件(经营许可)");

    /// <summary>
    /// 身份证明文件(中文)
    /// </summary>
    public static readonly ApplicantAttachmentSubType
        ChineseIdentification = new(6, "身份证明文件(中文)");

    /// <summary>
    /// 身份证明文件(外文)
    /// </summary>
    public static readonly ApplicantAttachmentSubType
        ForeignIdentification = new(9, "身份证明文件(外文)");

    /// <summary>
    /// 有关说明文件
    /// </summary>
    public static readonly ApplicantAttachmentSubType RelevantNotes = new(10, "有关说明文件");

    /// <summary>
    /// 委托书
    /// </summary>
    public static readonly ApplicantAttachmentSubType Attorney = new(3, "委托书");

    /// <summary>
    /// 受让人主体资格证明文件（中文）
    /// </summary>
    public static readonly ApplicantAttachmentSubType TransfereeSubjectQualification = new (12, "受让人主体资格证明文件（中文）");

    /// <summary>
    /// 受让人身份证明文件（中文）
    /// </summary>
    public static readonly ApplicantAttachmentSubType AssignChineseIdentification = new(14, "受让人身份证明文件（中文）");
    
    /// <summary>
    /// 被委托书
    /// </summary>
    public static readonly ApplicantAttachmentSubType Attorneyed = new(16, "被委托书");

    /// <summary>
    /// 转让声明文件
    /// </summary>
    public static readonly ApplicantAttachmentSubType TransferDeclarationDocument = new(17, "转让声明文件");

    /// <summary>
    /// 变更证明文件
    /// </summary>
    public static readonly ApplicantAttachmentSubType ChangeLicensor = new (18, "变更证明文件");

    /// <summary>
    /// 理由书
    /// </summary>
    public static readonly ApplicantAttachmentSubType Justification = new(26, "理由书");

    /// <summary>
    /// 证据目录
    /// </summary>
    public static readonly ApplicantAttachmentSubType EvidenceLists = new(27, "证据目录");

    /// <summary>
    /// 证据内容
    /// </summary>
    public static readonly ApplicantAttachmentSubType EvidenceContent = new(28, "证据内容");

    /// <summary>
    /// 送达证据
    /// </summary>
    public static readonly ApplicantAttachmentSubType DeliverEvidence = new(38, "送达证据");

    /// <summary>
    /// 分割申请书
    /// </summary>
    public static readonly ApplicantAttachmentSubType SplitApplication = new(39, "分割申请书");
   
    /// <summary>
    /// 同意撤回声明文件
    /// </summary>
    public static readonly ApplicantAttachmentSubType AgreeWithdrawDeclarationDocument = new (216, "同意撤回声明文件");

    /// <summary>
    /// 评审网申国际材料
    /// </summary>
    public static readonly ApplicantAttachmentSubType InternationalTrademarkMaterials = new(244, "评审网申国际商标材料");

    /// <summary>
    /// 其他委托书
    /// </summary>
    public static readonly ApplicantAttachmentSubType OtherAttorney = new(299, "其他委托书");

    static ApplicantAttachmentSubType()
    {
        attachmentSubTypes = new Dictionary<int, ApplicantAttachmentSubType>()
        {
            {3, Attorney},
            {5, ChineseSubjectQualification},
            {6, ChineseIdentification},
            {8, ForeignSubjectQualification},
            {9, ForeignIdentification},
            {10, RelevantNotes},
            {12, TransfereeSubjectQualification},
            {16, Attorneyed},
            {17, TransferDeclarationDocument},
            {18, ChangeLicensor},
            {26, Justification},
            {27, EvidenceLists},
            {28, EvidenceContent},
            {38, DeliverEvidence},
            {39, SplitApplication},
            {216, AgreeWithdrawDeclarationDocument},
            {244, InternationalTrademarkMaterials},
            {299, OtherAttorney}
        };

        descriptionSubTypes = new Dictionary<string, ApplicantAttachmentSubType>()
        {
            {"许可人委托书", Attorney},
            {"主体资格证明文件(中文)", ChineseSubjectQualification},
            {"身份证明文件(中文)", ChineseIdentification},
            {"主体资格证明原文件(外文)", ForeignSubjectQualification},
            {"身份证明原文件(外文)", ForeignIdentification},
            {"有关说明文件", RelevantNotes},
            {"受让人主体资格证明文件(中文)", TransfereeSubjectQualification},
            {"被委托书", Attorneyed},
            {"转让声明文件", TransferDeclarationDocument},
            {"变更证明文件", ChangeLicensor},
            {"理由书", Justification},
            {"证据目录", EvidenceLists},
            {"证据内容", EvidenceContent},
            {"送达证据", DeliverEvidence},
            {"分割申请书", SplitApplication},
            {"同意撤回声明文件", AgreeWithdrawDeclarationDocument},
            {"评审网申国际商标材料", InternationalTrademarkMaterials},
            {"其他委托书", OtherAttorney}
        };
    }

    /// <summary>
    /// 尝试获取申请人附件子类型
    /// </summary>
    /// <param name="code">类型编码</param>
    /// <param name="attachmentSubType">附件子类型</param>
    /// <returns>存在返回true，不存在返回false</returns>
    public static bool TryGetAttachmentSubType(int code, out ApplicantAttachmentSubType? attachmentSubType)
    {
        return attachmentSubTypes.TryGetValue(code, out attachmentSubType);
    }

    /// <summary>
    /// 根据编码直接获取对应的枚举类型
    /// </summary>
    /// <param name="code">编码值</param>
    /// <returns>枚举的附件类型</returns>
    /// <exception cref="ArgumentOutOfRangeException">编码值不在给定的枚举范围</exception>
    /// <exception cref="NullReferenceException">编码对应的枚举附件类型不存在</exception>
    public static ApplicantAttachmentSubType GetAttachmentSubType(int code)
    {
        if (!TryGetAttachmentSubType(code, out var subType))
            throw new ArgumentOutOfRangeException(nameof(code));

        if (subType is null)
        {
            throw new NullReferenceException($"编码{code}不存在对应的附件子类型");
        }

        return subType;

    }

    /// <summary>
    /// 获取权大师对应的文件描述
    /// </summary>
    /// <param name="description">业务系统的文件描述</param>
    /// <returns>权大师交官的附件描述</returns>
    /// <exception cref="NullReferenceException">不存对应的文件描述设置</exception>
    public static ApplicantAttachmentSubType GetDescriptionAttachmentSubType(string description)
    {
        if (descriptionSubTypes.TryGetValue(description, out var subType))
        {
            return subType;
        }

        throw new NullReferenceException($"文件描述：{description}不存在对应的附件类型");
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="code">权大师文件类型编码</param>
    /// <param name="description">权大师文件描述</param>
    public ApplicantAttachmentSubType(int code, string description)
    {
        Code = code;
        Description = description;
    }

    /// <summary>
    /// 编码
    /// </summary>
    public int Code { get; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; }
}