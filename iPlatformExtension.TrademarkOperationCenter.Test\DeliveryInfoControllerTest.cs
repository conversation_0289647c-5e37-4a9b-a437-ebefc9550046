﻿using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using System.Text.Json.Serialization;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Enum;
using Microsoft.AspNetCore.JsonPatch;

namespace iPlatformExtension.TrademarkOperation.Test;

public class DeliveryInfoControllerTest(TrademarkCenterWebApplicationFactory factory) 
    : IClassFixture<TrademarkCenterWebApplicationFactory>
{
    private readonly HttpClient _httpClient = factory.CreateClient();
    
    private readonly JsonSerializerOptions _serializerOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        Converters = { new JsonStringEnumConverter() }
    };

    [Fact]
    public async Task SaveAsync_ShouldReturnCreatedResult()
    {
        var procId = "testProcId";
        var response = await _httpClient.PostAsync($"DeliveryInfo/{procId}", null);

        Assert.Equal(HttpStatusCode.Created, response.StatusCode);
    }

    [Fact]
    public async Task GetAsync_ShouldReturnOkResult()
    {
        var procId = "testProcId";
        var response = await _httpClient.GetAsync($"DeliveryInfo/{procId}");

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    public async Task GetStatusAsync_ShouldReturnOkResult()
    {
        var procId = "testProcId";
        var response = await _httpClient.GetAsync($"DeliveryInfo/{procId}/status");

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    public async Task CreateDeliveryFilesAsync_ShouldReturnOkResult()
    {
        var procId = "testProcId";
        var caseFileIds = new List<string> { "file1", "file2" };
        var response = await _httpClient.PostAsJsonAsync($"DeliveryInfo/{procId}/files", caseFileIds, _serializerOptions);

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Theory]
    [InlineData("")]
    public async Task DeleteDeliveryFilesAsync_ShouldReturnOkResult(string procId, params string[] fileIds)
    {
        
        var response = await _httpClient.DeleteAsJsonAsync($"DeliveryInfo/{procId}/files", fileIds, _serializerOptions);

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Theory]
    [InlineData("", DeliveryButton.StartUpDelivery)]
    public async Task SendDeliveryAsync_ShouldReturnNoContentResult(string procId, DeliveryButton button)
    {
        var response = await _httpClient.PutAsync($"DeliveryInfo/{procId}/order?button={button}", null);

        Assert.Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Theory]
    [InlineData(DeliveryButton.StartUpDelivery)]
    public async Task SendBatchDeliveriesAsync_ShouldReturnNoContentResult(DeliveryButton button, params string[] procIds)
    {
        
        var response = await _httpClient.PostAsJsonAsync($"DeliveryInfo/orders?button={button}", procIds, _serializerOptions);

        Assert.Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    public async Task GetHistoriesAsync_ShouldReturnOkResult()
    {
        var procId = "testProcId";
        var response = await _httpClient.GetAsync($"DeliveryInfo/{procId}/histories");

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    public async Task GetResultAsync_ShouldReturnOkResult()
    {
        var resultId = 1;
        var response = await _httpClient.GetAsync($"DeliveryInfo/result/{resultId}");

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    public async Task UpdateResultAsync_ShouldReturnNoContentResult()
    {
        var procId = "testProcId";
        var dto = new DeliveryResultDto { Success = true, Operation = TrademarkDeliveryOperation.SubmitOfficial };
        var response = await _httpClient.PostAsJsonAsync($"DeliveryInfo/{procId}/result", dto, _serializerOptions);

        Assert.Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnNoContentResult()
    {
        var procId = "testProcId";
        var response = await _httpClient.DeleteAsync($"DeliveryInfo/{procId}");

        Assert.Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnNoContentResult()
    {
        var procId = "testProcId";
        var patchDocument = new JsonPatchDocument<DeliInfo>();
        
        var response = await _httpClient.PatchAsJsonAsync($"DeliveryInfo/{procId}", patchDocument, _serializerOptions);

        Assert.Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Theory]
    [InlineData(false)]
    public async Task CreateOrUpdateBatchAsync_ShouldReturnOkResult(bool refresh, params DeliveryItem[] procItems)
    {
        
        var response = await _httpClient.PutAsJsonAsync($"DeliveryInfo/batch?refresh={refresh}", procItems);

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Theory]
    [InlineData()]
    public async Task DeleteBatchAsync_ShouldReturnNoContentResult(params DeliveryItem[] procItems)
    {
        var response = await _httpClient.DeleteAsJsonAsync($"DeliveryInfo/batch", procItems);

        Assert.Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    public async Task GetBatchAsync_ShouldReturnOkResult()
    {
        var procIds = new List<string> { "proc1", "proc2" };
        var response = await _httpClient.PostAsJsonAsync($"DeliveryInfo/batch", procIds, _serializerOptions);

        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Theory]
    [InlineData(BatchDeliveriesValidateType.Deliver, "711BF1AC-C2A3-4A91-8F62-4F6E9469E16B")]
    public async Task ValidateBatchDeliveriesAsync_ShouldReturnSuccessResult(BatchDeliveriesValidateType validateType, params string[] procIds)
    {
        
        var response = await _httpClient.PostAsJsonAsync($"DeliveryInfo/batch/delivery/validation?validateType={validateType}", procIds, _serializerOptions);
        var result = await response.Content.ReadFromJsonAsync<ResultData>(_serializerOptions);

        Assert.NotNull(result);
        Assert.False(result.Success);
    }
}
