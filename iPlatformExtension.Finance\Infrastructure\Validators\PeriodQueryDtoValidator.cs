﻿using FluentValidation;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Infrastructure.Validators;

internal class PeriodQueryDtoValidator : AbstractValidator<PeriodQueryDto>
{
    public PeriodQueryDtoValidator()
    {
        RuleFor(dto => dto).Must(dto =>
        {
            var startTime = dto.StartTime;
            var endTime = dto.EndTime;
            if (startTime.HasValue && endTime.HasValue && startTime.Value > endTime.Value)
            {
                return false;
            }

            return true;
        }).WithMessage("开始时间不能大于结束时间");

        // RuleFor(dto => dto)
        //     .Must(dto => dto is {StartTime: not null, EndTime: not null} ||
        //                  (!dto.StartTime.HasValue && !dto.EndTime.HasValue)).WithMessage("请给出合适的时间段作为查询参数");
    }
    
}