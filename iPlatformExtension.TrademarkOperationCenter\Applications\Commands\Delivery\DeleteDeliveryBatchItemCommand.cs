﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

internal sealed record DeleteDeliveryBatchItemCommand(string ProcId, int? Version) :
    IFreeSqlUnitOfWorkCommand<PlatformFreeSql>, 
    IDeliveryBatchItemCommand, 
    IRequest<DeliveryItemOperationResult>;