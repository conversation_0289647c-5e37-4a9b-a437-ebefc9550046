﻿using FileMigration.Applications.Queries;
using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Clients.IPlatformWeb;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace FileMigration.Applications.Handlers;

public sealed class PlatformFileUriQueryHandler : IRequestHandler<PlatformFileUriQuery, string>
{
    private readonly HuaweiObsClient _huaweiObsClient;

    private readonly PlatformFileClient _platformFileClient;

    private readonly IFreeSql _freeSql;

    public PlatformFileUriQueryHandler(HuaweiObsClient huaweiObsClient, PlatformFileClient platformFileClient, IFreeSql freeSql)
    {
        _huaweiObsClient = huaweiObsClient;
        _platformFileClient = platformFileClient;
        _freeSql = freeSql;
    }

    public async Task<string> Handle(PlatformFileUriQuery request, CancellationToken cancellationToken)
    {
        var fileInfo = request.FileInfo;
        var platformFileResponse = await _platformFileClient.GetFileAsync(fileInfo.ServerPath, fileInfo.FileName);
        platformFileResponse.EnsureSuccessStatusCode();

        var objectName = Path.Combine(fileInfo.ServerPath, fileInfo.FileName).Replace('\\', '/');
        var putObjectResponse = await _huaweiObsClient.PutFileAsync(objectName,
            await platformFileResponse.Content.ReadAsStreamAsync(cancellationToken));

        var responseStatusCode = (int)putObjectResponse.StatusCode;
        if (responseStatusCode is < 200 or >= 300) 
            throw new FileLoadException("文件同步到华为云失败", fileInfo.Id.ToString());
        
        await _freeSql.Update<FileListA>(fileInfo.Id).Set(a => a.Bucket, _huaweiObsClient.Bucket)
            .Set(a => a.Migration, true).ExecuteAffrowsAsync(cancellationToken);
        return _huaweiObsClient.GenerateTemporaryUrl(objectName, _huaweiObsClient.Bucket, TimeSpan.FromDays(60)).SignUrl;

    }
}