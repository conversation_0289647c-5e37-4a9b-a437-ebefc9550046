﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Nice;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Nice;

internal sealed class SaveProcNiceCommandHandler(IMediator mediator) : IRequestHandler<SaveProcNiceCommand>
{
    public async Task Handle(SaveProcNiceCommand request, CancellationToken cancellationToken)
    {
        await mediator.Send(new DeleteProcNiceCommand(request.ProcId), cancellationToken);
        
        await mediator.Send(new InsertProcNiceCommand(request.ProcId, request.CustomCategories, request.GrandNumbers), cancellationToken);
    }
}