﻿using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using FreeSql.DataAnnotations;
using MongoDB.Bson.Serialization.IdGenerators;

namespace iPlatformExtension.Model.BaseModel
{
    /// <summary>
    /// 实体更改日志
    /// </summary>
    [Table(Name = "Kafka_produce_log")]
    internal class KafkaProduceLog
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Column(Name = "id", IsPrimary = true, IsIdentity = true)]
        public uint Id { get; set; }

        /// <summary>
        /// mongoDB的主键
        /// </summary>
        [BsonId(IdGenerator = typeof(ObjectIdGenerator))]
        [BsonElement("id")]
        public ObjectId ObjectId { get; set; }

        /// <summary>
        /// 主题
        /// </summary>
        [Column(Name = "topic")]
        [BsonElement("topic")]
        public string Topic { get; set; } = default!;

        /// <summary>
        /// 分区
        /// </summary>
        [Column(Name = "partition")]
        [BsonElement("partition")]
        public string Partition { get; set; } = default!;

        /// <summary>
        /// 调用方法
        /// </summary>
        [Column(Name = "key")]
        [BsonElement("key")]
        public DateTime Key { get; set; }

        /// <summary>
        /// 信息
        /// </summary>
        [Column(Name = "message")]
        [BsonElement("message")]
        public string Message { get; set; } = default!;

        /// <summary>
        /// 变化类型
        /// </summary>
        [Column(Name = "change_type")]
        [BsonElement("change_type")]
        public int ChangeType { get; set; }

        /// <summary>
        /// 操作人员
        /// </summary>
        [Column(Name = "operator")]
        [BsonElement("operator")]
        public string Operator { get; set; } = default!;

        /// <summary>
        /// 请求追踪id
        /// </summary>
        [Column(Name = "trace_id")]
        [BsonElement("trace_id")]
        public string TraceId { get; set; } = default!;

    }
}
