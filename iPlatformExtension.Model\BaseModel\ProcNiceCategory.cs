﻿using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;
using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 案件商标尼斯分类
/// </summary>
[Table(Name = "proc_nice_category", DisableSyncStructure = true)]
public sealed class ProcNiceCategory 
{

	/// <summary>
	/// 主键
	/// </summary>
	[Hide]
	[Display(Name = "ID", Order = 0)]
	[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
	public long Id { get; set; }

	/// <summary>
	/// 权大师尼斯分类id
	/// </summary>
	[Column(Name = "category_id", StringLength = 50, IsNullable = false)]
	public string CategoryId { get; set; } = "";

	/// <summary>
	/// 商品分类名称
	/// </summary>
	[Display(Name = "商品/服务项", Order = 2)]
	[Column(Name = "category_name", StringLength = 100, IsNullable = false)]
	public string CategoryName { get; set; } = "";

	/// <summary>
	/// 商品分类编码
	/// </summary>
	[Display(Name = "商品编码", Order = 3)]
	[Column(Name = "category_number", StringLength = 50, IsNullable = false)]
	public string CategoryNumber { get; set; } = "";

	/// <summary>
	/// 创建时间
	/// </summary>
	[Column(Name = "creation_time", InsertValueSql = "getdate()")]
	public DateTime CreationTime { get; set; }

	/// <summary>
	/// 大类分类id
	/// </summary>
	[Column(Name = "grand_id", StringLength = 50, IsNullable = false)]
	public string GrandId { get; set; } = "";

	/// <summary>
	/// 大类分类名称
	/// </summary>
	[Column(Name = "grand_name", StringLength = 50, IsNullable = false)]
	public string GrandName { get; set; } = "";

	/// <summary>
	/// 商品分类大类编码
	/// </summary>
	[Display(Name = "类别", Order = 1)]
	[Column(Name = "grand_number", StringLength = 50, IsNullable = false)]
	public string GrandNumber { get; set; } = "";

	/// <summary>
	/// 是否标准分类
	/// </summary>
	[Column(Name = "is_standard")]
	public bool IsStandard { get; set; } = true;

	/// <summary>
	/// 群id
	/// </summary>
	[Column(Name = "parent_id", StringLength = 50, IsNullable = false)]
	public string ParentId { get; set; } = "";

	/// <summary>
	/// 群名称
	/// </summary>
	[Column(Name = "parent_name", StringLength = 50, IsNullable = false)]
	public string ParentName { get; set; } = "";

	/// <summary>
	/// 父项商品分类编码
	/// </summary>
	[Display(Name = "类似群", Order = 4)]
	[Column(Name = "parent_number", StringLength = 50, IsNullable = false)]
	public string ParentNumber { get; set; } = "";

	/// <summary>
	/// 任务id
	/// </summary>
	[Column(Name = "proc_id", StringLength = 50, IsNullable = false)]
	public string ProcId { get; set; } = default!;

	/// <summary>
	/// 更新时间
	/// </summary>
	[Column(Name = "update_time", InsertValueSql = "getdate()")]
	public DateTime UpdateTime { get; set; }

	/// <summary>
	/// 次序
	/// </summary>
	[Display(Name = "次序", Order = 5)]
	[Hide]
	[Column(Name = "order")]
	public int Order { get; set; }

}