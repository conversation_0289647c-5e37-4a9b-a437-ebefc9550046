﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Commands.Applicant;
using iPlatformExtension.Public.Applications.Queries.Applicant;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Applicant;

internal sealed class Applicant<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(IMediator mediator) : 
    IRequestHandler<ApplicantKeywordQuery, IEnumerable<ApplicantInfo>>
{
    public async Task<IEnumerable<ApplicantInfo>> Handle(ApplicantKeywordQuery request, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.Keyword))
        {
            return Array.Empty<ApplicantInfo>();
        }
        
        var query = await mediator.Send(new BuildApplicantQueryCommand(request), cancellationToken);

        return await query.ToListAsync(
            applicant => new ApplicantInfo(applicant.ApplicantId, applicant.ApplicantNameCn, applicant.ApplicantNameEn),
            cancellationToken);
    }
}