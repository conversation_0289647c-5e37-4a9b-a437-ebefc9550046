﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailService.Applications.Queries;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.SignalR.Client;
using System.Collections.Generic;
using System.Threading;

namespace iPlatformExtension.MailService.HostedService
{
    public class ToDoListCountHostedService(
        ILogger<MailReceiveHostedService> logger, IFreeSql<MailCenterFreeSql> freeSql,
        IConfiguration configuration, IMediator mediator,
        HubConnection hubConnection) : BackgroundService
    {
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await mediator.Send(new FlowPrivateMessageQuery(), stoppingToken);
        }
    }
}
