using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery;

internal sealed class SubmitOrderCommandHandler(
    ObjectPool<StringBuilder> stringBuilderPool,
    IDeliveryInfoRepository deliveryInfoRepository,
    IDeliveryHistoryRepository deliveryHistoryRepository,
    UnitOfWorkManager unitOfWorkManager,
    ILoggerFactory loggerFactory,
    PhoenixClientFactory phoenixClientFactory)
    :
        PhoenixDeliveryHandleBase<SubmitOrderCommand, OrderSubmitParameters, PhoenixResponseParameters<OrderData>>(
            stringBuilderPool, deliveryInfoRepository, deliveryHistoryRepository, unitOfWorkManager, loggerFactory,
            phoenixClientFactory),
        IDeliveryHandler<SubmitOrderCommand, OrderSubmitParameters, PhoenixResponseParameters<OrderData>>
{
    public override TrademarkDeliveryOperation DeliveryOperation => TrademarkDeliveryOperation.StartupDelivery;

    public override Task<OrderSubmitParameters> CreateParameterAsync(CancellationToken cancellationToken = default)
    {
       ArgumentNullException.ThrowIfNull(_deliveryInfo);
       
       var niceClassNumbers = _deliveryInfo.OtherInfo?.TrademarkNiceClasses?.Split(';',
               StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries)
           .Select(category => category.PadLeft(2, '0'));
       
        return Task.FromResult(new OrderSubmitParameters()
        {
            OrderNo = _deliveryInfo.OrderNo,
            NiceClassNumbers = niceClassNumbers?.Any() ?? false ? string.Join(',',  niceClassNumbers) : null,
            ProxyNumber = _deliveryInfo.CtrlProcId == CtrlProcIds.TrademarkRegistration ? _deliveryInfo.Volume : null,
            SubmitKey = _deliveryInfo.DeliveryKey
        });
    }

    public override async Task<PhoenixResponseParameters<OrderData>?> HandleRemoteDeliveryAsync(OrderSubmitParameters request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_phoenixClient);
        return await _phoenixClient.OperateOrderAsync(PhoenixUri.SubmitOrder, request);
    }

    public override async Task HandleDeliveryInfoAsync(PhoenixResponseParameters<OrderData> response, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(_deliveryInfo);
        
        const int newStatus = (int) DeliveryStatus.Delivering;
        await Handler.UpdateDeliveryInfoInternalAsync(deliveryInfo =>
        {
            deliveryInfo.IsAuto = true;
            deliveryInfo.Status = newStatus;
            deliveryInfo.OperationResult = true;
            return ValueTask.CompletedTask;
        }, cancellationToken);
    }
}