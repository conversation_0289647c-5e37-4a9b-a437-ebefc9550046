﻿using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Clients.HuaweiObs.Options;
using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.Common.Extensions;

public static class HuaweiObsClientExtension
{
    public static void AddHuaweiObsClient(this IServiceCollection services, string optionsPath = "HuaweiObs")
    {
        ArgumentNullException.ThrowIfNull(services);
        services.AddOptions<ObsClientOptions>().BindConfiguration(optionsPath).Validate(options =>
        {
            var validateResult = true;
            validateResult = validateResult && !string.IsNullOrWhiteSpace(options.Host);
            validateResult = validateResult && !string.IsNullOrWhiteSpace(options.AccessKey);
            validateResult = validateResult && !string.IsNullOrWhiteSpace(options.SecretKey);
            validateResult = validateResult && !string.IsNullOrWhiteSpace(options.DefaultBucketName);
            return validateResult;
        });

        services.AddSingleton<HuaweiObsClient>();
    }
    
    public static void AddHuaweiObsClient(this IServiceCollection services, Action<ObsClientOptions> configureOptions)
    {
        ArgumentNullException.ThrowIfNull(services);
        services.AddOptions<ObsClientOptions>().Configure(configureOptions).Validate(options =>
        {
            var validateResult = true;
            validateResult = validateResult && !string.IsNullOrWhiteSpace(options.Host);
            validateResult = validateResult && !string.IsNullOrWhiteSpace(options.AccessKey);
            validateResult = validateResult && !string.IsNullOrWhiteSpace(options.SecretKey);
            validateResult = validateResult && !string.IsNullOrWhiteSpace(options.DefaultBucketName);
            return validateResult;
        });

        services.AddSingleton<HuaweiObsClient>();
    }
}