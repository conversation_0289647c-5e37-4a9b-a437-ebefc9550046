﻿using System.Collections.Frozen;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.File;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File;

internal sealed class TrademarkTransferProcApplicantCaseFileDescriptionQueryHandler(IFileDescriptionRepository fileDescriptionRepository) : 
    IMatchTrademarkDeliveryBusinessTypeNotificationHandler<ProcApplicantCaseFileDescriptionQuery>
{
    private static readonly IReadOnlyList<string> matchBusinessTypes;
    
    private static readonly FrozenDictionary<string, FileDescriptionKey> descriptionKeys;

    static TrademarkTransferProcApplicantCaseFileDescriptionQueryHandler()
    {
        matchBusinessTypes = ["1", "2"];
        descriptionKeys = new Dictionary<string, FileDescriptionKey>
        {
            {"5", new FileDescriptionKey("递交官方", "转让人营业执照(中文)")},
            {"8", new FileDescriptionKey("递交官方", "转让人营业执照(外文)")},
            {"6", new FileDescriptionKey("递交官方", "转让人身份证明(中文)")},
            {"9", new FileDescriptionKey("递交官方", "转让人身份证明(外文)")},
        }.ToFrozenDictionary();
    }

    /// <inheritdoc />
    IEnumerable<string> IMatchTrademarkDeliveryBusinessTypeNotificationHandler<ProcApplicantCaseFileDescriptionQuery>.MatchBusinessTypes => matchBusinessTypes;

    /// <inheritdoc />
    IReadOnlyDictionary<string, FileDescriptionKey> IMatchTrademarkDeliveryBusinessTypeNotificationHandler<ProcApplicantCaseFileDescriptionQuery>.DescriptionKeys => descriptionKeys;

    /// <inheritdoc />
    IFileDescriptionRepository IMatchTrademarkDeliveryBusinessTypeNotificationHandler<ProcApplicantCaseFileDescriptionQuery>.FileDescriptionRepository => fileDescriptionRepository;
}