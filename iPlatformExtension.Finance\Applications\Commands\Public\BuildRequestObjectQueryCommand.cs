using FreeSql;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Commands.Public;

/// <summary>
/// 构建请款对象的查询命令
/// </summary>
public class BuildRequestObjectQueryCommand : IRequest<ISelect<CusRequestObject>>
{
    /// <summary>
    /// 请款对象名称
    /// </summary>
    public string? Name { get; init; }

    /// <summary>
    /// 账单ID
    /// </summary>
    public string? BillId { get; init; }

    /// <summary>
    /// 客户ID集合
    /// </summary>
    public IEnumerable<string> CustomerIds { get; init; } = Array.Empty<string>();

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool IsEnable { get; init; } = true;
}