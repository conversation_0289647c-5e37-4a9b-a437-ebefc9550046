using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_customer_type", DisableSyncStructure = true)]
	public partial class BasCustomerType {

		/// <summary>
		/// 客户等级ID
		/// </summary>
		[ Column(Name = "customer_type_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CustomerTypeId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 客户等级英文
		/// </summary>
		[ Column(Name = "customer_type_en_us", StringLength = 100)]
		public string CustomerTypeEnUs { get; set; }

		/// <summary>
		/// 客户等级日文
		/// </summary>
		[ Column(Name = "customer_type_ja_jp", StringLength = 100)]
		public string CustomerTypeJaJp { get; set; }

		/// <summary>
		/// 客户等级中文
		/// </summary>
		[ Column(Name = "customer_type_zh_cn", StringLength = 50)]
		public string CustomerTypeZhCn { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
