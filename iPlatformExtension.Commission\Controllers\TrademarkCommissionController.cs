using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Net.Mime;
using System.Threading.Channels;
using System.Web;
using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Commission.Application.Commands.Trademark.Foreign;
using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Commission.Application.Queries.Authrorization;
using iPlatformExtension.Commission.Application.Queries.Trademark.Domestic;
using iPlatformExtension.Commission.Application.Queries.Trademark.Foreign;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using Newtonsoft.Json.Serialization;

namespace iPlatformExtension.Commission.Controllers;

/// <summary>
/// 商标提成控制器
/// </summary>
/// <param name="mediator"></param>
[ApiController]
[Route("trademark-commission")]
[Authorize(AuthenticationSchemes = PlatformAuthOptions.SchemeName)]
public sealed class TrademarkCommissionController(IMediator mediator) 
    : ControllerBase
{
    private const string DomesticOperatorRole = "国内商标报表操作（提成统计）";

    private const string ForeignOperatorRole = "出口商标报表操作（提成统计）";
    
    /// <summary>
    /// 创建国内商标权值
    /// </summary>
    /// <param name="channel">创建队列</param>
    [HttpPost("domestic-weight")]
    [AllowAnonymous]
    public ValueTask RefreshDomesticCommissionWeightAsync([FromServices] Channel<CreateDomesticCommissionWeightCommand> channel)
    {
        return channel.Writer.WriteAsync(new CreateDomesticCommissionWeightCommand
        {
            TraceParentId = Activity.Current?.ParentId ?? string.Empty
        });
    }
    
    /// <summary>
    /// 创建出口商标权值
    /// </summary>
    /// <param name="channel">创建队列</param>
    [HttpPost("foreign-weight")]
    [AllowAnonymous]
    public ValueTask RefreshForeignCommissionWeightAsync([FromServices] Channel<CreateForeignCommissionWeightCommand> channel)
    {
        return channel.Writer.WriteAsync(new CreateForeignCommissionWeightCommand
        {
            TraceParentId = Activity.Current?.ParentId ?? string.Empty
        });
    }

    /// <summary>
    /// 查询用户权值统计
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页面大小</param>
    /// <param name="districtCode">地区编码</param>
    /// <param name="keyword">关键词</param>
    /// <returns>合计后的用户提成权值统计</returns>
    [HttpGet("domestic-weight")]
    public async Task<IEnumerable<UserCommissionWeightStatistics>> GetUserCommissionWeightStatisticsAsync(
        [Required] DateOnly startDate,
        [Required] DateOnly endDate,
        [Required] int pageIndex,
        [Required] int pageSize,
        string? districtCode,
        string? keyword)
    {
        var deptIds = await mediator.Send(new AuthorizationDeptIdsQuery(), HttpContext.RequestAborted);
        
        var query = new DomesticCommissionQuery(new DateRange(startDate, endDate), districtCode, keyword, deptIds, pageIndex, pageSize);
        
        return await mediator.Send(query, HttpContext.RequestAborted);
    }

    /// <summary>
    /// 查询国内商标用户权值明细
    /// </summary>
    /// <param name="userId">用户id</param>
    /// <param name="month">年</param>
    /// <param name="year">月</param>
    /// <param name="keyword">查询关键字</param>
    /// <returns>用户任务权值明细</returns>
    [HttpGet("domestic-weight/{userId}/{year:int}/{month:int}")]
    [Authorize("国内商标权值用户明细")]
    public Task<IEnumerable<UserCommissionWeightProcDetail>> GetDomesticUserWeightProcDetailAsync(
        [Required] string userId,
        [Required] int month,
        [Required] int year,
        string? keyword)
    {
        var query = new DomesticWeightUserDetailQuery(userId, month, year, keyword);
        return mediator.Send(query, HttpContext.RequestAborted);
    }
    
    /// <summary>
    /// 查询出口商标用户权值明细
    /// </summary>
    /// <param name="userId">用户id</param>
    /// <param name="month">年</param>
    /// <param name="year">月</param>
    /// <param name="keyword">查询关键字</param>
    /// <returns>用户任务权值明细</returns>
    [HttpGet("foreign-weight/{userId}/{year:int}/{month:int}")]
    [Authorize("出口商标权值用户明细")]
    public Task<IEnumerable<UserCommissionWeightProcDetail>> GetForeignUserWeightProcDetailAsync(
        [Required] string userId,
        [Required] int month,
        [Required] int year,
        string? keyword)
    {
        var query = new ForeignWeightUserDetailQuery(userId, year, month, keyword);
        return mediator.Send(query, HttpContext.RequestAborted);
    }

    /// <summary>
    /// 更新国内商标权值明细
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <param name="patchDocument">权值更新数据</param>
    /// <returns></returns>
    [HttpPatch("domestic-weight/{procId}")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    [Authorize(Roles = DomesticOperatorRole)]
    public Task UpdateDomesticWeightAsync(string procId, JsonPatchDocument<DomesticWeightPatchDto> patchDocument)
    {
        return mediator.Send(new UpdateDomesticWeightCommand(procId, patchDocument), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 批量更新国内商标权值明细
    /// </summary>
    /// <param name="documents">批量更新的jsonPatchDocument参数</param>
    /// <exception cref="Exception">存在部分更失败在抛出异常</exception>
    [HttpPatch("domestic-weight")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    [Authorize(Roles = DomesticOperatorRole)]
    public async Task UpdateDomesticWeightsAsync(List<DomesticWeightJsonPatchDocument> documents)
    {
        foreach (var jsonPatchDocument in documents)
        {
            var procId = jsonPatchDocument.ProcId;
            await mediator.Send(new UpdateDomesticWeightCommand(procId, jsonPatchDocument, true), HttpContext.RequestAborted);
        }

        var exceptionHandlerFeature = HttpContext.Features.Get<IExceptionHandlerFeature>();
        if (exceptionHandlerFeature?.Error is not null)
        {
            throw exceptionHandlerFeature.Error;
        }
    }
    
    /// <summary>
    /// 更新出口商标权值明细
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <param name="patchDocument">权值更新数据</param>
    /// <returns></returns>
    [HttpPatch("foreign-weight/{procId}")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    [Authorize(Roles = ForeignOperatorRole)]
    public Task UpdateForeignWeightAsync(string procId, JsonPatchDocument<ForeignWeightPatchDto> patchDocument)
    {
        return mediator.Send(new UpdateForeignWeightCommand(procId, patchDocument), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 国内商标用户权值统计导出
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="districtCode">地区编码</param>
    /// <param name="keyword">关键词</param>
    /// <returns></returns>
    [HttpGet("domestic-weight/statistics/excel")]
    [Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", MediaTypeNames.Application.Json)]
    public async Task<IActionResult> ExportDomesticUserWeightStatisticsAsync(
        [Required] DateOnly startDate,
        [Required] DateOnly endDate,
        string? districtCode,
        string? keyword)
    {
        var deptIds = await mediator.Send(new AuthorizationDeptIdsQuery(), HttpContext.RequestAborted);
        const int pageIndex = 1;
        const int pageSize = 500;
        
        const int currentIndex = 0;
        const string dataIndexName = nameof(currentIndex);
        
        HttpContext.Items[dataIndexName] = currentIndex;
        
        const string downloadName = "商标提成统计.xlsx";
        
        var query = new DomesticCommissionQuery(new DateRange(startDate, endDate), districtCode, keyword, deptIds, pageIndex, pageSize);

        return await this.PageExportExcelAsync(query,
            new ExcelExportOptions<DomesticCommissionQuery, UserCommissionWeightStatistics>(downloadName,
                TempDirectory.DomesticTrademarkCommission)
            {
                OnQueried = OnQueried
            }, HttpContext.RequestAborted);

        ValueTask OnQueried(object sender, IEnumerable<UserCommissionWeightStatistics> results, CancellationToken _)
        {
            if (sender is not ControllerBase controller)
            {
                return ValueTask.CompletedTask;
            }

            var items = controller.HttpContext.Items;
            var index = items[dataIndexName] as int? ?? 0;
            var i = 0;

            foreach (var userCommissionWeightStatistics in results)
            {
                userCommissionWeightStatistics.SerialNumber += index;
                ++i;
            }

            index += i;
            items[dataIndexName] = index;

            return ValueTask.CompletedTask;
        }
    }
    
    /// <summary>
    /// 国内商标用户权值明细导出
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <param name="districtCode">地区编码</param>
    /// <param name="keyword">承办人关键字</param>
    /// <returns>明细数据</returns>
    [HttpGet("domestic-weight/details/excel")]
    [Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", MediaTypeNames.Application.Json)]
    public async Task<IActionResult> ExportDomesticUserWeightDetailsAsync(
        [Required] DateOnly startDate,
        [Required] DateOnly endDate,
        string? districtCode,
        string? keyword)
    {
        var deptIds = await mediator.Send(new AuthorizationDeptIdsQuery(), HttpContext.RequestAborted);
        const int pageIndex = 1;
        const int pageSize = 500;
        
        const int currentIndex = 0;
        const string dataIndexName = nameof(currentIndex);
        
        HttpContext.Items[dataIndexName] = currentIndex;
        
        const string downloadName = "商标提成明细.xlsx";
        
        var query = new DomesticWeightDetailQuery(new DateRange(startDate, endDate), deptIds, districtCode, keyword, pageIndex, pageSize);

        return await this.PageExportExcelAsync(query,
            new ExcelExportOptions<DomesticWeightDetailQuery, ProcWeightDetailExport>(downloadName,
                TempDirectory.DomesticTrademarkCommission)
            {
                OnQueried = OnQueried
            }, HttpContext.RequestAborted);

        ValueTask OnQueried(object sender, IEnumerable<ProcWeightDetailExport> results, CancellationToken _)
        {
            if (sender is not ControllerBase controller)
            {
                return ValueTask.CompletedTask;
            }

            var items = controller.HttpContext.Items;
            var index = items[dataIndexName] as int? ?? 0;
            var i = 0;

            foreach (var userCommissionWeightStatistics in results)
            {
                userCommissionWeightStatistics.SerialNumber += index;
                ++i;
            }

            index += i;
            items[dataIndexName] = index;

            return ValueTask.CompletedTask;
        }
    }
    
    /// <summary>
    /// 出口商标用户权值明细导出
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <param name="districtCode">地区编码</param>
    /// <param name="keyword">承办人关键字</param>
    /// <returns>明细数据</returns>
    [HttpGet("foreign-weight/details/excel")]
    [Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", MediaTypeNames.Application.Json)]
    public async Task<IActionResult> ExportForeignUserWeightDetailsAsync(
        [Required] DateOnly startDate,
        [Required] DateOnly endDate,
        string? districtCode,
        string? keyword)
    {
        var deptIds = await mediator.Send(new AuthorizationDeptIdsQuery(), HttpContext.RequestAborted);
        const int pageIndex = 1;
        const int pageSize = 500;
        
        const int currentIndex = 0;
        const string dataIndexName = nameof(currentIndex);
        
        HttpContext.Items[dataIndexName] = currentIndex;
        
        const string downloadName = "出口商标提成明细.xlsx";
        
        var query = new ForeignWeightDetailQuery(new DateRange(startDate, endDate), deptIds, districtCode, keyword, pageIndex, pageSize);

        return await this.PageExportExcelAsync(query,
            new ExcelExportOptions<ForeignWeightDetailQuery, ProcWeightDetailExport>(downloadName,
                TempDirectory.DomesticTrademarkCommission)
            {
                OnQueried = OnQueried
            }, HttpContext.RequestAborted);

        ValueTask OnQueried(object sender, IEnumerable<ProcWeightDetailExport> results, CancellationToken _)
        {
            if (sender is not ControllerBase controller)
            {
                return ValueTask.CompletedTask;
            }

            var items = controller.HttpContext.Items;
            var index = items[dataIndexName] as int? ?? 0;
            var i = 0;

            foreach (var userCommissionWeightStatistics in results)
            {
                userCommissionWeightStatistics.SerialNumber += index;
                ++i;
            }

            index += i;
            items[dataIndexName] = index;

            return ValueTask.CompletedTask;
        }
    }

    /// <summary>
    /// 推送国内商标权值数据到提成中心
    /// </summary>
    /// <param name="month">年份</param>
    /// <param name="channel">推送队列</param>
    /// <param name="year">月份</param>
    [HttpPost("/commission-service/trademark-domestic/commission-weight/{year:int}/{month:int}")]
    [Authorize(Roles = DomesticOperatorRole)]
    public async Task PushDomesticCommissionWeightsAsync(
        [Required] int year,
        [Required] int month,
        [FromServices] Channel<PushDomesticCommissionWeightCommand> channel)
    {
        var deptIds = await mediator.Send(new AuthorizationDeptIdsQuery(), HttpContext.RequestAborted);
        var userId = User.GetUserId();
        
        await channel.Writer.WriteAsync(new PushDomesticCommissionWeightCommand(userId, deptIds, year, month)
        {
            TraceParentId = Activity.Current?.ParentId ?? HttpContext.TraceIdentifier
        });
    }
    
    /// <summary>
    /// 推送出口商标权值数据到提成中心
    /// </summary>
    /// <param name="month">年份</param>
    /// <param name="channel">推送队列</param>
    /// <param name="year">月份</param>
    [HttpPost("/commission-service/trademark-foreign/commission-weight/{year:int}/{month:int}")]
    [Authorize(Roles = ForeignOperatorRole)]
    public async Task PushForeignCommissionWeightsAsync(
        [Required] int year,
        [Required] int month,
        [FromServices] Channel<PushForeignCommissionWeightCommand> channel)
    {
        var deptIds = await mediator.Send(new AuthorizationDeptIdsQuery(), HttpContext.RequestAborted);
        var userId = User.GetUserId();
        
        await channel.Writer.WriteAsync(new PushForeignCommissionWeightCommand(year, month, userId, deptIds)
        {
            TraceParentId = Activity.Current?.ParentId ?? HttpContext.TraceIdentifier
        });
    }

    /// <summary>
    /// 创建国内商标权值数据
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <param name="commissionWeightDto">权值数据</param>
    /// <returns>统一响应接口</returns>
    [Authorize(Roles = DomesticOperatorRole)]
    [HttpPost("domestic-weight/{procId}")]
    public Task CreateDomesticCommissionWeightAsync(string procId, CommissionWeightDto commissionWeightDto)
    {
        return mediator.Send(new CreateDomesticCommissionCommand(procId, commissionWeightDto), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 创建出口商标权值数据
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <param name="commissionWeightDto">权值数据</param>
    /// <returns>统一响应接口</returns>
    [Authorize(Roles = ForeignOperatorRole)]
    [HttpPost("foreign-weight/{procId}")]
    public Task CreateForeignCommissionWeightAsync(string procId, CommissionWeightDto commissionWeightDto)
    {
        return mediator.Send(new CreateForeignCommissionCommand(procId, commissionWeightDto), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 修复数据
    /// </summary>
    /// <param name="procIds">任务集合</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("domestic-weight/fix")]
    public IAsyncEnumerable<string> FixAsync([FromBody] string[] procIds)
    {
        return mediator.CreateStream(new FixDomesticCommissionsCommand(procIds), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 下载模板
    /// </summary>
    /// <param name="templateName">
    /// 模板名称<br/>
    /// 批更新调整权值模板.xlsx
    /// </param>
    /// <returns>模板文件</returns>
    [AllowAnonymous]
    [HttpGet("templates/{templateName}")]
    [Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", MediaTypeNames.Application.Json)]
    public IActionResult GetTemplate([FromRoute, Description("模板名称"), Required] string templateName)
    {
        return File(Path.Combine("templates", templateName),
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", HttpUtility.UrlEncode(templateName));
    }
    
    /// <summary>
    /// 批量更新国内商标权值
    /// </summary>
    /// <param name="file">上传的excel文件</param>
    [HttpPatch("domestic-weight/points")]
    [Consumes(MediaTypeNames.Multipart.FormData)]
    [Authorize(Roles = DomesticOperatorRole)]
    [Produces(MediaTypeNames.Application.Json)]
    public async Task BatchUpdateDomesticCommissionWeightAsync([Required] IFormFile file)
    {
        var importData = await file.OpenReadStream().QueryAsync<TrademarkCommissionImportPatchDto>("Sheet1", ExcelType.XLSX);
        foreach (var importPatchDto in importData)
        {
            var procId = await mediator.Send(new DomesticCommissionImportQuery(importPatchDto), HttpContext.RequestAborted);

            if (string.IsNullOrWhiteSpace(procId))
            {
                throw new NotFoundException(importPatchDto.ProcNo, "任务");
            }
            
            var patchDocument = new JsonPatchDocument<DomesticWeightPatchDto>(
            [
                new Operation<DomesticWeightPatchDto>(
                    OperationType.Replace.ToString().ToLower(), 
                    "/editedProcPoint", 
                    null,
                    importPatchDto.ProcPoint)
            ], new CamelCasePropertyNamesContractResolver());
                
            await mediator.Send(new UpdateDomesticWeightCommand(procId, patchDocument), HttpContext.RequestAborted);
        }
    }
    
    /// <summary>
    /// 获取建议类任务提成匹配规则
    /// </summary>
    /// <param name="configId">提成规则id</param>
    /// <returns>建议类任务规则数据</returns>
    [HttpGet("weight-rule/{configId}/suggestion-proc-rules")]
    public Task<IEnumerable<SuggestionProcWeightRuleDto>> GetSuggestionProcWeightMatchRulesAsync(
        [FromRoute, Required] string configId)
    {
        return mediator.Send(new SuggestionProcWeightMatchRulesQuery(configId), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 创建建议类任务提成匹配规则
    /// </summary>
    /// <param name="configId">提成规则id</param>
    /// <param name="dto">建议类任务规则数据</param>
    /// <returns>统一响应结果</returns>
    [HttpPost("weight-rule/{configId}/suggestion-proc-rules")]
    public Task AddSuggestionProcWeightMatchRulesAsync(
        [FromRoute, Required] string configId,
        [FromBody] List<SuggestionProcWeightRuleCreateDto> dto)
    {
        return mediator.Send(new CreateSuggestionProcWeightMatchRuleCommand(configId, dto), HttpContext.RequestAborted);
    }

    /// <summary>
    /// 更新建议类任务提成匹配规则<br/>
    /// 是否可用：/isEnable
    /// </summary>
    /// <param name="ruleId">规则id</param>
    /// <param name="document">补丁更新文档</param>
    /// <returns>统一响应接口</returns>
    [HttpPatch("weight-suggestion-proc-rules/{ruleId:int}")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    [Produces(MediaTypeNames.Application.Json)]
    public Task UpdateSuggestionProcWeightMatchRuleAsync([FromRoute, Required] int ruleId,
        [FromBody] JsonPatchDocument<SuggestionProcWeightMatchRulePatchDto> document)
    {
        return mediator.Send(new UpdateSuggestionProcWeightMatchRuleCommand(ruleId, document), HttpContext.RequestAborted);
    }
}