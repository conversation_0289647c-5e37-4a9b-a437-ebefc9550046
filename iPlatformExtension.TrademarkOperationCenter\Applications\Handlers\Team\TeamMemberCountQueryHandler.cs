﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 团队成员计数处理者
    /// </summary>
    public class TeamMemberCountQueryHandler : IRequestHandler<TeamMemberCountQuery, IEnumerable<TeamMemberCountDto>>
    {
        private readonly IFreeSql _freeSql;

        /// <summary>
        /// 团队成员计数处理者构造函数
        /// </summary>
        /// <param name="freeSql"></param>
        public TeamMemberCountQueryHandler(IFreeSql freeSql) => _freeSql = freeSql;

        /// <summary>
        /// 团队成员处理方法
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<IEnumerable<TeamMemberCountDto>> Handle(TeamMemberCountQuery request, CancellationToken cancellationToken)
        {
            return await _freeSql.Select<SysTeamAssociateMember>()
                .WhereIf(request.TeamId is not null, it => it.TeamId == request.TeamId)
                .GroupBy(it => it.TeamId)
                .ToListAsync(it => new TeamMemberCountDto(it.Key, it.Count(it.Value)), cancellationToken);
        }
    }
}

