﻿using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Finance.Applications.Models.Proc;

internal class UnpaidOfficialProcDto
{
    /// <summary>
    /// 任务id
    /// </summary>
    public string ProcId { get; set; } = string.Empty;
    
    /// <summary>
    /// 任务编号
    /// </summary>
    public string ProcNo { get; set; } = string.Empty;

    /// <summary>
    /// 案件id
    /// </summary>
    public string CaseId { get; set; } = string.Empty;

    /// <summary>
    /// 我方文号
    /// </summary>
    public string Volume { get; set; } = string.Empty;

    public IEnumerable<ApplicantInfo> Applicants { get; set; } = [];

    public CustomerInfo Customer { get; set; }

    public string CaseName { get; set; } = string.Empty;

    /// <summary>
    /// 国际分类
    /// </summary>
    public string TrademarkClasses { get; set; } = string.Empty;

    /// <summary>
    /// 送官方日
    /// </summary>
    public DateTime? SendOfficialDate { get; set; }

    /// <summary>
    /// 任务名称id
    /// </summary>
    public string CtrlProcId { get; set; } = string.Empty;

    public string ProcName { get; set; } = string.Empty;

    /// <summary>
    /// 缴费key
    /// </summary>
    public KeyValuePair<string, string> DeliveryKey { get; set; }
    
    public KeyValuePair<string, string> CaseDirection { get; set; }

    /// <summary>
    /// 申请号
    /// </summary>
    public string ApplicantNo { get; set; } = string.Empty;

    /// <summary>
    /// 注册号
    /// </summary>
    public string RegistrationNo { get; set; } = string.Empty;

    /// <summary>
    /// 递交方式
    /// </summary>
    public string DeliveryType { get; set; } = string.Empty;

    /// <summary>
    /// 费项总金额
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 缴费发文日
    /// </summary>
    public string OfficialPublicationDates { get; set; } = string.Empty;

    /// <summary>
    /// 管控标识
    /// </summary>
    public IEnumerable<CustomerControlIdentifierInfo> CustomerControlIdentifiers { get; set; } = [];
}