﻿using iPlatformExtension.Model.Pooled;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Common.ObjectPools.ApiResult;

public class ApiResultPooledObjectPolicy<T> : PooledObjectPolicy<T> where T : IPooledObject, new()
{
    private uint _createTimes;

    private readonly ILogger<ApiResultPooledObjectPolicy<T>> _logger;

    public ApiResultPooledObjectPolicy(ILogger<ApiResultPooledObjectPolicy<T>> logger)
    {
        _logger = logger;
    }

    public override T Create()
    {
        _logger.LogDebug("create apiResult in {CreateTimes} times", Interlocked.Increment(ref _createTimes));
        return new T();
    }

    public override bool Return(T obj)
    {
        obj.OnReturn();

        return true;
    }
}