﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 获取团队负责人处理者
    /// </summary>
    public class GetTeamLeaderQueryHandler(IFreeSql freeSql, ISystemRoleInfoRepository systemRoleInfoRepository)
        : IRequestHandler<GetTeamLeaderQuery, IEnumerable<GetTeamLeaderDto>>
    {
        public async Task<IEnumerable<GetTeamLeaderDto>> Handle(GetTeamLeaderQuery request, CancellationToken cancellationToken)
        {
            var teamLeaderRoleId = systemRoleInfoRepository.GetRole(RoleCode.TeamLeader, RoleType.TrademarkOperation);
            return await freeSql.Select<SysTeamAssociateMember, SysUserInfo>()
                            .LeftJoin(it => it.t1.UserId == it.t2.UserId)
                            .Where(it => it.t1.TeamId == request.TeamId && it.t1.RoleId == teamLeaderRoleId).ToListAsync(it => new GetTeamLeaderDto(it.t2.UserId, it.t2.UserName, it.t2.CnName) { }, cancellationToken);
        }
    }
}

