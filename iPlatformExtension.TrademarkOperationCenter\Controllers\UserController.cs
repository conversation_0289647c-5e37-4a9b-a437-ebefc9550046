﻿﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.User;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.User;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers
{
    /// <summary>
    /// 用户信息控制器
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    public class UserController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mediator">中介者</param>
        public UserController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 获取所有用户信息
        /// </summary>
        /// <returns>用户信息列表</returns>
        [HttpGet("GetAllUsers")]
        public async Task<IEnumerable<AllUserInfoDto>> GetAllUsersAsync()
        {
            return await _mediator.Send(new GetAllUsersQuery());
        }
    }
}
