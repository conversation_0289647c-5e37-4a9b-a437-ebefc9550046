using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_trademark_items_level", DisableSyncStructure = true)]
	public partial class BasTrademarkItemsLevel {

		[ Column(Name = "history_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string HistoryId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "cur_id", StringLength = 50, IsNullable = false)]
		public string CurId { get; set; }
		
		[Navigate(nameof(HistoryId))]
		public virtual BasTrademarkItems? TrademarkItems { get; set; }

		[ Column(Name = "fpId", StringLength = 50)]
		public string FpId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_open")]
		public bool IsOpen { get; set; } = true;

		[ Column(Name = "level")]
		public int? Level { get; set; }

		[ Column(Name = "lid", StringLength = 50, IsNullable = false)]
		public string Lid { get; set; }

		[ Column(Name = "pId", StringLength = 50)]
		public string PId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "version_id", StringLength = 50, IsNullable = false)]
		public string VersionId { get; set; }

	}

}
