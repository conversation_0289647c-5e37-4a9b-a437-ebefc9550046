using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "ant_url_label_list", DisableSyncStructure = true)]
	public partial class AntUrlLabelList {

		[ Column(Name = "list_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ListId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "info_id", StringLength = 500, IsNullable = false)]
		public string InfoId { get; set; }

		[ Column(Name = "label_id", StringLength = 500, IsNullable = false)]
		public string LabelId { get; set; }

	}

}
