using Confluent.Kafka;
using iPlatformExtension.Common.MQ.KafKa.Handlers.Producer;
using iPlatformExtension.Common.MQ.KafKa.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.MQ.KafKa.Producer;

public class ProducerBuilder<TMessage>
{
    internal ProducerBuilder(OptionsBuilder<ProducerOptions<TMessage>> optionsBuilder, IServiceCollection services)
    {
        OptionsBuilder = optionsBuilder;
        Services = services;
    }

    internal OptionsBuilder<ProducerOptions<TMessage>> OptionsBuilder { get; set; }

    public IServiceCollection Services { get; }

    public ProducerBuilder<TMessage> ConfigureServerOptions(Action<ProducerConfig> configure, string? configSectionPath = null)
    {
        if (!string.IsNullOrWhiteSpace(configSectionPath))
        {
            OptionsBuilder.Configure<IConfiguration>((options, configuration) =>
            {
                configuration.GetSection(configSectionPath).Bind(options.ServerOptions);
            });
        }
        OptionsBuilder.Configure(options =>
        {
            configure(options.ServerOptions);
            options.ProducerBuilder = new ProducerBuilder<MessageKey, TMessage>(options.ServerOptions);
        });

        return this;
    }

    public ProducerBuilder<TMessage> ConfigureProducer(Action<ProducerOptions<TMessage>, IServiceProvider> options)
    {
        OptionsBuilder.PostConfigure(options);
        return this;
    }

    public ProducerBuilder<TMessage> AddProducerInterceptor<T>() where T : DelegatingProducer<MessageKey, TMessage>
    {
        Services.TryAddEnumerable(ServiceDescriptor.Transient<DelegatingProducer<MessageKey, TMessage>, T>());
        return this;
    }

    public IServiceCollection Build()
    {
        var primaryKey = $"IProducer[{nameof(MessageKey)}, {typeof(TMessage).Name}]";
        Services.AddKeyedSingleton(primaryKey,(provider, _) =>
        {
            var options = provider.GetRequiredService<IOptions<ProducerOptions<TMessage>>>().Value;
            var builder = options.ProducerBuilder ?? new ProducerBuilder<MessageKey, TMessage>(options.ServerOptions);
            return builder.Build();
        });

        Services.AddTransient(provider =>
        {
            var primaryProducer = provider.GetRequiredKeyedService<IProducer<MessageKey, TMessage>>(primaryKey);
            var interceptors = provider.GetServices<DelegatingProducer<MessageKey, TMessage>>();

            return interceptors.Reverse().Aggregate(primaryProducer, (producer, delegatingProducer) =>
            {
                delegatingProducer.InnerProducer = producer;
                return delegatingProducer;
            });
        });

        return Services;
    }
}