﻿using System.Text.Json.Serialization;
using FreeSql.DataAnnotations;
using iPlatformExtension.Model.BaseModel;


namespace iPlatformExtension.Model.Dto.CaseEntrust
{
    public class AppCaseDto
    {

        /// <summary>
        /// 判断是否是新案
        /// </summary>
        [System.Text.Json.Serialization.JsonIgnore]
        public bool IsNewCase => string.IsNullOrWhiteSpace(Volume);
        
       
        ///// <summary>
        ///// 申请人列表
        ///// </summary>
        //[Navigate(nameof(BaseModel.AppApplicantList.CaseId))]
        public ICollection<AppApplicantDto>? AppApplicantList { get; set; }

        /// <summary>
        /// 发明人列表
        /// </summary>
        //[Navigate(nameof(BaseModel.AppInventorList.CaseId))]
        public ICollection<AppInventorDto>? AppInventorList { get; set; }

        /// <summary>
        /// 费项
        /// </summary>
        public ICollection<CaseFeeDto> CaseFeeList { get; set; }

        /// <summary>
        /// 联系人列表
        /// </summary>
        //[Navigate(nameof(BaseModel.AppContactList.ObjId))]
        public ICollection<AppContactDto>? AppContactList { get; set; }

        /// <summary>
        /// 生物保藏
        /// </summary>
        //[Navigate(nameof(BaseModel.AppBiologyList.CaseId))]
        public ICollection<AppBiologyDto>? AppBiologyList { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public ICollection<CaseEntrustFileDto> CaseFile { get; set; } = new List<CaseEntrustFileDto>();

        public ICollection<AppProductDto> appProductList { get; set; }

        ///// <summary>
        ///// 补充资料
        ///// </summary>
        //[Navigate(nameof(BaseModel.AppFurtherInformationList.CaseId))]
        //public ICollection<AppFurtherInformationList>? AppFurtherInformationList { get; set; }

        /// <summary>
        /// 优先权信息
        /// </summary>
        [Navigate(nameof(BaseModel.CasePriorityInfo.CaseId))]
        public ICollection<CasePriorityInfo>? CasePriorityInfo { get; set; }

        /// <summary>
        /// 订单案件ID
        /// </summary>
        public string orderCaseID { get; set; }

        /// <summary>
        /// 订单id
        /// </summary>
        [JsonPropertyName("orderId")]
        public string OrderId { get; set; } = null!;

        /// <summary>
        /// 开案案件ID
        /// </summary>
        public string? CaseId { get; set; }

        public string? Accelerate { get; set; }

        /// <summary>
        /// 是否惠州活动案
        /// </summary>
        public bool ActiveCase { get; set; } = false;

        /// <summary>
        /// 修改条数
        /// </summary>
        public string? ActiveTypeId { get; set; }

        /// <summary>
        /// 机构货币ID（未使用？）
        /// </summary>
        public string? AgencyCurrencyId { get; set; }

        /// <summary>
        /// 代理费（未使用）
        /// </summary>
        public decimal? AgencyFeeSum { get; set; }

        /// <summary>
        /// 代理机构
        /// </summary>
        public string? AgencyId { get; set; }

        /// <summary>
        /// 年度总数
        /// </summary>
        public string? AnnualTotal { get; set; }

        /// <summary>
        /// 申请日
        /// </summary>
        public DateTime? AppDate { get; set; }

        /// <summary>
        /// 申请号
        /// </summary>
        public string? AppNo { get; set; }

        /// <summary>
        /// 申请途径
        /// </summary>
        public string? ApplyChannel { get; set; }

        /// <summary>
        /// 技术揭露主键ID
        /// </summary>
        public string? ApplyId { get; set; }

        /// <summary>
        /// 专利类型
        /// </summary>
        public string? ApplyTypeId { get; set; }

        /// <summary>
        /// 业务类型ID
        /// </summary>
        [ Column(Name = "bus_type_id", StringLength = 50)]
        public string? BusTypeId { get; set; }

        /// <summary>
        /// 商标说明
        /// </summary>
        [ Column(Name = "case_descriptions", StringLength = 4000)]
        public string? CaseDescriptions { get; set; }

        /// <summary>
        /// 案件流向
        /// </summary>
        [ Column(Name = "case_direction", StringLength = 50)]
        public string? CaseDirection { get; set; }

        /// <summary>
        /// 急案标识
        /// </summary>
        [ Column(Name = "case_emergent", StringLength = 50)]
        public string? CaseEmergent { get; set; }

        [ Column(Name = "case_level_id", StringLength = 50)]
        public string? CaseLevelId { get; set; }

        /// <summary>
        /// 案件名称
        /// </summary>
        [ Column(Name = "case_name", StringLength = 500)]
        public string? CaseName { get; set; }

        /// <summary>
        /// 英文名称
        /// </summary>
        [ Column(Name = "case_name_en", StringLength = 500)]
        public string? CaseNameEn { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [ Column(Name = "case_remark", StringLength = 4000)]
        public string? CaseRemark { get; set; }

        /// <summary>
        /// 个案要求
        /// </summary>
        [ Column(Name = "case_request", StringLength = 2000)]
        public string? CaseRequest { get; set; }

        /// <summary>
        /// 案件内部状态
        /// </summary>
        [ Column(Name = "case_status_id", StringLength = 50)]
        public string? CaseStatusId { get; set; }

        /// <summary>
        /// 费用日期
        /// </summary>
        [ Column(Name = "charge_date")]
        public DateTime? ChargeDate { get; set; }

        /// <summary>
        /// 初审日
        /// </summary>
        [ Column(Name = "chushen_pub_date")]
        public DateTime? ChushenPubDate { get; set; }

        /// <summary>
        /// 初审公告号（未使用）
        /// </summary>
        [ Column(Name = "chushen_pub_no", StringLength = 50)]
        public string? ChushenPubNo { get; set; }

        /// <summary>
        /// 无数据
        /// </summary>
        [ Column(Name = "color_detail", StringLength = 200)]
        public string? ColorDetail { get; set; }

        /// <summary>
        /// 窗体颜色？未使用？
        /// </summary>
        [ Column(Name = "color_form", StringLength = 50)]
        public string? ColorForm { get; set; }

        /// <summary>
        /// 合同编号
        /// </summary>
        [ Column(Name = "contract_no", StringLength = 200)]
        public string? ContractNo { get; set; }

        /// <summary>
        /// 国家&地区ID
        /// </summary>
        [ Column(Name = "country_id", StringLength = 50)]
        public string? CountryId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [ Column(Name = "create_time", InsertValueSql = "getdate()")]
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        [ Column(Name = "create_user_id", StringLength = 50)]
        public string? CreateUserId { get; set; }

        [ Column(Name = "crm_case_id", StringLength = 50)]
        public string? CrmCaseId { get; set; }

        /// <summary>
        /// 起始任务
        /// </summary>
        public string? CtrlProcId { get; set; }

        /// <summary>
        /// 任务标识
        /// </summary>
        [ Column(Name = "ctrl_proc_mark", StringLength = 50)]
        public string? CtrlProcMark { get; set; }

        /// <summary>
        /// 任务属性
        /// </summary>
        [ Column(Name = "ctrl_proc_property", StringLength = 50)]
        public string? CtrlProcProperty { get; set; }

        /// <summary>
        /// 控制步骤共给？（未使用）
        /// </summary>
        [ Column(Name = "ctrl_proc_supply", StringLength = 50)]
        public string? CtrlProcSupply { get; set; }

        /// <summary>
        /// 客户期限
        /// </summary>
        [ Column(Name = "cus_due_date")]
        public DateTime? CusDueDate { get; set; }

        /// <summary>
        /// 定稿期限（外）
        /// </summary>
        [ Column(Name = "cus_finish_date")]
        public DateTime? CusFinishDate { get; set; }

        /// <summary>
        /// 初稿期限（外）
        /// </summary>
        [ Column(Name = "cus_first_date")]
        public DateTime? CusFirstDate { get; set; }

        /// <summary>
        /// 客户卷号
        /// </summary>
        [ Column(Name = "customer_volume", StringLength = 50)]
        public string? CustomerVolume { get; set; }

        /// <summary>
        /// 递交机构
        /// </summary>
        [ Column(Name = "deliver_agency", StringLength = 50)]
        public string? DeliverAgency { get; set; }

        /// <summary>
        /// 分案属性
        /// </summary>
        [ Column(Name = "division_property", StringLength = 50)]
        public string? DivisionProperty { get; set; }

        /// <summary>
        /// 费减比例
        /// </summary>
        [ Column(Name = "fee_reduce", StringLength = 50)]
        public string? FeeReduce { get; set; }

        /// <summary>
        /// 申请方式
        /// </summary>
        [ Column(Name = "filing_type", StringLength = 50)]
        public string? FilingType { get; set; }

        /// <summary>
        /// 第一代理人
        /// </summary>
        [ Column(Name = "first_agency_user", StringLength = 50)]
        public string? FirstAgencyUser { get; set; }

        /// <summary>
        /// 首缴年度
        /// </summary>
        [ Column(Name = "first_pay_annual", StringLength = 50)]
        public string? FirstPayAnnual { get; set; }

        /// <summary>
        /// 最早优先权日期
        /// </summary>
        [ Column(Name = "first_priority_date")]
        public DateTime? FirstPriorityDate { get; set; }

        /// <summary>
        /// 委外机构
        /// </summary>
        [ Column(Name = "foregin_agency_id", StringLength = 50)]
        public string? ForeginAgencyId { get; set; }

        /// <summary>
        /// 委外说明
        /// </summary>
        [ Column(Name = "foregin_agency_remark", StringLength = 2000)]
        public string? ForeginAgencyRemark { get; set; }

        /// <summary>
        /// 委外联系人
        /// </summary>
        [ Column(Name = "foregin_contact_id", StringLength = 50)]
        public string? ForeginContactId { get; set; }

        /// <summary>
        /// 母案申请日
        /// </summary>
        [ Column(Name = "initial_app_date")]
        public DateTime? InitialAppDate { get; set; }

        /// <summary>
        /// 母案申请号
        /// </summary>
        [ Column(Name = "initial_app_no", StringLength = 50)]
        public string? InitialAppNo { get; set; }

        /// <summary>
        /// 内部期限
        /// </summary>
        [ Column(Name = "int_due_date")]
        public DateTime? IntDueDate { get; set; }

        /// <summary>
        /// 定稿期限(内)
        /// </summary>
        [ Column(Name = "int_finish_date")]
        public DateTime? IntFinishDate { get; set; }

        /// <summary>
        /// 初稿期限(内)
        /// </summary>
        [ Column(Name = "int_first_date")]
        public DateTime? IntFirstDate { get; set; }

        /// <summary>
        /// 无效编码/委内编号
        /// </summary>
        [ Column(Name = "invalid_code", StringLength = 50)]
        public string? InvalidCode { get; set; }

        /// <summary>
        /// 有效终止日
        /// </summary>
        [ Column(Name = "invalid_date")]
        public DateTime? InvalidDate { get; set; }

        /// <summary>
        /// 专利权人
        /// </summary>
        [ Column(Name = "invalid_holder_name", StringLength = 4000)]
        public string? InvalidHolderName { get; set; }

        /// <summary>
        /// 请求人
        /// </summary>
        [ Column(Name = "invalid_request_user", StringLength = 1000)]
        public string? InvalidRequestUser { get; set; }

        /// <summary>
        /// 是否主动修改
        /// </summary>
        [ Column(Name = "is_active")]
        public bool? IsActive { get; set; } = false;

        [ Column(Name = "is_advance_check")]
        public bool? IsAdvanceCheck { get; set; } = false;

        /// <summary>
        /// 提前公布
        /// </summary>
        [ Column(Name = "is_ahead_pub")]
        public bool? IsAheadPub { get; set; } = false;

        /// <summary>
        /// 是否配案
        /// </summary>
        [ Column(Name = "is_allocate")]
        public bool? IsAllocate { get; set; } = false;

        /// <summary>
        /// CA申请
        /// </summary>
        [ Column(Name = "is_ca")]
        public bool? IsCa { get; set; } = false;

        /// <summary>
        /// 是否CIP申请
        /// </summary>
        [ Column(Name = "is_cip")]
        public bool? IsCip { get; set; } = false;

        /// <summary>
        /// 指定颜色
        /// </summary>
        [ Column(Name = "is_color")]
        public bool IsColor { get; set; } = false;

        /// <summary>
        /// 是否分案（未使用）
        /// </summary>
        [ Column(Name = "is_division")]
        public bool? IsDivision { get; set; } = false;

        /// <summary>
        /// 是否实质审查
        /// </summary>
        [ Column(Name = "is_essence_exam")]
        public bool? IsEssenceExam { get; set; } = false;

        [ Column(Name = "is_fee_config")]
        public bool? IsFeeConfig { get; set; } = false;

        /// <summary>
        /// 请求费用减缓
        /// </summary>
        [ Column(Name = "is_fee_reduce")]
        public bool? IsFeeReduce { get; set; } = false;

        /// <summary>
        /// 是否新开案
        /// </summary>
        [ Column(Name = "is_finish")]
        public bool? IsFinish { get; set; } = false;

        /// <summary>
        /// 是否请求宽限
        /// </summary>
        [ Column(Name = "is_grace")]
        public bool IsGrace { get; set; } = false;

        /// <summary>
        /// 是否PPH请求
        /// </summary>
        [ Column(Name = "is_pph")]
        public bool? IsPph { get; set; } = false;

        /// <summary>
        /// 是否优先审查
        /// </summary>
        [ Column(Name = "is_priority_review")]
        public bool? IsPriorityReview { get; set; } = false;

        [ Column(Name = "is_request_das")]
        public bool? IsRequestDas { get; set; } = false;

        /// <summary>
        /// 是否同日申请
        /// </summary>
        [ Column(Name = "is_same_day")]
        public bool? IsSameDay { get; set; } = false;

        /// <summary>
        /// 请求保密审查
        /// </summary>
        [ Column(Name = "is_secrecy_request")]
        public bool? IsSecrecyRequest { get; set; } = false;

        /// <summary>
        /// 是否立体？未使用？
        /// </summary>
        [ Column(Name = "is_stereoscopic")]
        public bool? IsStereoscopic { get; set; } = false;

        /// <summary>
        /// 公告日
        /// </summary>
        [ Column(Name = "issue_date", StringLength = 50)]
        public string? IssueDate { get; set; }

        /// <summary>
        /// 公告号
        /// </summary>
        [ Column(Name = "issue_no", StringLength = 50)]
        public string? IssueNo { get; set; }

        /// <summary>
        /// 官方期限
        /// </summary>
        [ Column(Name = "legal_due_date")]
        public DateTime? LegalDueDate { get; set; }

        /// <summary>
        /// 管理分所
        /// </summary>
        [ Column(Name = "manage_company", StringLength = 50)]
        public string? ManageCompany { get; set; }

        /// <summary>
        /// 管理地区
        /// </summary>
        [ Column(Name = "manage_district", StringLength = 50)]
        public string? ManageDistrict { get; set; }

        /// <summary>
        /// 材料？（未使用）
        /// </summary>
        [ Column(Name = "material", StringLength = 50)]
        public string? Material { get; set; }

        /// <summary>
        /// 成员国家
        /// </summary>
        [ Column(Name = "member_country", StringLength = 500)]
        public string? MemberCountry { get; set; }

        /// <summary>
        /// 申请方式
        /// </summary>
        [ Column(Name = "multi_type", StringLength = 50)]
        public string? MultiType { get; set; }

        /// <summary>
        /// 外派处理人
        /// </summary>
        [ Column(Name = "out_user", StringLength = 50)]
        public string? OutUser { get; set; }

        /// <summary>
        /// PCT申请日
        /// </summary>
        [ Column(Name = "pct_app_date")]
        public DateTime? PctAppDate { get; set; }

        /// <summary>
        /// PCT申请号
        /// </summary>
        [ Column(Name = "pct_app_no", StringLength = 50)]
        public string? PctAppNo { get; set; }

        /// <summary>
        /// PCT递交语言
        /// </summary>
        [ Column(Name = "pct_deliver_language", StringLength = 50)]
        public string? PctDeliverLanguage { get; set; }

        /// <summary>
        /// 是否PCT进入
        /// </summary>
        [ Column(Name = "pct_enter")]
        public bool PctEnter { get; set; } = false;

        /// <summary>
        /// PCT公布日
        /// </summary>
        [ Column(Name = "pct_pub_date")]
        public DateTime? PctPubDate { get; set; }

        /// <summary>
        /// PCT公布语言
        /// </summary>
        [ Column(Name = "pct_pub_language", StringLength = 50)]
        public string? PctPubLanguage { get; set; }

        /// <summary>
        /// PCT公布号
        /// </summary>
        [ Column(Name = "pct_pub_no", StringLength = 50)]
        public string? PctPubNo { get; set; }

        /// <summary>
        /// 国际检索局
        /// </summary>
        [ Column(Name = "pct_search_unit", StringLength = 200)]
        public string? PctSearchUnit { get; set; }

        /// <summary>
        /// 商标图样号（未使用）
        /// </summary>
        [ Column(Name = "pic_file_no", StringLength = 50)]
        public string? PicFileNo { get; set; }

        /// <summary>
        /// 任务ID？
        /// </summary>
        [ Column(Name = "proc_id", StringLength = 50)]
        public string? ProcId { get; set; } = Guid.NewGuid().ToString().ToUpper();

        /// <summary>
        /// 任务备注？
        /// </summary>
        [ Column(Name = "proc_note", StringLength = 2000)]
        public string? ProcNote { get; set; }

        /// <summary>
        /// 项目编号
        /// </summary>
        [ Column(Name = "project_no", StringLength = 50)]
        public string? ProjectNo { get; set; }

        /// <summary>
        /// 商标类别
        /// </summary>
        [ Column(Name = "register_class", StringLength = 50)]
        public string? RegisterClass { get; set; }

        [ Column(Name = "register_no", StringLength = 50)]
        public string? RegisterNo { get; set; }

        /// <summary>
        /// 注册类型代码（未使用）
        /// </summary>
        [ Column(Name = "register_type_code", StringLength = 100)]
        public string? RegisterTypeCode { get; set; }

        /// <summary>
        /// 返接口人日期
        /// </summary>
        public DateTime? ReturnInterfaceDate { get; set; }

        /// <summary>
        /// 返发明人日期
        /// </summary>
        public DateTime? ReturnInventorDate { get; set; }

        /// <summary>
        /// 审查阶段（未使用）
        /// </summary>
        public string? ReviewStage { get; set; }

        /// <summary>
        /// 第二代理人
        /// </summary>
        public string? SecondAgencyUser { get; set; }

        /// <summary>
        /// 标识种类
        /// </summary>
        public string? ShowMode { get; set; }

        /// <summary>
        /// 简单的交付日期（未使用）
        /// </summary>
        public DateTime? SimpleDeliverDate { get; set; }

        /// <summary>
        /// 奖励金（未使用）
        /// </summary>
        public bool? Subsidize { get; set; } = false;

        /// <summary>
        /// 技术领域
        /// </summary>
        public string? TechFieldId { get; set; }

        /// <summary>
        /// 名义承办人
        /// </summary>
        public string? TitularWriteUser { get; set; }

        /// <summary>
        /// 跟案人
        /// </summary>
        public string? TrackUser { get; set; }

        /// <summary>
        /// 跟案人
        /// </summary>
        // [Required(ErrorMessage = "跟案人不能为空")]
        public string? TrackUserId { get; set; }

        /// <summary>
        /// 翻译字数
        /// </summary>
        public string? TranslateAmount { get; set; }

        /// <summary>
        /// 翻译类型
        /// </summary>
        public string? TranslateType { get; set; }

        /// <summary>
        /// 承办部门
        /// </summary>
        public string? UndertakeDeptId { get; set; }

        /// <summary>
        /// 承办人
        /// </summary>
        public string? UndertakeUserId { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public string? UpdateUserId { get; set; }

        /// <summary>
        /// 有效起始日
        /// </summary>
        public DateTime? ValidDate { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string? VersionId { get; set; }

        /// <summary>
        /// 我方卷号
        /// </summary>
        public string? Volume { get; set; }

        /// <summary>
        /// 注册公告日（未使用？）
        /// </summary>
        public DateTime? ZhucePubDate { get; set; }

        /// <summary>
        /// 公告号
        /// </summary>
        public string? ZhucePubNo { get; set; }
        
        /// <summary>
        /// 同递交ID
        /// </summary>
        public RelatedCasesIds? SameSubmitIds { get; set; }
        
        /// <summary>
        /// 其他家族ID
        /// </summary>
        public RelatedCasesIds? FamilyOtherIds { get; set; }
        
        /// <summary>
        /// 同日ID（？）
        /// </summary>
        public RelatedCasesIds? SameDayIds { get; set; }
        
        /// <summary>
        /// 新案重提ID
        /// </summary>
        public RelatedCasesIds? CaseResubmitIds { get; set; }
    }
}
