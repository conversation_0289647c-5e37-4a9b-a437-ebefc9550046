using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_auth_filter", DisableSyncStructure = true)]
	public partial class SysAuthFilter {

		[ Column(Name = "auth_filter_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string AuthFilterId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "auth_id", StringLength = 50)]
		public string AuthId { get; set; }

		[ Column(Name = "filter_id", StringLength = 50)]
		public string FilterId { get; set; }

		[ Column(Name = "filter_type", StringLength = 50)]
		public string FilterType { get; set; }

		[ Column(Name = "filter_value", StringLength = 1000)]
		public string FilterValue { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
