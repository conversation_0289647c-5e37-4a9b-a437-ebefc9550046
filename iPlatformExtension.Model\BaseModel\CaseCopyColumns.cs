using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_copy_columns", DisableSyncStructure = true)]
	public partial class CaseCopyColumns {

		[ Column(Name = "columns_id", StringLength = 50, IsNullable = false)]
		public string ColumnsId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "columns_name_en_us", StringLength = 100)]
		public string ColumnsNameEnUs { get; set; }

		[ Column(Name = "columns_name_ja_jp", StringLength = 100)]
		public string ColumnsNameJaJp { get; set; }

		[ Column(Name = "columns_name_zh_cn", StringLength = 100)]
		public string ColumnsNameZhCn { get; set; }

		[ Column(Name = "dest_columns_name", StringLength = 50)]
		public string DestColumnsName { get; set; }

		[ Column(Name = "dest_table", StringLength = 50)]
		public string DestTable { get; set; }

		[ Column(Name = "from_columns_name", StringLength = 50)]
		public string FromColumnsName { get; set; }

		[ Column(Name = "from_table", StringLength = 50)]
		public string FromTable { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
