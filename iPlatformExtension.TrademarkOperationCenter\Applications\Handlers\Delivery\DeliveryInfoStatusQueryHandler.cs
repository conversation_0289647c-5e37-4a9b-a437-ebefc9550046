﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DeliveryInfoStatusQueryHandler(
    IFreeSql freeSql,
    IMediator mediator,
    IHttpContextAccessor httpContextAccessor,
    IRedisCache<RedisCacheOptionsBase> redisCache)
    : IRequestHandler<DeliveryInfoStatusQuery, DeliveryInfoStatus>
{
    public async Task<DeliveryInfoStatus> Handle(DeliveryInfoStatusQuery request, CancellationToken cancellationToken)
    {
        var currentUserId = (httpContextAccessor.HttpContext?.User).GetUserId();
        var status = new DeliveryInfoStatus();
        var deliveryInfo = await freeSql.Select<DeliInfo>().NoTracking().WithLock()
                .IncludeMany(deliInfo => deliInfo.Applicants)
                .IncludeMany(deliInfo => deliInfo.Priorities)
                .IncludeMany(deliInfo => deliInfo.NiceCategories)
                .Where(info => info.ProcId == request.ProcId).ToOneAsync(cancellationToken);
        var flowInfo = await mediator.Send(new FlowInfoQuery()
        {
            ObjId = request.ProcId,
            FlowSubType = "TII",
            FlowType = FlowType.Delivery
        }, cancellationToken);

        if (deliveryInfo is null)
        {
            status.HasNew = true;
            return status;
        }

        status.Locked =
            (await redisCache.GetCacheValueAsync<string, long?>(LockKey.DeliveringLockKey, deliveryInfo.ProcId, cancellationToken) ?? 0) >
            DateTimeOffset.Now.ToUnixTimeMilliseconds();
        status.HasNew = await mediator.Send(new CaseInfoComparisonQuery(deliveryInfo), cancellationToken);
        status.DeliveryStatus = deliveryInfo.GetDeliveryStatus();
        status.DeliveryButtons = await mediator.Send(new DeliveryButtonsQuery(deliveryInfo, currentUserId, flowInfo, status.Locked), cancellationToken);
        
        
        //caseinfo
        //appliant
        //prior
        //filelist
        //nice
        return status;
    }
}