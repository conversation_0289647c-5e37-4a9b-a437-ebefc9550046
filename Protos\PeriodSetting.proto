syntax = "proto3";

import public "google/protobuf/empty.proto";
option java_package = "com.aciplaw.patas.protobuf.commission";
option csharp_namespace = "iPlatformExtension.Commission.Clients";

service PeriodSettingService{
  rpc getPeriodSettingList(google.protobuf.Empty) returns (PeriodSettingList) {
  }
}

message PeriodSettingList{
  repeated PeriodSetting periodSettings = 1;
}

message PeriodSetting{
  //月份
  int32 month = 1;
  //权限数据推送截止日
  int32 deadlinePushingPermissionData = 2;
  //账期日
  int32 period = 3;
  //处理人确认截止日
  int32 deadlineConfirmationByProcessor = 4;

}
