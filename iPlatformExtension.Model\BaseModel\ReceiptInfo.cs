using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "receipt_info", DisableSyncStructure = true)]
	public partial class ReceiptInfo {

		[ Column(Name = "agenname", StringLength = 200)]
		public string Agenname { get; set; }

		[ Column(Name = "billbatchcode", StringLength = 50)]
		public string Billbatchcode { get; set; }

		[ Column(Name = "billdate")]
		public DateTime? Billdate { get; set; }

		[ Column(Name = "billname", StringLength = 100)]
		public string Billname { get; set; }

		/// <summary>
		/// 票据号码
		/// </summary>
		[ Column(Name = "billno", StringLength = 50)]
		public string Billno { get; set; }

		/// <summary>
		/// 申请号
		/// </summary>
		[ Column(Name = "busno", StringLength = 50)]
		public string Busno { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime CreateTime { get; set; }

		[ Column(Name = "fbillnature", StringLength = 50)]
		public string Fbillnature { get; set; }

		[ Column(Name = "file_ex", StringLength = 50)]
		public string FileEx { get; set; }

		[ Column(Name = "file_name", StringLength = 50)]
		public string FileName { get; set; }

		[ Column(Name = "file_no", StringLength = 50)]
		public string FileNo { get; set; }

		[ Column(Name = "file_size")]
		public long? FileSize { get; set; }

		[ Column(Name = "idcard", StringLength = 50)]
		public string Idcard { get; set; }

		[ Column(Name = "info_id", StringLength = 50, IsNullable = false)]
		public string InfoId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "list_id", StringLength = 50, IsNullable = false)]
		public string ListId { get; set; }

		[ Column(Name = "memo", StringLength = 500)]
		public string Memo { get; set; }

		[ Column(Name = "paydate")]
		public DateTime? Paydate { get; set; }

		[ Column(Name = "payer", StringLength = 200)]
		public string Payer { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		/// <summary>
		/// 校验码
		/// </summary>
		[ Column(Name = "random", StringLength = 50)]
		public string Random { get; set; }

		[ Column(Name = "redbillno", StringLength = 50)]
		public string Redbillno { get; set; }

		[ Column(Name = "relatedbillno", StringLength = 50)]
		public string Relatedbillno { get; set; }

		[ Column(Name = "state", StringLength = 50)]
		public string State { get; set; }

		[ Column(Name = "takingcode", StringLength = 50)]
		public string Takingcode { get; set; }

		[ Column(Name = "totalamt", StringLength = 50)]
		public string Totalamt { get; set; }

	}

}
