using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_backlog_cases", DisableSyncStructure = true)]
	public partial class RpBacklogCases {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "agency_user", StringLength = 50)]
		public string AgencyUser { get; set; }

		[ Column(Name = "case_status", StringLength = 50)]
		public string CaseStatus { get; set; }

		[ Column(Name = "charge_user", StringLength = 50)]
		public string ChargeUser { get; set; }

		[ Column(Name = "customer_full_name", StringLength = 100)]
		public string CustomerFullName { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "month", StringLength = 50)]
		public string Month { get; set; }

		[ Column(Name = "number")]
		public int? Number { get; set; }

		[ Column(Name = "quarter", StringLength = 50)]
		public string Quarter { get; set; }

		[ Column(Name = "tech_classify", StringLength = 50)]
		public string TechClassify { get; set; }

		[ Column(Name = "year", StringLength = 50)]
		public string Year { get; set; }

	}

}
