﻿using System.Text;
using Microsoft.Extensions.Http.Logging;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Common.Logging.HttpClient;

public class ResponseHeaderLogger(Func<string, bool> redactHeader, ILogger logger, ObjectPool<StringBuilder> stringBuilderPool) : IHttpClientLogger
{
    public object? LogRequestStart(HttpRequestMessage request)
    {
        return null;
    }

    public void LogRequestStop(object? context, HttpRequestMessage request, HttpResponseMessage response, TimeSpan elapsed)
    {
        var headers = request.Headers;

        var stringBuilder = stringBuilderPool.Get();
        var headerInfo = headers.Aggregate(stringBuilder,
                (builder, header) => builder.Append(header.Key).Append(": ").Append(redactHeader(header.Key) ? "[Redacted]" : string.Join(", ", header.Value)))
            .ToString();
        stringBuilderPool.Return(stringBuilder);
        
        logger.LogInformation("Request Headers: \n{HeaderInfo}", headerInfo);
    }

    public void LogRequestFailed(object? context, HttpRequestMessage request, HttpResponseMessage? response, Exception exception,
        TimeSpan elapsed)
    {
        
    }
}