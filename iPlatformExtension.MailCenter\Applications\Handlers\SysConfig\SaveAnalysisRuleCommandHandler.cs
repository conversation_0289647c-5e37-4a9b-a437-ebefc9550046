﻿using AutoMapper;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenter.Infrastructure.Provide;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    /// <summary>
    /// 保存收件解析规则
    /// </summary>
    internal sealed class SaveAnalysisRuleCommandHandler(
        IMediator mediator,
        IMailConfigRepository mailConfigRepository,
        IMapper mapper,
        IMemoryCache cache,
        ISemaphoreProvider semaphoreProvider,
        IHttpContextAccessor content
    ) : IRequestHandler<SaveAnalysisRuleCommand>
    {
        public async Task Handle(
            SaveAnalysisRuleCommand request,
            CancellationToken cancellationToken
        )
        {
            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            if (request.AnalysisRuleDetailList != null)
            {
                var specialCharacters = new[] {"*", "+", "?", "^", "$", "(", ")", "[", "]", "{", "}", "|" };
                foreach (var detail in request.AnalysisRuleDetailList)
                {
                    if (detail.FilterValue != null)
                    {
                        foreach (var specialChar in specialCharacters)
                        {
                            if (detail.FilterValue.Contains(specialChar))
                            {
                                throw new ApplicationException(
                                    $"匹配信息不能包含特殊字符: {specialChar}"
                                );
                            }
                        }
                    }
                }
            }
            var mailConfigMap = mapper.Map<MailConfig>(request);
            if (request.ConfigId is not null)
            {
                var mailConfig = await mailConfigRepository
                    .Where(it => it.ConfigId == request.ConfigId)
                    .FirstAsync(cancellationToken);
                if (mailConfig == null)
                {
                    throw new ApplicationException("ConfigId Not Found");
                }
                mailConfigMap.ConfigId = Guid.NewGuid().ToString();
                mailConfigMap.IsEnabled = request.IsEnable;
                mailConfigMap.UpdateUser = userId;
                mailConfigMap.UpdateTime = DateTime.Now;
                mailConfigMap.CreateUser = mailConfig.CreateUser;
                mailConfigMap.CreateTime = mailConfig.CreateTime;
                await mailConfigRepository.InsertAsync(mailConfigMap, cancellationToken);

                mailConfig.IsEnabled = SysEnum.Status.History.GetHashCode();
                if (await mailConfigRepository.UpdateAsync(mailConfig, cancellationToken) == 0)
                {
                    throw new ApplicationException("Update Fail");
                }
            }
            else
            {
                mailConfigMap.ConfigId = Guid.NewGuid().ToString();
                mailConfigMap.IsEnabled = request.IsEnable;
                mailConfigMap.CreateUser = userId;
                mailConfigMap.CreateTime = DateTime.Now;
                mailConfigMap.UpdateUser = userId;
                mailConfigMap.UpdateTime = DateTime.Now;
                await semaphoreProvider.LockActionAsync(
                    async () =>
                    {
                        var cacheValue = await cache.GetOrCreateAsync(
                            $"Analysis_{DateTime.Now:d}",
                            _ =>
                            {
                                return Task.FromResult(
                                    mailConfigRepository
                                        .Where(it =>
                                            it.RuleNumber != null
                                            && it.RuleNumber.Contains(
                                                $"JXGZ{DateTime.Now.ToString("yyMMdd")}"
                                            )
                                        )
                                        .Where(it =>
                                            it.IsEnabled != SysEnum.Status.History.GetHashCode()
                                        )
                                        .OrderByDescending(it => it.RuleNumber)
                                        .First(it => it.RuleNumber)
                                );
                            }
                        );
                        var num = cacheValue?.Substring(cacheValue.Length - 4, 4);
                        if (int.TryParse(num, out var value) && cacheValue?.Length == 14)
                        {
                            mailConfigMap.RuleNumber =
                                "JXGZ"
                                + DateTime.Now.ToString("yyMMdd")
                                + (value + 1).ToString("D4");
                        }
                        else
                        {
                            mailConfigMap.RuleNumber =
                                "JXGZ" + DateTime.Now.ToString("yyMMdd") + "0001";
                        }
                        cache.Set($"Analysis_{DateTime.Now:d}", mailConfigMap.RuleNumber);
                    },
                    cancellationToken
                );

                await mailConfigRepository.InsertAsync(mailConfigMap, cancellationToken);
            }

            if (
                request.AnalysisRuleDetailList is not null
                && request.AnalysisRuleDetailList.Count > 0
            )
            {
                await mediator.Send(
                    new SaveAnalysisRuleDetailCommand(
                        request.AnalysisRuleDetailList,
                        mailConfigMap.ConfigId
                    ),
                    cancellationToken
                );
            }
        }
    }
}
