﻿using MediatR;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Copy;

/// <summary>
/// 一对一复制
/// </summary>
/// <param name="Table"></param>
/// <param name="SourceId"></param>
/// <param name="TargetId"></param>
/// <param name="CopyFieldList"></param>
internal sealed record CopyCommand(
    string Table, 
    [Required] string SourceId, 
    [Required] string[] TargetId, 
    [Required] List<Tuple<string, string>> CopyFieldList) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

