﻿using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal abstract class BuildProcApplicantCommandHandler(IMapper mapper) : IMatchTrademarkProcCommandHandler<BuildProcApplicantInfoCommand>
{
    protected readonly IMapper _mapper = mapper;
    
    public abstract string CtrlProcId { get; }
    
    public abstract IEnumerable<string> CaseDirections { get; }
    
    public Task HandleAsync(BuildProcApplicantInfoCommand notification, CancellationToken cancellationToken)
    {
        var procInfo = notification.ProcInfo;
        var procApplicant = procInfo.Applicants?.MaxBy(applicant => applicant.UpdateTime);

        if (procApplicant is null) return Task.CompletedTask;

        var formerApplicant = _mapper.Map<DeliApplicant>(procApplicant);
        notification.Applicants.Add(formerApplicant);
        return Task.CompletedTask;
    }
}