using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_notice_convey_title_setting", DisableSyncStructure = true)]
	public partial class BasNoticeConveyTitleSetting {

		[ Column(Name = "title_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string TitleId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "convey_setting_id", StringLength = 50)]
		public string ConveySettingId { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "title", StringLength = 200)]
		public string Title { get; set; }

		[ Column(Name = "type_code", StringLength = 50)]
		public string TypeCode { get; set; }

		[ Column(Name = "unit_id", StringLength = 50)]
		public string UnitId { get; set; }

		[ Column(Name = "value", StringLength = 50)]
		public string Value { get; set; }

	}

}
