﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;


/// <summary>
/// 流程归入分类
/// </summary>
/// <param name="PrivateId">标签id</param>
/// <param name="Ids">id集合</param>
public record MoveProcessCommand(string PrivateId,  List<string> Ids) 
    : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;

