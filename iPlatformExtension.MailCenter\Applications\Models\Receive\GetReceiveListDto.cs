﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 获取收件列表
/// </summary>
/// <param name="MailFrom">发件人</param>
/// <param name="MailSubject">邮件主题</param>
/// <param name="MailDate">收件日期</param>
/// <param name="FinishDate">结束时间</param>
/// <param name="IgnoreTime">忽略时间</param>
/// <param name="IgnoreBy">忽略人id</param>
/// <param name="IgnoreByName">忽略人</param>
/// <param name="SortBy">分拣人id</param>
/// <param name="SortByName">分拣人</param>
/// <param name="SortTime">分拣日期</param>
/// <param name="Status">-1:取消,0:待解析,1:解析中,2:待分拣;3:办理中;4:已忽略;5:已办理;500:出错</param>
/// <param name="UndertakeUserId">承办人id</param>
/// <param name="UndertakeUserName">承办人</param>
/// <param name="MailNo">收件编号</param>
/// <param name="MailId">邮件id</param>
/// <param name="HostId">邮箱id</param>
/// <param name="MailTo">收件邮箱</param>
/// <param name="CreateTime">接收时间</param>
/// <param name="MailPriority">邮件优先级,Highest:紧急,其他(High,Normal,Low,Lowest)</param>
public record GetReceiveListDto(
    string MailFrom,
    string MailSubject,
    DateTime? MailDate,
    DateTime? FinishDate,
    DateTime? IgnoreTime,
    string IgnoreByTemp,
    string SortByTemp,
    DateTime? SortTime,
    int? Status,
    string UndertakeUserTemp,
    string MailNo,
    string MailId,
    string MailPriority, string HostId,string MailTo,DateTime? CreateTime)
{
    public object UndertakeUser { get; set; }
    public object SortBy { get; set; }
    public object IgnoreBy { get; set; }
    public bool? IsPrivate { get; set; }

};

