﻿using System.Threading.Channels;
using Confluent.Kafka;
using iPlatformExtension.Common.MQ.KafKa.Handlers.Consumer;
using iPlatformExtension.Common.MQ.KafKa.Models;
using iPlatformExtension.Common.MQ.KafKa.Route;
using iPlatformExtension.Common.MQ.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.MQ.KafKa.Consumer;

public class ConsumersBuilder<TMessage>
{
    public IServiceCollection Services { get; }

    internal ConsumeDelegatesBuilder<TMessage> RoutesBuilder { get; }

    public OptionsBuilder<ConsumerOptions<TMessage>> OptionsBuilder { get; }

    internal ConsumersBuilder(IServiceCollection services, ConsumeDelegatesBuilder<TMessage> routesBuilder, OptionsBuilder<ConsumerOptions<TMessage>> optionsBuilder)
    {
        Services = services;
        RoutesBuilder = routesBuilder;
        OptionsBuilder = optionsBuilder;
    }

    public ConsumersBuilder<TMessage> ConfigureServerOptions(Action<ConsumerConfig> config, string? configurationSectionName = null)
    {
        if (!string.IsNullOrWhiteSpace(configurationSectionName))
        {
            OptionsBuilder.Configure<IConfiguration>((options, configuration) =>
            {
                configuration.GetSection(configurationSectionName).Bind(options.ServerOptions);
            });
        }
        OptionsBuilder.Configure<ILoggerFactory>((options, loggerFactory) =>
        {
            config(options.ServerOptions);

            var logger = loggerFactory.CreateLogger<ConsumersBuilder<TMessage>>();
            logger.LogInformation("kafka地址：{BootstrapServers}", options.ServerOptions.BootstrapServers);
            
            if (options is {ConcurrentConsume: true, ConcurrentCount: > 0})
            {
                options.BackgroundConsumeChannel = Channel.CreateBounded<ConsumeResult<MessageKey, TMessage>>(
                    new BoundedChannelOptions(options.ConcurrentCount)
                    {
                        FullMode = BoundedChannelFullMode.Wait,
                        SingleWriter = true,
                    });
            }
            options.ConsumerBuilder = new ConsumerBuilder<MessageKey, TMessage>(options.ServerOptions);
        });
        return this;
    }

    public ConsumersBuilder<TMessage> ConfigureConsumerOptions(Action<ConsumerOptions<TMessage>, IServiceProvider> options, string? configurationSectionName = null)
    {
        if (!string.IsNullOrWhiteSpace(configurationSectionName))
        {
            OptionsBuilder.PostConfigure<IConfiguration>((consumerOptions, configuration) =>
            {
                configuration.GetSection(configurationSectionName).Bind(consumerOptions);
            });
        }
        OptionsBuilder.PostConfigure(options);
        return this;
    }

    public ConsumersBuilder<TMessage> ConfigureOptions(string optionsPath = "KafKa:Consumer")
    {
        OptionsBuilder.BindConfiguration(optionsPath);
        return this;
    }

    public ConsumersBuilder<TMessage> AddConsumer<TConsumer>()
        where TConsumer : class
    {
        RoutesBuilder.AddConsumer<TConsumer>();
        Services.AddTransient<TConsumer>();
        return this;
    }

    public ConsumersBuilder<TMessage> AddConsumeFailedHandler<T>() where T : class, IConsumeFailedHandler
    {
        Services.TryAddEnumerable(ServiceDescriptor.Transient<IConsumeFailedHandler, T>());
        return this;
    }

    public ConsumersBuilder<TMessage> SetKeyDeserializer<T>(Action<IServiceCollection>? configureDeserializer = null) 
        where T : class, IDeserializer<MessageKey>
    {
        configureDeserializer?.Invoke(Services);
        OptionsBuilder.PostConfigure<T>((options, deserializer) =>
            options.ConsumerBuilder?.SetKeyDeserializer(deserializer));
        return this;
    }
    
    public ConsumersBuilder<TMessage> SetValueDeserializer<T>(Action<IServiceCollection>? configureDeserializer = null) 
        where T : class, IDeserializer<TMessage>
    {
        configureDeserializer?.Invoke(Services);
        OptionsBuilder.PostConfigure<T>((options, deserializer) =>
            options.ConsumerBuilder?.SetValueDeserializer(deserializer));
        return this;
    }

    public IServiceCollection Build()
    {
        OptionsBuilder.Validate(options => options.ConsumerBuilder is not null);
        Services.AddSingleton(RoutesBuilder.Build());
        Services.TryAddSingleton<ConsumeContextAccessor>();
        return Services;
    }
}