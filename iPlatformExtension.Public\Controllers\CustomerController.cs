﻿using System.ComponentModel;
using System.Net.Mime;
using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Commands.Customer.Contract;
using iPlatformExtension.Public.Applications.Models.Customer;
using iPlatformExtension.Public.Applications.Models.Customer.Contract;
using iPlatformExtension.Public.Applications.Queries.Customer;
using iPlatformExtension.Public.Applications.Queries.Customer.Contract;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 客户控制器
/// </summary>
[Route("[controller]")]
[ApiController]
[Tags("客户信息控制器")]
public class CustomerController : ControllerBase
{
    private readonly IMediator _mediator;

    /// <summary>
    /// 客户控制器构造函数
    /// </summary>
    /// <param name="mediator"></param>
    public CustomerController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// 搜索客户申请人接口
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("SearchApplicant")]
    public async Task<IEnumerable<SearchApplicantDto>> SearchApplicant([FromQuery] SearchApplicantQuery query)
    {
        return await _mediator.Send(query);
    }

    /// <summary>
    /// 销售查询
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("SearchFollowUser")]
    public async Task<IEnumerable<SearchFollowUserDto>> SearchFollowUser([FromQuery] SearchFollowUserQuery query)
    {
        return await _mediator.Send(query);
    }

    /// <summary>
    /// 联系类别查询
    /// </summary>
    /// <returns></returns>
    [HttpGet("SearchContactType")]
    public async Task<IEnumerable<SearchContactTypeDto>> SearchContactType()
    {
        return await _mediator.Send(new SearchContactTypeQuery());
    }
    
    
    /// <summary>
    /// 异步搜索客户信息。
    /// </summary>
    /// <param name="keyword">查询关键词，可以是客户名称或描述等信息。</param>
    /// <param name="isCooperation">是否是合作客户，null表示不限。</param>
    /// <returns>返回一个任务，该任务的结果是客户信息的集合。</returns>
    [HttpGet]
    public async Task<IEnumerable<CustomerInfoDto>> SearchCustomerInfoAsync(
        [FromQuery] string? keyword, 
        [FromQuery] bool? isCooperation)
    {
        // 使用Mediator发送查询请求，查询满足条件的客户信息
        return await _mediator.Send(new CustomerInfoQuery(keyword, isCooperation), HttpContext.RequestAborted);
    }

    /// <summary>
    /// CRM创建合同接口
    /// </summary>
    /// <param name="crmCustomerId">CRM客户id</param>
    /// <param name="dto">合同数据</param>
    /// <returns>统一响应结果</returns>
    [EndpointName(nameof(PostCrmContractAsync))]
    [EndpointSummary("CRM创建合同接口")]
    [EndpointDescription("CRM回调业务系统，增加合同数据，合同明细以及相关文件")]
    [HttpPost("crm/{crmCustomerId}/contract")]
    [Consumes(MediaTypeNames.Multipart.FormData)]
    [ProducesResponseType<ResultData>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    [Authorize(AuthenticationSchemes = BladeAuthOptions.SchemeName)]
    public Task PostCrmContractAsync(
        [FromRoute, Description("CRM客户id")] string crmCustomerId, 
        [FromForm, Description("合同数据")] CrmContractCreateDto dto)
    {
        return _mediator.Send(new CreatContractCommand(crmCustomerId, dto), HttpContext.RequestAborted);
    }
    
    /// <summary>
    /// CRM更新合同接口
    /// </summary>
    /// <param name="crmContractId">CRM合同id</param>
    /// <param name="document">jsonPatch合同数据</param>
    /// <returns>统一响应</returns>
    [EndpointName(nameof(UpdateCrmContractAsync))]
    [EndpointSummary("CRM更新合同接口")]
    [EndpointDescription("""
                         CRM回调业务系统，更新合同主表数据。
                         json-patch path说明如下：
                         - `/contractTitle` - 更新合同标题
                         - `/signingDate` - 更新签订日期，日期格式：YYYY-MM-DD
                         - `/effectiveDate` - 更新生效日期，日期格式：YYYY-MM-DD
                         - `/endDate` - 更新截止日期，日期格式：YYYY-MM-DD
                         - `/myContractingEntity` - 更新我方签约主体
                         - `/customerContractingEntity` - 更新对方签约主体
                         - `/operator` - 更新操作用户编号
                         - `/isVoid` - 更新合同是否作废状态
                         - `/voidReason` - 更新合同作废原因
                         """)]
    [HttpPatch("crm-contract/{crmContractId}")]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    [ProducesResponseType<ResultData>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    [Authorize(AuthenticationSchemes = BladeAuthOptions.SchemeName)]
    public Task UpdateCrmContractAsync(
        [FromRoute, Description("CRM合同id")] string crmContractId, 
        [FromBody, Description("jsonPatch合同数据")] JsonPatchDocument<CrmContractPatchDto> document)
    {
        return _mediator.Send(new UpdateContractCommand(crmContractId, document), HttpContext.RequestAborted);
    }
    
    /// <summary>
    /// CRM添加合同明细接口
    /// </summary>
    /// <remarks>CRM回调业务系统，增加合同明细归档数据。</remarks>
    /// <remarks>一个业务类型只能有一条明细数据。</remarks>
    /// <remarks> 一次请求只接受一个合同的明细数据。</remarks>
    /// <param name="crmContractId">CRM合同id</param>
    /// <param name="details">归档后的合同明细数据</param>
    /// <returns>统一响应接口</returns>
    [EndpointName(nameof(AddCrmContractDetailsAsync))]
    [EndpointSummary("CRM添加合同明细接口")]
    [EndpointDescription("""
                         CRM回调业务系统，增加合同明细归档数据。
                         一个业务类型只能有一条明细数据。
                         一次请求只接受一个合同的明细数据。
                         """)]
    [HttpPost("crm-contract/{crmContractId}/details")]
    [Consumes(MediaTypeNames.Application.Json)]
    [ProducesResponseType<ResultData>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    [Authorize(AuthenticationSchemes = BladeAuthOptions.SchemeName)]
    public Task AddCrmContractDetailsAsync(
        [FromRoute, Description("CRM合同id")] string crmContractId, 
        [FromBody, Description("归档后的合同明细数据")] IEnumerable<CreateContractDetailDto> details)
    {
        return _mediator.Send(new AddContractDetailsCommand(crmContractId, details), HttpContext.RequestAborted);
    }

    /// <summary>
    /// CRM合同查询接口
    /// </summary>
    /// <param name="crmContractId">crm合同id</param>
    /// <returns>CRM合同信息</returns>
    [EndpointName(nameof(QueryCrmContractAsync))]
    [EndpointSummary("CRM合同查询接口")]
    [EndpointDescription("目前仅用于前期对接测试。数据没有经过转换和处理。")]
    [HttpGet("crm/contract/{crmContractId}")]
    [ProducesResponseType<ResultData<CrmContractDto>>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    public Task<CrmContractDto> QueryCrmContractAsync([FromRoute, Description("crm合同id")]string crmContractId)
    {
        return _mediator.Send(new CrmContractQuery(crmContractId));
    }
}