﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Commission;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.File;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class FinishDeliveryCommandHandler(IMediator mediator) 
    : IRequestHandler<FinishDeliveryCommand>
{
    public async Task Handle(FinishDeliveryCommand request, CancellationToken cancellationToken)
    {
        var procId = request.ObjectId!;
        
        await mediator.Send(new RefreshCommissionDateCommand(procId), cancellationToken);
        
        await mediator.Send(new InsertProcCommand(procId), cancellationToken);

        await mediator.Send(new DeliveryFinishPackageFilesCommand(procId), cancellationToken);
    }
}