﻿namespace iPlatformExtension.Model.Dto;

/// <summary>
/// 申请人类型
/// </summary>
public sealed class ApplicantTypeDto : INameInfo
{
    /// <summary>
    /// 编码
    /// </summary>
    public string Code { get; set; } = default!;

    /// <summary>
    /// id
    /// </summary>
    public string Id { get; set; } = default!;
    
    /// <summary>
    /// 中文名
    /// </summary>
    public string? CnName { get; set; }
    
    /// <summary>
    /// 英文名
    /// </summary>
    public string? EnName { get; set; }
}