﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeesKingdeeMaterialQueryHandler(
    IFreeSql freeSql,
    IInvoiceAliasConfigurationRepository configurationRepository)
    : IRequestHandler<FeesKingdeeMaterialQuery, IEnumerable<FeeKingdeeMaterialDto>>
{
    public async Task<IEnumerable<FeeKingdeeMaterialDto>> Handle(FeesKingdeeMaterialQuery request, CancellationToken cancellationToken)
    {
        var feeIds = request.FeeIds;
        var kingdeeMaterialTaxationKeys = await freeSql.Select<CaseFeeList>().WithLock()
            .From<CaseProcInfo, CaseInfo>()
            .InnerJoin((caseFee, procInfo, caseInfo) => caseFee.ProcId == procInfo.ProcId)
            .InnerJoin((caseFee, procInfo, caseInfo) => procInfo.CaseId == caseInfo.Id)
            .Where(tables => feeIds.Contains(tables.t1.FeeId))
            .ToListAsync((caseFee, procInfo, caseInfo) => new
            {
                caseFee.FeeId,
                caseInfo.ApplyTypeId,
                KingdeeMaterialTaxationKey = new KingdeeMaterialTaxationKey(caseFee.FeeTypeNameId, caseInfo.CaseTypeId,
                    caseInfo.CaseDirection, caseFee.FeeClass)
            }, cancellationToken);

        ICacheableRepository<KingdeeMaterialTaxationKey,
            IGrouping<KingdeeMaterialTaxationKey, KingdeeMaterialTaxationInfo>> invoiceAliasConfigurationRepository =
            configurationRepository;
        
        return await kingdeeMaterialTaxationKeys.ToAsyncEnumerable().SelectAwait(async dto =>
        {
            var infos =
                await invoiceAliasConfigurationRepository.GetCacheValueAsync(dto.KingdeeMaterialTaxationKey) as
                IEnumerable<KingdeeMaterialTaxationInfo> ?? Array.Empty<KingdeeMaterialTaxationInfo>();

            var result = new FeeKingdeeMaterialDto
            {
                FeeId = dto.FeeId,
                FeeClass = dto.KingdeeMaterialTaxationKey.FeeClass,
                ApplyTypeId = dto.ApplyTypeId,
                CaseDirection = dto.KingdeeMaterialTaxationKey.CaseDirection,
                CaseType = dto.KingdeeMaterialTaxationKey.CaseType,
                FeeTypeNameId = dto.KingdeeMaterialTaxationKey.FeeTypeNameId,
            };

            if (!infos.Any())
                return result;

            var matchedInfo =
                infos.FirstOrDefault(info =>
                    !string.IsNullOrWhiteSpace(info.ApplyType) && info.ApplyType == dto.ApplyTypeId) ?? infos.First();

            result.TaxationCode = matchedInfo.TaxationCode;
            result.TaxationName = matchedInfo.TaxationName;
            result.KingdeeMaterialCode = matchedInfo.KingdeeMaterialCode;
            result.KingdeeMaterialName = matchedInfo.KingdeeMaterialName;
            result.FeeNameAlias = matchedInfo.FeeNameAlias;

            return result;

        }).ToArrayAsync(cancellationToken);
    }
}