﻿using System.Text.Json.Serialization;
using iPlatformExtension.Model.BaseModel;
using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Commission.Application.Models.WinningReward;

/// <summary>
/// 胜诉奖励详细数据
/// </summary>
public class RewardUserDetail
{
    /// <summary>
    /// 序号
    /// </summary>
    [ExcelColumn(Name = "序号", Index = 0)]
    [JsonIgnore]
    public int SerialNumber { get; set; }
    
    /// <summary>
    /// 任务id
    /// </summary>
    [ExcelIgnore]
    public string ProcId { get; set; }=string.Empty;
    
    /// <summary>
    /// 我方文号
    /// </summary>
    [ExcelColumn(Name = "我方文号", Index = 1)]
    public string Volume { get; set; } = string.Empty;

    /// <summary>
    /// 申请号
    /// </summary>
    [ExcelColumn(Name = "申请号", Index = 2)]
    public string AppNo { get; set; }=string.Empty;

    /// <summary>
    /// 任务编号
    /// </summary>
    [ExcelColumn(Name = "任务编号", Index = 4)]
    public string ProcNo { get; set; } = string.Empty;

    /// <summary>
    /// 商标名称
    /// </summary>
    [ExcelColumn(Name = "商标名称", Index = 5)]
    public string CaseName { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    [ExcelColumn(Name = "任务名称", Index = 6)]
    public string CtrlProcName { get; set; }=string.Empty;

    /// <summary>
    /// 客户名称
    /// </summary>
    [ExcelColumn(Name = "客户名称", Index = 3)]
    public string CustomerName { get; set; }=string.Empty;

    /// <summary>
    /// 裁定结果
    /// </summary>
    [ExcelColumn(Name = "裁定结果", Index = 7)]
    public string RulingResult { get; set; } = string.Empty;

    /// <summary>
    /// 是否形势变更
    /// </summary>
    [ExcelColumn(Name = "是否情势变更", Index = 8)]
    public string SituationChanged { get; set; } = string.Empty;

    /// <summary>
    /// 受益人名称
    /// </summary>
    [ExcelColumn(Name = "受益人", Index = 9)]
    public string BeneficiaryName { get; set; } = string.Empty;

    /// <summary>
    /// 受益人类型
    /// </summary>
    [JsonConverter(typeof(JsonNumberEnumConverter<WinningRewardBeneficiaryType>))]
    [ExcelIgnore]
    public WinningRewardBeneficiaryType RewardBeneficiaryType { get; set; }

    /// <summary>
    /// 奖励类型
    /// </summary>
    [ExcelColumn(Name = "奖励类型", Index = 11)]
    public string RewardType { get; set; } = string.Empty;

    /// <summary>
    /// 受益人奖励
    /// </summary>
    [ExcelColumn(Name = "胜诉奖励", Index = 10)]
    public decimal BeneficiaryReward { get; set; }

    /// <summary>
    /// 计提时间
    /// </summary>
    [ExcelColumn(Name = "计提时间", Index = 12, Format = "yyyy-MM-dd")]
    public DateOnly CommissionDate { get; set; }

    /// <summary>
    /// 推送状态
    /// </summary>
    [ExcelColumn(Name = "推送状态", Index = 13)]
    public string PushedStatus { get; set; } = "未推送";
    
    
}