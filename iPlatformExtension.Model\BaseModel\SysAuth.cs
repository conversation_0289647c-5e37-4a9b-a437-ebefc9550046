using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_auth", DisableSyncStructure = true)]
	public partial class SysAuth {

		[ Column(Name = "auth_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string AuthId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "auth_type", StringLength = 50)]
		public string AuthType { get; set; }

		[ Column(Name = "auth_user", StringLength = 50)]
		public string AuthUser { get; set; }

		[ Column(Name = "control_all")]
		public int? ControlAll { get; set; } = 0;

		[ Column(Name = "control_business", StringLength = 1000)]
		public string ControlBusiness { get; set; }

		[ Column(Name = "control_button", StringLength = 1000)]
		public string ControlButton { get; set; }

		[ Column(Name = "control_field", StringLength = 1000)]
		public string ControlField { get; set; }

		[ Column(Name = "form_id", StringLength = 50)]
		public string FormId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

	}

}
