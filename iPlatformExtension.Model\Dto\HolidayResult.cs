﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Dto
{
    public class HolidayResult
    {
        public int code { get; set; }
        public Dictionary<string, Holidays> holiday { get; set; }

    }

    public class Holidays
    {
        public bool holiday { get; set; }
        public string name { get; set; }
        public int wage { get; set; }
        public string date { get; set; }
        public int rest { get; set; }
    }
}
