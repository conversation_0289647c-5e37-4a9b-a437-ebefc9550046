﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Commission.Application.Models.Trademark;

/// <summary>
/// 国内商标任权值导出
/// </summary>
public class ProcWeightDetailExport
{
    /// <summary>
    /// 任务id
    /// </summary>
    [ExcelColumn(Ignore = true)]
    public string ProcId { get; set; } = string.Empty;
    
    /// <summary>
    /// 序号
    /// </summary>
    [ExcelColumn(Name = "序号", IndexName = "A")]
    public int SerialNumber { get; set; }
    
    /// <summary>
    /// 我方文号
    /// </summary>
    [ExcelColumn(Name = "我方文号", IndexName = "B")]
    public string Volume { get; set; } = string.Empty;

    /// <summary>
    /// 申请号
    /// </summary>
    [ExcelColumn(Name = "申请号", IndexName = "C")]
    public string AppNo { get; set; }=string.Empty;

    /// <summary>
    /// 任务编号
    /// </summary>
    [ExcelColumn(Name = "任务编号", IndexName = "D")]
    public string ProcNo { get; set; } = string.Empty;

    /// <summary>
    /// 商标名称
    /// </summary>
    [ExcelColumn(Name = "商标名称", IndexName = "E")]
    public string CaseName { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    [ExcelColumn(Name = "任务名称", IndexName = "F")]
    public string CtrlProcName { get; set; }=string.Empty;

    /// <summary>
    /// 任务权值
    /// </summary>
    [ExcelColumn(Name = "任务权值", IndexName = "I")]
    public decimal ProcPoint { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    [ExcelColumn(Name = "客户名称", IndexName = "J")]
    public string CustomerName { get; set; }=string.Empty;

    /// <summary>
    /// 是否大客户
    /// </summary>
    [ExcelColumn(Name = "是否大客户", IndexName = "M")]
    public string BigClient { get; set; } = "否";

    /// <summary>
    /// 任务状态
    /// </summary>
    [ExcelColumn(Name = "任务状态", IndexName = "K")]
    public string ProcStatus { get; set; } = string.Empty;

    /// <summary>
    /// 任务标识
    /// </summary>
    [ExcelColumn(Name = "任务标识", IndexName = "G")]
    public string ProcMark { get; set; } = "/";

    /// <summary>
    /// 主承办人
    /// </summary>
    [ExcelColumn(Name = "主承办人", IndexName = "H")]
    public string UndertakerName { get; set; }=string.Empty;

    /// <summary>
    /// 提成生效日
    /// </summary>
    [ExcelColumn(Name = "提成生效日", IndexName = "L", Format = "yyyy-MM-dd")]
    public DateOnly CommissionDate { get; set; }

    /// <summary>
    /// 代理费合计
    /// </summary>
    [ExcelColumn(Name = "代理费合计", IndexName = "N")]
    public string AgentFees { get; set; } = string.Empty;

    /// <summary>
    /// 国际分类
    /// </summary>
    [ExcelColumn(Name = "国际分类", IndexName = "O")]
    public string TrademarkClasses { get; set; } = string.Empty;
}