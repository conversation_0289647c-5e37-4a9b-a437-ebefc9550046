﻿namespace iPlatformExtension.Outsourcing.Infrastructure.MessageQueue;

/// <summary>
/// 主题信息
/// </summary>
public class TopicInfo
{
    /// <summary>
    /// 名称
    /// </summary>
    public string TopicName { get; set; } = string.Empty;

    /// <summary>
    /// 分区号
    /// </summary>
    public int? Partition { get; set; }

    /// <summary>
    /// 偏移量
    /// </summary>
    public long? Offset { get; set; }
}