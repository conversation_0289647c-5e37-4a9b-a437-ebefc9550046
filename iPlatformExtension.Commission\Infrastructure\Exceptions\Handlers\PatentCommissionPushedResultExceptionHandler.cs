﻿using iPlatformExtension.Commission.Application.Commands.Patent;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class PatentCommissionPushedResultExceptionHandler(ILogger<PushedPatentWeightResultCommand> logger)
    : IRequestExceptionHandler<PushedPatentWeightResultCommand, Unit, Exception>
{
    public Task Handle(PushedPatentWeightResultCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        logger.LogPatentCommissionPushedError(exception);
        state.SetHandled(Unit.Value);
        return Task.CompletedTask;
    }
}