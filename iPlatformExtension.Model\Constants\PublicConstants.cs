﻿namespace iPlatformExtension.Model.Constants
{
    /// <summary>
    /// 常用常量定义
    /// David
    /// </summary>
    public class Comm
    {
        //public const string Version = "?v=********";
        // public static string Version { get { return "?v=" + System.Configuration.ConfigurationManager.AppSettings["version"]; } }

        public const string DalName = "iPlatform.Dal";

        public const string ClientInfo = "ClientInfo";

        public const string UserModel = "UserModel";

        public const string UserMenu = "UserMenu";

        public const string TempFile = "TempFile";

        public const string SessionUser = "UserInfo";
    }

    /// <summary>
    /// 可被替换的常量定义
    /// </summary>
    public class REPLACE
    {
        /// <summary>
        /// web站点路径
        /// </summary>
        public const string web_site = "key_web_site";
    }

    /// <summary>
    /// 费用总类
    /// </summary>
    public class FeeClass
    {
        /// <summary>
        /// 官费
        /// </summary>
        public const string Official = "O";

        /// <summary>
        /// 代理费
        /// </summary>
        public const string Agent = "A";

        /// <summary>
        ///第三方费用 
        /// </summary>
        public const string Third = "T";
    }

    /// <summary>
    /// 收款状态
    /// </summary>
    public class FEE_PAY_STATUS
    {
        /// <summary>
        /// 未收款
        /// </summary>
        public const string STATUS_NO = "no";

        /// <summary>
        /// 已收款
        /// </summary>
        public const string STATUS_YES = "yes";

        /// <summary>
        /// 不收款
        /// </summary>
        public const string STATUS_NOT = "not";

        /// <summary>
        /// 部分收款
        /// </summary>
        public const string STATUS_PART = "part";

    }

    /// <summary>
    /// 请款状态
    /// </summary>
    public class FEE_REQ_STATUS
    {
        /// <summary>
        /// 未导出,待缴费
        /// </summary>
        public const string REQ_1000 = "1000";

        /// <summary>
        /// 已导出,待缴费完成后更新
        /// </summary>
        public const string REQ_2000 = "2000";

        /// <summary>
        /// 缴官费完成 
        /// </summary>
        public const string REQ_5000 = "5000";
    }

    /// <summary>
    /// 缴官费状态
    /// </summary>
    public class OFFICER_STATUS
    {
        /// <summary>
        /// 未缴费
        /// </summary>
        public const string S0 = "0";

        /// <summary>
        /// 缴费中
        /// </summary>
        public const string S1000 = "1000";

        /// <summary>
        /// 不缴费
        /// </summary>
        public const string S3000 = "3000";

        /// <summary>
        /// 已缴费
        /// </summary>
        public const string S5000 = "5000";
    }

    /// <summary>
    /// 请款状态
    /// </summary>
    public class RECEIVE_STATUS
    {
        /// <summary>
        /// 未请款
        /// </summary>
        public const string S0 = "0";

        /// <summary>
        /// 请款中
        /// </summary>
        public const string S1000 = "1000";

        /// <summary>
        /// 不请款
        /// </summary>
        public const string S3000 = "3000";

        /// <summary>
        /// 已请款
        /// </summary>
        public const string S5000 = "5000";
    }

    /// <summary>
    /// 流程状态定义
    /// </summary>
    public class FLOW_STATUS
    {
        /// <summary>
        /// 流程开始
        /// </summary>
        public const int START = 0;

        /// <summary>
        /// 流程异常
        /// </summary>
        public const int S500 = 500;

        /// <summary>
        /// 流程开始
        /// </summary>
        public const int S1000 = 1000;

        /// <summary>
        /// 
        /// </summary>
        public const int S2000 = 2000;

        /// <summary>
        /// 1、商标官文解析异常
        /// </summary>
        public const int S2500 = 2500;

        /// <summary>
        /// 
        /// </summary>
        public const int S3000 = 3000;

        /// <summary>
        ///完成
        /// </summary>
        public const int S5000 = 5000;

        /// <summary>
        /// 流程结束
        /// </summary>
        public const int END = 5000;


        /// <summary>
        /// 商标官文主动忽略管制
        /// </summary>
        public const int S4000 = 4000;

        /// <summary>
        /// 外所账单付款状态:已支付 dictionary_name='fee_pay_status'
        /// </summary>
        public const int PAY_STATUS_END = 5600;

        public const int PAY_STATUS_REGISTERED = 2000;
    }

    /// <summary>
    /// 申请类型常量定义
    /// </summary>
    public class APPLY_TYPE_CODE
    {
        /// <summary>
        /// 发明
        /// </summary>
        public const string CODE_IV = "IV";

        /// <summary>
        /// 实用新型
        /// </summary>
        public const string CODE_UT = "UT";

        /// <summary>
        /// 外观设计
        /// </summary>
        public const string CODE_DE = "DE";
    }

    public class LANGUAGE_TYPE
    {
        /// <summary>
        /// 简体中文
        /// </summary>
        public const string ZH_CN = "zh_cn";

        /// <summary>
        /// 繁体中文
        /// </summary>
        public const string ZH_TW = "zh_tw";

    }

    public class FLOW_AUDIT_TYPE
    {
        /// <summary>
        /// 移交 
        /// </summary>
        public const string HANDOVER = "handover";

        /// <summary>
        /// 移交 
        /// </summary>
        public const string REJECT = "reject";

        /// <summary>
        /// 提交 
        /// </summary>
        public const string SUBMIT = "submit";
    }


    /// <summary>
    /// XMl模板文件定义
    /// </summary>
    public class XML_TEMPLATE
    {
        /// <summary>
        /// 开案提交审核流程
        /// </summary>
        public const string XML_1000 = "1000.xml";
    }


    /// <summary>
    /// 案件类型
    /// </summary>
    public static class CaseType
    {
        /// <summary>
        /// 专利CODE代表值
        /// </summary>
        public const string Patent = "P";


        /// <summary>
        /// 商标CODE代表值
        /// </summary>
        public const string Trade = "T";

        /// <summary>
        /// 法律CODE代表值
        /// </summary>
        public const string Legal = "L";

        /// <summary>
        /// 版权CODE代表值
        /// </summary>
        public const string Copy = "C";

        /// <summary>
        /// 项目CODE代表值
        /// </summary>
        public const string Project = "X";

        /// <summary>
        /// 所有案件类型
        /// </summary>
        public static readonly IEnumerable<string> AllTypes = new[] { Patent, Trade, Legal, Copy, Project };

        /// <summary>
        /// 是否属于专利类型的案件类型
        /// </summary>
        /// <param name="caseType">案件类型</param>
        /// <returns>案件类型为<c>P</c>、<c>L</c>、<c>X</c>为true，<c>C</c>、<c>T</c>为false</returns>
        /// <exception cref="ArgumentOutOfRangeException">案件类型不是<code>P,C,L,T,X</code></exception>
        public static bool IsBelongPatent(string caseType) => caseType switch
        {
            Copy => false,
            Trade => false,
            Patent => true,
            Legal => true,
            Project => true,
            _ => throw new ArgumentOutOfRangeException($"没有该案件类型{caseType}")
        };

    }

    /// <summary>
    /// 邮件接收所属用户类型
    /// </summary>
    public class MAIL_ADDRESS_TYPE
    {
        public const string All = "";

        public const string TO = "TO";

        public const string CC = "CC";

        public const string BCC = "BCC";

    }

    /// <summary>
    /// 常用符号定义
    /// </summary>
    public class SYMBOL
    {
        /// <summary>
        /// 分号
        /// </summary>
        public const string SEMICOLON = ";";

        /// <summary>
        /// 逗号
        /// </summary>
        public const string COMMA = ",";
    }

    /// <summary>
    /// 修改为静态类型
    /// 配合反射机制，动态完成按名称检查和移除缓存功能
    /// </summary>
    public class CACHE
    {
        public static string sys_dictionary { get { return "sys_dictionary"; } }
        public static string foreign_info { get { return "foreign_info"; } }

        /// <summary>
        /// 国家
        /// </summary>
        public static string bas_country { get { return "bas_country"; } }

        /// <summary>
        /// 相关属性
        /// </summary>
        public static string bas_related_type { get { return "bas_related_type"; } }

        ///// <summary>
        ///// 省份
        ///// </summary>
        //public static string PROVINCE { get { return "PROVINCE"; } }
        ///// <summary>
        ///// 客户来源
        ///// </summary>
        //public static string CUSTOMERFROM { get { return "CUSTOMERFROM"; } }
        ///// <summary>
        ///// 客户分类
        ///// </summary>
        //public static string CUSTOMERTYPE { get { return "CUSTOMERTYPE"; } }

        /// <summary>
        /// 案件类型  专利、商标、版权
        /// </summary>
        public static string case_type { get { return "case_type"; } }

        ///// <summary>
        ///// 客户信誉等级
        ///// </summary>
        //public static string CUSTOMERCREDIT { get { return "CUSTOMERCREDIT"; } }

        ///// <summary>
        ///// 称呼
        ///// </summary>
        //public static string Call { get { return "Call"; } }

        /// <summary>
        /// 部门
        /// </summary>
        public static string dept_info { get { return "dept_info"; } }

        /// <summary>
        /// 部门(ALL)
        /// </summary>
        public static string dept_info_all { get { return "dept_info_all"; } }

        /// <summary>
        /// 业务类型
        /// </summary>
        public static string bas_business_type { get { return "bas_business_type"; } }

        /// <summary>
        /// 专利类型
        /// </summary>
        public static string bas_apply_type { get { return "bas_apply_type"; } }


        /// <summary>
        /// 案件状态
        /// </summary>
        public static string bas_case_status { get { return "bas_case_status"; } }

        /// <summary>
        /// 处理事项
        /// </summary>
        public static string bas_ctrl_proc { get { return "bas_ctrl_proc"; } }


        /// <summary>
        /// 处理事项更新栏位基础数据
        /// </summary>
        public static string upproc_cnfig { get { return "upproc_cnfig"; } }


        /// <summary>
        /// 申请人类型
        /// </summary>
        public static string bas_applicant_type { get { return "bas_applicant_type"; } }


        ///// <summary>
        ///// 申请人的实体类型
        ///// </summary>
        //public static string APPLICANTENTITYPE { get { return "APPLICANTENTITY"; } }

        ///// <summary>
        ///// 保存数据自动补全配置表
        ///// </summary>
        //public static string AUTOCOMPLETE { get { return "AUTOCOMPLETE"; } }

        ///// <summary>
        ///// 客户保密等级
        ///// </summary>
        //public static string CUSTOMERSECRET { get { return "CUSTOMERSECRET"; } }

        /// <summary>
        /// obj_id对应部门信息
        /// </summary>
        public static string flow_dept { get { return "flow_dept"; } }

        /// <summary>
        /// 角色页面菜单
        /// </summary>
        public static string role_page { get { return "role_page"; } }



        ///// <summary>
        ///// 用户等级(工程师等级)
        ///// </summary>
        //public static string USERRANK { get { return "USERRANK"; } }

        ///// <summary>
        ///// 客户联系人的类别 财务联系人、发明人联系人、业务联系人
        ///// </summary>
        //public static string CONTACTTYPE { get { return "CONTACTTYPE"; } }

        ///// <summary>
        ///// 联系客户的方式 电话、邮件、拜访
        ///// </summary>
        //public static string VISITTYPE { get { return "VISITTYPE"; } }

        /// <summary>
        /// 文件类型
        /// </summary>
        public static string bas_file_type { get { return "bas_file_type"; } }

        /// <summary>
        /// 文件描述(与FILETYPE指向同一缓存)
        /// </summary>
        public static string bas_file_desc { get { return "bas_file_desc"; } }

        /// <summary>
        /// 公司部门组织结构
        /// </summary>
        public static string dept_user { get { return "dept_user"; } }

        public static string dept_tree { get { return "dept_tree"; } }

        /// <summary>
        /// 发文类型
        /// </summary>
        public static string mail_type { get { return "mail_type"; } }

        ///// <summary>
        ///// 报表类型
        ///// </summary>
        //public static string REPORTTYPE { get { return "REPORTTYPE"; } }

        public static string dept_role { get { return "dept_role"; } }
        public static string user_role { get { return "user_role"; } }

        public static string flow_node_config { get { return "flow_node_config"; } }

        public static string user { get { return "user"; } }

        public static string bas_district { get { return "bas_district"; } }
        public static string log_data_config { get { return "log_data_config"; } }


        public static string sys_auth { get { return "sys_auth"; } }
        public static string sys_auth_filter { get { return "sys_auth_filter"; } }
        public static string sys_auth_form_column { get { return "sys_auth_form_column"; } }
        public static string sys_auth_form { get { return "sys_auth_form"; } }
        public static string sys_auth_form_filter { get { return "sys_auth_form_filter"; } }

        public static string report_base { get { return "report_base"; } }

        public static string printer { get { return "printer"; } }

        /// <summary>
        /// 用户等级
        /// </summary>
        public const string USER_LEVEL = "user_level";

    }

    /// <summary>
    /// 获取自定义的常用SQL语句   
    /// </summary>
    public class SQL_KEY
    {
        public const string KEY_DICT = "dictionary";
    }


    public class SYS_PERIOD
    {
        /// <summary>
        /// 开案时期
        /// </summary>
        public const string APPLY = "apply";


        /// <summary>
        /// 案件时期
        /// </summary>
        public const string CASE = "case";
    }

    /// <summary>
    /// 案件加急类型
    /// </summary>
    public class CASE_EMERGENT
    {
        public const string NOMORAL = "nomoral";
    }

    /// <summary>
    /// 国家定义
    /// </summary>
    public class SYS_COUNTRY
    {
        /// <summary>
        ///  中国
        /// </summary>
        public const string SYS_CN = "cn";
    }

    /// <summary>
    /// 日期增量类型
    /// </summary>
    public class ANALYSIS_DATE
    {
        /// <summary>
        /// 年-月增量
        /// </summary>
        public const string YEAR_MONTH = "Y-M";

        /// <summary>
        /// 月-日增量
        /// </summary>
        public const string MONTH_DAY = "M-D";

        /// <summary>
        /// 日-月增量
        /// </summary>
        public const string DAY_MONTH = "D-M";

    }

    /// <summary>
    /// 向官方缴纳官费的方式
    /// </summary>
    public class PAY_MENT_WAY
    {
        /// <summary>
        /// 按代理机构
        /// </summary>
        public const string BY_AGENCY = "by_agency";

        /// <summary>
        /// 按第一申请人
        /// </summary>
        public const string BY_APPLICANT = "by_firstappliacnt";

    }

    public class SYS_ITEM_TYPE
    {
        /// <summary>
        /// 按优先权项数
        /// </summary>
        public const string ITEM_PR = "item_pr";

        /// <summary>
        /// 按说明书页数
        /// </summary>
        public const string ITEM_SP = "item_sp";

        /// <summary>
        /// 按权利项数
        /// </summary>
        public const string ITEM_CL = "item_cl";
    }

    /// <summary>
    /// 币别定义
    /// </summary>
    public class SYS_CURRENCY
    {
        public const string CNY = "CNY";
    }

    /// <summary>
    /// 费用类型
    /// </summary>
    public class SYS_FEE_TYPE
    {

        /// <summary>
        /// 案件加急代理费
        /// </summary>
        public const string TYPE_110 = "110";

        /// <summary>
        /// 优先权代理费
        /// </summary>
        public const string TYPE_111 = "111";

        /// <summary>
        /// 超页代理费
        /// </summary>
        public const string TYPE_112 = "112";

        /// <summary>
        /// 超项代理费
        /// </summary>
        public const string TYPE_113 = "113";
    }


    /// <summary>
    /// 系统角色
    /// </summary>
    public class SYS_ROLE
    {
        /// <summary>
        /// 具有此权限的用户可以查看所有应缴管理的数据
        /// </summary>
        public const string ROLE_OC = "OC";

        /// <summary>
        /// 国内流程部递交案件权限
        /// </summary>
        public const string ROLE_DE = "DE";

        /// <summary>
        /// 代理费配置角色
        /// </summary>
        public const string ROLE_AFEER = "AFEER";
    }


    /// <summary>
    /// 系统角色
    /// </summary>
    public class CASE_STATUS
    {
        /// <summary>
        /// 案件状态为结案
        /// </summary>
        public const string CLOSE = "CLOSE";
    }

    public class CASE_PROC_STATUS
    {
        /// <summary>
        /// 未处理
        /// </summary>
        public const string WCL = "WCL";

        /// <summary>
        /// 撰写
        /// </summary>
        public const string ZG = "ZG";

        /// <summary>
        /// 配案
        /// </summary>
        public const string PA = "PA";

        /// <summary>
        /// 完成
        /// </summary>
        public const string WC = "WC";

        /// <summary>
        /// 不处理
        /// </summary>
        public const string BC = "BC";

        /// <summary>
        /// 不处理[流程任务]
        /// </summary>
        public const string BCL = "BCL";

        /// <summary>
        /// 已处理
        /// </summary>
        public const string YCL = "YCL";
    }


    /// <summary>
    /// 前端传入后端界面代码值
    /// </summary>
    public class ASPX_CODE
    {
        /// <summary>
        /// 注册申请 
        /// </summary>
        public const string APPLY_TRADE_MARK = "apply_trademark";

        /// <summary>
        /// 评审类申请 
        /// </summary>
        public const string APPLY_TM_REVIEW = "apply_tm_review";

        /// <summary>
        /// 非注册非评审类申请 
        /// </summary>
        public const string APPLY_TM_OTHER = "apply_tm_other";


        /// <summary>
        /// 注册申请 
        /// </summary>
        public const string APPLY_PATENT = "apply_patent";

        /// <summary>
        /// 专利无效
        /// </summary>
        public const string APPLY_INVALID = "apply_invalid";
    }

    /// <summary>
    /// 任务分类
    /// </summary>
    public class CASE_TASK_ATTRIBUTE
    {
        /// <summary>
        /// 流程内部任务
        /// </summary>
        public const string IC_INTERNAL = "ic_internal";

        /// <summary>
        /// 流程官方任务
        /// </summary>
        public const string IC_OFFICIAL = "ic_official";

        /// <summary>
        /// 代理人内部任务
        /// </summary>
        public const string IP_INTERNAL = "ip_internal";

        /// <summary>
        /// 代理人官方任务
        /// </summary>
        public const string IP_OFFICIAL = "ip_official";

    }


    /// <summary>
    /// 商标案件的申请方式
    /// </summary>
    public class TM_MULTI_TYPE
    {
        /// <summary>
        /// 一类一案
        /// </summary>
        public const string A_CLASS = "a_class_a_case";

        /// <summary>
        /// 多类一案
        /// </summary>
        public const string M_CLASS = "m_class_a_case";
    }

    /// <summary>
    /// 商标通知书代码
    /// </summary>
    public class TM_NOTICE_NAME
    {
        /// <summary>
        /// 商标注册申请书
        /// </summary>
        public const string ZCSQ = "商标注册申请书";

    }

    /// <summary>
    /// 任务事项CODE
    /// </summary>
    public class CASE_PROC_CODE
    {
        /// <summary>
        /// 年费标识
        /// </summary>
        public const string AN = "AN";

        /// <summary>
        /// 商标注册申请
        /// </summary>
        public const string TM_AP = "TAP";

        /// <summary>
        /// 办理登记手续
        /// </summary>
        public const string NOA = "NOA";
    }

    public class CASE_DIRECTION
    {

        /// <summary>
        /// 外-外
        /// </summary>
        public const string CASE_OO = "OO";

        /// <summary>
        /// 外-内
        /// </summary>
        public const string CASE_OI = "OI";

        /// <summary>
        /// 内-外
        /// </summary>
        public const string CASE_IO = "IO";

        /// <summary>
        /// 内-内
        /// </summary>
        public const string CASE_II = "II";
    }
    public class CASE_DIRECTION_ZH
    {

        /// <summary>
        /// OO
        /// </summary>
        public const string CASE_OO = "外外";

        /// <summary>
        /// OI
        /// </summary>
        public const string CASE_OI = "外内";

        /// <summary>
        /// IO
        /// </summary>
        public const string CASE_IO = "内外";

        /// <summary>
        /// II
        /// </summary>
        public const string CASE_II = "内内";
    }

    public class TASK_TYPE
    {

        //public const string CTRL_PROC_ZH = "新申请";
        public const string CTRL_PROC_ZH = "AP";
    }
    public class CASE_STATE
    {
        public const string CASE_STATE_ZH = "结案";
    }
    public class BELONG_COMPANY
    {
        /// <summary>
        /// 杭州华进联浙知识产权代理有限公司
        /// </summary>
        public const string COMPANY_ZHELIAN = "f57bc7f8-d0c0-487b-81b0-2120484979e5";
        /// <summary>
        /// 北京华进京联知识产权代理有限公司杭州分公司
        /// </summary>
        public const string COMPANY_JINGLIAN = "740e5671-e1a5-47c2-8c79-dd0b01af4d55";
    }
    public class COUNTY_ID
    {
        public const string CONUTY_WO = "WO";
    }
    /// <summary>
    /// 官费支付规则
    /// </summary>
    public class SYS_PAY_WAY
    {
        /// <summary>
        /// 垫付
        /// </summary>
        public const string ADVANCED = "advanced";

        /// <summary>
        /// 不垫付
        /// </summary>
        public const string NOADVANCED = "noadvanced";

        /// <summary>
        /// 客户自缴
        /// </summary>
        public const string CUSTOMERPAY = "customerpay";
    }

    /// <summary>
    /// 客户案源人或跟案人
    /// </summary>
    public class CUSTOMER_USER_TYPE
    {
        /// <summary>
        /// 案源人
        /// </summary>
        public const string USER_AY = "AY";

        /// <summary>
        /// 跟案人
        /// </summary>
        public const string USER_GA = "GA";
    }

    /// <summary>
    /// 客户案源人或跟案人授权类型
    ///  0 表示创建人 1 表示案源人 2 表示跟案人 3 表示授权人
    /// </summary>
    public class CUSTOMER_GRANT_TYPE
    {
        /// <summary>
        /// 表示创建人
        /// </summary>
        public const string TYPE0 = "0";

        /// <summary>
        /// 表示案源人
        /// </summary>
        public const string TYPE1 = "1";

        /// <summary>
        /// 表示跟案人
        /// </summary>
        public const string TYPE2 = "2";

        /// <summary>
        /// 表示授权人
        /// </summary>
        public const string TYPE3 = "3";
    }

    /// <summary>
    /// 特殊情况下前端传入后端的自定义界面标识
    /// </summary>
    public class ASPX_TYPE
    {
        /// <summary>
        /// 专利配案界面
        /// </summary>
        public const string ASPX_A00001 = "A00001";
    }

    public class SEND_MAIL_TYPE
    {
        public const string EXPRESS = "MAIL_EXPRESS";
    }
}
