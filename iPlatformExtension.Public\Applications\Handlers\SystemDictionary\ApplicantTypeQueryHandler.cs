﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.SystemDictionary;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.SystemDictionary;

internal sealed class ApplicantTypeQueryHandler(IFreeSql freeSql) : IRequestHandler<ApplicantTypeQuery, IEnumerable<ApplicantTypeDto>>
{
    public async Task<IEnumerable<ApplicantTypeDto>> <PERSON>le(ApplicantTypeQuery request, CancellationToken cancellationToken)
    {
        return await freeSql.Select<BasApplicantType>()
            .WhereIf(request.IsEnable.HasValue, applicantType => applicantType.IsEnabled == request.IsEnable)
            .ToListAsync(type => new ApplicantTypeDto()
            {
                Id = type.ApplicantTypeId,
                Code = type.ApplicantTypeCode,
                CnName = type.ApplicantTypeZhCn,
                EnName = type.ApplicantTypeEnUs
            }, cancellationToken);
    }
}