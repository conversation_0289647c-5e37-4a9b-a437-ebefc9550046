﻿using System.Net.Mime;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Nice;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers;

/// <summary>
/// 尼斯分类控制器
/// </summary>
[ApiController]
[Route("[controller]")]
[Produces(MediaTypeNames.Application.Json)]
public sealed class NiceController : ControllerBase
{
    private readonly IMediator _mediator;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="mediator">中介者组件</param>
    public NiceController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// 获取尼斯分类配置信息
    /// </summary>
    /// <param name="versionId">尼斯分类版本id</param>
    /// <param name="parentNumbers">尼斯分类父级编号</param>
    /// <param name="caseId">案件id。可为空</param>
    /// <returns>尼斯分类配置信息</returns>
    [HttpGet]
    public Task<IEnumerable<NiceCategoryDto>> GetNiceCategoryInfoAsync(
        [FromQuery]string versionId, [FromQuery(Name = "pNos")]string[]? parentNumbers, [FromQuery]string? caseId)
    {
        return _mediator.Send(new CaseNiceCategoryQuery(versionId, parentNumbers ?? Array.Empty<string>(), caseId));
    }

    /// <summary>
    /// 案件国际分类查询
    /// </summary>
    /// <param name="caseId">案件id</param>
    /// <returns>国际分类类别</returns>
    [HttpGet("classes/{caseId}")]
    public Task<IEnumerable<string>> GetCaseNiceClassesAsync(string caseId)
    {
        return _mediator.Send(new CaseNiceClassesQuery(caseId));
    }
}