﻿using System.Security.Claims;
using iPlatformExtension.Common.Converters.Abstraction;
using StackExchange.Redis;

namespace iPlatformExtension.Common.Converters;

public class ClaimsIdentityConverter : IRedisValueConverter<ClaimsIdentity>
{
    public RedisValue ConvertToRedisValue(ClaimsIdentity? obj)
    {
        using var memoryStream = new MemoryStream();
        var writer = new BinaryWriter(memoryStream);
        obj?.WriteTo(writer);
        return memoryStream.ToArray();
    }

    public ClaimsIdentity? ConvertFromRedisValue(RedisValue redisValue)
    {
        var data = new BinaryData((ReadOnlyMemory<byte>) redisValue);
        return new ClaimsIdentity(new BinaryReader(data.ToStream()));
    }
}