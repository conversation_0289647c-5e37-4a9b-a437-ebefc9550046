﻿using System.Collections.Concurrent;

namespace iPlatformExtension.Public.Infrastructure.SignalR
{
    public class ConnectionMapping<T>
    {
        private readonly Dictionary<T, HashSet<string>> _connections = new();

        public int Count
        {
            get
            {
                return _connections.Count;
            }
        }

        public IReadOnlyDictionary<T, HashSet<string>> GetConnections()
        {
            return _connections;
        }

        /// <summary>
        /// 获取连接id集合
        /// </summary>
        /// <param name="userId">用户id</param>
        /// <returns></returns>
        public IEnumerable<string> GetConnectionIds(T userId) =>
            _connections.TryGetValue(userId, out var connectionIds)
                ? connectionIds
                : Enumerable.Empty<string>();

        public void Add(T key, string connectionId)
        {
            lock (_connections)
            {
                HashSet<string> connections;
                if (!_connections.TryGetValue(key, out connections))
                {
                    connections = new HashSet<string>();
                    _connections.Add(key, connections);
                }

                lock (connections)
                {
                    connections.Add(connectionId);
                }
            }
        }

        public IEnumerable<string> GetConnections(T key)
        {
            HashSet<string> connections;
            if (_connections.TryGetValue(key, out connections))
            {
                return connections;
            }

            return Enumerable.Empty<string>();
        }

        public void Remove(T key, string connectionId)
        {
            lock (_connections)
            {
                HashSet<string> connections;
                if (!_connections.TryGetValue(key, out connections))
                {
                    return;
                }

                lock (connections)
                {
                    connections.Remove(connectionId);

                    if (connections.Count == 0)
                    {
                        _connections.Remove(key);
                    }
                }
            }
        }
    }
}
