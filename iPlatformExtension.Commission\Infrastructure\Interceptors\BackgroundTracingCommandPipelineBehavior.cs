﻿using System.Diagnostics;
using iPlatformExtension.Commission.Infrastructure.Models;
using MediatR;

namespace iPlatformExtension.Commission.Infrastructure.Interceptors;

internal sealed class BackgroundTracingCommandPipelineBehavior<TRequest, TResult> 
    : IPipelineBehavior<TRequest, TResult> where TRequest : IBackgroundTracingCommand
{
    public async Task<TResult> Handle(TRequest request, RequestHandlerDelegate<TResult> next, CancellationToken cancellationToken)
    {
        using var activity = new Activity(request.OperationName);

        activity.SetParentId(request.TraceParentId);
        activity.Start();
        
        var result = await next();
        
        activity.Stop();
        
        return result;
    }
}