﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Flow;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Flow
{
    public class GetFlowConfigHandler : IRequestHandler<FlowConfigQuery, List<GetFlowConfigDto>>
    {
        private readonly IFreeSql _freeSql;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public GetFlowConfigHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor)
        {
            _freeSql = freeSql;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<List<GetFlowConfigDto>> Handle(FlowConfigQuery request, CancellationToken cancellationToken)
        {
            var deptId = "base";
            //商标递交旧流程:默认走TMII
            if (request.FlowType == FlowTypeEnum.AT && request.FlowSubType.ToUpper() == "T")
            {
                var operatorId = (_httpContextAccessor.HttpContext?.User).GetUserId();

                var userInfo = await _freeSql.Select<SysUserInfo>().Where(o => o.UserId == operatorId).FirstAsync();
                deptId = userInfo.DeptId;
            }

            //核稿流程没有团队时按旧流程处理
            if (request.FlowType == FlowTypeEnum.EX && string.IsNullOrEmpty(request.TeamId))
            {
                request.FlowSubType = "T";
                Console.WriteLine($"核稿流程没有团队时按旧流程处理");
                //配案发起人所在部门的通用流程subtype=null
                //核稿走承办部门
                deptId = request.ProcDeptId;
            }

            //商标递交旧流程:默认走TMII
            if (request.FlowType == FlowTypeEnum.DE && request.FlowSubType.ToUpper() == "T")
            {
                var deptInfo = await _freeSql.Select<SysDeptInfo>().Where(o => o.DeptCode == "TMII").FirstAsync();
                deptId = deptInfo.DeptId;
            }

            var sql = _freeSql.Select<SysFlowList, SysFlowNode, SysFlowConfig>()
                 .InnerJoin((fl, fn, fc) => fl.NodeId == fn.NodeId)
                 .InnerJoin((fl, fn, fc) => fc.FlowId == fl.FlowId)
                 .Where((fl, fn, fc) => fc.DeptId == deptId && fc.FlowType == request.FlowType && fc.FlowSubType == request.FlowSubType)
                 .OrderBy((fl, fn, fc) => fl.Seq);
            var list = await sql.ToListAsync((fl, fn, fc) => new GetFlowConfigDto
            {
                ListId = fl.ListId,
                FlowId = fl.FlowId,
                Seq = fl.Seq,
                Next = fl.Next,
                NodeId = fl.NodeId,
                AllowEdit = fl.AllowEdit,
                NodeType = fl.NodeType,
                UserList = fl.UserList,
                NodeName = fn.NameZhCn,
                NodeCode = fn.NodeCode,
                IsSkip = fn.IsSkip,
                FlowSubType = fc.FlowSubType
            });

            foreach (var item in list)
            {
                //查询用户UR
                if ("UR".Contains(item.NodeType))
                {
                    if (!string.IsNullOrWhiteSpace(item.UserList))
                    {
                        var userlist = item.UserList.Split(";");
                        item.AuditUsers = _freeSql.Select<SysUserInfo, BasCompany, BasDistrict, SysDeptInfo>()
                        .InnerJoin((ui, bc, bd, dept) => ui.DeptId == dept.DeptId)
                        .InnerJoin((ui, bc, bd, dept) => ui.ManageCompany == bc.CompanyId)
                        .LeftJoin((ui, bc, bd, dept) => bc.DistrictId == bd.DistrictCode)
                        .Where((ui, bc, bd, dept) => userlist.Any(u => u == ui.UserId))
                        .ToList((ui, bc, bd, dept) => new FlowUserDto($"{ui.CnName}({bd.TextZhCn},{dept.DeptName})", ui.UserId, ui.UserName));
                    }
                }
                else if ("T".Contains(item.NodeType))
                {
                    ArgumentException.ThrowIfNullOrEmpty(request.TeamId);

                    var userlist = item.UserList.Split(";");
                    item.AuditUsers = _freeSql.Select<SysUserInfo, BasCompany, BasDistrict, SysDeptInfo, SysTeamAssociateMember>()
                    .InnerJoin((ui, bc, bd, dept, team_mem) => ui.DeptId == dept.DeptId)
                    .InnerJoin((ui, bc, bd, dept, team_mem) => ui.ManageCompany == bc.CompanyId)
                    .InnerJoin((ui, bc, bd, dept, team_mem) => team_mem.UserId == ui.UserId)
                    .InnerJoin((ui, bc, bd, dept, team_mem) => ui.ManageCompany == bc.CompanyId)
                    .LeftJoin((ui, bc, bd, dept, team_mem) => bc.DistrictId == bd.DistrictCode)
                    .Where((ui, bc, bd, dept, team_mem) => userlist.Any(u => u == team_mem.RoleId) && team_mem.TeamId == request.TeamId)
                    .ToList((ui, bc, bd, dept, team_mem) => new FlowUserDto($"{ui.CnName}({bd.TextZhCn},{dept.DeptName})", ui.UserId, ui.UserName));
                }
                else
                {
                    throw new Exception("节点类型不正确,无法找到数据");
                }
            }
            return list;
        }
    }
}
