﻿using iPlatformExtension.MailCenter.Applications.Models.Platform;
using iPlatformExtension.MailCenter.Applications.Queries.Platform;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.MailCenter.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class PlatformController(IMediator mediator) : ControllerBase
    {

        /// <summary>
        /// 获取关联案件（专利、商标案件）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetRelateCaseList")]
        public async Task<IEnumerable<PlatformGetRelateCaseListDto>> GetRelateCaseList([FromQuery] PlatformGetRelateCaseListQuery query)
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取关联任务
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("GetProcList")]
        public async Task<IEnumerable<GetProcListDto>> GetProcList([FromQuery] GetProcListQuery query)
        {
            return await mediator.Send(query);
        }

        /// <summary>
        /// 获取关联客户
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("PlatformRelateCustomer")]
        public async Task<IEnumerable<PlatformRelateCustomerDto>> PlatformRelateCustomer([FromQuery] PlatformRelateCustomerQuery query)
        {
            return await mediator.Send(query);
        }
    }
}
