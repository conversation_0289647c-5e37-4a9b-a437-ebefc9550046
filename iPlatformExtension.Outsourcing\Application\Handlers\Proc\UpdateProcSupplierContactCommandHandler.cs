﻿using AutoMapper;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class UpdateProcSupplierContactCommandHandler(
    IMapper mapper,
    IHttpContextAccessor httpContextAccessor,
    IProcForeignSupplierContactRepository procForeignSupplierContactRepository) 
    : IRequestHandler<UpdateProcSupplierContactCommand>
{
    public async Task Handle(UpdateProcSupplierContactCommand request, CancellationToken cancellationToken)
    {
        var (procId, contactId, document) = request;
        var userId = httpContextAccessor.HttpContext?.User.GetUserId() ?? throw new NotAuthenticatedException();
        var procForeignSupplierContact = await procForeignSupplierContactRepository
            .Where(x => x.ProcId == procId && x.ContactId == contactId)
            .ToOneAsync(cancellationToken);

        if (procForeignSupplierContact is null)
        {
            throw new NotFoundException(contactId, "申请人");
        }

        var patchDto = mapper.Map<ProcContactPatchDto>(procForeignSupplierContact);
        document.ApplyTo(patchDto);
        mapper.Map(patchDto, procForeignSupplierContact);

        procForeignSupplierContact.Updater = userId;
        procForeignSupplierContact.UpdateTime = DateTime.Now;

        await procForeignSupplierContactRepository.UpdateAsync(procForeignSupplierContact, cancellationToken);

        if (await procForeignSupplierContactRepository
                .Where(contact => contact.ProcId == procId).Where(contact => contact.Representative == true)
                .CountAsync(cancellationToken) > 1)
        {
            throw new ApplicationException("不允许添加多个境外代理联系人代表");
        }
    }
}