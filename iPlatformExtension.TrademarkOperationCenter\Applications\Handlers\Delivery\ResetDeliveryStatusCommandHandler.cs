﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.MQ.Models;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class ResetDeliveryStatusCommandHandler(IMediator mediator, IFreeSql freeSql, ConsumeContextAccessor contextAccessor) : IRequestHandler<ResetDeliveryStatusCommand>
{
    public async Task Handle(ResetDeliveryStatusCommand request, CancellationToken cancellationToken)
    {
        var procId = request.ObjectId!;
        var userId = (contextAccessor.ConsumeContext?.User).GetUserId();

        await mediator.Send(new SaveDeliveryResultCommand(procId, new DeliveryResultDto()
        {
            Operation = TrademarkDeliveryOperation.WithdrawDelivery,
            Success = true,
        }, userId, false), cancellationToken);
    }
}