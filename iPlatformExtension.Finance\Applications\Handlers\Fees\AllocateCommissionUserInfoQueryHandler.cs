﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class AllocateCommissionUserInfoQueryHandler(IUserInfoRepository infoRepository)
    : IRequestHandler<AllocateCommissionUserInfoQuery>
{
    public async Task Handle(AllocateCommissionUserInfoQuery request, CancellationToken cancellationToken)
    {
        ICacheableRepository<string, UserBaseInfo> userInfoRepository = infoRepository;
        var result = request.FeesUpdateResult;

        if (!string.IsNullOrWhiteSpace(request.SalesUserId))
            result.Sales = await userInfoRepository.GetCacheValueAsync(request.SalesUserId);

        if (!string.IsNullOrWhiteSpace(request.TrackUserId))
            result.Tracker = await userInfoRepository.GetCacheValueAsync(request.TrackUserId);

        if (!string.IsNullOrWhiteSpace(request.BusinessUserId))
            result.BusinessUser = await userInfoRepository.GetCacheValueAsync(request.BusinessUserId);

        if (!string.IsNullOrWhiteSpace(request.ClueUserId))
            result.ClueUser = await userInfoRepository.GetCacheValueAsync(request.ClueUserId);
    }
}