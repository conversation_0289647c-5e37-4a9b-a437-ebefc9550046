using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_bonus_config", DisableSyncStructure = true)]
	public partial class BasBonusConfig {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "apply_type_id", StringLength = 50, IsNullable = false)]
		public string ApplyTypeId { get; set; }

		[ Column(Name = "base_date_type", StringLength = 50, IsNullable = false)]
		public string BaseDateType { get; set; }

		[ Column(Name = "bonus_amount", DbType = "money")]
		public decimal? BonusAmount { get; set; }

		[ Column(Name = "bonus_point", DbType = "money")]
		public decimal? BonusPoint { get; set; }

		[ Column(Name = "case_direction", StringLength = 50, IsNullable = false)]
		public string CaseDirection { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "ctrl_proc_id", StringLength = 50, IsNullable = false)]
		public string CtrlProcId { get; set; }

		[ Column(Name = "ctrl_proc_property", StringLength = 50)]
		public string CtrlProcProperty { get; set; }

		[ Column(Name = "has_priority", StringLength = 50)]
		public string HasPriority { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "is_same_day", StringLength = 50)]
		public string IsSameDay { get; set; }

		[ Column(Name = "is_substance", StringLength = 50)]
		public string IsSubstance { get; set; }

		[ Column(Name = "pct_enter", StringLength = 50)]
		public string PctEnter { get; set; }

		[ Column(Name = "pct_pub_language", StringLength = 50)]
		public string PctPubLanguage { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
