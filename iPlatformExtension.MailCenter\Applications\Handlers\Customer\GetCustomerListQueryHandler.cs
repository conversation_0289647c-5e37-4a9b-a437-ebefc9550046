using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Customer;
using iPlatformExtension.MailCenter.Applications.Queries.Customer;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Customer
{
    /// <summary>
    /// 获取客户列表查询处理器
    /// </summary>
    internal sealed class GetCustomerListQueryHandler(
        IFreeSql<PlatformFreeSql> freeSql,
        IUserInfoRepository userInfoRepository) : IRequestHandler<GetCustomerListQuery, PageResult<CustomerDto>>
    {
        /// <summary>
        /// 处理查询请求
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>客户信息列表</returns>
        public async Task<PageResult<CustomerDto>> Handle(GetCustomerListQuery request, CancellationToken cancellationToken)
        {
            // 使用两步查询方式解决distinct分页count问题

            // 1. 构建基础查询条件
            var baseQueryCondition = freeSql.Select<CusCustomer, CusJoinList, CusContact>()
                .WithLock()
                .LeftJoin((cc, jl, c) => cc.CustomerId == jl.JoinObjId)
                .LeftJoin((cc, jl, c) => c.ContactId == jl.FormObjId && c.IsEnabled == true )
                .Where((cc, jl, c) => c.Email != null && c.Email != "")
                .WhereIf(!string.IsNullOrWhiteSpace(request.CustomerName), (cc, jl, c) =>
                    cc.CustomerName.Contains(request.CustomerName!) || cc.CustomerFullName.Contains(request.CustomerName!))
                .WhereIf(!string.IsNullOrWhiteSpace(request.CustomerCode), (cc, jl, c) =>
                    cc.CustomerCode.Contains(request.CustomerCode!))
                .WhereIf(request.IsCooperation.HasValue, (cc, jl, c) =>
                    cc.IsCooperation == request.IsCooperation)
                .WhereIf(request.IsEnabled.HasValue, (cc, jl, c) =>
                    cc.IsEnabled == request.IsEnabled)
                .WhereIf(!string.IsNullOrWhiteSpace(request.ContactName), (cc, jl, c) =>
                    c.ContactName == request.ContactName)
                .WhereIf(request.BigAreas != null && request.BigAreas.Count > 0, (cc, jl, c) =>
                    cc.BigArea != null && request.BigAreas!.Contains(cc.BigArea))
                .WhereIf(request.CountryIds != null && request.CountryIds.Count > 0, (cc, jl, c) =>
                    cc.CountryId != null && request.CountryIds!.Contains(cc.CountryId))
                .WhereIf(request.CustomerIds != null && request.CustomerIds.Count > 0, (cc, jl, c) =>
                    cc.CustomerId != null && request.CustomerIds!.Contains(cc.CustomerId))
                .WhereIf(request.ContactType != null && request.ContactType.Count > 0, (cc, jl, c) =>
                    c != null && freeSql.Select<CusJoinList>()
                        .Where(ct => ct.FormObjId == c.ContactId && ct.JoinType == "contact_type" && ct.FormType == "contact")
                        .Where(ct => request.ContactType!.Contains(ct.JoinObjId))
                        .Any());

            // 2. 先执行一个子查询获取去重后的客户ID列表
            var distinctQuery = baseQueryCondition
                .GroupBy((cc, jl, c) => cc.CustomerId);

            // 3. 获取总记录数
            var total = await distinctQuery.CountAsync(cancellationToken);

            // 4. 获取分页后的客户ID列表
            var pageCustomerIds = await distinctQuery
                .Page(request.PageIndex, request.PageSize)
                .ToListAsync(x => x.Key, cancellationToken);

            // 5. 查询分页数据的详细信息
            var customers = await freeSql.Select<CusCustomer, CusJoinList, CusContact>()
                .WithLock()
                .LeftJoin((cc, jl, c) => cc.CustomerId == jl.JoinObjId)
                .LeftJoin((cc, jl, c) => c.ContactId == jl.FormObjId && c.IsEnabled == true)
                .Where((cc, jl, c) => pageCustomerIds.Contains(cc.CustomerId) &&  c.Email != null && c.Email != "")
                .Page(request.PageIndex, request.PageSize)
                .OrderByDescending((cc, jl, c) => cc.CreateTime)
                .ToListAsync((cc, jl, c) => new CustomerDto
                {
                    CustomerId = cc.CustomerId,
                    CustomerName = cc.CustomerName ?? string.Empty,
                    CustomerNameEn = cc.CustomerNameEn,
                    CustomerCode = cc.CustomerCode,
                    CustomerFullName = cc.CustomerFullName,
                    IsCooperation = cc.IsCooperation,
                    IsEnabled = cc.IsEnabled,
                    BigArea = cc.BigArea,
                    CustomerType = cc.CustomerType,
                    BusiUserId = c.ContactId,
                    BusiUserName = c.ContactName,
                    Email = c.Email,
                    Fax = cc.Fax,
                    CreateTime = cc.CreateTime
                }, cancellationToken);

            // 返回分页结果
            return new PageResult<CustomerDto>
            {
                Data = customers,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = total,
            };
        }
    }
}



