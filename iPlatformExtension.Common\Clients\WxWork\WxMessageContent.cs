﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Common.Clients.WxWork
{
    public class WxMessageContent
    {
        public string Title { get; set; }
        public string OperationPath { get; set; }
        /// <summary>
        /// 提交人
        /// </summary>
        public string OperationUser { get; set; }
        /// <summary>
        /// 接收人名称
        /// </summary>
        public string Auditor { get; set; }
        /// <summary>
        /// 接收人工号
        /// </summary>
        public string RecipientUserName { get; set; }
        public string SubmitDate { get; set; }
    }
}
