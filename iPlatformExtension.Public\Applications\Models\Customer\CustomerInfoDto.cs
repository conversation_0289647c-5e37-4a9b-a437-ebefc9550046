﻿using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Public.Applications.Models.Customer;

/// <summary>
/// 客户信息
/// </summary>
public class CustomerInfoDto : INameInfo
{
    /// <summary>
    /// 客户唯一标识符
    /// </summary>
    public string Id { get; set; } = string.Empty;
    
    /// <summary>
    /// 客户中文名称
    /// </summary>
    public string? CnName { get; set; }
    
    /// <summary>
    /// 客户英文名称
    /// </summary>
    public string? EnName { get; set; }

    /// <summary>
    /// 客户全称
    /// </summary>
    public string? FullName { get; set; }

    /// <summary>
    /// 表示客户是否为合作伙伴
    /// </summary>
    public bool IsCooperation { get; set; }

    /// <summary>
    /// 客户编码
    /// </summary>
    public string CustomerCode { get; set; } = string.Empty;
}
