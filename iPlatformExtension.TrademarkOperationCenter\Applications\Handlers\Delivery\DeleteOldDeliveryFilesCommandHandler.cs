﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Implement;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery
{

    /// <summary>
    /// 删除旧流程文件
    /// </summary>
    internal sealed class DeleteOldDeliveryFilesCommandHandler : IRequestHandler<DeleteOldDeliveryFilesCommand>
    {

        private readonly ICaseProcFlowRepository _caseProcFlowRepository;

        private readonly ICaseFileRepository _caseFileRepository;



        public DeleteOldDeliveryFilesCommandHandler(ICaseProcFlowRepository caseProcFlowRepository, ICaseFileRepository caseFileRepository)
        {
            _caseProcFlowRepository = caseProcFlowRepository;
            _caseFileRepository = caseFileRepository;
        }

        public async Task Handle(DeleteOldDeliveryFilesCommand request, CancellationToken cancellationToken)
        {
            var caseProcFlow = await _caseProcFlowRepository.Where(o => o.ProcId == request.ProcId && o.FlowType == "trademark_delivery").FirstAsync(cancellationToken);
            if (caseProcFlow != null)
            {
                await _caseFileRepository.DeleteByProcFlowId(caseProcFlow.ProcFlowId, cancellationToken);
            }

        }
    }
}
