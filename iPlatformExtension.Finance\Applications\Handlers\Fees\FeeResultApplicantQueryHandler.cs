﻿using iPlatformExtension.Finance.Applications.Queries;
using iPlatformExtension.Finance.Applications.Queries.Fees;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Service.Interface;
using MediatR;

namespace iPlatformExtension.Finance.Applications.Handlers.Fees;

internal sealed class FeeResultApplicantQueryHandler(IApplicantQueryService applicantQuery)
    : IRequestHandler<FeeResultApplicantsQuery>
{
    public async Task Handle(FeeResultApplicantsQuery request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
        {
            return ;
        }

        var caseIds = request.CaseIds;
        var feeResult = request.FeeResults;

        var applicants = await applicantQuery.GetApplicantsByCaseIdsAsync(caseIds);
        foreach (var feeItem in feeResult)
        {
            feeItem.Applicants = applicants.TryGetValue(feeItem.CaseId, out var applicantInfo)
                ? applicantInfo
                : Array.Empty<ApplicantInfo>();
        }
    }
}