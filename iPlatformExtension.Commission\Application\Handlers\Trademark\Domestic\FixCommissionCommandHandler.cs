﻿using System.Runtime.CompilerServices;
using System.Text;
using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Common.Extensions;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class FixCommissionsCommandHandler(ISender sender) 
    : IStreamRequestHandler<FixDomesticCommissionsCommand, string>
{
    public async IAsyncEnumerable<string> <PERSON>le(FixDomesticCommissionsCommand request, [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        var procIds = request.ProcIds;
        await using var writer = File.AppendText("C:/Users/<USER>/Desktop/fix_proc_ids.txt");
        foreach (var procId in procIds)
        {
            if (!await sender.Send(new FixDomesticCommissionCommand(procId), cancellationToken)
                    .ConfigureAwait(false)) continue;
            
            await writer.WriteLineAsync(procId).ConfigureAwait(false);
            yield return procId;
        }
    }
}