﻿using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class CreateDomesticRewardExceptionHandler(ILogger<CreateRewardCommand> logger) : IRequestExceptionHandler<CreateRewardCommand, Unit, Exception>
{
    public Task Handle(CreateRewardCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        var (procId, dto) = request;
        logger.LogInsertDomesticRewardDataError(procId, exception);
        
        if (dto is null)
        {
            state.SetHandled(Unit.Value);
        }
        
        return Task.CompletedTask;
    }
}