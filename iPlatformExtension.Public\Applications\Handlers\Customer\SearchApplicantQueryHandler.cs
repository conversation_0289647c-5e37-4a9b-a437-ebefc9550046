﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Customer;
using iPlatformExtension.Public.Applications.Queries.Customer;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Customer
{
    /// <summary>
    /// 搜索申请人处理者
    /// </summary>
    internal sealed class SearchApplicantQueryHandler : IRequestHandler<SearchApplicantQuery, IEnumerable<SearchApplicantDto>>
    {
        private readonly IFreeSql _freeSql;

        public SearchApplicantQueryHandler(IFreeSql freeSql)
        {
            _freeSql = freeSql;
        }


        public async Task<IEnumerable<SearchApplicantDto>> Handle(SearchApplicantQuery request, CancellationToken cancellationToken)
        {
            if (!await _freeSql.Select<CusCustomer>().AnyAsync(x => x.CrmCustomerId == request.CrmCustomerId, cancellationToken))
            {
                return new List<SearchApplicantDto>();
            }
            var searchApplicantDtos = await _freeSql.Select<CusCustomer, CusJoinList, CusApplicant, BasCountry, BasApplicantType, SysDictionary>()
                .LeftJoin((cc, cj, ca, bc, bat, dic) => cc.CustomerId == cj.JoinObjId)
                .LeftJoin((cc, cj, ca, bc, bat, dic) => ca.ApplicantId == cj.FormObjId && cj.FormType == "applicant")
                .LeftJoin((cc, cj, ca, bc, bat, dic) => bc.CountryId == ca.CountryId)
                .LeftJoin((cc, cj, ca, bc, bat, dic) => ca.TypeId == bat.ApplicantTypeId)
                .LeftJoin((cc, cj, ca, bc, bat, dic) => dic.Value == ca.CardType && dic.DictionaryName == "card_type")
                .Where((cc, cj, ca, bc, bat, dic) => ca.ApplicantId != null && cc.CrmCustomerId == request.CrmCustomerId && ca.IsEnabled == true)
                .ToListAsync((cc, cj, ca, bc, bat, dic) => new SearchApplicantDto(cc.CustomerId, ca.ApplicantId, ca.ApplicantNameCn, ca.ApplicantNameEn,ca.TypeId,
                            bat.ApplicantTypeZhCn, bc.CountryZhCn, bc.CountryEnUs,ca.CardType, dic.TextZhCn, ca.CardNo), cancellationToken);
            var applicantIdList = searchApplicantDtos.Select(it => it.ApplicantId).ToList();
            var addressList = await _freeSql.Select<CusJoinList, CusAddressList>()
                .LeftJoin((cj, ad) => cj.FormObjId == ad.AddressId)
                .WhereIf(searchApplicantDtos.Count > 0, (cj, ad) => cj.FormType == "address" && applicantIdList.Contains(cj.JoinObjId) && ad.IsEnabled)
                .ToListAsync(it => new { ApplicantId = it.t1.JoinObjId, it.t2.AddressCn, it.t2.AddressEn, it.t2.AddressType }, cancellationToken);
            searchApplicantDtos.ForEach((it) =>
            {
                it.Addresses = addressList.Where(x => x.ApplicantId == it.ApplicantId)
                    .Select(x => new Address(x.AddressCn, x.AddressEn)).ToList();
            });
            return searchApplicantDtos;
        }
    }
}

