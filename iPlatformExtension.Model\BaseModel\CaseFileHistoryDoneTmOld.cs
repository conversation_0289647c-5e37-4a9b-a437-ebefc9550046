using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_file_history_done_tm_old", DisableSyncStructure = true)]
	public partial class CaseFileHistoryDoneTmOld {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "case_id", StringLength = 50)]
		public string CaseId { get; set; }

		[ Column(Name = "file_id", StringLength = 50, IsNullable = false)]
		public string FileId { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "volume_num", StringLength = 50)]
		public string VolumeNum { get; set; }

	}

}
