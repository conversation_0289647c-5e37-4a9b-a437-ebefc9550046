﻿using System.Text;
using FreeSql;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkDelivery.Applications.Commands.Delivery;
using MediatR;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.TrademarkDelivery.Applications.Handlers.Delivery;

public interface IDeliveryHandler<in TCommand, TRequest, TResponse> 
    : IRequestHandler<TCommand, DeliInfo?> 
    where TCommand : IDeliveryCommand
{
    
    string DefaultUserId => IDeliveryCommand.AdminUserId;
    
    ObjectPool<StringBuilder> StringBuilderPool { get; }
    
    IDeliveryInfoRepository DeliveryInfoRepository { get; }

    IDeliveryHistoryRepository DeliveryHistoryRepository { get; }

    UnitOfWorkManager UnitOfWorkManager { get; }

    TrademarkDeliveryOperation DeliveryOperation { get; }

    protected DeliInfo? DeliveryInfo { get; set; }

    ILogger Logger { get; }

    IDeliveryHandler<TCommand, TRequest, TResponse> Handler { get;}

    Task<TRequest> CreateParameterAsync(CancellationToken cancellationToken = default);

    Task<TResponse?> HandleRemoteDeliveryAsync(TRequest request, CancellationToken cancellationToken = default);

    Task HandleDeliveryInfoAsync(TResponse response, CancellationToken cancellationToken = default);

    Task<bool> HandleExceptionAsync(Exception ex, CancellationToken cancellationToken = default) =>
        HandleExceptionInternalAsync(ex, true, false, cancellationToken);

    Task InitializeAsync(TCommand command, CancellationToken cancellationToken = default) =>
        InitializeInternalAsync(command, cancellationToken);

    async Task InitializeInternalAsync(TCommand command, CancellationToken cancellationToken = default)
    {
        var procId = command.ProcId;
        ArgumentNullException.ThrowIfNull(procId);
        DeliveryInfo = await DeliveryInfoRepository.Where(info => info.ProcId == procId)
            .IncludeMany(info => info.NiceCategories).ToOneAsync(cancellationToken);
        if (DeliveryInfo is null)
        {
            throw new NotFoundException(procId, "递交任务");
        }
    }

    public async Task UpdateDeliveryInfoInternalAsync(Func<DeliInfo, ValueTask> updateAsync, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(DeliveryInfo);
        
        var unitOfWork = UnitOfWorkManager.Begin();
        var deliveryInfo = await DeliveryInfoRepository.GetAsync(DeliveryInfo.ProcId, cancellationToken);
        
        await updateAsync(deliveryInfo);
        deliveryInfo.UpdateTime = DateTime.Now;
        deliveryInfo.UpdateUserId = IDeliveryCommand.AdminUserId;

        await DeliveryInfoRepository.UpdateAsync(deliveryInfo, cancellationToken);
        unitOfWork.Commit();

        DeliveryInfo = deliveryInfo;
    }

    /// <summary>
    /// 第三方调用失败后的后续处理
    /// </summary>
    /// <param name="ex">异常信息</param>
    /// <param name="displayHistory">记录的历史是否显示</param>
    /// <param name="operationResult">操作标记是否为成功</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否已经处理异常</returns>
    async Task<bool> HandleExceptionInternalAsync(Exception ex, bool displayHistory = true, bool operationResult = false, CancellationToken cancellationToken = default)
    {
        var errorBuilder = StringBuilderPool.Get();
        var errorMessage = ex is PhoenixRequestException requestException
            ? errorBuilder.BuildExceptionMessage(requestException)
            : errorBuilder.BuildExceptionMessage(ex);
        StringBuilderPool.Return(errorBuilder);

        if (DeliveryInfo is null)
        {
            Logger.LogError(ex, "递交信息为空");
        }

        ArgumentNullException.ThrowIfNull(DeliveryInfo);

        // const int deliveryStatus = (int) DeliveryStatus.Error;

        Logger.LogError(ex, "递交操作失败！任务Id{ProcId}。任务递交状态{Status}。命令类型{Name}", DeliveryInfo.ProcId, DeliveryInfo.Status,
            typeof(TCommand).Name);

        using var unitOfWork = UnitOfWorkManager.Begin();

        var deliveryInfo = await DeliveryInfoRepository.GetAsync(DeliveryInfo.ProcId, cancellationToken);
        deliveryInfo.UpdateTime = DateTime.Now;
        deliveryInfo.UpdateUserId = IDeliveryCommand.AdminUserId;
        deliveryInfo.OperationResult = operationResult;
        await DeliveryInfoRepository.UpdateAsync(deliveryInfo, cancellationToken);
        DeliveryInfo = deliveryInfo;

        await DeliveryHistoryRepository.InsertAsync(new DeliHistory
        {
            StartTime = DateTime.Now,
            FinishTime = DateTime.Now,
            ErrorMessage = errorMessage,
            UserId = IDeliveryCommand.AdminUserId,
            ProcId = DeliveryInfo.ProcId,
            Operation = $"{DeliveryOperation.GetDescription()}失败",
            Display = displayHistory
        }, cancellationToken);

        unitOfWork.Commit();

        return true;
    }

    async Task<DeliInfo?> IRequestHandler<TCommand, DeliInfo?>.Handle(TCommand command, CancellationToken cancellationToken)
    {
        try
        {
            await InitializeAsync(command, cancellationToken);
            var request = await CreateParameterAsync(cancellationToken);
            var response = await HandleRemoteDeliveryAsync(request, cancellationToken);
            if (response is null)
                throw new InvalidDataException("can not get response from remote delivery host!");
            await HandleDeliveryInfoAsync(response, cancellationToken);
        }
        catch (Exception e)
        {
            if (!await HandleExceptionAsync(e, cancellationToken))
            {
                throw;
            }
        }

        return DeliveryInfo;
    }
}
