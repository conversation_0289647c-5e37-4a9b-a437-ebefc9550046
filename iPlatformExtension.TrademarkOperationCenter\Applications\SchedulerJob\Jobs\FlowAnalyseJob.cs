﻿
using Hangfire;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.SchedulerJob.Jobs
{
    /// <summary>
    /// 
    /// </summary>
    public sealed class FlowAnalyseJob //: IScopeDependency
    {
        private readonly IMediator _mediator;

        private readonly ILogger<FlowAnalyseJob> _logger;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="mediator"></param>
        /// <param name="logger"></param>
        public FlowAnalyseJob(IMediator mediator, ILogger<FlowAnalyseJob> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// 流程计算并推送
        /// </summary>
        [DisableConcurrentExecution(10)]
        [Queue("flow")]
        public async Task ExecuteAsync()
        {
            try
            {

                // _logger.LogInformation("开始流程计算服务，{Now}...", DateTime.Now);
              // await _mediator.Publish(new FlowAnalyseCommand());

                await Task.WhenAll(_mediator.Publish(new FlowPrivateCountCommand())
                    , _mediator.Publish(new FlowAnalyseCommand())
                    );

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "流程计算出现异常");
            }
            finally
            {
                //_logger.LogInformation("结束处理流程计算服务...");
                Console.WriteLine("结束处理流程计算服务..."+DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }
        }
    }
}
