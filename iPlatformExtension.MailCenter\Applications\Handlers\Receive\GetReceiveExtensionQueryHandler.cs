﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 获取收件办理信息处理者
    /// </summary>
    internal sealed class GetReceiveExtensionQueryHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IUserInfoRepository userInfoRepository
    ) : IRequestHandler<GetReceiveExtensionQuery, GetReceiveExtensionDto>
    {
        public async Task<GetReceiveExtensionDto> Handle(
            GetReceiveExtensionQuery request,
            CancellationToken cancellationToken
        )
        {
            var getReceiveExtensionDtos = await freeSql
                .Select<MailReceive, MailReceiveFlow>()
                .WithLock()
                .LeftJoin(it => it.t1.MailId == it.t2.MailId)
                .Where(it => it.t1.MailId == request.MailId)
                .FirstAsync(
                    it => new GetReceiveExtensionDto(
                        it.t1.MailId,
                        it.t2.IgnoreBy,
                        it.t2.IgnoreTime,
                        it.t1.MailNo,
                        it.t1.Status,
                        it.t1.MailTo,
                        it.t1.MailDate,
                        it.t2.SortBy,
                        it.t2.SortTime,
                        it.t2.UndertakeUserId,
                        it.t2.FinishDate,
                        it.t1.HostId,
                        it.t1.CreateTime,
                        it.t2.SendName
                    ),
                    cancellationToken
                );
            if (getReceiveExtensionDtos.IgnoreByTemp is not null)
            {
                ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                    userInfoRepository;
                getReceiveExtensionDtos.IgnoreByUser = new
                {
                    CnName = (
                        await userBaseInfoRepository.GetCacheValueAsync(
                            getReceiveExtensionDtos.IgnoreByTemp, cancellationToken: cancellationToken)
                    )?.CnName ?? "",
                    UserId = getReceiveExtensionDtos.IgnoreByTemp,
                };
            }

            if (getReceiveExtensionDtos.SortByTemp is not null)
            {
                ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                    userInfoRepository;
                getReceiveExtensionDtos.SortBy = new
                {
                    CnName = (
                        await userBaseInfoRepository.GetCacheValueAsync(
                            getReceiveExtensionDtos.SortByTemp, cancellationToken: cancellationToken)
                    )?.CnName ?? "",
                    UserId = getReceiveExtensionDtos.SortByTemp,
                };
            }

            if (getReceiveExtensionDtos.UndertakeUserTemp is not null)
            {
                ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                    userInfoRepository;
                getReceiveExtensionDtos.UndertakeUser = new
                {
                    CnName = (
                        await userBaseInfoRepository.GetCacheValueAsync(
                            getReceiveExtensionDtos.UndertakeUserTemp, cancellationToken: cancellationToken)
                    )?.CnName ?? "",
                    UserId = getReceiveExtensionDtos.UndertakeUserTemp,
                };
            }

            return getReceiveExtensionDtos;
        }
    }
}
