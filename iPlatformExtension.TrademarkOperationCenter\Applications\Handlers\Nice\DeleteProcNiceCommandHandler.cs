﻿using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Nice;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Nice;

internal sealed class DeleteProcNiceCommandHandler(IProcNiceCategoryRepository procNiceCategoryRepository) : 
    IRequestHandler<DeleteProcNiceCommand>
{
    public Task Handle(DeleteProcNiceCommand request, CancellationToken cancellationToken)
    {
        return procNiceCategoryRepository.DeleteAsync(category => category.ProcId == request.ProcId, cancellationToken);
    }
}