﻿using AutoMapper;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.Mappers;

/// <summary>
/// 分页查询映射
/// </summary>
public sealed class PageResultMapper : Profile
{
    /// <summary>
    /// 构造函数
    /// 配置映射配置
    /// </summary>
    public PageResultMapper()
    {
        CreateMap<FileListDto, ProcFileListDto>();

        CreateMap<PageResult<FileListDto>, PageResult<ProcFileListDto>>()
            .ForMember(destination => destination.Data,
                expression => expression.MapFrom(source => source.Data))
            .ForMember(destination => destination.TotalPage, expression => expression.Ignore());
    }
}