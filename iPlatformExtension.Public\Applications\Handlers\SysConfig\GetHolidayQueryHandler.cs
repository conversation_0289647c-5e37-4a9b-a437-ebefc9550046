﻿using AutoMapper;
using iPlatformExtension.Common.Clients.Holiday;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.SysConfig;
using iPlatformExtension.Public.Applications.Queries.SysConfig;
using iPlatformExtension.Repository.Implement;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.SysConfig
{
    /// <summary>
    /// 获取节假日
    /// </summary>
    internal sealed class GetHolidayQueryHandler(IFreeSql freeSql, HolidayClient client, IMapper mapper, ISysHolidayRepository sysHolidayRepository)
        : IRequestHandler<GetHolidayQuery>
    {
        public async Task Handle(GetHolidayQuery request, CancellationToken cancellationToken)
        {
            var holiday = await client.GetHoliday(request.year);
            if (holiday?.code == 0)
            {
                var sysHolidays = mapper.Map<List<SysHoliday>>(holiday.holiday.Values);
                var year = DateTime.Parse(request.year + "/01/01");
                await sysHolidayRepository.DeleteAsync(it => it.Date < year.AddYears(1) && it.Date >= year, cancellationToken);

                await sysHolidayRepository.InsertAsync(sysHolidays, cancellationToken);
            }
            else
            {
                throw new HttpRequestException("GetHoliday接口 获取失败");
            }
        }
    }
}

