﻿// See https://aka.ms/new-console-template for more information

using System.Diagnostics;
using System.Globalization;
using System.Text.Json;
using BillInfoTransferTool.Models;
using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Clients.Finance;
using iPlatformExtension.Common.Clients.HttpMessageHandlers;
using iPlatformExtension.Common.Clients.Options;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.Service.Interface;
using Microsoft.AspNetCore.Http.Json;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Hosting.Internal;
using Microsoft.Extensions.Logging;
using NLog.Extensions.Logging;

const string connectionString = "data source=**********,8433;initial catalog=acip_iplatform;user id=AcipBussiness;password=*************$;packet size=4096;TrustServerCertificate=True;";
const int stepSize = 1;
const string CNY = nameof(CNY);
const string productionToken =
    "bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXB0X2JlbG9uZ19pZCI6IjE2MiIsInRlbmFudF9pZCI6IjAwMDAwMCIsImJlbG9uZ190b19kaXN0cmljdCI6IkdaIiwiYmVsb25nX3RvX2NvbXBhbnkiOiI5MTQ0MDEwNjcxNjM2ODI2NUoiLCJ1c2VyX25hbWUiOiJIMDQyNDYiLCJtYW5hZ2VfZGlzdHJpY3QiOiJHWiIsInJlYWxfbmFtZSI6IuiwreaDoOaDoCIsImF2YXRhciI6IiIsImF1dGhvcml0aWVzIjpbIui0puWKoeS4reW_g-WfuuehgCIsIui0puWKoeS4reW_g-mDqOmXqOeuoeeQhuWRmCIsIuWVhuWKoeS6uuWRmCIsIui0puWKoeS4reW_g-i0ouWKoeeJueauiuadg-mZkCIsIui0puWKoeS4reW_g-WVhuWKoSIsIua1geeoi-S6uuWRmCJdLCJjbGllbnRfaWQiOiJhY2lwY2MiLCJyb2xlX25hbWUiOiLmtYHnqIvkurrlkZgs5ZWG5Yqh5Lq65ZGYLOi0puWKoeS4reW_g-WfuuehgCzotKbliqHkuK3lv4Ppg6jpl6jnrqHnkIblkZgs6LSm5Yqh5Lit5b-D6LSi5Yqh54m55q6K5p2D6ZmQLOi0puWKoeS4reW_g-WVhuWKoSIsInJvbGV0eXBlcyI6WyJmbG93VXNlciIsImRlcGFydG1lbnQiLCJmaW5hbmNlIiwiYnVzaW5lc3MiXSwicG9zdF9pZCI6IjEzNjciLCJ1c2VyX2lkIjoiMjM5MyIsInJvbGVfaWQiOiIxNTE2OTcxNTU2NzY3NjQ1Njk4LDE0ODMzNjc3MDk3ODQ3OTcxODUsMTYwOTE2NjI0NDc5NDA3NzE4NSwxNjA5MTY4MzQwODUxMzYzODQyLDE2MDkxNjg0NTEwMTg5NTI3MDUsMTYwOTE3MjA2NTA3ODc0MzA0MSIsInNjb3BlIjpbImFsbCJdLCJuaWNrX25hbWUiOiLosK3mg6Dmg6AiLCJtYW5hZ2VfY29tcGFueSI6IjMxMTEwMDAwMzM1NTcwMjgzVSw5MTExMDEwODc1OTYyMzMzNFgsOTE0NDAzMDA3NDg4NzU0NzJULDkxNjEwMTMxTUE2VTdQNkM4Syw5MTMxMDAwMDA3ODE3ODMyNzcs5pegLDkxNDIwNTAwTUE0RjVYM0QwUiw5MTQ0MDYwNjU3OTY1NDIzWFksMzE0NDAwMDA2NjE4MDIwNlhZLDkxNDQwMTAxTUE1OUVGM1Q4OSzml6AsOTE0NDA0MDA1Nzc5MjgxMTBELDkxNDQwMzAwMzU5NzYzMzcwMSwzMTQ0MDAwME1EMDEwMzk5OFUsOTE0NDAxMDY3MTYzNjgyNjVKLDkxMTEwMTA4NjYyMTUyOTE5VSw5MTQ0MTkwMDc0NzA2NzEzMVIsNzE2MzY4MjYtNSw5MTMyMDU5NDU3NTM2ODg2OUwsOTEzMzAxMDhNQTI4UkFMSDVELDkxMzMwMTA2MzI4MjU5ODVYMSw5MTQ0MTkwME1BNFcyRFk0OU0s5pegLDkxMzQwMTAwTUE4TExDV1U3UCw5MTQ0MDEwMU1BNTlQMVc4MkosOTEzMjAyMTNNQTIxNFRSNDdSLDkxNDQwMzAwMzYwMDEzMDY3Niw5MTMyMDEwNU1BQkxYNkgyN0MsOTEyMTAxMDZNQUM0RzNLMTNHLDMxMzMwMDAwTUQwMjkxMDYwVSw5MTMxMDExNU1BMUszV0dZMEUsOTE2MTAxMzFNQTZVUjQxRjJSLDkxNDMwMTAwNTk1NDg4OTNYQyw3MTYzNjgyNi01LDkxNDIwMTAwTUE0OUpHTEgyNyw5MTMyMDU5NDA2NzY4NTYzMEQsOTE0NDAzMDA2OTExNjI4MzNMLEM0MDY5OTc3LDkxMTIwMTkzTUE4MVg4SjgwRSw5MTMyMDI4Mk1BMjIxRUsyWEMsOTEzMzAyMDFNQUJVQkREQzAzLDkxNDQxMzAyNTg0NzQxODNYQyIsIm9hdXRoX2lkIjoiIiwiZGV0YWlsIjp7InR5cGUiOiJ3ZWIiLCJkZXB0QmVsb25nSWQiOiIxNjIiLCJjb2RlIjoiSDA0MjQ2In0sImV4cCI6MTY3MjgwNDA4MSwiZGVwdF9pZCI6IjEwMDAwLDExMjAwMDAsMTE0MDAwMCwxMzAwMDAsMTM4MDAwMCwxMzkwMDAwLDE0MDAwMCwyNjAwMDAsMzAwMDAsNDAwMDAsNTAwMDAsNTkwMDAwLDYwMDAwLDcyMDAwMCw3NjAwMDAsOTAwMDAsOTEwMDAwIiwianRpIjoiZTRhOGY1ODItZmQwMi00MzdiLTk4OGQtMWQ1MzM5ZjU2NTA2IiwiYWNjb3VudCI6IkgwNDI0NiJ9.lSC1baUeMK0FwPesz_ZJIfUesN1PO1FarDlc8gi3LaU";

var testBillNos = new string[]
{
     "DGP-22-33848"
};

var transferCount = 0;

var services = new ServiceCollection();
services.AddSingleton<IHostEnvironment>(new HostingEnvironment()
{
    EnvironmentName = "Development",
    ApplicationName = Process.GetCurrentProcess().ProcessName,
    ContentRootPath = Environment.CurrentDirectory,
    ContentRootFileProvider = new PhysicalFileProvider(Environment.CurrentDirectory)
});
services.AddLogging(builder =>
{
    builder.AddFilter(nameof(IFreeSql), LogLevel.Debug);
    builder.AddConsole();
    builder.AddNLog(options:new NLogProviderOptions()
    {
        RemoveLoggerFactoryFilter = true
    });
});
services.AddObjectPools();

services.AddPlatformFreeSql(connectionString);
services.AddDataService();
services.AddCache().ConfigureRedis<StackExchangeRedisCacheOptions, DefaultRedisCache>(options =>
{
    options.ConnectionString = "**************:6379,password=Acip_cc@54,defaultDatabase=3";
    options.Converters.Add(new ClaimPrincipalRedisValueConverter());
});
var provider = services.BuildServiceProvider();

var freeSql = provider.GetRequiredService<IFreeSql>();
var logger = provider.GetRequiredService<ILogger<Program>>();
var userInfoRepository = provider.GetRequiredService<ICacheableRepository<string, UserInfoDto>>();

var applyList = await freeSql.Select<AppApplyInfo>().WithLock().Where(info => info.CreateUser.StartsWith("H0"))
    .ToListAsync(info => new AppApplyInfo()
    {
        ApplyId = info.ApplyId,
        CreateUser = info.CreateUser
    });

foreach (var appApplyInfo in applyList)
{
    var userInfo = await userInfoRepository.GetCacheValueAsync(appApplyInfo.CreateUser);
    if (!string.IsNullOrWhiteSpace(userInfo?.UserId))
    {
        await freeSql.Update<AppApplyInfo>(appApplyInfo.ApplyId).Set(info => info.CreateUser, userInfo.UserId)
            .ExecuteAffrowsAsync();
    }
    
}
    