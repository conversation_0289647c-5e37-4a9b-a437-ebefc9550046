﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Extensions;

namespace iPlatformExtension.Repository.Extensions;

public static class SystemDictionaryRepositoryExtension
{
    /// <summary>
    /// 获取字典名称和值获取对应字典的中文名称
    /// </summary>
    /// <param name="dictionaryRepository">字典仓储</param>
    /// <param name="name">字典名</param>
    /// <param name="value">字典值</param>
    /// <returns>字典值和对应的中文名称</returns>
    public static async ValueTask<KeyValuePair<string, string>> GetChineseKeyValueAsync(this ICacheableRepository<SystemDictionaryValueKey, SysDictionary> dictionaryRepository, string? name, string? value)
    {
        ArgumentNullException.ThrowIfNull(dictionaryRepository);
        if (string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(value))
        {
            return KeyValuePair.Create(string.Empty, string.Empty);
        }
        var dictionary = await dictionaryRepository.GetCacheValueAsync(new SystemDictionaryValueKey(name, value));
        return dictionary?.ToChineseKeyValue() ?? KeyValuePair.Create(value, string.Empty);
    }
    
    /// <summary>
    /// 获取对应字典的中文名称
    /// </summary>
    /// <param name="dictionaryRepository">字典仓储</param>
    /// <param name="name">字典名</param>
    /// <param name="value">字典值</param>
    /// <returns>字典值对应的中文名称</returns>
    public static async ValueTask<string> GetChineseValueAsync(this ICacheableRepository<SystemDictionaryValueKey, SysDictionary> dictionaryRepository, string? name, string? value)
    {
        ArgumentNullException.ThrowIfNull(dictionaryRepository);
        if (string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(value))
        {
            return string.Empty;
        }
        var dictionary = await dictionaryRepository.GetCacheValueAsync(new SystemDictionaryValueKey(name, value));
        return dictionary?.TextZhCn ?? string.Empty;
    }
    
    /// <summary>
    /// 获取字典名称和值获取对应字典的中文名称
    /// </summary>
    /// <remarks>键值对可以为空。则返回默认的键值对</remarks>
    /// <param name="dictionaryRepository">字典仓储</param>
    /// <param name="keyValuePair">字典名和字典值对应的键值对</param>
    /// <returns>字典值和对应的中文名称</returns>
    public static async ValueTask<KeyValuePair<string, string>> GetChineseKeyValueAsync(this ICacheableRepository<SystemDictionaryValueKey, SysDictionary> dictionaryRepository, KeyValuePair<string, string>? keyValuePair)
    {
        ArgumentNullException.ThrowIfNull(dictionaryRepository);
        if (!keyValuePair.HasValue)
        {
            return new KeyValuePair<string, string>();
        }
        
        return await dictionaryRepository.GetChineseKeyValueAsync(keyValuePair.Value.Key, keyValuePair.Value.Value);
    }
    
    /// <summary>
    /// 获取字典名称和值获取对应字典的中文名称
    /// </summary>
    /// <param name="dictionaryRepository">字典仓储</param>
    /// <param name="keyValuePair">字典名和字典值对应的键值对</param>
    /// <returns>字典值和对应的中文名称</returns>
    public static async ValueTask<KeyValuePair<string, string>> GetChineseKeyValueAsync(this ICacheableRepository<SystemDictionaryValueKey, SysDictionary> dictionaryRepository, KeyValuePair<string, string> keyValuePair)
    {
        ArgumentNullException.ThrowIfNull(dictionaryRepository);
        return await dictionaryRepository.GetChineseKeyValueAsync(keyValuePair.Key, keyValuePair.Value);
    }
}