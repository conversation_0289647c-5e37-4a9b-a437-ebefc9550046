﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class DeleteDeliveryBatchItemCommandHandler(IMediator mediator, IFreeSql freeSql)
    : IRequestHandler<DeleteDeliveryBatchItemCommand, DeliveryItemOperationResult>
{
    public async Task<DeliveryItemOperationResult> Handle(DeleteDeliveryBatchItemCommand request, CancellationToken cancellationToken)
    {
        var procId = request.ProcId;
        
        if (await freeSql.Select<SysFlowActivity>().AnyAsync(activity => 
                activity.ObjId == procId 
                && activity.FlowType == FlowType.Delivery 
                && activity.FlowSubType == "TII", cancellationToken))
        {
            return new DeliveryItemOperationResult(procId).Fail("流程已经启动!请关闭窗口刷新页面。");
        }
        
        await mediator.Send(new DeleteDeliveryCommand(procId, false, request.Version), cancellationToken);

        return new DeliveryItemOperationResult(procId);
    }
}