﻿using System.Text.Json;
using System.Text.Json.Serialization.Metadata;
using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.Common.Modifiers;

public sealed class PropertySerializationSourceModifier : IJsonTypeModifier
{

    public void ModifyTypeInfo(JsonTypeInfo jsonTypeInfo)
    {
        if (jsonTypeInfo.Kind is not JsonTypeInfoKind.Object)
        {
            return;
        }

        foreach (var jsonPropertyInfo in jsonTypeInfo.Properties)
        {
            var serializationSourceAttribute = jsonPropertyInfo.AttributeProvider
                ?.GetCustomAttributes(typeof(JsonSerializationSourceAttribute), true)
                .Cast<JsonSerializationSourceAttribute>().FirstOrDefault();
            
            if (serializationSourceAttribute is null) continue;
            
            var sourceProperty =
                jsonTypeInfo.Properties.FirstOrDefault(info => info.Name == info.Options.PropertyNamingPolicy?.ConvertName(serializationSourceAttribute.SourceName));
            if (sourceProperty?.Get is null) continue;

            jsonPropertyInfo.Get = o =>
            {
                var sourceValue = sourceProperty.Get(o);
                return sourceValue is null ? null : JsonSerializer.Serialize(sourceValue, sourceProperty.Options);
            };
            if (serializationSourceAttribute.IgnoreSource)
            {
                sourceProperty.ShouldSerialize = (_, _) => false;
            }
        }
    }
}