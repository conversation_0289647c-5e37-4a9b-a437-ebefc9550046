﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models.NiceCategory;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Nice;

/// <summary>
/// 案件尼斯分类商品项查询
/// </summary>
/// <param name="VersionId">版本号</param>
/// <param name="ParentCategoryNumbers">上级编码</param>
/// <param name="CaseId">案件id</param>
public record CaseNiceCategoryQuery(string VersionId, IEnumerable<string> ParentCategoryNumbers, string? CaseId = null) 
    : IRequest<IEnumerable<NiceCategoryDto>>;