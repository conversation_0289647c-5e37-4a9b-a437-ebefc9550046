using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_notice_convey_common", DisableSyncStructure = true)]
	public partial class BasNoticeConveyCommon {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "convey_time", StringLength = 50)]
		public string ConveyTime { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "effective_begin")]
		public DateTime? EffectiveBegin { get; set; }

		[ Column(Name = "effective_end")]
		public DateTime? EffectiveEnd { get; set; }

		[ Column(Name = "file_name_type", StringLength = 50)]
		public string FileNameType { get; set; }

		[ Column(Name = "frequency_type", StringLength = 50)]
		public string FrequencyType { get; set; }

		[ Column(Name = "frequency_value")]
		public int? FrequencyValue { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "mail_cc", StringLength = 50)]
		public string MailCc { get; set; }

		[ Column(Name = "mail_subject", StringLength = 50)]
		public string MailSubject { get; set; }

		[ Column(Name = "mail_to", StringLength = 50)]
		public string MailTo { get; set; }

		[ Column(Name = "notice_code", StringLength = 50)]
		public string NoticeCode { get; set; }

		[ Column(Name = "notice_name", StringLength = 50)]
		public string NoticeName { get; set; }

		[ Column(Name = "payment_type", StringLength = 50)]
		public string PaymentType { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
