﻿using System.Text;
using iPlatformExtension.Commission.Application.Notifications.Trademark.Foreign;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using MediatR;
using Microsoft.Extensions.ObjectPool;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Foreign;

internal sealed class ForeignCommissionFeesResultHandler(
    IFreeSql freeSql, 
    ICurrencyRepository currencyRepository,
    ObjectPool<StringBuilder> stringBuilderPool) : INotificationHandler<ForeignCommissionResultNotification>
{
    public async Task Handle(ForeignCommissionResultNotification notification, CancellationToken cancellationToken)
    {
        var commission = notification.ForeignTrademarkBonus;
        var procId = commission.ProcId;
        
        var agentFees = await freeSql.Select<CaseFeeList>()
            .Where(f => f.ProcId == procId)
            .Where(list => list.IsEnabled == true)
            .Where(list => list.FeeClass == FeeClass.Agent)
            .ToListAsync(list => new CaseFeeList()
            {
                FeeId = list.FeeId,
                ProcId = list.ProcId,
                CurrencyId = list.CurrencyId,
                Amount = list.Amount
            }, cancellationToken);

        var builder = await agentFees.ToAsyncEnumerable().AggregateAwaitAsync(stringBuilderPool.Get(), async (agentFeesBuilder, list) =>
        {
            var currency = await currencyRepository.GetTextValueAsync(list.CurrencyId) ?? string.Empty;
            agentFeesBuilder.Append(currency).Append((list.Amount ?? 0M).ToString("F2")).Append(';');
            return agentFeesBuilder;
        }, cancellationToken);

        if (builder.Length > 0 && builder[^1] is ';')
        {
            builder.Remove(builder.Length - 1, 1);
        }

        commission.AgencyFee = builder.ToString();
        
        stringBuilderPool.Return(builder);
    }
}