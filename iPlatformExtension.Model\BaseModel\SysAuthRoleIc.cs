using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_auth_role_ic", DisableSyncStructure = true)]
	public partial class SysAuthRoleIc {

		[ Column(Name = "auth_group_id", StringLength = 50, IsNullable = false)]
		public string AuthGroupId { get; set; }

		[ Column(Name = "auth_ic_id", StringLength = 50, IsNullable = false)]
		public string AuthIcId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "auth_scope", StringLength = 50, IsNullable = false)]
		public string AuthScope { get; set; }

		[ Column(Name = "auth_type_id", StringLength = 50, IsNullable = false)]
		public string AuthTypeId { get; set; }

		[ Column(Name = "auth_user_id", StringLength = 50, IsNullable = false)]
		public string AuthUserId { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
