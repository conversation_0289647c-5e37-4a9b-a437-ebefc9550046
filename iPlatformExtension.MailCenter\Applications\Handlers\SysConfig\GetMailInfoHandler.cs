﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Commands.SysConfig;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    public class GetMailInfoHandler(IMailHostRepository mailHostRepository, IFreeSql<PlatformFreeSql> msSql) : IRequestHandler<GetMailInfoQuery, GetMailHostListDto>
    {
        public async Task<GetMailHostListDto> Handle(GetMailInfoQuery request, CancellationToken cancellationToken)
        {
            var mailInfo = await mailHostRepository.Where(o => o.HostId == request.HostId).ToOneAsync<GetMailHostListDto>();

            var users = await msSql.Select<SysUserInfo>()
                .Where(o => o.UserId == mailInfo.CreateBy || o.UserId == mailInfo.UpdateBy)
                .ToListAsync(o => new { UserId = o.UserId, CnName = o.CnName });
            mailInfo.CreateBy = users.FirstOrDefault(o => o.UserId == mailInfo.CreateBy)?.CnName;
            mailInfo.UpdateBy = users.FirstOrDefault(o => o.UserId == mailInfo.UpdateBy)?.CnName;
            return mailInfo;
        }
    }
}
