using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_dept_role", DisableSyncStructure = true)]
	public partial class SysDeptRole {

		[ Column(Name = "authority", StringLength = 50)]
		public string Authority { get; set; }

		[ Column(Name = "is_default")]
		public bool IsDefault { get; set; } = false;

		[ Column(Name = "role_code", StringLength = 50)]
		public string RoleCode { get; set; }

		[ Column(Name = "role_id", StringLength = 50, IsNullable = false)]
		public string RoleId { get; set; }

		[ Column(Name = "role_name", StringLength = 50)]
		public string RoleName { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
