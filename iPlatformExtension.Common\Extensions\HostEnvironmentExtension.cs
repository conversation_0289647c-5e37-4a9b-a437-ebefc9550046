﻿using System.Collections;
using System.Text;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.Extensions;

public static class HostEnvironmentExtension
{
    /// <summary>
    /// 判断环境变量是否是本地
    /// </summary>
    /// <param name="hostEnvironment">主机环境变量</param>
    /// <returns>是否是本地环境</returns>
    public static bool IsLocal(this IHostEnvironment hostEnvironment)
    {
        ArgumentNullException.ThrowIfNull(hostEnvironment);
        return hostEnvironment.IsEnvironment("Local");
    }

    public static void LogEnvironmentVariables(this ILogger logger)
    {
        var msgBuilder = new StringBuilder();
        foreach (DictionaryEntry environmentVariable in Environment.GetEnvironmentVariables())
        {
            msgBuilder.AppendLine($"{environmentVariable.Key}={environmentVariable.Value}");
        }
        
        logger.LogInformation("环境变量：{}",msgBuilder.ToString());
    }
}