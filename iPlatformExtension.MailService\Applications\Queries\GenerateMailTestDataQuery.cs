﻿using MediatR;

namespace iPlatformExtension.MailService.Applications.Queries
{
    public class GenerateMailTestDataQuery : IRequest<bool>
    {
        /// <summary>
        /// 复制对象
        /// </summary>
        public string MailId { get; set; } = "3c051789-30d0-4140-87c2-d90d24c7026c";

        /// <summary>
        /// 新的名称,后台自动在加序号
        /// </summary>
        public string MailSubject { get; set; } = $"邮件发送测试{DateTime.Now.Date}";

        /// <summary>
        /// 复制数量
        /// </summary>
        public int Count { get; set; } = 1;
    }
}
