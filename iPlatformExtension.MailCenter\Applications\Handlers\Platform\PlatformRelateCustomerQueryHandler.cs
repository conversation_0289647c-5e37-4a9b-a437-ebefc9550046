﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Platform;
using iPlatformExtension.MailCenter.Applications.Queries.Platform;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Platform;

/// <summary>
///
/// </summary>
internal sealed class PlatformRelateCustomerQueryHandler(
    IFreeSql<MailCenterFreeSql> freeSql,
    IUserInfoRepository userInfoRepository
) : IRequestHandler<PlatformRelateCustomerQuery, IEnumerable<PlatformRelateCustomerDto>>
{
    public async Task<IEnumerable<PlatformRelateCustomerDto>> Handle(
        PlatformRelateCustomerQuery request,
        CancellationToken cancellationToken
    )
    {
        var mailCorrelatives = await freeSql
            .Select<MailCorrelative, MailReceive, MailSend, MailReceiveFlow>()
            .WithLock()
            .LeftJoin((it) => it.t1.MailId == it.t2.MailId)
            .LeftJoin((it) => it.t1.MailId == it.t3.MailId)
            .LeftJoin((it) => it.t2.MailId == it.t4.MailId)
            .Where(it =>
                it.t1.CorrelateType == SysEnum.CorrelateType.Customer.ToString()
                && it.t1.ObjId == request.CustomerId
            )
            .OrderByDescending(it => it.t1.CreateTime)
            .ToListAsync(
                it => new PlatformRelateCustomerDto(
                    it.t1.MailId,
                    it.t2.MailNo ?? it.t3.MailTo,
                    it.t2.MailSubject ?? it.t3.MailSubject,
                    it.t2.MailFrom ?? it.t3.MailFrom,
                    it.t2.MailDate ?? it.t3.MailDate,
                    it.t2.Status ?? 0,
                    it.t1.CreateBy ?? it.t3.MailFrom,
                    it.t1.CreateTime,
                    it.t1.ObjId,
                    it.t2.MailId == null ? "发件" : "收件",
                    it.t4.SendName
                ),
                cancellationToken
            );
        return await mailCorrelatives
            .ToAsyncEnumerable()
            .SelectAwait(async it =>
            {
                if (it.CreateByTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                        userInfoRepository;
                    it.CreateBy = new
                    {
                        CnName = (
                            await userBaseInfoRepository.GetCacheValueAsync(it.CreateByTemp)
                        )?.CnName ?? "",
                        UserId = it.CreateByTemp,
                    };
                }

                return it;
            })
            .ToListAsync(cancellationToken: cancellationToken);
    }
}
