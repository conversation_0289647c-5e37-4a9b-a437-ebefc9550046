using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_flow_relation", DisableSyncStructure = true)]
	public partial class MailFlowRelation {

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "finish_time")]
		public DateTime? FinishTime { get; set; }

		[ Column(Name = "handle_user", StringLength = 50)]
		public string HandleUser { get; set; }

		[ Column(Name = "mail_id", StringLength = 50, IsNullable = false)]
		public string MailId { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "read_only")]
		public bool ReadOnly { get; set; } = false;

	}

}
