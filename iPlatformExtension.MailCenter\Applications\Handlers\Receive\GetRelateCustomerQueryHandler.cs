﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using MediatR;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 获取已关联客户处理者
    /// </summary>
    internal sealed class GetRelateCustomerQueryHandler(IFreeSql<MailCenterFreeSql> freeSql, IFreeSql<PlatformFreeSql> platformFreeSql
        , IUserInfoRepository userInfoRepository, IBaseCountryRepository countryRepository, IDistrictRepository basDistrictRepository) : IRequestHandler<GetRelateCustomerQuery, IEnumerable<GetRelateCustomerDto>>
    {
        public async Task<IEnumerable<GetRelateCustomerDto>> Handle(GetRelateCustomerQuery request, CancellationToken cancellationToken)
        {
            var mailCorrelatives = await freeSql.Select<MailCorrelative>().WithLock().Where(it => it.MailId == request.MailId && it.CorrelateType == SysEnum.CorrelateType.Customer.ToString())
                .ToListAsync(it => new { it.Id, it.ObjId, it.CreateBy, it.CreateTime }, cancellationToken);
            var caseList = mailCorrelatives.Select(it => it.ObjId);
            var getRelateProcDtos = await platformFreeSql.Select<CusCustomer>().WithLock().Where(it => caseList.Contains(it.CustomerId))
                .OrderByDescending(it => it.CreateTime)
                .Page(request.PageIndex.Value, request.PageSize.Value).Count(out var count)
                .ToListAsync(it => new GetRelateCustomerDto(it.CustomerId, it.CustomerName, it.CrmCustomerCode, it.CountryId, it.IsCooperation, it.District,
it.BigArea, it.CustomerType, it.BusiUserId, it.Corporation), cancellationToken);
            var data = await getRelateProcDtos.ToAsyncEnumerable().SelectAwait(async it =>
            {
                if (it.BusiUserId is not null)
                {
                    it.BusiUser = new { CnName = await userInfoRepository.GetTextValueAsync(it.BusiUserId), UserId = it.BusiUserId };
                }

                var relate = mailCorrelatives.FirstOrDefault(x => x.ObjId == it.CustomerId);
                if (relate is not null)
                {
                    it.CreateBy = new
                    {
                        CnName = await userInfoRepository.GetTextValueAsync(relate.CreateBy),
                        UserId = relate.CreateBy
                    };
                    it.CreateTime = relate.CreateTime;
                    it.RelateId = relate.Id;

                }

                if (it.CountryId is not null)
                {
                    it.Country = new { CnName = await countryRepository.GetTextValueAsync(it.CountryId), Id = it.CountryId };
                }

                if (it.DistrictTemp is not null)
                {
                    it.District = new { CnName = await basDistrictRepository.GetTextValueAsync(it.DistrictTemp), Id = it.DistrictTemp };
                }
                return it;
            }).ToListAsync(cancellationToken: cancellationToken);
            return new PageResult<GetRelateCustomerDto>()
            {
                Data = data,
                Page = request.PageIndex.Value,
                PageSize = request.PageSize.Value,
                Total = count
            };
        }
    }
}

