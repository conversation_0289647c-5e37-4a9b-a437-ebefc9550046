using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_notice_convey_setting", DisableSyncStructure = true)]
	public partial class BasNoticeConveySetting {

		[ Column(Name = "case_direction", StringLength = 50)]
		public string CaseDirection { get; set; }

		[ Column(Name = "convey_setting_id", StringLength = 50, IsNullable = false)]
		public string ConveySettingId { get; set; }

		[ Column(Name = "convey_time", StringLength = 50)]
		public string ConveyTime { get; set; }

		[ Column(Name = "convey_type", StringLength = 50)]
		public string ConveyType { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "effective_begin")]
		public DateTime? EffectiveBegin { get; set; }

		[ Column(Name = "effective_end")]
		public DateTime? EffectiveEnd { get; set; }

		[ Column(Name = "file_name_type", StringLength = 500)]
		public string FileNameType { get; set; }

		[ Column(Name = "frequency_type", StringLength = 50)]
		public string FrequencyType { get; set; }

		[ Column(Name = "frequency_value")]
		public int? FrequencyValue { get; set; }

		[ Column(Name = "is_default")]
		public bool IsDefault { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "language", StringLength = 50)]
		public string Language { get; set; }

		[ Column(Name = "mail_cc", StringLength = 50)]
		public string MailCc { get; set; }

		[ Column(Name = "mail_subject", StringLength = 500)]
		public string MailSubject { get; set; }

		[ Column(Name = "mail_template_id", StringLength = 50)]
		public string MailTemplateId { get; set; }

		[ Column(Name = "mail_to", StringLength = 50)]
		public string MailTo { get; set; }

		[ Column(Name = "notice_code", StringLength = 50)]
		public string NoticeCode { get; set; }

		[ Column(Name = "payment_type", StringLength = 50)]
		public string PaymentType { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
