﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkOperationCenter.Controllers
{
    /// <summary>
    /// 团队客户管理控制器
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    [Authorize]
    public class TeamCustomerController : ControllerBase
    {
        private readonly IMediator _mediator;

        public TeamCustomerController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 编辑客户
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        public async Task EditCustomerAsync(EditCustomerCommand command)
        {
            await _mediator.Send(command);
        }

        /// <summary>
        /// 删除客户
        /// </summary>
        /// <param name="SysTeamCustomerId">客户主键</param>
        /// <returns></returns>
        [HttpDelete]
        public async Task DeleteCustomerAsync([FromQuery, Required] string[] SysTeamCustomerId)
        {
            await _mediator.Send(new DeleteCustomerCommand(SysTeamCustomerId));
        }

        /// <summary>
        /// 获取当前团队客户列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetTeamCustomerList")]
        public async Task<IEnumerable<TeamCustomerDto>> GetTeamCustomerListAsync([FromQuery] TeamCustomerQuery model)
        {
            return await _mediator.Send(model);
        }


        /// <summary>
        /// 获取关联客户列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRelateTeamCustomerList")]
        public async Task<RelateTeamCustomerDto> GetRelateTeamMemberListAsync([FromQuery] RelateTeamCustomerQuery model)
        {
            return await _mediator.Send(model);
        }
        /// <summary>
        /// 获取团队成员分页
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetTeamCustomerPage")]
        public async Task<IEnumerable<TeamCustomerPageDto>> GetTeamCustomerPageAsync([FromBody] TeamCustomerPageQuery model)
        {
            return await _mediator.Send(model);
        }
    }
}
