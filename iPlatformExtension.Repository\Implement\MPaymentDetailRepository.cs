﻿using System.Linq.Expressions;
using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class MPaymentDetailRepository : DefaultRepository<MPaymentDetail, long>, IMPaymentDetailRepository
{
    public MPaymentDetailRepository(IFreeSql freeSql, UnitOfWorkManager uowManger) : base(freeSql, uowManger)
    {
    }
}