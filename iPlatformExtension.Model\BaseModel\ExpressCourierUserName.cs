using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "express_courier_user_name", DisableSyncStructure = true)]
	public partial class ExpressCourierUserName {

		[ Column(Name = "id", IsPrimary = true, IsIdentity = true)]
		public int Id { get; set; }

		[ Column(Name = "area", StringLength = 50)]
		public string Area { get; set; }

		[ Column(Name = "city", StringLength = 50)]
		public string City { get; set; }

		[ Column(Name = "courier_id", StringLength = 50)]
		public string CourierId { get; set; }

		[ Column(StringLength = 50)]
		public string CustomerName { get; set; }

		[ Column(StringLength = 50)]
		public string CustomerPwd { get; set; }

		[ Column(StringLength = 50)]
		public string MonthCode { get; set; }

		[ Column(Name = "province", StringLength = 50)]
		public string Province { get; set; }

		[ Column(StringLength = 50)]
		public string SendSite { get; set; }

		[ Column(StringLength = 50)]
		public string SendStaff { get; set; }

	}

}
