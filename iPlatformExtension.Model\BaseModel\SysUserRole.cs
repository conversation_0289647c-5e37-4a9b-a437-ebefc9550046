using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_user_role", DisableSyncStructure = true)]
	public partial class SysUserRole {

		[ Column(Name = "role_id", StringLength = 50, IsNullable = false)]
		public string RoleId { get; set; }

		[ Column(Name = "user_id", StringLength = 50, IsNullable = false)]
		public string UserId { get; set; }

		/// <summary>
		/// 导航属性用户信息
		/// </summary>
		[Navigate(nameof(UserId))]
		public virtual SysUserInfo? User { get; set; }

		/// <summary>
		/// 导航属性角色信息
		/// </summary>
		[Navigate(nameof(RoleId))]
		public virtual SysRoleInfo? Role { get; set; }
	}

}
