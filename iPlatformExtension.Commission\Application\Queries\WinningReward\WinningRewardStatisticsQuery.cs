﻿using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Commission.Application.Queries.WinningReward;

internal record WinningRewardStatisticsQuery(
    DateRange DateRange,
    string? DistrictCode,
    IEnumerable<string> DeptIds,
    string? Keyword) : IRequest<IEnumerable<RewardStatistics>>, IPageQuery
{
    
    public int PageIndex { get; set; }
    
    public int PageSize { get; set; }

    public long? Total { get; set; }
}