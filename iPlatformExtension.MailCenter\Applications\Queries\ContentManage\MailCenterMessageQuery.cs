﻿using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Dto.MailCenter;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.ContentManage
{
    /// <summary>
    /// 邮件中心消息通知
    /// </summary>
    /// <param name="ReaderList"></param>
    /// <param name="OperationType"></param>
    /// <param name="MailId"></param>
    public record MailCenterMessageQuery(List<MailReaderList> ReaderList, OperationTypeEnum OperationType, string MailId = null) : IRequest;

}
