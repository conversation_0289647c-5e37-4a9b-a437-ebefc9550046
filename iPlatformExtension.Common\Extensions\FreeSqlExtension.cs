using System.Linq.Expressions;
using FreeSql;
using FreeSql.Internal.Model;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Db.FreeSQL.Handlers.Curd;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Extensions;

/// <summary>
/// 
/// </summary>
public static class FreeSqlExtension
{

    /// <summary>
    /// 添加<see cref="IFreeSql{TMark}"/>的依赖注入
    /// </summary>
    /// <param name="services">服务注册集合</param>
    /// <param name="options">配置选项</param>
    /// <typeparam name="T">类型参数可用于区分不同数据源</typeparam>
    /// <returns><see cref="PlatformFreeSqlBuilder{T}"/></returns>
    public static PlatformFreeSqlBuilder<T> AddFreeSql<T>(this IServiceCollection services,
        Action<PlatformFreeSqlBuilderOptions<T>> options)
    {
        var builder = new PlatformFreeSqlBuilder<T>
        {
            Services = services,
            // FreeSqlOptionsBuilder = services.AddOptions<PlatformFreeSqlOptions<T>>()
            //     .Configure<IFreeSql<T>>((freeSqlOptions, freeSql) => freeSqlOptions.FreeSql = freeSql),
            FreeSqlBuilderOptionsBuilder = services.AddOptions<PlatformFreeSqlBuilderOptions<T>>().Configure(options)
                .Validate(builderOptions => !string.IsNullOrWhiteSpace(builderOptions.ConnectionString), "连接字符串不能为空")
                .Validate(builderOptions => builderOptions.CommandTimeout.TotalSeconds >= 5, "SQL命令超时时间要5秒以上")
        };
        builder.Configure(options);

        builder.Services.TryAddSingleton(provider =>
        {
            var builderOptions = provider.GetRequiredService<IOptions<PlatformFreeSqlBuilderOptions<T>>>().Value;
            var freeSql = new FreeSqlBuilder()
                .UseConnectionString(builderOptions.DbType, builderOptions.ConnectionString)
                .UseSlave(builderOptions.SlaveConnectionStrings)
                .UseLazyLoading(builderOptions.LazyLoading)
                .UseMonitorCommand(command => command.CommandTimeout = (int)builderOptions.CommandTimeout.TotalSeconds)
                .UseGenerateCommandParameterWithLambda(builderOptions.CommandParameterWithLambda)
                .UseAdoConnectionPool(builderOptions.UseAdoConnectionPool)
                .Build<T>();
            builderOptions.GlobalConfigure(freeSql, provider);
            return new FreeSqlAccessor<T>(freeSql);
        });

        builder.Services.TryAdd(ServiceDescriptor.Describe(
            typeof(IFreeSql<T>), 
            typeof(FreeSqlWrapper<T>),
            builder.BuilderOptions.Lifetime));

        builder.Services.TryAddScoped<UnitOfWorkManage<T>>();

        builder.Services.TryAddScoped<UnitOfWorkManagerProvider>();

        return builder;
    }

    public static PlatformFreeSqlBuilder<T> ConfigureGlobalCurdLogging<T>(this PlatformFreeSqlBuilder<T> builder,
        Action<CurdLoggingOptions> options)
    {
        builder.Services.AddOptions<CurdLoggingOptions>(DefaultCurdLoggingHandler.OptionsName)
            .Configure(options);

        return ConfigureGlobalCurdLoggingCore<T>(builder);
    }

    public static PlatformFreeSqlBuilder<T> ConfigureGlobalCurdLogging<T>(this PlatformFreeSqlBuilder<T> builder,
        string configureSectionName)
    {
        builder.Services.AddOptions<CurdLoggingOptions>(DefaultCurdLoggingHandler.OptionsName)
            .BindConfiguration(configureSectionName);

        return ConfigureGlobalCurdLoggingCore<T>(builder);
    }

    private static PlatformFreeSqlBuilder<T> ConfigureGlobalCurdLoggingCore<T>(this PlatformFreeSqlBuilder<T> builder)
    {
        builder.Services.TryAddSingleton<DefaultCurdLoggingHandler>();

        builder.FreeSqlBuilderOptionsBuilder.PostConfigure(builderOptions =>
        {
            builderOptions.GlobalConfigure += (freeSql, provider) =>
            {
                var loggingHandler = provider.GetRequiredService<DefaultCurdLoggingHandler>();
                freeSql.Aop.CurdBefore += loggingHandler.LogExecutingCurd;
                freeSql.Aop.CurdAfter += loggingHandler.LogExecutedCurd;
            };
        });

        return builder;
    }

    public static PlatformFreeSqlBuilder<PlatformFreeSql> AddPlatformFreeSql(this IServiceCollection services,
        string connectionString, DataType type = DataType.SqlServer,ServiceLifetime serviceLifetime = ServiceLifetime.Scoped)
    {
        var builder = AddFreeSql<PlatformFreeSql>(services, options =>
        {
            options.CommandTimeout = TimeSpan.FromSeconds(300);
            options.Lifetime = serviceLifetime;
            options.ConnectionString = connectionString;
            options.DbType = type;
            options.LazyLoading = true;
            options.CommandParameterWithLambda = true;
            options.TypeHandlers.TryAdd(typeof(DateOnly), new NullableDateOnlyConverter());
        });
        
        services.TryAdd(ServiceDescriptor.Describe(typeof(IFreeSql), 
            provider => provider.GetRequiredService<IFreeSql<PlatformFreeSql>>(), ServiceLifetime.Scoped));
        services.AddScoped<UnitOfWorkManager>(provider =>
            provider.GetRequiredService<UnitOfWorkManage<PlatformFreeSql>>());
        
        return builder;
    }

    /// <summary>
    /// 构建“包含”的查询条件。相当于sql语法的in
    /// </summary>
    /// <param name="toMatchValues">要匹配的值</param>
    /// <param name="propertyName">字段对应的属性名。不能是字段名</param>
    /// <param name="alias">表的别名。取实体类的类名</param>
    /// <param name="otherFilters">其他查询条件</param>
    /// <typeparam name="T">类型参数。对应匹配值的类型参数</typeparam>
    /// <returns><see cref="DynamicFilterInfo"/></returns>
    public static DynamicFilterInfo? BuildContainsDynamicFilter<T>(this IEnumerable<T> toMatchValues, string propertyName, string alias = "", params DynamicFilterInfo[] otherFilters)
    {
        var field = GetFieldName(propertyName, alias);
        DynamicFilterInfo? filterInfo = null;
        if (toMatchValues.Any())
        {
            filterInfo = new DynamicFilterInfo();
            if (toMatchValues.Count() == 1)
            {
                var value = toMatchValues.First();
                filterInfo.Value = value;

                filterInfo.Field = field;
                filterInfo.Operator = DynamicFilterOperator.Eq;
            }
            else
            {
                filterInfo.Value = toMatchValues;
                filterInfo.Field = field;
                filterInfo.Operator = DynamicFilterOperator.Any;
            }

            if (otherFilters.Any())
            {
                filterInfo.Filters = otherFilters.ToList();
            }
        }

        return filterInfo;
    }

    /// <summary>
    /// 构建模糊查询匹配查询条件
    /// </summary>
    /// <param name="value">匹配值</param>
    /// <param name="propertyName">字段对应的属性名。不能是字段名</param>
    /// <param name="alias">表的别名</param>
    /// <param name="otherFilters">其他查询条件</param>
    /// <returns><see cref="DynamicFilterInfo"/></returns>
    public static DynamicFilterInfo? BuildLikeDynamicFilterInfo(this string? value, string propertyName,
        string alias = "", params DynamicFilterInfo[] otherFilters)
    {
        DynamicFilterInfo? filterInfo = null;
        if (!string.IsNullOrWhiteSpace(value))
        {
            var field = GetFieldName(propertyName, alias);

            filterInfo = new DynamicFilterInfo()
            {
                Field = field,
                Operator = DynamicFilterOperator.Contains,
                Value = value
            };

            if (otherFilters.Any())
            {
                filterInfo.Filters = otherFilters.ToList();
            }
        }

        return filterInfo;
    }

    /// <summary>
    /// 构建等值查询条件
    /// </summary>
    /// <param name="value">要比较的值</param>
    /// <param name="propertyName">字段对应的属性名。不能是字段名</param>
    /// <param name="alias">表的别名。取实体类的类名</param>
    /// <param name="otherFilters">其他查询条件</param>
    /// <typeparam name="T">类型参数。对应匹配值的类型参数</typeparam>
    /// <returns><see cref="DynamicFilterInfo"/></returns>
    public static DynamicFilterInfo BuildEqualsDynamicFilter<T>(this T value, string propertyName, string alias = "", params DynamicFilterInfo?[] otherFilters)
    {
        var field = GetFieldName(propertyName, alias);
        var filterInfo = new DynamicFilterInfo
        {
            Value = value,
            Field = field,
            Operator = DynamicFilterOperator.Eq
        };

        if (otherFilters.Length != 0)
        {
            filterInfo.Filters = otherFilters.Where(info => info is not null).ToList();
        }

        return filterInfo;
    }

    /// <summary>
    /// 构建不等于查询表达式
    /// </summary>
    /// <param name="value">带比较的值</param>
    /// <param name="propertyName">字段对应的属性名。不能是字段名</param>
    /// <param name="alias">表达别名。取实体类的名字</param>
    /// <param name="otherFilters">其他过滤条件</param>
    /// <typeparam name="T">类型参数。对应匹配值的类型参数</typeparam>
    /// <returns><see cref="DynamicFilterInfo"/></returns>
    public static DynamicFilterInfo BuildNotEqualsDynamicFilterInfo<T>(this T value, string propertyName,
        string alias = "", params DynamicFilterInfo[] otherFilters)
    {
        var field = GetFieldName(propertyName, alias);
        var filterInfo = new DynamicFilterInfo
        {
            Value = value,
            Field = field,
            Operator = DynamicFilterOperator.NotEqual
        };

        if (otherFilters.Length != 0)
        {
            filterInfo.Filters = otherFilters.ToList();
        }

        return filterInfo;
    }

    public static DynamicFilterInfo BuildDateRangeDynamicFilterInfo(this DateRange dateRange, string propertyName,
        string alias = "", params DynamicFilterInfo?[] otherFilters)
    {
        var (startDate, endDate) = dateRange;
        var filterInfo = new DynamicFilterInfo()
        {
            Value = new[]
            {
                startDate?.ToDateTime(TimeOnly.MinValue, DateTimeKind.Local),
                endDate?.AddDays(1).ToDateTime(TimeOnly.MinValue, DateTimeKind.Local)
            },
            Field = GetFieldName(propertyName, alias),
            Operator = DynamicFilterOperator.DateRange
        };
        
        if (otherFilters.Length != 0)
        {
            filterInfo.Filters = otherFilters.Where(info => info is not null).ToList();
        }

        return filterInfo;
    }

    /// <summary>
    /// 构建排除表达式
    /// </summary>
    /// <param name="toExceptValues">要排除的值</param>
    /// <param name="propertyName">字段对应的属性名。不能是字段名名</param>
    /// <param name="alias">表对应的别名。取类名</param>
    /// <param name="otherFilters">其他过滤条件</param>
    /// <typeparam name="T">排除值对应的类型参数</typeparam>
    /// <returns><see cref="DynamicFilterInfo"/></returns>
    public static DynamicFilterInfo? BuildExceptsDynamicFilterInfo<T>(this IEnumerable<T> toExceptValues,
        string propertyName, string alias = "", params DynamicFilterInfo[] otherFilters)
    {
        if (!toExceptValues.Any()) return default;

        var field = GetFieldName(propertyName, alias);
        var filterInfo = new DynamicFilterInfo();

        if (toExceptValues.Count() == 1)
        {
            var value = toExceptValues.First();
            return value.BuildNotEqualsDynamicFilterInfo(propertyName, alias, otherFilters);
        }
        else
        {
            filterInfo.Value = toExceptValues;
            filterInfo.Field = field;
            filterInfo.Operator = DynamicFilterOperator.NotAny;
        }

        if (otherFilters.Length != 0)
        {
            filterInfo.Filters = otherFilters.ToList();
        }

        return filterInfo;
    }

    /// <summary>
    /// 根据条件是否应用过滤条件
    /// </summary>
    /// <param name="query"><see cref="ISelect{T1}"/></param>
    /// <param name="condition">条件</param>
    /// <param name="dynamicFilterInfo">过滤条件</param>
    /// <typeparam name="T">类型参数。对应某个具体表的实体</typeparam>
    /// <returns><see cref="ISelect{T1}"/></returns>
    public static ISelect<T> WhereIfDynamicFilter<T>(this ISelect<T> query, bool condition,
        DynamicFilterInfo? dynamicFilterInfo)
    {
        return condition ? query.WhereDynamicFilter(dynamicFilterInfo) : query;
    }

    /// <summary>
    /// 根据条件是否应用集合导航属性的加载
    /// </summary>
    /// <param name="query">原查询条件</param>
    /// <param name="condition">条件</param>
    /// <param name="collection">集合导航属性lambda表达式</param>
    /// <param name="then">集合导航属性的额外扩展</param>
    /// <typeparam name="T">主表的类型参数</typeparam>
    /// <typeparam name="TCollection">集合导航属性对应的类型参数</typeparam>
    /// <returns><see cref="ISelect{T1}"/></returns>
    public static ISelect<T> IncludeManyIf<T, TCollection>(this ISelect<T> query, bool condition,
        Expression<Func<T, IEnumerable<TCollection>>> collection, Action<ISelect<TCollection>>? then = null) where TCollection : class
    {
        return condition ? query.IncludeMany(collection, then) : query;
    }

    /// <summary>
    /// 构建freeSql时间段查询条件
    /// </summary>
    /// <param name="dto">时间段参数<see cref="PeriodQueryDto"/></param>
    /// <param name="propertyName">字段对应的属性名。不能是字段名</param>
    /// <param name="alias">表的别名。取实体类的类名</param>
    /// <returns><see cref="DynamicFilterInfo"/>动态查询条件</returns>
    public static DynamicFilterInfo? BuildDynamicTimePeriodFilter(this PeriodQueryDto dto, string propertyName, string alias = "")
    {
        if (!dto.StartTime.HasValue && !dto.EndTime.HasValue)
        {
            return null;
        }

        var field = GetFieldName(propertyName, alias);
        var filterInfo = new DynamicFilterInfo();
        switch (dto)
        {
            case { StartTime: not null, EndTime: null }:
                filterInfo.Field = field;
                filterInfo.Value = dto.StartTime.Value;
                filterInfo.Operator = DynamicFilterOperator.GreaterThanOrEqual;
                break;
            case { EndTime: not null, StartTime: null }:
                filterInfo.Field = field;
                filterInfo.Value = dto.EndTime.Value;
                filterInfo.Operator = DynamicFilterOperator.LessThanOrEqual;
                break;
            default:
                filterInfo.Field = field;
                filterInfo.Value = dto.StartTime;
                filterInfo.Operator = DynamicFilterOperator.GreaterThanOrEqual;
                filterInfo.Filters =
                [
                    new DynamicFilterInfo
                    {
                        Field = field,
                        Value = dto.EndTime,
                        Operator = DynamicFilterOperator.LessThanOrEqual
                    }
                ];
                break;
        }

        return filterInfo;
    }

    public static async Task<IEnumerable<TEntity>> ToPageableResultAsync<TEntity>(this ISelect<TEntity> query,
        IPaginationParameters paginationParameters, CancellationToken cancellationToken = default)
    {
        var pageInfo = paginationParameters.PagingInfo;
        if (pageInfo is null)
            return await query.ToListAsync(cancellationToken);

        var result = await query.Page(pageInfo).ToListAsync(cancellationToken);
        return new PageResult<TEntity>
        {
            Data = result,
            Page = pageInfo.PageNumber,
            PageSize = pageInfo.PageSize,
            Total = pageInfo.Count
        };
    }

    public static async Task<IEnumerable<TResult>> ToPageableResultAsync<T1, TResult>(this ISelect<T1> query,
        IPaginationParameters paginationParameters, Expression<Func<T1, TResult>> selectExpression,
        CancellationToken cancellationToken = default)
    {
        var pageInfo = paginationParameters.PagingInfo;
        if (pageInfo is null)
            return await query.ToListAsync(selectExpression, cancellationToken);

        var result = await query.Page(pageInfo).ToListAsync(selectExpression, cancellationToken);
        return new PageResult<TResult>
        {
            Data = result,
            Page = pageInfo.PageNumber,
            PageSize = pageInfo.PageSize,
            Total = pageInfo.Count
        };
    }

    public static async Task<IEnumerable<TResult>> ToPageableResultAsync<T1, T2, TResult>(this ISelect<T1, T2> query,
        IPaginationParameters paginationParameters, Expression<Func<T1, T2, TResult>> selectExpression,
        CancellationToken cancellationToken = default) where T2 : class
    {
        var pageInfo = paginationParameters.PagingInfo;
        if (pageInfo is null)
            return await query.ToListAsync(selectExpression, cancellationToken);

        var result = await query.Page(pageInfo).ToListAsync(selectExpression, cancellationToken);
        return new PageResult<TResult>
        {
            Data = result,
            Page = pageInfo.PageNumber,
            PageSize = pageInfo.PageSize,
            Total = pageInfo.Count
        };
    }

    public static async Task<IEnumerable<TResult>> ToPageableResultAsync<T1, T2, T3, TResult>(this ISelect<T1, T2, T3> query,
        IPaginationParameters paginationParameters, Expression<Func<T1, T2, T3, TResult>> selectExpression,
        CancellationToken cancellationToken = default) where T2 : class where T3 : class
    {
        var pageInfo = paginationParameters.PagingInfo;
        if (pageInfo is null)
            return await query.ToListAsync(selectExpression, cancellationToken);

        var result = await query.Page(pageInfo).ToListAsync(selectExpression, cancellationToken);
        return new PageResult<TResult>
        {
            Data = result,
            Page = pageInfo.PageNumber,
            PageSize = pageInfo.PageSize,
            Total = pageInfo.Count
        };
    }

    public static async Task<IEnumerable<TResult>> ToPageableResultAsync<T1, T2, T3, T4, TResult>(this ISelect<T1, T2, T3, T4> query,
        IPaginationParameters paginationParameters, Expression<Func<T1, T2, T3, T4, TResult>> selectExpression,
        CancellationToken cancellationToken = default) where T2 : class where T3 : class where T4 : class
    {
        var pageInfo = paginationParameters.PagingInfo;
        if (pageInfo is null)
            return await query.ToListAsync(selectExpression, cancellationToken);

        var result = await query.Page(pageInfo).ToListAsync(selectExpression, cancellationToken);
        return new PageResult<TResult>
        {
            Data = result,
            Page = pageInfo.PageNumber,
            PageSize = pageInfo.PageSize,
            Total = pageInfo.Count
        };
    }

    public static async Task<IEnumerable<TResult>> ToPageableResultAsync<T1, T2, T3, T4, T5, TResult>(this ISelect<T1, T2, T3, T4, T5> query,
        IPaginationParameters paginationParameters, Expression<Func<T1, T2, T3, T4, T5, TResult>> selectExpression,
        CancellationToken cancellationToken = default) where T2 : class where T3 : class where T4 : class where T5 : class
    {
        var pageInfo = paginationParameters.PagingInfo;
        if (pageInfo is null)
            return await query.ToListAsync(selectExpression, cancellationToken);

        var result = await query.Page(pageInfo).ToListAsync(selectExpression, cancellationToken);
        return new PageResult<TResult>
        {
            Data = result,
            Page = pageInfo.PageNumber,
            PageSize = pageInfo.PageSize,
            Total = pageInfo.Count
        };
    }



    /// <summary>
    /// 将字符串转换为sql语句的常量字符串
    /// </summary>
    /// <param name="constantString">原始字符串</param>
    /// <returns>转后的sql语句常量字符串</returns>
    public static string ToSqlStringConstant(this string constantString)
    {
        ArgumentNullException.ThrowIfNull(constantString);
        return constantString;
    }

    /// <summary>
    /// 拼接sql的字段名
    /// </summary>
    /// <param name="propertyName">属性名称</param>
    /// <param name="alias">实体类名</param>
    /// <returns>拼接的别名</returns>
    public static string GetFieldName(string propertyName, string? alias)
    {
        ArgumentNullException.ThrowIfNull(propertyName);
        return string.IsNullOrWhiteSpace(alias) ? propertyName : $"{alias}.{propertyName}";
    }
    
    /// <summary>
    /// 根据条件动态拼接sql查询条件
    /// </summary>
    /// <param name="query">sql查询</param>
    /// <param name="condition">判断条件</param>
    /// <param name="filterExpression">sql查询表达式</param>
    /// <typeparam name="T">主表的类型</typeparam>
    /// <typeparam name="T1">连接的字表类型</typeparam>
    /// <returns>sql查询</returns>
    public static ISelect<T> WhereIf<T, T1>(this ISelect<T> query, bool condition, Expression<Func<T, T1, bool>> filterExpression) where T1 : class
    {
        return condition ? query.Where<T1>(filterExpression) : query;
    }

    /// <summary>
    /// 查找第一条
    /// </summary>
    /// <typeparam name="T1"></typeparam>
    /// <param name="select"></param>
    /// <param name="exp"></param>
    /// <returns></returns>
    public static T1 WhereToOne<T1>(this ISelect<T1> select, Expression<Func<T1, bool>> exp)
    {
        return select.Where(exp).ToOne<T1>();
    }

    /// <summary>
    /// 根据判断条件查找第一条
    /// </summary>
    /// <typeparam name="T1"></typeparam>
    /// <param name="select"></param>
    /// <param name="attach"></param>
    /// <param name="predicate"></param>
    /// <returns></returns>
    public static T1 WhereIf<T1>(this ISelect<T1> select, bool attach, Expression<Func<T1, bool>> exp)
    {
        return attach ? select.WhereToOne(exp) : select.ToOne<T1>();
    }

    /// <summary>
    /// 查找列表
    /// </summary>
    /// <typeparam name="T1"></typeparam>
    /// <param name="select"></param>
    /// <param name="exp">表达式</param>
    /// <returns></returns>
    public static List<T1> WhereToList<T1>(this ISelect<T1> select, Expression<Func<T1, bool>> exp)
    {
        return select.Where(exp).ToList<T1>();
    }

    /// <summary>
    /// 查找列表
    /// </summary>
    /// <typeparam name="T1"></typeparam>
    /// <param name="select"></param>
    /// <param name="attach">是否启用</param>
    /// <param name="predicate"></param>
    /// <returns></returns>
    public static List<T1> WhereToList<T1>(this ISelect<T1> select, bool attach, Expression<Func<T1, bool>> exp)
    {
        return attach ? select.WhereToList(exp) : select.ToList<T1>();
    }

    /// <summary>
    /// 左右对比一对多拼接
    /// </summary>
    /// <typeparam name="T1"></typeparam>
    /// <typeparam name="T2"></typeparam>
    /// <returns></returns>
    public static Expression<Func<T1, T2, bool>>? OrThanExpression<T1, T2>(string left, List<string> rightList,
        DynamicFilterOperator dynamicFilterOperator)
    {
        Expression<Func<T1, T2, bool>>? expression = null;
        // 创建参数表达式
        var t1 = Expression.Parameter(typeof(T1), "T1");
        var t2 = Expression.Parameter(typeof(T2), "T2");

        var leftExpression = Expression.Property(t1, left);

        foreach (var right in rightList)
        {
            BinaryExpression? comparisonExpression = null;

            var rightExpression = Expression.Property(t1, right);
            comparisonExpression = dynamicFilterOperator switch
            {
                DynamicFilterOperator.GreaterThanOrEqual => Expression.GreaterThanOrEqual(leftExpression,
                    rightExpression),
                DynamicFilterOperator.GreaterThan => Expression.GreaterThan(leftExpression, rightExpression),
                DynamicFilterOperator.LessThan => Expression.LessThan(leftExpression, rightExpression),
                DynamicFilterOperator.LessThanOrEqual => Expression.LessThanOrEqual(leftExpression, rightExpression),
                _ => comparisonExpression
            };

            if (comparisonExpression == null) throw new ArgumentException("DynamicFilterOperator Error!");
            expression = expression == null ? Expression.Lambda<Func<T1, T2, bool>>(comparisonExpression, t1, t2) :
                expression.Or(Expression.Lambda<Func<T1, T2, bool>>(comparisonExpression, t1, t2));
        }

        return expression;
    }

    
    /// <summary>
    /// 异步设置回滚源为指定的实体，忽略版本号自动加一，并指定更新内容为将实体的版本号减1。
    /// </summary>
    /// <param name="repository">基础仓库接口，用于执行数据操作。</param>
    /// <param name="entity">需要回滚版本的实体对象。</param>
    /// <param name="version">当前实体的版本号，仅当实体的版本号等于指定的版本时执行更新操作。</param>
    /// <param name="cancellationToken">用于取消操作的令牌。</param>
    /// <typeparam name="T">实体类型，需要实现IVersionEntity接口。</typeparam>
    /// <returns>返回一个任务，表示异步操作，并包含影响的行数。</returns>
    public static Task<int> RollbackVersionEntityAsync<T>(this IBaseRepository<T> repository, T entity, int version, CancellationToken cancellationToken = default)
        where T : class, IVersionEntity
    {
        // 设置更新源为指定的实体，忽略版本号自动加一，并指定更新内容为将实体的版本号减1。
        // 仅当实体的版本号等于指定的版本时执行更新操作。
        // 执行异步操作，返回影响的行数。
        return repository.UpdateDiy.SetSource([entity], ignoreVersion: true)
            .Set(update => update.Version - 1)
            .Where(update => update.Version == version)
            .ExecuteAffrowsAsync(cancellationToken);
    }

}