﻿using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Commission.Infrastructure.Logging;
using MediatR;
using MediatR.Pipeline;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class CreateDomesticRewardsExceptionHandler(ILogger<CreateRewardsCommand> logger) 
    : IRequestExceptionHandler<CreateRewardsCommand, Unit, Exception>
{
    public Task Handle(CreateRewardsCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        logger.LogCreatingDomesticRewardsError(exception);
        state.SetHandled(Unit.Value);
        return Task.CompletedTask;
    }
}