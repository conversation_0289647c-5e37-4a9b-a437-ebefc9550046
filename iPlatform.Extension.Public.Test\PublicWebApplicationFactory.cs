﻿using System.Text.Json;
using System.Text.Json.Serialization;
using iPlatformExtension.Common.Converters;
using Microsoft.AspNetCore.Http.Json;
using Microsoft.AspNetCore.Mvc.Testing;

namespace iPlatform.Extension.Public.Test;

public class PublicWebApplicationFactory : WebApplicationFactory<Program>
{
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        // builder.ConfigureServices(services => services.AddOptions<JsonOptions>().Configure(options =>
        // {
        //     options.SerializerOptions.Converters.Add(new JsonStringEnumConverter());
        //     options.SerializerOptions.Converters.Add(new DateTimeOffsetJsonConverter());
        //     options.SerializerOptions.Converters.Add(new NullableDateTimeJsonConverter());
        //     options.SerializerOptions.Converters.Add(new DateOnlyJsonConverter());
        //     options.SerializerOptions.Converters.Add(new TimeOnlyJsonConverter());
        //     options.SerializerOptions.Converters.Add(new DateTimeToDateOnlyJsonConverter());
        //     options.SerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        // }));
        
        
        builder.UseEnvironment("Local");
        base.ConfigureWebHost(builder);
    }

    protected override void ConfigureClient(HttpClient client)
    {
        client.Timeout = TimeSpan.FromMinutes(5);
        client.DefaultRequestHeaders.Add("blade-auth", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZW5hbnRfaWQiOiIwMDAwMDAiLCJiZWxvbmdfdG9fZGlzdHJpY3QiOm51bGwsInVzZXJfbmFtZSI6IkNSTSIsIm1hbmFnZV9kaXN0cmljdCI6bnVsbCwicmVhbF9uYW1lIjoiQ1JNIiwiY2xpZW50X2lkIjoiY2FzZWNjIiwicm9sZV9pZCI6IjE2MDg0ODYyMjgyNTUwNTE3NzgsMTcxOTI4NTU5NzQ0NjYwNjg1MCIsInNjb3BlIjpbImFsbCJdLCJvYXV0aF9pZCI6IiIsImV4cCI6MTc0NTkyODA1OSwianRpIjoic3BQbkVIeHNvQXRIMVF6Vl9fM3lnWGFiOFpZIiwidXNlcl9hZ2VudCI6Ik1vemlsbGEvNS4wIChXaW5kb3dzIE5UIDEwLjA7IFdpbjY0OyB4NjQpIEFwcGxlV2ViS2l0LzUzNy4zNiAoS0hUTUwsIGxpa2UgR2Vja28pIENocm9tZS8xMzAuMC4wLjAgU2FmYXJpLzUzNy4zNiBRdWFya1BDLzIuNS41LjMwNCIsImRlcHRfYmVsb25nX2lkIjoiNjc3IiwiYmVsb25nX3RvX2NvbXBhbnkiOm51bGwsImF1dGhfdXVpZCI6Ik9XUTJZall4TmpVdE16ZzVNeTAwT1RBMkxUaGhZMll0T1dJek0yWmxOR0ZtTVRWaSIsImF2YXRhciI6IiIsImF1dGhvcml0aWVzIjpbIuaZrumAmuWRmOW3pSIsIuesrOS4ieaWueaOpeWPo-S4k-eUqOinkuiJsijli7_liKDmlLkpIl0sInJvbGVfbmFtZSI6IuesrOS4ieaWueaOpeWPo-S4k-eUqOinkuiJsijli7_liKDmlLkpLOaZrumAmuWRmOW3pSIsInJvbGV0eXBlcyI6WyJmbG93VXNlciIsInRyYWRlLWFyZWEiXSwicG9zdF9pZCI6IjIiLCJ1c2VyX2lkIjoiMTc2Mjc3MjQ1MDQ5MjM2Mjc1NCIsIm5pY2tfbmFtZSI6IkNSTSIsIm1hbmFnZV9jb21wYW55IjpudWxsLCJkZXRhaWwiOnsidHlwZSI6IndlYiIsImRlcHRCZWxvbmdJZCI6IjY3NyIsImNvZGUiOiJDUk0ifSwiZGVwdF9pZCI6IjY3NyIsImFjY291bnQiOiJDUk0ifQ.zENEfLA_ic3C3928R-geYXd8VLSL2uRlVosQEbVhE7o");
        base.ConfigureClient(client);
    }
}