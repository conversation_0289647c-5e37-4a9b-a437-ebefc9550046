﻿namespace iPlatformExtension.Model.Dto.File;

/// <summary>
/// 文件列表
/// </summary>
public class FileListDto
{
    /// <summary>
    /// 文件id
    /// </summary>
    public string FileId { get; set; } = default!;
    
    /// <summary>
    /// 文件名
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 文件类型
    /// </summary>
    public string? FileType { get; set; }

    /// <summary>
    /// 文件描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 上传者
    /// </summary>
    public string? Uploader { get; set; }

    /// <summary>
    /// 上传时间
    /// </summary>
    public DateTime UploadTime { get; set; }
}