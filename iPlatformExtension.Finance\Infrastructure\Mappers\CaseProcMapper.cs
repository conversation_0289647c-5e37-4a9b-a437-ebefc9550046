﻿using AutoMapper;
using iPlatformExtension.Finance.Applications.Models.Proc;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Finance.Infrastructure.Mappers;

internal sealed class CaseProcMapper : Profile
{
    public CaseProcMapper()
    {
        CreateMap<CaseProcPatchDto, CaseProcInfo>()
            .ForMember(info => info.DeliveryKey, options =>
            {
                options.Condition((_, info) => string.IsNullOrWhiteSpace(info.DeliveryKey));
                options.MapFrom(dto => dto.DeliveryKey);
            });

        CreateMap<CaseProcInfo, CaseProcPatchDto>();

        CreateMap<UnpaidOfficialProcDto, UnpaidOfficialProcExportDto>()
            .ForMember(dto => dto.ApplicantNames, options =>
                options.MapFrom(dto => string.Join(',', dto.Applicants.Select(info => info.ApplicantCnName))))
            .ForMember(dto => dto.CustomerName, options =>
                options.MapFrom(dto => dto.Customer.CnName))
            .ForMember(dto => dto.TrademarkName, options =>
                options.MapFrom(dto => dto.CaseName))
            .ForMember(dto => dto.DeliveryKey, options =>
                options.MapFrom(dto => dto.DeliveryKey.Value))
            .ForMember(dto => dto.CaseDirection, options =>
                options.MapFrom(dto => dto.CaseDirection.Value))
            .ForMember(dto => dto.ApplyNoOrRegistrationNo, options =>
                options.MapFrom(
                    dto => string.IsNullOrWhiteSpace(dto.ApplicantNo) ? dto.RegistrationNo : dto.ApplicantNo))
            .ForMember(dto => dto.ControlIdentifiers, options =>
                options.MapFrom(dto => string.Join(';', dto.CustomerControlIdentifiers.Select(info => info.CnName))));
    }
}