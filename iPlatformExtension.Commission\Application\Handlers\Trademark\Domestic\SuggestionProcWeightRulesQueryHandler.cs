﻿using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class SuggestionProcWeightRulesQueryHandler(
    IFreeSql<PlatformFreeSql> freeSql,
    IBaseCtrlProcRepository baseCtrlProcRepository,
    ISystemDictionaryRepository systemDictionaryRepository) 
    : IRequestHandler<SuggestionProcWeightMatchRulesQuery, IEnumerable<SuggestionProcWeightRuleDto>>
{
    public async Task<IEnumerable<SuggestionProcWeightRuleDto>> Handle(SuggestionProcWeightMatchRulesQuery request, CancellationToken cancellationToken)
    {
        var configId = request.ConfigId;
        var rules = await freeSql.Select<SuggestionProcPointConfig>().WithLock()
            .Where(config => config.TrademarkBonusConfigId == configId)
            .ToListAsync(cancellationToken);

        return await rules.ToAsyncEnumerable().SelectAwait(async rule => new SuggestionProcWeightRuleDto
        {
            RuleId = rule.Id,
            CtrlProc = new CtrlProcInfo(rule.TargetCtrlProcId,
                await baseCtrlProcRepository.GetChineseValueAsync(rule.TargetCtrlProcId)),
            CtrlProcMark = new SystemDictionaryInfo(rule.TargetProcMark,
                await systemDictionaryRepository.GetChineseValueAsync(SystemDictionaryName.CtrlProcMark,
                    rule.TargetProcMark)),
            IsEnable = rule.IsEnable.GetBooleanChineseDescription()
        }).ToListAsync(cancellationToken);
    }
}