﻿using Confluent.Kafka;
using iPlatformExtension.Public.Applications.Queries.Flow;
using iPlatformExtension.Public.Hubs;
using iPlatformExtension.Public.Infrastructure.SignalR;
using MediatR;
using Microsoft.AspNetCore.SignalR;
using MongoDB.Bson.IO;

namespace iPlatformExtension.Public.Applications.Handlers.SignalR
{
    public class FlowAnalyseHandler : IRequestHandler<FlowAnalyseQuery, bool>
    {
        private readonly IFreeSql _freeSql;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IHubContext<NotificationHub> _hubContext;

        public FlowAnalyseHandler(IFreeSql freeSql, IHttpContextAccessor httpContextAccessor, IHubContext<NotificationHub> hubContext)
        {
            _freeSql = freeSql;
            _httpContextAccessor = httpContextAccessor;
            _hubContext = hubContext;
        }

        public async Task<bool> Handle(FlowAnalyseQuery request, CancellationToken cancellationToken)
        {
            //public async Task SendMessage(string user, string message)
            //{
            //    // await Clients.All.SendAsync("FlowMessage", user, message);

            //    string name = _hubContext.Clients.Clients..Identity.Name;

            //    foreach (var connectionId in _connections.GetConnections(who))
            //    {
            //        // Clients.Client(connectionId).addChatMessage(name + ": " + message);
            //        await Clients.Client(connectionId).SendAsync("FlowMessage", user, message);
            //    }
            //}

            //todo:根据用户过滤发送
            _hubContext.Clients.All.SendAsync("FlowMessage","user1" ,Newtonsoft.Json.JsonConvert.SerializeObject(request.MessageJsonData));
            return true;
        }
    }
}
