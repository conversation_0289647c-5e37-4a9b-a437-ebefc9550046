﻿using iPlatformExtension.Finance.Applications.Commands.Fees;
using iPlatformExtension.Finance.Applications.Models.Fees;
using iPlatformExtension.Model.Dto;
using MediatR;

namespace iPlatformExtension.Finance.Applications.PipelineBehaviors.FeeQuery;

internal abstract class CheckFeesQueryParametersBehaviorBase<TRequest> 
    : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPipelineBehavior<TRequest, Unit> where TRequest : IBuildFeeQueryCommand
{
    private readonly ILogger _logger;

    protected CheckFeesQueryParametersBehaviorBase(ILoggerFactory loggerFactory)
    {
        _logger = loggerFactory.CreateLogger(GetType());
    }

    public virtual async Task<Unit> Handle(TRequest request, RequestHandlerDelegate<Unit> next, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
        {
            _logger.LogWarning("费项查询参数检查：操作取消");
            return Unit.Value;
        }

        if (Check(request.Dto))
        {
            await next();
        }
        
        return Unit.Value;
    }

    public abstract bool Check(FeeQueryDto dto);
}