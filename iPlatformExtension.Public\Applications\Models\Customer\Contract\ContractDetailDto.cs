﻿namespace iPlatformExtension.Public.Applications.Models.Customer.Contract;

using System.ComponentModel;

/// <summary>
/// 合同明细
/// </summary>
[Description("合同明细")]
public class ContractDetailDto
{
    /// <summary>
    /// 业务类型
    /// </summary>
    [Description("业务类型")]
    public required string BusinessType { get; set; }

    /// <summary>
    /// 销售用户工号
    /// </summary>
    [Description("销售用户工号")]
    public required string PreSalesUser { get; set; }

    /// <summary>
    /// 合同备注
    /// </summary>
    [Description("合同备注")]
    public string? Remark { get; set; }

    /// <summary>
    /// crm的合同id
    /// </summary>
    [Description("crm的合同id")]
    public required string CrmContractId { get; set; }

    /// <summary>
    /// 是否有效
    /// </summary>
    [Description("是否有效")]
    public required bool IsEnable { get; set; }

    /// <summary>
    /// 生效日期
    /// </summary>
    [Description("生效日期")]
    public required DateOnly EffectiveDate { get; set; }
}