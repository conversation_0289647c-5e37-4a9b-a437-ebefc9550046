using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mon_allot", DisableSyncStructure = true)]
	public partial class MonAllot {

		[ Column(Name = "allot_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string AllotId { get; set; }

		[ Column(Name = "agent_fee_add", DbType = "money")]
		public decimal? AgentFeeAdd { get; set; }

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "amount_pre", DbType = "money")]
		public decimal? AmountPre { get; set; }

		[ Column(Name = "bs_id", StringLength = 50)]
		public string BsId { get; set; }

		[ Column(Name = "del_enabled")]
		public bool? DelEnabled { get; set; } = false;

		[ Column(Name = "deli_id", StringLength = 50)]
		public string DeliId { get; set; }

		[ Column(Name = "deli_status")]
		public int? DeliStatus { get; set; } = 0;

		[ Column(Name = "deli_update_time")]
		public DateTime? DeliUpdateTime { get; set; }

		[ Column(Name = "deli_update_user_id", StringLength = 50)]
		public string DeliUpdateUserId { get; set; }

		[ Column(Name = "proc_id", StringLength = 50)]
		public string ProcId { get; set; }

		[ Column(Name = "processor", StringLength = 50)]
		public string Processor { get; set; }

		[ Column(Name = "receive_id", StringLength = 50)]
		public string ReceiveId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "status")]
		public int? Status { get; set; } = 0;

		[ Column(Name = "with_pre")]
		public bool? WithPre { get; set; } = false;

	}

}
