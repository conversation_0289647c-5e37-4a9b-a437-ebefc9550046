using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Json.Serialization.Metadata;
using FluentValidation;
using iPlatformExtension.Common.Clients.HttpMessageHandlers;
using iPlatformExtension.Common.Clients.Options;
using iPlatformExtension.Common.Clients.Phoenix;
using iPlatformExtension.Common.Converters;
using iPlatformExtension.Common.Modifiers;
using iPlatformExtension.Model.Constants;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.Extensions;

public static class PhoenixClientExtension
{
    /// <summary>
    /// 添加权大师客户端的依赖注入配置
    /// </summary>
    /// <param name="services">依赖注入的服务描述集合</param>
    /// <param name="optionsName">权大师客户端配置选项的名称</param>
    /// <param name="isProduction">是否生产环境</param>
    public static void AddPhoenixClient(this IServiceCollection services, string optionsName = "Phoenix", bool isProduction = false)
    {
        ArgumentNullException.ThrowIfNull(services);

        services.AddOptions<PhoenixClientOptions>(optionsName)
            .BindConfiguration(optionsName, options => options.ErrorOnUnknownConfiguration = true).Configure(options =>
            {
                options.SerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.SerializerOptions.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
                options.SerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.SerializerOptions.Converters.Add(new DateOnlyJsonConverter());
                options.SerializerOptions.Converters.Add(new DateTimeJsonConverter());
                options.SerializerOptions.Converters.Add(new DateTimeOffsetJsonConverter());
                options.SerializerOptions.Converters.Add(new NullableDateTimeJsonConverter());
                options.SerializerOptions.Converters.Add(new NullableDateTimeOffsetConverter());
                options.SerializerOptions.TypeInfoResolver =
                    (options.SerializerOptions.TypeInfoResolver ?? new DefaultJsonTypeInfoResolver()).WithAddedModifier(
                        new PropertySerializationSourceModifier()
                            .ModifyTypeInfo);
            }).Validate<IValidator<PhoenixClientOptions>>((options, validator) => validator.Validate(options).IsValid,
                "递交配置选项验证错误！请确认是否已添加对应的递交Key以及正确配置对应选项");
        services.AddScoped<PublicParametersHandler>();
        services.AddScoped<TokenHandler>();
        services.AddScoped<Md5SignHandler>();
        services.AddScoped<W3CTraceContextHandler>();
        var builder = services.AddHttpClient<PhoenixClient>(optionsName)
            .AddHttpMessageHandler<W3CTraceContextHandler>()
            .AddHttpMessageHandler<PublicParametersHandler>()
            .AddHttpMessageHandler<Md5SignHandler>()
            .ConfigureAdditionalHttpMessageHandlers((handlers, _) =>
            {
                foreach (var t in handlers)
                {
                    if (t is NamedDelegatingHandler namedDelegatingHandler)
                    {
                        namedDelegatingHandler.OptionsName = optionsName;
                    }
                }
            });
        services.TryAddSingleton<PhoenixClientFactory>();

        if (!isProduction)
        {
            services.AddOptions<HttpClientLoggingOptions>().Configure(options =>
            {
                options[PhoenixUri.NiceCategories] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true
                };
                options[PhoenixUri.CreateOrder] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
                options[PhoenixUri.SubmitOrder] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
                options[PhoenixUri.CancelOrder] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
                options[PhoenixUri.StopOrder] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
                options[PhoenixUri.WithdrawOrder] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };              
                options[PhoenixUri.WithdrawalLicenseFiling] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
                options[PhoenixUri.TrademarkRenewal] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
                options[PhoenixUri.TrademarkRefuseReexamination] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
                options[PhoenixUri.ChangeOrder] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
                options[PhoenixUri.TransferOrder] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
                options[PhoenixUri.WithdrawThreeYears] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
                options[PhoenixUri.TrademarkObjections] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
                options[PhoenixUri.InvalidationOrder] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
                options[PhoenixUri.TrademarkLicenseFiling] = new HttpClientLoggingFieldsOptions()
                {
                    RequestBody = true,
                    RequestHeader = true,
                    ResponseBody = true
                };
            });
            services.AddScoped<ContentLoggingHandler>();
            builder.AddHttpMessageHandler<ContentLoggingHandler>();
        }
        builder.AddHttpMessageHandler(provider =>
                new HttpRequestExceptionHandler<PhoenixClient>(provider.GetRequiredService<ILoggerFactory>(), true));
    }
}