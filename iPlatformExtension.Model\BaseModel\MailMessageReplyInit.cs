using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "mail_message_reply_init", DisableSyncStructure = true)]
	public partial class MailMessageReplyInit {

		[ Column(Name = "customer_id", StringLength = 50, IsNullable = false)]
		public string CustomerId { get; set; }

		[ Column(Name = "init_id", StringLength = 50, IsNullable = false)]
		public string InitId { get; set; } = Guid.NewGuid().ToString().ToUpper();

	}

}
