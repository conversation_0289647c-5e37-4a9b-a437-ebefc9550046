﻿namespace iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;

/// <summary>
/// 递交任务状态
/// </summary>
public class DeliveryInfoStatus
{
    /// <summary>
    /// 流程状态
    /// </summary>
    public int FlowStatus { get; set; }

    /// <summary>
    /// 递交状态
    /// </summary>
    public string DeliveryStatus { get; set; } = null!;

    /// <summary>
    /// 递交快照是否需要刷新
    /// </summary>
    public bool HasNew { get; set; }

    /// <summary>
    /// 是否已经锁定
    /// </summary>
    public bool Locked { get; set; }

    /// <summary>
    /// 递交按钮
    /// </summary>
    public IReadOnlyDictionary<string, bool> DeliveryButtons { get; set; } = default!;
}