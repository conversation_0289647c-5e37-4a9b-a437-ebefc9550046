﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using iPlatformExtension.Commission.Application.Models.Trademark;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Model.Dto.Customer;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.Domestic;

internal sealed class ComputeDomesticCommissionPointCommandHandler(
    ISender sender,
    IBigClientRepository bigClientRepository) 
    : IRequestHandler<ComputeDomesticCommissionPointCommand, bool>
{
    public async Task<bool> Handle(ComputeDomesticCommissionPointCommand request, CancellationToken cancellationToken)
    {
        var commission = request.Commission;
        var freeSql = bigClientRepository.Orm;

        var configs = await freeSql.Select<TrademarkBonusConfig>()
            .Where(config => config.CtrlProcId == commission.CtrlProcId)
            .Where(config => config.IsEnabled == true)
            .Where(config => config.CaseDirection == CaseDirection.II)
            .Where(config => config.CtrlProcStatusId == commission.ProcStatusId)
            .ToListAsync(cancellationToken);

        List<TrademarkBonusConfig> matchConfigs;
        if (!string.IsNullOrWhiteSpace(commission.CtrlProcMark))
        {
            matchConfigs = configs.Where(config => config.CtrlProcMark == commission.CtrlProcMark).ToList();
            if (matchConfigs.Count == 0)
            {
                matchConfigs = configs.Where(config => string.IsNullOrWhiteSpace(config.CtrlProcMark)).ToList();
            }
        }
        else
        {
            matchConfigs = configs.Where(config => string.IsNullOrWhiteSpace(config.CtrlProcMark)).ToList();
        }

        if (matchConfigs.Count is not 1)
        {
            return false;
        }

        var matchConfig = matchConfigs[0];
        
        var factor = new ProcCommissionWeightFactor
        {
            ProcPoint = matchConfig.RealPoint
        };

        var undertakerId = commission.ProcMainUndertakerId;
        var userLevels = await freeSql.Select<SysDeptUser>()
            .Where(user => user.UserId == undertakerId)
            .Where(user => user.IsDefault == true)
            .GroupBy(user => user.UserLevel)
            .ToListAsync(aggregate => aggregate.Key, cancellationToken);

        switch (userLevels.Count)
        {
            case > 1:
                return false;
            case 1:
                factor.UserPoint = userLevels[0] switch
                {
                    1 => 0.5M,
                    2 => 1M,
                    3 => 1.2M,
                    4 => 1.3M,
                    _ => 0.5M
                };
                break;
        }

        var bigClient =
            await bigClientRepository.GetCacheValueAsync(
                new BigClientKey(commission.CustomerId, CaseDirection.II, true), cancellationToken: cancellationToken);
        
        var isBigClient = bigClient is not null;
        commission.BigClient = isBigClient.GetBooleanChineseDescription();
        
        if (bigClient is not null)
        {
            factor.BigClientPoint = bigClient.RealPoint;
        }

        var suggestionProcPointConfigs = await freeSql.Select<SuggestionProcPointConfig>()
            .Where(config => config.TrademarkBonusConfigId == matchConfig.Id).Where(config => config.IsEnable == true)
            .ToListAsync(cancellationToken);

        if (suggestionProcPointConfigs.Count != 0 && !await sender.Send(new SuggestionProcMatchCommand(commission, suggestionProcPointConfigs), cancellationToken))
        {
            factor.ProcPoint = 0;
            return false;
        }
        

        commission.ProcPoint = factor.CalculateDomesticTrademarkPoint();
        
        return true;
    }
}