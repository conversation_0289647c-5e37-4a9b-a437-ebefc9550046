﻿namespace iPlatformExtension.Commission.Application.Models.Trademark;

/// <summary>
/// 案件任务提成预览预览
/// </summary>
public sealed class CaseProcDisplayInfo
{
    /// <summary>
    /// 任务id
    /// </summary>
    public string ProcId { get; set; } = string.Empty;

    /// <summary>
    /// 案件id
    /// </summary>
    public string CaseId { get; set; } = string.Empty;

    /// <summary>
    /// 我方文号
    /// </summary>
    public string Volume { get; set; } = string.Empty;

    /// <summary>
    /// 申请号
    /// </summary>
    public string AppNo { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称id
    /// </summary>
    public string CtrlProcId { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    public string CtrProcName { get; set; } = string.Empty;

    /// <summary>
    /// 客户id
    /// </summary>
    public string CustomerId { get; set; } = string.Empty;

    /// <summary>
    /// 客户名称
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string ProcStatusId { get; set; } = string.Empty;

    /// <summary>
    /// 任务状态
    /// </summary>
    public string ProcStatusText { get; set; } = string.Empty;

    /// <summary>
    /// 承办人id
    /// </summary>
    public string UndertakerId { get; set; } = string.Empty;

    /// <summary>
    /// 任务承办人
    /// </summary>
    public string UndertakerName { get; set; } = string.Empty;

    /// <summary>
    /// 注册号
    /// </summary>
    public string RegisterNo { get; set; } = string.Empty;

    /// <summary>
    /// 任务编号
    /// </summary>
    public string ProcNo { get; set; } = string.Empty;

    /// <summary>
    /// 商标名称
    /// </summary>
    public string CaseName { get; set; } = string.Empty;

    /// <summary>
    /// 是否大客户
    /// </summary>
    public bool IsBigClient { get; set; }

    /// <summary>
    /// 任务标识id
    /// </summary>
    public string CtrlProcMark { get; set; } = string.Empty;

    /// <summary>
    /// 任务标识
    /// </summary>
    public string ProcMarkText { get; set; } = string.Empty;

    /// <summary>
    /// 裁定结果
    /// </summary>
    public string RulingResult { get; set; } = string.Empty;

    /// <summary>
    /// 是否情势变更
    /// </summary>
    public bool SituationChanged { get; set; }
}