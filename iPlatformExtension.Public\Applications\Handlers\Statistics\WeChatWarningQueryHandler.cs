﻿using System.Text;
using AutoMapper;
using iPlatformExtension.Common.Clients.BladeCommon;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Public.Applications.Models.Statistics;
using iPlatformExtension.Public.Applications.Queries.Statistics;
using MediatR;
using Microsoft.Extensions.ObjectPool;
using MongoDB.Bson;

namespace iPlatformExtension.Public.Applications.Handlers.Statistics
{
    /// <summary>
    /// 3.1微信一天通知
    /// </summary>
    internal sealed class WeChatWarningQueryHandler(
        BladeCommonClient bladeCommonClient,
        IMediator mediator,
        IMapper mapper,
        ObjectPool<StringBuilder> stringBuilderPool,
        ILogger<WeChatWarningQuery> logger,
        IHostEnvironment iHostEnvironment
    ) : IRequestHandler<WeChatWarningQuery, IEnumerable<WeChatWarningDto>>
    {
        private int _count;

        public async Task<IEnumerable<WeChatWarningDto>> Handle(
            WeChatWarningQuery request,
            CancellationToken cancellationToken
        )
        {
            var today = DateTime.Now.Date;
            var intFirstDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.IntFirstDate, 2),
                cancellationToken
            );
            var cusFirstDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.CusFirstDate, 2),
                cancellationToken
            );
            var intFinishDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.IntFinishDate, 2),
                cancellationToken
            );
            var cusFinishDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.CusFinishDate, 2),
                cancellationToken
            );
            var legalDueDateWarningDtos = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.LegalDueDate, 2),
                cancellationToken
            );
            var intFirstDateWarning = mapper.Map<List<IntFirstDateWarning>>(
                intFirstDateWarningDtos
            );
            var cusFirstDateWarning = mapper.Map<List<CusFirstDateWarning>>(
                cusFirstDateWarningDtos
            );
            var intFinishDateWarning = mapper.Map<List<IntFinishDateWarning>>(
                intFinishDateWarningDtos
            );
            var cusFinishDateWarning = mapper.Map<List<CusFinishDateWarning>>(
                cusFinishDateWarningDtos
            );
            var legalDueDateWarning = mapper.Map<List<LegalDueDateWarning>>(
                legalDueDateWarningDtos
            );

            var underTakeUserList = intFirstDateWarning
                .Select(it => new
                {
                    it.UndertakeUserId,
                    it.UndertakeUserName,
                    it.UserAccount,
                })
                .ToList();
            underTakeUserList.AddRange(
                cusFirstDateWarning.Select(it => new
                {
                    it.UndertakeUserId,
                    it.UndertakeUserName,
                    it.UserAccount,
                })
            );
            underTakeUserList.AddRange(
                intFinishDateWarning.Select(it => new
                {
                    it.UndertakeUserId,
                    it.UndertakeUserName,
                    it.UserAccount,
                })
            );
            underTakeUserList.AddRange(
                cusFinishDateWarning.Select(it => new
                {
                    it.UndertakeUserId,
                    it.UndertakeUserName,
                    it.UserAccount,
                })
            );
            underTakeUserList.AddRange(
                legalDueDateWarning.Select(it => new
                {
                    it.UndertakeUserId,
                    it.UndertakeUserName,
                    it.UserAccount,
                })
            );
            if (request.UserId is not null)
            {
                underTakeUserList = underTakeUserList
                    .Where(it => request.UserId.Contains(it.UndertakeUserId))
                    .ToList();
            }
            foreach (var underTakeUser in underTakeUserList.Distinct())
            {
                var intFirstDateWarnings = intFirstDateWarning
                    .Where(it => it.UndertakeUserId == underTakeUser.UndertakeUserId)
                    .ToList();
                var cusFirstDateWarnings = cusFirstDateWarning
                    .Where(it =>
                        it.UndertakeUserId == underTakeUser.UndertakeUserId
                        && it.CusFirstDate >= today
                    )
                    .ToList();
                var intFinishDateWarnings = intFinishDateWarning
                    .Where(it =>
                        it.UndertakeUserId == underTakeUser.UndertakeUserId
                        && it.IntFinishDate >= today
                    )
                    .ToList();
                var cusFinishDateWarnings = cusFinishDateWarning
                    .Where(it =>
                        it.UndertakeUserId == underTakeUser.UndertakeUserId
                        && it.CusFinishDate >= today
                    )
                    .ToList();
                var legalDueDateWarnings = legalDueDateWarning
                    .Where(it =>
                        it.UndertakeUserId == underTakeUser.UndertakeUserId
                        && it.LegalDueDate >= today
                    )
                    .ToList();
                var stringBuilder = stringBuilderPool.Get();
                var counts =
                    intFirstDateWarnings.Count
                    + cusFirstDateWarnings.Count
                    + intFinishDateWarnings.Count
                    + cusFinishDateWarnings.Count
                    + legalDueDateWarnings.Count;
                stringBuilder.Append(
                    $"尊敬的{underTakeUser.UndertakeUserName}:您好!您有{counts}条任务即将在今日或明日到期,请您尽快处理,谢谢！\r\n"
                );
                intFirstDateWarnings.ForEach(it =>
                    StringCombine(
                        stringBuilder,
                        it.Volume,
                        it.CtrlProcZhCn,
                        it.CtrlProcPropertyValue,
                        it.SubProcStatusValue,
                        SysEnum.ReportType.IntFirstDate,
                        it.IntFirstDate.Value
                    )
                );
                cusFirstDateWarnings.ForEach(it =>
                    StringCombine(
                        stringBuilder,
                        it.Volume,
                        it.CtrlProcZhCn,
                        it.CtrlProcPropertyValue,
                        it.SubProcStatusValue,
                        SysEnum.ReportType.CusFirstDate,
                        it.CusFirstDate.Value
                    )
                );
                intFinishDateWarnings.ForEach(it =>
                    StringCombine(
                        stringBuilder,
                        it.Volume,
                        it.CtrlProcZhCn,
                        it.CtrlProcPropertyValue,
                        it.SubProcStatusValue,
                        SysEnum.ReportType.IntFinishDate,
                        it.IntFinishDate.Value
                    )
                );
                cusFinishDateWarnings.ForEach(it =>
                    StringCombine(
                        stringBuilder,
                        it.Volume,
                        it.CtrlProcZhCn,
                        it.CtrlProcPropertyValue,
                        it.SubProcStatusValue,
                        SysEnum.ReportType.CusFinishDate,
                        it.CusFinishDate.Value
                    )
                );
                legalDueDateWarnings.ForEach(it =>
                    StringCombine(
                        stringBuilder,
                        it.Volume,
                        it.CtrlProcZhCn,
                        it.CtrlProcPropertyValue,
                        it.SubProcStatusValue,
                        SysEnum.ReportType.LegalDueDate,
                        it.LegalDueDate.Value
                    )
                );
                stringBuilder.Append(
                    $"【本人超期任务提醒】\r\n您有以下案件任务已经超期，请您尽快处理，谢谢！\r\n"
                );
                var cusFirstDateOverWarning = cusFirstDateWarning.Count(it =>
                    it.UndertakeUserId == underTakeUser.UndertakeUserId && it.CusFirstDate < today
                );
                var intFirstDateOverWarning = intFirstDateWarning.Count(it =>
                    it.UndertakeUserId == underTakeUser.UndertakeUserId && it.IntFirstDate < today
                );
                var cusFinishDateOverWarnings = cusFinishDateWarning.Count(it =>
                    it.UndertakeUserId == underTakeUser.UndertakeUserId && it.CusFinishDate < today
                );
                var intFinishDateOverWarnings = intFinishDateWarning.Count(it =>
                    it.UndertakeUserId == underTakeUser.UndertakeUserId && it.IntFinishDate < today
                );
                var legalDueDateOverWarnings = legalDueDateWarning.Count(it =>
                    it.UndertakeUserId == underTakeUser.UndertakeUserId && it.LegalDueDate < today
                );
                if (
                    counts
                        + cusFirstDateOverWarning
                        + intFirstDateOverWarning
                        + cusFinishDateOverWarnings
                        + intFinishDateOverWarnings
                        + legalDueDateOverWarnings
                    == 0
                )
                    continue;
                stringBuilder.Append($"初稿期限(外)：{cusFirstDateOverWarning} ");
                stringBuilder.Append($"初稿期限(内)：{intFirstDateOverWarning} ");
                stringBuilder.Append($"定稿期限(外)：{cusFinishDateOverWarnings} ");
                stringBuilder.Append($"定稿期限(内)：{intFinishDateOverWarnings} ");
                stringBuilder.Append($"官方期限：{legalDueDateOverWarnings} ");
                var enterpriseWechatNotification = new EnterpriseWechatNotification
                {
                    Title = "【本人期限前1天/当天任务提醒】",
                    //UserAccount = "H04788",
                    UserAccount = underTakeUser?.UserAccount,
                    Content = stringBuilder.ToString(),
                    Url = "https://ipr.aciplaw.com/openDefaultBrowser.html",
                    ButtonText = "点击跳转",
                };
                if (!iHostEnvironment.IsProduction())
                {
                    enterpriseWechatNotification.UserAccount = "H05091";
                }
                var sendSimpleNotificationAsync =
                    await bladeCommonClient.SendTextCardNotificationAsync(
                        enterpriseWechatNotification
                    );

                _count = 0;
                stringBuilderPool.Return(stringBuilder);
                if (!sendSimpleNotificationAsync!.Success)
                {
                    logger.LogError(
                        $"MessageId:{sendSimpleNotificationAsync.Data?.MessageId} Para:{enterpriseWechatNotification.ToJson()} Result:{sendSimpleNotificationAsync.Data?.Message}"
                    );
                }
            }
            return new List<WeChatWarningDto>();
        }

        private void StringCombine(
            StringBuilder builder,
            string volume,
            string? ctrlProcZhCn,
            string? CtrlProcPropertyValue,
            string? subProcStatusValue,
            SysEnum.ReportType type,
            DateTime date
        )
        {
            if (_count < 2)
            {
                builder.Append(
                    $"{volume}  {ctrlProcZhCn}{(!string.IsNullOrWhiteSpace(CtrlProcPropertyValue) ? "(" + CtrlProcPropertyValue + ")" : " ")}  {subProcStatusValue ?? "  "}  {type.GetDescription()}：{date.ToString("yyyy-MM-dd")}  {(_count == 1 ? "..." : "")}\r\n"
                );
                _count++;
            }
        }
    }
}
