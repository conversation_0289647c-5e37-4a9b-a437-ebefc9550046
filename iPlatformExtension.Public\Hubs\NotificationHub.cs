﻿using iPlatformExtension.Common.Authentication.BladeAuth;
using iPlatformExtension.Common.Authentication.PlatformAuth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;

namespace iPlatformExtension.Public.Hubs;

/// <summary>
/// 通知集线器
/// </summary>
[Authorize(AuthenticationSchemes = $"{BladeAuthOptions.SchemeName},{PlatformAuthOptions.SchemeName}")]
public sealed class NotificationHub : Hub {
    public override async Task OnConnectedAsync()
    {
        var userId = Context.UserIdentifier; // 获取用户的唯一标识
        Console.WriteLine($"userid:{userId}连接...");
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception exception)
    {
        var userId = Context.UserIdentifier; // 获取用户的唯一标识
        Console.WriteLine($"userid:{userId}失去连接...{exception?.Message}");
        await base.OnDisconnectedAsync(exception);
    }
}