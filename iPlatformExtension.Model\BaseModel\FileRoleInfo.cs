using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "file_role_info", DisableSyncStructure = true)]
	public partial class FileRoleInfo {

		[ Column(Name = "file_id", StringLength = 50, IsNullable = false)]
		public string FileId { get; set; }

		[ Column(Name = "is_down")]
		public bool IsDown { get; set; } = true;

		[ Column(Name = "is_view")]
		public bool IsView { get; set; } = true;

	}

}
