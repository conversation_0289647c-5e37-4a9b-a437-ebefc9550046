﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkRegistration;

internal sealed class BuildRegistrationInfoHandler(
    IDeliveryOtherInfoRepository otherInfoRepository, 
    IHttpContextAccessor httpContextAccessor) : 
    IMatchTrademarkProcCommandHandler<BuildOtherInfoCommand>
{
    async Task IMatchNotificationHandler<BuildOtherInfoCommand>.HandleAsync(BuildOtherInfoCommand notification, CancellationToken cancellationToken)
    {
        var procInfo = notification.ProcInfo;
        var operatorId = (httpContextAccessor.HttpContext?.User).GetUserId();

        var otherInfo = await otherInfoRepository.GetAsync(procInfo.ProcId, cancellationToken) ?? new DeliOtherInfo()
        {
            ProcId = procInfo.ProcId,
            CreationTime = DateTime.Now,
            Creator = operatorId
        };

        otherInfo.OfficialName = procInfo.OfficialName;
        otherInfo.TrademarkNiceClasses = procInfo.CaseInfo.TrademarkClass;
        otherInfo.Updater = operatorId;
        otherInfo.UpdateTime = DateTime.Now;

        await otherInfoRepository.InsertOrUpdateAsync(otherInfo, cancellationToken);
    }

    string IMatchTrademarkProcCommandHandler<BuildOtherInfoCommand>.CtrlProcId => CtrlProcIds.TrademarkRegistration;

    IEnumerable<string> IMatchTrademarkProcCommandHandler<BuildOtherInfoCommand>.CaseDirections => [CaseDirection.II];
}