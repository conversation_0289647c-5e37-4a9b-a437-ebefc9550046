using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bill_bonus_tm_month_info", DisableSyncStructure = true)]
	public partial class BillBonusTmMonthInfo {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "amount", DbType = "money")]
		public decimal? Amount { get; set; }

		[ Column(Name = "bonus_amount", DbType = "money")]
		public decimal? BonusAmount { get; set; }

		[ Column(Name = "bonus_point", DbType = "money")]
		public decimal? BonusPoint { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "draft_num")]
		public int? DraftNum { get; set; }

		[ Column(Name = "examine_num")]
		public int? ExamineNum { get; set; }

		[ Column(Name = "f_bonus_amount", DbType = "money")]
		public decimal? FBonusAmount { get; set; }

		[ Column(Name = "f_bonus_point", DbType = "money")]
		public decimal? FBonusPoint { get; set; }

		[ Column(Name = "f_real_point", DbType = "money")]
		public decimal? FRealPoint { get; set; }

		[ Column(Name = "flow_status_code", StringLength = 50)]
		public string FlowStatusCode { get; set; }

		[ Column(Name = "initial_point", DbType = "money")]
		public decimal? InitialPoint { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "month")]
		public int? Month { get; set; }

		[ Column(Name = "num_new")]
		public int? NumNew { get; set; }

		[ Column(Name = "num_oa")]
		public int? NumOa { get; set; }

		[ Column(Name = "num_other")]
		public int? NumOther { get; set; }

		[ Column(Name = "obj_id", StringLength = 50, IsNullable = false)]
		public string ObjId { get; set; }

		[ Column(Name = "obj_type", StringLength = 50, IsNullable = false)]
		public string ObjType { get; set; }

		[ Column(Name = "other_amount", DbType = "money")]
		public decimal? OtherAmount { get; set; }

		[ Column(Name = "other_num")]
		public int? OtherNum { get; set; }

		[ Column(Name = "real_point", DbType = "money")]
		public decimal? RealPoint { get; set; }

		[ Column(Name = "real_point_pure", DbType = "money")]
		public decimal? RealPointPure { get; set; }

		[ Column(Name = "real_point_pure_add", DbType = "money")]
		public decimal? RealPointPureAdd { get; set; }

		[ Column(Name = "real_point_pure_balance", DbType = "money")]
		public decimal? RealPointPureBalance { get; set; }

		[ Column(Name = "real_point_pure_normal", DbType = "money")]
		public decimal? RealPointPureNormal { get; set; }

		[ Column(Name = "real_point_pure_show", DbType = "money")]
		public decimal? RealPointPureShow { get; set; }

		[ Column(Name = "remark", StringLength = 4000, IsNullable = false)]
		public string Remark { get; set; }

		[ Column(Name = "translate_num")]
		public int? TranslateNum { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "year")]
		public int? Year { get; set; }

	}

}
