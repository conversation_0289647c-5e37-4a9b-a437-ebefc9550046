﻿using System.Collections.Immutable;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Nice;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Nice;

internal sealed class InsertCaseNiceCommandHandler(
    ICaseNiceCategoryRepository caseNiceCategoryRepository,
    ICaseInfoRepository caseInfoRepository)
    : IRequestHandler<InsertCaseNiceCommand>
{
    public async Task Handle(InsertCaseNiceCommand request, CancellationToken cancellationToken)
    {
        if (!await caseNiceCategoryRepository.Orm.Select<CaseInfo>().WithLock()
                .AnyAsync(info => info.Id == request.CaseId, cancellationToken))
        {
            throw new NotFoundException(request.CaseId, "案件");
        }

        var caseCategories = request.StandardCategories
            .Select(dto => new CaseTrademarkNiceCategory()
            {
                Id = 0,
                CaseId = request.CaseId,
                CategoryName = dto.CategoryName,
                CategoryNumber = dto.CategoryNumber,
                CategoryId = dto.CategoryId,
                CreationTime = DateTime.Now,
                GrandId = dto.GrandCategoryId,
                GrandNumber = dto.GrandNumber,
                GrandName = dto.GrandName,
                ParentId = dto.ParentCategoryId,
                ParentNumber = dto.ParentNumber,
                ParentName = dto.ParentName,
                UpdateTime = DateTime.Now,
                IsStandard = true,
                Order = dto.Order
            }).ToList();
        
        
        if (request.CustomCategories.Any())
        {
            var customCategoryIds = request.CustomCategories.Select(dto => dto.CategoryId).ToImmutableHashSet();
            var customCategoryNumbers = await caseNiceCategoryRepository.Orm.Select<BasTrademarkItemsLevel>()
                .From<BasTrademarkItems>().InnerJoin((level, items) => level.CurId == items.CurId)
                .Where((level, items) => customCategoryIds.Contains(level.CurId))
                .Where((level, items) => level.VersionId == request.VersionId)
                .Where((level, items) => level.Level == 0)
                .ToDictionaryAsync(level => level.CurId, level => level.Lid, cancellationToken);

            caseCategories.AddRange(request.CustomCategories.Select(customCategory => new CaseTrademarkNiceCategory()
            {
                CaseId = request.CaseId,
                CategoryName = customCategory.Description,
                GrandNumber = customCategoryNumbers.TryGetValue(customCategory.CategoryId, out var categoryNumber)
                    ? categoryNumber
                    : throw new NotFoundException(customCategory.CategoryId, "尼斯分类Id"),
                IsStandard = false,
                CreationTime = DateTime.Now,
                UpdateTime = DateTime.Now,
                Order = customCategory.Order
            }));
        }
        
        await caseNiceCategoryRepository.InsertAsync(caseCategories, cancellationToken);
        
        var grandNumbers = new HashSet<int>(request.GrandNumbers.Select(number => Convert.ToInt32(number)));
        grandNumbers.UnionWith(caseCategories.Select(category => Convert.ToInt32(category.GrandNumber)));
        
        var caseInfo = await caseInfoRepository.GetAsync(request.CaseId, cancellationToken);
        caseInfo.TrademarkClass = string.Join(';', grandNumbers.Order());

        await caseInfoRepository.UpdateAsync(caseInfo, cancellationToken);
    }
}