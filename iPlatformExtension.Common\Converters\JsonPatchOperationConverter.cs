﻿using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using iPlatformExtension.Common.Extensions;
using Microsoft.AspNetCore.JsonPatch.Internal;
using Microsoft.AspNetCore.JsonPatch.Operations;

namespace iPlatformExtension.Common.Converters;

public class JsonPatchOperationConverter<T> : JsonConverter<Operation<T>> where T : class
{
    protected readonly byte[] _opName = nameof(Operation<T>.op).GetBytes(Encoding.UTF8);

    protected readonly byte[] _pathName =
        nameof(Operation<T>.path).GetBytes(Encoding.UTF8);

    protected readonly byte[] _fromName =
        nameof(Operation<T>.from).GetBytes(Encoding.UTF8);

    protected readonly byte[] _valueName =
        nameof(Operation<T>.value).GetBytes(Encoding.UTF8);
    
    public override Operation<T>? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var operation = new Operation<T>();

        while (reader.Read() && reader.TokenType != JsonTokenType.EndObject)
        {
            if (reader.TokenType != JsonTokenType.PropertyName)
            {
                continue;
            }

            if (reader.ValueTextEquals(_opName))
            {
                reader.Read();
                operation.op = reader.GetString();
            }
            else if (reader.ValueTextEquals(_pathName))
            {
                reader.Read();
                operation.path = reader.GetString();
            }
            else if (reader.ValueTextEquals(_fromName))
            {
                reader.Read();
                operation.from = reader.GetString();
            }
            else if (reader.ValueTextEquals(_valueName))
            {
                reader.Read();
                operation.value = ReadValue(ref reader, operation.from, operation.path, options);
            }
        }
        
        return operation;
    }

    public override void Write(Utf8JsonWriter writer, Operation<T> value, JsonSerializerOptions options)
    {
        writer.WriteStartObject();
        writer.WriteString(_opName, value.op);
        writer.WriteString(_pathName, value.path);
        writer.WriteString(_fromName, value.from);
        
        writer.WritePropertyName(_valueName);
        JsonSerializer.Serialize(writer, value.value, options);
        
        writer.WriteEndObject();
    }

    /// <summary>
    /// 读取JsonPatch的value值
    /// </summary>
    /// <param name="reader">读取器</param>
    /// <param name="from">from的值</param>
    /// <param name="path">path的值</param>
    /// <param name="serializerOptions">序列化选项</param>
    /// <returns>value的值</returns>
    protected virtual object? ReadValue(ref Utf8JsonReader reader, string? from, string? path, JsonSerializerOptions serializerOptions)
    {
        var parsedPath = new ParsedPath(path);
        var jsonTypeInfo = serializerOptions.GetTypeInfo(typeof(T));
        var propertyType = default(Type);
        
        foreach (var jsonPropertyName in parsedPath.Segments)
        {
            var name = jsonPropertyName;
            var jsonPropertyInfo = jsonTypeInfo.Properties.FirstOrDefault(info =>
                info.Name.JsonPropertyName(serializerOptions) == name);

            if (jsonPropertyInfo is null) continue;
            
            propertyType = jsonPropertyInfo.PropertyType;
            jsonTypeInfo = serializerOptions.GetTypeInfo(propertyType);
        }

        return propertyType != default ? JsonSerializer.Deserialize(ref reader, jsonTypeInfo) : null;
    }
}