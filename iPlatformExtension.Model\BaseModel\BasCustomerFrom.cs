using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_customer_from", DisableSyncStructure = true)]
	public partial class BasCustomerFrom {

		/// <summary>
		/// 客户来源ID
		/// </summary>
		[ Column(Name = "from_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string FromId { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		/// <summary>
		/// 客户来源英文
		/// </summary>
		[ Column(Name = "from_name_en_us", StringLength = 200)]
		public string FromNameEnUs { get; set; }

		/// <summary>
		/// 客户来源日文
		/// </summary>
		[ Column(Name = "from_name_ja_jp", StringLength = 200)]
		public string FromNameJaJp { get; set; }

		/// <summary>
		/// 客户来源中文
		/// </summary>
		[ Column(Name = "from_name_zh_cn", StringLength = 200)]
		public string FromNameZhCn { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 更新时间
		/// </summary>
		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		/// <summary>
		/// 更新人
		/// </summary>
		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
