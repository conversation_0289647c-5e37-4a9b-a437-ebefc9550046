﻿namespace iPlatformExtension.MailCenter.Applications.Models.Receive;

/// <summary>
/// 获取关联规则列表
/// </summary>
/// <param name="ConfigId">规则id</param>
/// <param name="ConfigName">规则名称</param>
/// <param name="ConfigRemark">办理意见</param>
/// <param name="ConfigType">分拣类型,finish:分拣到承办人,allot:移入待分拣,ignore:忽略</param>
/// <param name="CreateTime">创建时间</param>
/// <param name="CreateUser">创建用户</param>
/// <param name="HandUser">分拣人</param>
/// <param name="HandUserType">处理人类型</param>
/// <param name="HostId">邮箱id</param>
/// <param name="Account">邮箱</param>
/// <param name="IgnoreUser">忽略人</param>
/// <param name="IsEnabled">是否生效</param>
/// <param name="ReadUser">阅读人</param>
/// <param name="ReadUserType">阅读人类型</param>
/// <param name="Remark">备注</param>
/// <param name="RuleNumber">规则编号</param>
/// <param name="UndertakeUser">承办人</param>
/// <param name="UpdateTime">更新时间</param>
/// <param name="UpdateUser">更新用户</param>
public record GetRelatedRuleListDto(
    string ConfigId,
    string ConfigName,
    string ConfigRemark,
    string ConfigType,
    DateTime? CreateTime,
    string CreateUser,
    string HandUserId,
    string HandUserType,
    string HostId,
    string IgnoreUserId,
    int? IsEnabled,
    string ReadUserId,
    string ReadUserType,
    string Remark,
    string RuleNumber,
    string UndertakeUserId,
    DateTime? UpdateTime,
    string UpdateUserId)
{
    public object HandUser { get; set; }
    public object IgnoreUser { get; set; }
    public object ReadUser { get; set; }
    public object UndertakeUser { get; set; }
    public object UpdateUser { get; set; }
    public object Account { get; set; }
};

