﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Team
{
    /// <summary>
    /// 获取团队角色
    /// </summary>
    public class TeamRoleQueryHandler : IRequestHandler<TeamRoleQuery, IEnumerable<TeamRoleDto>>
    {
        private readonly IFreeSql _freeSql;

        public TeamRoleQueryHandler(IFreeSql freeSql)
        {
            _freeSql = freeSql;
        }

        public async Task<IEnumerable<TeamRoleDto>> Handle(TeamRoleQuery request, CancellationToken cancellationToken)
        {
            return _freeSql.Select<SysRoleInfo>().Where(it => it.RoleType == RoleType.TrademarkOperation.GetHashCode())
                 .ToList(it => new TeamRoleDto(it.RoleId, it.RoleName));
        }
    }
}

