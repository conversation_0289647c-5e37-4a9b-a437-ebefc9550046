﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Nice;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Nice;

internal sealed class SaveCaseNiceCommandHandler(IMediator mediator) : IRequestHandler<SaveCaseNiceCommand>
{
    public async Task Handle(SaveCaseNiceCommand request, CancellationToken cancellationToken)
    {
        await mediator.Send(new DeleteCaseNiceCommand(request.CaseId), cancellationToken);
        
        await mediator.Send(
            new InsertCaseNiceCommand(request.VersionId, request.CaseId, request.StandardCategories,
                request.CustomCategories, request.GrandNumbers),
            cancellationToken);
    }
}