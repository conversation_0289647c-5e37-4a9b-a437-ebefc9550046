﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.MailCenter.Infrastructure;
using iPlatformExtension.MailCenter.Infrastructure.Extension;
using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    public class GetFlowPrivateMessageHandler(ILogger<GetFlowPrivateMessageHandler> logger, IFreeSql<MailCenterFreeSql> freeSql,
        IConfiguration configuration, IMediator mediator, MailTool mailTool, IHttpContextAccessor httpContextAccessor
        ) : IRequestHandler<GetFlowPrivateQuery, List<FlowAnalyseDto>>
    {
        public async Task<List<FlowAnalyseDto>> Handle(GetFlowPrivateQuery request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var lst = mailTool.TryGetTodoList();
            var notClassified = freeSql.Select<MailReceive, FlowRecord, FlowPrivateList>()
                .InnerJoin(o => o.t1.MailId == o.t2.MailId)
                .LeftJoin(o => o.t1.MailId == o.t3.MailId)
                .Where(o => o.t2.AuditUser == userId && o.t2.IsCurrent == MailFlowActionStatus.Enable.GetHashCode() && o.t3.PrivateId == null).Count();

            if (lst.Any())
            {
                var res = lst.Where(o => o.UserId == userId).ToList();
                res.Add(new FlowAnalyseDto { Count = (int)notClassified, Title = "", Type = "FlowPrivate".ToString(), UserId = userId });//统计未分类
                return res;
            }
            else
            {
                lst =
                [
                    new FlowAnalyseDto { Count = (int)notClassified, Title = "", Type = "FlowPrivate".ToString(), UserId = userId },
                ];
                return lst;
            }
        }
    }
}
