using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "rp_tm_win_rate", DisableSyncStructure = true)]
	public partial class RpTmWinRate {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "business_type", StringLength = 50)]
		public string BusinessType { get; set; }

		[ Column(Name = "dept_id", StringLength = 50)]
		public string DeptId { get; set; }

		[ Column(Name = "dept_name", StringLength = 500)]
		public string DeptName { get; set; }

		[ Column(Name = "month", StringLength = 5)]
		public string Month { get; set; }

		[ Column(Name = "num", DbType = "money")]
		public decimal? Num { get; set; }

		[ Column(Name = "quarter", StringLength = 5)]
		public string Quarter { get; set; }

		[ Column(Name = "result_remark", StringLength = 50)]
		public string ResultRemark { get; set; }

		[ Column(Name = "track_user", StringLength = 50)]
		public string TrackUser { get; set; }

		[ Column(Name = "user_district", StringLength = 50)]
		public string UserDistrict { get; set; }

		[ Column(Name = "year", StringLength = 5)]
		public string Year { get; set; }

	}

}
