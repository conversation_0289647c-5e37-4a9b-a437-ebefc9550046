﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Models.Company;
using iPlatformExtension.Public.Applications.Queries.Company;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.Company
{
    /// <summary>
    /// 代理机构搜索
    /// </summary>
    internal sealed class SearchAgencyQueryHandler(IFreeSql freeSql)
        : IRequestHandler<SearchAgencyQuery, IEnumerable<SearchAgencyDto>>
    {
        public async Task<IEnumerable<SearchAgencyDto>> Handle(SearchAgencyQuery request, CancellationToken cancellationToken)
        {
            var dbSelect = freeSql.Select<BasAgency>()
                .Where(it=>it.IsEnabled == true)
                .WhereIf(request.Name != null,
                    it => it.AgencyNameCn.Contains(request.Name) || it.AgencyNameEn.Contains(request.Name));
            if (request.PageIndex is not null && request.PageSize is not null)
            {
                dbSelect = dbSelect.Page(request.PageIndex.Value, request.PageSize.Value).Count(out var totalCount);
                var result = await dbSelect.WithLock().ToListAsync(it => new SearchAgencyDto(it.AgencyId, it.AgencyNameCn, it.AgencyNameEn, it.Tel, it.Contacter,it.AddressCn,it.AddressEn), cancellationToken);
                return new PageResult<SearchAgencyDto>()
                {
                    Data = result,
                    Page = request.PageIndex.Value,
                    PageSize = request.PageSize.Value,
                    Total = totalCount
                };
            }
            return await dbSelect.WithLock().ToListAsync(it => new SearchAgencyDto(it.AgencyId, it.AgencyNameCn, it.AgencyNameEn, it.Tel, it.Contacter, it.AddressCn, it.AddressEn), cancellationToken);
        }
    }
}

