﻿using iPlatformExtension.Model.Dto.Flow;
using iPlatformExtension.Public.Applications.Queries.Flow;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 消息控制器
/// </summary>
[Route("[controller]")]
[ApiController]
public sealed class MessageController : ControllerBase
{
    private readonly IMediator _mediator;
        
    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="mediator"></param>
    public MessageController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Flow发送消息
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    [HttpPost("SendMessage")]
    public async Task<bool> SendMessage([FromBody] SignalRMessage<FlowAnalyseDto[]> message)
    {
        return await _mediator.Send(new FlowAnalyseQuery() { MessageJsonData = message.MessageData });
    }

}