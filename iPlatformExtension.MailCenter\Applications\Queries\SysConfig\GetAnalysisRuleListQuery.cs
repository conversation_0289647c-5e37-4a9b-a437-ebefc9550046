﻿using iPlatformExtension.MailCenter.Applications.Models;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.SysConfig;

/// <summary>
/// 获取收件解析规则列表
/// </summary>
/// <param name="Search">搜索/规则名称(匹配)规则编号,应用邮箱(模糊)</param>
public record GetAnalysisRuleListQuery(string? Search)
    : PageModel,
        IRequest<IEnumerable<GetAnalysisRuleListDto>>;
