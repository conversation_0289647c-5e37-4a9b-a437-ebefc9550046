﻿using Grpc.Core.Utils;
using iPlatformExtension.Commission.Application.Commands.Patent;
using iPlatformExtension.Commission.Application.Models.Patent;
using iPlatformExtension.Commission.Grpc;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Patent;

internal sealed class PushPatentCommissionWeightHandler(
    ISender sender,
    IFreeSql freeSql,
    CommissionService.CommissionServiceClient client) : IRequestHandler<PushPatentCommissionWeightCommand>
{
    public async Task Handle(PushPatentCommissionWeightCommand request, CancellationToken cancellationToken)
    {
        var (year, month) = request;
        var patentBonusList = await freeSql.Select<BillBonusCaseList, CaseProcInfo, CaseInfo>().WithLock()
            .InnerJoin((list, proInfo, caseInfo) => list.ProcId == proInfo.ProcId)
            .InnerJoin((list, procInfo, caseInfo) => list.CaseId == caseInfo.Id)
            .Where((list, procInfo, caseInfo) => list.Pushed == false)
            .Where((list, procInfo, caseInfo) => list.IsEnabled == true)
            .Where((list, procInfo, caseInfo) => list.Year == year.ToString())
            .Where((list, procInfo, caseInfo) => list.Month == month.ToString())
            .Where((list, procInfo, caseInfo) => list.CaseTypeId == CaseType.Patent)
            .ToListAsync((list, procInfo, caseInfo) => new KeyValuePair<CommissionWeightKey, BillBonusCaseList>(
                new CommissionWeightKey(
                    list.Year,
                    list.Month,
                    list.ProcId,
                    list.CtrlProcId,
                    caseInfo.CaseName,
                    list.CaseDirection,
                    list.CaseTypeId,
                    procInfo.UndertakeUserId ?? string.Empty,
                    caseInfo.Volume,
                    caseInfo.CustomerId),
                list), cancellationToken);

        if (patentBonusList.Count <= 0)
        {
            return;
        }

        var context = client.PushCommissionWeights(cancellationToken: cancellationToken);
        var requestStream = context.RequestStream;
        var responseStream = context.ResponseStream;

        var responseTask =
            Task.Run(
                () => responseStream.ForEachAsync(result =>
                    sender.Send(new PushedPatentWeightResultCommand(result), cancellationToken)), cancellationToken);
        
        foreach (var bonusGroup in patentBonusList.GroupBy(pair => pair.Key, pair => pair.Value))
        {
            await sender.CreateStream(new CreateCommissionWeightsCommand(bonusGroup), cancellationToken)
                .ForEachAwaitWithCancellationAsync((commissionWeight, token) => requestStream.WriteAsync(commissionWeight, token), cancellationToken);
            
        }

        await requestStream.CompleteAsync();
        await responseTask;
    }
}