﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class BuildOtherInfoCommandHandler(
    IHttpContextAccessor httpContextAccessor, 
    IDeliveryOtherInfoRepository otherInfoRepository) : IMatchNotificationHandler<BuildOtherInfoCommand>
{
    
    public ValueTask<bool> MatchAsync(BuildOtherInfoCommand notification, CancellationToken cancellationToken)
    {
        return new ValueTask<bool>(
            StringComparer.OrdinalIgnoreCase.Equals(notification.CaseDirection, CaseDirection.II) &&
            !notification.CtrlProcId.IsAutomaticDeliveryCtrlProcId());
    }

    public async Task HandleAsync(BuildOtherInfoCommand notification, CancellationToken cancellationToken)
    {
        var procInfo = notification.ProcInfo;
        var operatorId = (httpContextAccessor.HttpContext?.User).GetUserId();
        
        var otherInfo = await otherInfoRepository.GetAsync(procInfo.ProcId, cancellationToken) ?? new DeliOtherInfo()
        {
            ProcId = procInfo.ProcId,
            CreationTime = DateTime.Now,
            Creator = operatorId
        };

        otherInfo.CtrlProcMark = procInfo.CtrlProcMark;
        otherInfo.Updater = operatorId;
        otherInfo.UpdateTime = DateTime.Now;
        
        await otherInfoRepository.InsertOrUpdateAsync(otherInfo, cancellationToken);
    }
}