﻿using Microsoft.AspNetCore.Authentication;

namespace iPlatformExtension.Common.Authentication.BladeAuth;

internal sealed class BladeAuthenticationResultFeature : IAuthenticateResultFeature
{
    public AuthenticateResult? AuthenticateResult { get; set; }

    public BladeAuthenticationResultFeature(AuthenticateResult? authenticateResult)
    {
        AuthenticateResult = authenticateResult;
    }
}