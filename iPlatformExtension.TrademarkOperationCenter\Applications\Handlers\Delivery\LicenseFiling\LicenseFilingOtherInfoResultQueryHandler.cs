﻿using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.LicenseFiling;

internal sealed class LicenseFilingOtherInfoResultQueryHandler(ISystemDictionaryRepository dictionaryRepository) 
    : OtherInfoResultNotificationHandlerBase(dictionaryRepository)
{
    protected override ValueTask<bool> MatchAsync(OtherInfoResultQuery query, CancellationToken cancellationToken)
    {
        return new ValueTask<bool>(StringComparer.OrdinalIgnoreCase.Equals(query.CtrlProcId, CtrlProcIds.LicenseFiling));
    }

    protected override async Task HandleAsync(OtherInfoResultQuery query, CancellationToken cancellationToken)
    {
        var dto = query.TrademarkDeliveryDto;
        var otherInfoSnapshot = dto.OtherInfoSnapshot;

        otherInfoSnapshot.LicenseType =
            (await _dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.LicenseType,
                otherInfoSnapshot.LicenseType)).Value;
        
        await base.HandleAsync(query, cancellationToken);
    }
}