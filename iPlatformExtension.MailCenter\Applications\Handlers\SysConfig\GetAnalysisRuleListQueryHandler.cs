﻿using System.ComponentModel;
using System.Linq.Expressions;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;
using MailHost = iPlatformExtension.Model.MailCenter.MailHost;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    /// <summary>
    /// 获取解析规则列表
    /// </summary>
    internal sealed class GetAnalysisRuleListQueryHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IUserInfoRepository userInfoRepository,
        IMailHostRepository mailHostRepository,
        IMediator mediator,
        IHttpContextAccessor content,
        IFreeSql<PlatformFreeSql> platformFreesql
    ) : IRequestHandler<GetAnalysisRuleListQuery, IEnumerable<GetAnalysisRuleListDto>>
    {
        public async Task<IEnumerable<GetAnalysisRuleListDto>> Handle(
            GetAnalysisRuleListQuery request,
            CancellationToken cancellationToken
        )
        {
            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            var hostIds = await mediator.Send(
                new GetUserAccessQuery(userId, "manager"),
                cancellationToken
            );
            //获取用户角色
            var mailSuperManager = await platformFreesql
                .Select<SysUserRole, SysRoleInfo>()
                .LeftJoin(it => it.t1.RoleId == it.t2.RoleId)
                .Where(it => it.t1.UserId == userId && it.t2.RoleCode == "MailSuperManager")
                .ToListAsync(cancellationToken);
            var getAnalysisRuleListSelect = freeSql
                .Select<MailConfig, MailHost>()
                .WithLock()
                .LeftJoin(it => it.t1.HostId == it.t2.HostId)
                .Where(it => it.t1.IsEnabled != SysEnum.Status.History.GetHashCode())
                .WhereIf(
                    request.Search is not null,
                    it =>
                        it.t1.RuleNumber == request.Search
                        || it.t2.Account.Contains(request.Search)
                        || it.t1.ConfigName.Contains(request.Search)
                )
                .Distinct()
                .OrderByDescending(it => it.t1.UpdateTime)
                .Page(request.PageIndex!.Value, request.PageSize!.Value);
            //如果用户是超级管理员，则不进行权限过滤
            if (!mailSuperManager.Any())
            {
                Expression<Func<MailConfig, MailHost, bool>> predicate = (it, it2) => false;
                foreach (var hostId in hostIds.HostId)
                {
                    predicate = predicate.Or((it, it2) => it.HostId.Contains(hostId));
                }
                getAnalysisRuleListSelect = getAnalysisRuleListSelect.Where(predicate);
            }

            var getAnalysisRuleListDtos = await getAnalysisRuleListSelect
                .Count(out var totalCount)
                .ToListAsync(
                    it => new GetAnalysisRuleListDto(
                        it.t1.ConfigId,
                        it.t1.ConfigName,
                        it.t1.RuleNumber,
                        it.t1.ConfigType,
                        it.t1.HandUser,
                        it.t1.HostId,
                        it.t1.ReadUser,
                        it.t1.IgnoreUser,
                        it.t1.IsEnabled!.Value,
                        it.t1.UndertakeUser,
                        it.t1.ConfigRemark,
                        it.t1.UpdateTime,
                        it.t1.UpdateUser,
                        it.t1.CreateTime,
                        it.t1.CreateUser
                    ),
                    cancellationToken
                );
            return new PageResult<GetAnalysisRuleListDto>()
            {
                Data = await getAnalysisRuleListDtos
                    .ToAsyncEnumerable()
                    .SelectAwait(async rule =>
                    {
                        if (rule.UndertakeUserTemp is not null)
                        {
                            ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                                userInfoRepository;
                            rule.UndertakeUser = new
                            {
                                CnName = (
                                    await userBaseInfoRepository.GetCacheValueAsync(
                                        rule.UndertakeUserTemp
                                    )
                                )?.CnName ?? "",
                                UserId = rule.UndertakeUserTemp,
                            };
                        }

                        if (rule.HandUserTemp is not null)
                        {
                            ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                                userInfoRepository;
                            rule.HandUser = new
                            {
                                CnName = (
                                    await userBaseInfoRepository.GetCacheValueAsync(
                                        rule.HandUserTemp
                                    )
                                )?.CnName ?? "",
                                UserId = rule.HandUserTemp,
                            };
                        }

                        if (rule.IgnoreUserTemp is not null)
                        {
                            ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                                userInfoRepository;
                            rule.IgnoreUser = new
                            {
                                CnName = (
                                    await userBaseInfoRepository.GetCacheValueAsync(
                                        rule.IgnoreUserTemp
                                    )
                                )?.CnName ?? "",
                                UserId = rule.IgnoreUserTemp,
                            };
                        }

                        if (rule.UpdateUserTemp is not null)
                        {
                            ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                                userInfoRepository;
                            rule.UpdateUser = new
                            {
                                CnName = (
                                    await userBaseInfoRepository.GetCacheValueAsync(
                                        rule.UpdateUserTemp
                                    )
                                )?.CnName ?? "",
                                UserId = rule.UpdateUserTemp,
                            };
                        }

                        if (rule.CreateUserTemp is not null)
                        {
                            ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                                userInfoRepository;
                            rule.CreateUser = new
                            {
                                CnName = (
                                    await userBaseInfoRepository.GetCacheValueAsync(
                                        rule.CreateUserTemp
                                    )
                                )?.CnName ?? "",
                                UserId = rule.CreateUserTemp,
                            };
                        }

                        if (rule.ReadUserTemp is not null)
                        {
                            ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                                userInfoRepository;
                            var readList = rule.ReadUserTemp.Split(";");
                            rule.ReadUser = await readList
                                .ToAsyncEnumerable()
                                .SelectAwait(async it =>
                                {
                                    var cacheValueAsync =
                                        await userBaseInfoRepository.GetCacheValueAsync(it);
                                    return new { cacheValueAsync?.UserId, cacheValueAsync?.CnName };
                                })
                                .ToListAsync(cancellationToken: cancellationToken);
                            //rule.ReadUser = await readList.ToAsyncEnumerable().AggregateAwaitAsync(async (next, current) =>
                            //    (await userBaseInfoRepository.GetCacheValueAsync(current))?.CnName + ";"
                            //    + (await userBaseInfoRepository.GetCacheValueAsync(next))?.CnName, cancellationToken: cancellationToken);
                        }

                        if (rule.AccountTemp is not null)
                        {
                            var accountTempList = rule.AccountTemp.Split(";");
                            rule.Account = await accountTempList
                                .ToAsyncEnumerable()
                                .SelectAwait(async it =>
                                {
                                    var cacheValueAsync =
                                        await mailHostRepository.GetCacheValueAsync(it);
                                    return new
                                    {
                                        cacheValueAsync?.Account,
                                        cacheValueAsync?.ShowName,
                                        cacheValueAsync?.HostId,
                                    };
                                })
                                .ToListAsync(cancellationToken: cancellationToken);
                        }
                        return rule;
                    })
                    .ToListAsync(cancellationToken: cancellationToken),
                Page = request.PageIndex.Value,
                PageSize = request.PageSize.Value,
                Total = totalCount,
            };
        }
    }
}
