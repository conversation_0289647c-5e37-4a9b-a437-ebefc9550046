{"Logging": {"LogLevel": {"Default": "Debug", "System": "Information", "Microsoft": "Information", "Hangfire": "Information"}, "FreeSql": {"Curd": {"IncludeCurdTypes": ["Select", "Delete", "Update", "Insert", "InsertOrUpdate"], "Environments": ["Local", "Development"]}}}, "ConnectionStrings": {"Default": "data source=***************,7433;initial catalog=acip_iplatform;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true", "Redis": "*************:6379,password=Acip_cc@54,defaultDatabase=10", "Hangfire": "data source=***************,7433;initial catalog=acip_iplatform;user id=sa;password=*******;persist security info=True;packet size=4096;TrustServerCertificate=true;Min Pool Size=5;Max Pool Size=10;Connection Lifetime=300;MultipleActiveResultSets=True"}}