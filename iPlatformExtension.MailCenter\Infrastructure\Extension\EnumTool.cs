﻿using System.ComponentModel;
using System.Reflection;

namespace iPlatformExtension.MailCenter.Infrastructure.Extension
{
    public class EnumTool
    {
        /// <summary>
        /// 获取枚举注释
        /// </summary>
        /// <param name="enumValue"></param>
        /// <returns></returns>
        public static string GetDescription(Enum enumValue)
        {
            Type type = enumValue.GetType();
            string name = Enum.GetName(type, enumValue);
            if (name != null)
            {
                FieldInfo field = type.GetField(name);
                if (field != null)
                {
                    DescriptionAttribute[] attributes =
                        (DescriptionAttribute[])field.GetCustomAttributes(typeof(DescriptionAttribute), false);
                    if (attributes.Length > 0)
                    {
                        return attributes[0].Description;
                    }
                }
            }
            return enumValue.ToString(); 
        }
    }
}
