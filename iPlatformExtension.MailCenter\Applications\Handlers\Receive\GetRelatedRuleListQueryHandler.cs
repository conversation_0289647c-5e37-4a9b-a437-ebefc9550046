﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 获取关联规则列表处理者
    /// </summary>
    internal sealed class GetRelatedRuleListQueryHandler(IFreeSql<MailCenterFreeSql> freeSql, IUserInfoRepository userInfoRepository, IMailHostRepository mailHostRepository) : IRequestHandler<GetRelatedRuleListQuery, IEnumerable<GetRelatedRuleListDto>>
    {
        public async Task<IEnumerable<GetRelatedRuleListDto>> Handle(GetRelatedRuleListQuery request, CancellationToken cancellationToken)
        {
            var getRelatedRuleListDtos = await freeSql.Select<MailConfigHistory, MailConfig>().WithLock()
                .LeftJoin(it => it.t1.ConfigId == it.t2.ConfigId).Where(it => it.t1.MailId == request.MailId)
                .ToListAsync(it => new GetRelatedRuleListDto(it.t2.ConfigId, it.t2.ConfigName, it.t2.ConfigRemark, it.t2.ConfigType, it.t2.CreateTime, it.t2.CreateUser,
                    it.t2.HandUser, it.t2.HandUserType, it.t2.HostId, it.t2.IgnoreUser, it.t2.IsEnabled, it.t2.ReadUser, it.t2.ReadUserType, it.t2.Remark, it.t2.RuleNumber,
                    it.t2.UndertakeUser, it.t2.UpdateTime, it.t2.UpdateUser), cancellationToken);

            return await getRelatedRuleListDtos.ToAsyncEnumerable().SelectAwait(async rule =>
                        {
                            if (rule.UndertakeUserId is not null)
                            {
                                ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                                rule.UndertakeUser = new
                                {
                                    CnName = (await userBaseInfoRepository.GetCacheValueAsync(rule.UndertakeUserId))?.CnName ?? "",
                                    UserId = rule.UndertakeUserId
                                };
                            }

                            if (rule.HandUserId is not null)
                            {
                                ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                                rule.HandUser = new
                                {
                                    CnName = (await userBaseInfoRepository.GetCacheValueAsync(rule.HandUserId))?.CnName ?? "",
                                    UserId = rule.UndertakeUserId
                                };
                            }

                            if (rule.IgnoreUserId is not null)
                            {
                                ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                                rule.IgnoreUser = new
                                {
                                    CnName = (await userBaseInfoRepository.GetCacheValueAsync(rule.IgnoreUserId))?.CnName ?? "",
                                    UserId = rule.UndertakeUserId
                                };
                            }

                            if (rule.ReadUserId is not null)
                            {
                                ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                                var readList = rule.ReadUserId.Split(";");
                                rule.ReadUser = await readList.ToAsyncEnumerable().SelectAwait(async it =>
                                {
                                    var cacheValueAsync = await userBaseInfoRepository.GetCacheValueAsync(it);
                                    return new { cacheValueAsync?.UserId, cacheValueAsync?.CnName };
                                }).ToListAsync(cancellationToken: cancellationToken);
                            }

                            if (rule.UpdateUserId is not null)
                            {
                                ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                                rule.UpdateUser = new
                                {
                                    CnName = (await userBaseInfoRepository.GetCacheValueAsync(rule.UpdateUserId))?.CnName ?? "",
                                    UserId = rule.UpdateUserId
                                };
                            }

                            if (rule.HostId is not null)
                            {
                                var accountIdList = rule.HostId.Split(";");
                                rule.Account = await accountIdList.ToAsyncEnumerable().SelectAwait(async it =>
                                {
                                    var cacheValueAsync = await mailHostRepository.GetCacheValueAsync(it);
                                    return new { cacheValueAsync?.Account, cacheValueAsync?.ShowName, cacheValueAsync?.HostId };
                                }).ToListAsync(cancellationToken: cancellationToken);
                            }
                            return rule;
                        }).ToListAsync(cancellationToken: cancellationToken);
        }
    }
}

