﻿using System.Net.Http.Json;
using iPlatformExtension.Common.Clients.HttpMessageHandlers;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Clients.Phoenix;

public sealed class PublicParametersHandler(IOptionsMonitor<PhoenixClientOptions> optionsMonitor)
    : NamedDelegatingHandler
{
    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        if (request.Content is not JsonContent {Value: PhoenixRequestParameters requestParameters})
            return base.SendAsync(request, cancellationToken);
        
        var options = optionsMonitor.Get(OptionsName);
        requestParameters.UserId = options.UserId;
        requestParameters.Executor = options.Executor;
        requestParameters.Version = options.Version;
        requestParameters.AppKey = options.AppKey;
        requestParameters.SignMethod = options.SighMethod;
        requestParameters.Timestamp = DateTimeOffset.Now.ToUnixTimeMilliseconds();

        switch (requestParameters)
        {
            case PhoenixOrderRequestParameters orderRequestParameters:
                orderRequestParameters.UserName = options.UserName;
                orderRequestParameters.AgentOrganName = options.UserName;
                orderRequestParameters.AgentOrganId = options.OrganizationId;
                break;
            case PhoenixOrderInfoParameters orderInfoParameters:
                orderInfoParameters.AgentOrganId = options.OrganizationId;
                break;
        }
        return base.SendAsync(request, cancellationToken);
    }
}