﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.Flow;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Infrastructure.PipelineBehaviors;

internal sealed class FlowObjectIdQueryHandler<TRequest, TResponse>(IFreeSql freeSql)
    : IPipelineBehavior<TRequest, TResponse> where TRequest : IFlowEventRequest
{
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        if (!request.RequiredObjectId) 
            return await next();
        
        var eventMessage = request.EventMessage;
        
        request.ObjectId = await freeSql.Select<SysFlowActivity>(eventMessage.ActivityId).WithLock()
            .ToOneAsync(activity => activity.ObjId, cancellationToken);
        if (string.IsNullOrWhiteSpace(request.ObjectId))
        {
            throw new PropertyMissingException(eventMessage.ActivityId, request.ObjectId, "流程上下文", "关联实体id");
        }

        return await next();
    }
}