﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using StackExchange.Redis;

namespace iPlatformExtension.MailCenter.Applications.Handlers.SysConfig
{
    /// <summary>
    /// 获取邮箱权限
    /// </summary>
    internal sealed class GetUserAccessQueryHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IFreeSql<PlatformFreeSql> platformFreeSql,
        IRedisCache<RedisCacheOptionsBase> redisCache
    ) : IRequestHandler<GetUserAccessQuery, GetUserAccessDto>
    {
        public async Task<GetUserAccessDto> Handle(
            GetUserAccessQuery request,
            CancellationToken cancellationToken
        )
        {
            //if (
            //    await redisCache.GetCacheValueAsync<string, GetUserAccessDto>(
            //            "UserAccess",
            //            request.UserId,
            //            cancellationToken
            //        )
            //        is var result && result is not null
            //)
            //{
            //    return result;
            //}
            var deptInfo = await platformFreeSql
                .Select<SysUserInfo>()
                .Where(it => it.UserId == request.UserId)
                .FirstAsync(it => it.DeptId, cancellationToken);
            var sysUserRole = await platformFreeSql
                .Select<SysUserRole>()
                .Where(it => it.UserId == request.UserId)
                .ToListAsync(it => it.RoleId, cancellationToken);
            var mailHostAccesses = await freeSql
                .Select<MailAccess>()
                .Where(it =>
                    sysUserRole.Contains(it.UseId)
                    || it.UseId == deptInfo
                    || it.UseId == request.UserId
                )
                .WhereIf(request.AccessMode is not null, it => it.AccessMode == request.AccessMode)
                .ToListAsync(it => it.HostId, cancellationToken);

            var privatHostList = await freeSql
                .Select<Model.MailCenter.MailHost>()
                .Where(o => o.IsPrivate && o.PrivateUserId == request.UserId)
                .ToListAsync(it => it.HostId, cancellationToken);
            mailHostAccesses.AddRange(privatHostList);
            var results = new GetUserAccessDto(mailHostAccesses.DistinctBy(it => it).ToList());
            //await redisCache.SetCacheValuesAsync("UserAccess", new Dictionary<string, GetUserAccessDto>() { { request.UserId, results } }, 
            //    TimeSpan.FromMinutes(5), cancellationToken: cancellationToken);
            return results;
        }
    }
}
