﻿using FreeSql;

namespace iPlatformExtension.Repository.FreeSql;

public class DefaultRepository<TEntity, TKey> : BaseRepository<TEntity, TKey> where TEntity : class
{
    public DefaultRepository(IFreeSql freeSql, UnitOfWorkManager unitOfWorkManager) : base(unitOfWorkManager.Orm ?? freeSql)
    {
        unitOfWorkManager.Binding(this);
    }
}

public class DefaultRepository<TEntity> : BaseRepository<TEntity> where TEntity : class
{
    public DefaultRepository(IFreeSql freeSql, UnitOfWorkManager unitOfWorkManager) : base(unitOfWorkManager.Orm ?? freeSql)
    {
        unitOfWorkManager.Binding(this);
    }
}