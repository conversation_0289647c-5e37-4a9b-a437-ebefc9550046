﻿FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
ENV ASPNETCORE_URLS=http://+:7831
ENV ASPNETCORE_ENVIRONMENT=Production
ENV TZ=Asia/Shanghai
ENV DOTNET_GCHeapHardLimit=80000000
ENV DOTNET_DbgEnableMiniDump=1
ENV DOTNET_DbgMiniDumpTyp=4
ENV DOTNET_DbgMiniDumpName=/tmp/iplatform-commission-%p-%t.dmp
ENV DOTNET_EnableCrashReport=1
ENV DOTNET_CreateDumpDiagnostics=1
ENV ASPNETCORE_HOSTINGSTARTUPASSEMBLIES=SkyAPM.Agent.AspNetCore
ENV SKYWALKING__SERVICENAME=prod::iplatform-commission
WORKDIR /app
EXPOSE 7831

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["obs_net/esdk_obs_.net_core.csproj", "obs_net/"]
COPY ["iPlatformExtension.Commission/iPlatformExtension.Commission.csproj", "iPlatformExtension.Commission/"]
COPY ["iPlatformExtension.Repository/iPlatformExtension.Repository.csproj", "iPlatformExtension.Repository/"]
COPY ["iPlatformExtension.Common/iPlatformExtension.Common.csproj", "iPlatformExtension.Common/"]
COPY ["iPlatformExtension.Model/iPlatformExtension.Model.csproj", "iPlatformExtension.Model/"]
RUN dotnet restore "iPlatformExtension.Commission/iPlatformExtension.Commission.csproj"
COPY . .
WORKDIR "/src/iPlatformExtension.Commission"
RUN dotnet build "iPlatformExtension.Commission.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "iPlatformExtension.Commission.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "iPlatformExtension.Commission.dll"]
