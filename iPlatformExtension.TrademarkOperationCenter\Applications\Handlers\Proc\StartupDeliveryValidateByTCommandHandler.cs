﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Proc;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.Extensions;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Proc;

internal sealed class StartupDeliveryValidateByTCommandHandler(IMediator mediator, IHttpContextAccessor httpContextAccessor)
    : IRequestHandler<StartupDeliveryValidateByTCommand, BatchDeliveryValidateResult>
{
    public async Task<BatchDeliveryValidateResult> Handle(StartupDeliveryValidateByTCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = httpContextAccessor.HttpContext?.User.GetUserId() ?? string.Empty;
        var procList = await mediator.Send(new ProcIdsQuery(request.ProcIds), cancellationToken);

        var validResult = new BatchDeliveryValidateResult(procList.Count())
        {
            Success = procList.GroupBy(dto => dto.CtrlProcId).Count() == 1
        };

        if (!validResult.Success)
        {
            return validResult.MultipartCtrlProc();
        }

        validResult = procList.Where(dto => dto.UndertakerId != currentUserId)
            .Aggregate(validResult, (result, dto) => result.InvalidUndertaker(dto.ProcNo));
        if (!validResult.Success)
        {
            return validResult;
        }

        var procIds = procList.Select(dto => dto.ProcId).ToList();
        var deliveryList = await mediator.Send(new BatchDeliveryValidationByTQuery(procIds, false), cancellationToken);
        validResult = deliveryList
            .Where(dto => dto.FlowType == FlowType.Delivery || dto.DeliveryFlowSubType != request.FlowSubType)
            .Aggregate(validResult, (result, info) => result.CannotStartup(info.ProcNo));

        return validResult;
    }
}