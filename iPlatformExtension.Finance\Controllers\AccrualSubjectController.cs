﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Finance.Applications.Models.Commission;
using iPlatformExtension.Finance.Applications.Queries;
using iPlatformExtension.Finance.Applications.Queries.Commission;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Finance.Controllers;

/// <summary>
/// 提记对象控制器
/// </summary>
[ApiController]
[Route("[controller]")]
public sealed class AccrualSubjectController : ControllerBase
{
    private readonly IMediator _mediator;

    /// <summary>
    /// 构造函数依赖注入
    /// </summary>
    /// <param name="mediator">中介者組件</param>
    public AccrualSubjectController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// 根据费项id获取提记对象信息
    /// </summary>
    /// <param name="feeId">费项id</param>
    /// <returns>提记对象信息</returns>
    [HttpGet]
    public Task<FeesAccrualSubjectDto> GetAccrualSubjectInfoAsync([Required]string feeId)
    {
        return _mediator.Send(new AccrualSubjectQuery(feeId));
    }
}