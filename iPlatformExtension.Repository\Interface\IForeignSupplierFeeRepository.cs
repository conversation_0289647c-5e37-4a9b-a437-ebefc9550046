﻿using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface IForeignSupplierFeeRepository : IScopeDependency
{
    public Task<bool> AddAsync(string feesId, IEnumerable<CaseFeeList> fees);
    
    public Task<IEnumerable<CaseFeeList?>> GetAsync(string feesId);
    
    public Task<bool> DeleteAsync(string feesId);
}