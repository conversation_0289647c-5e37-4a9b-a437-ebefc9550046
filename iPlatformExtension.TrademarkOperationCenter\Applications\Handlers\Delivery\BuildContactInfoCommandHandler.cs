using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal sealed class BuildContactInfoCommandHandler(IAgencyRepository agencyRepository)
    : IRequestHandler<BuildContactCommand>
{
    public async Task Handle(BuildContactCommand request, CancellationToken cancellationToken)
    {
        var deliverInfo = request.DeliveryInfo;
        var procInfo = request.ProcInfo;

        deliverInfo.ContactAddress = procInfo.TrademarkDeliveryContactAddressCn;
        deliverInfo.ContactPostCode = procInfo.TrademarkDeliveryContactPostCode;
        deliverInfo.ContactMailBox = procInfo.TrademarkDeliveryContactMailbox;
        deliverInfo.ContactTel = procInfo.TrademarkDeliveryContactTel;
        deliverInfo.AgencyId = procInfo.TrademarkDeliveryAgencyId;
        deliverInfo.AgencyName =
            (await agencyRepository.GetCacheValueAsync(procInfo.TrademarkDeliveryAgencyId ?? string.Empty, cancellationToken: cancellationToken))
            ?.AgencyNameCn;
        deliverInfo.ContactPerson = procInfo.TrademarkDeliveryContactor;
    }
    
    
}