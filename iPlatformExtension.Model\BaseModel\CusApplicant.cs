using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "cus_applicant", DisableSyncStructure = true)]
	public partial class CusApplicant {

		[ Column(Name = "applicant_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ApplicantId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "applicant_name_cn", StringLength = 500)]
		public string ApplicantNameCn { get; set; }

		[ Column(Name = "applicant_name_en", StringLength = 500)]
		public string ApplicantNameEn { get; set; }

		[ Column(Name = "applicant_name_other", StringLength = 300)]
		public string ApplicantNameOther { get; set; }

		/// <summary>
		/// 营业所在地
		/// </summary>
		[ Column(Name = "business_location", StringLength = 100)]
		public string BusinessLocation { get; set; }

		[ Column(Name = "card_no", StringLength = 50)]
		public string CardNo { get; set; }

		[ Column(Name = "card_type", StringLength = 50)]
		public string CardType { get; set; }

		[ Column(Name = "country_id", StringLength = 50)]
		public string CountryId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "email", StringLength = 500)]
		public string Email { get; set; }

		[ Column(Name = "entity_id", StringLength = 50)]
		public string EntityId { get; set; }

		[ Column(Name = "fax", StringLength = 50)]
		public string Fax { get; set; }

		[ Column(Name = "fee_reduce", StringLength = 50)]
		public string FeeReduce { get; set; }

		[ Column(Name = "fee_reduce_date")]
		public DateTime? FeeReduceDate { get; set; }

		[ Column(Name = "fee_reduce_year", StringLength = 500)]
		public string FeeReduceYear { get; set; }

		[ Column(Name = "is_contact")]
		public bool? IsContact { get; set; } = false;

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "is_fill_credit_code")]
		public bool IsFillCreditCode { get; set; } = false;

		[ Column(Name = "mobile", StringLength = 50)]
		public string Mobile { get; set; }

		[ Column(Name = "payment_name", StringLength = 50)]
		public string PaymentName { get; set; }

		/// <summary>
		/// 电子送件注册代码（中国）
		/// </summary>
		[ Column(Name = "register_code", StringLength = 50)]
		public string RegisterCode { get; set; }

		[ Column(Name = "remark", StringLength = 2000)]
		public string Remark { get; set; }

		[ Column(Name = "tel", StringLength = 50)]
		public string Tel { get; set; }

		[ Column(Name = "type_id", StringLength = 50)]
		public string TypeId { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[Column(Name = "is_chinese_identity", IsNullable = false)]
		public bool IsChineseIdentity { get; set; } = true;



    }

}
