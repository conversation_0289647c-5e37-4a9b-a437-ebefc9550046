﻿using iPlatformExtension.Model.Dto;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Queries.Supplier;

internal sealed record SupplierOutputCaseInfoCountsQuery(
    string ManageCompanyId,
    string CaseDirection,
    string CtrlProcId,
    string ApplicationTypeId,
    DateRange DateRange,
    string[] SupplierIds) : IStreamRequest<SupplierCaseCountDto>;