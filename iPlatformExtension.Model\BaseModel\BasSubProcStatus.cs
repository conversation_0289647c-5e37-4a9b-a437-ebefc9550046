using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_sub_proc_status", DisableSyncStructure = true)]
	public partial class BasSubProcStatus {

		[ Column(Name = "id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string Id { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "code", StringLength = 50)]
		public string Code { get; set; }

		[ Column(Name = "create_time", InsertValueSql = "getdate()")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; } = true;

		[ Column(Name = "parent_code", StringLength = 50, IsNullable = false)]
		public string ParentCode { get; set; }

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "text_en_us", StringLength = 50)]
		public string TextEnUs { get; set; }

		[ Column(Name = "text_ja_jp", StringLength = 50)]
		public string TextJaJp { get; set; }

		[ Column(Name = "text_zh_cn", StringLength = 50)]
		public string TextZhCn { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
