﻿using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Handlers;
using iPlatformExtension.Common.Mediator.Notifications;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Outsourcing.Application.Commands.Case;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Case;

internal sealed class CaseForeignContactSynchronizationHandler(ISender sender, IFreeSql<PlatformFreeSql> freeSql) 
    : IMatchNotificationHandler<EntityChangeNotification>
{
    private List<KeyValuePair<DbContext.EntityChangeType, ProcForeignSupplierContact?>> _contacts = [];
    
    public ValueTask<bool> MatchAsync(EntityChangeNotification notification, CancellationToken cancellationToken)
    {
        var reports = notification.Reports;
        _contacts = reports.Where(report => report.EntityType == typeof(ProcForeignSupplierContact))
            .Where(report => report.Type is DbContext.EntityChangeType.Insert or DbContext.EntityChangeType.Delete)
            .Select(report => KeyValuePair.Create(report.Type, report.Object as ProcForeignSupplierContact))
            .ToList();

        return ValueTask.FromResult(_contacts.Count > 0);
    }

    public async Task HandleAsync(EntityChangeNotification notification, CancellationToken cancellationToken)
    {
        foreach (var (entityChangeType, procForeignSupplierContact) in _contacts)
        {
            if (procForeignSupplierContact is null)
            {
                continue;
            }

            var latestProcInfo = await freeSql.Select<CaseProcInfo>().WithLock()
                .Where(procInfo => freeSql.Select<CaseProcInfo>()
                    .Where(caseProcInfo => caseProcInfo.ProcId == procForeignSupplierContact.ProcId)
                    .ToList(caseProcInfo => caseProcInfo.CaseId).Contains(procInfo.CaseId))
                .OrderByDescending(procInfo => procInfo.CreateTime)
                .ToOneAsync(procInfo => new CaseProcInfo()
                {
                    ProcId = procInfo.ProcId,
                    CaseId = procInfo.CaseId,
                    BasCtrlProc = new BasCtrlProc()
                    {
                        IsOutsourcing = procInfo.BasCtrlProc.IsOutsourcing,
                        CtrlProcId = procInfo.BasCtrlProc.CtrlProcId
                    }
                }, cancellationToken);
            
            if (latestProcInfo.ProcId != procForeignSupplierContact.ProcId || !latestProcInfo.BasCtrlProc.IsOutsourcing)
            {
                continue;
            }
            
            IRequest? request = entityChangeType switch
            {
                DbContext.EntityChangeType.Insert => new CreateCaseSupplierContactCommand(latestProcInfo.CaseId,
                    [procForeignSupplierContact.ContactId]),
                DbContext.EntityChangeType.Delete => new RemoveCaseSupplierContactCommand(latestProcInfo.CaseId,
                    procForeignSupplierContact.ContactId),
                _ => null
            };
            
            if (request is not null)
            {
                await sender.Send(request, cancellationToken);
            }
        }
    }
}