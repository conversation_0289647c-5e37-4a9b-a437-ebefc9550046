﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class FlowNodeResourcesRepository(
    IFreeSql<PlatformFreeSql> freeSql,
    IMemoryCache memoryCache,
    CacheExpirationToken<IEnumerable<FlowNodeResources>> expirationToken)
    : BaseRepository<FlowNodeResources, int>(freeSql), IFlowNodeResourcesRepository
{
    public IMemoryCache MemoryCache { get; } = memoryCache;

    public CacheExpirationToken<IEnumerable<FlowNodeResources>> ExpirationToken { get; } = expirationToken;
}