using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "proc_trademark_bonus_total", DisableSyncStructure = true)]
	public partial class ProcTrademarkBonusTotal {

		[ Column(Name = "@page_index")]
		public int PageIndex { get; set; }

		[ Column(Name = "@page_size")]
		public int PageSize { get; set; }

		[ Column(Name = "@send_official_date_begin")]
		public DateTime SendOfficialDateBegin { get; set; }

		[ Column(Name = "@send_official_date_end")]
		public DateTime SendOfficialDateEnd { get; set; }

		[ Column(Name = "@where", DbType = "varchar(MAX)", IsNullable = false)]
		public string Where { get; set; }

	}

}
