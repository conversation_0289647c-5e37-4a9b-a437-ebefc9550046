﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Proc;

/// <summary>
/// 任务扩展动态配置命令
/// </summary>
/// <param name="CtrlProcId">任务类型id</param>
/// <param name="TypeName">类型名称</param>
/// <param name="CaseDirection">案件流向</param>
public sealed record SaveProcExtensionDynamicConfigCommand([Required]string CtrlProcId, [Required]string TypeName, [Required]string CaseDirection) : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;