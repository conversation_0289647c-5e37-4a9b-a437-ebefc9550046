﻿using iPlatformExtension.MailCenter.Applications.Models.Receive;
using MediatR;
using iPlatformExtension.MailCenter.Applications.Models;
using iPlatformExtension.Model.MailCenter;

namespace iPlatformExtension.MailCenter.Applications.Queries.Receive;

/// <summary>
/// 获取流程状态
/// </summary>
/// <param name="MailFrom">发件人</param>
/// <param name="SendDateStart">发送日期开始</param>
/// <param name="SendDateEnd">发送日期结束</param>
/// <param name="ReceiveMail">收件邮箱</param>
/// <param name="Status">收件状态-1:取消,0:待解析,1:解析中,2:待分拣;3:办理中;4:已忽略;5:已办理;500:出错</param>
/// <param name="UndertakeUserId">承办人</param>
/// <param name="SortBy">分拣人</param>
/// <param name="SortTimeStart">分拣时间开始</param>
/// <param name="SortTimeEnd">分拣时间结束</param>
/// <param name="FinishDateStart">完成时间开始</param>
/// <param name="FinishDateEnd">完成时间结束</param>
/// <param name="IgnoreBy">忽略人</param>
/// <param name="IgnoreTimeStart">忽略时间开始</param>
/// <param name="IgnoreTimeEnd">忽略时间结束</param>
/// <param name="MailSubject">收件主题</param>
/// <param name="Search">搜索</param>
/// <param name="MailNo">收件编号</param>
/// <param name="ReadUser">阅读人</param>
/// <param name="Ignore">排除（忽略人、分拣人是自己）</param>
/// <param name="Sort">排序</param>
/// <param name="SortType">Acs,Desc默认Acs</param>
public record GetReceiveListQuery(string? MailFrom, DateTime? SendDateStart, DateTime? SendDateEnd, string? ReceiveMail, List<int>? Status, List<string>? UndertakeUserId,
    List<string>? SortBy, DateTime? SortTimeStart, DateTime? SortTimeEnd, DateTime? FinishDateStart, DateTime? FinishDateEnd, List<string>? IgnoreBy, DateTime? IgnoreTimeStart, DateTime? IgnoreTimeEnd, string? MailSubject,
    string? Search, string? MailNo, List<string>? ReadUser, bool Ignore = false, string Sort = nameof(MailReceive.MailDate), string SortType = "Desc") : PageModel, IRequest<IEnumerable<GetReceiveListDto>>;

