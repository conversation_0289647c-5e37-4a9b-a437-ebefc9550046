﻿using iPlatformExtension.MailCenter.Applications.Models.SysConfig;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.SysConfig;

/// <summary>
/// 获取枚举值
/// </summary>
/// <param name="Enum">FilterHead：匹配字段；ReceiveFileType：收件状态；FilterType:匹配条件；ConfigType:分拣类型（系统动作）;ParseScope:解析范围 ;SendStatusType:发送状态</param>
public record GetEnumQuery(string Enum = "FilterHead") : IRequest<IEnumerable<GetEnumDto>>;

