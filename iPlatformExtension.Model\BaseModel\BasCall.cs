using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_call", DisableSyncStructure = true)]
	public partial class BasCall {

		[ Column(Name = "call_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string CallId { get; set; }

		[ Column(Name = "call_en_us", StringLength = 100)]
		public string CallEnUs { get; set; }

		[ Column(Name = "call_ja_jp", StringLength = 100)]
		public string CallJaJp { get; set; }

		[ Column(Name = "call_zh_cn", StringLength = 50)]
		public string CallZhCn { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
