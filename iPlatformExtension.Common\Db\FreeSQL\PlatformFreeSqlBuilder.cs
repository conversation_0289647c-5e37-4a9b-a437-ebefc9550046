using FreeSql;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Db.FreeSQL;

/// <summary>
/// 自定义<see cref="IFreeSql{TMark}"/>的构建器
/// </summary>
/// <typeparam name="T">类型参数用于区分不同的数据源</typeparam>
public class PlatformFreeSqlBuilder<T>
{
    public required IServiceCollection Services { get; set; }

    public PlatformFreeSqlBuilderOptions<T> BuilderOptions { get; set; } = new()
    {
        ConnectionString = string.Empty
    };

    // public required OptionsBuilder<PlatformFreeSqlOptions<T>> FreeSqlOptionsBuilder { get; set; }

    public required OptionsBuilder<PlatformFreeSqlBuilderOptions<T>> FreeSqlBuilderOptionsBuilder { get; set; }

    /// <summary>
    /// 根据作用域配置<see cref="IFreeSql"/>
    /// </summary>
    /// <param name="configure">配置逻辑</param>
    /// <typeparam name="T1">配置的依赖服务</typeparam>
    /// <returns><see cref="PlatformFreeSqlBuilder{T}"/></returns>
    public PlatformFreeSqlBuilder<T> ConfigureFreeSql<T1>(Action<IFreeSql, T1> configure) where T1 : class
    {
        Services.AddTransient<IPostConfigureOptions<IFreeSql>>(sp =>
            new PostConfigureOptions<IFreeSql, T1>(Options.DefaultName, sp.GetRequiredService<T1>(), configure));
        return this;
    }

    /// <summary>
    /// 根据作用域配置<see cref="IFreeSql"/>
    /// </summary>
    /// <param name="configure">配置逻辑</param>
    /// <returns><see cref="PlatformFreeSqlBuilder{T}"/></returns>
    public PlatformFreeSqlBuilder<T> ConfigureFreeSql(Action<IFreeSql> configure)
    {
        Services.AddSingleton<IPostConfigureOptions<IFreeSql>>(_ =>
            new PostConfigureOptions<IFreeSql>(Options.DefaultName, configure));
        return this;
    }

    public PlatformFreeSqlBuilder<T> ConfigureGlobal(Action<IFreeSql, IServiceProvider> configure)
    {
        FreeSqlBuilderOptionsBuilder.PostConfigure(options => options.GlobalConfigure += configure);
        return this;
    }
    
    public PlatformFreeSqlBuilder<T> ConfigureGlobal(Action<IFreeSql> configure)
    {
        FreeSqlBuilderOptionsBuilder.PostConfigure(options => options.GlobalConfigure += (freeSql, _) => configure(freeSql));
        return this;
    }

    internal PlatformFreeSqlBuilder<T> Configure(Action<PlatformFreeSqlBuilderOptions<T>> configure)
    {
        configure(BuilderOptions);
        
        ValidOptions();

        return this;
    }

    internal void ValidOptions()
    {
        if (string.IsNullOrWhiteSpace(BuilderOptions.ConnectionString))
        {
            throw new ArgumentException("连接字符串不能为空", nameof(BuilderOptions.ConnectionString));
        }

        if (BuilderOptions.CommandTimeout.TotalSeconds < 5)
        {
            throw new InvalidDataException("SQL命令超时时间要5秒以上");
        }
    }
}