﻿using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.Model.Dto.Flow;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using iPlatformExtension.Common.Db.FreeSQL;

namespace iPlatformExtension.Public.Applications.Commands.Flow
{
    public record class FlowMessageCommand(string message) 
        : IRequest, IFreeSqlUnitOfWorkCommand<PlatformFreeSql>;
}
