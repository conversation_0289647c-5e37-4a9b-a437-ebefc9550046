﻿﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.Signature
{
    /// <summary>
    /// 保存签名命令
    /// </summary>
    public class SaveSignatureCommand : IRequest<string>,IUnitOfWorkCommandMysql
    {
        /// <summary>
        /// 签名ID（为空表示新增，有值表示更新）
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 签名名称
        /// </summary>
        [Required(ErrorMessage = "签名名称不能为空")]
        [MaxLength(255, ErrorMessage = "签名名称长度不能超过255个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 签名内容
        /// </summary>
        [Required(ErrorMessage = "签名内容不能为空")]
        public string Content { get; set; } = string.Empty;
    }
}
