﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Model.Constants;

namespace iPlatformExtension.Outsourcing.Application.Models.Fee;

[Description("供应商费用创建Dto")]
public sealed record SupplierFeeCreateDto(
    [property: Required, Description($"""
                                     费用类型:
                                     官费：{FeeClass.Official}
                                     代理费：{FeeClass.Agent}
                                     第三方费：{FeeClass.Third}
                                     """)] string FeeClass, 
    [property: Required, Description("费用金额")] decimal FeeAmount);