﻿using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Outsourcing.Application.Handlers.Proc;

internal sealed class RemoveProcSupplierContactCommandHandler(
    IHttpContextAccessor httpContextAccessor,
    ICaseProcInfoRepository caseProcInfoRepository,
    IProcForeignSupplierContactRepository procForeignSupplierContactRepository
    ) : IRequestHandler<RemoveProcSupplierContactCommand>
{
    public async Task Handle(RemoveProcSupplierContactCommand request, CancellationToken cancellationToken)
    {
        var (procId, contactId, version) = request;
        var userId = httpContextAccessor.HttpContext?.User.GetUserId() ?? string.Empty;
        
        var procInfo = await caseProcInfoRepository.GetAsync(procId, cancellationToken);
        if (procInfo == null)
        {
            throw new NotFoundException(procId, "案件任务");
        }
        
        procInfo.ValidateVersion(version);
        
        await procForeignSupplierContactRepository
            .DeleteAsync(contact => contact.ProcId == procId && contact.ContactId == contactId, cancellationToken);

        procInfo.UpdateTime = DateTime.Now;
        procInfo.UpdateUserId = userId;
        
        await caseProcInfoRepository.UpdateAsync(procInfo, cancellationToken);
    }
}