using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_dept_info", DisableSyncStructure = true)]
	public partial class SysDeptInfo {

		[ Column(Name = "dept_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string DeptId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "dept_code", StringLength = 50)]
		public string DeptCode { get; set; }

		[ Column(Name = "dept_level", StringLength = 50)]
		public string DeptLevel { get; set; }

		[ Column(Name = "dept_name", StringLength = 100)]
		public string DeptName { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "full_name", StringLength = 500)]
		public string FullName { get; set; }

		[ Column(Name = "is_branch")]
		public bool? IsBranch { get; set; }

		[ Column(Name = "is_charge")]
		public bool? IsCharge { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "parent_id", StringLength = 50)]
		public string ParentId { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

    }

}
