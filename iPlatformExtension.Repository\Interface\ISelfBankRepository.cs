﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;

namespace iPlatformExtension.Repository.Interface;

public interface ISelfBankRepository : 
    IBaseRepository<BasSelfBank, string>, 
    IScopeDependency,
    IRedisCacheableRepository<string, BasSelfBank>
{
    Task<BasSelfBank?> ICacheableRepository<string, BasSelfBank>.GetValueFromDbAsync(string key, CancellationToken cancellationToken)
    {
        return Select.WithLock().Where(bank => bank.BankId == key).FirstAsync(cancellationToken)!;
    }

    async Task<IEnumerable<BasSelfBank>> ICacheableRepository<string, BasSelfBank>.GetValuesFromDbAsync(CancellationToken cancellationToken)
    {
        return await Select.WithLock().ToListAsync(cancellationToken);
    }

    string ICacheableRepository<string, BasSelfBank>.GenerateKey(BasSelfBank value)
    {
        return value.BankId;
    }
}