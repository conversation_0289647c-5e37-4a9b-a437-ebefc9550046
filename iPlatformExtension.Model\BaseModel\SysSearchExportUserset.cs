using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "sys_search_export_userset", DisableSyncStructure = true)]
	public partial class SysSearchExportUserset {

		[ Column(Name = "user_export_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string UserExportId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "is_default")]
		public int? IsDefault { get; set; }

		[ Column(Name = "search_form_code", StringLength = 50)]
		public string SearchFormCode { get; set; }

		[ Column(Name = "sub_code", StringLength = 50)]
		public string SubCode { get; set; }

		[ Column(Name = "title", StringLength = 100)]
		public string Title { get; set; }

		[ Column(Name = "user_id", StringLength = 50)]
		public string UserId { get; set; }

	}

}
