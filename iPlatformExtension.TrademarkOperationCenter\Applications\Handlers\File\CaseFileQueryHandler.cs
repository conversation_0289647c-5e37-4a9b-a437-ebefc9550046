﻿using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.File;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.File;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.File;

internal sealed class CaseFileQueryHandler(IFreeSql freeSql, IUserInfoRepository userInfoRepository, HuaweiObsClient huaweiObsClient)
    : IRequestHandler<CaseFileQuery, IEnumerable<FileListDto>>
{
    public async Task<IEnumerable<FileListDto>> Handle(CaseFileQuery request, CancellationToken cancellationToken)
    {
        if (!request.ObjectIds.Any())
        {
            return Array.Empty<FileListDto>();
        }
        
        const string caseFileAlias = nameof(CaseFile);
        var fileListQuery = freeSql.Select<CaseFile>().NoTracking().WithLock()
            .WhereDynamicFilter(request.ObjectIds.BuildContainsDynamicFilter(nameof(CaseFile.ObjId), caseFileAlias))
            .Where(file => !string.IsNullOrEmpty(file.FileDescription!.FileTypeId))
            .Where(file => !string.IsNullOrEmpty(file.FileDescription!.FileDescId))
            .WhereIf(!string.IsNullOrWhiteSpace(request.Keyword),
                file => file.FileName.Contains(request.Keyword!) ||
                        file.FileDescription!.FileDescZhCn.Contains(request.Keyword!) ||
                        file.FileDescription!.FileType!.FileTypeZhCn.Contains(request.Keyword!));

        var fileResult = await fileListQuery.ToPageableResultAsync(request, file => new FileListDto
        {
            FileNo = file.FileNo,
            CaseFileId = file.FileId,
            ObjectId = file.ObjId,
            FileType = file.FileDescription!.FileType!.FileTypeZhCn,
            FileName = file.FileName,
            FileDescription = file.FileDescription!.FileDescZhCn,
            FileDescriptionId = file.FileDescription.FileDescId,
            UploadTime = file.CreateTime,
            Uploader = file.CreateUserId
        }, cancellationToken);
        
        foreach (var fileListInfo in fileResult)
        {
            fileListInfo.FileId = Convert.ToInt32(fileListInfo.FileNo[4..]);
            fileListInfo.Uploader = await userInfoRepository.GetChineseValueAsync(fileListInfo.Uploader) ?? string.Empty;
        }

        var fileIds = fileResult.Select(dto => dto.FileId).ToArray();
        
        if (!request.ReturnUrl || fileIds.Length == 0) return fileResult;
        
        var files = await freeSql.Select<FileListA>().WithLock()
            .Where(a => fileIds.Contains(a.Id))
            .ToDictionaryAsync(a => a.Id, a => new FileListA
            {
                Id = a.Id,
                ServerPath = a.ServerPath,
                FileName = a.FileName,
                Bucket = a.Bucket
            }, cancellationToken);
            
        foreach (var fileListDto in fileResult)
        {
            if (files.TryGetValue(fileListDto.FileId, out var fileDetail))
            {
                fileListDto.Url = huaweiObsClient.GenerateTemporaryUrl(fileDetail.GetObjectName(), fileDetail.Bucket,
                    TimeSpan.FromDays(365 * 7)).SignUrl;
            }
        }

        return fileResult;
    }
}