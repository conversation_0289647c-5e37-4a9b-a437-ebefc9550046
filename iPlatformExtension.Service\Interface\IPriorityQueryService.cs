﻿using iPlatformExtension.Common.DependencyInjection;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;

namespace iPlatformExtension.Service.Interface;

public interface IPriorityQueryService : IFreeSqlQueryService, ITransientDependency
{
    async Task<Dictionary<string, IEnumerable<PriorityInfo>>> GetPrioritiesByCaseIdsAsync(IEnumerable<string> caseIds)
    {
        if (!caseIds.Any())
        {
            return new Dictionary<string, IEnumerable<PriorityInfo>>();
        }

        var priorities = await DbQuery.Select<CasePriorityInfo>().WithLock()
            .Where(priorityInfo => caseIds.Contains(priorityInfo.CaseId)).ToListAsync(priorityInfo =>
                new KeyValuePair<string, PriorityInfo>(priorityInfo.CaseId,
                    new PriorityInfo(priorityInfo.PriorityNo, priorityInfo.PriorityDate)));

        return priorities.GroupBy(priority => priority.Key, priority => priority.Value)
            .ToDictionary(group => group.Key, group => group as IEnumerable<PriorityInfo>);
    }
}