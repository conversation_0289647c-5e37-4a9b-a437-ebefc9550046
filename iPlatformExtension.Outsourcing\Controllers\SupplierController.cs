﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using iPlatformExtension.Common.Attributes;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Outsourcing.Application.Commands.Proc;
using iPlatformExtension.Outsourcing.Application.Models.Supplier;
using iPlatformExtension.Outsourcing.Application.Queries.Proc;
using iPlatformExtension.Outsourcing.Application.Queries.Supplier;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Outsourcing.Controllers;

/// <summary>
/// 供应商控制器
/// </summary>
/// <param name="sender"></param>
[Tags("供应商", "境外代理")]
[Description("供应商控制器")]
[ApiController]
[Route("supplier")]
[AllowAnonymous]
[ProducesResponseType<ResultData>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
public sealed class SupplierController(ISender sender) : ControllerBase
{
    /// <summary>
    /// 更新年度统计
    /// </summary>
    /// <returns></returns>
    [HttpPut("annual-statistics")]
    [EndpointName(nameof(RefreshSupplierStatisticsAsync))]
    [EndpointSummary("统计境外代理每年的任务计数")]
    [EndpointDescription("定时任务后台更新境外代理每年的任务计数。服务端后台定时任务，前端不用对接。")]
    public Task RefreshSupplierStatisticsAsync()
    {
        return sender.Send(new RefreshSuppliersStatisticsCommand());
    }

    /// <summary>
    /// 根据当前任务外所商标任务统计
    /// </summary>
    /// <param name="procId">任务id</param>
    /// <returns>统计数据</returns>
    [HttpGet("trademark-proc-count")]
    [EndpointName(nameof(GetTrademarkProcCountsAsync))]
    [EndpointSummary("客户历史使用所")]
    [EndpointDescription("根据当前前任务信息查询历史任务的各个境外代理计数。目前只针对出口商标选外所时使用")]
    public Task<IEnumerable<SupplierTrademarkProcCountDto>> GetTrademarkProcCountsAsync(
        [Description("任务id"), FromQuery, Required] string procId)
    {
        return sender.Send(new SupplierTrademarkProcCountCommand(procId), HttpContext.RequestAborted);
    }
    
    /// <summary>
    /// 获取个外所商标任务统计
    /// </summary>
    /// <param name="query">查询参数</param>
    /// <returns>统计数据</returns>
    [HttpPost("trademark-proc-count")]
    [EndpointName($"Apply{nameof(GetTrademarkProcCountsAsync)}")]
    [EndpointSummary("客户历史使用所")]
    [EndpointDescription("根据当前开案客户，国家和任务名称信息查询历史任务的各个境外代理计数。目前只针对出口商标选外所时使用")]
    public Task<IEnumerable<SupplierTrademarkProcCountDto>> GetTrademarkProcCountsAsync([FromBody] SupplierProcCountQuery query)
    {
        return sender.Send(query, HttpContext.RequestAborted);
    }

    [HttpGet("case-count/input")]
    [EndpointName(nameof(GetInputCaseCountsAsync))]
    [EndpointSummary("获取供应商来案计数")]
    [EndpointDescription("供应商作为客户名称的案件数量")]
    [AsyncStream(Delay = 50)]
    [ProducesResponseType<ResultData<SupplierCaseCountDto>>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    public IAsyncEnumerable<SupplierCaseCountDto> GetInputCaseCountsAsync(
        [FromQuery, Description("管理分所"), Required] string manageCompanyId,
        [FromQuery, Description("案件流向"), Required] string caseDirections,
        [FromQuery, Description("任务名称"), Required] string ctrlProcIds,
        [FromQuery, Description("申请类型"), Required] string applicationTypeIds,
        [FromQuery, Description("开始时间"), Required] DateOnly startDate,
        [FromQuery, Description("结束时间"), Required] DateOnly endDate,
        [FromQuery, Description("供应商id"), Required] string supplierIds)
    {
        var countsQuery = new SupplierInputCaseInfoCountsQuery(
            manageCompanyId, 
            caseDirections.Split(',', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries), 
            ctrlProcIds.Split(',', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries), 
            applicationTypeIds.Split(',', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries), 
            new DateRange(startDate, endDate), 
            supplierIds.Split(',', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries));
        return sender.CreateStream(countsQuery, HttpContext.RequestAborted);
    }
    
    [HttpGet("case-count/output")]
    [EndpointName(nameof(GetOutputCaseCountsAsync))]
    [EndpointSummary("获取供应商去案计数")]
    [EndpointDescription("供应商作为境外代理的案件数量")]
    [AsyncStream(Delay = 50)]
    [ProducesResponseType<ResultData<SupplierCaseCountDto>>(StatusCodes.Status200OK, MediaTypeNames.Application.Json)]
    public IAsyncEnumerable<SupplierCaseCountDto> GetOutputCaseCountsAsync(
        [FromQuery, Description("管理分所"), Required] string manageCompanyId,
        [FromQuery, Description("案件流向"), Required] string caseDirection,
        [FromQuery, Description("任务名称"), Required] string ctrlProcId,
        [FromQuery, Description("申请类型"), Required] string applicationTypeId,
        [FromQuery, Description("开始时间"), Required] DateOnly startDate,
        [FromQuery, Description("结束时间"), Required] DateOnly endDate,
        [FromQuery, Description("供应商id"), Required] string supplierIds)
    {
        var countsQuery = new SupplierOutputCaseInfoCountsQuery(
            manageCompanyId, 
            caseDirection, 
            ctrlProcId, 
            applicationTypeId, 
            new DateRange(startDate, endDate), 
            supplierIds.Split(',', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries));
        return sender.CreateStream(countsQuery, HttpContext.RequestAborted);
    }
}