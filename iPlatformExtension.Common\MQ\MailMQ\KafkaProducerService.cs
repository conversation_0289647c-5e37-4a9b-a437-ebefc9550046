﻿using Confluent.Kafka;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.MQ.MailMQ
{
    public class KafkaProducerService<TKey, TValue> : IKafkaProducerService
    {
        private readonly IOptionsMonitor<KafkaProducerSettings> _config;
        private readonly KafkaProducerFactory<TKey, TValue> _producerFactory;
        private IProducer<TKey, TValue> _producer;
        public KafkaProducerService(KafkaProducerFactory<TKey, TValue> producerFactory, IOptionsMonitor<KafkaProducerSettings> config)
        {
            _producerFactory = producerFactory;
            _config = config;
        }

        public async Task<DeliveryResult<TKey, TValue>> SendMessageAsync(TValue content, string configName, CancellationToken cancellationToken = default)
        {
            try
            {
                var config = _config.Get(configName);
                var topicPartition = new TopicPartition(config.Topic, new Partition(0));
                _producer ??= _producerFactory.CreateProducer(configName);
                var res = await _producer.ProduceAsync(topicPartition,
                    new Message<TKey, TValue>() { Value = content }, cancellationToken);
                Console.WriteLine($"Delivered '{res.Value}' to '{res.TopicPartitionOffset}'");
                return res;
            }
            catch (ProduceException<Null, string> e)
            {
                Console.WriteLine($"Delivery failed: {e.Error.Reason}");
            }
            return new DeliveryResult<TKey, TValue>();
        }
    }
}
