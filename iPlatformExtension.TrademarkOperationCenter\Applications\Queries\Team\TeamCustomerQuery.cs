﻿using iPlatformExtension.TrademarkOperationCenter.Applications.Models;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Team;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Team;

/// <summary>
/// 团队客户查询
/// </summary>
/// <param name="TeamId">团队id</param>
public record TeamCustomerQuery(string? TeamId) : PageModel, IRequest<IEnumerable<TeamCustomerDto>>;

