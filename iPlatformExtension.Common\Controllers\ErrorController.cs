﻿using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.Common.Controllers;

[ApiController]
[Route("[controller]")]
[ApiExplorerSettings(IgnoreApi = true)]
public sealed class ErrorController(ILoggerFactory loggerFactory) : ControllerBase
{

    /// <summary>
    /// 记录无法捕捉的错误，并重新抛出。
    /// </summary>
    /// <returns></returns>
    [HttpGet, HttpPost, HttpOptions, HttpPut, HttpPatch]
    [ApiExplorerSettings(IgnoreApi = true)]
    public void Index()
    {
        var feature = HttpContext.Features.Get<IExceptionHandlerPathFeature>();
        if (feature == null) return;
        var logger = loggerFactory.CreateLogger(feature.Path);
        var error = feature.Error;
        logger.LogWarning(error, "");
        throw error;
    }
}