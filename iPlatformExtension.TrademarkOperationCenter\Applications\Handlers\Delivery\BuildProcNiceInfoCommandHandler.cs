﻿using System.Runtime.InteropServices;
using AutoMapper;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.NotificationHandlers;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery;

internal abstract class BuildProcNiceInfoCommandHandler(IMapper mapper, IDeliveryNiceCategoryRepository niceCategoryRepository) 
    : IMatchTrademarkProcCommandHandler<BuildNiceInfoCommand>
{
    protected readonly IMapper _mapper = mapper;

    protected readonly IDeliveryNiceCategoryRepository _niceCategoryRepository = niceCategoryRepository;
    
    public Task HandleAsync(BuildNiceInfoCommand notification, CancellationToken cancellationToken)
    {
        var procNiceCategories = notification.ProcInfo.NiceCategories ?? Array.Empty<ProcNiceCategory>();
        if (procNiceCategories.Count == 0)
        {
            return Task.CompletedTask;
        }

        var niceClasses =
            notification.ProcInfo.TrademarkNiceClasses?.Split(';').Select(niceClass => Convert.ToInt32(niceClass)) ??
            Array.Empty<int>();
        var deliveryNiceCategories =
            _mapper.Map<List<DeliveryNiceCategory>>(procNiceCategories.Where(category =>
                niceClasses.Contains(Convert.ToInt32(category.GrandNumber))));
        
        var deliveryNiceCategoriesSpan = CollectionsMarshal.AsSpan(deliveryNiceCategories);
        foreach (var deliveryNiceCategory in deliveryNiceCategoriesSpan)
        {
            deliveryNiceCategory.Id = default;
            deliveryNiceCategory.CreationTime = DateTime.Now;
            deliveryNiceCategory.UpdateTime = DateTime.Now;
            deliveryNiceCategory.CaseId = notification.ProcInfo.CaseInfo.Id;
        }

        return _niceCategoryRepository.InsertAsync(deliveryNiceCategories, cancellationToken);
    }

    public abstract string CtrlProcId { get; }
    
    public abstract IEnumerable<string> CaseDirections { get; }
}