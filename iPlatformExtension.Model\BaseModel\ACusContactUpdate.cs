using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "a_cus_contact_update", DisableSyncStructure = true)]
	public partial class ACusContactUpdate {

		[ Column(Name = "address_cn", StringLength = 500)]
		public string AddressCn { get; set; }

		[ Column(Name = "address_en", StringLength = 500)]
		public string AddressEn { get; set; }

		[ Column(Name = "call_name", StringLength = 50)]
		public string CallName { get; set; }

		[ Column(Name = "contact_id", StringLength = 50, IsNullable = false)]
		public string ContactId { get; set; }

		[ Column(Name = "contact_name", StringLength = 200)]
		public string ContactName { get; set; }

		[ Column(Name = "customer_id", StringLength = 50)]
		public string CustomerId { get; set; }

		[ Column(Name = "email", StringLength = 500)]
		public string Email { get; set; }

		[ Column(Name = "fax", StringLength = 50)]
		public string Fax { get; set; }

		[ Column(Name = "is_del")]
		public bool? IsDel { get; set; } = false;

		[ Column(Name = "is_do")]
		public bool? IsDo { get; set; } = false;

		[ Column(Name = "mobile", StringLength = 50)]
		public string Mobile { get; set; }

		[ Column(Name = "new_contact_id", StringLength = 50)]
		public string NewContactId { get; set; }

		[ Column(Name = "num")]
		public int? Num { get; set; }

		[ Column(Name = "qq", StringLength = 50)]
		public string Qq { get; set; }

		[ Column(Name = "tel", StringLength = 100)]
		public string Tel { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
