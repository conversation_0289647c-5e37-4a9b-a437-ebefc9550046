using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel;

/// <summary>
/// 流程事件错误日志表
/// </summary>
[Table(Name = "flow_event_error_log", DisableSyncStructure = true)]
public class FlowEventErrorLog 
{

	/// <summary>
	/// 主键
	/// </summary>
	[Column(Name = "id", IsPrimary = true, IsIdentity = true)]
	public int Id { get; set; }

	/// <summary>
	/// 流程上下文id
	/// </summary>
	[Column(Name = "activity_id", StringLength = 50, IsNullable = false)]
	public string ActivityId { get; set; } = null!;

	/// <summary>
	/// 创建时间
	/// </summary>
	[Column(Name = "creation_time", InsertValueSql = "getdate()")]
	public DateTime CreationTime { get; set; }

	/// <summary>
	/// 错误消息
	/// </summary>
	[Column(Name = "error_message", StringLength = -2, IsNullable = false)]
	public string ErrorMessage { get; set; } = "";

	/// <summary>
	/// 消息键
	/// </summary>
	[Column(Name = "message_key", StringLength = 50, IsNullable = false)]
	public string MessageKey { get; set; } = null!;

	/// <summary>
	/// 消息内容(以json或base64存储)
	/// </summary>
	[Column(Name = "message_value", StringLength = -2, IsNullable = false)]
	public string MessageValue { get; set; } = null!;

	/// <summary>
	/// 节点id
	/// </summary>
	[Column(Name = "node_id", StringLength = 50, IsNullable = false)]
	public string NodeId { get; set; } = null!;

	/// <summary>
	/// 提交类型
	/// </summary>
	[Column(Name = "submit_type", StringLength = 50, IsNullable = false)]
	public string SubmitType { get; set; } = null!;

	/// <summary>
	/// 主题
	/// </summary>
	[Column(Name = "topic", StringLength = 50, IsNullable = false)]
	public string Topic { get; set; } = null!;

}