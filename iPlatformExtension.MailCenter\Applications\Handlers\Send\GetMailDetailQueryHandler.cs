using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Commands.Send;
using iPlatformExtension.MailCenter.Applications.Queries.Send;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send;

/// <summary>
/// 获取邮件详情查询处理器
/// </summary>
internal sealed class GetMailDetailQueryHandler(
    IFreeSql<MailCenterFreeSql> freeSql,
    IMailSendRepository mailSendRepository,
    IMailUserRepository mailUserRepository) : IRequestHandler<GetMailDetailQuery, GetMailDetailDto>
{
    public async Task<GetMailDetailDto> Handle(GetMailDetailQuery request, CancellationToken cancellationToken)
    {
        // 获取邮件信息
        var mailSend = await mailSendRepository
            .Where(it => it.MailId == request.MailId)
            .WithLock().FirstAsync(cancellationToken)
            ?? throw new ApplicationException("邮件不存在");

        // 获取邮件用户信息
        var mailUsers = await mailUserRepository
            .Where(it => it.MailId == request.MailId)
            .WithLock().ToListAsync(cancellationToken);

        // 构建结果
        var result = new GetMailDetailDto
        {
            MailId = mailSend.MailId,
            Subject = mailSend.MailSubject,
            HtmlBody = mailSend.MailHtmlBody,
            MailFrom = mailUsers.Where(u => u.AddressType == "from").Select(u => new MailAddressList() { MailAddress = u.MailAddress, DisplayName = u.DisplayName }).ToList(),
            MailTo = mailUsers.Where(u => u.AddressType == "to").Select(u => new MailAddressList() { MailAddress = u.MailAddress, DisplayName = u.DisplayName }).ToList(),
            MailCc = mailUsers.Where(u => u.AddressType == "cc").Select(u => new MailAddressList() { MailAddress = u.MailAddress, DisplayName = u.DisplayName }).ToList(),
            MailBcc = mailUsers.Where(u => u.AddressType == "bcc").Select(u => new MailAddressList() { MailAddress = u.MailAddress, DisplayName = u.DisplayName }).ToList(),
            SendTime = mailSend.SendTime,
            Attachments = mailSend.Attachments,
            MailRelayBody = mailSend.MailRelayBody ?? string.Empty,
            IsImportant = mailSend.IsImportant,
            IsRead = mailSend.IsRead,
            IsRequiredProcessTime = mailSend.IsRequiredProcessTime,
            SignatureBody = mailSend.SignatureBody ?? string.Empty,
            HostId = mailSend.HostId
        };

        return result;
    }

}
