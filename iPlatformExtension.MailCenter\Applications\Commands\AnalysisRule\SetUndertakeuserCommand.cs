﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.AnalysisRule;

/// <summary>
/// 设置承办人
/// </summary>
/// <param name="UserId">承办人id</param>
/// <param name="MailId">邮件id</param>
public record SetUndertakeUserCommand([Required] string UserId, List<string> MailId) : IRequest, IUnitOfWorkCommandMysql;

