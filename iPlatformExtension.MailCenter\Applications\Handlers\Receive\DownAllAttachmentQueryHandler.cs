﻿using System.IO.Compression;
using System.Text.RegularExpressions;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive;

/// <summary>
///
/// </summary>
internal sealed class DownAllAttachmentQueryHandler(
    IFreeSql<MailCenterFreeSql> freeSql,
    IConfiguration configuration
) : IRequestHandler<DownAllAttachmentQuery, DownAllAttachmentDto>
{
    public async Task<DownAllAttachmentDto> Handle(
        DownAllAttachmentQuery request,
        CancellationToken cancellationToken
    )
    {
        var attachments = await freeSql
            .Select<MailAttachments>()
            .WithLock()
            .Where(it => request.MailId == it.MailId)
            .ToListAsync(cancellationToken);
        var mailNo = await freeSql
            .Select<MailReceive>()
            .WithLock()
            .Where(it => request.MailId == it.MailId)
            .FirstAsync(it => it.MailNo, cancellationToken);
        using var memoryStream = new MemoryStream();
        using var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true);
        using var httpClient = new HttpClient();

        // 先下载所有文件到内存中
        var downloadResults = await Task.WhenAll(
            attachments.Select(async attachment =>
            {
                var obsUrl =
                    $"https://{attachment.Bucket}.{Regex.Replace(configuration["HuaweiObs:Host"]!, @"^https?://", "")}/{attachment.ServerPath}/{attachment.FileName}";
                var response = await httpClient.GetAsync(obsUrl, cancellationToken);
                if (response.IsSuccessStatusCode)
                {
                    var fileBytes = await response.Content.ReadAsByteArrayAsync(cancellationToken);
                    return (attachment.RealName, fileBytes);
                }
                return default;
            })
        );

        // 依次将文件写入到ZIP中
        foreach (var result in downloadResults.Where(r => r != default))
        {
            var zipEntry = archive.CreateEntry(result.RealName);
            await using var zipStream = zipEntry.Open();
            await zipStream.WriteAsync(result.fileBytes, cancellationToken);
            await zipStream.FlushAsync(cancellationToken);
        }

        // 确保所有内容都写入到 MemoryStream
        archive.Dispose();

        return new DownAllAttachmentDto(memoryStream.ToArray(), $"{mailNo}.zip");
    }
}
