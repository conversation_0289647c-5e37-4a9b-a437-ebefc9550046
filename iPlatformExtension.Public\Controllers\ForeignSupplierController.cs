﻿using iPlatformExtension.Public.Applications.Models.Supplier;
using iPlatformExtension.Public.Applications.Queries.Supplier;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 外所供应商控制器
/// </summary>
/// <param name="sender">服务调用中介者</param>
[ApiController]
[Route("foreign-supplier")]
public sealed class ForeignSupplierController(ISender sender) : ControllerBase
{
    /// <summary>
    /// 查询外所供应商
    /// </summary>
    /// <param name="keyword">关键字</param>
    /// <returns>供应商信息</returns>
    [HttpGet]
    public Task<IEnumerable<SupplierDto>> GetSuppliersAsync([FromQuery(Name = "keyword")] string keyword = "")
    {
        return sender.Send(new SupplierQuery(keyword), HttpContext.RequestAborted);
    }
}