﻿using FluentValidation;
using iPlatformExtension.Commission.Application.Models.WinningReward;
using iPlatformExtension.Common.Validation.Validators;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Commission.Infrastructure.Validators;

internal sealed class WinningRewardRulePatchValidator : AbstractValidator<RulePatchDto>
{
    public WinningRewardRulePatchValidator(ISystemDictionaryRepository dictionaryRepository)
    {
        var dateTypeValidator = new SystemDictionaryValueValidator<RulePatchDto>(dictionaryRepository, SystemDictionaryName.WinningRewardDateType);
        var rulingResultValidator =
            new SystemDictionaryValueValidator<RulePatchDto>(dictionaryRepository, SystemDictionaryName.RulingResult);
        
        RuleFor(dto => dto.DateType).SetAsyncValidator(dateTypeValidator);
        RuleFor(dto => dto.RulingResult).SetAsyncValidator(rulingResultValidator);
    }
}