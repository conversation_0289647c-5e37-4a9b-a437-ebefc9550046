using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_bill_template", DisableSyncStructure = true)]
	public partial class BasBillTemplate {

		[ Column(Name = "template_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string TemplateId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		[ Column(Name = "seq")]
		public short? Seq { get; set; }

		[ Column(Name = "template_code", StringLength = 50)]
		public string TemplateCode { get; set; }

		[ Column(Name = "template_en_us", StringLength = 100)]
		public string TemplateEnUs { get; set; }

		[ Column(Name = "template_ja_jp", StringLength = 100)]
		public string TemplateJaJp { get; set; }

		[ Column(Name = "template_zh_cn", StringLength = 50)]
		public string TemplateZhCn { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
