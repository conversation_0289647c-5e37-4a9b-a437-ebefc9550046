using System.Reflection;
using iPlatformExtension.Common.ServiceDiscovery.LoadBalance;
using iPlatformExtension.Common.ServiceDiscovery.Nacos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.ServiceDiscovery;

namespace iPlatformExtension.Common.Extensions;

public static class ServiceDiscoveryExtension
{
    public static IServiceCollection AddNacosServiceEndpointProvider(this IServiceCollection services, Action<NacosServiceEndpointOptions> options)
    {
        services.TryAddEnumerable(ServiceDescriptor.Singleton<IServiceEndpointProviderFactory, NacosServiceEndpointProviderFactory>());
        services.Configure(options);
        return services;
    }

    public static IServiceCollection AddNacosServiceEndpointProvider(this IServiceCollection services)
    {
        return AddNacosServiceEndpointProvider(services, _ => { });
    }

    public static IServiceCollection AddNacosServiceDiscovery(this IServiceCollection services,
        Action<ServiceDiscoveryOptions> options)
    {
        return services.AddServiceDiscoveryCore(options).AddNacosServiceEndpointProvider();
    }

    public static IServiceCollection AddNacosServiceDiscovery(this IServiceCollection services,
        Action<NacosServiceEndpointOptions> options)
    {
        return services.AddServiceDiscoveryCore().AddNacosServiceEndpointProvider(options);
    }

    public static IServiceCollection AddServiceEndpointSelector<TSelector>(this IServiceCollection services) 
        where TSelector : class, IServiceEndpointSelector
    {
        var interfaceType = EndpointSelectorProxy<TSelector>.EndpointInterfaceType;
        
        services.TryAddTransient<TSelector>();
        services.AddTransient(interfaceType, provider =>
        {
            var proxy = DispatchProxy.Create(interfaceType, typeof(EndpointSelectorProxy<TSelector>));
            var instance = provider.GetRequiredService<TSelector>();

            if (proxy is EndpointSelectorProxy<TSelector> selectorProxy)
            {
                selectorProxy.Instance = instance;
            }

            return proxy;
        });

        return services;
    }

    public static HybridSelectorBuilder AddHybridSelector(this IServiceCollection services)
    {
        var builder = new HybridSelectorBuilder(services);
        services.AddServiceEndpointSelector<HybridServiceEndpointSelector>();

        return builder;
    }

    public static HybridSelectorBuilder AddMatchSelector<TSelector>(this HybridSelectorBuilder builder) 
        where TSelector : class, IMatchServiceEndpointSelector
    {
        var services = builder.ServiceCollection;
        services.TryAddEnumerable(ServiceDescriptor.Transient<IMatchServiceEndpointSelector, TSelector>());
        
        return builder;
    }
}