﻿using FreeSql;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Caching.Memory;

namespace iPlatformExtension.Repository.Implement;

internal sealed class BaseBankRepository(
    IFreeSql<PlatformFreeSql> freeSql, 
    IMemoryCache memoryCache, 
    IRedisCache<RedisCacheOptionsBase> redisCache, 
    CacheExpirationToken<BasSelfBank> expirationToken) : BaseRepository<BasSelfBank, string>(freeSql), 
    IBaseBankRepository
{

    public IMemoryCache MemoryCache { get; } = memoryCache;
    
    public CacheExpirationToken<BasSelfBank> ExpirationToken { get; } = expirationToken;
    
    public IRedisCache<RedisCacheOptionsBase> RedisCache { get; } = redisCache;
}