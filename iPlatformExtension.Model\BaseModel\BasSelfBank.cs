using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "bas_self_bank", DisableSyncStructure = true)]
	public partial class BasSelfBank {

		[ Column(Name = "account", StringLength = 50, IsNullable = false)]
		public string Account { get; set; }

		[ Column(Name = "agency_id", StringLength = 50)]
		public string AgencyId { get; set; }

		[ Column(Name = "bank_address_en_us", StringLength = 1000)]
		public string BankAddressEnUs { get; set; }

		[ Column(Name = "bank_address_zh_cn", StringLength = 1000)]
		public string BankAddressZhCn { get; set; }

		[ Column(Name = "bank_code", StringLength = 50, IsNullable = false)]
		public string BankCode { get; set; }

		[ Column(Name = "bank_id", StringLength = 50, IsNullable = false)]
		public string BankId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		[ Column(Name = "bank_name_en_us", StringLength = 1000)]
		public string BankNameEnUs { get; set; }

		[ Column(Name = "bank_name_zh_cn", StringLength = 1000)]
		public string BankNameZhCn { get; set; }

		[ Column(Name = "company_id", StringLength = 50)]
		public string CompanyId { get; set; }

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "currency", StringLength = 50, IsNullable = false)]
		public string Currency { get; set; }

		[ Column(Name = "district", StringLength = 50)]
		public string District { get; set; }

		[ Column(Name = "is_enabled")]
		public bool IsEnabled { get; set; } = true;

		/// <summary>
		/// 是否为共用账号
		/// </summary>
		[ Column(Name = "is_public")]
		public bool? IsPublic { get; set; } = true;

		[ Column(Name = "seq")]
		public int? Seq { get; set; }

		[ Column(Name = "update_time", InsertValueSql = "getdate()")]
		public DateTime UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

		[ Column(Name = "user_name_en_us", StringLength = 1000)]
		public string UserNameEnUs { get; set; }

		[ Column(Name = "user_name_zh_cn", StringLength = 1000)]
		public string UserNameZhCn { get; set; }

	}

}
