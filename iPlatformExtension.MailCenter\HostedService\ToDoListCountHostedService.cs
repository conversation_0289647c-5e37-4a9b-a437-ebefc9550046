﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using System.Collections.Generic;
using System.Threading;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using Microsoft.Extensions.DependencyInjection;

namespace iPlatformExtension.MailCenter.HostedService
{
    public class ToDoListCountHostedService(
        ILogger<ToDoListCountHostedService> logger, IFreeSql<MailCenterFreeSql> freeSql,
        IConfiguration configuration, IServiceScopeFactory serviceScopeFactory
      ) : BackgroundService
    {
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            using (var scope = serviceScopeFactory.CreateScope())
            {
                var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
                await mediator.Send(new CalculateFlowPrivateCountQuery(), stoppingToken);
            }
        }
    }
}
