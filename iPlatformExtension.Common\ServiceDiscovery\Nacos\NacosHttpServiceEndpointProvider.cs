﻿using System.Runtime.CompilerServices;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.ServiceDiscovery;
using Nacos.V2;

namespace iPlatformExtension.Common.ServiceDiscovery.Nacos;

internal sealed class NacosHttpServiceEndpointProvider(
    string serviceName,
    string groupName,
    INacosNamingService namingService,
    NacosServiceEndpointReloadToken reloadToken)
    : NacosServiceEndpointProviderBase(groupName, serviceName, reloadToken, namingService)
{

    protected override async IAsyncEnumerable<ServiceEndpoint> ProvideEndpointsAsync([EnumeratorCancellation] CancellationToken cancellationToken)
    {
        var instances = await _namingService.GetAllInstances(_serviceName, _groupName);

        var httpInstances = instances.Where(instance =>
            !instance.Metadata.TryGetValue("secure", out var secureValue) || !Convert.ToBoolean(secureValue)).ToList();
        
        foreach (var httpInstance in httpInstances)
        {
           var metaData = httpInstance.Metadata;
            
            var httpEndPoint = httpInstance.GetEndPoint();
            
            var featureCollection = new FeatureCollection(3);
            featureCollection.Set(metaData);
            featureCollection.Set(httpInstance);
            featureCollection.Set(new NacosServiceNameFeature(_groupName, _serviceName, httpInstance.ToInetAddr() ?? string.Empty, Uri.UriSchemeHttp));

            yield return ServiceEndpoint.Create(httpEndPoint, featureCollection);
            
            if (metaData.TryGetValue("gRPC_port", out var grpcPort))
            {
                foreach (var grpcEndPoint in httpInstance.GetEndPoints(grpcPort))
                {
                    featureCollection.Set(new NacosServiceNameFeature(_groupName, _serviceName, grpcEndPoint.ToString() ?? string.Empty, Uri.UriSchemeHttps));
                    yield return ServiceEndpoint.Create(grpcEndPoint, featureCollection);
                }
            }
        }
    }
}