using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	/// <summary>
	/// 国内商标提成权值固化
	/// </summary>
	[Table(Name = "domestic_trademark_commission", DisableSyncStructure = true)]
	public partial class DomesticTrademarkCommission
	{

		/// <summary>
		/// 任务id
		/// </summary>
		[Column(Name = "proc_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string ProcId { get; set; } = string.Empty;

		/// <summary>
		/// 申请号
		/// </summary>
		[Column(Name = "app_no", StringLength = 50)]
		public string AppNo { get; set; } = null!;

		/// <summary>
		/// 是否大客户
		/// </summary>
		[Column(Name = "bigclient", DbType = "varchar(2)", IsNullable = false)]
		public string BigClient { get; set; } = null!;

		/// <summary>
		/// 案件名称
		/// </summary>
		[Column(Name = "case_name", StringLength = 500, IsNullable = false)]
		public string CaseName { get; set; } = "";

		/// <summary>
		/// 用户中文名
		/// </summary>
		[Column(Name = "cn_name", StringLength = 50, IsNullable = false)]
		public string CnName { get; set; } = string.Empty;

		/// <summary>
		/// 提记日期
		/// </summary>
		[Column(Name = "commission_date")]
		public DateTime CommissionDate { get; set; }

		/// <summary>
		/// 任务名称id
		/// </summary>
		[Column(Name = "ctrl_proc_id", StringLength = 50, IsNullable = false)]
		public string CtrlProcId { get; set; } = string.Empty;

		/// <summary>
		/// 任务标识
		/// </summary>
		[Column(Name = "ctrl_proc_mark", StringLength = 50)]
		public string CtrlProcMark { get; set; } = string.Empty;

		/// <summary>
		/// 任务标识中文名称
		/// </summary>
		[Column(Name = "ctrl_proc_mark_cn", StringLength = 100, IsNullable = false)]
		public string CtrlProcMarkCn { get; set; } = "";

		/// <summary>
		/// 任务名称
		/// </summary>
		[Column(Name = "ctrl_proc_zh_cn", StringLength = 50, IsNullable = false)]
		public string CtrlProcZhCn { get; set; } = "";

		/// <summary>
		/// 客户名称
		/// </summary>
		[Column(Name = "customer_name", StringLength = 1000, IsNullable = false)]
		public string CustomerName { get; set; } = "";

		/// <summary>
		/// 客户id
		/// </summary>
		[Column(Name = "customer_id", StringLength = 50, IsNullable = false)]
		public string CustomerId { get; set; } = string.Empty;

		/// <summary>
		/// 用户所属部门id
		/// </summary>
		[Column(Name = "dept_id", StringLength = 50, IsNullable = false)]
		public string DeptId { get; set; } = string.Empty;

		/// <summary>
		/// 用户所属部门名称
		/// </summary>
		[Column(Name = "dept_name", StringLength = 100, IsNullable = false)]
		public string DeptName { get; set; } = "";

		/// <summary>
		/// 分区编码
		/// </summary>
		[Column(Name = "district_code", StringLength = 50, IsNullable = false)]
		public string DistrictCode { get; set; } = "";

		/// <summary>
		/// 分区名称
		/// </summary>
		[Column(Name = "district_name", StringLength = 50, IsNullable = false)]
		public string DistrictName { get; set; } = "";

		/// <summary>
		/// 月
		/// </summary>
		[Column(Name = "month")]
		public int Month { get; set; }

		/// <summary>
		/// 权值计算日
		/// </summary>
		[Column(Name = "point_date")]
		public DateTime PointDate { get; set; }

		/// <summary>
		/// 任务编号
		/// </summary>
		[Column(Name = "proc_no", StringLength = 50, IsNullable = false)]
		public string ProcNo { get; set; } = "";

		/// <summary>
		/// 权值
		/// </summary>
		[Column(Name = "proc_point", DbType = "money")]
		public decimal ProcPoint { get; set; } = 0M;

		/// <summary>
		/// 任务状态
		/// </summary>
		[Column(Name = "proc_status_id", StringLength = 50)]
		public string ProcStatusId { get; set; } = string.Empty;

		/// <summary>
		/// 注册号
		/// </summary>
		[Column(Name = "register_no", StringLength = 50)]
		public string? RegisterNo { get; set; }

		/// <summary>
		/// 任务状态
		/// </summary>
		[Column(Name = "status", StringLength = 50)]
		public string Status { get; set; } = string.Empty;

		/// <summary>
		/// 尼斯分类
		/// </summary>
		[Column(Name = "trademark_classes", StringLength = 100)]
		public string TrademarkClasses { get; set; } = string.Empty;

		/// <summary>
		/// 尼斯分类大类数量
		/// </summary>
		[Column(Name = "trademark_items_num")]
		public int TrademarkItemsNum { get; set; } = 0;

		/// <summary>
		/// 任务主承办人id
		/// </summary>
		[Column(Name = "proc_main_undertaker_id", StringLength = 50, IsNullable = false)]
		public string ProcMainUndertakerId { get; set; } = string.Empty;

		/// <summary>
		/// 用户编号
		/// </summary>
		[Column(Name = "user_name", StringLength = 50, IsNullable = false)]
		public string UserName { get; set; } = string.Empty;

		/// <summary>
		/// 我方文号
		/// </summary>
		[Column(Name = "volume", StringLength = 50, IsNullable = false)]
		public string Volume { get; set; } = "";

		/// <summary>
		/// 案件id
		/// </summary>
		[Column(Name = "case_id", StringLength = 50, IsNullable = false)]
		public string CaseId { get; set; }=string.Empty;

		/// <summary>
		/// 年
		/// </summary>
		[Column(Name = "year")]
		public int Year { get; set; }

		/// <summary>
		/// 代理费
		/// </summary>
		[Column(Name = "agent_fees")]
		public string AgentFees { get; set; } = string.Empty;

		/// <summary>
		/// 是否已推送
		/// </summary>
		[Column(Name = "pushed", IsNullable = false)]
		public bool Pushed { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "creation_time")]
		public DateTime CreationTime { get; set; } = DateTime.Now;

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time")]
		public DateTime UpdateTime { get; set; } = DateTime.Now;

		/// <summary>
		/// 创建者
		/// </summary>
		[Column(Name = "creator")]
		public string Creator { get; set; } = string.Empty;

		/// <summary>
		/// 更新者
		/// </summary>
		[Column(Name = "updater")]
		public string Updater { get; set; } = string.Empty;

		/// <summary>
		/// 编辑后的任务权值
		/// </summary>
		[Column(Name = "edited_proc_point")]
		public decimal? EditedProcPoint { get; set; }

	}

}
