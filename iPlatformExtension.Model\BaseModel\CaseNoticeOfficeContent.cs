using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "case_notice_office_content", DisableSyncStructure = true)]
	public partial class CaseNoticeOfficeContent {

		[ Column(Name = "annual_year")]
		public int? AnnualYear { get; set; }

		[ Column(Name = "audit_result", StringLength = 500)]
		public string AuditResult { get; set; }

		[ Column(Name = "content", StringLength = -2)]
		public string Content { get; set; }

		[ Column(Name = "examiner", StringLength = 50)]
		public string Examiner { get; set; }

		[ Column(Name = "examiner_dept", StringLength = 500)]
		public string ExaminerDept { get; set; }

		[ Column(Name = "examiner_tel", StringLength = 50)]
		public string ExaminerTel { get; set; }

		[ Column(Name = "fee_djf", StringLength = 50)]
		public string FeeDjf { get; set; }

		[ Column(Name = "fee_ggf", StringLength = 50)]
		public string FeeGgf { get; set; }

		[ Column(Name = "fee_nf", StringLength = 50)]
		public string FeeNf { get; set; }

		[ Column(Name = "fee_qfj", StringLength = 50)]
		public string FeeQfj { get; set; }

		[ Column(Name = "fee_reduce")]
		public int? FeeReduce { get; set; }

		[ Column(Name = "fee_scf", StringLength = 50)]
		public string FeeScf { get; set; }

		[ Column(Name = "fee_sfj", StringLength = 50)]
		public string FeeSfj { get; set; }

		[ Column(Name = "fee_sqf", StringLength = 50)]
		public string FeeSqf { get; set; }

		[ Column(Name = "fee_yhf", StringLength = 50)]
		public string FeeYhf { get; set; }

		[ Column(Name = "fee_ysf", StringLength = 50)]
		public string FeeYsf { get; set; }

		[ Column(Name = "fee_yxq", StringLength = 50)]
		public string FeeYxq { get; set; }

		[ Column(Name = "notice_id", StringLength = 50)]
		public string NoticeId { get; set; }

		[ Column(Name = "pay_due_date")]
		public DateTime? PayDueDate { get; set; }

	}

}
