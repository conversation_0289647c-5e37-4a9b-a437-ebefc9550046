﻿using iPlatformExtension.MailCenter.Applications.Commands.ContentManage;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using iPlatformExtension.Common.Extensions;
using Microsoft.AspNetCore.Http;
using iPlatformExtension.Model.MailCenter;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{

    public class SaveFlowPrivateHandler : IRequestHandler<SaveFlowPrivateCommand>
    {
        private readonly IMailFlowPrivateRepository _mailFlowPrivateRepository;
        private readonly IFlowPrivateListRepository _flowPrivateListRepository;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public SaveFlowPrivateHandler(
            IMailFlowPrivateRepository mailFlowPrivateRepository,
            IFlowPrivateListRepository flowPrivateListRepository,
            IHttpContextAccessor httpContextAccessor)
        {
            _mailFlowPrivateRepository = mailFlowPrivateRepository;
            _flowPrivateListRepository = flowPrivateListRepository;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task Handle(SaveFlowPrivateCommand request, CancellationToken cancellationToken)
        {
            var userId = ValidateAndGetUserId();
            await DeleteRemovedItems(request, userId, cancellationToken);
            await SaveNewAndUpdatedItems(request, userId, cancellationToken);
        }

        private string ValidateAndGetUserId()
        {
            var userId = _httpContextAccessor.HttpContext?.User.GetUserID();
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("用户未登录或身份无效");
            }
            return userId;
        }

        private async Task DeleteRemovedItems(SaveFlowPrivateCommand request, string userId, CancellationToken cancellationToken)
        {
            var flowPrivatesToDelete = await _mailFlowPrivateRepository
                .Where(o => !request.PrivateList.Any(r => r.Id == o.Id) && o.UserId == userId)
                .ToListAsync(cancellationToken);

            if (!flowPrivatesToDelete.Any()) return;


            // 批量删除相关数据
            await _flowPrivateListRepository
                  .Where(o => flowPrivatesToDelete.Any(f => f.Id == o.PrivateId))
                  .ToDelete()
                  .ExecuteAffrowsAsync(cancellationToken);
            await _mailFlowPrivateRepository
                 .Where(o => flowPrivatesToDelete.Any(f => f.Id == o.Id) && o.UserId == userId)
                 .ToDelete()
                 .ExecuteAffrowsAsync(cancellationToken);

        }

        private async Task SaveNewAndUpdatedItems(SaveFlowPrivateCommand request, string userId, CancellationToken cancellationToken)
        {
            var (itemsToAdd, itemsToUpdate) = await PrepareItemsForSave(request, userId, cancellationToken);

            var saveTasks = new List<Task>();
            if (itemsToAdd.Any())
            {
                saveTasks.Add(_mailFlowPrivateRepository.InsertAsync(itemsToAdd, cancellationToken));
            }
            if (itemsToUpdate.Any())
            {
                saveTasks.Add(_mailFlowPrivateRepository.UpdateAsync(itemsToUpdate, cancellationToken));
            }

            await Task.WhenAll(saveTasks);
        }

        private async Task<(List<MailFlowPrivate> ItemsToAdd, List<MailFlowPrivate> ItemsToUpdate)>
            PrepareItemsForSave(SaveFlowPrivateCommand request, string userId, CancellationToken cancellationToken)
        {
            var itemsToAdd = new List<MailFlowPrivate>();
            var itemsToUpdate = new List<MailFlowPrivate>();

            foreach (var item in request.PrivateList)
            {
                if (string.IsNullOrEmpty(item.Id))
                {
                    itemsToAdd.Add(new MailFlowPrivate
                    {
                        Id = Guid.NewGuid().ToString(),
                        LabelName = item.LabelName,
                        Sort = item.Seq,
                        UserId = userId,
                        IsDefault = item.IsDefault,
                        MailType = item.MailType,
                        CreateBy = userId,
                        CreateTime = DateTime.Now
                    });
                }
                else
                {
                    var existingItem = await _mailFlowPrivateRepository.GetAsync(item.Id, cancellationToken);
                    if (existingItem != null)
                    {
                        existingItem.Sort = item.Seq;
                        existingItem.LabelName = item.LabelName;
                        existingItem.IsDefault = item.IsDefault;
                        existingItem.MailType = item.MailType;
                        existingItem.UpdateBy = userId;
                        existingItem.UpdateTime = DateTime.Now;
                        itemsToUpdate.Add(existingItem);
                    }
                }
            }

            return (itemsToAdd, itemsToUpdate);
        }
    }
}
