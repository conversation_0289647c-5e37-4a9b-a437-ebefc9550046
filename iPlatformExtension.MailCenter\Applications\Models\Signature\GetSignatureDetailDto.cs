﻿﻿using System;

namespace iPlatformExtension.MailCenter.Applications.Models.Signature
{
    /// <summary>
    /// 签名详情DTO
    /// </summary>
    public class GetSignatureDetailDto
    {
        /// <summary>
        /// 签名ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 签名名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 签名内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 语言类型
        /// </summary>
        public string Language { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
    }
}
