﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Platform;
using iPlatformExtension.MailCenter.Applications.Queries.Platform;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Platform;

/// <summary>
///
/// </summary>
internal sealed class GetProcListQueryHandler(
    IFreeSql<MailCenterFreeSql> freeSql,
    IUserInfoRepository userInfoRepository
) : IRequestHandler<GetProcListQuery, IEnumerable<GetProcListDto>>
{
    public async Task<IEnumerable<GetProcListDto>> Handle(
        GetProcListQuery request,
        CancellationToken cancellationToken
    )
    {
        var procList = await freeSql
            .Select<MailCorrelative, MailReceive, MailSend, MailReceiveFlow>()
            .LeftJoin(it => it.t1.MailId == it.t2.MailId)
            .LeftJoin(it => it.t1.MailId == it.t3.MailId)
            .LeftJoin(it => it.t2.MailId == it.t4.MailId)
            .WithLock()
            .Where(it =>
                it.t1.ObjId == request.ProcId
                && it.t1.CorrelateType == SysEnum.CorrelateType.Proc.ToString()
            )
            .OrderByDescending(it => it.t1.CreateTime)
            .ToListAsync(
                it => new GetProcListDto(
                    it.t1.MailId,
                    it.t2.MailNo ?? it.t3.MailNo,
                    it.t2.MailSubject ?? it.t3.MailSubject,
                    it.t2.MailFrom ?? it.t3.MailFrom,
                    it.t2.MailDate ?? it.t3.MailDate,
                    it.t2.Status ?? 0,
                    it.t1.CreateBy,
                    it.t1.CreateTime,
                    it.t1.ObjId,
                    it.t2.MailId == null ? "发件" : "收件",
                    it.t4.SendName
                ),
                cancellationToken
            );

        return await procList
            .ToAsyncEnumerable()
            .SelectAwait(async it =>
            {
                if (it.CreateByTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                        userInfoRepository;
                    it.CreateBy = new
                    {
                        CnName = (
                            await userBaseInfoRepository.GetCacheValueAsync(it.CreateByTemp)
                        )?.CnName ?? "",
                        UserId = it.CreateByTemp,
                    };
                }

                return it;
            })
            .ToListAsync(cancellationToken: cancellationToken);
    }
}
