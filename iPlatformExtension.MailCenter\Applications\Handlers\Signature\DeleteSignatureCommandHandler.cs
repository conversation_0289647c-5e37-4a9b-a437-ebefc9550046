﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.Signature;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Signature
{
    /// <summary>
    /// 删除签名命令处理器
    /// </summary>
    internal sealed class DeleteSignatureCommandHandler(
        IMailSignatureRepository mailSignatureRepository,
        IHttpContextAccessor httpContextAccessor
    ) : IRequestHandler<DeleteSignatureCommand>
    {
        public async Task Handle(
            DeleteSignatureCommand request,
            CancellationToken cancellationToken
        )
        {
            // 获取当前用户ID
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;

            // 查询签名是否存在
            var signature = await mailSignatureRepository
                .Where(it => it.Id == request.Id && it.UserId == userId)
                .FirstAsync(cancellationToken);

            if (signature == null)
            {
                throw new Exception("签名不存在或无权限删除");
            }

            // 删除签名
            await mailSignatureRepository.DeleteAsync(signature, cancellationToken);
        }
    }
}
