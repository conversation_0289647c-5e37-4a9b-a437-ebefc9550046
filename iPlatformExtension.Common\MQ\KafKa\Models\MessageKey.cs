﻿using System.Diagnostics;

namespace iPlatformExtension.Common.MQ.KafKa.Models;

public struct MessageKey
{
    public bool Equals(MessageKey other)
    {
        return Route == other.Route;
    }

    public override bool Equals(object? obj)
    {
        return obj is MessageKey other && Equals(other);
    }

    public override int GetHashCode()
    {
        return Route.GetHashCode();
    }

    public string? Consumer { get; set; }

    public string Operation { get; set; }

    public string Route { get; set; }

    public string? TraceId { get; set; } = Activity.Current?.TraceId.ToString();

    public MessageKey(string? consumer, string operation)
    {
        consumer ??= string.Empty;
        ArgumentException.ThrowIfNullOrEmpty(operation);

        Route = (string.IsNullOrEmpty(consumer) ? operation : $"{consumer}:{operation}").ToLower();
        Consumer = consumer.ToLower();
        Operation = operation.ToLower();
    }

    public MessageKey(string route)
    {
        Route = route.ToLower();

        if (Route.Contains(':'))
        {
            var routes = Route.Split(':');
            switch (routes.Length)
            {
                case 1:
                    Operation = routes[0];
                    break;
                case 2:
                    Consumer = routes[0];
                    Operation = routes[1];
                    break;
                default:
                    Consumer = string.Empty;
                    Operation = string.Empty;
                    break;
            }
        }
        else
        {
            Operation = route;
        }
    }

    public override string ToString()
    {
        return Route;
    }

    public static implicit operator string(MessageKey messageKey)
    {
        return messageKey.Route;
    }

    public static implicit operator MessageKey(string route)
    {
        return new MessageKey(route);
    }

    public static bool operator ==(MessageKey x, MessageKey y)
    {
        return x.Route == y.Route;
    }

    public static bool operator !=(MessageKey x, MessageKey y)
    {
        return !(x == y);
    }
}