﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Common.Office.Excel.Models;
using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.Common.TypeInfo;

public class EntityPropertyInfo
{
    /// <summary>
    /// 属性名称
    /// </summary>
    public string PropertyName { get; set; } = default!;

    /// <summary>
    /// 列名
    /// </summary>
    public string? ColumnName { get; internal set; }
    
    /// <summary>
    /// 模型绑定名称
    /// </summary>
    internal string? ModelBindPropertyName { get; set; }

    /// <summary>
    /// 是否忽略列
    /// </summary>
    internal bool ColumnIgnore { get; set; }

    /// <summary>
    /// 是否是主键
    /// </summary>
    public bool IsKey { get; internal set; }

    /// <summary>
    /// 是否隐藏
    /// </summary>
    public bool IsHidden { get; internal set; }

    /// <summary>
    /// 属性setter委托
    /// </summary>
    public Action<object, object?>? Set { get; internal set; }

    /// <summary>
    /// 属性getter的委托
    /// </summary>
    public Func<object, object?>? Get { get; internal set; }

    /// <summary>
    /// 属性的类型信息
    /// </summary>
    public Type PropertyType { get; internal set; } = default!;
    
    /// <summary>
    /// 前端显示名称
    /// </summary>
    public DisplayAttribute? DisplayInfo { get; internal set; }

    /// <summary>
    /// 关于键值对显示的信息数据
    /// </summary>
    public KeyValueDisplayAttribute? KeyValueDisplayInfo { get; internal set; }

    /// <summary>
    /// excel列信息
    /// </summary>
    public ExcelColumnInfo ExcelColumnInfo { get; internal set; } = null!;
}