﻿using iPlatformExtension.Model.Attributes;
using System.Text.Json.Serialization;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;

/// <summary>
/// 商标许可备案
/// </summary>
public sealed class TrademarkLicenseFilingOrder : PhoenixOrderRequestParameters
{
    /// <summary>
    /// 代理人姓名
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? AgentOrganConName { get; set; }
    
    /// <summary>
    /// 申请人地址
    /// </summary>
    [JsonPropertyName("applicantAddress")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ApplicantAddress { get; set; }

    /// <summary>
    /// 申请人地址（英文）
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("applicantEnglishAddress")]
    public string? ApplicantEnglishAddress { get; set; }

    /// <summary>
    /// 申请人英文名称
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    [JsonPropertyName("applicantEnglishName")]
    public string? ApplicantEnglishName { get; set; }

    /// <summary>
    /// 申请人名称
    /// </summary>
    [JsonPropertyName("applicantName")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ApplicantName { get; set; }

    /// <summary>
    /// 被许可人信息序列化数据
    /// </summary>
    [JsonPropertyName("assigneeInfo")]
    [JsonSerializationSource(nameof(Licensee))]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? AssigneeInfo { get; set; }

    /// <summary>
    /// 被许可人信息
    /// </summary>
    public DeliveryApplicantInfo? Licensee { get; set; }

    /// <summary>
    /// 许可类型
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? LicenseType { get; set; }

    /// <summary>
    /// 书证类型：1表示大陆,2表示海外,3表示台湾,4表示香港,5表示澳门
    /// </summary>
    [JsonPropertyName("bookType")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? BookType { get; set; }

    /// <summary>
    /// 证件类型：0不是任何类型,1身份证,2护照,3其他
    /// </summary>
    [JsonPropertyName("certificatesType")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? CertificatesType { get; set; }

    /// <summary>
    /// 申请人地址邮编
    /// </summary>
    [JsonPropertyName("code")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Code { get; set; }

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    [JsonPropertyName("contactEmail")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ContactEmail { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    [JsonPropertyName("contactName")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ContactName { get; set; }

    /// <summary>
    /// 联系人电话
    /// </summary>
    [JsonPropertyName("contactTel")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ContactTel { get; set; }

    /// <summary>
    /// 许可使用合同生效日期
    /// </summary>
    [JsonPropertyName("contractStartTime")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public DateOnly? ContractEffectiveDate { get; set; }

    /// <summary>
    /// 许可使用合同终止日期
    /// </summary>
    [JsonPropertyName("contractStopTime")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public DateOnly? ContractTerminationDate { get; set; }

    /// <summary>
    /// 国家或地区
    /// </summary>
    [JsonPropertyName("country")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Country { get; set; }

    /// <summary>
    /// 证件编号
    /// </summary>
    [JsonPropertyName("idCard")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? IdCard { get; set; }

    /// <summary>
    /// 申请人资质：0个人 1公司
    /// </summary>
    [JsonPropertyName("ownerType")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? OwnerType { get; set; }

    /// <summary>
    /// 机构委托人姓名
    /// </summary>
    [JsonPropertyName("principalName")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? PrincipalName { get; set; }

    /// <summary>
    /// 机构委托人电话
    /// </summary>
    [JsonPropertyName("principalTel")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? PrincipalTel { get; set; }

    /// <summary>
    /// 主体资格类型：1表示中文,0表示非中文
    /// </summary>
    [JsonPropertyName("subjectType")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? SubjectType { get; set; }
    
    /// <summary>
    /// 申请人递交资料
    /// 不做参数序列化
    /// </summary>
    public IEnumerable<ApplicantAttachment> ApplicantAttachments { get; set; } = Array.Empty<ApplicantAttachment>();
    
    /// <summary>
    /// 申请人材料
    /// </summary>
    [JsonSerializationSource(nameof(ApplicantAttachments))]
    public string? Attachments { get; private set; }
    
    /// <summary>
    /// 商标商品信息
    /// </summary>
    public IEnumerable<BrandInfo> BrandInfos { get; set; } = Array.Empty<BrandInfo>();

    /// <summary>
    /// 序列化后商品信息
    /// </summary>
    [JsonSerializationSource(nameof(BrandInfos))]
    public string? BrandTransferParam { get; private set; }
}