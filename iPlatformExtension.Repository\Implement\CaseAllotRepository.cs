﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Repository.Implement
{
    internal class CaseAllotRepository : DefaultRepository<CaseAllot, string>, ICaseAllotRepository
    {
        public CaseAllotRepository(IFreeSql fsql, UnitOfWorkManager uowManger) : base(fsql, uowManger)
        {
        }
    }
}
