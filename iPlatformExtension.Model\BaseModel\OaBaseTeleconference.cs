using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "oa_base_teleconference", DisableSyncStructure = true)]
	public partial class OaBaseTeleconference {

		[ Column(Name = "create_time")]
		public DateTime? CreateTime { get; set; }

		[ Column(Name = "create_user_id", StringLength = 50)]
		public string CreateUserId { get; set; }

		[ Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		[ Column(Name = "seq", StringLength = 50)]
		public string Seq { get; set; }

		[ Column(Name = "teleconference_id", StringLength = 50, IsNullable = false)]
		public string TeleconferenceId { get; set; } = Guid.NewGuid().ToString().ToUpper();

		/// <summary>
		/// 远程会议名称
		/// </summary>
		[ Column(Name = "teleconference_name", StringLength = 50)]
		public string TeleconferenceName { get; set; }

		/// <summary>
		/// 远程会议号
		/// </summary>
		[ Column(Name = "teleconference_number", StringLength = 50)]
		public string TeleconferenceNumber { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

		[ Column(Name = "update_user_id", StringLength = 50)]
		public string UpdateUserId { get; set; }

	}

}
