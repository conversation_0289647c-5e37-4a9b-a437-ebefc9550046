﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Logging.Mongo;

internal sealed class MongoLogger : ILogger
{
    private readonly IExternalScopeProvider _scopeProvider;

    private readonly IOptionsMonitor<LoggerFilterOptions> _filterOptions;

    private readonly string _categoryName;

    public MongoLogger(IExternalScopeProvider scopeProvider, IOptionsMonitor<LoggerFilterOptions> filterOptions, string categoryName)
    {
        _scopeProvider = scopeProvider;
        _filterOptions = filterOptions;
        _categoryName = categoryName;
    }

    public IDisposable? BeginScope<TState>(TState state) where TState : notnull
    {
        return _scopeProvider?.Push(state) ?? NullScope.Instance;
    }

    public bool IsEnabled(LogLevel logLevel)
    {
        var filterOptions = _filterOptions.CurrentValue;
        var minLevelRule = filterOptions.Rules.OrderBy(rule => rule.LogLevel ?? LogLevel.None)
            .FirstOrDefault(rule => string.Equals(rule.CategoryName, _categoryName));

        return logLevel >= (minLevelRule?.LogLevel ?? filterOptions.MinLevel);
    }

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        throw new NotImplementedException();
    }
}