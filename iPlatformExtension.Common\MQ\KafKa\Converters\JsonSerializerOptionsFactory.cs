using System.Text.Json;
using System.Text.Json.Serialization;
using iPlatformExtension.Common.Converters;

namespace iPlatformExtension.Common.MQ.KafKa.Converters
{
    /// <summary>
    /// JSON序列化选项工厂，用于创建和配置JSON序列化选项
    /// </summary>
    public static class JsonSerializerOptionsFactory
    {
        /// <summary>
        /// 创建默认的JSON序列化选项
        /// </summary>
        /// <param name="useSnakeCase">是否使用下划线命名方式</param>
        /// <returns>JSON序列化选项</returns>
        public static JsonSerializerOptions CreateDefault(bool useSnakeCase = false)
        {
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = useSnakeCase ? new SnakeCaseNamingPolicy() : JsonNamingPolicy.CamelCase,
                DictionaryKeyPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            };

            // 添加内置转换器
            options.Converters.Add(new DateTimeJsonConverter());
            options.Converters.Add(new DateTimeOffsetJsonConverter());
            options.Converters.Add(new TimeOnlyJsonConverter());

            return options;
        }

        /// <summary>
        /// 创建支持Column特性的JSON序列化选项
        /// </summary>
        /// <typeparam name="T">要序列化/反序列化的类型</typeparam>
        /// <returns>JSON序列化选项</returns>
        public static JsonSerializerOptions CreateWithColumnSupport<T>() where T : class
        {
            var options = CreateDefault();
            return options;
        }

        /// <summary>
        /// 配置Kafka消息值转换器的JSON序列化选项
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="supportColumnAttribute">是否支持Column特性</param>
        /// <returns>配置好的JSON序列化选项</returns>
        public static JsonSerializerOptions ConfigureForKafkaMessage<T>(
            bool supportColumnAttribute = false,
            bool useSnakeCase = false) where T : class
        {
            return supportColumnAttribute
                ? CreateWithColumnSupport<T>()
                : CreateDefault(useSnakeCase);
        }

        /// <summary>
        /// 配置CDC消息的JSON序列化选项
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <returns>配置好的JSON序列化选项</returns>
        public static JsonSerializerOptions ConfigureForCdcMessage<T>() where T : class
        {
            return CreateDefault(useSnakeCase: true);
        }
    }
}