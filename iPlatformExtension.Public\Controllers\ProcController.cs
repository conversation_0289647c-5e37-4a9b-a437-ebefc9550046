﻿using System.ComponentModel.DataAnnotations;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Public.Applications.Queries.Proc;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers;

/// <summary>
/// 任务基础信息控制器
/// </summary>
[ApiController]
[Route("proc")]
public class ProcController(ISender sender) : ControllerBase
{
    /// <summary>
    /// 根据关键字查询任务名称
    /// </summary>
    /// <param name="keyword">关键字</param>
    /// <param name="caseDirection">案件流向</param>
    /// <param name="caseType">案件类型</param>
    /// <param name="isEnabled">是否有效</param>
    /// <returns></returns>
    [HttpGet("base-ctrl-proc")]
    [ResponseCache(
        Location = ResponseCacheLocation.Any, 
        VaryByQueryKeys = [nameof(keyword), nameof(caseDirection), nameof(caseType), nameof(isEnabled)],
        Duration = 3000)]
    public Task<IEnumerable<CtrlProcInfo>> GetCtrlProcInfoAsync(string? keyword, string? caseDirection,
        string? caseType, bool? isEnabled = null)
    {
        return sender.Send(new CtrlProcQuery(keyword, caseType, caseDirection, isEnabled), HttpContext.RequestAborted);
    }
    
    /// <summary>
    /// 获取任务标识信息
    /// </summary>
    /// <param name="ctrlProcId">任务名称id</param>
    /// <returns>任务标识信息</returns> 
    [HttpGet("{ctrlProcId}/mark")]
    public Task<IEnumerable<INameInfo>> GetCtrlProcMarkInfoAsync([FromRoute, Required] string ctrlProcId)
    {
        return sender.Send(new CtrlProcMarkQuery(ctrlProcId), HttpContext.RequestAborted);
    }
}