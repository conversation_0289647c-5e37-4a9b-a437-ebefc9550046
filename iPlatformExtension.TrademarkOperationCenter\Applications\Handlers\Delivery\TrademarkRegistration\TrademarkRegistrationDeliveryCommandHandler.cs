﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Model.Dto.TrademarkDelivery;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Infrastructure.SendDelivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.TrademarkRegistration;

internal sealed class TrademarkRegistrationDeliveryCommandHandler(
    IMediator mediator,
    IApplyTypeRepository applyTypeRepository,
    IDeliveryInfoRepository deliveryInfoRepository,
    IRedisCache<RedisCacheOptionsBase> redisCache)
    : DeliveryCommandHandlerBase(mediator, redisCache, deliveryInfoRepository)
{
    public override async Task<SendDeliveryInternalCommand?> HandleSendStartupCommandAsync(SendContext context, CancellationToken cancellationToken)
    {
        var deliveryInfo = context.DeliveryInfo;
        if ((deliveryInfo.Priorities?.Count ?? 0) > 0)
        {
            throw new ApplicationException("当前递交信息包含优先权信息，不能做自动递交");
        }

        var niceCategories = deliveryInfo.NiceCategories;
        if (niceCategories is null)
        {
            throw new ApplicationException("案件信息没有尼斯分类");
        }

        var niceClassCount = deliveryInfo.NiceCategories?.Where(category => category.IsStandard).DistinctBy(category => category.GrandNumber).Count() ?? 0;
        if (niceClassCount != 1)
        {
            throw new ApplicationException("自动递交只支持一标一类");
        }

        if (deliveryInfo.DeliveryType != "1")
        {
            throw new ApplicationException("非网交方式不支持自动递交");
        }

        if (await applyTypeRepository.GetChineseValueAsync(deliveryInfo.ApplyTypeId) != "普通")
        {
            throw new ApplicationException("只有普通类型的商标才可以自动递交");
        }

        if (deliveryInfo.IsVoice || deliveryInfo.IsThreeD || deliveryInfo.IsMultipartColor || deliveryInfo.IsPortraits)
        {
            throw new ApplicationException("标识种类为声音、立体、颜色组合或肖像的任务不能作自动递交");
        }

        if (deliveryInfo.Applicants?.Any(applicant => applicant.IsOtherApplicant) ?? false)
        {
            throw new ApplicationException("含有共同申请人的任务不能做自动递交");
        }

        if (!await _mediator.Send(new StartupDeliveryButtonQuery(deliveryInfo), cancellationToken))
        {
            throw new ApplicationException($"当前递交状态不能做{context.DeliveryOperation}操作");
        }

        return new SendDeliveryInternalCommand(new DeliveryMessage()
            {
                ProcId = deliveryInfo.ProcId,
                Version = deliveryInfo.Version
            },
            context.CurrentUserId ?? string.Empty, context.DeliveryOperation,
            context.MessageKey, null, true);
    }

   
}