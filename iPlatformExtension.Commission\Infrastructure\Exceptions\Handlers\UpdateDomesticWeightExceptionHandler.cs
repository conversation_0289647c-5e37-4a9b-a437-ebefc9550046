﻿using iPlatformExtension.Commission.Application.Commands.Trademark.Domestic;
using MediatR;
using MediatR.Pipeline;
using Microsoft.AspNetCore.Diagnostics;

namespace iPlatformExtension.Commission.Infrastructure.Exceptions.Handlers;

internal sealed class UpdateDomesticWeightExceptionHandler(IHttpContextAccessor httpContextAccessor) : IRequestExceptionHandler<UpdateDomesticWeightCommand, Unit, Exception>
{
    public Task Handle(UpdateDomesticWeightCommand request, Exception exception, RequestExceptionHandlerState<Unit> state,
        CancellationToken cancellationToken)
    {
        if (request.HandleException && httpContextAccessor.HttpContext is not null)
        {
            var httpContext = httpContextAccessor.HttpContext;
            if (httpContext.Features.Get<IExceptionHandlerFeature>() is ExceptionHandlerFeature exceptionHandlerFeature)
            {
                if (exceptionHandlerFeature.Error is AggregateException aggregateException)
                {
                    var exceptionsList = aggregateException.InnerExceptions.ToList();
                    exceptionsList.Add(exception);
                    exceptionHandlerFeature.Error = new AggregateException(exceptionsList);
                }
                else
                {
                    exceptionHandlerFeature.Error = new AggregateException(exceptionHandlerFeature.Error, exception);
                }
            }
            else
            {
                var error = new AggregateException(exception);
                httpContext.Features.Set(new ExceptionHandlerFeature
                {
                    Error = error,
                    Path = httpContext.Request.Path,
                    RouteValues = httpContext.Request.RouteValues,
                    Endpoint = httpContext.GetEndpoint()
                });
            }
            state.SetHandled(Unit.Value);
        }
        
        return Task.CompletedTask;
    }
}
