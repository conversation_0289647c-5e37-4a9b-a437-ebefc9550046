﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using MediatR;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Commands.Delivery;

internal interface IUpdateDeliveryResultCommand : IRequest<bool>
{
    public string OperatorId { get; }

    public DeliInfo DeliveryInfo { get; }

    public DeliveryResultDto Result { get; }
}