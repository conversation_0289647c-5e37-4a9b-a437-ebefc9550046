﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.Filters;

public class ModelValidationExceptionFilter(IOptions<ApiBehaviorOptions> options) : IAsyncExceptionFilter
{
    public Task OnExceptionAsync(ExceptionContext context)
    {
        var modelState = context.ModelState;
        
        var exception = context.Exception;
        switch (exception)
        {
            case FluentValidation.ValidationException fluentValidationException:
            {
                var errors = fluentValidationException.Errors;
                foreach (var error in errors)
                {
                    modelState.AddModelError(error.PropertyName, error.ErrorMessage);
                }
                context.ExceptionHandled = true;
                break;
            }
            case System.ComponentModel.DataAnnotations.ValidationException validationException:
                var validationResult = validationException.ValidationResult;
                foreach (var name in validationResult.MemberNames)
                {
                    modelState.AddModelError(name, validationResult.ErrorMessage ?? string.Empty);
                }
                context.ExceptionHandled = true;
                break;
        }

        if (context.ExceptionHandled)
        {
            context.Result = options.Value.InvalidModelStateResponseFactory(context);
        }
        
        return Task.CompletedTask;
    }
}