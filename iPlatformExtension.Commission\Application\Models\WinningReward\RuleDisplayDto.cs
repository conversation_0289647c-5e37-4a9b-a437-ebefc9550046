﻿using System.Text.Json.Serialization;

namespace iPlatformExtension.Commission.Application.Models.WinningReward;

/// <summary>
/// 胜诉奖励规则显示DTO
/// </summary>
public sealed class RuleDisplayDto
{
    /// <summary>
    /// 规则id
    /// </summary>
    public int RuleId { get; set; }
    
    /// <summary>
    /// 任务名称
    /// </summary>
    public string ProcName { get; set; } = string.Empty;

    /// <summary>
    /// 案件流向
    /// </summary>
    public string CaseDirection { get; set; } = string.Empty;

    /// <summary>
    /// 裁定结果
    /// </summary>
    public string RulingResult { get; set; } = string.Empty;

    /// <summary>
    /// 形势变化
    /// </summary>
    public string SituationChanged { get; set; } = string.Empty;

    /// <summary>
    /// 日期类型
    /// </summary>
    public string DateType { get; set; } = string.Empty;

    /// <summary>
    /// 撰写人奖励
    /// </summary>
    public decimal WriterReward { get; set; }

    /// <summary>
    /// 导师奖励
    /// </summary>
    public decimal MentorReward { get; set; }

    /// <summary>
    /// 是否可用
    /// </summary>
    public string IsEnabled { get; set; } = string.Empty;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; }
}