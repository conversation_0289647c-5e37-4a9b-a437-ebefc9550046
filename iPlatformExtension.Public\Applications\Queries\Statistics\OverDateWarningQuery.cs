﻿using iPlatformExtension.Public.Applications.Models.Statistics;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.Public.Applications.Queries.Statistics;

/// <summary>
/// 超期查询
/// </summary>
/// <param name="ReportType">报告类型</param>
/// <param name="Day">天数,默认1</param>
/// <param name="UserId">员工id</param>
public record OverDateWarningQuery(ReportType ReportType, int Day = 1, List<string>? UserId = null, List<string>? DeptList = null, DateTime? EndDateTime = null) : IRequest<IEnumerable<OverDateWarningDto>>;

