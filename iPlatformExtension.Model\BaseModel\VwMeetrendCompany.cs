using System;
using System.Collections.Generic;

using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.BaseModel {

	[ Table(Name = "vw_meetrend_Company", DisableSyncStructure = true)]
	public partial class VwMeetrendCompany {

		[ Column(Name = "area_id", StringLength = 50)]
		public string AreaId { get; set; }

		[ Column(StringLength = 50, IsNullable = false)]
		public string FID { get; set; }

		[ Column(StringLength = 50)]
		public string FNAME { get; set; }

		
		public bool? FSTATUS { get; set; }

		[ Column(Name = "update_time")]
		public DateTime? UpdateTime { get; set; }

	}

}
