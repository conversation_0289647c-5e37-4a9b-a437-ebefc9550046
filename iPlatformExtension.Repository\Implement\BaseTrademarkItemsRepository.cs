﻿using FreeSql;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Repository.Interface;

namespace iPlatformExtension.Repository.Implement;

internal sealed class BaseTrademarkItemsRepository : DefaultRepository<BasTrademarkItems, string>, IBaseTrademarkItemsRepository
{
    public BaseTrademarkItemsRepository(IFreeSql freeSql, UnitOfWorkManager uowManger) : base(freeSql, uowManger)
    {
    }
}