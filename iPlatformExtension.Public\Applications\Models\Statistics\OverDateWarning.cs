﻿using MiniExcelLibs.Attributes;

namespace iPlatformExtension.Public.Applications.Models.Statistics
{

    /// <summary>
    /// 超时BaseModel
    /// </summary>
    public class OverDateWarning()
    {
        [ExcelColumn(Name = "我方文号"), ExcelColumnIndex("C")]
        public string Volume { get; init; }

        [ExcelColumn(Name = "任务名称"), ExcelColumnIndex("E")]
        public string? CtrlProcZhCn { get; set; }

        /// <summary>
        /// 预审
        /// </summary>
        [ExcelColumn(Name = "预审"), ExcelColumnIndex("A")]
        public string? IsAdvanceCheckValue { get; set; }

        /// <summary>
        /// 优审
        /// </summary>
        [ExcelColumn(Name = "优审"), ExcelColumnIndex("B")]
        public string? IsPriorityReviewValue { get; set; }

        [ExcelColumn(Name = "客户名称"), ExcelColumnIndex("D")]
        public string? CustomerName { get; init; }

        [ExcelColumn(Ignore = true)]
        public string? CtrlProcProperty { get; init; }

        [ExcelColumn(Name = "任务属性"), ExcelColumnIndex("F")]
        public string? CtrlProcPropertyValue { get; set; }

        [ExcelColumn(Ignore = true)]
        public string? ProcStatusId { get; init; }

        [ExcelColumn(Name = "任务状态"), ExcelColumnIndex("G")]
        public string? ProcStatusName { get; set; }

        [ExcelColumn(Name = "任务备注"), ExcelColumnIndex("H")]
        public string? ProcNote { get; init; }

        [ExcelColumn(Ignore = true)]
        public string? UndertakeUserId { get; init; }

        [ExcelColumn(Name = "承办人"), ExcelColumnIndex("I")]
        public string? UndertakeUserName { get; set; }


        [ExcelColumn(Name = "代理人处理状态"), ExcelColumnIndex("J")]
        public string? SubProcStatusValue { get; set; }


        [ExcelColumn(Name = "名义承办人"), ExcelColumnIndex("K")]
        public string? TitularWriteUserName { get; set; }

        [ExcelColumn(Ignore = true)]
        public string? DeptId { get; set; }

        /// <summary>
        /// 账户
        /// </summary>
        [ExcelColumn(Ignore = true)]
        public string? UserAccount { get; set; }
    }

}
