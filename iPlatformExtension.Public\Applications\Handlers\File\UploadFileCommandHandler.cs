﻿using iPlatformExtension.Common.Clients.HuaweiObs;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Commands.File;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.Public.Applications.Handlers.File
{
    internal sealed class UploadFileCommandHandler(
        HuaweiObsClient huaweiObsClient,
        IFileListARepository fileListARepository)
        : IRequestHandler<UploadFileCommand, long>
    {
        public async Task<long> Handle(UploadFileCommand request, CancellationToken cancellationToken)
        {
            var dto = request.FileUpload;
            var bucket = string.IsNullOrWhiteSpace(dto.Bucket) ? huaweiObsClient.Bucket : dto.Bucket;
            var guid = Guid.NewGuid().ToString();
            var stream = dto.File.OpenReadStream();
            var length = stream.Length;
            var fileName = guid.Replace("-", "") + "-" + length + Path.GetExtension(dto.FileName);
            var fileListA = new FileListA()
            {
                FileName = fileName,
                FileSize = length,
                InputTime = DateTime.Now,
                InstanceId = 1,
                RealName = dto.FileName,
                ServerPath = dto.ServerPath,
                Bucket = bucket
            };

            var objectName = fileListA.GetObjectName();
            var result = await huaweiObsClient.PutFileAsync(objectName, stream, bucket, dto.FileName);
            var responseStatusCode = (int)result.StatusCode;
            if (responseStatusCode is < 200 or >= 300)
                throw new ApplicationException("文件同步到华为云失败");
            
            fileListA = await fileListARepository.InsertAsync(fileListA, cancellationToken);
            return fileListA.Id;
        }
    }
}
