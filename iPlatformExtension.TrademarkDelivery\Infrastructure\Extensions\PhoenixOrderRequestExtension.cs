﻿using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Dto.TrademarkDelivery;

namespace iPlatformExtension.TrademarkDelivery.Infrastructure.Extensions;

public static class PhoenixOrderRequestExtension
{
    public static string GetOwnerType(this BasApplicantType applicantType) =>
        applicantType.ApplicantTypeCode == "5" ? "0" : "1";

    public static string GetSubjectType(this DeliApplicant applicant) => applicant.IsChineseIdentity ? "1" : "0";

    public static string GetApplicantCertificationType(this string cardType) =>
        ApplicantCertificationType.GetApplicantCertificationType(cardType).Code.ToString();
}