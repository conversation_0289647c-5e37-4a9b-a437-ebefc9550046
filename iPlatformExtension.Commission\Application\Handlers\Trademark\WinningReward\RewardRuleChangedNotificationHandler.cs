﻿using System.Threading.Channels;
using FreeSql;
using iPlatformExtension.Commission.Application.Commands.Trademark.WinningReward;
using iPlatformExtension.Common.Mediator.Notifications;
using iPlatformExtension.Model.BaseModel;
using MediatR;

namespace iPlatformExtension.Commission.Application.Handlers.Trademark.WinningReward;

internal sealed class RewardRuleChangedNotificationHandler(
    Channel<RefreshProcRewardDateCommand> channel) : INotificationHandler<EntityChangeNotification>
{
    public async Task Handle(EntityChangeNotification notification, CancellationToken cancellationToken)
    {
        var reports = notification.Reports
            .Where(info => info.EntityType == typeof(WinningRewardRule))
            .Where(info => info.Type == DbContext.EntityChangeType.Update)
            .Select(info => (info.BeforeObject as WinningRewardRule, info.Object as WinningRewardRule))
            .Where(tuple =>
            {
                var (oldObject, currentObject) = tuple;
                return oldObject!.RulingResult != currentObject!.RulingResult
                       || oldObject.DateType != currentObject.DateType
                       || oldObject.SituationChanged != currentObject.SituationChanged
                       || oldObject.IsEnabled != currentObject.IsEnabled;
            }).ToList();

        foreach (var (oldInfo, currentInfo) in reports)
        {
            await channel.Writer.WriteAsync(new RefreshProcRewardDateCommand((oldInfo!, currentInfo!)), cancellationToken);
        }
    }
}