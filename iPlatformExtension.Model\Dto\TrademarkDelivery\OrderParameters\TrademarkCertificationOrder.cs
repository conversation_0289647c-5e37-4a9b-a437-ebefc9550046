﻿using System.Text.Json.Serialization;
using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.Model.Dto.TrademarkDelivery.OrderParameters;

/// <summary>
/// 商标补发证书下单
/// </summary>
public sealed class TrademarkCertificationOrder : PhoenixOrderRequestParameters
{
    /// <summary>
    /// 申请补正理由
    /// </summary>
    [JsonPropertyName("applyProve")]
    public string ApplyReason { get; set; } = string.Empty;
    
    /// <summary>
    /// 申请人资质
    /// 申请人类型
    /// </summary>
    public string OwnerType { get; set; } = null!;
    
    /// <summary>
    /// 国家
    /// </summary>
    public string Country { get; set; } = null!;

    /// <summary>
    /// 申请人邮编
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// （企业）统一社会信用代码
    /// （个人）身 份证号
    /// </summary>
    public string IdCard { get; set; } = null!;

    /// <summary>
    /// 委托人电话
    /// </summary>
    public string PrincipalTel { get; set; } = null!;

    /// <summary>
    /// 代理人姓名
    /// </summary>
    public string AgentOrganConName { get; set; } = null!;

    /// <summary>
    /// 客户的联系电话
    /// </summary>
    public string ContactTel { get; set; } = null!;

    /// <summary>
    /// 主体资格类型:1表示中文,0表示非中文
    /// </summary>
    public string SubjectType { get; set; } = null!;
    
    /// <summary>
    /// 申请人国内接受人电邮
    /// </summary>
    public string DomesticReceiverEmail { get; set; } = null!;

    /// <summary>
    /// 申请人国内接受姓名
    /// </summary>
    public string DomesticReceiverName { get; set; } = null!;

    
    /// <summary>
    /// 申请人国内接收人地址
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? DomesticReceiverAddress { get; set; }

    /// <summary>
    /// 客户的联系邮箱
    /// </summary>
    public string ContactEmail { get; set; } = null!;

    /// <summary>
    /// 申请人地址
    /// </summary>
    public string ApplicantAddress { get; set; } = null!;

    /// <summary>
    /// 申请人英文地址
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? ApplicantEnglishAddress { get; set; }

    /// <summary>
    /// 客户的联系名称
    /// </summary>
    public string ContactName { get; set; } = null!;

    /// <summary>
    /// 申请人国内接收人地址邮编
    /// </summary>
    public string? DomesticReceiverCode { get; set; }

    /// <summary>
    /// 委托人姓名
    /// </summary>
    public string PrincipalName { get; set; } = null!;

    /// <summary>
    /// 申请主体名称(中文)
    /// </summary>
    public string ApplicantName { get; set; } = null!;

    /// <summary>
    /// 申请主体名称(英文)
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? EnglishApplicantName { get; set; }

    /// <summary>
    /// 申请人书式类型,1表示中国大陆,2表示海外,3表示台湾,4表示香港,5表示澳门
    /// </summary>
    public string BookType { get; set; } = null!;

    /// <summary>
    /// "申请人证件类型,
    /// 0不是任何类型,
    /// 1身份证,
    /// 2护照,
    /// 3其他(ownerType为企业可以忽略)"
    /// </summary>
    public string CertificatesType { get; set; } = null!;

    /// <summary>
    /// 申请人递交资料
    /// 不做参数序列化
    /// </summary>
    public IEnumerable<ApplicantAttachment> ApplicantAttachments { get; set; } = Array.Empty<ApplicantAttachment>();
    
    /// <summary>
    /// 申请人材料
    /// </summary>
    [JsonSerializationSource(nameof(ApplicantAttachments))]
    public string? Attachments { get; private set; }

    /// <summary>
    /// 商标商品信息
    /// </summary>
    public IEnumerable<BrandInfo> BrandInfos { get; set; } = Array.Empty<BrandInfo>();

    /// <summary>
    /// 序列化后商品信息
    /// </summary>
    [JsonSerializationSource(nameof(BrandInfos))]
    public string? BrandTransferParam { get; private set; }
}