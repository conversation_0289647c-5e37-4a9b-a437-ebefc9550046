﻿using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Extensions;
using iPlatformExtension.Repository.Interface;
using iPlatformExtension.TrademarkOperationCenter.Applications.Models.Delivery;
using iPlatformExtension.TrademarkOperationCenter.Applications.Queries.Delivery;

namespace iPlatformExtension.TrademarkOperationCenter.Applications.Handlers.Delivery.RefuseReexamination;

internal sealed class RefuseReexaminationOtherInfoResultQueryHandler(ISystemDictionaryRepository dictionaryRepository) 
    : OtherInfoResultNotificationHandlerBase(dictionaryRepository)
{
    protected override ValueTask<bool> MatchAsync(OtherInfoResultQuery query, CancellationToken cancellationToken)
    {
        return new ValueTask<bool>(StringComparer.OrdinalIgnoreCase.Equals(query.CtrlProcId, CtrlProcIds.RefuseReexamination));
    }

    protected override async Task HandleAsync(OtherInfoResultQuery query, CancellationToken cancellationToken)
    {
        var otherInfoSnapshot = query.TrademarkDeliveryDto.OtherInfoSnapshot;
        otherInfoSnapshot.NominalChangesType =
            (await _dictionaryRepository.GetChineseKeyValueAsync(SystemDictionaryName.NominalChangesType,
                otherInfoSnapshot.NominalChangesType)).Value;
        
        await base.HandleAsync(query, cancellationToken);
    }
}